{"date": "February 29", "url": "https://wikipedia.org/wiki/February_29", "data": {"Events": [{"year": "888", "text": "<PERSON><PERSON>, count of Paris, is crowned king of West Francia (France) by Archbishop <PERSON> of Sens at Compiègne.", "html": "888 - <a href=\"https://wikipedia.org/wiki/Odo_of_France\" title=\"Odo of France\"><PERSON>do</a>, <a href=\"https://wikipedia.org/wiki/Count_of_Paris\" title=\"Count of Paris\">count of Paris</a>, is crowned king of <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Francia</a> (France) by Archbishop <a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Sens)\" title=\"<PERSON> (archbishop of Sens)\"><PERSON> of Sens</a> at <a href=\"https://wikipedia.org/wiki/Compi%C3%A8gne\" title=\"Compiègne\">Compiègne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Odo_of_France\" title=\"Odo of France\">Odo</a>, <a href=\"https://wikipedia.org/wiki/Count_of_Paris\" title=\"Count of Paris\">count of Paris</a>, is crowned king of <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Francia</a> (France) by Archbishop <a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Sens)\" title=\"<PERSON> (archbishop of Sens)\"><PERSON> of Sens</a> at <a href=\"https://wikipedia.org/wiki/Compi%C3%A8gne\" title=\"Compiègne\">Compiègne</a>.", "links": [{"title": "<PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/Odo_of_France"}, {"title": "Count of Paris", "link": "https://wikipedia.org/wiki/Count_of_Paris"}, {"title": "West Francia", "link": "https://wikipedia.org/wiki/West_Francia"}, {"title": "<PERSON> (archbishop of Sens)", "link": "https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Sens)"}, {"title": "Compiègne", "link": "https://wikipedia.org/wiki/Compi%C3%A8gne"}]}, {"year": "1504", "text": "<PERSON> uses his knowledge of a lunar eclipse that night to convince Jamaican natives to provide him with supplies.", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> uses his knowledge of <a href=\"https://wikipedia.org/wiki/March_1504_lunar_eclipse\" title=\"March 1504 lunar eclipse\">a lunar eclipse that night</a> to convince Jamaican natives to provide him with supplies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Christopher <PERSON>\"><PERSON></a> uses his knowledge of <a href=\"https://wikipedia.org/wiki/March_1504_lunar_eclipse\" title=\"March 1504 lunar eclipse\">a lunar eclipse that night</a> to convince Jamaican natives to provide him with supplies.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "March 1504 lunar eclipse", "link": "https://wikipedia.org/wiki/March_1504_lunar_eclipse"}]}, {"year": "1644", "text": "<PERSON>'s second Pacific voyage begins as he leaves Batavia in command of three ships.", "html": "1644 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s second Pacific voyage begins as he leaves <a href=\"https://wikipedia.org/wiki/Batavia,_Dutch_East_Indies\" title=\"Batavia, Dutch East Indies\">Batavia</a> in command of three ships.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s second Pacific voyage begins as he leaves <a href=\"https://wikipedia.org/wiki/Batavia,_Dutch_East_Indies\" title=\"Batavia, Dutch East Indies\">Batavia</a> in command of three ships.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Abel_<PERSON>sman"}, {"title": "Batavia, Dutch East Indies", "link": "https://wikipedia.org/wiki/Batavia,_Dutch_East_Indies"}]}, {"year": "1704", "text": "In Queen Anne's War, French forces and Native Americans stage a raid on Deerfield, Massachusetts Bay Colony, killing 56 villagers and taking more than 100 captive.", "html": "1704 - In <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>%27s_War\" title=\"Queen <PERSON>'s War\">Queen <PERSON>'s War</a>, French forces and <a href=\"https://wikipedia.org/wiki/Native_American_(U.S.)\" class=\"mw-redirect\" title=\"Native American (U.S.)\">Native Americans</a> stage a <a href=\"https://wikipedia.org/wiki/Raid_on_Deerfield\" title=\"Raid on Deerfield\">raid on Deerfield, Massachusetts Bay Colony</a>, killing 56 villagers and taking more than 100 captive.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>%27s_War\" title=\"Queen <PERSON>'s War\">Queen <PERSON>'s War</a>, French forces and <a href=\"https://wikipedia.org/wiki/Native_American_(U.S.)\" class=\"mw-redirect\" title=\"Native American (U.S.)\">Native Americans</a> stage a <a href=\"https://wikipedia.org/wiki/Raid_on_Deerfield\" title=\"Raid on Deerfield\">raid on Deerfield, Massachusetts Bay Colony</a>, killing 56 villagers and taking more than 100 captive.", "links": [{"title": "Queen Anne's War", "link": "https://wikipedia.org/wiki/Queen_<PERSON>%27s_War"}, {"title": "Native American (U.S.)", "link": "https://wikipedia.org/wiki/Native_American_(U.S.)"}, {"title": "Raid on Deerfield", "link": "https://wikipedia.org/wiki/Raid_on_Deerfield"}]}, {"year": "1712", "text": "February 29 is followed by February 30 in Sweden, in a move to abolish the Swedish calendar for a return to the Julian calendar.", "html": "1712 - February 29 is followed by <a href=\"https://wikipedia.org/wiki/February_30\" class=\"mw-redirect\" title=\"February 30\">February 30</a> in Sweden, in a move to abolish the <a href=\"https://wikipedia.org/wiki/Swedish_calendar\" title=\"Swedish calendar\">Swedish calendar</a> for a return to the <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>.", "no_year_html": "February 29 is followed by <a href=\"https://wikipedia.org/wiki/February_30\" class=\"mw-redirect\" title=\"February 30\">February 30</a> in Sweden, in a move to abolish the <a href=\"https://wikipedia.org/wiki/Swedish_calendar\" title=\"Swedish calendar\">Swedish calendar</a> for a return to the <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>.", "links": [{"title": "February 30", "link": "https://wikipedia.org/wiki/February_30"}, {"title": "Swedish calendar", "link": "https://wikipedia.org/wiki/Swedish_calendar"}, {"title": "Julian calendar", "link": "https://wikipedia.org/wiki/Julian_calendar"}]}, {"year": "1720", "text": "<PERSON><PERSON><PERSON>, Queen of Sweden abdicates in favour of her husband, who becomes King <PERSON> on March 24.", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Queen_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Queen of Sweden\"><PERSON><PERSON><PERSON>, Queen of Sweden</a> abdicates in favour of her husband, who becomes King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"Frederick I of Sweden\"><PERSON></a> on March 24.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Queen_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Queen of Sweden\"><PERSON><PERSON><PERSON>, Queen of Sweden</a> abdicates in favour of her husband, who becomes King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"Frederick I of Sweden\"><PERSON></a> on March 24.", "links": [{"title": "<PERSON><PERSON><PERSON>, Queen of Sweden", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>,_Queen_of_Sweden"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1768", "text": "Polish nobles form the Bar Confederation.", "html": "1768 - Polish nobles form the <a href=\"https://wikipedia.org/wiki/Bar_Confederation\" title=\"Bar Confederation\">Bar Confederation</a>.", "no_year_html": "Polish nobles form the <a href=\"https://wikipedia.org/wiki/Bar_Confederation\" title=\"Bar Confederation\">Bar Confederation</a>.", "links": [{"title": "Bar Confederation", "link": "https://wikipedia.org/wiki/Bar_Confederation"}]}, {"year": "1796", "text": "The Jay Treaty between the United States and Great Britain comes into force, facilitating ten years of peaceful trade between the two nations.", "html": "1796 - The <a href=\"https://wikipedia.org/wiki/Jay_Treaty\" title=\"Jay Treaty\">Jay Treaty</a> between the United States and Great Britain comes into force, facilitating ten years of peaceful trade between the two nations.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Jay_Treaty\" title=\"Jay Treaty\">Jay Treaty</a> between the United States and Great Britain comes into force, facilitating ten years of peaceful trade between the two nations.", "links": [{"title": "Jay <PERSON>", "link": "https://wikipedia.org/wiki/Jay_Treaty"}]}, {"year": "1892", "text": "St. Petersburg, Florida is incorporated.", "html": "1892 - <a href=\"https://wikipedia.org/wiki/St._Petersburg,_Florida\" title=\"St. Petersburg, Florida\">St. Petersburg, Florida</a> is incorporated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St._Petersburg,_Florida\" title=\"St. Petersburg, Florida\">St. Petersburg, Florida</a> is incorporated.", "links": [{"title": "St. Petersburg, Florida", "link": "https://wikipedia.org/wiki/St._Petersburg,_Florida"}]}, {"year": "1908", "text": "James Madison University is founded at Harrisonburg, Virginia, United States as The State Normal and Industrial School for Women by the Virginia General Assembly.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/James_Madison_University\" title=\"James Madison University\">James Madison University</a> is founded at <a href=\"https://wikipedia.org/wiki/Harrisonburg,_Virginia\" title=\"Harrisonburg, Virginia\">Harrisonburg, Virginia</a>, United States as The State Normal and Industrial School for Women by the <a href=\"https://wikipedia.org/wiki/Virginia_General_Assembly\" title=\"Virginia General Assembly\">Virginia General Assembly</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/James_Madison_University\" title=\"James Madison University\">James Madison University</a> is founded at <a href=\"https://wikipedia.org/wiki/Harrisonburg,_Virginia\" title=\"Harrisonburg, Virginia\">Harrisonburg, Virginia</a>, United States as The State Normal and Industrial School for Women by the <a href=\"https://wikipedia.org/wiki/Virginia_General_Assembly\" title=\"Virginia General Assembly\">Virginia General Assembly</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Madison_University"}, {"title": "Harrisonburg, Virginia", "link": "https://wikipedia.org/wiki/Harrisonburg,_Virginia"}, {"title": "Virginia General Assembly", "link": "https://wikipedia.org/wiki/Virginia_General_Assembly"}]}, {"year": "1912", "text": "The <PERSON><PERSON> (Moving Stone) of Tandil falls and breaks.", "html": "1912 - The <a href=\"https://wikipedia.org/wiki/Piedra_Movediza\" title=\"Piedra Movediza\"><PERSON><PERSON> Movedi<PERSON></a> (Moving Stone) of <a href=\"https://wikipedia.org/wiki/Tandil\" title=\"Tandil\"><PERSON><PERSON><PERSON></a> falls and breaks.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pie<PERSON>_Movediza\" title=\"Piedra Movediza\"><PERSON><PERSON> Movedi<PERSON></a> (Moving Stone) of <a href=\"https://wikipedia.org/wiki/Tandil\" title=\"Tandil\"><PERSON><PERSON><PERSON></a> falls and breaks.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>dra_Movediza"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tandil"}]}, {"year": "1916", "text": "Tokelau is annexed by the United Kingdom.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Tokelau\" title=\"Tokelau\">Tokelau</a> is annexed by the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokelau\" title=\"Tokelau\">Tokelau</a> is annexed by the United Kingdom.", "links": [{"title": "Tokelau", "link": "https://wikipedia.org/wiki/Tokelau"}]}, {"year": "1916", "text": "In South Carolina, the minimum working age for factory, mill and mine workers is raised from 12 to 14 years old.", "html": "1916 - In <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>, the minimum <a href=\"https://wikipedia.org/wiki/Working_age\" class=\"mw-redirect\" title=\"Working age\">working age</a> for factory, mill and mine workers is <a href=\"https://wikipedia.org/wiki/Child_labor\" class=\"mw-redirect\" title=\"Child labor\">raised from 12 to 14 years old</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>, the minimum <a href=\"https://wikipedia.org/wiki/Working_age\" class=\"mw-redirect\" title=\"Working age\">working age</a> for factory, mill and mine workers is <a href=\"https://wikipedia.org/wiki/Child_labor\" class=\"mw-redirect\" title=\"Child labor\">raised from 12 to 14 years old</a>.", "links": [{"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}, {"title": "Working age", "link": "https://wikipedia.org/wiki/Working_age"}, {"title": "Child labor", "link": "https://wikipedia.org/wiki/Child_labor"}]}, {"year": "1920", "text": "The Czechoslovak National Assembly adopts the Constitution.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/First_Czechoslovak_Republic\" title=\"First Czechoslovak Republic\">Czechoslovak</a> National Assembly adopts the <a href=\"https://wikipedia.org/wiki/Czechoslovak_Constitution_of_1920\" title=\"Czechoslovak Constitution of 1920\">Constitution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Czechoslovak_Republic\" title=\"First Czechoslovak Republic\">Czechoslovak</a> National Assembly adopts the <a href=\"https://wikipedia.org/wiki/Czechoslovak_Constitution_of_1920\" title=\"Czechoslovak Constitution of 1920\">Constitution</a>.", "links": [{"title": "First Czechoslovak Republic", "link": "https://wikipedia.org/wiki/First_Czechoslovak_Republic"}, {"title": "Czechoslovak Constitution of 1920", "link": "https://wikipedia.org/wiki/Czechoslovak_Constitution_of_1920"}]}, {"year": "1936", "text": "The February 26 Incident in Tokyo ends.", "html": "1936 - The <a href=\"https://wikipedia.org/wiki/February_26_Incident\" class=\"mw-redirect\" title=\"February 26 Incident\">February 26 Incident</a> in Tokyo ends.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/February_26_Incident\" class=\"mw-redirect\" title=\"February 26 Incident\">February 26 Incident</a> in Tokyo ends.", "links": [{"title": "February 26 Incident", "link": "https://wikipedia.org/wiki/February_26_Incident"}]}, {"year": "1940", "text": "For her performance as <PERSON><PERSON> in Gone with the Wind, <PERSON><PERSON> becomes the first African American to win an Academy Award.", "html": "1940 - For her performance as <PERSON><PERSON> in <i><a href=\"https://wikipedia.org/wiki/Gone_with_the_Wind_(film)\" title=\"Gone with the Wind (film)\">Gone with the Wind</a></i>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> to <a href=\"https://wikipedia.org/wiki/12th_Academy_Awards\" title=\"12th Academy Awards\">win</a> an <a href=\"https://wikipedia.org/wiki/Academy_Awards\" title=\"Academy Awards\">Academy Award</a>.", "no_year_html": "For her performance as <PERSON><PERSON> in <i><a href=\"https://wikipedia.org/wiki/Gone_with_the_Wind_(film)\" title=\"Gone with the Wind (film)\">Gone with the Wind</a></i>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aniel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> to <a href=\"https://wikipedia.org/wiki/12th_Academy_Awards\" title=\"12th Academy Awards\">win</a> an <a href=\"https://wikipedia.org/wiki/Academy_Awards\" title=\"Academy Awards\">Academy Award</a>.", "links": [{"title": "Gone with the Wind (film)", "link": "https://wikipedia.org/wiki/Gone_with_the_Wind_(film)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}, {"title": "12th Academy Awards", "link": "https://wikipedia.org/wiki/12th_Academy_Awards"}, {"title": "Academy Awards", "link": "https://wikipedia.org/wiki/Academy_Awards"}]}, {"year": "1940", "text": "Finland initiates Winter War peace negotiations.", "html": "1940 - Finland initiates <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a> peace negotiations.", "no_year_html": "Finland initiates <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a> peace negotiations.", "links": [{"title": "Winter War", "link": "https://wikipedia.org/wiki/Winter_War"}]}, {"year": "1940", "text": "In a ceremony held in Berkeley, California, physicist <PERSON> receives the 1939 Nobel Prize in Physics from Sweden's consul general in San Francisco.", "html": "1940 - In a ceremony held in <a href=\"https://wikipedia.org/wiki/Berkeley,_California\" title=\"Berkeley, California\">Berkeley, California</a>, physicist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives the 1939 <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize in Physics</a> from Sweden's <a href=\"https://wikipedia.org/wiki/Consul_General\" class=\"mw-redirect\" title=\"Consul General\">consul general</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "no_year_html": "In a ceremony held in <a href=\"https://wikipedia.org/wiki/Berkeley,_California\" title=\"Berkeley, California\">Berkeley, California</a>, physicist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives the 1939 <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize in Physics</a> from Sweden's <a href=\"https://wikipedia.org/wiki/Consul_General\" class=\"mw-redirect\" title=\"Consul General\">consul general</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "links": [{"title": "Berkeley, California", "link": "https://wikipedia.org/wiki/Berkeley,_California"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}, {"title": "Consul General", "link": "https://wikipedia.org/wiki/Consul_General"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}]}, {"year": "1944", "text": "The Admiralty Islands are invaded in Operation Brewer, led by American general <PERSON>, in World War II.", "html": "1944 - The <a href=\"https://wikipedia.org/wiki/Admiralty_Islands\" title=\"Admiralty Islands\">Admiralty Islands</a> are invaded in <a href=\"https://wikipedia.org/wiki/Operation_Brewer\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, led by American general <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in World War II.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Admiralty_Islands\" title=\"Admiralty Islands\">Admiralty Islands</a> are invaded in <a href=\"https://wikipedia.org/wiki/Operation_Brewer\" class=\"mw-redirect\" title=\"<PERSON> Brewer\"><PERSON></a>, led by American general <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in World War II.", "links": [{"title": "Admiralty Islands", "link": "https://wikipedia.org/wiki/Admiralty_Islands"}, {"title": "Operation Brewer", "link": "https://wikipedia.org/wiki/Operation_Brewer"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "The 5.7 Mw  Agadir earthquake shakes coastal Morocco with a maximum perceived intensity of X (Extreme), destroying Agadir and leaving 12,000 dead and another 12,000 injured.", "html": "1960 - The 5.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1960_Agadir_earthquake\" title=\"1960 Agadir earthquake\">Agadir earthquake</a> shakes coastal <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a> with a maximum perceived intensity of <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">X (<i>Extreme</i>)</a>, destroying Agadir and leaving 12,000 dead and another 12,000 injured.", "no_year_html": "The 5.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1960_Agadir_earthquake\" title=\"1960 Agadir earthquake\">Agadir earthquake</a> shakes coastal <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a> with a maximum perceived intensity of <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">X (<i>Extreme</i>)</a>, destroying Agadir and leaving 12,000 dead and another 12,000 injured.", "links": [{"title": "1960 Agadir earthquake", "link": "https://wikipedia.org/wiki/1960_Agadir_earthquake"}, {"title": "Morocco", "link": "https://wikipedia.org/wiki/Morocco"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1964", "text": "British Eagle International Airlines Flight 802/6 crashes into the Glungezer mountain in the Tux Alps of Austria, killing all 75 people aboard.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/British_Eagle_International_Airlines_Flight_802/6\" title=\"British Eagle International Airlines Flight 802/6\">British Eagle International Airlines Flight 802/6</a> crashes into the <a href=\"https://wikipedia.org/wiki/Glungezer\" title=\"Glungezer\">Glungezer</a> mountain in the <a href=\"https://wikipedia.org/wiki/Tux_Alps\" title=\"Tux Alps\">Tux Alps</a> of Austria, killing all 75 people aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_Eagle_International_Airlines_Flight_802/6\" title=\"British Eagle International Airlines Flight 802/6\">British Eagle International Airlines Flight 802/6</a> crashes into the <a href=\"https://wikipedia.org/wiki/Glungezer\" title=\"Glungezer\">Glungezer</a> mountain in the <a href=\"https://wikipedia.org/wiki/Tux_Alps\" title=\"Tux Alps\">Tux Alps</a> of Austria, killing all 75 people aboard.", "links": [{"title": "British Eagle International Airlines Flight 802/6", "link": "https://wikipedia.org/wiki/British_Eagle_International_Airlines_Flight_802/6"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>ezer"}, {"title": "Tux Alps", "link": "https://wikipedia.org/wiki/Tux_Alps"}]}, {"year": "1968", "text": "Aeroflot Flight 15 crashes Irkutsk Oblast, Soviet Union, due to a loss of control. Eighty-three of the 84 occupants onboard die. The exact cause of the accident is unknown.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_15\" title=\"Aeroflot Flight 15\">Aeroflot Flight 15</a> crashes <a href=\"https://wikipedia.org/wiki/Irkutsk_Oblast\" title=\"Irkutsk Oblast\">Irkutsk Oblast</a>, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, due to a loss of control. Eighty-three of the 84 occupants onboard die. The exact cause of the accident is unknown.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_15\" title=\"Aeroflot Flight 15\">Aeroflot Flight 15</a> crashes <a href=\"https://wikipedia.org/wiki/Irkutsk_Oblast\" title=\"Irkutsk Oblast\">Irkutsk Oblast</a>, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, due to a loss of control. Eighty-three of the 84 occupants onboard die. The exact cause of the accident is unknown.", "links": [{"title": "Aeroflot Flight 15", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_15"}, {"title": "Irkutsk Oblast", "link": "https://wikipedia.org/wiki/Irkutsk_Oblast"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1972", "text": "South Korea withdraws 11,000 of its 48,000 troops from South Vietnam as part of <PERSON>'s Vietnamization policy in the Vietnam War.", "html": "1972 - South Korea withdraws 11,000 of its 48,000 troops from <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> as part of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a> policy in the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>.", "no_year_html": "South Korea withdraws 11,000 of its 48,000 troops from <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> as part of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a> policy in the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>.", "links": [{"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vietnamization", "link": "https://wikipedia.org/wiki/Vietnamization"}, {"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON> of the Hartford Whalers makes NHL history as he scores his 800th goal.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Hartford_Whalers\" title=\"Hartford Whalers\">Hartford Whalers</a> makes <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">NHL</a> history as he scores his 800th goal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Hartford_Whalers\" title=\"Hartford Whalers\">Hartford Whalers</a> makes <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">NHL</a> history as he scores his 800th goal.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON><PERSON>_<PERSON>"}, {"title": "Hartford Whalers", "link": "https://wikipedia.org/wiki/Hartford_Whalers"}, {"title": "National Hockey League", "link": "https://wikipedia.org/wiki/National_Hockey_League"}]}, {"year": "1984", "text": "<PERSON> announces his retirement as Liberal Party leader and Prime Minister of Canada.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces his retirement as <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Canada\" title=\"Liberal Party of Canada\">Liberal Party</a> leader and <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces his retirement as <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Canada\" title=\"Liberal Party of Canada\">Liberal Party</a> leader and <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Liberal Party of Canada", "link": "https://wikipedia.org/wiki/Liberal_Party_of_Canada"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1988", "text": "South African archbishop <PERSON> is arrested along with 100 other clergymen during a five-day anti-apartheid demonstration in Cape Town.", "html": "1988 - South African archbishop <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested along with 100 other clergymen during a five-day anti-<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a> demonstration in <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>.", "no_year_html": "South African archbishop <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested along with 100 other clergymen during a five-day anti-<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a> demonstration in <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}, {"title": "Cape Town", "link": "https://wikipedia.org/wiki/Cape_Town"}]}, {"year": "1988", "text": "<PERSON><PERSON> becomes the first member of the House of Commons of Canada to come out as gay.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first member of the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_Canada\" title=\"House of Commons of Canada\">House of Commons of Canada</a> to <a href=\"https://wikipedia.org/wiki/Coming_out\" title=\"Coming out\">come out as gay</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first member of the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_Canada\" title=\"House of Commons of Canada\">House of Commons of Canada</a> to <a href=\"https://wikipedia.org/wiki/Coming_out\" title=\"Coming out\">come out as gay</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "House of Commons of Canada", "link": "https://wikipedia.org/wiki/House_of_Commons_of_Canada"}, {"title": "Coming out", "link": "https://wikipedia.org/wiki/Coming_out"}]}, {"year": "1992", "text": "A referendum is begun in Bosnia and Herzegovina for the determination of Bosnian independence.", "html": "1992 - A referendum is begun in <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Bosnia_and_Herzegovina\" title=\"Socialist Republic of Bosnia and Herzegovina\">Bosnia and Herzegovina</a> for the <a href=\"https://wikipedia.org/wiki/Bosnian_independence_referendum,_1992\" class=\"mw-redirect\" title=\"Bosnian independence referendum, 1992\">determination of Bosnian independence</a>.", "no_year_html": "A referendum is begun in <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Bosnia_and_Herzegovina\" title=\"Socialist Republic of Bosnia and Herzegovina\">Bosnia and Herzegovina</a> for the <a href=\"https://wikipedia.org/wiki/Bosnian_independence_referendum,_1992\" class=\"mw-redirect\" title=\"Bosnian independence referendum, 1992\">determination of Bosnian independence</a>.", "links": [{"title": "Socialist Republic of Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Socialist_Republic_of_Bosnia_and_Herzegovina"}, {"title": "Bosnian independence referendum, 1992", "link": "https://wikipedia.org/wiki/Bosnian_independence_referendum,_1992"}]}, {"year": "1996", "text": "Faucett Perú Flight 251 crashes in the Andes; all 123 passengers and crew are killed.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Faucett_Per%C3%BA_Flight_251\" title=\"Faucett Perú Flight 251\">Faucett Perú Flight 251</a> crashes in <a href=\"https://wikipedia.org/wiki/The_Andes\" class=\"mw-redirect\" title=\"The Andes\">the Andes</a>; all 123 passengers and crew are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Faucett_Per%C3%BA_Flight_251\" title=\"Faucett Perú Flight 251\">Faucett Perú Flight 251</a> crashes in <a href=\"https://wikipedia.org/wiki/The_Andes\" class=\"mw-redirect\" title=\"The Andes\">the Andes</a>; all 123 passengers and crew are killed.", "links": [{"title": "Faucett Perú Flight 251", "link": "https://wikipedia.org/wiki/Faucett_Per%C3%BA_Flight_251"}, {"title": "The Andes", "link": "https://wikipedia.org/wiki/The_Andes"}]}, {"year": "1996", "text": "The Siege of Sarajevo officially ends.", "html": "1996 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Sarajevo\" title=\"Siege of Sarajevo\">Siege of Sarajevo</a> officially ends.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Sarajevo\" title=\"Siege of Sarajevo\">Siege of Sarajevo</a> officially ends.", "links": [{"title": "Siege of Sarajevo", "link": "https://wikipedia.org/wiki/Siege_of_Sarajevo"}]}, {"year": "2000", "text": "Chechens attack a guard post near Ulus Kert, eventually killing 84 Russian paratroopers during the Second Chechen War.", "html": "2000 - Chechens attack a guard post near Ulus Kert, eventually killing 84 Russian paratroopers during the <a href=\"https://wikipedia.org/wiki/Second_Chechen_War\" title=\"Second Chechen War\">Second Chechen War</a>.", "no_year_html": "Chechens attack a guard post near Ulus Kert, eventually killing 84 Russian paratroopers during the <a href=\"https://wikipedia.org/wiki/Second_Chechen_War\" title=\"Second Chechen War\">Second Chechen War</a>.", "links": [{"title": "Second Chechen War", "link": "https://wikipedia.org/wiki/Second_Chechen_War"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON> is removed as president of Haiti following a coup.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is removed as president of <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a> following <a href=\"https://wikipedia.org/wiki/2004_Haitian_coup_d%27%C3%A9tat\" title=\"2004 Haitian coup d'état\">a coup</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is removed as president of <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a> following <a href=\"https://wikipedia.org/wiki/2004_Haitian_coup_d%27%C3%A9tat\" title=\"2004 Haitian coup d'état\">a coup</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}, {"title": "2004 Haitian coup d'état", "link": "https://wikipedia.org/wiki/2004_Haitian_coup_d%27%C3%A9tat"}]}, {"year": "2008", "text": "The United Kingdom's Ministry of Defence withdraws Prince <PERSON> from a tour of Afghanistan after news of his deployment is leaked to foreign media.", "html": "2008 - The United Kingdom's <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(United_Kingdom)\" title=\"Ministry of Defence (United Kingdom)\">Ministry of Defence</a> withdraws <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Prince <PERSON>\">Prince <PERSON></a> from a <a href=\"https://wikipedia.org/wiki/Operation_Herrick\" title=\"Operation Herrick\">tour of Afghanistan</a> after news of his deployment is leaked to foreign media.", "no_year_html": "The United Kingdom's <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(United_Kingdom)\" title=\"Ministry of Defence (United Kingdom)\">Ministry of Defence</a> withdraws <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Prince <PERSON>\">Prince <PERSON></a> from a <a href=\"https://wikipedia.org/wiki/Operation_Herrick\" title=\"Operation Herrick\">tour of Afghanistan</a> after news of his deployment is leaked to foreign media.", "links": [{"title": "Ministry of Defence (United Kingdom)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(United_Kingdom)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Operation Herrick", "link": "https://wikipedia.org/wiki/Operation_Herrick"}]}, {"year": "2008", "text": "<PERSON><PERSON> admits to fabricating her memoir, <PERSON><PERSON>: <PERSON> Mémoire of the Holocaust Years, in which she claims to have lived with a pack of wolves in the woods during the Holocaust.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> admits to fabricating her memoir, <i><a href=\"https://wikipedia.org/wiki/Misha:_A_M%C3%A9<PERSON>ire_of_the_Holocaust_Years\" title=\"<PERSON><PERSON>: <PERSON> Mémoire of the Holocaust Years\"><PERSON><PERSON>: <PERSON> of the Holocaust Years</a></i>, in which she claims to have lived with a pack of wolves in the woods during <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">the Holocaust</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> admits to fabricating her memoir, <i><a href=\"https://wikipedia.org/wiki/Misha:_A_M%C3%A9<PERSON>ire_of_the_Holocaust_Years\" title=\"<PERSON><PERSON>: <PERSON> Mémoire of the Holocaust Years\"><PERSON><PERSON>: <PERSON> of the Holocaust Years</a></i>, in which she claims to have lived with a pack of wolves in the woods during <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">the Holocaust</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>fonseca"}, {"title": "<PERSON>sha: <PERSON> M<PERSON>mo<PERSON> of the Holocaust Years", "link": "https://wikipedia.org/wiki/Misha:_A_M%C3%A9moire_of_the_Holocaust_Years"}, {"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}]}, {"year": "2012", "text": "North Korea agrees to suspend uranium enrichment and nuclear and long-range missile tests in return for US food aid.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> agrees to suspend <a href=\"https://wikipedia.org/wiki/Uranium_enrichment\" class=\"mw-redirect\" title=\"Uranium enrichment\">uranium enrichment</a> and nuclear and long-range missile tests in return for US food aid.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> agrees to suspend <a href=\"https://wikipedia.org/wiki/Uranium_enrichment\" class=\"mw-redirect\" title=\"Uranium enrichment\">uranium enrichment</a> and nuclear and long-range missile tests in return for US food aid.", "links": [{"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Uranium enrichment", "link": "https://wikipedia.org/wiki/Uranium_enrichment"}]}, {"year": "2016", "text": "In the Miqdadiyah bombing: at least 40 people are killed and 58 others wounded following a suicide bombing by ISIL at a Shi'ite funeral in the city of Miqdadiyah, Diyala, Iraq.", "html": "2016 - In the <a href=\"https://wikipedia.org/wiki/2016_Miqdadiyah_bombing\" title=\"2016 Miqdadiyah bombing\">Miqdadiyah bombing</a>: at least 40 people are killed and 58 others wounded following a <a href=\"https://wikipedia.org/wiki/Suicide_bombing\" class=\"mw-redirect\" title=\"Suicide bombing\">suicide bombing</a> by <a href=\"https://wikipedia.org/wiki/ISIL\" class=\"mw-redirect\" title=\"ISIL\">ISIL</a> at a <a href=\"https://wikipedia.org/wiki/Shi%27ite\" class=\"mw-redirect\" title=\"Shi'ite\">Shi'ite</a> funeral in the city of <a href=\"https://wikipedia.org/wiki/Miqdadiyah\" title=\"Miqdadiyah\">Miqdadiyah</a>, <a href=\"https://wikipedia.org/wiki/Diyala_Governorate\" title=\"Diyala Governorate\">Diyala</a>, Iraq.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/2016_Miqdadiyah_bombing\" title=\"2016 Miqdadiyah bombing\">Miqdadiyah bombing</a>: at least 40 people are killed and 58 others wounded following a <a href=\"https://wikipedia.org/wiki/Suicide_bombing\" class=\"mw-redirect\" title=\"Suicide bombing\">suicide bombing</a> by <a href=\"https://wikipedia.org/wiki/ISIL\" class=\"mw-redirect\" title=\"ISIL\">ISIL</a> at a <a href=\"https://wikipedia.org/wiki/Shi%27ite\" class=\"mw-redirect\" title=\"Shi'ite\">Shi'ite</a> funeral in the city of <a href=\"https://wikipedia.org/wiki/Miqdadiyah\" title=\"Miqdadiyah\">Miqdadiyah</a>, <a href=\"https://wikipedia.org/wiki/Diyala_Governorate\" title=\"Diyala Governorate\">Diyala</a>, Iraq.", "links": [{"title": "2016 Miqdadiyah bombing", "link": "https://wikipedia.org/wiki/2016_Miq<PERSON>iyah_bombing"}, {"title": "Suicide bombing", "link": "https://wikipedia.org/wiki/Suicide_bombing"}, {"title": "ISIL", "link": "https://wikipedia.org/wiki/ISIL"}, {"title": "Shi'ite", "link": "https://wikipedia.org/wiki/Shi%27ite"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miq<PERSON><PERSON>h"}, {"title": "Diyala Governorate", "link": "https://wikipedia.org/wiki/Diyala_Governorate"}]}, {"year": "2020", "text": "During a demonstration, pro-government colectivos shoot at disputed President and Speaker of the National Assembly <PERSON> and his supporters in Barquisimeto, Venezuela, leaving five injured.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/2020\" title=\"2020\">2020</a> - During a demonstration, pro-government <a href=\"https://wikipedia.org/wiki/Colectivo_(Venezuela)\" title=\"Colectivo (Venezuela)\">colectivos</a> <a href=\"https://wikipedia.org/wiki/2020_Barquisimeto_shooting\" title=\"2020 Barquisimeto shooting\">shoot</a> at disputed President and Speaker of the <a href=\"https://wikipedia.org/wiki/National_Assembly_(Venezuela)\" class=\"mw-redirect\" title=\"National Assembly (Venezuela)\">National Assembly</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3\" title=\"<PERSON>\"><PERSON></a> and his supporters in <a href=\"https://wikipedia.org/wiki/Barquisimeto\" title=\"Barquisimeto\">Barquisimeto</a>, Venezuela, leaving five injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2020\" title=\"2020\">2020</a> - During a demonstration, pro-government <a href=\"https://wikipedia.org/wiki/Colectivo_(Venezuela)\" title=\"Colectivo (Venezuela)\">colectivos</a> <a href=\"https://wikipedia.org/wiki/2020_Barquisimeto_shooting\" title=\"2020 Barquisimeto shooting\">shoot</a> at disputed President and Speaker of the <a href=\"https://wikipedia.org/wiki/National_Assembly_(Venezuela)\" class=\"mw-redirect\" title=\"National Assembly (Venezuela)\">National Assembly</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3\" title=\"<PERSON>\"><PERSON></a> and his supporters in <a href=\"https://wikipedia.org/wiki/Barquisimeto\" title=\"Barquisimeto\">Barquisimeto</a>, Venezuela, leaving five injured.", "links": [{"title": "2020", "link": "https://wikipedia.org/wiki/2020"}, {"title": "Colectivo (Venezuela)", "link": "https://wikipedia.org/wiki/Colectivo_(Venezuela)"}, {"title": "2020 Barquisimeto shooting", "link": "https://wikipedia.org/wiki/2020_Barquisimeto_shooting"}, {"title": "National Assembly (Venezuela)", "link": "https://wikipedia.org/wiki/National_Assembly_(Venezuela)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3"}, {"title": "Barquisimeto", "link": "https://wikipedia.org/wiki/Barquisimeto"}]}, {"year": "2020", "text": "The United States and the Taliban sign the Doha Agreement for bringing peace to Afghanistan.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> and the <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> sign the <a href=\"https://wikipedia.org/wiki/United_States%E2%80%93Taliban_deal\" title=\"United States-Taliban deal\">Doha Agreement</a> for bringing peace to <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> and the <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> sign the <a href=\"https://wikipedia.org/wiki/United_States%E2%80%93Taliban_deal\" title=\"United States-Taliban deal\">Doha Agreement</a> for bringing peace to <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "links": [{"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Taliban", "link": "https://wikipedia.org/wiki/Taliban"}, {"title": "United States-Taliban deal", "link": "https://wikipedia.org/wiki/United_States%E2%80%93Taliban_deal"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON><PERSON> is appointed as the 8th Prime Minister of Malaysia, amid the 2020 Malaysian political crisis.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is appointed as the 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a>, amid the <a href=\"https://wikipedia.org/wiki/2020_Malaysian_political_crisis\" class=\"mw-redirect\" title=\"2020 Malaysian political crisis\">2020 Malaysian political crisis</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is appointed as the 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a>, amid the <a href=\"https://wikipedia.org/wiki/2020_Malaysian_political_crisis\" class=\"mw-redirect\" title=\"2020 Malaysian political crisis\">2020 Malaysian political crisis</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Malaysia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malaysia"}, {"title": "2020 Malaysian political crisis", "link": "https://wikipedia.org/wiki/2020_Malaysian_political_crisis"}]}, {"year": "2024", "text": "The Flour massacre (also known as the Al-<PERSON> massacre) took place on al-Rashid street at the Al-Nabulsi roundabout to the west of Gaza City in the Palestinian territory of the Gaza Strip where more than 100 Palestinians were killed and over 750 were wounded after Israeli forces opened fire on Palestinians waiting for humanitarian aid amidst the Israel-Hamas war.", "html": "2024 - The <a href=\"https://wikipedia.org/wiki/Flour_massacre\" title=\"Flour massacre\">Flour massacre</a> (also known as the Al-Rashid massacre) took place on al-Rashid street at the Al-Nabulsi roundabout to the west of <a href=\"https://wikipedia.org/wiki/Gaza_City\" title=\"Gaza City\">Gaza City</a> in the <a href=\"https://wikipedia.org/wiki/Palestinian_territories\" class=\"mw-redirect\" title=\"Palestinian territories\">Palestinian territory</a> of the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> where more than 100 Palestinians were killed and over 750 were wounded after Israeli forces opened fire on Palestinians waiting for humanitarian aid amidst the <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Hamas_war\" class=\"mw-redirect\" title=\"Israel-Hamas war\">Israel-Hamas war</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Flour_massacre\" title=\"Flour massacre\">Flour massacre</a> (also known as the Al-Rashid massacre) took place on al-Rashid street at the Al-Nabulsi roundabout to the west of <a href=\"https://wikipedia.org/wiki/Gaza_City\" title=\"Gaza City\">Gaza City</a> in the <a href=\"https://wikipedia.org/wiki/Palestinian_territories\" class=\"mw-redirect\" title=\"Palestinian territories\">Palestinian territory</a> of the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> where more than 100 Palestinians were killed and over 750 were wounded after Israeli forces opened fire on Palestinians waiting for humanitarian aid amidst the <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Hamas_war\" class=\"mw-redirect\" title=\"Israel-Hamas war\">Israel-Hamas war</a>.", "links": [{"title": "Flour massacre", "link": "https://wikipedia.org/wiki/Flour_massacre"}, {"title": "Gaza City", "link": "https://wikipedia.org/wiki/Gaza_City"}, {"title": "Palestinian territories", "link": "https://wikipedia.org/wiki/Palestinian_territories"}, {"title": "Gaza Strip", "link": "https://wikipedia.org/wiki/Gaza_Strip"}, {"title": "Israel-Hamas war", "link": "https://wikipedia.org/wiki/Israel%E2%80%93Hamas_war"}]}], "Births": [{"year": "1468", "text": "<PERSON> (d. 1549)", "html": "1468 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> (d. 1549)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1549)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1528", "text": "<PERSON>, Duke of Bavaria (d. 1579)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1579)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1528", "text": "<PERSON>, Spanish theologian (d. 1604)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/Domingo_B%C3%A1%C3%B1ez\" title=\"<PERSON>\"><PERSON></a>, Spanish theologian (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Domingo_B%C3%A1%C3%B1ez\" title=\"<PERSON>\"><PERSON></a>, Spanish theologian (d. 1604)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Domingo_B%C3%A1%C3%B1ez"}]}, {"year": "1572", "text": "<PERSON>, 1st Viscount <PERSON> (d. 1638)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a> (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a> (d. 1638)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1576", "text": "<PERSON>, Florentine priest and glassmaker (d. 1614)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Florentine priest and glassmaker (d. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Florentine priest and glassmaker (d. 1614)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1640", "text": "<PERSON>, Particular Baptist preacher and author whose name is given to <PERSON><PERSON>'s Catechism (d. 1704)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Particular_Baptist\" class=\"mw-redirect\" title=\"Particular Baptist\">Particular Baptist</a> preacher and author whose name is given to <a href=\"https://wikipedia.org/wiki/Ke<PERSON>%27s_Catechism\" title=\"<PERSON><PERSON>'s Catechism\"><PERSON><PERSON>'s Catechism</a> (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Particular_Baptist\" class=\"mw-redirect\" title=\"Particular Baptist\">Particular Baptist</a> preacher and author whose name is given to <a href=\"https://wikipedia.org/wiki/Keach%27s_Catechism\" title=\"<PERSON><PERSON>'s Catechism\"><PERSON><PERSON>'s Catechism</a> (d. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Particular Baptist", "link": "https://wikipedia.org/wiki/Particular_Baptist"}, {"title": "<PERSON><PERSON>'s Catechism", "link": "https://wikipedia.org/wiki/Keach%27s_Catechism"}]}, {"year": "1692", "text": "<PERSON>, English poet and educator (d. 1763)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and educator (d. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and educator (d. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, Austrian-English dancer (d. 1822)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English dancer (d. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English dancer (d. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1736", "text": "<PERSON>, English-American religious leader, founder of the Shakers (d. 1784)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American religious leader, founder of the <a href=\"https://wikipedia.org/wiki/Shakers\" title=\"Shakers\">Shakers</a> (d. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American religious leader, founder of the <a href=\"https://wikipedia.org/wiki/Shakers\" title=\"Shakers\">Shakers</a> (d. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shakers", "link": "https://wikipedia.org/wiki/Shakers"}]}, {"year": "1792", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer (d. 1868)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (d. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>achi<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, Scottish-Australian soldier and politician, eighth Premier of Tasmania (d. 1880)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian soldier and politician, eighth <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian soldier and politician, eighth <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1828", "text": "<PERSON><PERSON><PERSON>, American journalist, poet and activist (d. 1921)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American journalist, poet and activist (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American journalist, poet and activist (d. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON><PERSON>, American baseball player and manager (d. 1908)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, German ophthalmologist (d. 1917)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Ophthalmologist\" class=\"mw-redirect\" title=\"Ophthalmologist\">ophthalmologist</a> (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Ophthalmologist\" class=\"mw-redirect\" title=\"Ophthalmologist\">ophthalmologist</a> (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ophthalmologist", "link": "https://wikipedia.org/wiki/Ophthalmologist"}]}, {"year": "1852", "text": "<PERSON>, Irish-Australian lawyer and judge, fourth Chief Justice of Australia (d. 1936)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian lawyer and judge, fourth <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Australia\" title=\"Chief Justice of Australia\">Chief Justice of Australia</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian lawyer and judge, fourth <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Australia\" title=\"Chief Justice of Australia\">Chief Justice of Australia</a> (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Justice of Australia", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Australia"}]}, {"year": "1852", "text": "<PERSON> <PERSON>, 6th Duke of Leuchtenberg (d. 1912)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Duke_of_Leuchtenberg\" title=\"<PERSON>, 6th Duke of Leuchtenberg\">Prince <PERSON>, 6th Duke of Leuchtenberg</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Duke_of_Leuchtenberg\" title=\"<PERSON>, 6th Duke of Leuchtenberg\">Prince <PERSON>, 6th Duke of Leuchtenberg</a> (d. 1912)", "links": [{"title": "<PERSON>, 6th Duke of Leuchtenberg", "link": "https://wikipedia.org/wiki/<PERSON>,_6th_Duke_of_Leuchtenberg"}]}, {"year": "1860", "text": "<PERSON>, American statistician and businessman, co-founder of the Computing-Tabulating-Recording Company (d. 1929)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician and businessman, co-founder of the <a href=\"https://wikipedia.org/wiki/Computing-Tabulating-Recording_Company\" title=\"Computing-Tabulating-Recording Company\">Computing-Tabulating-Recording Company</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician and businessman, co-founder of the <a href=\"https://wikipedia.org/wiki/Computing-Tabulating-Recording_Company\" title=\"Computing-Tabulating-Recording Company\">Computing-Tabulating-Recording Company</a> (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Computing-Tabulating-Recording Company", "link": "https://wikipedia.org/wiki/Computing-Tabulating-Recording_Company"}]}, {"year": "1884", "text": "<PERSON>, American lawyer and politician (d. 1941)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American sculptor (d. 1962)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Augusta_Savage\" title=\"Augusta Savage\"><PERSON></a>, American sculptor (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta_Savage\" title=\"Augusta Savage\"><PERSON></a>, American sculptor (d. 1962)", "links": [{"title": "Augusta Savage", "link": "https://wikipedia.org/wiki/Augusta_Savage"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Indian civil servant and politician, fourth Prime Minister of India (d. 1995)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian civil servant and politician, fourth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian civil servant and politician, fourth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1896", "text": "<PERSON>, American actor, director, producer and screenwriter (d. 1975)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer and screenwriter (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer and screenwriter (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American saxophonist, composer and bandleader (d. 1957)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer and bandleader (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer and bandleader (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American baseball player and manager (d. 1965)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Martin\"><PERSON></a>, American baseball player and manager (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pepper Martin\"><PERSON></a>, American baseball player and manager (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, French-Swiss painter and illustrator (d. 2001)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Balthus\" title=\"Balthus\"><PERSON><PERSON><PERSON></a>, French-Swiss painter and illustrator (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Balthus\" title=\"Balthus\"><PERSON><PERSON><PERSON></a>, French-Swiss painter and illustrator (d. 2001)", "links": [{"title": "Balthus", "link": "https://wikipedia.org/wiki/Balthus"}]}, {"year": "1908", "text": "<PERSON>, American historian and author (d. 2002)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American historian and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American historian and author (d. 2002)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1908", "text": "<PERSON><PERSON>, English cricketer and coach (d. 2001)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>f_Gover\" title=\"<PERSON>f Gover\"><PERSON><PERSON></a>, English cricketer and coach (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>f Gover\"><PERSON><PERSON></a>, English cricketer and coach (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alf_Gover"}]}, {"year": "1908", "text": "<PERSON>, Welsh writer (d. 1968)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh writer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh writer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Turkish industrialist (d. 1978)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON>l_Tolon\" title=\"Ka<PERSON><PERSON> Tolon\"><PERSON><PERSON><PERSON></a>, Turkish industrialist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON>l_Tolon\" title=\"Ka<PERSON><PERSON> Tolon\"><PERSON><PERSON><PERSON></a>, Turkish industrialist (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lon"}]}, {"year": "1916", "text": "<PERSON>, American lawyer (d. 1970)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, founder of U-Haul Corp. (d. 1999)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of <a href=\"https://wikipedia.org/wiki/U-Haul\" title=\"U-Haul\">U-Haul</a> Corp. (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of <a href=\"https://wikipedia.org/wiki/U-Haul\" title=\"U-Haul\">U-Haul</a> Corp. (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}, {"title": "U-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U-<PERSON>ul"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Russian author and critic (d. 1983)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author and critic (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author and critic (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor (d. 2006)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor and dancer (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and dancer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and dancer (d. 2010)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, French-American actress and singer (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Mich%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-American actress and singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mich%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-American actress and singer (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mich%C3%A8le_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American lawyer and politician (d. 2011)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Rolland_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>and <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rolland_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>and <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician (d. 2011)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rolland_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, New Zealand judge and politician, 14th Governor-General of New Zealand (d. 2001)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand judge and politician, 14th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand judge and politician, 14th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}]}, {"year": "1924", "text": "<PERSON>, Salvadoran politician, President of El Salvador (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran politician, <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran politician, <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of El Salvador", "link": "https://wikipedia.org/wiki/President_of_El_Salvador"}]}, {"year": "1924", "text": "<PERSON>, American baseball player and manager (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Rosen"}]}, {"year": "1928", "text": "<PERSON><PERSON>, English actor (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>land\"><PERSON><PERSON></a>, English actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> A<PERSON>land\"><PERSON><PERSON></a>, English actor (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1928", "text": "<PERSON>, British writer and illustrator (<PERSON><PERSON> and Tim) (d. 2024)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer and illustrator (<i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a></i>) (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer and illustrator (<i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a></i>) (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American archaeologist, geologist and author", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist, geologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist, geologist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English Anglican suffragan bishop (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Anglican suffragan bishop (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Anglican suffragan bishop (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, South African mathematician and computer scientist, co-creator of the Logo programming language (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African mathematician and computer scientist, co-creator of the <a href=\"https://wikipedia.org/wiki/Logo_(programming_language)\" title=\"Logo (programming language)\">Logo programming language</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African mathematician and computer scientist, co-creator of the <a href=\"https://wikipedia.org/wiki/Logo_(programming_language)\" title=\"Logo (programming language)\">Logo programming language</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}, {"title": "Logo (programming language)", "link": "https://wikipedia.org/wiki/Logo_(programming_language)"}]}, {"year": "1928", "text": "<PERSON><PERSON>, born <PERSON>, \"The Queen Of Exotic Dancers\", American burlesque performer and actress (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Tempest_Storm\" title=\"Tempest Storm\">Tempest Storm</a>, born <PERSON>, \"The Queen Of Exotic Dancers\", American burlesque performer and actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tempest_Storm\" title=\"Tempest Storm\">Tempest Storm</a>, born <PERSON>, \"The Queen Of Exotic Dancers\", American burlesque performer and actress (d. 2021)", "links": [{"title": "Tempest Storm", "link": "https://wikipedia.org/wiki/Tempest_Storm"}]}, {"year": "1932", "text": "<PERSON>, American mathematician and academic (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._Golub"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American race car driver (d. 1985)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American soprano and actress", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Brazilian cartoonist", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Brazilian cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Brazilian cartoonist", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cartoonist)"}]}, {"year": "1932", "text": "<PERSON>, Australian cricketer", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Indonesian writer (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Nh._<PERSON><PERSON>\" title=\"Nh. Dini\"><PERSON><PERSON><PERSON> <PERSON></a>, Indonesian writer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nh._<PERSON><PERSON>\" title=\"Nh. Dini\"><PERSON><PERSON><PERSON> <PERSON></a>, Indonesian writer (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Nh._<PERSON>i"}]}, {"year": "1936", "text": "<PERSON>, American colonel, astronaut and politician", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, astronaut and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, astronaut and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Canadian ice hockey player (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Dutch talk show host", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch talk show host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Barend"}]}, {"year": "1940", "text": "<PERSON>, current Ecumenical Patriarch of Constantinople.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_<PERSON>_of_Constantinople\" title=\"Ecumenical Patriarch Bartholomew of Constantinople\"><PERSON> I</a>, current <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople\" title=\"Ecumenical Patriarch of Constantinople\">Ecumenical Patriarch of Constantinople</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_<PERSON>_of_Constantinople\" title=\"Ecumenical Patriarch Bartholomew of Constantinople\"><PERSON> I</a>, current <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople\" title=\"Ecumenical Patriarch of Constantinople\">Ecumenical Patriarch of Constantinople</a>.", "links": [{"title": "Ecumenical Patriarch <PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Ecumenical_Patriarch_<PERSON>_of_Constantinople"}, {"title": "Ecumenical Patriarch of Constantinople", "link": "https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople"}]}, {"year": "1944", "text": "<PERSON>, American police officer and actor (d. 2013)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English priest and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actress (d. 2014)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American baseball player (d. 2008)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Italian author and illustrator", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Eleuteri_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Swedish ice hockey player (d. 1972).", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>berg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player (d. 1972).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>vedberg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player (d. 1972).", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lennart_Svedberg"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Iranian actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, English author, critic and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author, critic and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>mio<PERSON>\"><PERSON><PERSON><PERSON></a>, English author, critic and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American author (d. 2022)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Central African basketball player and politician, Minister of Equipment and Transport (2003-2005)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Pokomandji\" title=\"<PERSON>\"><PERSON></a>, Central African basketball player and politician, Minister of Equipment and Transport (2003-2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Pokomandji\" title=\"<PERSON>\"><PERSON></a>, Central African basketball player and politician, Minister of Equipment and Transport (2003-2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sonny_M%27Pokomandji"}]}, {"year": "1952", "text": "<PERSON>, American author and educator", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Russian cross-country skier", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Smetanina\" title=\"<PERSON><PERSON> Smetanina\"><PERSON><PERSON></a>, Russian cross-country skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>na\" title=\"<PERSON><PERSON> Smetanina\"><PERSON><PERSON></a>, Russian cross-country skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Raisa_Smetanina"}]}, {"year": "1952", "text": "<PERSON>, American police officer and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Swedish singer, actor and comedian", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Knut_Agnred\" title=\"Knut Agnred\"><PERSON><PERSON></a>, Swedish singer, actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Agnred\" title=\"Knut Agnred\"><PERSON><PERSON></a>, Swedish singer, actor and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K<PERSON>_Agnred"}]}, {"year": "1956", "text": "<PERSON>, English-Australian radio and television host (d. 2021)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(presenter)\" title=\"<PERSON> (presenter)\"><PERSON></a>, English-Australian radio and television host (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(presenter)\" title=\"<PERSON> (presenter)\"><PERSON></a>, English-Australian radio and television host (d. 2021)", "links": [{"title": "<PERSON> (presenter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(presenter)"}]}, {"year": "1956", "text": "<PERSON>, Canadian businessman and politician, 30th Canadian Minister of Agriculture (d. 2021)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 30th <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)\" class=\"mw-redirect\" title=\"Minister of Agriculture (Canada)\">Canadian Minister of Agriculture</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 30th <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)\" class=\"mw-redirect\" title=\"Minister of Agriculture (Canada)\">Canadian Minister of Agriculture</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Agriculture (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American serial killer (d. 2002)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American serial killer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American serial killer (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Algerian singer-songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Algerian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Algerian singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)"}]}, {"year": "1960", "text": "<PERSON>, American serial killer and sex offender (d. 2013)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and sex offender (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and sex offender (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American author and motivational activist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and motivational activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and motivational activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English cyclist and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, former Maltese footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Carmel_Busuttil\" title=\"Carmel Busuttil\"><PERSON></a>, former Maltese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carmel_Busuttil\" title=\"Carmel Busuttil\"><PERSON></a>, former Maltese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmel_Busuttil"}]}, {"year": "1964", "text": "<PERSON>, Canadian ice hockey player and radio host", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American tenor, composer and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tenor, composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tenor, composer and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, New Zealand composer and percussionist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand composer and percussionist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand composer and percussionist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American curler", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American football player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American author and illustrator", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Ukrainian-American lawyer and educator", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American lawyer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American lawyer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eugene_Volokh"}]}, {"year": "1968", "text": "<PERSON>, Australian actor, producer and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, producer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, producer and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Italian showgirl", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian showgirl", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian showgirl", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yl<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English footballer and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Italian-American model and actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_Jr.\" title=\"<PERSON>.\"><PERSON>.</a>, Italian-American model and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_Jr.\" title=\"<PERSON>\"><PERSON>.</a>, Italian-American model and actor", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A0to_Jr."}]}, {"year": "1972", "text": "<PERSON>, Prime Minister of Spain", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Pedro_<PERSON>%C3%<PERSON><PERSON><PERSON>_(Spanish_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Spanish politician)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_S%C3%<PERSON><PERSON><PERSON>_(Spanish_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Spanish politician)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a>", "links": [{"title": "<PERSON> (Spanish politician)", "link": "https://wikipedia.org/wiki/Pedro_S%C3%<PERSON><PERSON><PERSON>_(Spanish_politician)"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "1972", "text": "<PERSON>, American singer (d. 2002)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (d. 2002)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Cuban-American activist and educator (d. 1994)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American activist and educator (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American activist and educator (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Hungarian sprint kayaker", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kov%C3%A1cs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian sprint kayaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kov%C3%A1cs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian sprint kayaker", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>alin_Kov%C3%A1cs"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Te<PERSON>_<PERSON>\" title=\"Terrence <PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Terrence <PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Te<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American rapper and actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Ja_<PERSON>\" title=\"Ja Rule\"><PERSON><PERSON> <PERSON></a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ja_<PERSON>\" title=\"Ja Rule\"><PERSON><PERSON> <PERSON></a>, American rapper and actor", "links": [{"title": "<PERSON>a <PERSON>", "link": "https://wikipedia.org/wiki/Ja_Rule"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish footballer and coach", "html": "1980 - <a href=\"https://wikipedia.org/wiki/%C3%87a%C4%9Fda%C5%9F_<PERSON><PERSON>\" title=\"Çağdaş Atan\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%87a%C4%9Fda%C5%9F_<PERSON><PERSON>\" title=\"Çağdaş Atan\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and coach", "links": [{"title": "Çağdaş Atan", "link": "https://wikipedia.org/wiki/%C3%87a%C4%9Fda%C5%9F_Atan"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Simon_Gagn%C3%A9"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Spanish cyclist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Plaza\" title=\"Rubén Plaza\">Rubén Plaza</a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Plaza\" title=\"Rubén Plaza\">R<PERSON><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Plaza"}]}, {"year": "1980", "text": "<PERSON>, New Zealand rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American soccer player and sportscaster", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>wellman\"><PERSON></a>, American soccer player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor (Law & Order: Special Victims Unit)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (<i><a href=\"https://wikipedia.org/wiki/Law_%26_Order:_Special_Victims_Unit\" title=\"Law &amp; Order: Special Victims Unit\">Law &amp; Order: Special Victims Unit</a></i>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (<i><a href=\"https://wikipedia.org/wiki/Law_%26_Order:_Special_Victims_Unit\" title=\"Law &amp; Order: Special Victims Unit\">Law &amp; Order: Special Victims Unit</a></i>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Law & Order: Special Victims Unit", "link": "https://wikipedia.org/wiki/Law_%26_Order:_Special_Victims_Unit"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Japanese model and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Rica_Imai\" title=\"Rica Imai\"><PERSON></a>, Japanese model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rica_Imai\" title=\"Rica Imai\">Rica Imai</a>, Japanese model and actress", "links": [{"title": "Rica Imai", "link": "https://wikipedia.org/wiki/Rica_Imai"}]}, {"year": "1984", "text": "<PERSON>, American swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Spanish basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Nuria_Mart%C3%ADnez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uria_Mart%C3%ADnez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nuria_Mart%C3%ADnez"}]}, {"year": "1984", "text": "<PERSON>, American video game composer and producer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game composer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/Cam_Ward_(ice_hockey)"}]}, {"year": "1984", "text": "<PERSON>, American singer, songwriter and musician", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer, songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer, songwriter and musician", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1988", "text": "<PERSON>, German model and television host", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German model and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German model and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, German footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Benedikt_H%C3%B6wedes\" title=\"Benedikt Höwed<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Benedikt_H%C3%B6wedes\" title=\"Benedikt Höwed<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON>ed<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Benedikt_H%C3%B6wedes"}]}, {"year": "1988", "text": "<PERSON>, Australian Rules footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian Rules footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian Rules footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Welsh sports sailor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sports sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sports sailor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian cricketer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American paralympic swimmer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Paralympic_swimming\" class=\"mw-redirect\" title=\"Paralympic swimming\">paralympic swimmer</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Paralympic_swimming\" class=\"mw-redirect\" title=\"Paralympic swimming\">paralympic swimmer</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Paralympic swimming", "link": "https://wikipedia.org/wiki/Paralympic_swimming"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON> T<PERSON>her\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>her"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Algerian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Saphir_Ta%C3%AFder\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saphir_Ta%C3%AFder\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saphir_Ta%C3%AFder"}]}, {"year": "1996", "text": "<PERSON><PERSON>, New Zealand rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Argentine-Armenian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine-Armenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine-Armenian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, British sprinter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>d\"><PERSON></a>, British sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>d\"><PERSON></a>, British sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>scod"}]}, {"year": "1996", "text": "<PERSON>, New Zealand tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, New Zealand tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, New Zealand tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Tyrese_Haliburton\" title=\"Tyrese Haliburton\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tyrese_<PERSON>ib<PERSON>on\" title=\"Tyrese Haliburton\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tyrese_Haliburton"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Danish footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Je<PERSON>_<PERSON>tr%C3%B8m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B8m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lindstr%C3%B8m"}]}, {"year": "2004", "text": "<PERSON>, American swimmer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, Uzbek footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Uzbek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Uzbek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "468", "text": "<PERSON>", "html": "468 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>larius\" title=\"<PERSON> Hilarius\">Pope <PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lar<PERSON>\" title=\"<PERSON> Hilarius\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>larius"}]}, {"year": "992", "text": "<PERSON> of Worcester, Anglo-Saxon archbishop and saint (b. 925)", "html": "992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Worcester\" title=\"<PERSON> of Worcester\"><PERSON> of Worcester</a>, Anglo-Saxon archbishop and saint (b. 925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Worcester\" title=\"<PERSON> of Worcester\"><PERSON> of Worcester</a>, Anglo-Saxon archbishop and saint (b. 925)", "links": [{"title": "<PERSON> of Worcester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Worcester"}]}, {"year": "1460", "text": "<PERSON>, Duke of Bavaria-Munich (b. 1401)", "html": "1460 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, Duke of Bavaria-Munich (b. 1401)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, Duke of Bavaria-Munich (b. 1401)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1528", "text": "<PERSON>, Scottish Protestant reformer and martyr (b. 1504)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, Scottish Protestant reformer and martyr (b. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, Scottish Protestant reformer and martyr (b. 1504)", "links": [{"title": "<PERSON> (martyr)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)"}]}, {"year": "1592", "text": "<PERSON>, Italian composer and diplomat (b. 1536/1537)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and diplomat (b. 1536/1537)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and diplomat (b. 1536/1537)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1600", "text": "<PERSON><PERSON><PERSON>, German pastor, historian and cartographer (b. 1529)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pastor, historian and cartographer (b. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pastor, historian and cartographer (b. 1529)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1604", "text": "<PERSON>, English archbishop and academic (b. 1530)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and academic (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and academic (b. 1530)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>gift"}]}, {"year": "1712", "text": "<PERSON>, Swiss anatomist (b. 1653)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss anatomist (b. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss anatomist (b. 1653)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON>, French-English physicist and philosopher (b. 1683)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"John <PERSON>phi<PERSON>\"><PERSON></a>, French-English physicist and philosopher (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English physicist and philosopher (b. 1683)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Desagu<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, German piano builder (b. 1728)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German piano builder (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German piano builder (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, German historian and critic (b. 1743)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and critic (b. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and critic (b. 1743)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON>, French general, painter and lithographer (b. 1775)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A7<PERSON>_Le<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general, painter and lithographer (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7<PERSON>_Le<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general, painter and lithographer (b. 1775)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A7<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, French Christian missionary (b. 1814)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Christian <a href=\"https://wikipedia.org/wiki/Missionary\" title=\"Missionary\">missionary</a> (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Christian <a href=\"https://wikipedia.org/wiki/Missionary\" title=\"Missionary\">missionary</a> (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Missionary"}]}, {"year": "1868", "text": "<PERSON> of Bavaria (b. 1786)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Ludwig_I_of_Bavaria\" title=\"<PERSON> I of Bavaria\"><PERSON> of Bavaria</a> (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludwig_I_of_Bavaria\" title=\"<PERSON> I of Bavaria\"><PERSON> of Bavaria</a> (b. 1786)", "links": [{"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/Ludwig_I_of_Bavaria"}]}, {"year": "1880", "text": "<PERSON>, Scottish-Australian soldier and politician, 8th Premier of Tasmania (b. 1812)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1904", "text": "<PERSON>, Irish-Australian politician (b. 1818)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27S<PERSON>iva<PERSON>_(Queensland_politician)\" title=\"<PERSON> (Queensland politician)\"><PERSON></a>, Irish-Australian politician (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27S<PERSON><PERSON><PERSON>_(Queensland_politician)\" title=\"<PERSON> (Queensland politician)\"><PERSON></a>, Irish-Australian politician (b. 1818)", "links": [{"title": "<PERSON> (Queensland politician)", "link": "https://wikipedia.org/wiki/<PERSON>_O%27S<PERSON><PERSON><PERSON>_(Queensland_politician)"}]}, {"year": "1904", "text": "<PERSON>, French astronomer (b. 1845)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American sheriff (b. 1850)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sheriff (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sheriff (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, 1st Marquess of Linlithgow, Scottish-Australian politician, 1st Governor-General of Australia (b. 1860)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Linlithgow\" class=\"mw-redirect\" title=\"<PERSON>, 1st Marquess of Linlithgow\"><PERSON>, 1st Marquess of Linlithgow</a>, Scottish-Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Linlithgow\" class=\"mw-redirect\" title=\"<PERSON>, 1st Marquess of Linlithgow\"><PERSON>, 1st Marquess of Linlithgow</a>, Scottish-Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (b. 1860)", "links": [{"title": "<PERSON>, 1st Marquess of Linlithgow", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Linlithgow"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}]}, {"year": "1916", "text": "<PERSON>, English-Australian journalist and politician (b. 1863)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian journalist and politician (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian journalist and politician (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American baseball player (b. 1875)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Australian educator (b. 1845)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Swiss architect and theorist (b. 1862)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss architect and theorist (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss architect and theorist (b. 1862)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American poet and librarian (b. 1841)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Ina_<PERSON>th\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and librarian (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/In<PERSON>_Cool<PERSON>th\" title=\"In<PERSON> Coolbrith\"><PERSON><PERSON></a>, American poet and librarian (b. 1841)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>th"}]}, {"year": "1932", "text": "<PERSON>, Australian entomologist (b. 1868)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian entomologist (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian entomologist (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arthur_Mills_Lea"}]}, {"year": "1932", "text": "<PERSON>, Italian mathematician (b. 1875)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON> <PERSON><PERSON>, English archaeologist and author (b. 1867)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English archaeologist and author (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English archaeologist and author (b. 1867)", "links": [{"title": "E<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Finnish lawyer, judge and politician, 3rd President of Finland (b. 1861)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>hufvud\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish lawyer, judge and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>fvud\" title=\"<PERSON><PERSON><PERSON>vu<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish lawyer, judge and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (b. 1861)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>d"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1948", "text": "<PERSON>-<PERSON>, English lawyer and journalist (b. 1891)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Robert_<PERSON>-Ward\" title=\"<PERSON>Ward\"><PERSON></a>, English lawyer and journalist (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Robert_<PERSON>-Ward\" title=\"<PERSON>-Ward\"><PERSON></a>, English lawyer and journalist (b. 1891)", "links": [{"title": "<PERSON>-Ward", "link": "https://wikipedia.org/wiki/Robert_Barrington-Ward"}]}, {"year": "1952", "text": "<PERSON>, Australian entrepreneur (b. 1865)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian entrepreneur (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian entrepreneur (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Filipino lawyer and politician, 6th President of the Philippines (b. 1890)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1960", "text": "<PERSON>, American police officer and FBI agent (b. 1903)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agent (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agent (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "FBI", "link": "https://wikipedia.org/wiki/FBI"}]}, {"year": "1960", "text": "<PERSON>, American journalist and author (b. 1894)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor and singer (b. 1909)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Norwegian poet and educator (b. 1886)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Tore_%C3%98rjas%C3%A6ter\" title=\"Tor<PERSON> Ørjas<PERSON>\"><PERSON><PERSON></a>, Norwegian poet and educator (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tore_%C3%98rjas%C3%A6ter\" title=\"Tor<PERSON> Ørjas<PERSON>ter\"><PERSON><PERSON></a>, Norwegian poet and educator (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tore_%C3%98rjas%C3%A6ter"}]}, {"year": "1972", "text": "<PERSON>, American football player and coach (b. 1896)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1896)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1976", "text": "<PERSON>, American politician (b. 1902)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Florence_<PERSON>_<PERSON>\" title=\"Florence <PERSON>\"><PERSON> <PERSON></a>, American politician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_P<PERSON>_<PERSON>\" title=\"Florence P<PERSON>\"><PERSON> <PERSON></a>, American politician (b. 1902)", "links": [{"title": "Florence <PERSON>", "link": "https://wikipedia.org/wiki/Florence_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Israeli general and politician, Prime Minister of Israel (b. 1918)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>on"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}]}, {"year": "1980", "text": "<PERSON>, American painter and illustrator (b. 1914)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish screenwriter and songwriter (b. 1903)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>dwik_<PERSON>ki\" title=\"Ludwik <PERSON>ki\"><PERSON><PERSON><PERSON><PERSON></a>, Polish screenwriter and songwriter (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ki\" title=\"Ludwik <PERSON>ki\"><PERSON><PERSON><PERSON><PERSON></a>, Polish screenwriter and songwriter (b. 1903)", "links": [{"title": "Ludwik <PERSON>ki", "link": "https://wikipedia.org/wiki/<PERSON>dwik_<PERSON>ki"}]}, {"year": "1992", "text": "<PERSON>, English poet and author (b. 1897)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Czech-American director, producer and screenwriter (b. 1926)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American director, producer and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Daniel\"><PERSON></a>, Czech-American director, producer and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American singer-songwriter and producer (b. 1939)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American baseball player, coach and manager (b. 1924)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach and manager (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach and manager (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American guitarist (b. 1961)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 42nd <PERSON><PERSON><PERSON><PERSON> (b. 1923)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Kagamisato_Kiyoji\" title=\"Kagamisato Kiyoji\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 42nd <a href=\"https://wikipedia.org/wiki/Yokozuna\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kagami<PERSON><PERSON>_Kiyoji\" title=\"Kagamisat<PERSON> Kiyoji\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 42nd <a href=\"https://wikipedia.org/wiki/Yoko<PERSON>na\" class=\"mw-redirect\" title=\"Yo<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kagamisato_Ki<PERSON>ji"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yokozuna"}]}, {"year": "2004", "text": "<PERSON>, American playwright and author (b. 1915)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Barbadian lawyer and politician, 3rd Prime Minister of Barbados (b. 1931)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>._John\" title=\"Harold Bernard St. John\"><PERSON>. John</a>, Barbadian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Barbados\" title=\"Prime Minister of Barbados\">Prime Minister of Barbados</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_John\" title=\"Harold Bernard St. John\"><PERSON>. John</a>, Barbadian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Barbados\" title=\"Prime Minister of Barbados\">Prime Minister of Barbados</a> (b. 1931)", "links": [{"title": "<PERSON>. John", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>._John"}, {"title": "Prime Minister of Barbados", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Barbados"}]}, {"year": "2004", "text": "<PERSON><PERSON>, South African cricketer (b. 1943)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wilmot\" title=\"Lorrie Wilmot\"><PERSON><PERSON></a>, South African cricketer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wilmot\" title=\"Lorrie Wilmot\"><PERSON><PERSON></a>, South African cricketer (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wilmot"}]}, {"year": "2008", "text": "<PERSON>, American author (b. 1946)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Danish painter and illustrator (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and illustrator (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and illustrator (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Japanese scholar and philosopher (b. 1922)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese scholar and philosopher (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese scholar and philosopher (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English singer, guitarist and actor (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer, guitarist and actor (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer, guitarist and actor (b. 1945)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2012", "text": "<PERSON>, American illustrator (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian social leader (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian social leader (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian social leader (b. 1930)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Filipino director and screenwriter (b. 1966)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Wen<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Filipino director and screenwriter (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Filipino director and screenwriter (b. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wen<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American police officer, actor and politician (b. 1931)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gil Hill\"><PERSON></a>, American police officer, actor and politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gil Hill\"><PERSON></a>, American police officer, actor and politician (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gil_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Swedish singer (b. 1969)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer (b. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Pakistani assassin, executed (b. 1985)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani assassin, executed (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani assassin, executed (b. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English author (b. 1951)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, German actor (b. 1942)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Hungarian Hall of Fame swimmer and 1952 Olympic champion (b. 1927)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/%C3%89va_Sz%C3%A9kely\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian <a href=\"https://wikipedia.org/wiki/International_Swimming_Hall_of_Fame\" title=\"International Swimming Hall of Fame\">Hall of Fame</a> swimmer and <a href=\"https://wikipedia.org/wiki/Swimming_at_the_1952_Summer_Olympics_%E2%80%93_Women%27s_200_metre_breaststroke\" title=\"Swimming at the 1952 Summer Olympics - Women's 200 metre breaststroke\">1952 Olympic champion</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89va_Sz%C3%A9kely\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian <a href=\"https://wikipedia.org/wiki/International_Swimming_Hall_of_Fame\" title=\"International Swimming Hall of Fame\">Hall of Fame</a> swimmer and <a href=\"https://wikipedia.org/wiki/Swimming_at_the_1952_Summer_Olympics_%E2%80%93_Women%27s_200_metre_breaststroke\" title=\"Swimming at the 1952 Summer Olympics - Women's 200 metre breaststroke\">1952 Olympic champion</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89va_Sz%C3%A9kely"}, {"title": "International Swimming Hall of Fame", "link": "https://wikipedia.org/wiki/International_Swimming_Hall_of_Fame"}, {"title": "Swimming at the 1952 Summer Olympics - Women's 200 metre breaststroke", "link": "https://wikipedia.org/wiki/Swimming_at_the_1952_Summer_Olympics_%E2%80%93_Women%27s_200_metre_breaststroke"}]}, {"year": "2024", "text": "<PERSON>, 2nd President of Tanzania and 3rd President of Zanzibar (b. 1925)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024\" title=\"2024\">2024</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Tanzania\" class=\"mw-redirect\" title=\"President of Tanzania\">President of Tanzania</a> and 3rd <a href=\"https://wikipedia.org/wiki/President_of_Zanzibar\" title=\"President of Zanzibar\">President of Zanzibar</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024\" title=\"2024\">2024</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Tanzania\" class=\"mw-redirect\" title=\"President of Tanzania\">President of Tanzania</a> and 3rd <a href=\"https://wikipedia.org/wiki/President_of_Zanzibar\" title=\"President of Zanzibar\">President of Zanzibar</a> (b. 1925)", "links": [{"title": "2024", "link": "https://wikipedia.org/wiki/2024"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Tanzania", "link": "https://wikipedia.org/wiki/President_of_Tanzania"}, {"title": "President of Zanzibar", "link": "https://wikipedia.org/wiki/President_of_Zanzibar"}]}, {"year": "2024", "text": "<PERSON>, 18th Prime Minister of Canada (b. 1939)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 18th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 18th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}]}}