{"date": "January 29", "url": "https://wikipedia.org/wiki/January_29", "data": {"Events": [{"year": "904", "text": "<PERSON><PERSON><PERSON> is elected pope, after coming out of retirement to take over the papacy from the deposed antipope <PERSON>.", "html": "904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sergius_III\" title=\"Pope Sergius III\"><PERSON><PERSON><PERSON></a> is elected pope, after coming out of retirement to take over the <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">papacy</a> from the deposed <a href=\"https://wikipedia.org/wiki/Antipop<PERSON>_Christopher\" title=\"Antipop<PERSON> Christopher\">antipope <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Ser<PERSON>us_III\" title=\"Pope Sergius III\"><PERSON><PERSON><PERSON> III</a> is elected pope, after coming out of retirement to take over the <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">papacy</a> from the deposed <a href=\"https://wikipedia.org/wiki/Antipop<PERSON>_Christopher\" title=\"Antipop<PERSON> Christopher\">antipope <PERSON></a>.", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_Ser<PERSON><PERSON>_III"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "946", "text": "<PERSON><PERSON><PERSON> is blinded and deposed by <PERSON><PERSON><PERSON><PERSON>, ruler of the Buyid Empire. He is succeeded by <PERSON><PERSON><PERSON><PERSON> as caliph of the Abbasid Caliphate.", "html": "946 - <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Al-Must<PERSON>\" title=\"<PERSON>-<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is blinded and deposed by <a href=\"https://wikipedia.org/wiki/Mu%27iz<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, ruler of the <a href=\"https://wikipedia.org/wiki/Buyid_dynasty\" title=\"Buyid dynasty\">Buyid Empire</a>. He is succeeded by <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> as caliph of the <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\">Abbasid Caliphate</a>.", "no_year_html": "Caliph <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Al-<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is blinded and deposed by <a href=\"https://wikipedia.org/wiki/Mu%27izz_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, ruler of the <a href=\"https://wikipedia.org/wiki/Buyid_dynasty\" title=\"Buyid dynasty\">Buyid Empire</a>. He is succeeded by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> as caliph of the <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\">Abbasid Caliphate</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mu%27iz<PERSON>_<PERSON>-<PERSON>"}, {"title": "Buyid dynasty", "link": "https://wikipedia.org/wiki/Buyid_dynasty"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abbasid_Caliphate"}]}, {"year": "1814", "text": "War of the Sixth Coalition: France engages Russia and Prussia in the Battle of Brienne.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/War_of_the_Sixth_Coalition\" title=\"War of the Sixth Coalition\">War of the Sixth Coalition</a>: France engages Russia and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Brienne\" title=\"Battle of Brienne\">Battle of Brienne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Sixth_Coalition\" title=\"War of the Sixth Coalition\">War of the Sixth Coalition</a>: France engages Russia and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Brienne\" title=\"Battle of Brienne\">Battle of Brienne</a>.", "links": [{"title": "War of the Sixth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Sixth_Coalition"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "Battle of Brienne", "link": "https://wikipedia.org/wiki/Battle_of_Brienne"}]}, {"year": "1819", "text": "Stamford Raffles lands on the island of Singapore.", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Stamford_Raffles\" title=\"Stamford Raffles\">Stamford Raffles</a> lands on the island of <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stamford_Raffles\" title=\"Stamford Raffles\">Stamford Raffles</a> lands on the island of <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>.", "links": [{"title": "Stamford Raffles", "link": "https://wikipedia.org/wiki/Stamford_Raffles"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}]}, {"year": "1845", "text": "\"The Raven\" is published in The Evening Mirror in New York, the first publication with the name of the author, <PERSON>.", "html": "1845 - \"<a href=\"https://wikipedia.org/wiki/The_Raven\" title=\"The Raven\">The Raven</a>\" is published in <i><a href=\"https://wikipedia.org/wiki/The_Evening_Mirror\" class=\"mw-redirect\" title=\"The Evening Mirror\">The Evening Mirror</a></i> in New York, the first publication with the name of the author, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "\"<a href=\"https://wikipedia.org/wiki/The_Raven\" title=\"The Raven\">The Raven</a>\" is published in <i><a href=\"https://wikipedia.org/wiki/The_Evening_Mirror\" class=\"mw-redirect\" title=\"The Evening Mirror\">The Evening Mirror</a></i> in New York, the first publication with the name of the author, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "The Raven", "link": "https://wikipedia.org/wiki/The_Raven"}, {"title": "The Evening Mirror", "link": "https://wikipedia.org/wiki/The_Evening_Mirror"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON> introduces the Compromise of 1850 to the U.S. Congress.", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> introduces the <a href=\"https://wikipedia.org/wiki/Compromise_of_1850\" title=\"Compromise of 1850\">Compromise of 1850</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> introduces the <a href=\"https://wikipedia.org/wiki/Compromise_of_1850\" title=\"Compromise of 1850\">Compromise of 1850</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Compromise of 1850", "link": "https://wikipedia.org/wiki/Compromise_of_1850"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1856", "text": "Queen <PERSON> issues a Warrant under the Royal sign-manual that establishes the Victoria Cross to recognise acts of valour by British military personnel during the Crimean War.", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a> issues a <a href=\"https://wikipedia.org/wiki/Warrant_(law)\" title=\"Warrant (law)\">Warrant</a> under the <a href=\"https://wikipedia.org/wiki/Royal_sign-manual\" title=\"Royal sign-manual\">Royal sign-manual</a> that establishes the <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> to recognise acts of valour by <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> military personnel during the <a href=\"https://wikipedia.org/wiki/Crimean_War\" title=\"Crimean War\">Crimean War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen <PERSON>\">Queen <PERSON></a> issues a <a href=\"https://wikipedia.org/wiki/Warrant_(law)\" title=\"Warrant (law)\">Warrant</a> under the <a href=\"https://wikipedia.org/wiki/Royal_sign-manual\" title=\"Royal sign-manual\">Royal sign-manual</a> that establishes the <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> to recognise acts of valour by <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> military personnel during the <a href=\"https://wikipedia.org/wiki/Crimean_War\" title=\"Crimean War\">Crimean War</a>.", "links": [{"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}, {"title": "War<PERSON> (law)", "link": "https://wikipedia.org/wiki/Warrant_(law)"}, {"title": "Royal sign-manual", "link": "https://wikipedia.org/wiki/Royal_sign-manual"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}, {"title": "Crimean War", "link": "https://wikipedia.org/wiki/Crimean_War"}]}, {"year": "1861", "text": "Kansas is admitted as the 34th U.S. state.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Kansas\" title=\"Kansas\">Kansas</a> is admitted as the 34th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kansas\" title=\"Kansas\">Kansas</a> is admitted as the 34th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Kansas", "link": "https://wikipedia.org/wiki/Kansas"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1863", "text": "The Bear River Massacre: A detachment of California Volunteers led by Colonel <PERSON> engage the Shoshone at Bear River, Washington Territory, killing hundreds of men, women and children.", "html": "1863 - The <a href=\"https://wikipedia.org/wiki/Bear_River_Massacre\" title=\"Bear River Massacre\">Bear River Massacre</a>: A detachment of <a href=\"https://wikipedia.org/wiki/List_of_California_Civil_War_Union_units\" title=\"List of California Civil War Union units\">California Volunteers</a> led by Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> engage the <a href=\"https://wikipedia.org/wiki/Shoshone\" title=\"Shoshone\">Shoshone</a> at <a href=\"https://wikipedia.org/wiki/Bear_River_(Washington)\" title=\"Bear River (Washington)\">Bear River</a>, <a href=\"https://wikipedia.org/wiki/Washington_Territory\" title=\"Washington Territory\">Washington Territory</a>, killing hundreds of men, women and children.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bear_River_Massacre\" title=\"Bear River Massacre\">Bear River Massacre</a>: A detachment of <a href=\"https://wikipedia.org/wiki/List_of_California_Civil_War_Union_units\" title=\"List of California Civil War Union units\">California Volunteers</a> led by Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> engage the <a href=\"https://wikipedia.org/wiki/Shoshone\" title=\"Shoshone\">Shoshone</a> at <a href=\"https://wikipedia.org/wiki/Bear_River_(Washington)\" title=\"Bear River (Washington)\">Bear River</a>, <a href=\"https://wikipedia.org/wiki/Washington_Territory\" title=\"Washington Territory\">Washington Territory</a>, killing hundreds of men, women and children.", "links": [{"title": "Bear River Massacre", "link": "https://wikipedia.org/wiki/Bear_River_Massacre"}, {"title": "List of California Civil War Union units", "link": "https://wikipedia.org/wiki/List_of_California_Civil_War_Union_units"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Shoshone", "link": "https://wikipedia.org/wiki/Shoshone"}, {"title": "Bear River (Washington)", "link": "https://wikipedia.org/wiki/Bear_River_(Washington)"}, {"title": "Washington Territory", "link": "https://wikipedia.org/wiki/Washington_Territory"}]}, {"year": "1886", "text": "Karl Benz patents the first successful gasoline-driven automobile.", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Karl Benz\"><PERSON></a> patents the first successful <a href=\"https://wikipedia.org/wiki/Gasoline\" title=\"Gasoline\">gasoline</a>-driven automobile.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karl_<PERSON>\" class=\"mw-redirect\" title=\"Karl Benz\"><PERSON></a> patents the first successful <a href=\"https://wikipedia.org/wiki/Gasoline\" title=\"Gasoline\">gasoline</a>-driven automobile.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gasoline", "link": "https://wikipedia.org/wiki/Gasoline"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is proclaimed the last monarch and only queen regnant of the Kingdom of Hawaii.", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Lili%CA%B<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> is proclaimed the last monarch and only <a href=\"https://wikipedia.org/wiki/Queen_regnant\" title=\"Queen regnant\">queen regnant</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Kingdom of Hawaii\">Kingdom of Hawaii</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lili%CA%BB<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>kal<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> is proclaimed the last monarch and only <a href=\"https://wikipedia.org/wiki/Queen_regnant\" title=\"Queen regnant\">queen regnant</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Kingdom of Hawaii\">Kingdom of Hawaii</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lili%CA%<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "Queen regnant", "link": "https://wikipedia.org/wiki/Queen_regnant"}, {"title": "Kingdom of Hawaii", "link": "https://wikipedia.org/wiki/Kingdom_of_Hawaii"}]}, {"year": "1907", "text": "<PERSON> of Kansas becomes the first Native American U.S. Senator.", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Kansas\" title=\"Kansas\">Kansas</a> becomes the first Native American <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">U.S. Senator</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Kansas\" title=\"Kansas\">Kansas</a> becomes the first Native American <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">U.S. Senator</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kansas", "link": "https://wikipedia.org/wiki/Kansas"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}]}, {"year": "1911", "text": "Mexican Revolution: <PERSON><PERSON><PERSON><PERSON> is captured by the Mexican Liberal Party, igniting the Magonista rebellion of 1911.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>: <a href=\"https://wikipedia.org/wiki/Mexicali\" title=\"Mexicali\">Mexicali</a> is <a href=\"https://wikipedia.org/wiki/Capture_of_Mexicali\" title=\"Capture of Mexicali\">captured</a> by the <a href=\"https://wikipedia.org/wiki/Mexican_Liberal_Party\" title=\"Mexican Liberal Party\">Mexican Liberal Party</a>, igniting the <a href=\"https://wikipedia.org/wiki/Magonista_rebellion_of_1911\" title=\"Magonista rebellion of 1911\">Magonista rebellion of 1911</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>: <a href=\"https://wikipedia.org/wiki/Mexicali\" title=\"Mexicali\">Mexicali</a> is <a href=\"https://wikipedia.org/wiki/Capture_of_Mexicali\" title=\"Capture of Mexicali\">captured</a> by the <a href=\"https://wikipedia.org/wiki/Mexican_Liberal_Party\" title=\"Mexican Liberal Party\">Mexican Liberal Party</a>, igniting the <a href=\"https://wikipedia.org/wiki/Magonista_rebellion_of_1911\" title=\"Magonista rebellion of 1911\">Magonista rebellion of 1911</a>.", "links": [{"title": "Mexican Revolution", "link": "https://wikipedia.org/wiki/Mexican_Revolution"}, {"title": "Mexicali", "link": "https://wikipedia.org/wiki/Mexicali"}, {"title": "Capture of Mexicali", "link": "https://wikipedia.org/wiki/Capture_of_Mexicali"}, {"title": "Mexican Liberal Party", "link": "https://wikipedia.org/wiki/Mexican_Liberal_Party"}, {"title": "Magonista rebellion of 1911", "link": "https://wikipedia.org/wiki/Magonista_rebellion_of_1911"}]}, {"year": "1918", "text": "Ukrainian-Soviet War: The Bolshevik Red Army, on its way to besiege Kyiv, is met by a small group of military students at the Battle of Kruty.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Ukrainian%E2%80%93Soviet_War\" title=\"Ukrainian-Soviet War\">Ukrainian-Soviet War</a>: The <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>, on its way to besiege <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a>, is met by a small group of military students at the <a href=\"https://wikipedia.org/wiki/Battle_of_Kruty\" title=\"Battle of Kruty\">Battle of Kruty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ukrainian%E2%80%93Soviet_War\" title=\"Ukrainian-Soviet War\">Ukrainian-Soviet War</a>: The <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>, on its way to besiege <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a>, is met by a small group of military students at the <a href=\"https://wikipedia.org/wiki/Battle_of_Kruty\" title=\"Battle of Kruty\">Battle of Kruty</a>.", "links": [{"title": "Ukrainian-Soviet War", "link": "https://wikipedia.org/wiki/Ukrainian%E2%80%93Soviet_War"}, {"title": "Bolshevik", "link": "https://wikipedia.org/wiki/Bolshevik"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Kyiv", "link": "https://wikipedia.org/wiki/Kyiv"}, {"title": "Battle of Kruty", "link": "https://wikipedia.org/wiki/Battle_of_Kruty"}]}, {"year": "1918", "text": "Ukrainian-Soviet War: An armed uprising organized by the Bolsheviks in anticipation of the encroaching Red Army begins at the Kiev Arsenal, which will be put down six days later.", "html": "1918 - Ukrainian-Soviet War: An <a href=\"https://wikipedia.org/wiki/Kiev_Arsenal_January_Uprising\" title=\"Kiev Arsenal January Uprising\">armed uprising</a> organized by the <a href=\"https://wikipedia.org/wiki/Bolsheviks\" title=\"Bolsheviks\">Bolsheviks</a> in anticipation of the encroaching <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> begins at the <a href=\"https://wikipedia.org/wiki/Kiev_Arsenal\" class=\"mw-redirect\" title=\"Kiev Arsenal\">Kiev Arsenal</a>, which will be put down six days later.", "no_year_html": "Ukrainian-Soviet War: An <a href=\"https://wikipedia.org/wiki/Kiev_Arsenal_January_Uprising\" title=\"Kiev Arsenal January Uprising\">armed uprising</a> organized by the <a href=\"https://wikipedia.org/wiki/Bolsheviks\" title=\"Bolsheviks\">Bolsheviks</a> in anticipation of the encroaching <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> begins at the <a href=\"https://wikipedia.org/wiki/Kiev_Arsenal\" class=\"mw-redirect\" title=\"Kiev Arsenal\">Kiev Arsenal</a>, which will be put down six days later.", "links": [{"title": "Kiev Arsenal January Uprising", "link": "https://wikipedia.org/wiki/Kiev_Arsenal_January_Uprising"}, {"title": "Bolsheviks", "link": "https://wikipedia.org/wiki/Bolsheviks"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Kiev Arsenal", "link": "https://wikipedia.org/wiki/Kiev_Arsenal"}]}, {"year": "1936", "text": "The first inductees into the Baseball Hall of Fame are announced.", "html": "1936 - The <a href=\"https://wikipedia.org/wiki/List_of_members_of_the_Baseball_Hall_of_Fame\" title=\"List of members of the Baseball Hall of Fame\">first inductees</a> into the <a href=\"https://wikipedia.org/wiki/Baseball_Hall_of_Fame\" class=\"mw-redirect\" title=\"Baseball Hall of Fame\">Baseball Hall of Fame</a> are announced.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/List_of_members_of_the_Baseball_Hall_of_Fame\" title=\"List of members of the Baseball Hall of Fame\">first inductees</a> into the <a href=\"https://wikipedia.org/wiki/Baseball_Hall_of_Fame\" class=\"mw-redirect\" title=\"Baseball Hall of Fame\">Baseball Hall of Fame</a> are announced.", "links": [{"title": "List of members of the Baseball Hall of Fame", "link": "https://wikipedia.org/wiki/List_of_members_of_the_Baseball_Hall_of_Fame"}, {"title": "Baseball Hall of Fame", "link": "https://wikipedia.org/wiki/Baseball_Hall_of_Fame"}]}, {"year": "1940", "text": "Three trains on the Nishinari Line; present Sakurajima Line, in Osaka, Japan, collide and explode while approaching Ajikawaguchi Station. One hundred and eighty-one people are killed.", "html": "1940 - Three trains on the Nishinari Line; present <a href=\"https://wikipedia.org/wiki/Sakurajima_Line\" title=\"Sakurajima Line\">Sakurajima Line</a>, in <a href=\"https://wikipedia.org/wiki/Osaka\" title=\"Osaka\">Osaka</a>, Japan, collide and explode while approaching <a href=\"https://wikipedia.org/wiki/Ajikawaguchi_Station\" title=\"Ajikawaguchi Station\">Ajikawaguchi Station</a>. One hundred and eighty-one people are killed.", "no_year_html": "Three trains on the Nishinari Line; present <a href=\"https://wikipedia.org/wiki/Sakurajima_Line\" title=\"Sakurajima Line\">Sakurajima Line</a>, in <a href=\"https://wikipedia.org/wiki/Osaka\" title=\"Osaka\">Osaka</a>, Japan, collide and explode while approaching <a href=\"https://wikipedia.org/wiki/Ajikawaguchi_Station\" title=\"Ajikawaguchi Station\">Ajikawaguchi Station</a>. One hundred and eighty-one people are killed.", "links": [{"title": "Sakurajima Line", "link": "https://wikipedia.org/wiki/Sakurajima_Line"}, {"title": "Osaka", "link": "https://wikipedia.org/wiki/Osaka"}, {"title": "Ajikawaguchi Station", "link": "https://wikipedia.org/wiki/Ajikawaguchi_Station"}]}, {"year": "1943", "text": "World War II: The first day of the Battle of Rennell Island, USS Chicago (CA-29) is torpedoed and heavily damaged by Japanese bombers.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Rennell_Island\" title=\"Battle of Rennell Island\">Battle of Rennell Island</a>, <a href=\"https://wikipedia.org/wiki/USS_Chicago_(CA-29)\" title=\"USS Chicago (CA-29)\">USS <i>Chicago</i> (CA-29)</a> is torpedoed and heavily damaged by Japanese bombers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Rennell_Island\" title=\"Battle of Rennell Island\">Battle of Rennell Island</a>, <a href=\"https://wikipedia.org/wiki/USS_Chicago_(CA-29)\" title=\"USS Chicago (CA-29)\">USS <i>Chicago</i> (CA-29)</a> is torpedoed and heavily damaged by Japanese bombers.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Rennell Island", "link": "https://wikipedia.org/wiki/Battle_of_Rennell_Island"}, {"title": "USS Chicago (CA-29)", "link": "https://wikipedia.org/wiki/USS_Chicago_(CA-29)"}]}, {"year": "1944", "text": "World War II: Approximately 38 people are killed and about a dozen injured when the Polish village of Koniuchy (present-day Kaniūkai, Lithuania) is attacked by Soviet partisan units.", "html": "1944 - World War II: Approximately 38 people are killed and about a dozen injured when the Polish village of Koniuchy (present-day <a href=\"https://wikipedia.org/wiki/Kani%C5%ABkai\" title=\"Kaniūkai\">Kaniūkai</a>, Lithuania) is <a href=\"https://wikipedia.org/wiki/Koniuchy_massacre\" title=\"Koniuchy massacre\">attacked</a> by <a href=\"https://wikipedia.org/wiki/Soviet_partisan\" class=\"mw-redirect\" title=\"Soviet partisan\">Soviet partisan</a> units.", "no_year_html": "World War II: Approximately 38 people are killed and about a dozen injured when the Polish village of Koniuchy (present-day <a href=\"https://wikipedia.org/wiki/Kani%C5%ABkai\" title=\"Kaniūkai\">Kaniūkai</a>, Lithuania) is <a href=\"https://wikipedia.org/wiki/Koniuchy_massacre\" title=\"Koniuchy massacre\">attacked</a> by <a href=\"https://wikipedia.org/wiki/Soviet_partisan\" class=\"mw-redirect\" title=\"Soviet partisan\">Soviet partisan</a> units.", "links": [{"title": "Kaniūkai", "link": "https://wikipedia.org/wiki/Kani%C5%ABkai"}, {"title": "Koniuchy massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_massacre"}, {"title": "Soviet partisan", "link": "https://wikipedia.org/wiki/Soviet_partisan"}]}, {"year": "1944", "text": "World War II: In Bologna, Italy, the Anatomical theatre of the Archiginnasio is completely destroyed in an air-raid.", "html": "1944 - World War II: In <a href=\"https://wikipedia.org/wiki/Bologna\" title=\"Bologna\">Bologna</a>, Italy, the <a href=\"https://wikipedia.org/wiki/Anatomical_theatre_of_the_Archiginnasio\" title=\"Anatomical theatre of the Archiginnasio\">Anatomical theatre of the Archiginnasio</a> is completely destroyed in an air-raid.", "no_year_html": "World War II: In <a href=\"https://wikipedia.org/wiki/Bologna\" title=\"Bologna\">Bologna</a>, Italy, the <a href=\"https://wikipedia.org/wiki/Anatomical_theatre_of_the_Archiginnasio\" title=\"Anatomical theatre of the Archiginnasio\">Anatomical theatre of the Archiginnasio</a> is completely destroyed in an air-raid.", "links": [{"title": "Bologna", "link": "https://wikipedia.org/wiki/Bologna"}, {"title": "Anatomical theatre of the Archiginnasio", "link": "https://wikipedia.org/wiki/Anatomical_theatre_of_the_Archiginnasio"}]}, {"year": "1959", "text": "The first Melodifestivalen is held at Cirkus in Stockholm, Sweden.", "html": "1959 - The first <a href=\"https://wikipedia.org/wiki/Melodifestivalen\" title=\"Melodifestivalen\">Melodifestivalen</a> is held at <a href=\"https://wikipedia.org/wiki/<PERSON>irkus_(Stockholm)\" title=\"<PERSON><PERSON><PERSON> (Stockholm)\"><PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a>, Sweden.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Melodifestivalen\" title=\"Melodifestivalen\">Melodifestivalen</a> is held at <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Stockholm)\" title=\"<PERSON><PERSON><PERSON> (Stockholm)\"><PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a>, Sweden.", "links": [{"title": "Melodifestivalen", "link": "https://wikipedia.org/wiki/Melodifestivalen"}, {"title": "<PERSON><PERSON><PERSON> (Stockholm)", "link": "https://wikipedia.org/wiki/Cirkus_(Stockholm)"}, {"title": "Stockholm", "link": "https://wikipedia.org/wiki/Stockholm"}]}, {"year": "1973", "text": "EgyptAir Flight 741 crashes into the Kyrenia Mountains in Cyprus, killing 37 people.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/EgyptAir_Flight_741\" title=\"EgyptAir Flight 741\">EgyptAir Flight 741</a> crashes into the <a href=\"https://wikipedia.org/wiki/Kyrenia_Mountains\" title=\"Kyrenia Mountains\">Kyrenia Mountains</a> in <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a>, killing 37 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/EgyptAir_Flight_741\" title=\"EgyptAir Flight 741\">EgyptAir Flight 741</a> crashes into the <a href=\"https://wikipedia.org/wiki/Kyrenia_Mountains\" title=\"Kyrenia Mountains\">Kyrenia Mountains</a> in <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a>, killing 37 people.", "links": [{"title": "EgyptAir Flight 741", "link": "https://wikipedia.org/wiki/EgyptAir_Flight_741"}, {"title": "Kyrenia Mountains", "link": "https://wikipedia.org/wiki/Kyrenia_Mountains"}, {"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}]}, {"year": "1983", "text": "Singapore cable car crash: Panamanian-registered oil rig, Eniwetok, strikes the cables of the Singapore Cable Car system linking the mainland and Sentosa Island, causing two cabins to fall into the water and killing seven people and leaving thirteen others trapped for hours.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Singapore_cable_car_crash\" title=\"Singapore cable car crash\">Singapore cable car crash</a>: Panamanian-registered <a href=\"https://wikipedia.org/wiki/Oil_platform\" title=\"Oil platform\">oil rig</a>, <i>Eniwetok</i>, strikes the cables of the <a href=\"https://wikipedia.org/wiki/Singapore_Cable_Car\" title=\"Singapore Cable Car\">Singapore Cable Car</a> system linking the mainland and <a href=\"https://wikipedia.org/wiki/Sentosa\" title=\"Sentosa\">Sentosa Island</a>, causing two cabins to fall into the water and killing seven people and leaving thirteen others trapped for hours.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Singapore_cable_car_crash\" title=\"Singapore cable car crash\">Singapore cable car crash</a>: Panamanian-registered <a href=\"https://wikipedia.org/wiki/Oil_platform\" title=\"Oil platform\">oil rig</a>, <i>Eniwetok</i>, strikes the cables of the <a href=\"https://wikipedia.org/wiki/Singapore_Cable_Car\" title=\"Singapore Cable Car\">Singapore Cable Car</a> system linking the mainland and <a href=\"https://wikipedia.org/wiki/Sentosa\" title=\"Sentosa\">Sentosa Island</a>, causing two cabins to fall into the water and killing seven people and leaving thirteen others trapped for hours.", "links": [{"title": "Singapore cable car crash", "link": "https://wikipedia.org/wiki/Singapore_cable_car_crash"}, {"title": "Oil platform", "link": "https://wikipedia.org/wiki/Oil_platform"}, {"title": "Singapore Cable Car", "link": "https://wikipedia.org/wiki/Singapore_Cable_Car"}, {"title": "Sentosa", "link": "https://wikipedia.org/wiki/<PERSON>tosa"}]}, {"year": "1989", "text": "Cold War: Hungary establishes diplomatic relations with South Korea, making it the first Eastern Bloc nation to do so.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Hungary%E2%80%93South_Korea_relations\" title=\"Hungary-South Korea relations\">Hungary establishes diplomatic relations with South Korea</a>, making it the first <a href=\"https://wikipedia.org/wiki/Eastern_Bloc\" title=\"Eastern Bloc\">Eastern Bloc</a> nation to do so.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Hungary%E2%80%93South_Korea_relations\" title=\"Hungary-South Korea relations\">Hungary establishes diplomatic relations with South Korea</a>, making it the first <a href=\"https://wikipedia.org/wiki/Eastern_Bloc\" title=\"Eastern Bloc\">Eastern Bloc</a> nation to do so.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Hungary-South Korea relations", "link": "https://wikipedia.org/wiki/Hungary%E2%80%93South_Korea_relations"}, {"title": "Eastern Bloc", "link": "https://wikipedia.org/wiki/Eastern_Bloc"}]}, {"year": "1991", "text": "Gulf War: The Battle of Khafji, the first major ground engagement of the war, as well as its deadliest, begins between Iraq and Saudi Arabia.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Khafji\" title=\"Battle of Khafji\">Battle of Khafji</a>, the first major ground engagement of the war, as well as its deadliest, begins between <a href=\"https://wikipedia.org/wiki/Ba%27athist_Iraq\" title=\"Ba'athist Iraq\">Iraq</a> and <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Khafji\" title=\"Battle of Khafji\">Battle of Khafji</a>, the first major ground engagement of the war, as well as its deadliest, begins between <a href=\"https://wikipedia.org/wiki/Ba%27athist_Iraq\" title=\"Ba'athist Iraq\">Iraq</a> and <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>.", "links": [{"title": "Gulf War", "link": "https://wikipedia.org/wiki/Gulf_War"}, {"title": "Battle of Khafji", "link": "https://wikipedia.org/wiki/Battle_of_Khafji"}, {"title": "Ba'athist Iraq", "link": "https://wikipedia.org/wiki/Ba%27athist_Iraq"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}]}, {"year": "1996", "text": "President <PERSON> announces a \"definitive end\" to French nuclear weapons testing.", "html": "1996 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces a \"definitive end\" to French <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">nuclear weapons testing</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces a \"definitive end\" to French <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">nuclear weapons testing</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nuclear weapons testing", "link": "https://wikipedia.org/wiki/Nuclear_weapons_testing"}]}, {"year": "2001", "text": "Thousands of student protesters in Indonesia storm parliament and demand that President <PERSON><PERSON><PERSON><PERSON> resign due to alleged involvement in corruption scandals.", "html": "2001 - Thousands of student protesters in <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> storm parliament and demand that <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ahi<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Wahid\"><PERSON><PERSON><PERSON><PERSON></a> resign due to alleged involvement in corruption scandals.", "no_year_html": "Thousands of student protesters in <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> storm parliament and demand that <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ahi<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> W<PERSON>d\"><PERSON><PERSON><PERSON><PERSON></a> resign due to alleged involvement in corruption scandals.", "links": [{"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "President of Indonesia", "link": "https://wikipedia.org/wiki/President_of_Indonesia"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2002", "text": "In his State of the Union address, President <PERSON> describes \"regimes that sponsor terror\" as an Axis of evil, in which he includes Iraq, Iran and North Korea.", "html": "2002 - In his <a href=\"https://wikipedia.org/wiki/2002_State_of_the_Union_Address\" title=\"2002 State of the Union Address\">State of the Union address</a>, President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> describes \"regimes that sponsor <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terror</a>\" as an <i><a href=\"https://wikipedia.org/wiki/Axis_of_evil\" title=\"Axis of evil\">Axis of evil</a></i>, in which he includes <a href=\"https://wikipedia.org/wiki/Ba%27athist_Iraq\" title=\"Ba'athist Iraq\">Iraq</a>, <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> and <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>.", "no_year_html": "In his <a href=\"https://wikipedia.org/wiki/2002_State_of_the_Union_Address\" title=\"2002 State of the Union Address\">State of the Union address</a>, President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> describes \"regimes that sponsor <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terror</a>\" as an <i><a href=\"https://wikipedia.org/wiki/Axis_of_evil\" title=\"Axis of evil\">Axis of evil</a></i>, in which he includes <a href=\"https://wikipedia.org/wiki/Ba%27athist_Iraq\" title=\"Ba'athist Iraq\">Iraq</a>, <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> and <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>.", "links": [{"title": "2002 State of the Union Address", "link": "https://wikipedia.org/wiki/2002_State_of_the_Union_Address"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Terrorism", "link": "https://wikipedia.org/wiki/Terrorism"}, {"title": "Axis of evil", "link": "https://wikipedia.org/wiki/Axis_of_evil"}, {"title": "Ba'athist Iraq", "link": "https://wikipedia.org/wiki/Ba%27athist_Iraq"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}]}, {"year": "2005", "text": "The first direct commercial flights from mainland China (from Guangzhou) to Taiwan since 1949 arrived in Taipei. Shortly afterwards, a China Airlines flight lands in Beijing.", "html": "2005 - The first direct commercial flights from <a href=\"https://wikipedia.org/wiki/Mainland_China\" title=\"Mainland China\">mainland China</a> (from <a href=\"https://wikipedia.org/wiki/Guangzhou\" title=\"Guangzhou\">Guangzhou</a>) to <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> since 1949 arrived in <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a>. Shortly afterwards, a <a href=\"https://wikipedia.org/wiki/China_Airlines\" title=\"China Airlines\">China Airlines</a> flight lands in <a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Beijing</a>.", "no_year_html": "The first direct commercial flights from <a href=\"https://wikipedia.org/wiki/Mainland_China\" title=\"Mainland China\">mainland China</a> (from <a href=\"https://wikipedia.org/wiki/Guangzhou\" title=\"Guangzhou\">Guangzhou</a>) to <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> since 1949 arrived in <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a>. Shortly afterwards, a <a href=\"https://wikipedia.org/wiki/China_Airlines\" title=\"China Airlines\">China Airlines</a> flight lands in <a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Beijing</a>.", "links": [{"title": "Mainland China", "link": "https://wikipedia.org/wiki/Mainland_China"}, {"title": "Guangzhou", "link": "https://wikipedia.org/wiki/Guangzhou"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}, {"title": "Taipei", "link": "https://wikipedia.org/wiki/Taipei"}, {"title": "China Airlines", "link": "https://wikipedia.org/wiki/China_Airlines"}, {"title": "Beijing", "link": "https://wikipedia.org/wiki/Beijing"}]}, {"year": "2008", "text": "An Egyptian court rules that people who do not adhere to one of the three government-recognised religions, while not allowed to list any belief outside of those three, are still eligible to receive government identity documents.", "html": "2008 - An Egyptian court rules that people who do not adhere to one of the three <a href=\"https://wikipedia.org/wiki/Religion_in_Egypt#Recognized_religions\" title=\"Religion in Egypt\">government-recognised religions</a>, while not allowed to list any belief outside of those three, are still <a href=\"https://wikipedia.org/wiki/Egyptian_identification_card_controversy\" title=\"Egyptian identification card controversy\">eligible to receive</a> government <a href=\"https://wikipedia.org/wiki/Identity_document\" title=\"Identity document\">identity documents</a>.", "no_year_html": "An Egyptian court rules that people who do not adhere to one of the three <a href=\"https://wikipedia.org/wiki/Religion_in_Egypt#Recognized_religions\" title=\"Religion in Egypt\">government-recognised religions</a>, while not allowed to list any belief outside of those three, are still <a href=\"https://wikipedia.org/wiki/Egyptian_identification_card_controversy\" title=\"Egyptian identification card controversy\">eligible to receive</a> government <a href=\"https://wikipedia.org/wiki/Identity_document\" title=\"Identity document\">identity documents</a>.", "links": [{"title": "Religion in Egypt", "link": "https://wikipedia.org/wiki/Religion_in_Egypt#Recognized_religions"}, {"title": "Egyptian identification card controversy", "link": "https://wikipedia.org/wiki/Egyptian_identification_card_controversy"}, {"title": "Identity document", "link": "https://wikipedia.org/wiki/Identity_document"}]}, {"year": "2009", "text": "Governor of Illinois <PERSON> is removed from office following his conviction of several corruption charges, including solicitation of personal benefit in exchange for an appointment to the United States Senate as a replacement for then-U.S. president-elect <PERSON>.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Governor of Illinois</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_corruption_charges\" title=\"<PERSON> corruption charges\">is removed from office</a> following his conviction of several corruption charges, including solicitation of personal benefit in exchange for an appointment to the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> as a replacement for then-<a href=\"https://wikipedia.org/wiki/President-elect_of_the_United_States\" title=\"President-elect of the United States\">U.S. president-elect</a> <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"<PERSON> Obama\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Governor of Illinois</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_corruption_charges\" title=\"<PERSON> corruption charges\">is removed from office</a> following his conviction of several corruption charges, including solicitation of personal benefit in exchange for an appointment to the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> as a replacement for then-<a href=\"https://wikipedia.org/wiki/President-elect_of_the_United_States\" title=\"President-elect of the United States\">U.S. president-elect</a> <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Governor of Illinois", "link": "https://wikipedia.org/wiki/Governor_of_Illinois"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON> corruption charges", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_corruption_charges"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "President-elect of the United States", "link": "https://wikipedia.org/wiki/President-elect_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barack<PERSON>Obama"}]}, {"year": "2013", "text": "SCAT Airlines Flight 760 crashes near the Kazakh city of Almaty, killing 21 people.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/SCAT_Airlines_Flight_760\" title=\"SCAT Airlines Flight 760\">SCAT Airlines Flight 760</a> crashes near the <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakh</a> city of <a href=\"https://wikipedia.org/wiki/Almaty\" title=\"Almaty\">Almaty</a>, killing 21 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SCAT_Airlines_Flight_760\" title=\"SCAT Airlines Flight 760\">SCAT Airlines Flight 760</a> crashes near the <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakh</a> city of <a href=\"https://wikipedia.org/wiki/Almaty\" title=\"Almaty\">Almaty</a>, killing 21 people.", "links": [{"title": "SCAT Airlines Flight 760", "link": "https://wikipedia.org/wiki/SCAT_Airlines_Flight_760"}, {"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}, {"title": "Almaty", "link": "https://wikipedia.org/wiki/Almaty"}]}, {"year": "2014", "text": "Rojava conflict: The Afrin Canton declares its autonomy from the Syrian Arab Republic.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Rojava_conflict\" title=\"Rojava conflict\">Rojava conflict</a>: The <a href=\"https://wikipedia.org/wiki/Afrin_Region\" title=\"Afrin Region\">Afrin Canton</a> declares its autonomy from the <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syrian Arab Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rojava_conflict\" title=\"Rojava conflict\">Rojava conflict</a>: The <a href=\"https://wikipedia.org/wiki/Afrin_Region\" title=\"Afrin Region\">Afrin Canton</a> declares its autonomy from the <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syrian Arab Republic</a>.", "links": [{"title": "Rojava conflict", "link": "https://wikipedia.org/wiki/Rojava_conflict"}, {"title": "Afrin Region", "link": "https://wikipedia.org/wiki/Afrin_Region"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}, {"year": "2017", "text": "A gunman opens fire at the Islamic Cultural Centre of Quebec City, killing six people and wounding 19 others in a spree shooting.", "html": "2017 - A gunman <a href=\"https://wikipedia.org/wiki/Quebec_City_mosque_shooting\" title=\"Quebec City mosque shooting\">opens fire</a> at the <a href=\"https://wikipedia.org/wiki/Islamic_Cultural_Centre_of_Quebec_City\" title=\"Islamic Cultural Centre of Quebec City\">Islamic Cultural Centre of Quebec City</a>, killing six people and wounding 19 others in a spree shooting.", "no_year_html": "A gunman <a href=\"https://wikipedia.org/wiki/Quebec_City_mosque_shooting\" title=\"Quebec City mosque shooting\">opens fire</a> at the <a href=\"https://wikipedia.org/wiki/Islamic_Cultural_Centre_of_Quebec_City\" title=\"Islamic Cultural Centre of Quebec City\">Islamic Cultural Centre of Quebec City</a>, killing six people and wounding 19 others in a spree shooting.", "links": [{"title": "Quebec City mosque shooting", "link": "https://wikipedia.org/wiki/Quebec_City_mosque_shooting"}, {"title": "Islamic Cultural Centre of Quebec City", "link": "https://wikipedia.org/wiki/Islamic_Cultural_Centre_of_Quebec_City"}]}, {"year": "2022", "text": "Canadian truck drivers and pedestrians gathered to rally and protest on Parliament Hill against Canadian COVID-19 restrictions, which caused traffic and closures around the city.", "html": "2022 - Canadian <a href=\"https://wikipedia.org/wiki/Truck_driver\" title=\"Truck driver\">truck drivers</a> and pedestrians gathered to <a href=\"https://wikipedia.org/wiki/Canada_convoy_protest\" title=\"Canada convoy protest\">rally and protest</a> on <a href=\"https://wikipedia.org/wiki/Parliament_Hill\" title=\"Parliament Hill\">Parliament Hill</a> against <a href=\"https://wikipedia.org/wiki/COVID-19_vaccination_in_Canada\" title=\"COVID-19 vaccination in Canada\">Canadian COVID-19 restrictions</a>, which caused traffic and closures around <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">the city</a>.", "no_year_html": "Canadian <a href=\"https://wikipedia.org/wiki/Truck_driver\" title=\"Truck driver\">truck drivers</a> and pedestrians gathered to <a href=\"https://wikipedia.org/wiki/Canada_convoy_protest\" title=\"Canada convoy protest\">rally and protest</a> on <a href=\"https://wikipedia.org/wiki/Parliament_Hill\" title=\"Parliament Hill\">Parliament Hill</a> against <a href=\"https://wikipedia.org/wiki/COVID-19_vaccination_in_Canada\" title=\"COVID-19 vaccination in Canada\">Canadian COVID-19 restrictions</a>, which caused traffic and closures around <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">the city</a>.", "links": [{"title": "Truck driver", "link": "https://wikipedia.org/wiki/Truck_driver"}, {"title": "Canada convoy protest", "link": "https://wikipedia.org/wiki/Canada_convoy_protest"}, {"title": "Parliament Hill", "link": "https://wikipedia.org/wiki/Parliament_Hill"}, {"title": "COVID-19 vaccination in Canada", "link": "https://wikipedia.org/wiki/COVID-19_vaccination_in_Canada"}, {"title": "Ottawa", "link": "https://wikipedia.org/wiki/Ottawa"}]}, {"year": "2025", "text": "American Eagle Flight 5342 collided mid-air with a Sikorsky UH-60 Black Hawk operated by the United States Army and crashed into the Potomac River, killing all 67 people onboard both aircraft.", "html": "2025 - <a href=\"https://wikipedia.org/wiki/American_Eagle_(airline_brand)\" title=\"American Eagle (airline brand)\">American Eagle</a> Flight 5342 <a href=\"https://wikipedia.org/wiki/2025_Potomac_River_mid-air_collision\" title=\"2025 Potomac River mid-air collision\">collided mid-air</a> with a <a href=\"https://wikipedia.org/wiki/Sikorsky_UH-60_Black_Hawk\" title=\"Sikorsky UH-60 Black Hawk\">Sikorsky UH-60 Black Hawk</a> operated by the <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> and crashed into the <a href=\"https://wikipedia.org/wiki/Potomac_River\" title=\"Potomac River\">Potomac River</a>, killing all 67 people onboard both aircraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Eagle_(airline_brand)\" title=\"American Eagle (airline brand)\">American Eagle</a> Flight 5342 <a href=\"https://wikipedia.org/wiki/2025_Potomac_River_mid-air_collision\" title=\"2025 Potomac River mid-air collision\">collided mid-air</a> with a <a href=\"https://wikipedia.org/wiki/Sikorsky_UH-60_Black_Hawk\" title=\"Sikorsky UH-60 Black Hawk\">Sikorsky UH-60 Black Hawk</a> operated by the <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> and crashed into the <a href=\"https://wikipedia.org/wiki/Potomac_River\" title=\"Potomac River\">Potomac River</a>, killing all 67 people onboard both aircraft.", "links": [{"title": "American Eagle (airline brand)", "link": "https://wikipedia.org/wiki/American_Eagle_(airline_brand)"}, {"title": "2025 Potomac River mid-air collision", "link": "https://wikipedia.org/wiki/2025_Potomac_River_mid-air_collision"}, {"title": "Sikorsky UH-60 Black Hawk", "link": "https://wikipedia.org/wiki/Sikorsky_UH-60_Black_Hawk"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Potomac River", "link": "https://wikipedia.org/wiki/Potomac_River"}]}, {"year": "2025", "text": "A chartered Beechcraft 1900 crashes near the Unity oilfield in South Sudan, killing 20 people.", "html": "2025 - A chartered <a href=\"https://wikipedia.org/wiki/Beechcraft_1900\" title=\"Beechcraft 1900\">Beechcraft 1900</a> <a href=\"https://wikipedia.org/wiki/2025_Light_Air_Services_Beechcraft_1900_crash\" title=\"2025 Light Air Services Beechcraft 1900 crash\">crashes</a> near the <a href=\"https://wikipedia.org/wiki/Unity_oilfield\" title=\"Unity oilfield\">Unity oilfield</a> in South Sudan, killing 20 people.", "no_year_html": "A chartered <a href=\"https://wikipedia.org/wiki/Beechcraft_1900\" title=\"Beechcraft 1900\">Beechcraft 1900</a> <a href=\"https://wikipedia.org/wiki/2025_Light_Air_Services_Beechcraft_1900_crash\" title=\"2025 Light Air Services Beechcraft 1900 crash\">crashes</a> near the <a href=\"https://wikipedia.org/wiki/Unity_oilfield\" title=\"Unity oilfield\">Unity oilfield</a> in South Sudan, killing 20 people.", "links": [{"title": "Beechcraft 1900", "link": "https://wikipedia.org/wiki/Beechcraft_1900"}, {"title": "2025 Light Air Services Beechcraft 1900 crash", "link": "https://wikipedia.org/wiki/2025_Light_Air_Services_Beechcraft_1900_crash"}, {"title": "Unity oilfield", "link": "https://wikipedia.org/wiki/Unity_oilfield"}]}], "Births": [{"year": "1455", "text": "<PERSON>, German-born humanist and scholar (d. 1522)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born humanist and scholar (d. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born humanist and scholar (d. 1522)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1475", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian painter (d. 1555)", "html": "1475 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (d. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (d. 1555)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1499", "text": "<PERSON><PERSON><PERSON>, wife of <PERSON>; formerly a Roman Catholic nun (d. 1552)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Kat<PERSON><PERSON> <PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>; formerly a Roman Catholic nun (d. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Kat<PERSON><PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>; formerly a Roman Catholic nun (d. 1552)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1525", "text": "<PERSON><PERSON>, Italian humanist and reformer (d. 1562)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian humanist and reformer (d. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian humanist and reformer (d. 1562)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1584", "text": "<PERSON>, Prince of Orange (d. 1647)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (d. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (d. 1647)", "links": [{"title": "<PERSON>, Prince of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange"}]}, {"year": "1591", "text": "<PERSON><PERSON>, German pioneer philologist (d. 1677)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(the_younger)\" title=\"<PERSON><PERSON> (the younger)\"><PERSON><PERSON></a>, German pioneer philologist (d. 1677)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(the_younger)\" title=\"<PERSON><PERSON> (the younger)\"><PERSON><PERSON></a>, German pioneer philologist (d. 1677)", "links": [{"title": "<PERSON><PERSON> (the younger)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(the_younger)"}]}, {"year": "1602", "text": "Countess <PERSON><PERSON><PERSON> of Hanau-Münzenberg (d. 1651)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/Countess_<PERSON><PERSON><PERSON>_Elisabeth_of_Hanau-M%C3%BCnz<PERSON>\" title=\"Countess <PERSON><PERSON><PERSON> of Hanau-Münzenberg\">Countess <PERSON><PERSON><PERSON> of Hanau-Münzenberg</a> (d. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Countess_<PERSON><PERSON><PERSON>_Elisabeth_of_Hanau-M%C3%BCnz<PERSON>\" title=\"Countess <PERSON><PERSON><PERSON> of Hanau-Münzenberg\">Countess <PERSON><PERSON><PERSON> of Hanau-Münzenberg</a> (d. 1651)", "links": [{"title": "Countess <PERSON><PERSON><PERSON> of Hanau-Münzenberg", "link": "https://wikipedia.org/wiki/Countess_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Hanau-M%C3%BCnz<PERSON>"}]}, {"year": "1632", "text": "<PERSON>, German scholar and critic (d. 1703)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and critic (d. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and critic (d. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON>, Swedish astronomer, philosopher, and theologian (d. 1772)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish astronomer, philosopher, and theologian (d. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish astronomer, philosopher, and theologian (d. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emanuel_<PERSON>borg"}]}, {"year": "1711", "text": "<PERSON>, Austrian composer (d. 1788)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer (d. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, Austrian organist and composer (d. 1777)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (d. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (d. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON><PERSON>, 1st Baron <PERSON>, English field marshal and politician, 19th Governor General of Canada (d. 1797)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON>, 1st Baron <PERSON></a>, English field marshal and politician, 19th <a href=\"https://wikipedia.org/wiki/List_of_Governors_General_of_Canada\" class=\"mw-redirect\" title=\"List of Governors General of Canada\">Governor General of Canada</a> (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON>, 1st Baron <PERSON></a>, English field marshal and politician, 19th <a href=\"https://wikipedia.org/wiki/List_of_Governors_General_of_Canada\" class=\"mw-redirect\" title=\"List of Governors General of Canada\">Governor General of Canada</a> (d. 1797)", "links": [{"title": "<PERSON><PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>"}, {"title": "List of Governors General of Canada", "link": "https://wikipedia.org/wiki/List_of_Governors_General_of_Canada"}]}, {"year": "1718", "text": "<PERSON>, French pastor (d. 1794)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pastor (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pastor (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1737", "text": "<PERSON>, English-American political activist, philosopher, political theorist, and revolutionary (d. 1809)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American political activist, philosopher, political theorist, and revolutionary (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American political activist, philosopher, political theorist, and revolutionary (d. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON> of Denmark (d. 1808)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/Christian_VII_of_Denmark\" title=\"Christian VII of Denmark\">Christian VII of Denmark</a> (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_VII_of_Denmark\" title=\"Christian VII of Denmark\">Christian VII of Denmark</a> (d. 1808)", "links": [{"title": "Christian VII of Denmark", "link": "https://wikipedia.org/wiki/Christian_VII_of_Denmark"}]}, {"year": "1754", "text": "<PERSON>, American general, lawyer, and politician, founded Cleveland, Ohio (d. 1806)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, founded <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a> (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, founded <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a> (d. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}, {"title": "Cleveland, Ohio", "link": "https://wikipedia.org/wiki/Cleveland,_Ohio"}]}, {"year": "1756", "text": "<PERSON>, American general and politician, 9th Governor of Virginia (d. 1818)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a> (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a> (d. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Virginia", "link": "https://wikipedia.org/wiki/Governor_of_Virginia"}]}, {"year": "1761", "text": "<PERSON>, Swiss-American ethnologist, linguist, and politician, 4th United States Secretary of the Treasury (d. 1849)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American ethnologist, linguist, and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American ethnologist, linguist, and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1782", "text": "<PERSON>, French composer (d. 1871)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON><PERSON><PERSON>, American politician (d. 1852)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American politician (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American politician (d. 1852)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, Dutch violinist, composer, and conductor (d. 1857)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch violinist, composer, and conductor (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch violinist, composer, and conductor (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, Polish-German mathematician and academic (d. 1893)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German mathematician and academic (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German mathematician and academic (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, American teacher, school founder, and hymnwriter (d. 1862)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American teacher, school founder, and hymnwriter (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American teacher, school founder, and hymnwriter (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American soldier, lawyer, and politician, 25th President of the United States (d. 1901)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1846", "text": "<PERSON><PERSON>, Polish chemist, mathematician and physicist (d. 1915)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish chemist, mathematician and physicist (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish chemist, mathematician and physicist (d. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, Jamaican-English pianist, composer, and conductor (d. 1935)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English pianist, composer, and conductor (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English pianist, composer, and conductor (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, American painter and academic (d. 1916)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Russian playwright and short story writer (d. 1904)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian playwright and short story writer (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian playwright and short story writer (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1861", "text": "Florida R<PERSON><PERSON>, American civil rights activist, teacher, editor, and writer (d. 1943)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Florida_R<PERSON><PERSON>_<PERSON>\" title=\"Florida Ruffin Ridley\">Florida R<PERSON><PERSON></a>, American civil rights activist, teacher, editor, and writer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florida_R<PERSON><PERSON>_<PERSON>\" title=\"Florida Ruffin Ridley\">Florida Ruff<PERSON></a>, American civil rights activist, teacher, editor, and writer (d. 1943)", "links": [{"title": "Florida Ruffin <PERSON>", "link": "https://wikipedia.org/wiki/Florida_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, English composer (d. 1934)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, British astronomer (d. 1952)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\"><PERSON></a>, British astronomer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\"><PERSON></a>, British astronomer (d. 1952)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}]}, {"year": "1866", "text": "<PERSON><PERSON>, French historian, author, and playwright, Nobel Prize laureate (d. 1944)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French historian, author, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Roma<PERSON>\"><PERSON><PERSON></a>, French historian, author, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>and"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1867", "text": "<PERSON>, Spanish journalist and author (d. 1928)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>co_Ib%C3%A1%C3%B1ez\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist and author (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>co_Ib%C3%A1%C3%B1ez\" title=\"<PERSON> Blas<PERSON>báñez\"><PERSON></a>, Spanish journalist and author (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vicente_Blasco_Ib%C3%A1%C3%B1ez"}]}, {"year": "1874", "text": "<PERSON>, American businessman and philanthropist (d. 1960)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American businessman and philanthropist (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American businessman and philanthropist (d. 1960)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, English composer (d. 1972)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English composer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English composer (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor, comedian, and screenwriter (d. 1946)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor, comedian, and screenwriter (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor, comedian, and screenwriter (d. 1946)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._C._Fields"}]}, {"year": "1881", "text": "<PERSON>, American microbiologist (d. 1975)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Estonian-Swedish composer and conductor (d. 1982)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-Swedish composer and conductor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-Swedish composer and conductor (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, German chemist (d. 1983)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, English mathematician and geophysicist (d. 1970)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and geophysicist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and geophysicist (d. 1970)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)"}]}, {"year": "1888", "text": "<PERSON>, Chinese statesman (d. 1985)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Wellington_Ko<PERSON>\" title=\"Wellington Ko<PERSON>\"><PERSON></a>, Chinese statesman (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ko<PERSON>\" title=\"Wellington Koo\"><PERSON></a>, Chinese statesman (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wellington_Koo"}]}, {"year": "1892", "text": "<PERSON>, German American film director, producer, writer, and actor (d. 1947)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German American film director, producer, writer, and actor (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German American film director, producer, writer, and actor (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, American poet and author (d. 1965)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON> (writer)\"><PERSON><PERSON></a>, American poet and author (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON> (writer)\"><PERSON><PERSON></a>, American poet and author (d. 1965)", "links": [{"title": "<PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(writer)"}]}, {"year": "1901", "text": "<PERSON>, American engineer and broadcaster, founded the DuMont Television Network (d. 1965)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and broadcaster, founded the <a href=\"https://wikipedia.org/wiki/DuMont_Television_Network\" title=\"DuMont Television Network\">DuMont Television Network</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and broadcaster, founded the <a href=\"https://wikipedia.org/wiki/DuMont_Television_Network\" title=\"DuMont Television Network\">DuMont Television Network</a> (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "DuMont Television Network", "link": "https://wikipedia.org/wiki/DuMont_Television_Network"}]}, {"year": "1901", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian businessman and horse breeder (d. 1989)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian businessman and horse breeder (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian businessman and horse breeder (d. 1989)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American painter and etcher (d. 1970)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and etcher (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and etcher (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actor (d. 1999)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American author and illustrator (d. 2002)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Italian-American concert accordionist and composer (d. 2003)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, Italian-American concert accordionist and composer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, Italian-American concert accordionist and composer (d. 2003)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1916", "text": "<PERSON>, British plant virologist (d. 1979)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British plant virologist (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British plant virologist (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actor and singer (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actor (d. 2010)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American R&B pianist, songwriter, producer, and record company executive (d. 1991)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B pianist, songwriter, producer, and record company executive (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B pianist, songwriter, producer, and record company executive (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American author and screenwriter (d. 1981)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American electric blues guitarist and singer (d. 1985)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American electric blues guitarist and singer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American electric blues guitarist and singer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Pakistani-British physicist and academic, Nobel Prize laureate (d. 1996)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-British physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-British physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1927", "text": "<PERSON>, American environmentalist and author (d. 1989)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Edward_Abbey\" title=\"Edward Abbey\">Edward Abbey</a>, American environmentalist and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edward_Abbey\" title=\"Edward Abbey\">Edward Abbey</a>, American environmentalist and author (d. 1989)", "links": [{"title": "Edward Abbey", "link": "https://wikipedia.org/wiki/Edward_Abbey"}]}, {"year": "1928", "text": "<PERSON>, American mathematician and computer scientist (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and computer scientist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and computer scientist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Italian director and screenwriter (d. 1982)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English playwright and composer (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and composer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and composer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Hungarian academic and politician, 2nd President of Hungary (d. 2011)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Ferenc_M%C3%A1dl\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian academic and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Hungary\" title=\"President of Hungary\">President of Hungary</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferenc_M%C3%A1dl\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian academic and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Hungary\" title=\"President of Hungary\">President of Hungary</a> (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_M%C3%A1dl"}, {"title": "President of Hungary", "link": "https://wikipedia.org/wiki/President_of_Hungary"}]}, {"year": "1932", "text": "<PERSON><PERSON>, English cricketer and referee (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Raman_Subba_Row\" title=\"Raman Subba Row\"><PERSON><PERSON></a>, English cricketer and referee (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Raman_Subba_Row\" title=\"Raman Subba Row\"><PERSON><PERSON></a>, English cricketer and referee (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Raman_Subba_Row"}]}, {"year": "1933", "text": "<PERSON><PERSON>, French singer and guitarist (d. 2004)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer and guitarist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer and guitarist (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>stel"}]}, {"year": "1934", "text": "<PERSON>, British chemist (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chemist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chemist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American bass player (d. 1983)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Indian poet and songwriter (d. 2010)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Veturi\" title=\"Veturi\"><PERSON><PERSON><PERSON></a>, Indian poet and songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Veturi\" title=\"Veturi\"><PERSON><PERSON><PERSON></a>, Indian poet and songwriter (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Veturi"}]}, {"year": "1937", "text": "<PERSON>, British musician (d. 2009)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British musician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British musician (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Australian journalist and author", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American jazz singer, poet and composer (d. 2000)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz singer, poet and composer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz singer, poet and composer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Puerto Rican opera singer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Justino_D%C3%ADaz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican opera singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Justino_D%C3%ADaz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican opera singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Justino_D%C3%ADaz"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American actress and author", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American actress, journalist, and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, journalist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, journalist, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Sri Lankan politician (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>wickrama_Perera\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>wickrama_Perera\" title=\"<PERSON><PERSON><PERSON> Jayawick<PERSON> Perera\"><PERSON><PERSON><PERSON></a>, Sri Lankan politician (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gamini_Jayawickrama_Perera"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Cuban military officer, legislator and cosmonaut", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Arnaldo_Tamayo_M%C3%A9ndez\" title=\"Arnaldo <PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban military officer, legislator and cosmonaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arnaldo_Tamayo_M%C3%A9ndez\" title=\"Arnaldo <PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban military officer, legislator and cosmonaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arnaldo_Tamayo_M%C3%A9ndez"}]}, {"year": "1943", "text": "<PERSON>, English radio and television host", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2014)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2014)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1943", "text": "<PERSON>, English singer and actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Malian academic and politician, Prime Minister of Mali (d. 2022)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AFta\" title=\"<PERSON>\"><PERSON></a>, Malian academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mali\" class=\"mw-redirect\" title=\"Prime Minister of Mali\">Prime Minister of Mali</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AFta\" title=\"<PERSON>\"><PERSON></a>, Malian academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mali\" class=\"mw-redirect\" title=\"Prime Minister of Mali\">Prime Minister of Mali</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%AFta"}, {"title": "Prime Minister of Mali", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Mali"}]}, {"year": "1945", "text": "<PERSON>, American actor and businessman", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter (d. 1984)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American biologist and academic, Nobel Prize laureate", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter (d. 1985)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Slovak organist and composer (d. 2017)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Mari%C3%A1n_Varga\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak organist and composer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mari%C3%A1n_Varga\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak organist and composer (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mari%C3%A1n_Varga"}]}, {"year": "1948", "text": "<PERSON>, English chess player and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chess player and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chess player and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Cuban-American journalist, actress and talk show host", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American journalist, actress and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American journalist, actress and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian-American actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American poet and teacher", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and teacher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and teacher", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1949", "text": "<PERSON>, Hungarian-American drummer and producer (d. 2014)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American drummer and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American drummer and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actress and singer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian writer, novelist, screenwriter and translator", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_V%C3%A1mos\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian writer, novelist, screenwriter and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_V%C3%A1mos\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian writer, novelist, screenwriter and translator", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikl%C3%B3s_V%C3%A1mos"}]}, {"year": "1952", "text": "<PERSON>, American attorney and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, British actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1953", "text": "<PERSON>, Taiwanese singer (d. 1995)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese singer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese singer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1954", "text": "<PERSON>, American actor and director", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American talk show host, actress, and producer, founded Harpo Productions", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Oprah_Winfrey\" title=\"Oprah Winfrey\"><PERSON><PERSON> Winfrey</a>, American talk show host, actress, and producer, founded <a href=\"https://wikipedia.org/wiki/Harpo_Productions\" title=\"Harpo Productions\">Harpo Productions</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oprah_Winfrey\" title=\"Oprah Winfrey\"><PERSON><PERSON> Winfrey</a>, American talk show host, actress, and producer, founded <a href=\"https://wikipedia.org/wiki/Harpo_Productions\" title=\"Harpo Productions\">Harpo Productions</a>", "links": [{"title": "<PERSON>rah Winfrey", "link": "https://wikipedia.org/wiki/Oprah_Winfrey"}, {"title": "Harpo Productions", "link": "https://wikipedia.org/wiki/Harpo_Productions"}]}, {"year": "1955", "text": "<PERSON>, American basketball player and coach (d. 2016)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (d. 2016)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1955", "text": "<PERSON>, American boxer (d. 1998)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (d. 1998)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(boxer)"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American musician, actress, and model", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician, actress, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician, actress, and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American singer and dancer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ii_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actress (d. 2024)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American author and journalist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress and theater director", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and theater director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and theater director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Taiwanese-American musician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>-liang_<PERSON>\" title=\"<PERSON>-liang <PERSON>\"><PERSON><PERSON><PERSON>ian<PERSON></a>, Taiwanese-American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-liang_<PERSON>\" title=\"<PERSON>-liang <PERSON>\"><PERSON><PERSON><PERSON>ian<PERSON></a>, Taiwanese-American musician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-lian<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American diver and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diver and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diver and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American supermodel (d. 1986)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/G<PERSON>_Carangi\" title=\"<PERSON><PERSON> Cara<PERSON>\"><PERSON><PERSON></a>, American supermodel (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON>_Carang<PERSON>\" title=\"<PERSON><PERSON> Carangi\"><PERSON><PERSON></a>, American supermodel (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gia_<PERSON>ngi"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Zimbabwean businessman and philanthropist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Strive_Masiyiwa\" title=\"Strive Masiyiwa\"><PERSON><PERSON></a>, Zimbabwean businessman and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Strive_Masiyiwa\" title=\"Strive Masiyiwa\"><PERSON><PERSON></a>, Zimbabwean businessman and philanthropist", "links": [{"title": "Strive Masiyiwa", "link": "https://wikipedia.org/wiki/Strive_Masiyiwa"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Indian journalist and activist (d. 2017)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and activist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and activist (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American politician and lawyer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Scottish singer-songwriter and musician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ame\"><PERSON><PERSON></a>, Scottish singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ame\"><PERSON><PERSON></a>, Scottish singer-songwriter and musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ame"}]}, {"year": "1964", "text": "<PERSON>, American football player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American physician and author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Dominik_Ha%C5%A1ek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dom<PERSON><PERSON>_Ha%C5%A1ek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dominik_Ha%C5%A1ek"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American basketball player, coach, and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor, director, writer, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, writer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, writer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American game designer and writer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cook\"><PERSON></a>, American game designer and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, German swimmer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON><PERSON><PERSON> (swimmer)\"><PERSON><PERSON><PERSON></a>, German swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON><PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON><PERSON><PERSON> (swimmer)\"><PERSON><PERSON><PERSON></a>, German swimmer", "links": [{"title": "<PERSON><PERSON><PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/J%C3%B6<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian colonel and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian colonel and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian colonel and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American politician, 62nd Speaker of the United States House of Representatives", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 62nd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 62nd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1971", "text": "<PERSON>, English broadcaster, journalist and author", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English broadcaster, journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English broadcaster, journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American writer, illustrator and graphic designer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American writer, illustrator and graphic designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American writer, illustrator and graphic designer", "links": [{"title": "<PERSON> (comics)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)"}]}, {"year": "1973", "text": "<PERSON>, American journalist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress, producer, and talk show host", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, producer, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, producer, and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor and screenwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American engineer and astronaut", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor and musician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Mexican actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American singer, songwriter and guitarist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American singer, songwriter and actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Spanish basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian actress and model", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, English singer-songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Rag%27n%27Bone_Man\" title=\"Rag'n'Bone Man\">Rag'n'<PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rag%27n%27Bone_Man\" title=\"Rag'n'Bone Man\">Rag'n'<PERSON></a>, English singer-songwriter", "links": [{"title": "Rag'n'Bone Man", "link": "https://wikipedia.org/wiki/Rag%27n%27Bone_Man"}]}, {"year": "1986", "text": "<PERSON>, American ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, German ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Curaçaoan baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Curaçaoan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Curaçaoan baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Cuban baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Abreu"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American author", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_Mih%C3%A1lik\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_Mih%C3%A1lik\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vladim%C3%ADr_Mih%C3%A1lik"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian author", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian author", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1988", "text": "<PERSON>, American politician, businessman, and Marine veteran", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, businessman, and Marine veteran", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, businessman, and Marine veteran", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Egyptian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, German basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ber"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Japanese singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yu_Pamyu\" title=\"<PERSON><PERSON><PERSON> Pamyu\"><PERSON><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yu_Pamyu\" title=\"<PERSON><PERSON><PERSON>yu Pamyu\"><PERSON><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON>y <PERSON>", "link": "https://wikipedia.org/wiki/Kyary_Pamyu_Pamyu"}]}, {"year": "1997", "text": "<PERSON>, Swedish ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, South Korean singer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hwi\" title=\"<PERSON>hwi\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hwi\" title=\"<PERSON>hwi\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>wi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hwi"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>h"}]}], "Deaths": [{"year": "757", "text": "<PERSON>, Chinese general (b. 703)", "html": "757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"An Lushan\"><PERSON></a>, Chinese general (b. 703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An_<PERSON>\" title=\"An Lushan\"><PERSON></a>, Chinese general (b. 703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>shan"}]}, {"year": "1119", "text": "<PERSON> <PERSON><PERSON> (b. 1060)", "html": "1119 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_II\" title=\"<PERSON> <PERSON><PERSON><PERSON> II\"><PERSON> <PERSON><PERSON><PERSON> II</a> (b. 1060)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON><PERSON>sius II\">Pope <PERSON><PERSON><PERSON> II</a> (b. 1060)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1597", "text": "<PERSON>, German organist and composer (b. 1530)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1530)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1647", "text": "<PERSON>, English priest and author (b. 1565)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1565)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1678", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese missionary and author (b. 1593)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/Jer%C3%B3nimo_Lobo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese missionary and author (b. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jer%C3%B3nimo_Lobo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese missionary and author (b. 1593)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jer%C3%B3nimo_Lobo"}]}, {"year": "1706", "text": "<PERSON>, 6th Earl of Dorset, English poet and courtier (b. 1643)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Dorset\" title=\"<PERSON>, 6th Earl of Dorset\"><PERSON>, 6th Earl of Dorset</a>, English poet and courtier (b. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Dorset\" title=\"<PERSON>, 6th Earl of Dorset\"><PERSON>, 6th Earl of Dorset</a>, English poet and courtier (b. 1643)", "links": [{"title": "<PERSON>, 6th Earl of Dorset", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Dorset"}]}, {"year": "1737", "text": "<PERSON>, 1st Earl of Orkney, Scottish-English field marshal and politician, Colonial Governor of Virginia (b. 1666)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Orkney\" title=\"<PERSON>, 1st Earl of Orkney\"><PERSON>, 1st Earl of Orkney</a>, Scottish-English field marshal and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Colonial Governor of Virginia</a> (b. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Orkney\" title=\"<PERSON>, 1st Earl of Orkney\"><PERSON>, 1st Earl of Orkney</a>, Scottish-English field marshal and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Colonial Governor of Virginia</a> (b. 1666)", "links": [{"title": "<PERSON>, 1st Earl of Orkney", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Orkney"}, {"title": "List of colonial governors of Virginia", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia"}]}, {"year": "1743", "text": "<PERSON><PERSON><PERSON><PERSON>, French cardinal (b. 1653)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French cardinal (b. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French cardinal (b. 1653)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9-<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, Mexican bishop and Catholic scholar (b. 1696)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Eguiara_y_Eguren\" title=\"<PERSON> y <PERSON>\"><PERSON> y <PERSON></a>, Mexican bishop and Catholic scholar (b. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Eguiara_y_Eguren\" title=\"<PERSON> y <PERSON>\"><PERSON> y <PERSON></a>, Mexican bishop and Catholic scholar (b. 1696)", "links": [{"title": "<PERSON> y <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>ren"}]}, {"year": "1763", "text": "<PERSON>, French poet (b. 1692)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet (b. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet (b. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON> of the United Kingdom (b. 1738)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> of the United Kingdom\"><PERSON> of the United Kingdom</a> (b. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> of the United Kingdom\"><PERSON> of the United Kingdom</a> (b. 1738)", "links": [{"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom"}]}, {"year": "1829", "text": "<PERSON>, French captain and politician (b. 1755)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French captain and politician (b. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French captain and politician (b. 1755)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Grand Duke of Tuscany (b. 1797)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Tuscany\" title=\"<PERSON>, Grand Duke of Tuscany\"><PERSON>, Grand Duke of Tuscany</a> (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Tuscany\" title=\"<PERSON>, Grand Duke of Tuscany\"><PERSON>, Grand Duke of Tuscany</a> (b. 1797)", "links": [{"title": "<PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Tuscany"}]}, {"year": "1888", "text": "<PERSON>, English poet and illustrator (b. 1812)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and illustrator (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and illustrator (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, French-English painter (b. 1839)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English painter (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English painter (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, French bassoonist, composer and pedagogue (b. 1815)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bassoonist, composer and pedagogue (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bassoonist, composer and pedagogue (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON> Denmark (b. 1818)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Christian_IX_of_Denmark\" title=\"Christian IX of Denmark\">Christian IX of Denmark</a> (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_IX_of_Denmark\" title=\"Christian IX of Denmark\">Christian IX of Denmark</a> (b. 1818)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_IX_of_Denmark"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, French-Swiss novelist (b. 1857)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/%C3%89douard_Rod\" title=\"<PERSON><PERSON><PERSON> Rod\"><PERSON><PERSON><PERSON></a>, French-Swiss novelist (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89douard_Rod\" title=\"É<PERSON>uard Rod\"><PERSON><PERSON><PERSON></a>, French-Swiss novelist (b. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89douard_Rod"}]}, {"year": "1912", "text": "<PERSON>, Danish journalist and author (b. 1857)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish journalist and author (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bang\"><PERSON></a>, Danish journalist and author (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, German art teacher, author and nun (b. 1881)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German art teacher, author and nun (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German art teacher, author and nun (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, 1st Earl of Cromer, British statesman, diplomat and colonial administrator (b. 1841)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Cromer\" title=\"<PERSON>, 1st Earl of Cromer\"><PERSON>, 1st Earl of Cromer</a>, British statesman, diplomat and colonial administrator (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Cromer\" title=\"<PERSON>, 1st Earl of Cromer\"><PERSON>, 1st Earl of Cromer</a>, British statesman, diplomat and colonial administrator (b. 1841)", "links": [{"title": "<PERSON>, 1st Earl of Cromer", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_C<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American symbolist painter, book illustrator and poet (b. 1836)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American symbolist painter, book illustrator and poet (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American symbolist painter, book illustrator and poet (b. 1836)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Vedder"}]}, {"year": "1928", "text": "<PERSON>, 1st <PERSON>, Scottish field marshal (b. 1861)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, Scottish field marshal (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st <PERSON></a>, Scottish field marshal (b. 1861)", "links": [{"title": "<PERSON>, 1st Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Belgian baritone (b. 1848)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian baritone (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian baritone (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American preacher and evangelist (b. 1873)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American preacher and evangelist (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American preacher and evangelist (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ham"}]}, {"year": "1933", "text": "<PERSON>, American poet (b. 1884)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>sdale\"><PERSON></a>, American poet (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>sdale\"><PERSON></a>, American poet (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sara_Teasdale"}]}, {"year": "1934", "text": "<PERSON>, Polish-German chemist and engineer, Nobel Prize laureate (b. 1868)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, British botanist (b. 1854)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, British botanist (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, British botanist (b. 1854)", "links": [{"title": "Dukin<PERSON> Henry <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American explorer (b. 1853)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American philanthropist (b. 1874)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Greek general and politician, 130th Prime Minister of Greece (b. 1871)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general and politician, 130th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general and politician, 130th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ax<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1944", "text": "<PERSON>, American journalist and author (b. 1868)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American businessman and politician, 8th United States Secretary of Commerce (b. 1890)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Commerce", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Commerce"}]}, {"year": "1946", "text": "<PERSON>, English conductor and composer (b. 1861)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English conductor and composer (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English conductor and composer (b. 1861)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1948", "text": "<PERSON> <PERSON><PERSON><PERSON>, Duke of Aosta (b. 1900)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>,_Duke_of_Aosta\" title=\"Prince <PERSON><PERSON><PERSON>, Duke of Aosta\">Prince <PERSON><PERSON><PERSON>, Duke of Aosta</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>,_Duke_of_Aosta\" title=\"Prince <PERSON><PERSON><PERSON>, Duke of Aosta\">Prince <PERSON><PERSON><PERSON>, Duke of Aosta</a> (b. 1900)", "links": [{"title": "Prince <PERSON><PERSON><PERSON>, Duke of Aosta", "link": "https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>,_Duke_of_Aosta"}]}, {"year": "1951", "text": "<PERSON>, Scottish playwright, screenwriter and physician (b. 1888)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish playwright, screenwriter and physician (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish playwright, screenwriter and physician (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ie"}]}, {"year": "1954", "text": "<PERSON>, American art collector, critic and poet (b. 1878)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector, critic and poet (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector, critic and poet (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Danish politician (b. 1903)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish politician (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hans_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON> <PERSON><PERSON>, American journalist and critic (b. 1880)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American journalist and critic (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American journalist and critic (b. 1880)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, South African novelist, short story writer, memoirist and playwright (b. 1882)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African novelist, short story writer, memoirist and playwright (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African novelist, short story writer, memoirist and playwright (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American operatic and concert baritone vocalist (b. 1909)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic and concert baritone vocalist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic and concert baritone vocalist (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American diplomat (b. 1883)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English novelist (b. 1890)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Austrian-American violinist and composer (b. 1875)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American violinist and composer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American violinist and composer (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Anglo-American physicist (b. 1884)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-American physicist (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-American physicist (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American poet and playwright (b. 1874)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American folk singer (b. 1902)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Vera_Hall\" title=\"Vera Hall\"><PERSON></a>, American folk singer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vera_Hall\" title=\"Vera Hall\"><PERSON></a>, American folk singer (b. 1902)", "links": [{"title": "Vera <PERSON>", "link": "https://wikipedia.org/wiki/Vera_Hall"}]}, {"year": "1964", "text": "<PERSON>, American actor (b. 1913)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English pianist, composer, band leader and impresario (b. 1892)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, band leader and impresario (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, band leader and impresario (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian composer, TV producer, bassoonist and administrator (b. 1927)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer, TV producer, bassoonist and administrator (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer, TV producer, bassoonist and administrator (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English zoologist (b. 1889)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American banker, lawyer, and diplomat, 5th Director of Central Intelligence (b. 1893)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker, lawyer, and diplomat, 5th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker, lawyer, and diplomat, 5th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Director of Central Intelligence", "link": "https://wikipedia.org/wiki/Director_of_Central_Intelligence"}]}, {"year": "1969", "text": "<PERSON>, Russian-American-Jewish linguist and cofounder of YIVO (b. 1894)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American-Jewish linguist and cofounder of <a href=\"https://wikipedia.org/wiki/YIVO\" title=\"YIVO\">YIVO</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American-Jewish linguist and cofounder of <a href=\"https://wikipedia.org/wiki/YIVO\" title=\"YIVO\">YIVO</a> (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "YIVO", "link": "https://wikipedia.org/wiki/YIVO"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Canadian painter (b. 1885)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON> <PERSON><PERSON>, French-English soldier, historian, and journalist (b. 1895)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, French-English soldier, historian, and journalist (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, French-English soldier, historian, and journalist (b. 1895)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, German composer (b. 1903)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON> <PERSON><PERSON>, English writer (b. 1905)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English writer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English writer (b. 1905)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American one-man band musician (b. 1896)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American one-man band musician (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American one-man band musician (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English record producer and pianist (b. 1922)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English record producer and pianist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English record producer and pianist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American comedian and actor (b. 1954)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor and military officer (b. 1891)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and military officer (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and military officer (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian politician, 28th Premier of Queensland (b. 1895)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1979", "text": "<PERSON>, American jazz drummer (b. 1926)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz drummer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz drummer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American entertainer (b. 1893)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entertainer (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entertainer (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON> <PERSON><PERSON>, New Zealander literary scholar (b. 1911)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON> <PERSON><PERSON><PERSON></a>, New Zealander literary scholar (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON> <PERSON><PERSON><PERSON></a>, New Zealander literary scholar (b. 1911)", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian poet, memoirist and novelist (b. 1909)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet, memoirist and novelist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet, memoirist and novelist (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, British biochemist (b. 1889)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British biochemist (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British biochemist (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian microbiologist (b. 1916)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian microbiologist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian microbiologist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, British physicist and metallurgist (b. 1905)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(metallurgist)\" title=\"<PERSON> (metallurgist)\"><PERSON></a>, British physicist and metallurgist (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(metallurgist)\" title=\"<PERSON> (metallurgist)\"><PERSON></a>, British physicist and metallurgist (b. 1905)", "links": [{"title": "<PERSON> (metallurgist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(metallurgist)"}]}, {"year": "1983", "text": "<PERSON>, American naval aviator, USN vice admiral (b. 1898)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American naval aviator, USN vice admiral (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American naval aviator, USN vice admiral (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress, dramatist and screenwriter (b. 1890)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dramatist and screenwriter (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dramatist and screenwriter (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, British mathematician (b. 1905)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hit<PERSON>\" title=\"<PERSON>hittaker\"><PERSON></a>, British mathematician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hittaker\" title=\"<PERSON>hittaker\"><PERSON></a>, British mathematician (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>taker"}]}, {"year": "1987", "text": "<PERSON>, American politician and judge, 101st Mayor of New York City (b. 1900)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ellitteri\" title=\"<PERSON>\"><PERSON></a>, American politician and judge, 101st <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ellitteri\" title=\"<PERSON>\"><PERSON></a>, American politician and judge, 101st <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>i"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1988", "text": "<PERSON>, American educator, scientist and White House advisor (b. 1904)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, scientist and White House advisor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, scientist and White House advisor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American theatre and film director, film producer, writer and actor (b. 1914)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theatre and film director, film producer, writer and actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theatre and film director, film producer, writer and actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Morton_DaCosta"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Japanese author and poet (b. 1907)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and poet (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and poet (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American singer-songwriter and producer (b. 1915)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Nigerian lawyer and jurist, 2nd Chief Justice of Nigeria (b. 1906)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Ad<PERSON><PERSON><PERSON><PERSON>_Ademola\" title=\"Adetoku<PERSON><PERSON> Ademola\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Nigerian lawyer and jurist, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Nigeria\" title=\"Chief Justice of Nigeria\">Chief Justice of Nigeria</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ad<PERSON><PERSON><PERSON><PERSON>_Ademola\" title=\"Ad<PERSON>ku<PERSON><PERSON> Ademola\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Nigerian lawyer and jurist, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Nigeria\" title=\"Chief Justice of Nigeria\">Chief Justice of Nigeria</a> (b. 1906)", "links": [{"title": "Adetokunbo Ademola", "link": "https://wikipedia.org/wiki/Adetokunbo_Ademola"}, {"title": "Chief Justice of Nigeria", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Nigeria"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Austrian skier (b. 1967)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian skier (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian skier (b. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American model and dancer (b. 1918)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>i_<PERSON>._Cyr\" title=\"<PERSON>i St. Cyr\"><PERSON><PERSON></a>, American model and dancer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>i_<PERSON>._Cyr\" title=\"Lili St. Cyr\"><PERSON><PERSON></a>, American model and dancer (b. 1918)", "links": [{"title": "Lili St. Cyr", "link": "https://wikipedia.org/wiki/Lili_<PERSON>._Cyr"}]}, {"year": "2002", "text": "<PERSON>, Canadian-American soldier and actor (b. 1914)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American soldier and actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American soldier and actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American lawyer and politician (b. 1911)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician (b. 1911)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "2004", "text": "<PERSON>, New Zealand author and poet (b. 1924)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand author and poet (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand author and poet (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli author, screenwriter, and director (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli author, screenwriter, and director (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli author, screenwriter, and director (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON> <PERSON>, South Korean-American artist (b. 1932)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Nam_June_Paik\" title=\"Nam June <PERSON>\"><PERSON> <PERSON></a>, South Korean-American artist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nam_June_Paik\" title=\"Nam June <PERSON>\"><PERSON> <PERSON></a>, South Korean-American artist (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nam_June_Paik"}]}, {"year": "2008", "text": "<PERSON>, American singer and author (b. 1924)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and author (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Brazilian martial artist (b. 1913)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_Gracie\" title=\"<PERSON><PERSON><PERSON> Gracie\"><PERSON><PERSON><PERSON></a>, Brazilian martial artist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_Gracie\" title=\"<PERSON><PERSON><PERSON> Gracie\"><PERSON><PERSON><PERSON></a>, Brazilian martial artist (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9lio_Gracie"}]}, {"year": "2009", "text": "<PERSON>, British singer-songwriter and guitarist (b. 1948)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British singer-songwriter and guitarist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British singer-songwriter and guitarist (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American composer, educator, and theorist (b. 1916)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, educator, and theorist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, educator, and theorist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Indian general and politician, 10th Lieutenant Governor of Puducherry (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian general and politician, 10th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Puducherry\" class=\"mw-redirect\" title=\"Lieutenant Governor of Puducherry\">Lieutenant Governor of Puducherry</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian general and politician, 10th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Puducherry\" class=\"mw-redirect\" title=\"Lieutenant Governor of Puducherry\">Lieutenant Governor of Puducherry</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Puducherry", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Puducherry"}]}, {"year": "2012", "text": "<PERSON>, Italian lawyer and politician, 9th President of Italy (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American soprano and educator (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and educator (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and educator (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Australian neuroscientist, author, and academic (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian neuroscientist, author, and academic (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian neuroscientist, author, and academic (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American singer-songwriter and poet (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American commander and pilot (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and pilot (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and pilot (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Guinean lawyer and politician, 11th Prime Minister of Guinea (b. 1938)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guinean lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Guinea\" class=\"mw-redirect\" title=\"Prime Minister of Guinea\">Prime Minister of Guinea</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guinean lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Guinea\" class=\"mw-redirect\" title=\"Prime Minister of Guinea\">Prime Minister of Guinea</a> (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9"}, {"title": "Prime Minister of Guinea", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Guinea"}]}, {"year": "2016", "text": "<PERSON>, French director, screenwriter, and critic (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, screenwriter, and critic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, screenwriter, and critic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Indian politician (b. 1930)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American musician (b. 1952)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Canadian actor (b. 1944)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American actor (b. 1940)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Canadian businesswoman and politician, 5th Mayor of Mississauga (b. 1921)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman and politician, 5th <a href=\"https://wikipedia.org/wiki/Mayor_of_Mississauga\" title=\"Mayor of Mississauga\">Mayor of Mississauga</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman and politician, 5th <a href=\"https://wikipedia.org/wiki/Mayor_of_Mississauga\" title=\"Mayor of Mississauga\">Mayor of Mississauga</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Mississauga", "link": "https://wikipedia.org/wiki/Mayor_of_Mississauga"}]}, {"year": "2023", "text": "<PERSON>, American-Australian chemist (b. 1947)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian chemist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian chemist (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Will_Steffen"}]}, {"year": "2023", "text": "<PERSON><PERSON>, German politician (b. 1958)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "2025", "text": "<PERSON><PERSON>, Iraqi refugee and anti-Islam activist (b. 1986)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ika\" title=\"Salwan Momika\"><PERSON><PERSON></a>, Iraqi refugee and anti-Islam activist (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Salwan Momika\"><PERSON><PERSON></a>, Iraqi refugee and anti-Islam activist (b. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sal<PERSON>_<PERSON>ika"}]}, {"year": "2025", "text": "<PERSON>, British Catholic traditionalist bishop (b. 1940)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, British Catholic traditionalist bishop (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, British Catholic traditionalist bishop (b. 1940)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)"}]}, {"year": "2025", "text": "Victims in the 2025 Potomac River mid-air collision:\n<PERSON><PERSON><PERSON>, Russian pair skater (b. 1969)\n<PERSON><PERSON><PERSON><PERSON>, Russian pair skater and coach (b. 1972)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/2025_Potomac_River_mid-air_collision\" title=\"2025 Potomac River mid-air collision\">Victims in the 2025 Potomac River mid-air collision</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pair skater (b. 1969)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian pair skater and coach (b. 1972)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2025_Potomac_River_mid-air_collision\" title=\"2025 Potomac River mid-air collision\">Victims in the 2025 Potomac River mid-air collision</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pair skater (b. 1969)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian pair skater and coach (b. 1972)</li>\n</ul>", "links": [{"title": "2025 Potomac River mid-air collision", "link": "https://wikipedia.org/wiki/2025_Potomac_River_mid-air_collision"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, Russian pair skater (b. 1969)", "text": null, "html": "<PERSON><PERSON><PERSON>, Russian pair skater (b. 1969) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pair skater (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pair skater (b. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON><PERSON>, Russian pair skater and coach (b. 1972)", "text": null, "html": "<PERSON><PERSON><PERSON><PERSON>, Russian pair skater and coach (b. 1972) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Ev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian pair skater and coach (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian pair skater and coach (b. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>"}]}]}}