{"date": "January 13", "url": "https://wikipedia.org/wiki/January_13", "data": {"Events": [{"year": "27 BC", "text": "<PERSON><PERSON><PERSON> transfers the state to the free disposal of the Roman Senate and the people. He receives Spain, Gaul, and Syria as his province for ten years.", "html": "27 BC - 27 BC - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a> transfers the state to the free disposal of the <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a> and the people. He receives <a href=\"https://wikipedia.org/wiki/Hispania\" title=\"Hispania\">Spain</a>, <a href=\"https://wikipedia.org/wiki/Roman_Gaul\" title=\"Roman Gaul\">Gaul</a>, and <a href=\"https://wikipedia.org/wiki/Roman_Syria\" title=\"Roman Syria\">Syria</a> as his province for ten years.", "no_year_html": "27 BC - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a> transfers the state to the free disposal of the <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a> and the people. He receives <a href=\"https://wikipedia.org/wiki/Hispania\" title=\"Hispania\">Spain</a>, <a href=\"https://wikipedia.org/wiki/Roman_Gaul\" title=\"Roman Gaul\">Gaul</a>, and <a href=\"https://wikipedia.org/wiki/Roman_Syria\" title=\"Roman Syria\">Syria</a> as his province for ten years.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}, {"title": "Roman Senate", "link": "https://wikipedia.org/wiki/Roman_Senate"}, {"title": "Hispania", "link": "https://wikipedia.org/wiki/Hispania"}, {"title": "Roman Gaul", "link": "https://wikipedia.org/wiki/Roman_Gaul"}, {"title": "Roman Syria", "link": "https://wikipedia.org/wiki/Roman_Syria"}]}, {"year": "532", "text": "The Nika riots break out, during the racing season at the Hippodrome in Constantinople, as a result of discontent with the rule of the Emperor <PERSON><PERSON>.", "html": "532 - The <a href=\"https://wikipedia.org/wiki/Nika_riots\" title=\"Nika riots\">Nika riots</a> break out, during the racing season at the <a href=\"https://wikipedia.org/wiki/Hippodrome\" title=\"Hippodrome\">Hippodrome</a> in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>, as a result of discontent with the rule of the Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> I</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nika_riots\" title=\"Nika riots\">Nika riots</a> break out, during the racing season at the <a href=\"https://wikipedia.org/wiki/Hippodrome\" title=\"Hippodrome\">Hippodrome</a> in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>, as a result of discontent with the rule of the Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> I</a>.", "links": [{"title": "Nika riots", "link": "https://wikipedia.org/wiki/Nika_riots"}, {"title": "Hippodrome", "link": "https://wikipedia.org/wiki/Hippodrome"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1435", "text": "Sicut <PERSON>, forbidding the enslavement by the Spanish of the Guanche natives in Canary Islands who had converted, or were converting to, Christianity, is promulgated by Pope <PERSON>.", "html": "1435 - <i><a href=\"https://wikipedia.org/wiki/Sicut_Dudum\" class=\"mw-redirect\" title=\"Sicut Dudum\">Sicut Dudum</a></i>, forbidding the enslavement by the Spanish of the <a href=\"https://wikipedia.org/wiki/Guanches\" title=\"Guanches\">Guanche</a> natives in <a href=\"https://wikipedia.org/wiki/Canary_Islands\" title=\"Canary Islands\">Canary Islands</a> who had converted, or were converting to, Christianity, is promulgated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Eugene IV\"><PERSON> IV</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Sicut_Dudum\" class=\"mw-redirect\" title=\"Sicut Dudum\">Sicut Dudum</a></i>, forbidding the enslavement by the Spanish of the <a href=\"https://wikipedia.org/wiki/Guanches\" title=\"Guanches\">Guanche</a> natives in <a href=\"https://wikipedia.org/wiki/Canary_Islands\" title=\"Canary Islands\">Canary Islands</a> who had converted, or were converting to, Christianity, is promulgated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"<PERSON> Eugene IV\"><PERSON> IV</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sicut_Dudum"}, {"title": "Guanches", "link": "https://wikipedia.org/wiki/Guanches"}, {"title": "Canary Islands", "link": "https://wikipedia.org/wiki/Canary_Islands"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1547", "text": "<PERSON>, Earl of Surrey, is sentenced to death for treason, on the grounds of having quartered his arms to make them similar to those of the King, <PERSON> of England.", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Earl_of_Surrey\" title=\"<PERSON>, Earl of Surrey\"><PERSON>, Earl of Surrey</a>, is sentenced to death for treason, on the grounds of having quartered his arms to make them similar to those of the King, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON> of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Earl_of_Surrey\" title=\"<PERSON>, Earl of Surrey\"><PERSON>, Earl of Surrey</a>, is sentenced to death for treason, on the grounds of having quartered his arms to make them similar to those of the King, <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON> of England</a>.", "links": [{"title": "<PERSON>, Earl of Surrey", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Surrey"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1793", "text": "<PERSON>, representative of Revolutionary France, is lynched by a mob in Rome.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, representative of <a href=\"https://wikipedia.org/wiki/Revolutionary_France\" class=\"mw-redirect\" title=\"Revolutionary France\">Revolutionary France</a>, is lynched by a mob in Rome.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, representative of <a href=\"https://wikipedia.org/wiki/Revolutionary_France\" class=\"mw-redirect\" title=\"Revolutionary France\">Revolutionary France</a>, is lynched by a mob in Rome.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Revolutionary France", "link": "https://wikipedia.org/wiki/Revolutionary_France"}]}, {"year": "1797", "text": "French Revolutionary Wars: A naval battle between a French ship of the line and two British frigates off the coast of Brittany ends with the French vessel running aground, resulting in over 900 deaths.", "html": "1797 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: A <a href=\"https://wikipedia.org/wiki/Action_of_13_January_1797\" title=\"Action of 13 January 1797\">naval battle</a> between a French <a href=\"https://wikipedia.org/wiki/Ship_of_the_line\" title=\"Ship of the line\">ship of the line</a> and two British frigates off the coast of <a href=\"https://wikipedia.org/wiki/Brittany\" title=\"Brittany\">Brittany</a> ends with the French vessel running aground, resulting in over 900 deaths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: A <a href=\"https://wikipedia.org/wiki/Action_of_13_January_1797\" title=\"Action of 13 January 1797\">naval battle</a> between a French <a href=\"https://wikipedia.org/wiki/Ship_of_the_line\" title=\"Ship of the line\">ship of the line</a> and two British frigates off the coast of <a href=\"https://wikipedia.org/wiki/Brittany\" title=\"Brittany\">Brittany</a> ends with the French vessel running aground, resulting in over 900 deaths.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "Action of 13 January 1797", "link": "https://wikipedia.org/wiki/Action_of_13_January_1797"}, {"title": "Ship of the line", "link": "https://wikipedia.org/wiki/Ship_of_the_line"}, {"title": "Brittany", "link": "https://wikipedia.org/wiki/Brittany"}]}, {"year": "1815", "text": "War of 1812: British troops capture Fort Peter in St. Marys, Georgia, the only battle of the war to take place in the state.", "html": "1815 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> troops capture <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Peter\" title=\"Battle of Fort Peter\">Fort Peter</a> in <a href=\"https://wikipedia.org/wiki/St._Marys,_Georgia\" title=\"St. Marys, Georgia\">St. Marys, Georgia</a>, the only battle of the war to take place in the state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> troops capture <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Peter\" title=\"Battle of Fort Peter\">Fort Peter</a> in <a href=\"https://wikipedia.org/wiki/St._Marys,_Georgia\" title=\"St. Marys, Georgia\">St. Marys, Georgia</a>, the only battle of the war to take place in the state.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}, {"title": "Battle of Fort Peter", "link": "https://wikipedia.org/wiki/Battle_of_Fort_Peter"}, {"title": "St. Marys, Georgia", "link": "https://wikipedia.org/wiki/St._Marys,_Georgia"}]}, {"year": "1822", "text": "The design of the Greek flag is adopted by the First National Assembly at Epidaurus.", "html": "1822 - The design of the <a href=\"https://wikipedia.org/wiki/Flag_of_Greece\" title=\"Flag of Greece\">Greek flag</a> is adopted by the <a href=\"https://wikipedia.org/wiki/First_National_Assembly_at_Epidaurus\" title=\"First National Assembly at Epidaurus\">First National Assembly at Epidaurus</a>.", "no_year_html": "The design of the <a href=\"https://wikipedia.org/wiki/Flag_of_Greece\" title=\"Flag of Greece\">Greek flag</a> is adopted by the <a href=\"https://wikipedia.org/wiki/First_National_Assembly_at_Epidaurus\" title=\"First National Assembly at Epidaurus\">First National Assembly at Epidaurus</a>.", "links": [{"title": "Flag of Greece", "link": "https://wikipedia.org/wiki/Flag_of_Greece"}, {"title": "First National Assembly at Epidaurus", "link": "https://wikipedia.org/wiki/First_National_Assembly_at_Epidaurus"}]}, {"year": "1833", "text": "United States President <PERSON> writes to Vice President elect <PERSON> expressing his opposition to South Carolina's defiance of federal authority in the Nullification Crisis.", "html": "1833 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> writes to <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President</a> elect <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> expressing his opposition to <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>'s defiance of federal authority in the <a href=\"https://wikipedia.org/wiki/Nullification_Crisis\" class=\"mw-redirect\" title=\"Nullification Crisis\">Nullification Crisis</a>.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> writes to <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President</a> elect <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> expressing his opposition to <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>'s defiance of federal authority in the <a href=\"https://wikipedia.org/wiki/Nullification_Crisis\" class=\"mw-redirect\" title=\"Nullification Crisis\">Nullification Crisis</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}, {"title": "Nullification Crisis", "link": "https://wikipedia.org/wiki/Nullification_Crisis"}]}, {"year": "1840", "text": "The steamship Lexington burns and sinks four miles off the coast of Long Island with the loss of 139 lives.", "html": "1840 - The steamship <i><a href=\"https://wikipedia.org/wiki/Lexington_(steamship)\" title=\"Lexington (steamship)\">Lexington</a></i> burns and sinks four miles off the coast of <a href=\"https://wikipedia.org/wiki/Long_Island\" title=\"Long Island\">Long Island</a> with the loss of 139 lives.", "no_year_html": "The steamship <i><a href=\"https://wikipedia.org/wiki/Lexington_(steamship)\" title=\"Lexington (steamship)\">Lexington</a></i> burns and sinks four miles off the coast of <a href=\"https://wikipedia.org/wiki/Long_Island\" title=\"Long Island\">Long Island</a> with the loss of 139 lives.", "links": [{"title": "Lexington (steamship)", "link": "https://wikipedia.org/wiki/Lexington_(steamship)"}, {"title": "Long Island", "link": "https://wikipedia.org/wiki/Long_Island"}]}, {"year": "1842", "text": "Dr. <PERSON>, an assistant surgeon in the British East India Company Army during the First Anglo-Afghan War, becomes famous for being the sole survivor of an army of 4,500 men and 12,000 camp followers when he reaches the safety of a garrison in Jalalabad, Afghanistan.", "html": "1842 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, an assistant surgeon in the British <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> Army during the <a href=\"https://wikipedia.org/wiki/First_Anglo-Afghan_War\" title=\"First Anglo-Afghan War\">First Anglo-Afghan War</a>, becomes famous for being the <a href=\"https://wikipedia.org/wiki/1842_retreat_from_Kabul\" title=\"1842 retreat from Kabul\">sole survivor of an army</a> of 4,500 men and 12,000 <a href=\"https://wikipedia.org/wiki/Camp_follower\" title=\"Camp follower\">camp followers</a> when he reaches the safety of a garrison in <a href=\"https://wikipedia.org/wiki/Jalalabad\" title=\"Jalalabad\">Jalalabad</a>, Afghanistan.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, an assistant surgeon in the British <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> Army during the <a href=\"https://wikipedia.org/wiki/First_Anglo-Afghan_War\" title=\"First Anglo-Afghan War\">First Anglo-Afghan War</a>, becomes famous for being the <a href=\"https://wikipedia.org/wiki/1842_retreat_from_Kabul\" title=\"1842 retreat from Kabul\">sole survivor of an army</a> of 4,500 men and 12,000 <a href=\"https://wikipedia.org/wiki/Camp_follower\" title=\"Camp follower\">camp followers</a> when he reaches the safety of a garrison in <a href=\"https://wikipedia.org/wiki/Jalalabad\" title=\"Jalalabad\">Jalalabad</a>, Afghanistan.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "East India Company", "link": "https://wikipedia.org/wiki/East_India_Company"}, {"title": "First Anglo-Afghan War", "link": "https://wikipedia.org/wiki/First_Anglo-Afghan_War"}, {"title": "1842 retreat from Kabul", "link": "https://wikipedia.org/wiki/1842_retreat_from_Kabul"}, {"title": "Camp follower", "link": "https://wikipedia.org/wiki/Camp_follower"}, {"title": "Jalalabad", "link": "https://wikipedia.org/wiki/Jalalabad"}]}, {"year": "1847", "text": "The Treaty of Cahuenga ends the Mexican-American War in California.", "html": "1847 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Cahuenga\" title=\"Treaty of Cahuenga\">Treaty of Cahuenga</a> ends the <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Cahuenga\" title=\"Treaty of Cahuenga\">Treaty of Cahuenga</a> ends the <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "links": [{"title": "Treaty of Cahuenga", "link": "https://wikipedia.org/wiki/Treaty_of_Cahuenga"}, {"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}]}, {"year": "1849", "text": "Establishment of the Colony of Vancouver Island.", "html": "1849 - Establishment of the <a href=\"https://wikipedia.org/wiki/Colony_of_Vancouver_Island\" title=\"Colony of Vancouver Island\">Colony of Vancouver Island</a>.", "no_year_html": "Establishment of the <a href=\"https://wikipedia.org/wiki/Colony_of_Vancouver_Island\" title=\"Colony of Vancouver Island\">Colony of Vancouver Island</a>.", "links": [{"title": "Colony of Vancouver Island", "link": "https://wikipedia.org/wiki/Colony_of_Vancouver_Island"}]}, {"year": "1849", "text": "Second Anglo-Sikh War: Battle of Chillianwala: British forces retreat from the Sikhs.", "html": "1849 - Second Anglo-Sikh War: <a href=\"https://wikipedia.org/wiki/Battle_of_Chillianwala\" title=\"Battle of Chillianwala\">Battle of Chillianwala</a>: British forces retreat from the Sikhs.", "no_year_html": "Second Anglo-Sikh War: <a href=\"https://wikipedia.org/wiki/Battle_of_Chillianwala\" title=\"Battle of Chillianwala\">Battle of Chillianwala</a>: British forces retreat from the Sikhs.", "links": [{"title": "Battle of Chillianwala", "link": "https://wikipedia.org/wiki/Battle_of_Chillianwala"}]}, {"year": "1888", "text": "The National Geographic Society is founded in Washington, D.C.", "html": "1888 - The <a href=\"https://wikipedia.org/wiki/National_Geographic_Society\" title=\"National Geographic Society\">National Geographic Society</a> is founded in Washington, D.C.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Geographic_Society\" title=\"National Geographic Society\">National Geographic Society</a> is founded in Washington, D.C.", "links": [{"title": "National Geographic Society", "link": "https://wikipedia.org/wiki/National_Geographic_Society"}]}, {"year": "1893", "text": "The Independent Labour Party of the United Kingdom holds its first meeting.", "html": "1893 - The <a href=\"https://wikipedia.org/wiki/Independent_Labour_Party\" title=\"Independent Labour Party\">Independent Labour Party</a> of the United Kingdom holds its first meeting.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Independent_Labour_Party\" title=\"Independent Labour Party\">Independent Labour Party</a> of the United Kingdom holds its first meeting.", "links": [{"title": "Independent Labour Party", "link": "https://wikipedia.org/wiki/Independent_Labour_Party"}]}, {"year": "1893", "text": "U.S. Marines land in Honolulu, Hawaii from the USS Boston to prevent the queen from abrogating the Bayonet Constitution.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> land in <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu, Hawaii</a> from the <a href=\"https://wikipedia.org/wiki/USS_Boston_(1884)\" title=\"USS Boston (1884)\">USS <i>Boston</i></a> to prevent the queen from abrogating the <a href=\"https://wikipedia.org/wiki/1887_Constitution_of_the_Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"1887 Constitution of the Kingdom of Hawaii\">Bayonet Constitution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> land in <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu, Hawaii</a> from the <a href=\"https://wikipedia.org/wiki/USS_Boston_(1884)\" title=\"USS Boston (1884)\">USS <i>Boston</i></a> to prevent the queen from abrogating the <a href=\"https://wikipedia.org/wiki/1887_Constitution_of_the_Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"1887 Constitution of the Kingdom of Hawaii\">Bayonet Constitution</a>.", "links": [{"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "Honolulu", "link": "https://wikipedia.org/wiki/Honolulu"}, {"title": "USS Boston (1884)", "link": "https://wikipedia.org/wiki/USS_Boston_(1884)"}, {"title": "1887 Constitution of the Kingdom of Hawaii", "link": "https://wikipedia.org/wiki/1887_Constitution_of_the_Kingdom_of_Hawaii"}]}, {"year": "1895", "text": "First Italo-Ethiopian War: The war's opening battle, the Battle of Coatit, occurs; it is an Italian victory.", "html": "1895 - <a href=\"https://wikipedia.org/wiki/First_Italo-Ethiopian_War\" title=\"First Italo-Ethiopian War\">First Italo-Ethiopian War</a>: The war's opening battle, the <a href=\"https://wikipedia.org/wiki/Battle_of_Coatit\" title=\"Battle of Coatit\">Battle of Coatit</a>, occurs; it is an Italian victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Italo-Ethiopian_War\" title=\"First Italo-Ethiopian War\">First Italo-Ethiopian War</a>: The war's opening battle, the <a href=\"https://wikipedia.org/wiki/Battle_of_Coatit\" title=\"Battle of Coatit\">Battle of Coatit</a>, occurs; it is an Italian victory.", "links": [{"title": "First Italo-Ethiopian War", "link": "https://wikipedia.org/wiki/First_Italo-Ethiopian_War"}, {"title": "Battle of Coatit", "link": "https://wikipedia.org/wiki/Battle_of_Coatit"}]}, {"year": "1898", "text": "<PERSON><PERSON>'s J<PERSON>accuse…! exposes the <PERSON><PERSON><PERSON><PERSON> affair.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Zola\" title=\"Émile <PERSON>\"><PERSON><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/J%27accuse%E2%80%A6!\" class=\"mw-redirect\" title=\"J'accuse…!\">J'accuse…!</a></i> exposes the <a href=\"https://wikipedia.org/wiki/Dreyfus_affair\" title=\"Dreyfus affair\">Dreyfus affair</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Zola\" title=\"Émile <PERSON>\"><PERSON><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/J%27accuse%E2%80%A6!\" class=\"mw-redirect\" title=\"J'accuse…!\">J'accuse…!</a></i> exposes the <a href=\"https://wikipedia.org/wiki/Dreyfus_affair\" title=\"Dreyfus affair\">Dreyfus affair</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Zola"}, {"title": "J'accuse…!", "link": "https://wikipedia.org/wiki/J%27accuse%E2%80%A6!"}, {"title": "<PERSON><PERSON><PERSON><PERSON> affair", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_affair"}]}, {"year": "1900", "text": "To combat Czech nationalism, Emperor <PERSON> decrees German will be language of the Austro-Hungarian Armed Forces.", "html": "1900 - To combat <a href=\"https://wikipedia.org/wiki/Czech_nationalism\" title=\"Czech nationalism\">Czech nationalism</a>, Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> decrees <a href=\"https://wikipedia.org/wiki/German_language\" title=\"German language\">German</a> will be language of the <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Armed_Forces\" title=\"Austro-Hungarian Armed Forces\">Austro-Hungarian Armed Forces</a>.", "no_year_html": "To combat <a href=\"https://wikipedia.org/wiki/Czech_nationalism\" title=\"Czech nationalism\">Czech nationalism</a>, Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> decrees <a href=\"https://wikipedia.org/wiki/German_language\" title=\"German language\">German</a> will be language of the <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Armed_Forces\" title=\"Austro-Hungarian Armed Forces\">Austro-Hungarian Armed Forces</a>.", "links": [{"title": "Czech nationalism", "link": "https://wikipedia.org/wiki/Czech_nationalism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "German language", "link": "https://wikipedia.org/wiki/German_language"}, {"title": "Austro-Hungarian Armed Forces", "link": "https://wikipedia.org/wiki/Austro-Hungarian_Armed_Forces"}]}, {"year": "1908", "text": "The Rhoads Opera House fire in Boyertown, Pennsylvania kills 171 people.", "html": "1908 - The <a href=\"https://wikipedia.org/wiki/Rhoads_Opera_House_fire\" title=\"Rhoads Opera House fire\">Rhoads Opera House fire</a> in <a href=\"https://wikipedia.org/wiki/Boyertown,_Pennsylvania\" title=\"Boyertown, Pennsylvania\">Boyertown, Pennsylvania</a> kills 171 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rhoads_Opera_House_fire\" title=\"Rhoads Opera House fire\">Rhoads Opera House fire</a> in <a href=\"https://wikipedia.org/wiki/Boyertown,_Pennsylvania\" title=\"Boyertown, Pennsylvania\">Boyertown, Pennsylvania</a> kills 171 people.", "links": [{"title": "Rhoads Opera House fire", "link": "https://wikipedia.org/wiki/Rhoads_Opera_House_fire"}, {"title": "Boyertown, Pennsylvania", "link": "https://wikipedia.org/wiki/Boyertown,_Pennsylvania"}]}, {"year": "1915", "text": "The 6.7 Mw  Avezzano earthquake shakes the Province of L'Aquila in Italy with a maximum Mercalli intensity of XI (Extreme), killing between 29,978 and 32,610.", "html": "1915 - The 6.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1915_Avezzano_earthquake\" title=\"1915 Avezzano earthquake\">Avezzano earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Province_of_L%27Aquila\" title=\"Province of L'Aquila\">Province of L'Aquila</a> in Italy with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>), killing between 29,978 and 32,610.", "no_year_html": "The 6.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1915_Avezzano_earthquake\" title=\"1915 Avezzano earthquake\">Avezzano earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Province_of_L%27Aquila\" title=\"Province of L'Aquila\">Province of L'Aquila</a> in Italy with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>), killing between 29,978 and 32,610.", "links": [{"title": "1915 Avezzano earthquake", "link": "https://wikipedia.org/wiki/1915_Avezzano_earthquake"}, {"title": "Province of L'Aquila", "link": "https://wikipedia.org/wiki/Province_of_L%27Aquila"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1920", "text": "The Reichstag Bloodbath of January 13, 1920, the bloodiest demonstration in German history.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/Reichstag_Bloodbath\" title=\"Reichstag Bloodbath\">Reichstag Bloodbath of January 13, 1920</a>, the bloodiest demonstration in German history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Reichstag_Bloodbath\" title=\"Reichstag Bloodbath\">Reichstag Bloodbath of January 13, 1920</a>, the bloodiest demonstration in German history.", "links": [{"title": "Reichstag Bloodbath", "link": "https://wikipedia.org/wiki/Reichstag_Bloodbath"}]}, {"year": "1935", "text": "A plebiscite in Saarland shows that 90.3% of those voting wish to no more being a \"region occupied and governed by the United Kingdom and France\".", "html": "1935 - A <a href=\"https://wikipedia.org/wiki/1935_Saar_status_referendum\" title=\"1935 Saar status referendum\">plebiscite</a> in <a href=\"https://wikipedia.org/wiki/Saar_(League_of_Nations)\" class=\"mw-redirect\" title=\"Saar (League of Nations)\">Saarland</a> shows that 90.3% of those voting wish to no more <a href=\"https://wikipedia.org/wiki/Territory_of_the_Saar_Basin\" title=\"Territory of the Saar Basin\">being a \"region occupied and governed by the United Kingdom and France\"</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1935_Saar_status_referendum\" title=\"1935 Saar status referendum\">plebiscite</a> in <a href=\"https://wikipedia.org/wiki/Saar_(League_of_Nations)\" class=\"mw-redirect\" title=\"Saar (League of Nations)\">Saarland</a> shows that 90.3% of those voting wish to no more <a href=\"https://wikipedia.org/wiki/Territory_of_the_Saar_Basin\" title=\"Territory of the Saar Basin\">being a \"region occupied and governed by the United Kingdom and France\"</a>.", "links": [{"title": "1935 Saar status referendum", "link": "https://wikipedia.org/wiki/1935_Saar_status_referendum"}, {"title": "Saar (League of Nations)", "link": "https://wikipedia.org/wiki/Saar_(League_of_Nations)"}, {"title": "Territory of the Saar Basin", "link": "https://wikipedia.org/wiki/Territory_of_the_Saar_Basin"}]}, {"year": "1939", "text": "The Black Friday bushfires burn 20,000 square kilometres (7,700 sq mi) of land in Australia, claiming the lives of 71 people.", "html": "1939 - The <a href=\"https://wikipedia.org/wiki/Black_Friday_bushfires\" title=\"Black Friday bushfires\">Black Friday bushfires</a> burn 20,000 square kilometres (7,700 sq mi) of land in Australia, claiming the lives of 71 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Black_Friday_bushfires\" title=\"Black Friday bushfires\">Black Friday bushfires</a> burn 20,000 square kilometres (7,700 sq mi) of land in Australia, claiming the lives of 71 people.", "links": [{"title": "Black Friday bushfires", "link": "https://wikipedia.org/wiki/Black_Friday_bushfires"}]}, {"year": "1942", "text": "Henry Ford patents a soybean car, which is 30% lighter than a regular car.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Henry Ford\"><PERSON></a> patents a <a href=\"https://wikipedia.org/wiki/Soybean_car\" title=\"Soybean car\">soybean car</a>, which is 30% lighter than a regular car.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ford\" title=\"Henry Ford\"><PERSON></a> patents a <a href=\"https://wikipedia.org/wiki/Soybean_car\" title=\"Soybean car\">soybean car</a>, which is 30% lighter than a regular car.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soybean car", "link": "https://wikipedia.org/wiki/Soybean_car"}]}, {"year": "1942", "text": "World War II: First use of an aircraft ejection seat by a German test pilot in a Heinkel He 280 jet fighter.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: First use of an aircraft <a href=\"https://wikipedia.org/wiki/Ejection_seat\" title=\"Ejection seat\">ejection seat</a> by a German test pilot in a <a href=\"https://wikipedia.org/wiki/Heinkel_He_280\" title=\"Heinkel He 280\">Heinkel He 280</a> jet fighter.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: First use of an aircraft <a href=\"https://wikipedia.org/wiki/Ejection_seat\" title=\"Ejection seat\">ejection seat</a> by a German test pilot in a <a href=\"https://wikipedia.org/wiki/Heinkel_He_280\" title=\"Heinkel He 280\">Heinkel He 280</a> jet fighter.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Ejection seat", "link": "https://wikipedia.org/wiki/Ejection_seat"}, {"title": "<PERSON><PERSON><PERSON> 280", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_280"}]}, {"year": "1950", "text": "British submarine HMS Truculent collides with an oil tanker in the Thames Estuary, killing 64 men.", "html": "1950 - British submarine <a href=\"https://wikipedia.org/wiki/HMS_Truculent_(P315)\" title=\"HMS Truculent (P315)\">HMS <i>Truculent</i></a> collides with an oil tanker in the <a href=\"https://wikipedia.org/wiki/Thames_Estuary\" title=\"Thames Estuary\">Thames Estuary</a>, killing 64 men.", "no_year_html": "British submarine <a href=\"https://wikipedia.org/wiki/HMS_Truculent_(P315)\" title=\"HMS Truculent (P315)\">HMS <i>Truculent</i></a> collides with an oil tanker in the <a href=\"https://wikipedia.org/wiki/Thames_Estuary\" title=\"Thames Estuary\">Thames Estuary</a>, killing 64 men.", "links": [{"title": "HMS Truculent (P315)", "link": "https://wikipedia.org/wiki/HMS_Truculent_(P315)"}, {"title": "Thames Estuary", "link": "https://wikipedia.org/wiki/Thames_Estuary"}]}, {"year": "1950", "text": "Finland forms diplomatic relations with the People's Republic of China.", "html": "1950 - Finland forms <a href=\"https://wikipedia.org/wiki/China%E2%80%93Finland_relations\" title=\"China-Finland relations\">diplomatic relations</a> with the People's Republic of China.", "no_year_html": "Finland forms <a href=\"https://wikipedia.org/wiki/China%E2%80%93Finland_relations\" title=\"China-Finland relations\">diplomatic relations</a> with the People's Republic of China.", "links": [{"title": "China-Finland relations", "link": "https://wikipedia.org/wiki/China%E2%80%93Finland_relations"}]}, {"year": "1951", "text": "First Indochina War: The Battle of Vĩnh Yên begins.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_V%C4%A9nh_Y%C3%AAn\" title=\"Battle of Vĩnh Yên\">Battle of Vĩnh Yên</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_V%C4%A9nh_Y%C3%AAn\" title=\"Battle of Vĩnh Yên\">Battle of Vĩnh Yên</a> begins.", "links": [{"title": "First Indochina War", "link": "https://wikipedia.org/wiki/First_Indochina_War"}, {"title": "Battle of Vĩnh Yên", "link": "https://wikipedia.org/wiki/Battle_of_V%C4%A9nh_Y%C3%AAn"}]}, {"year": "1953", "text": "An article appears in Pravda accusing some of the most prestigious and prominent doctors, mostly Jews, in the Soviet Union of taking part in a vast plot to poison members of the top Soviet political and military leadership.", "html": "1953 - An article appears in <i><a href=\"https://wikipedia.org/wiki/Pravda\" title=\"Pravda\">Pravda</a></i> accusing some of the most prestigious and prominent doctors, mostly Jews, in the Soviet Union of taking part in a <a href=\"https://wikipedia.org/wiki/Doctors%27_plot\" title=\"Doctors' plot\">vast plot</a> to poison members of the top Soviet political and military leadership.", "no_year_html": "An article appears in <i><a href=\"https://wikipedia.org/wiki/Pravda\" title=\"Pravda\">Pravda</a></i> accusing some of the most prestigious and prominent doctors, mostly Jews, in the Soviet Union of taking part in a <a href=\"https://wikipedia.org/wiki/Doctors%27_plot\" title=\"Doctors' plot\">vast plot</a> to poison members of the top Soviet political and military leadership.", "links": [{"title": "Pravda", "link": "https://wikipedia.org/wiki/Pravda"}, {"title": "Doctors' plot", "link": "https://wikipedia.org/wiki/Doctors%27_plot"}]}, {"year": "1958", "text": "The Moroccan Army of Liberation ambushes a Spanish patrol in the Battle of Edchera.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/Moroccan_Army_of_Liberation\" title=\"Moroccan Army of Liberation\">Moroccan Army of Liberation</a> ambushes a Spanish patrol in the <a href=\"https://wikipedia.org/wiki/Ifni_War\" title=\"Ifni War\">Battle of Edchera</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Moroccan_Army_of_Liberation\" title=\"Moroccan Army of Liberation\">Moroccan Army of Liberation</a> ambushes a Spanish patrol in the <a href=\"https://wikipedia.org/wiki/Ifni_War\" title=\"Ifni War\">Battle of Edchera</a>.", "links": [{"title": "Moroccan Army of Liberation", "link": "https://wikipedia.org/wiki/Moroccan_Army_of_Liberation"}, {"title": "Ifni War", "link": "https://wikipedia.org/wiki/Ifni_War"}]}, {"year": "1963", "text": "Coup d'état in Togo results in the assassination of president <PERSON><PERSON><PERSON><PERSON>.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/1963_Togolese_coup_d%27%C3%A9tat\" title=\"1963 Togolese coup d'état\">Coup d'état</a> in <a href=\"https://wikipedia.org/wiki/Togo\" title=\"Togo\">Togo</a> results in the assassination of president <a href=\"https://wikipedia.org/wiki/Sylvanus_Olympio\" title=\"Sylvanus Olympio\">Sylvanus Olympio</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1963_Togolese_coup_d%27%C3%A9tat\" title=\"1963 Togolese coup d'état\">Coup d'état</a> in <a href=\"https://wikipedia.org/wiki/Togo\" title=\"Togo\">Togo</a> results in the assassination of president <a href=\"https://wikipedia.org/wiki/Sylvanus_Olympio\" title=\"Sylvanus Olympio\">Sylvanus Olympio</a>.", "links": [{"title": "1963 Togolese coup d'état", "link": "https://wikipedia.org/wiki/1963_Togolese_coup_d%27%C3%A9tat"}, {"title": "Togo", "link": "https://wikipedia.org/wiki/Togo"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sylvanus_Olympio"}]}, {"year": "1964", "text": "Anti-Muslim riots break out in Calcutta, in response to anti-Hindu riots in East Pakistan. About one hundred people are killed.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Violence_against_Muslims_in_India\" class=\"mw-redirect\" title=\"Violence against Muslims in India\">Anti-Muslim</a> riots break out in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>, in response to <a href=\"https://wikipedia.org/wiki/1964_East_Pakistan_riots\" title=\"1964 East Pakistan riots\">anti-Hindu riots</a> in <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a>. About one hundred people are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Violence_against_Muslims_in_India\" class=\"mw-redirect\" title=\"Violence against Muslims in India\">Anti-Muslim</a> riots break out in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>, in response to <a href=\"https://wikipedia.org/wiki/1964_East_Pakistan_riots\" title=\"1964 East Pakistan riots\">anti-Hindu riots</a> in <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a>. About one hundred people are killed.", "links": [{"title": "Violence against Muslims in India", "link": "https://wikipedia.org/wiki/Violence_against_Muslims_in_India"}, {"title": "Kolkata", "link": "https://wikipedia.org/wiki/Kolkata"}, {"title": "1964 East Pakistan riots", "link": "https://wikipedia.org/wiki/1964_East_Pakistan_riots"}, {"title": "East Pakistan", "link": "https://wikipedia.org/wiki/East_Pakistan"}]}, {"year": "1964", "text": "In Manchester, New Hampshire, fourteen-year-old <PERSON> is murdered. <PERSON> is tried and convicted of the crime, but the conviction is set aside by the landmark Fourth Amendment case <PERSON><PERSON> v. New Hampshire (1971).", "html": "1964 - In <a href=\"https://wikipedia.org/wiki/Manchester,_New_Hampshire\" title=\"Manchester, New Hampshire\">Manchester, New Hampshire</a>, fourteen-year-old <PERSON> is murdered. <PERSON> is tried and convicted of the crime, but the conviction is set aside by the landmark <a href=\"https://wikipedia.org/wiki/Fourth_Amendment_to_the_United_States_Constitution\" title=\"Fourth Amendment to the United States Constitution\">Fourth Amendment</a> case <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v._New_Hampshire\" title=\"<PERSON><PERSON> v. New Hampshire\"><PERSON><PERSON> v. New Hampshire</a> (1971).", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Manchester,_New_Hampshire\" title=\"Manchester, New Hampshire\">Manchester, New Hampshire</a>, fourteen-year-old <PERSON> is murdered. <PERSON> is tried and convicted of the crime, but the conviction is set aside by the landmark <a href=\"https://wikipedia.org/wiki/Fourth_Amendment_to_the_United_States_Constitution\" title=\"Fourth Amendment to the United States Constitution\">Fourth Amendment</a> case <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v._New_Hampshire\" title=\"<PERSON><PERSON> v. New Hampshire\"><PERSON><PERSON> v. New Hampshire</a> (1971).", "links": [{"title": "Manchester, New Hampshire", "link": "https://wikipedia.org/wiki/Manchester,_New_Hampshire"}, {"title": "Fourth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Fourth_Amendment_to_the_United_States_Constitution"}, {"title": "<PERSON><PERSON> v. New Hampshire", "link": "https://wikipedia.org/wiki/Coolidge_v._New_Hampshire"}]}, {"year": "1966", "text": "<PERSON> becomes the first African American Cabinet member when he is appointed United States Secretary of Housing and Urban Development.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> Cabinet member when he is appointed <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development\" title=\"United States Secretary of Housing and Urban Development\">United States Secretary of Housing and Urban Development</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> Cabinet member when he is appointed <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development\" title=\"United States Secretary of Housing and Urban Development\">United States Secretary of Housing and Urban Development</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}, {"title": "United States Secretary of Housing and Urban Development", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development"}]}, {"year": "1968", "text": "<PERSON> performs live at Folsom State Prison.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Cash\" title=\"Johnny Cash\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/At_Folsom_Prison\" title=\"At Folsom Prison\">performs live</a> at <a href=\"https://wikipedia.org/wiki/Folsom_State_Prison\" title=\"Folsom State Prison\">Folsom State Prison</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Cash\" title=\"Johnny Cash\"><PERSON> Cash</a> <a href=\"https://wikipedia.org/wiki/At_Folsom_Prison\" title=\"At Folsom Prison\">performs live</a> at <a href=\"https://wikipedia.org/wiki/Folsom_State_Prison\" title=\"Folsom State Prison\">Folsom State Prison</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "At Folsom Prison", "link": "https://wikipedia.org/wiki/At_Folsom_Prison"}, {"title": "Folsom State Prison", "link": "https://wikipedia.org/wiki/Folsom_State_Prison"}]}, {"year": "1972", "text": "Prime Minister <PERSON><PERSON> and President <PERSON> of Ghana are ousted in a bloodless military coup by Colonel <PERSON><PERSON><PERSON>.", "html": "1972 - Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>fi_Abrefa_Busia\" title=\"Kofi Abrefa Busia\"><PERSON><PERSON> Abrefa Busia</a> and President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a> are ousted in a bloodless <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">military coup</a> by Colonel <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"I<PERSON><PERSON>\">I<PERSON><PERSON></a>.", "no_year_html": "Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>fi_Abrefa_Busia\" title=\"Kofi Abrefa Busia\"><PERSON><PERSON> Abrefa Busia</a> and President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a> are ousted in a bloodless <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">military coup</a> by Colonel <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">I<PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>fi_Abrefa_Busia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ghana", "link": "https://wikipedia.org/wiki/Ghana"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "Japan Air Lines Cargo Flight 1045, a Douglas DC-8 jet, crashes onto the runway during takeoff from Ted Stevens Anchorage International Airport, killing five.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Japan_Air_Lines_Cargo_Flight_1045\" title=\"Japan Air Lines Cargo Flight 1045\">Japan Air Lines Cargo Flight 1045</a>, a <a href=\"https://wikipedia.org/wiki/Douglas_DC-8\" title=\"Douglas DC-8\">Douglas DC-8</a> jet, crashes onto the runway during takeoff from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anchorage_International_Airport\" title=\"Ted <PERSON> Anchorage International Airport\"><PERSON>age International Airport</a>, killing five.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japan_Air_Lines_Cargo_Flight_1045\" title=\"Japan Air Lines Cargo Flight 1045\">Japan Air Lines Cargo Flight 1045</a>, a <a href=\"https://wikipedia.org/wiki/Douglas_DC-8\" title=\"Douglas DC-8\">Douglas DC-8</a> jet, crashes onto the runway during takeoff from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anchorage_International_Airport\" title=\"Ted <PERSON> Anchorage International Airport\">Ted <PERSON> Anchorage International Airport</a>, killing five.", "links": [{"title": "Japan Air Lines Cargo Flight 1045", "link": "https://wikipedia.org/wiki/Japan_Air_Lines_Cargo_Flight_1045"}, {"title": "Douglas DC-8", "link": "https://wikipedia.org/wiki/Douglas_DC-8"}, {"title": "<PERSON> Anchorage International Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anchorage_International_Airport"}]}, {"year": "1978", "text": "United States Food and Drug Administration requires all blood donations to be labeled \"paid\" or \"volunteer\" donors.", "html": "1978 - United States <a href=\"https://wikipedia.org/wiki/Food_and_Drug_Administration\" title=\"Food and Drug Administration\">Food and Drug Administration</a> requires all <a href=\"https://wikipedia.org/wiki/Blood_donation\" title=\"Blood donation\">blood donations</a> to be labeled \"paid\" or \"volunteer\" donors.", "no_year_html": "United States <a href=\"https://wikipedia.org/wiki/Food_and_Drug_Administration\" title=\"Food and Drug Administration\">Food and Drug Administration</a> requires all <a href=\"https://wikipedia.org/wiki/Blood_donation\" title=\"Blood donation\">blood donations</a> to be labeled \"paid\" or \"volunteer\" donors.", "links": [{"title": "Food and Drug Administration", "link": "https://wikipedia.org/wiki/Food_and_Drug_Administration"}, {"title": "Blood donation", "link": "https://wikipedia.org/wiki/Blood_donation"}]}, {"year": "1982", "text": "Shortly after takeoff, Air Florida Flight 90, a Boeing 737 jet, crashes into Washington, D.C.'s 14th Street Bridge and falls into the Potomac River, killing 78 including four motorists.", "html": "1982 - Shortly after takeoff, <a href=\"https://wikipedia.org/wiki/Air_Florida_Flight_90\" title=\"Air Florida Flight 90\">Air Florida Flight 90</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737</a> jet, crashes into <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>'s <a href=\"https://wikipedia.org/wiki/14th_Street_bridges\" title=\"14th Street bridges\">14th Street Bridge</a> and falls into the <a href=\"https://wikipedia.org/wiki/Potomac_River\" title=\"Potomac River\">Potomac River</a>, killing 78 including four motorists.", "no_year_html": "Shortly after takeoff, <a href=\"https://wikipedia.org/wiki/Air_Florida_Flight_90\" title=\"Air Florida Flight 90\">Air Florida Flight 90</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737</a> jet, crashes into <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>'s <a href=\"https://wikipedia.org/wiki/14th_Street_bridges\" title=\"14th Street bridges\">14th Street Bridge</a> and falls into the <a href=\"https://wikipedia.org/wiki/Potomac_River\" title=\"Potomac River\">Potomac River</a>, killing 78 including four motorists.", "links": [{"title": "Air Florida Flight 90", "link": "https://wikipedia.org/wiki/Air_Florida_Flight_90"}, {"title": "Boeing 737", "link": "https://wikipedia.org/wiki/Boeing_737"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}, {"title": "14th Street bridges", "link": "https://wikipedia.org/wiki/14th_Street_bridges"}, {"title": "Potomac River", "link": "https://wikipedia.org/wiki/Potomac_River"}]}, {"year": "1985", "text": "A passenger train plunges into a ravine in Ethiopia, killing 428 in the worst railroad disaster in Africa.", "html": "1985 - A passenger train <a href=\"https://wikipedia.org/wiki/Awash_rail_disaster\" title=\"Awash rail disaster\">plunges into a ravine</a> in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>, killing 428 in the worst railroad disaster in Africa.", "no_year_html": "A passenger train <a href=\"https://wikipedia.org/wiki/Awash_rail_disaster\" title=\"Awash rail disaster\">plunges into a ravine</a> in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>, killing 428 in the worst railroad disaster in Africa.", "links": [{"title": "Awash rail disaster", "link": "https://wikipedia.org/wiki/Awash_rail_disaster"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "1986", "text": "A month-long violent struggle begins in Aden, South Yemen between supporters of <PERSON> and <PERSON>, resulting in thousands of casualties.", "html": "1986 - A month-long violent struggle begins in <a href=\"https://wikipedia.org/wiki/Aden\" title=\"Aden\">Aden, South Yemen</a> between supporters of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, resulting in thousands of casualties.", "no_year_html": "A month-long violent struggle begins in <a href=\"https://wikipedia.org/wiki/Aden\" title=\"Aden\">Aden, South Yemen</a> between supporters of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, resulting in thousands of casualties.", "links": [{"title": "Aden", "link": "https://wikipedia.org/wiki/Aden"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON> becomes the first native Taiwanese President of the Republic of China.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hui\" title=\"<PERSON>hui\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Taiwanese_people\" title=\"Taiwanese people\">native Taiwanese</a> <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hui\" title=\"<PERSON>hui\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Taiwanese_people\" title=\"Taiwanese people\">native Taiwanese</a> <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hui"}, {"title": "Taiwanese people", "link": "https://wikipedia.org/wiki/Taiwanese_people"}, {"title": "President of the Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_China"}]}, {"year": "1990", "text": "<PERSON> becomes the first elected African American governor as he takes office as Governor of Virginia in Richmond, Virginia.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first elected African American governor as he takes office as <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a> in <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first elected African American governor as he takes office as <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a> in <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Virginia", "link": "https://wikipedia.org/wiki/Governor_of_Virginia"}, {"title": "Richmond, Virginia", "link": "https://wikipedia.org/wiki/Richmond,_Virginia"}]}, {"year": "1991", "text": "Soviet Union troops attack Lithuanian independence supporters in Vilnius, killing 14 people and wounding around 1,000 others.", "html": "1991 - Soviet Union troops <a href=\"https://wikipedia.org/wiki/January_Events_(Lithuania)\" class=\"mw-redirect\" title=\"January Events (Lithuania)\">attack</a> <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuanian</a> independence supporters in <a href=\"https://wikipedia.org/wiki/Vilnius\" title=\"Vilnius\">Vilnius</a>, killing 14 people and wounding around 1,000 others.", "no_year_html": "Soviet Union troops <a href=\"https://wikipedia.org/wiki/January_Events_(Lithuania)\" class=\"mw-redirect\" title=\"January Events (Lithuania)\">attack</a> <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuanian</a> independence supporters in <a href=\"https://wikipedia.org/wiki/Vilnius\" title=\"Vilnius\">Vilnius</a>, killing 14 people and wounding around 1,000 others.", "links": [{"title": "January Events (Lithuania)", "link": "https://wikipedia.org/wiki/January_Events_(Lithuania)"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "Vilnius", "link": "https://wikipedia.org/wiki/Vilnius"}]}, {"year": "1993", "text": "Space Shuttle program: <PERSON><PERSON><PERSON> heads for space for the third time as STS-54 launches from the Kennedy Space Center.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\"><i>Endeavour</i></a> heads for space for the third time as <a href=\"https://wikipedia.org/wiki/STS-54\" title=\"STS-54\">STS-54</a> launches from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\"><i>Endeavour</i></a> heads for space for the third time as <a href=\"https://wikipedia.org/wiki/STS-54\" title=\"STS-54\">STS-54</a> launches from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-54", "link": "https://wikipedia.org/wiki/STS-54"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}]}, {"year": "1993", "text": "The Chemical Weapons Convention (CWC) is signed.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Chemical_Weapons_Convention\" title=\"Chemical Weapons Convention\">Chemical Weapons Convention</a> (CWC) is signed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chemical_Weapons_Convention\" title=\"Chemical Weapons Convention\">Chemical Weapons Convention</a> (CWC) is signed.", "links": [{"title": "Chemical Weapons Convention", "link": "https://wikipedia.org/wiki/Chemical_Weapons_Convention"}]}, {"year": "1993", "text": "Operation Southern Watch: U.S.A.F., U.S.N., R.A.F. and French Air Force jets attack AAA and SAM sites in Southern Iraq.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Operation_Southern_Watch\" title=\"Operation Southern Watch\">Operation Southern Watch</a>: <a href=\"https://wikipedia.org/wiki/January_1993_airstrikes_on_Iraq\" title=\"January 1993 airstrikes on Iraq\">U.S.A.F., U.S.N., R.A.F. and French Air Force jets attack AAA and SAM sites in Southern Iraq.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Southern_Watch\" title=\"Operation Southern Watch\">Operation Southern Watch</a>: <a href=\"https://wikipedia.org/wiki/January_1993_airstrikes_on_Iraq\" title=\"January 1993 airstrikes on Iraq\">U.S.A.F., U.S.N., R.A.F. and French Air Force jets attack AAA and SAM sites in Southern Iraq.</a>", "links": [{"title": "Operation Southern Watch", "link": "https://wikipedia.org/wiki/Operation_Southern_Watch"}, {"title": "January 1993 airstrikes on Iraq", "link": "https://wikipedia.org/wiki/January_1993_airstrikes_on_Iraq"}]}, {"year": "1998", "text": "<PERSON> sets himself on fire in St. Peter's Square, protesting against homophobia.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets himself on fire in <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Square\" title=\"St. Peter's Square\">St. Peter's Square</a>, protesting against <a href=\"https://wikipedia.org/wiki/Homophobia\" title=\"Homophobia\">homophobia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets himself on fire in <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Square\" title=\"St. Peter's Square\">St. Peter's Square</a>, protesting against <a href=\"https://wikipedia.org/wiki/Homophobia\" title=\"Homophobia\">homophobia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "St. Peter's Square", "link": "https://wikipedia.org/wiki/St._Peter%27s_Square"}, {"title": "Homophobia", "link": "https://wikipedia.org/wiki/Homophobia"}]}, {"year": "2000", "text": "A Short 360 aircraft chartered by the Sirte Oil Company crashes off the coast of Brega, Libya, killing 21.", "html": "2000 - A <a href=\"https://wikipedia.org/wiki/Short_360\" title=\"Short 360\">Short 360</a> aircraft chartered by the <a href=\"https://wikipedia.org/wiki/Sirte_Oil_Company\" title=\"Sirte Oil Company\">Sirte Oil Company</a> <a href=\"https://wikipedia.org/wiki/2000_Marsa_Brega_Short_360_crash\" title=\"2000 Marsa Brega Short 360 crash\">crashes</a> off the coast of <a href=\"https://wikipedia.org/wiki/Brega\" title=\"Brega\">Brega</a>, Libya, killing 21.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Short_360\" title=\"Short 360\">Short 360</a> aircraft chartered by the <a href=\"https://wikipedia.org/wiki/Sirte_Oil_Company\" title=\"Sirte Oil Company\">Sirte Oil Company</a> <a href=\"https://wikipedia.org/wiki/2000_Marsa_Brega_Short_360_crash\" title=\"2000 Marsa Brega Short 360 crash\">crashes</a> off the coast of <a href=\"https://wikipedia.org/wiki/Brega\" title=\"Brega\">Brega</a>, Libya, killing 21.", "links": [{"title": "Short 360", "link": "https://wikipedia.org/wiki/Short_360"}, {"title": "Sirte Oil Company", "link": "https://wikipedia.org/wiki/Sirte_Oil_Company"}, {"title": "2000 Marsa Brega Short 360 crash", "link": "https://wikipedia.org/wiki/2000_Mars<PERSON>_Brega_Short_360_crash"}, {"title": "Brega", "link": "https://wikipedia.org/wiki/Brega"}]}, {"year": "2001", "text": "An earthquake hits El Salvador, killing more than 800.", "html": "2001 - An <a href=\"https://wikipedia.org/wiki/January_2001_El_Salvador_earthquake#The_January_13_earthquake\" title=\"January 2001 El Salvador earthquake\">earthquake</a> hits <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a>, killing more than 800.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/January_2001_El_Salvador_earthquake#The_January_13_earthquake\" title=\"January 2001 El Salvador earthquake\">earthquake</a> hits <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a>, killing more than 800.", "links": [{"title": "January 2001 El Salvador earthquake", "link": "https://wikipedia.org/wiki/January_2001_El_Salvador_earthquake#The_January_13_earthquake"}, {"title": "El Salvador", "link": "https://wikipedia.org/wiki/El_Salvador"}]}, {"year": "2012", "text": "The passenger cruise ship Costa Concordia sinks off the coast of Italy due to the captain <PERSON>'s negligence and irresponsibility. There are 32 confirmed deaths.", "html": "2012 - The passenger <a href=\"https://wikipedia.org/wiki/Cruise_ship\" title=\"Cruise ship\">cruise ship</a> <i><a href=\"https://wikipedia.org/wiki/Costa_Concordia\" title=\"Costa Concordia\">Costa Concordia</a></i> <a href=\"https://wikipedia.org/wiki/Costa_Concordia_disaster\" title=\"Costa Concordia disaster\">sinks</a> off the coast of Italy due to the captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s negligence and irresponsibility. There are 32 confirmed deaths.", "no_year_html": "The passenger <a href=\"https://wikipedia.org/wiki/Cruise_ship\" title=\"Cruise ship\">cruise ship</a> <i><a href=\"https://wikipedia.org/wiki/Costa_Concordia\" title=\"Costa Concordia\">Costa Concordia</a></i> <a href=\"https://wikipedia.org/wiki/Costa_Concordia_disaster\" title=\"Costa Concordia disaster\">sinks</a> off the coast of Italy due to the captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s negligence and irresponsibility. There are 32 confirmed deaths.", "links": [{"title": "Cruise ship", "link": "https://wikipedia.org/wiki/Cruise_ship"}, {"title": "Costa Concordia", "link": "https://wikipedia.org/wiki/Costa_Concordia"}, {"title": "Costa Concordia disaster", "link": "https://wikipedia.org/wiki/Costa_Concordia_disaster"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "A false emergency alert warning of an impending missile strike in Hawaii causes widespread panic in the state.", "html": "2018 - A <a href=\"https://wikipedia.org/wiki/2018_Hawaii_false_missile_alert\" title=\"2018 Hawaii false missile alert\">false emergency alert</a> warning of an impending missile strike in <a href=\"https://wikipedia.org/wiki/Hawaii\" title=\"Hawaii\">Hawaii</a> causes widespread panic in the state.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2018_Hawaii_false_missile_alert\" title=\"2018 Hawaii false missile alert\">false emergency alert</a> warning of an impending missile strike in <a href=\"https://wikipedia.org/wiki/Hawaii\" title=\"Hawaii\">Hawaii</a> causes widespread panic in the state.", "links": [{"title": "2018 Hawaii false missile alert", "link": "https://wikipedia.org/wiki/2018_Hawaii_false_missile_alert"}, {"title": "Hawaii", "link": "https://wikipedia.org/wiki/Hawaii"}]}, {"year": "2020", "text": "The Thai Ministry of Public Health confirms the first case of COVID-19 outside China.", "html": "2020 - The Thai <a href=\"https://wikipedia.org/wiki/Ministry_of_Public_Health_(Thailand)\" title=\"Ministry of Public Health (Thailand)\">Ministry of Public Health</a> confirms the first case of <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a> outside China.", "no_year_html": "The Thai <a href=\"https://wikipedia.org/wiki/Ministry_of_Public_Health_(Thailand)\" title=\"Ministry of Public Health (Thailand)\">Ministry of Public Health</a> confirms the first case of <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a> outside China.", "links": [{"title": "Ministry of Public Health (Thailand)", "link": "https://wikipedia.org/wiki/Ministry_of_Public_Health_(Thailand)"}, {"title": "COVID-19", "link": "https://wikipedia.org/wiki/COVID-19"}]}, {"year": "2021", "text": "Outgoing U.S. President <PERSON> is impeached for a second time on a charge of incitement of insurrection following the January 6 United States Capitol attack one week prior.", "html": "2021 - Outgoing <a href=\"https://wikipedia.org/wiki/U.S._President\" class=\"mw-redirect\" title=\"U.S. President\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Second_impeachment_of_<PERSON>_<PERSON>\" title=\"Second impeachment of <PERSON>\">impeached for a second time</a> on a charge of incitement of insurrection following the <a href=\"https://wikipedia.org/wiki/January_6_United_States_Capitol_attack\" title=\"January 6 United States Capitol attack\">January 6 United States Capitol attack</a> one week prior.", "no_year_html": "Outgoing <a href=\"https://wikipedia.org/wiki/U.S._President\" class=\"mw-redirect\" title=\"U.S. President\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Second_impeachment_of_<PERSON>_<PERSON>\" title=\"Second impeachment of <PERSON>\">impeached for a second time</a> on a charge of incitement of insurrection following the <a href=\"https://wikipedia.org/wiki/January_6_United_States_Capitol_attack\" title=\"January 6 United States Capitol attack\">January 6 United States Capitol attack</a> one week prior.", "links": [{"title": "U.S. President", "link": "https://wikipedia.org/wiki/U.S._President"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Second impeachment of <PERSON>", "link": "https://wikipedia.org/wiki/Second_impeachment_of_<PERSON>_<PERSON>"}, {"title": "January 6 United States Capitol attack", "link": "https://wikipedia.org/wiki/January_6_United_States_Capitol_attack"}]}], "Births": [{"year": "5 BC", "text": "<PERSON><PERSON><PERSON> of Han, Chinese emperor (d. 57)", "html": "5 BC - 5 BC - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON><PERSON> of Han\"><PERSON><PERSON><PERSON> of Han</a>, Chinese emperor (d. 57)", "no_year_html": "5 BC - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON><PERSON> of Han\"><PERSON><PERSON><PERSON> of Han</a>, Chinese emperor (d. 57)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON> of Han", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Han"}]}, {"year": "101", "text": "<PERSON><PERSON>, Roman adopted son of <PERSON><PERSON> (d. 138)", "html": "101 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>us\" class=\"mw-redirect\" title=\"Lucius Aelius\">Lucius <PERSON></a>, Roman adopted son of <a href=\"https://wikipedia.org/wiki/Hadrian\" title=\"Had<PERSON>\"><PERSON><PERSON></a> (d. 138)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>elius\" class=\"mw-redirect\" title=\"Lucius Aelius\"><PERSON></a>, Roman adopted son of <a href=\"https://wikipedia.org/wiki/Hadrian\" title=\"Had<PERSON>\"><PERSON><PERSON></a> (d. 138)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "915", "text": "Al-<PERSON><PERSON><PERSON> II, Umayyad caliph (d. 976)", "html": "915 - <a href=\"https://wikipedia.org/wiki/Al-Hakam_II\" title=\"Al-Hakam II\">Al-Hakam II</a>, Umayya<PERSON> caliph (d. 976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Hakam_II\" title=\"Al-Hakam II\">Al-Hakam II</a>, Umayya<PERSON> caliph (d. 976)", "links": [{"title": "Al-Hakam II", "link": "https://wikipedia.org/wiki/Al-<PERSON>kam_II"}]}, {"year": "1334", "text": "<PERSON>, king of Castile and León (d. 1379)", "html": "1334 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Castile\" title=\"<PERSON> II of Castile\"><PERSON> II</a>, king of Castile and León (d. 1379)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> II of Castile\"><PERSON> II</a>, king of Castile and León (d. 1379)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Castile"}]}, {"year": "1338", "text": "<PERSON><PERSON><PERSON>, Korean civil minister, diplomat and scholar (d. 1392)", "html": "1338 - <a href=\"https://wikipedia.org/wiki/Ch%C5%8Fng_Mong-ju\" title=\"Chŏng Mong-ju\"><PERSON><PERSON><PERSON>-ju</a>, Korean civil minister, diplomat and scholar (d. 1392)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ch%C5%8Fng_Mong-ju\" title=\"Chŏng Mong-ju\"><PERSON><PERSON><PERSON>-ju</a>, Korean civil minister, diplomat and scholar (d. 1392)", "links": [{"title": "<PERSON><PERSON><PERSON>u", "link": "https://wikipedia.org/wiki/Ch%C5%8Fng_Mong-ju"}]}, {"year": "1381", "text": "<PERSON><PERSON> of Corbie, French abbess and saint in the Catholic Church (d. 1447)", "html": "1381 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Corbie\" title=\"<PERSON><PERSON> of Corbie\"><PERSON><PERSON> of Corbie</a>, French abbess and saint in the Catholic Church (d. 1447)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Corbie\" title=\"<PERSON><PERSON> of Corbie\"><PERSON><PERSON> of Corbie</a>, French abbess and saint in the Catholic Church (d. 1447)", "links": [{"title": "<PERSON><PERSON> of Corbie", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Corbie"}]}, {"year": "1400", "text": "<PERSON><PERSON><PERSON>, Constable of Portugal (d. 1442)", "html": "1400 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Lord_of_Reguengos_de_Monsaraz\" class=\"mw-redirect\" title=\"<PERSON>, Lord of Reguengos de Monsaraz\">In<PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Constable_of_Portugal\" title=\"Constable of Portugal\">Constable of Portugal</a> (d. 1442)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Lord_of_Reguengos_de_Monsaraz\" class=\"mw-redirect\" title=\"<PERSON>, Lord of Reguengos de Monsaraz\">In<PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Constable_of_Portugal\" title=\"Constable of Portugal\">Constable of Portugal</a> (d. 1442)", "links": [{"title": "<PERSON>, Lord of Reguengos de Monsaraz", "link": "https://wikipedia.org/wiki/<PERSON>,_Lord_of_<PERSON>uen<PERSON>_<PERSON>_<PERSON>"}, {"title": "Constable of Portugal", "link": "https://wikipedia.org/wiki/Constable_of_Portugal"}]}, {"year": "1477", "text": "<PERSON>, 5th Earl of Northumberland (d. 1527)", "html": "1477 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Northumberland\" title=\"<PERSON>, 5th Earl of Northumberland\"><PERSON>, 5th Earl of Northumberland</a> (d. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Northumberland\" title=\"<PERSON>, 5th Earl of Northumberland\"><PERSON>, 5th Earl of Northumberland</a> (d. 1527)", "links": [{"title": "<PERSON>, 5th Earl of Northumberland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Northumberland"}]}, {"year": "1505", "text": "<PERSON>, Elector of Brandenburg (d. 1571)", "html": "1505 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Brandenburg\" class=\"mw-redirect\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (d. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Brandenburg\" class=\"mw-redirect\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (d. 1571)", "links": [{"title": "<PERSON>, Elector of Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Brandenburg"}]}, {"year": "1562", "text": "<PERSON>, Scottish poet and soldier (d. 1601)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and soldier (d. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and soldier (d. 1601)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1596", "text": "<PERSON>, Dutch painter and illustrator (d. 1656)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and illustrator (d. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and illustrator (d. 1656)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1610", "text": "<PERSON> of Bavaria, archduchess of Austria (d. 1665)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria_(1610%E2%80%931665)\" class=\"mw-redirect\" title=\"Archduchess <PERSON> of Austria (1610-1665)\"><PERSON> of Bavaria</a>, archduchess of Austria (d. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria_(1610%E2%80%931665)\" class=\"mw-redirect\" title=\"Archduchess <PERSON> of Austria (1610-1665)\"><PERSON> of Bavaria</a>, archduchess of Austria (d. 1665)", "links": [{"title": "Archduchess <PERSON> of Austria (1610-1665)", "link": "https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria_(1610%E2%80%931665)"}]}, {"year": "1616", "text": "<PERSON><PERSON>, French-Flemish mystic and author (d. 1680)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Flemish mystic and author (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Flemish mystic and author (d. 1680)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1651", "text": "<PERSON>, 1st Earl of Warrington, English soldier and politician, Chancellor of the Exchequer (d. 1694)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Warrington\" title=\"<PERSON>, 1st Earl of Warrington\"><PERSON>, 1st Earl of Warrington</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Warrington\" title=\"<PERSON>, 1st Earl of Warrington\"><PERSON>, 1st Earl of Warrington</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1694)", "links": [{"title": "<PERSON>, 1st Earl of Warrington", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Warrington"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1672", "text": "<PERSON>, Italian teacher and saint (d. 1732)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian teacher and saint (d. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian teacher and saint (d. 1732)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON>, German harpsichord player and composer (d. 1760)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German harpsichord player and composer (d. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German harpsichord player and composer (d. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1720", "text": "<PERSON>, English bishop (d. 1808)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (d. 1808)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1749", "text": "<PERSON><PERSON>, German poet, painter, and playwright (d. 1825)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/Maler_M%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet, painter, and playwright (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>r_<PERSON>%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet, painter, and playwright (d. 1825)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maler_M%C3%BCller"}]}, {"year": "1787", "text": "<PERSON>, American lawyer and politician, 14th Governor of Massachusetts (d. 1854)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Massachusetts_governor)\" title=\"<PERSON> (Massachusetts governor)\"><PERSON></a>, American lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Massachusetts_governor)\" title=\"<PERSON> (Massachusetts governor)\"><PERSON></a>, American lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1854)", "links": [{"title": "<PERSON> (Massachusetts governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Massachusetts_governor)"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1804", "text": "<PERSON>, French illustrator (d. 1866)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French illustrator (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French illustrator (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, American lawyer and politician, 18th Mayor of Chicago (d. 1862)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1808", "text": "<PERSON>, American jurist and politician, 6th Chief Justice of the United States (d. 1873)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Salmon P<PERSON> Chase\"><PERSON></a>, American jurist and politician, 6th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Salmon P<PERSON> Chase\"><PERSON></a>, American jurist and politician, 6th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Chase"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1810", "text": "<PERSON><PERSON>, American suffragist, abolitionist, and freethinker (d. 1892)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American suffragist, abolitionist, and freethinker (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American suffragist, abolitionist, and freethinker (d. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, French poet and critic (d. 1883)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON><PERSON><PERSON>, Jr., American novelist and journalist (d. 1899)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON>, Jr.</a>, American novelist and journalist (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON>, Jr.</a>, American novelist and journalist (d. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1845", "text": "<PERSON>, French astronomer and academic (d. 1896)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_T<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_T<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_<PERSON><PERSON><PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON>, Lithuanian-German biologist and academic (d. 1931)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian-German biologist and academic (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian-German biologist and academic (d. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON>, Greek poet and playwright (d. 1943)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and playwright (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and playwright (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, German neurologist and academic (d. 1959)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German neurologist and academic (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German neurologist and academic (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (d. 1928)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Wilhelm_Wien\" title=\"Wilhelm Wien\"><PERSON> Wien</a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilhelm_Wien\" title=\"Wilhelm Wien\"><PERSON> Wien</a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1928)", "links": [{"title": "Wilhelm Wien", "link": "https://wikipedia.org/wiki/Wilhelm_Wien"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1865", "text": "Princess <PERSON> of Orléans (d. 1908)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_Orl%C3%A9ans_(1865%E2%80%931909)\" title=\"Princess <PERSON> Orléans (1865-1909)\">Princess <PERSON> of Orléans</a> (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_Orl%C3%A9ans_(1865%E2%80%931909)\" title=\"Princess <PERSON> of Orléans (1865-1909)\">Princess <PERSON> of Orléans</a> (d. 1908)", "links": [{"title": "Princess <PERSON> of Orléans (1865-1909)", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_Orl%C3%A9ans_(1865%E2%80%931909)"}]}, {"year": "1866", "text": "<PERSON><PERSON>, Russian bassoon player and composer (d. 1901)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian <a href=\"https://wikipedia.org/wiki/Bassoon\" title=\"Bassoon\">bassoon</a> player and composer (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian <a href=\"https://wikipedia.org/wiki/Bassoon\" title=\"Bassoon\">bassoon</a> player and composer (d. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Bassoon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1869", "text": "<PERSON> <PERSON><PERSON>, Duke of Aosta (d. 1931)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>_<PERSON>,_Duke_of_Aosta_(1869%E2%80%931931)\" class=\"mw-redirect\" title=\"Prince <PERSON><PERSON>, Duke of Aosta (1869-1931)\">Prince <PERSON><PERSON>, Duke of Aosta</a> (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>_<PERSON>,_Duke_of_Aosta_(1869%E2%80%931931)\" class=\"mw-redirect\" title=\"Prince <PERSON><PERSON>, Duke of Aosta (1869-1931)\">Prince <PERSON><PERSON>, Duke of Aosta</a> (d. 1931)", "links": [{"title": "Prince <PERSON><PERSON>, Duke of Aosta (1869-1931)", "link": "https://wikipedia.org/wiki/Prince_<PERSON><PERSON>_<PERSON>,_Duke_of_Aosta_(1869%E2%80%931931)"}]}, {"year": "1870", "text": "<PERSON>, American biologist and anatomist (d. 1959)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ross <PERSON>\"><PERSON></a>, American biologist and anatomist (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ross <PERSON> Harrison\"><PERSON></a>, American biologist and anatomist (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ross_Granville_Harrison"}]}, {"year": "1878", "text": "<PERSON>, Canadian priest and historian (d. 1967)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian priest and historian (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian priest and historian (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lionel_<PERSON>lx"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Australian engineer and businessman (d. 1961)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian engineer and businessman (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian engineer and businessman (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American runner and coach (d. 1967)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Canadian-American businessman, founded the Fuller Brush Company (d. 1973)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman, founded the <a href=\"https://wikipedia.org/wiki/Fuller_Brush_Company\" title=\"Fuller Brush Company\">Fuller Brush Company</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman, founded the <a href=\"https://wikipedia.org/wiki/Fuller_Brush_Company\" title=\"Fuller Brush Company\">Fuller Brush Company</a> (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fuller Brush Company", "link": "https://wikipedia.org/wiki/Fuller_Brush_Company"}]}, {"year": "1886", "text": "<PERSON>, Canadian-American ice hockey player and coach (d. 1964)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Ross"}]}, {"year": "1886", "text": "<PERSON>, Russian-born American singer and actress (d. 1966)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born American singer and actress (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born American singer and actress (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Estonian journalist, lawyer, and politician, 7th Prime Minister of Estonia (d. 1945)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian journalist, lawyer, and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian journalist, lawyer, and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a> (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCri_Uluots"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, Italian-Swiss footballer (d. 1976)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Ermanno_Aebi\" title=\"<PERSON>rmanno Aebi\"><PERSON><PERSON><PERSON></a>, Italian-Swiss footballer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erman<PERSON>_A<PERSON>i\" title=\"<PERSON>rmanno Aebi\"><PERSON><PERSON><PERSON></a>, Italian-Swiss footballer (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erman<PERSON>_<PERSON>ebi"}]}, {"year": "1893", "text": "<PERSON>, English lieutenant and pilot (d. 1974)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and pilot (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and pilot (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Australian footballer and coach (d. 1963)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American poet, sculptor, painter, and author (d. 1961)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, sculptor, painter, and author (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, sculptor, painter, and author (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Belarusian-French painter (d. 1943)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Cha%C3%AFm_Soutine\" title=\"Cha<PERSON><PERSON> Souti<PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian-French painter (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cha%C3%AFm_Soutine\" title=\"<PERSON><PERSON><PERSON> Soutine\"><PERSON><PERSON><PERSON></a>, Belarusian-French painter (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cha%C3%AFm_Soutine"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler (d. 1967)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Motokichi"}]}, {"year": "1900", "text": "<PERSON>, American mathematician (d. 1978)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON> <PERSON><PERSON>, Jr., American novelist, screenwriter, historian (d. 1991)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>, Jr.\"><PERSON><PERSON> <PERSON><PERSON>, Jr.</a>, American novelist, screenwriter, historian (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>, Jr.\"><PERSON><PERSON> <PERSON><PERSON>, Jr.</a>, American novelist, screenwriter, historian (d. 1991)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish priest and historian (d. 1978)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Mieczys%C5%82aw_%C5%BBywczy%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish priest and historian (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mieczys%C5%82aw_%C5%BBywczy%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish priest and historian (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mieczys%C5%82aw_%C5%BBywczy%C5%84ski"}]}, {"year": "1902", "text": "<PERSON>, Austrian-American mathematician from the Vienna Circle (d. 1985)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American mathematician from the Vienna Circle (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American mathematician from the Vienna Circle (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English composer (d. 1977)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Ukrainian-American violinist and composer (d. 1992)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American violinist and composer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American violinist and composer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Irish footballer (d. 1984)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American actress (d. 1968)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, English sprinter and pianist (d. 1966)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English sprinter and pianist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English sprinter and pianist (d. 1966)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1906", "text": "<PERSON>, Chinese linguist, sinologist, and academic (d. 2017)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese linguist, sinologist, and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese linguist, sinologist, and academic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, German race car driver (d. 1993)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/He<PERSON>_Gl%C3%B6ckler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON>_Gl%C3%B6ckler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Helm_Gl%C3%B6ckler"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Greek painter and illustrator (d. 1989)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and illustrator (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and illustrator (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, New Zealand-Australian farmer and politician, 31st Premier of Queensland (d. 2005)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-Australian farmer and politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-Australian farmer and politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Danish-American actress (d. 2006)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-American actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-American actress (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Osa_Massen"}]}, {"year": "1914", "text": "<PERSON>, <PERSON>, English author, playwright, and screenwriter (d. 1992)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English author, playwright, and screenwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English author, playwright, and screenwriter (d. 1992)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American actor (d. 2003)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Greek-Turkish author and poet (d. 2001)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Necati_Cumal%C4%B1\" title=\"<PERSON><PERSON>ti Cumalı\"><PERSON><PERSON><PERSON></a>, Greek-Turkish author and poet (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Necati_Cumal%C4%B1\" title=\"<PERSON><PERSON><PERSON> Cumalı\"><PERSON><PERSON><PERSON></a>, Greek-Turkish author and poet (d. 2001)", "links": [{"title": "Necati Cumalı", "link": "https://wikipedia.org/wiki/Necati_Cumal%C4%B1"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, American-English author and poet (d. 2000)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American-English author and poet (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Rain<PERSON>\"><PERSON><PERSON><PERSON></a>, American-English author and poet (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_<PERSON>er"}]}, {"year": "1921", "text": "<PERSON>, English footballer (d. 2007)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer (d. 2007)", "links": [{"title": "<PERSON> (English footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)"}]}, {"year": "1922", "text": "<PERSON>, French director and producer (d. 1970)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and producer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and producer (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Russian cellist (d. 1997)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian cellist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian cellist (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Dutch runner (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch runner (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch runner (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English actor (d. 2001)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ling"}]}, {"year": "1924", "text": "<PERSON>, Austrian-Swiss philosopher and academic (d. 1994)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss philosopher and academic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss philosopher and academic (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, French dancer and choreographer (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French dancer and choreographer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Petit\"><PERSON></a>, French dancer and choreographer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actress (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American singer and actress (d. 1994)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Australian engineer and businessman (d. 2020)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian engineer and businessman (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian engineer and businessman (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actress and dancer (d. 2000)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English author, created <PERSON><PERSON> (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, created <a href=\"https://wikipedia.org/wiki/Paddington_Bear\" title=\"Paddington Bear\">Paddington Bear</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, created <a href=\"https://wikipedia.org/wiki/Paddington_Bear\" title=\"Paddington Bear\">Paddington Bear</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pa<PERSON> Bear", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American author and academic (d. 2003)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>run\"><PERSON></a>, American author and academic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American trombonist and composer (d. 1999)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Liston\"><PERSON><PERSON></a>, American trombonist and composer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Liston\"><PERSON><PERSON></a>, American trombonist and composer (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Liston"}]}, {"year": "1927", "text": "<PERSON>, American lawyer and politician, 5th United States Secretary of Transportation (d. 2004)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Transportation\" title=\"United States Secretary of Transportation\">United States Secretary of Transportation</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Transportation\" title=\"United States Secretary of Transportation\">United States Secretary of Transportation</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Transportation", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Transportation"}]}, {"year": "1927", "text": "<PERSON>, American singer-songwriter (d. 2011)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, South African biologist and academic, Nobel Prize laureate (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Sydney_Brenner\" title=\"Sydney Brenner\"><PERSON> Brenner</a>, South African biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Brenner\" title=\"Sydney Brenner\"><PERSON> Brenner</a>, South African biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2019)", "links": [{"title": "Sydney Brenner", "link": "https://wikipedia.org/wiki/Sydney_Brenner"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1929", "text": "<PERSON>, American guitarist and composer (d. 1994)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Pass\" title=\"Joe Pass\"><PERSON></a>, American guitarist and composer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joe_Pass\" title=\"Joe Pass\"><PERSON></a>, American guitarist and composer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American actress (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English actor (d. 1984)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor, comedian, director, game show panelist, and television personality (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, director, game show panelist, and television personality (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, director, game show panelist, and television personality (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American actor and comedian (d. 2019)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor and comedian (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor and comedian (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rip_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English-born Canadian actor (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Canadian actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Canadian actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American mountaineer, photographer, and scholar (d. 1994)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a>, American mountaineer, photographer, and scholar (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a>, American mountaineer, photographer, and scholar (d. 1994)", "links": [{"title": "<PERSON> (mountaineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)"}]}, {"year": "1933", "text": "<PERSON>, American basketball player, coach, and politician (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Italian opera singer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Ren<PERSON>_B<PERSON>on\" title=\"Renato Bruson\"><PERSON><PERSON></a>, Italian opera singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren<PERSON>_B<PERSON>on\" title=\"Renato Bruson\"><PERSON><PERSON></a>, Italian opera singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>on"}]}, {"year": "1937", "text": "<PERSON>, New Zealand-English biochemist and academic (d. 2012)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English biochemist and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English biochemist and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Australian singer-songwriter and guitarist (d. 2015)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and guitarist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and guitarist (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Egyptian-French singer-songwriter (d. 2015)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Egyptian-French singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Egyptian-French singer-songwriter (d. 2015)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1938", "text": "<PERSON>, American actor, voice artist, and comedian", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, voice artist, and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American actor, voice artist, and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, French cartoonist (d. 2015)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>ab<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cartoonist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ab<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cartoonist (d. 2015)", "links": [{"title": "Cab<PERSON>", "link": "https://wikipedia.org/wiki/Cabu"}]}, {"year": "1938", "text": "<PERSON>, American captain and politician (d. 2013)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Wyoming_politician)\" title=\"<PERSON> (Wyoming politician)\"><PERSON></a>, American captain and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Wyoming_politician)\" title=\"<PERSON> (Wyoming politician)\"><PERSON></a>, American captain and politician (d. 2013)", "links": [{"title": "<PERSON> (Wyoming politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Wyoming_politician)"}]}, {"year": "1938", "text": "<PERSON>, American actor, competitive motorcycle racer and inventor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, competitive motorcycle racer and inventor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, competitive motorcycle racer and inventor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Swedish footballer and manager", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Tord_Grip\" title=\"Tord Grip\"><PERSON><PERSON></a>, Swedish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tord_Grip\" title=\"Tord Grip\"><PERSON><PERSON></a>, Swedish footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tord_Grip"}]}, {"year": "1938", "text": "<PERSON>, English children's television executive and producer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Anna_Home\" title=\"Anna Home\"><PERSON></a>, English children's television executive and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anna_Home\" title=\"Anna Home\"><PERSON></a>, English children's television executive and producer", "links": [{"title": "Anna Home", "link": "https://wikipedia.org/wiki/Anna_Home"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Argentinian author, screenwriter, and director (d. 2024)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian author, screenwriter, and director (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian author, screenwriter, and director (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Polish footballer and coach", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>sar<PERSON>_<PERSON>go\" title=\"Cesar<PERSON> Man<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Man<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sar<PERSON>_<PERSON>go"}]}, {"year": "1940", "text": "<PERSON>, American novelist, memoirist, and essayist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, memoirist, and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, memoirist, and essayist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish academic and politician, 127th President of the Generalitat de Catalunya", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Pasqual_Maragall\" title=\"Pasqual Maragall\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish academic and politician, 127th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Generalitat_de_Catalunya\" class=\"mw-redirect\" title=\"List of Presidents of the Generalitat de Catalunya\">President of the Generalitat de Catalunya</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pasqual_Maragall\" title=\"Pasqual Maragall\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish academic and politician, 127th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Generalitat_de_Catalunya\" class=\"mw-redirect\" title=\"List of Presidents of the Generalitat de Catalunya\">President of the Generalitat de Catalunya</a>", "links": [{"title": "Pasqua<PERSON>", "link": "https://wikipedia.org/wiki/Pasqual_Maragall"}, {"title": "List of Presidents of the Generalitat de Catalunya", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Generalitat_de_Catalunya"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, German bobsledder", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German bobsledder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German bobsledder", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American composer and author (d. 2012)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and author (d. 2012)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1943", "text": "<PERSON>, American actor (d. 2023)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English oncologist and author (d. 2021)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English oncologist and author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English oncologist and author (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English footballer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1945)\" title=\"<PERSON> (footballer, born 1945)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1945)\" title=\"<PERSON> (footballer, born 1945)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1945)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1945)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Turkish physicist and academic (d. 2004)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Ordal_De<PERSON>kan\" title=\"Ordal Demokan\"><PERSON><PERSON></a>, Turkish physicist and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ordal_De<PERSON>kan\" title=\"Ordal Demokan\"><PERSON><PERSON></a>, Turkish physicist and academic (d. 2004)", "links": [{"title": "Ordal Demokan", "link": "https://wikipedia.org/wiki/Ordal_Demokan"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Finnish saxophonist, composer, and conductor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish saxophonist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish saxophonist, composer, and conductor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Polish historian, lawyer, and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish historian, lawyer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish historian, lawyer, and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Spanish footballer and coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Indian lawyer and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Indian commander, pilot, and cosmonaut", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian commander, pilot, and cosmonaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian commander, pilot, and cosmonaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American screenwriter and producer (d. 1997)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English economist and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American baseball player (d. 2011)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Iranian footballer and manager (d. 2014)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian footballer and manager (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English journalist, co-founded The Independent", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>(columnist)\" title=\"<PERSON> (columnist)\"><PERSON></a>, English journalist, co-founded <i><a href=\"https://wikipedia.org/wiki/The_Independent\" title=\"The Independent\">The Independent</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(columnist)\" title=\"<PERSON> (columnist)\"><PERSON></a>, English journalist, co-founded <i><a href=\"https://wikipedia.org/wiki/The_Independent\" title=\"The Independent\">The Independent</a></i>", "links": [{"title": "<PERSON> (columnist)", "link": "https://wikipedia.org/wiki/<PERSON>_(columnist)"}, {"title": "The Independent", "link": "https://wikipedia.org/wiki/The_Independent"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American actress and producer (d. 2012)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English composer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, South African-American singer-songwriter, guitarist, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian singer-songwriter, guitarist, and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_musician)\" title=\"<PERSON> (Australian musician)\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_musician)\" title=\"<PERSON> (Australian musician)\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (Australian musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_musician)"}]}, {"year": "1955", "text": "<PERSON>, American novelist and critic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English diplomat, British Ambassador to Russia", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Russia\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Russia\">British Ambassador to Russia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Russia\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Russia\">British Ambassador to Russia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Ambassadors of the United Kingdom to Russia", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Russia"}]}, {"year": "1957", "text": "<PERSON>, American poet and academic (d. 2014)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English lawyer and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mary_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American golfer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Meara\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Meara\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mark_O%27Meara"}]}, {"year": "1958", "text": "<PERSON>, Spanish footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Francisco Buyo\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Buy<PERSON>\" title=\"Francisco Buyo\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Spanish handball player (d. 2016)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish handball player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish handball player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Ugandan engineer, politician, and diplomat", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ugandan engineer, politician, and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ugandan engineer, politician, and diplomat", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1960", "text": "<PERSON>, American physicist and chemist, Nobel Prize laureate", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1960", "text": "<PERSON>, English choreographer and director", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English choreographer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English choreographer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and musician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American actress, comedian, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, English singer-songwriter, musician, and actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>ggs\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter, musician, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ggs\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter, musician, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter and actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian ice hockey player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1962", "text": "<PERSON>, American baseball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_(baseball)"}]}, {"year": "1964", "text": "<PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English musician and comedian", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor and race car driver", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Dutch speed skater and pilot", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch speed skater and pilot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch speed skater and pilot", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>isser"}]}, {"year": "1967", "text": "<PERSON>, American actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American actress, model, and television personality", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, model, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, model, and television personality", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English footballer and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Italian skier", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stefania_<PERSON>mondo"}]}, {"year": "1969", "text": "<PERSON>, Scottish snooker player and journalist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish snooker player and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish snooker player and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Dutch footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Italian cyclist (d. 2004)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American actress, director, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>him<PERSON>\" title=\"<PERSON><PERSON><PERSON> Rhim<PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Rhim<PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian footballer and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Belarusian gymnast", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Italian race driver", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1973", "text": "<PERSON>, Russian ice hockey player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Russian ice hockey player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Norwegian guitarist and composer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian guitarist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Estonian academic and politician, 31st Estonian Minister of Education and Research", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Mailis_Reps\" title=\"Mailis Reps\"><PERSON><PERSON></a>, Estonian academic and politician, 31st <a href=\"https://wikipedia.org/wiki/Minister_of_Education_and_Research_(Estonia)\" title=\"Minister of Education and Research (Estonia)\">Estonian Minister of Education and Research</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Reps\" title=\"Mail<PERSON> Reps\"><PERSON><PERSON></a>, Estonian academic and politician, 31st <a href=\"https://wikipedia.org/wiki/Minister_of_Education_and_Research_(Estonia)\" title=\"Minister of Education and Research (Estonia)\">Estonian Minister of Education and Research</a>", "links": [{"title": "Mailis Reps", "link": "https://wikipedia.org/wiki/Mailis_Reps"}, {"title": "Minister of Education and Research (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Education_and_Research_(Estonia)"}]}, {"year": "1975", "text": "<PERSON>, American entrepreneur, founder of Venture for America, and 2020 Democratic presidential candidate", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur, founder of <a href=\"https://wikipedia.org/wiki/Venture_for_America\" title=\"Venture for America\">Venture for America</a>, and <a href=\"https://wikipedia.org/wiki/2020_Democratic_Party_presidential_primaries\" title=\"2020 Democratic Party presidential primaries\">2020 Democratic presidential candidate</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur, founder of <a href=\"https://wikipedia.org/wiki/Venture_for_America\" title=\"Venture for America\">Venture for America</a>, and <a href=\"https://wikipedia.org/wiki/2020_Democratic_Party_presidential_primaries\" title=\"2020 Democratic Party presidential primaries\">2020 Democratic presidential candidate</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Venture for America", "link": "https://wikipedia.org/wiki/Venture_for_America"}, {"title": "2020 Democratic Party presidential primaries", "link": "https://wikipedia.org/wiki/2020_Democratic_Party_presidential_primaries"}]}, {"year": "1976", "text": "<PERSON>, Scottish actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e%C3%B1a"}]}, {"year": "1976", "text": "<PERSON>, Colombian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Orlando_Bloom\" title=\"Orlando Bloom\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Bloom\" title=\"Orlando Bloom\"><PERSON></a>, English actor", "links": [{"title": "Orlando Bloom", "link": "https://wikipedia.org/wiki/Orlando_Bloom"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean golfer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean golfer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English trombonist and keyboard player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trombonist and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trombonist and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American basketball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Indian soldier (d. 2009)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soldier)\" title=\"<PERSON><PERSON> (soldier)\"><PERSON><PERSON></a>, Indian soldier (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soldier)\" title=\"<PERSON><PERSON> (soldier)\"><PERSON><PERSON></a>, Indian soldier (d. 2009)", "links": [{"title": "<PERSON><PERSON> (soldier)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soldier)"}]}, {"year": "1978", "text": "<PERSON>, American journalist and statistician, developed PECOTA", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Silver\"><PERSON></a>, American journalist and statistician, developed <a href=\"https://wikipedia.org/wiki/PECOTA\" title=\"PECOTA\">PECOTA</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Silver\"><PERSON></a>, American journalist and statistician, developed <a href=\"https://wikipedia.org/wiki/PECOTA\" title=\"PECOTA\">PECOTA</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "PECOTA", "link": "https://wikipedia.org/wiki/PECOTA"}]}, {"year": "1979", "text": "<PERSON>, English actress and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Katy_Brand\" title=\"Katy Brand\"><PERSON></a>, English actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Katy_Brand\" title=\"Katy Brand\"><PERSON></a>, English actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Katy_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish organist and conductor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON><PERSON>tof_Czerwi%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish organist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON><PERSON><PERSON><PERSON>_Czerwi%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish organist and conductor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>z<PERSON>f_Czerwi%C5%84ski"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Japanese footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Austrian ski jumper", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ski jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, German footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American wrestler and actor (d. 2020)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and actor (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>pard"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Argentinian tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guillermo_<PERSON>ria"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Cypriot footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Constantinos_Makrides\" title=\"Con<PERSON><PERSON>s Makrides\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON><PERSON>_Ma<PERSON>rides\" title=\"Con<PERSON><PERSON><PERSON> Mak<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantinos_Ma<PERSON>rides"}]}, {"year": "1982", "text": "<PERSON>, English actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Turkish basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>i%C3%9Fl\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>i%C3%9Fl\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sebastian_Knei%C3%9Fl"}]}, {"year": "1983", "text": "<PERSON>, English actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Argentine_footballer)\" title=\"<PERSON><PERSON><PERSON> (Argentine footballer)\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Argentine_footballer)\" title=\"<PERSON><PERSON><PERSON> (Argentine footballer)\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (Argentine footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Argentine_footballer)"}]}, {"year": "1983", "text": "<PERSON><PERSON>, French basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1984)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1984)\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1984)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1984)\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON> (footballer born 1984)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1984)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, German sprinter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Kam<PERSON><PERSON>_<PERSON>\" title=\"Kamgh<PERSON> Gaba\"><PERSON><PERSON><PERSON><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kam<PERSON><PERSON>_<PERSON>\" title=\"Kamgh<PERSON> Gaba\"><PERSON><PERSON><PERSON><PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>aba"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Canadian figure skater", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Italian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Romanian gymnast", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>lor<PERSON>_<PERSON>\" title=\"Florica <PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lor<PERSON>_<PERSON>\" title=\"Florica Leon<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian gymnast", "links": [{"title": "Flor<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lorica_<PERSON>ida"}]}, {"year": "1987", "text": "<PERSON>, Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Italian cyclist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (running back)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)"}]}, {"year": "1989", "text": "<PERSON>, Canadian-American actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Italian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English-Irish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Welsh footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, German tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English artistic gymnast", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>lock\"><PERSON></a>, English artistic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>lock\"><PERSON></a>, English artistic gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Vasili<PERSON>_<PERSON>ci%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>li<PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vasilije_Mici%C4%87"}]}, {"year": "1995", "text": "<PERSON>, American actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Russian ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1995)\" title=\"<PERSON> (ice hockey, born 1995)\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1995)\" title=\"<PERSON> (ice hockey, born 1995)\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1995)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1995)"}]}, {"year": "1995", "text": "<PERSON><PERSON>, English actor and comedian", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ros_Vlah<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Brazilian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>o"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Colombian cyclist", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>al\"><PERSON><PERSON></a>, Colombian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>al"}]}, {"year": "1997", "text": "<PERSON>, Colombian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_D%C3%<PERSON><PERSON>_(footballer,_born_1997)\" title=\"<PERSON> (footballer, born 1997)\"><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_D%C3%<PERSON><PERSON>_(footballer,_born_1997)\" title=\"<PERSON> (footballer, born 1997)\"><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON> (footballer, born 1997)", "link": "https://wikipedia.org/wiki/Luis_D%C3%<PERSON><PERSON>_(footballer,_born_1997)"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Canadian ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>avi<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Russian ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Russian tennis player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "86 BC", "text": "<PERSON>, Roman general and politician (b. 157 BC)", "html": "86 BC - 86 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gaius <PERSON>\"><PERSON></a>, Roman general and politician (b. 157 BC)", "no_year_html": "86 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman general and politician (b. 157 BC)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "533", "text": "<PERSON><PERSON><PERSON><PERSON>, French bishop and saint (b. 437)", "html": "533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Remigius\" title=\"Saint Remigius\"><PERSON><PERSON><PERSON><PERSON></a>, French bishop and saint (b. 437)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Remigius\" title=\"<PERSON> Remigius\"><PERSON><PERSON><PERSON><PERSON></a>, French bishop and saint (b. 437)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saint_Remigius"}]}, {"year": "614", "text": "<PERSON><PERSON>, English-Scottish bishop and saint", "html": "614 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"Saint Mungo\"><PERSON><PERSON></a>, English-Scottish bishop and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> Mungo\"><PERSON><PERSON></a>, English-Scottish bishop and saint", "links": [{"title": "Saint <PERSON>", "link": "https://wikipedia.org/wiki/Saint_Mungo"}]}, {"year": "703", "text": "<PERSON><PERSON>, Japanese empress (b. 645)", "html": "703 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>%C5%8D\" title=\"Empress <PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese empress (b. 645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>%C5%8D\" title=\"Empress <PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese empress (b. 645)", "links": [{"title": "Empress <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Empress_<PERSON><PERSON>%C5%8D"}]}, {"year": "858", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, king of Wessex", "html": "858 - <a href=\"https://wikipedia.org/wiki/%C3%86the<PERSON><PERSON><PERSON>_of_Wessex\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Wessex\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, king of Wessex", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86the<PERSON><PERSON><PERSON>_of_Wessex\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Wessex\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, king of Wessex", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Wessex", "link": "https://wikipedia.org/wiki/%C3%86the<PERSON><PERSON><PERSON>_of_Wessex"}]}, {"year": "888", "text": "<PERSON> the <PERSON>, Frankish king and emperor (b. 839)", "html": "888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Fat\"><PERSON> the <PERSON></a>, Frankish king and emperor (b. 839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON></a>, Frankish king and emperor (b. 839)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fat"}]}, {"year": "927", "text": "<PERSON><PERSON> of Cluny, Frankish monk and abbot", "html": "927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Cluny\" title=\"<PERSON><PERSON> of Cluny\"><PERSON><PERSON> of Cluny</a>, Frankish monk and abbot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Cluny\" title=\"<PERSON><PERSON> of Cluny\"><PERSON><PERSON> of Cluny</a>, Frankish monk and abbot", "links": [{"title": "<PERSON><PERSON> of Cluny", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Cluny"}]}, {"year": "1001", "text": "<PERSON><PERSON>, Japanese empress (b. 977)", "html": "1001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_<PERSON>ishi\" title=\"<PERSON>wara no Teishi\"><PERSON><PERSON> no <PERSON></a>, Japanese empress (b. 977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_<PERSON>ishi\" title=\"<PERSON>wara no Teishi\"><PERSON><PERSON> no <PERSON></a>, Japanese empress (b. 977)", "links": [{"title": "<PERSON><PERSON> no <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>ishi"}]}, {"year": "1147", "text": "<PERSON>, Grand Master of the Knights Templar", "html": "1147 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Master of the Knights Templar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Master of the Knights Templar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1151", "text": "<PERSON><PERSON>, French historian and politician (b. 1081)", "html": "1151 - <a href=\"https://wikipedia.org/wiki/<PERSON>ger\" title=\"<PERSON>ger\"><PERSON><PERSON></a>, French historian and politician (b. 1081)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suger\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French historian and politician (b. 1081)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Suger"}]}, {"year": "1177", "text": "<PERSON>, count palatine and duke of Austria (b. 1107)", "html": "1177 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON> II</a>, count palatine and duke of Austria (b. 1107)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON> II</a>, count palatine and duke of Austria (b. 1107)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}]}, {"year": "1321", "text": "<PERSON><PERSON><PERSON>, Italian noblewoman (b. 1254)", "html": "1321 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian noblewoman (b. 1254)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian noblewoman (b. 1254)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1330", "text": "<PERSON>, duke and king of Germany", "html": "1330 - <a href=\"https://wikipedia.org/wiki/Frederick_the_Fair\" title=\"Frederick the Fair\"><PERSON> I</a>, duke and king of Germany", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frederick_<PERSON>_Fair\" title=\"Frederick the Fair\"><PERSON></a>, duke and king of Germany", "links": [{"title": "Frederick the <PERSON>", "link": "https://wikipedia.org/wiki/Frederick_the_Fair"}]}, {"year": "1363", "text": "<PERSON><PERSON><PERSON>, German nobleman (b. 1344)", "html": "1363 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III,_Count_of_Gorizia-Tyrol\" title=\"<PERSON><PERSON><PERSON>, Count of Gorizia-Tyrol\"><PERSON><PERSON><PERSON></a>, German nobleman (b. 1344)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III,_Count_of_Gorizia-Tyrol\" title=\"<PERSON><PERSON><PERSON>, Count of Gorizia-Tyrol\"><PERSON><PERSON><PERSON></a>, German nobleman (b. 1344)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Gorizia-Tyrol", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Gorizia-Tyrol"}]}, {"year": "1400", "text": "<PERSON>, 1st Earl of Gloucester, English politician (b. 1373)", "html": "1400 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Gloucester\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl of Gloucester\"><PERSON>, 1st Earl of Gloucester</a>, English politician (b. 1373)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Gloucester\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl of Gloucester\"><PERSON>, 1st Earl of Gloucester</a>, English politician (b. 1373)", "links": [{"title": "<PERSON>, 1st Earl of Gloucester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Gloucester"}]}, {"year": "1599", "text": "<PERSON>, English poet, Chief Secretary for Ireland (b. 1552)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_for_Ireland\" title=\"Chief Secretary for Ireland\">Chief Secretary for Ireland</a> (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_for_Ireland\" title=\"Chief Secretary for Ireland\">Chief Secretary for Ireland</a> (b. 1552)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Secretary for Ireland", "link": "https://wikipedia.org/wiki/Chief_Secretary_for_Ireland"}]}, {"year": "1612", "text": "<PERSON>, English lady-in-waiting (b. 1538)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lady-in-waiting (b. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lady-in-waiting (b. 1538)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1625", "text": "<PERSON> the Elder, Flemish painter (b. 1568)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Flemish painter (b. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Flemish painter (b. 1568)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder"}]}, {"year": "1684", "text": "<PERSON>, 6th Duke of Norfolk, English nobleman (b. 1628)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Duke_of_Norfolk\" title=\"<PERSON>, 6th Duke of Norfolk\"><PERSON>, 6th Duke of Norfolk</a>, English nobleman (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Duke_of_Norfolk\" title=\"<PERSON>, 6th Duke of Norfolk\"><PERSON>, 6th Duke of Norfolk</a>, English nobleman (b. 1628)", "links": [{"title": "<PERSON>, 6th Duke of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Duke_of_Norfolk"}]}, {"year": "1691", "text": "<PERSON>, English religious leader, founded the Religious Society of Friends (b. 1624)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English religious leader, founded the <a href=\"https://wikipedia.org/wiki/Religious_Society_of_Friends\" class=\"mw-redirect\" title=\"Religious Society of Friends\">Religious Society of Friends</a> (b. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English religious leader, founded the <a href=\"https://wikipedia.org/wiki/Religious_Society_of_Friends\" class=\"mw-redirect\" title=\"Religious Society of Friends\">Religious Society of Friends</a> (b. 1624)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Religious Society of Friends", "link": "https://wikipedia.org/wiki/Religious_Society_of_Friends"}]}, {"year": "1717", "text": "<PERSON>, German entomologist and illustrator (b. 1647)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German entomologist and illustrator (b. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German entomologist and illustrator (b. 1647)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON>, German theologian and author (b. 1693)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (b. 1693)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, French admiral (b. 1712)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Bou%C3%ABxic,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte <PERSON>\"><PERSON></a>, French admiral (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Bou%C3%ABxic,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte <PERSON>\"><PERSON></a>, French admiral (b. 1712)", "links": [{"title": "<PERSON>, comte <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>u%C3%ABxic,_comte_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1796", "text": "<PERSON>, Scottish philosopher and educator (b. 1726)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(natural_philosopher)\" title=\"<PERSON> (natural philosopher)\"><PERSON></a>, Scottish philosopher and educator (b. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(natural_philosopher)\" title=\"<PERSON> (natural philosopher)\"><PERSON></a>, Scottish philosopher and educator (b. 1726)", "links": [{"title": "<PERSON> (natural philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_(natural_philosopher)"}]}, {"year": "1832", "text": "<PERSON>, English cricketer, founded Lord's Cricket Ground (b. 1755)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, founded <a href=\"https://wikipedia.org/wiki/Lord%27s_Cricket_Ground\" class=\"mw-redirect\" title=\"Lord's Cricket Ground\">Lord's Cricket Ground</a> (b. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, founded <a href=\"https://wikipedia.org/wiki/Lord%27s_Cricket_Ground\" class=\"mw-redirect\" title=\"Lord's Cricket Ground\">Lord's Cricket Ground</a> (b. 1755)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord's Cricket Ground", "link": "https://wikipedia.org/wiki/Lord%27s_Cricket_Ground"}]}, {"year": "1838", "text": "<PERSON>, German pianist and composer (b. 1784)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, American surgeon and politician (b. 1786)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_York_politician)\" title=\"<PERSON> (New York politician)\"><PERSON></a>, American surgeon and politician (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_York_politician)\" title=\"<PERSON> (New York politician)\"><PERSON></a>, American surgeon and politician (b. 1786)", "links": [{"title": "<PERSON> (New York politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_York_politician)"}]}, {"year": "1864", "text": "<PERSON>, American composer and songwriter (b. 1826)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, English architect and engineer (b. 1801)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and engineer (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and engineer (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, German engineer and businessman, co-founded the Mauser Company (b. 1834)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wilhelm Ma<PERSON>\"><PERSON></a>, German engineer and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Mauser\" title=\"Mauser\">Mauser Company</a> (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Wilhelm Mauser\"><PERSON></a>, German engineer and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Mauser\" title=\"Mauser\">Mauser Company</a> (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ma<PERSON>", "link": "https://wikipedia.org/wiki/Mauser"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, American journalist and politician, 17th Vice President of the United States (b. 1823)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Colfax\"><PERSON><PERSON><PERSON></a>, American journalist and politician, 17th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and politician, 17th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1823)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1889", "text": "<PERSON>, American lawyer and politician (b. 1823)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bundy\"><PERSON></a>, American lawyer and politician (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bundy\"><PERSON></a>, American lawyer and politician (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Russian physicist and academic (b. 1859)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Estonian theologist and linguist (b. 1839)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian theologist and linguist (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hurt\"><PERSON></a>, Estonian theologist and linguist (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Scottish-Nigerian missionary (b. 1848)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Nigerian missionary (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Nigerian missionary (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Mexican military officer and president, 1913-1914 (b. 1850)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican military officer and president, 1913-1914 (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican military officer and president, 1913-1914 (b. 1850)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, French academic and politician, Prime Minister of France (b. 1842)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1924", "text": "<PERSON>, German physicist and academic (b. 1834)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American serial killer", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American police officer (b. 1848)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>p"}]}, {"year": "1929", "text": "<PERSON><PERSON> <PERSON><PERSON>, Irish-Australian judge and politician, 3rd Attorney-General for Australia (b. 1851)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish-Australian judge and politician, 3rd <a href=\"https://wikipedia.org/wiki/Attorney-General_for_Australia\" class=\"mw-redirect\" title=\"Attorney-General for Australia\">Attorney-General for Australia</a> (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish-Australian judge and politician, 3rd <a href=\"https://wikipedia.org/wiki/Attorney-General_for_Australia\" class=\"mw-redirect\" title=\"Attorney-General for Australia\">Attorney-General for Australia</a> (b. 1851)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Attorney-General for Australia", "link": "https://wikipedia.org/wiki/Attorney-General_for_Australia"}]}, {"year": "1934", "text": "<PERSON>, French physicist and chemist (b. 1860)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Irish novelist, short story writer, and poet (b. 1882)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, short story writer, and poet (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, short story writer, and poet (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Swiss painter and sculptor (b. 1889)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and sculptor (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and sculptor (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Finnish architect and designer (b. 1894)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish architect and designer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish architect and designer (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, German-American painter and illustrator (b. 1871)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American painter and illustrator (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American painter and illustrator (b. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1957", "text": "<PERSON><PERSON> <PERSON><PERSON> English poet and short story writer (b. 1878)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> English poet and short story writer (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> English poet and short story writer (b. 1878)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American film producer, co-founded Paramount Pictures (b. 1880)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Paramount_Pictures\" title=\"Paramount Pictures\">Paramount Pictures</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Paramount_Pictures\" title=\"Paramount Pictures\">Paramount Pictures</a> (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Paramount Pictures", "link": "https://wikipedia.org/wiki/Paramount_Pictures"}]}, {"year": "1958", "text": "<PERSON>, American actress (b. 1895)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor and game show host (b. 1919)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Togolese businessman and politician, President of Togo (b. 1902)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Sylvanus_Olympio\" title=\"Sylvanus Olympio\"><PERSON><PERSON><PERSON><PERSON></a>, Togolese businessman and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Togo\" class=\"mw-redirect\" title=\"List of heads of state of Togo\">President of Togo</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sylvanus_Olympio\" title=\"Sylvanus Olympio\"><PERSON><PERSON><PERSON><PERSON></a>, Togolese businessman and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Togo\" class=\"mw-redirect\" title=\"List of heads of state of Togo\">President of Togo</a> (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sylvanus_Olympio"}, {"title": "List of heads of state of Togo", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Togo"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Russian-English screenwriter and producer (b. 1910)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-English screenwriter and producer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-English screenwriter and producer (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English composer and educator (b. 1910)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Turkish screenwriter and producer (b. 1908)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Sabahattin_Ey%C3%BCbo%C4%9Flu\" title=\"Sabahattin Eyüboğlu\"><PERSON><PERSON><PERSON></a>, Turkish screenwriter and producer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sabahattin_Ey%C3%BCbo%C4%9Flu\" title=\"Sabahattin Eyüboğlu\"><PERSON><PERSON><PERSON></a>, Turkish screenwriter and producer (b. 1908)", "links": [{"title": "Sabahattin <PERSON>", "link": "https://wikipedia.org/wiki/Sabahattin_Ey%C3%BCbo%C4%9Flu"}]}, {"year": "1974", "text": "<PERSON>, Canadian tenor and educator (b. 1906)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tenor and educator (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tenor and educator (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Mexican playwright and poet (b. 1904)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Salvador_Novo\" title=\"Salvador Novo\"><PERSON> Novo</a>, Mexican playwright and poet (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Novo\" title=\"Salvador Novo\">Salvador Novo</a>, Mexican playwright and poet (b. 1904)", "links": [{"title": "Salvador Novo", "link": "https://wikipedia.org/wiki/Salvador_Novo"}]}, {"year": "1976", "text": "<PERSON>, English actress (b. 1922)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Turkish-French historian, co-founded the Cinémathèque Française (b. 1914)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-French historian, co-founded the <a href=\"https://wikipedia.org/wiki/Cin%C3%A9math%C3%A8<PERSON>_<PERSON>an%C3%A7aise\" class=\"mw-redirect\" title=\"Cinémathèque Française\">Cinémathèque Française</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-French historian, co-founded the <a href=\"https://wikipedia.org/wiki/Cin%C3%A9math%C3%A8<PERSON>_<PERSON>an%C3%A7aise\" class=\"mw-redirect\" title=\"Cinémathèque Française\">Cinémathèque Française</a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Cinémathèque Française", "link": "https://wikipedia.org/wiki/Cin%C3%A9math%C3%A8que_Fran%C3%A7aise"}]}, {"year": "1978", "text": "<PERSON>, American pharmacist, academic, and politician, 38th Vice President of the United States (b. 1911)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacist, academic, and politician, 38th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacist, academic, and politician, 38th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1978", "text": "<PERSON>, American baseball player and manager (b. 1887)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(manager)\" class=\"mw-redirect\" title=\"<PERSON> (manager)\"><PERSON></a>, American baseball player and manager (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(manager)\" class=\"mw-redirect\" title=\"<PERSON> (manager)\"><PERSON></a>, American baseball player and manager (b. 1887)", "links": [{"title": "<PERSON> (manager)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(manager)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American singer-songwriter, pianist, and producer (b. 1945)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian-American soprano (b. 1907)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American soprano (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American soprano (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Russian-American conductor (b. 1901)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American conductor (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American conductor (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, French director and screenwriter (b. 1912)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, French race car driver and engineer (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver and engineer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver and engineer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Bonnet"}]}, {"year": "1986", "text": "<PERSON>, Yemeni educator and politician, 4th President of South Yemen (b. 1939)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Yemeni educator and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_South_Yemen\" title=\"President of South Yemen\">President of South Yemen</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Yemeni educator and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_South_Yemen\" title=\"President of South Yemen\">President of South Yemen</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of South Yemen", "link": "https://wikipedia.org/wiki/President_of_South_Yemen"}]}, {"year": "1986", "text": "<PERSON>, Australian rugby league player (b. 1940)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Chinese politician, President of the Republic of China (b. 1910)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "President of the Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_China"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Brazilian composer and conductor (b. 1907)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Camar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian composer and conductor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Camar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian composer and conductor (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Camargo_<PERSON>ieri"}]}, {"year": "1995", "text": "<PERSON>, Australian journalist, poet, and author (b. 1921)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Australian journalist, poet, and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Australian journalist, poet, and author (b. 1921)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "2002", "text": "<PERSON>, Canadian actor, comedian, and screenwriter (b. 1916)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, comedian, and screenwriter (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, comedian, and screenwriter (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American director and screenwriter (b. 1914)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Norman_Panama\" title=\"Norman Panama\"><PERSON></a>, American director and screenwriter (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norman_Panama\" title=\"Norman Panama\"><PERSON></a>, American director and screenwriter (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Norman_Panama"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Jr., Norwegian businessman and mountaineer (b. 1937)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A6ss,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, Norwegian businessman and mountaineer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A6ss,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, Norwegian businessman and mountaineer (b. 1937)", "links": [{"title": "<PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/Arne_N%C3%A6ss,_<PERSON>."}]}, {"year": "2005", "text": "<PERSON>, Canadian journalist (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, Canadian journalist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, Canadian journalist (b. 1915)", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)"}]}, {"year": "2005", "text": "<PERSON>, American soprano and actress (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American journalist and sportscaster (b. 1934)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Canadian-American ice hockey player and coach (b. 1967)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American saxophonist and composer (b. 1949)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American race car driver (b. 1911)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American baseball player and coach (b. 1932)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Welsh socialite and politician (b. 1946)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh socialite and politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh socialite and politician (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Irish-American actor, director, and producer (b. 1928)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor, director, and producer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor, director, and producer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Lebanese poet, composer, and producer (b. 1925)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Mansour_Rahbani\" title=\"<PERSON>our Rahban<PERSON>\"><PERSON><PERSON></a>, Lebanese poet, composer, and producer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mansour_Rah<PERSON>i\" title=\"Mansour Rahbani\"><PERSON><PERSON></a>, Lebanese poet, composer, and producer (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mansour_<PERSON><PERSON><PERSON>i"}]}, {"year": "2009", "text": "<PERSON><PERSON> <PERSON><PERSON>, American poet (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/W._<PERSON>._Snodgrass\" title=\"W. D. Snodgrass\"><PERSON><PERSON> <PERSON><PERSON></a>, American poet (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._D._Snodgrass\" title=\"W. D. Snodgrass\"><PERSON><PERSON> <PERSON><PERSON></a>, American poet (b. 1926)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_Snodgrass"}]}, {"year": "2009", "text": "<PERSON>, Australian pilot (b. 1915)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pilot (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pilot (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American singer-songwriter (b. 1950)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>grass\"><PERSON></a>, American singer-songwriter (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Dutch businessman (b. 1927)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1927)\" class=\"mw-redirect\" title=\"<PERSON> (born 1927)\"><PERSON></a>, Dutch businessman (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1927)\" class=\"mw-redirect\" title=\"<PERSON> (born 1927)\"><PERSON></a>, Dutch businessman (b. 1927)", "links": [{"title": "<PERSON> (born 1927)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1927)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Turkish-Cypriot lawyer and politician, 1st President of Northern Cyprus (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-Cypriot lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Northern_Cyprus\" title=\"President of Northern Cyprus\">President of Northern Cyprus</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-Cypriot lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Northern_Cyprus\" title=\"President of Northern Cyprus\">President of Northern Cyprus</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rauf_Denkta%C5%9F"}, {"title": "President of Northern Cyprus", "link": "https://wikipedia.org/wiki/President_of_Northern_Cyprus"}]}, {"year": "2012", "text": "<PERSON>, German physicist and engineer (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and engineer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and engineer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Serbian footballer and manager (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Miljani%C4%87"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American philosopher and theologian (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American philosopher and theologian (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American philosopher and theologian (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Sr., American lieutenant and politician (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American lieutenant and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American lieutenant and politician (b. 1924)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Sr."}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Chinese-American mathematician and academic (b. 1916)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American mathematician and academic (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American mathematician and academic (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Scottish footballer and manager (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager (b. 1931)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American journalist and politician (b. 1978)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and politician (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and politician (b. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, German general and lawyer (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Wald<PERSON><PERSON>_von_Gazen\" title=\"<PERSON><PERSON><PERSON><PERSON> von Gazen\"><PERSON><PERSON><PERSON><PERSON> <PERSON> Gazen</a>, German general and lawyer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wald<PERSON><PERSON>_von_Gazen\" title=\"<PERSON><PERSON><PERSON><PERSON> von Gazen\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, German general and lawyer (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wald<PERSON><PERSON>_von_Gazen"}]}, {"year": "2015", "text": "<PERSON>, Australian journalist and author (b. 1971)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American diplomat, United States Ambassador to Paraguay (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(ambassador)\" class=\"mw-redirect\" title=\"<PERSON> (ambassador)\"><PERSON></a>, American diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Paraguay\" class=\"mw-redirect\" title=\"United States Ambassador to Paraguay\">United States Ambassador to Paraguay</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(ambassador)\" class=\"mw-redirect\" title=\"<PERSON> (ambassador)\"><PERSON></a>, American diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Paraguay\" class=\"mw-redirect\" title=\"United States Ambassador to Paraguay\">United States Ambassador to Paraguay</a> (b. 1926)", "links": [{"title": "<PERSON> (ambassador)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(ambassador)"}, {"title": "United States Ambassador to Paraguay", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Paraguay"}]}, {"year": "2016", "text": "<PERSON>, English-American actor and director (b. 1935)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and director (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and director (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Georgian-American director, producer, songwriter, and manager (b. 1934)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-American director, producer, songwriter, and manager (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-American director, producer, songwriter, and manager (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American football player (b. 1975)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, 1st Earl of Snowdon, English photographer and a former member of the British royal family (b. 1930)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Snowdon\" title=\"<PERSON>, 1st Earl of Snowdon\"><PERSON>, 1st Earl of Snowdon</a>, English photographer and a former member of the British royal family (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Snowdon\" title=\"<PERSON>, 1st Earl of Snowdon\"><PERSON>, 1st Earl of Snowdon</a>, English photographer and a former member of the British royal family (b. 1930)", "links": [{"title": "<PERSON>, 1st Earl of Snowdon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Snowdon"}]}, {"year": "2017", "text": "<PERSON>, American actor (b. 1931)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Greek electronics engineer (b. 1942)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Magic_Alex\" title=\"Magic Alex\"><PERSON> Alex</a>, Greek electronics engineer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magic_Alex\" title=\"Magic Alex\"><PERSON> Alex</a>, Greek electronics engineer (b. 1942)", "links": [{"title": "Magic Alex", "link": "https://wikipedia.org/wiki/Magic_Alex"}]}, {"year": "2019", "text": "<PERSON>, South African footballer (b. 1969)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American journalist and educator, (b. 1965)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and educator, (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and educator, (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Scottish prelate, Catholic archbishop of Glasgow (b. 1951)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish prelate, Catholic archbishop of <a href=\"https://wikipedia.org/wiki/Glasgow\" title=\"Glasgow\">Glasgow</a> (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish prelate, Catholic archbishop of <a href=\"https://wikipedia.org/wiki/Glasgow\" title=\"Glasgow\">Glasgow</a> (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Glasgow", "link": "https://wikipedia.org/wiki/Glasgow"}]}, {"year": "2024", "text": "<PERSON>, American actress (b. 1924)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON><PERSON>, Italian photographer (b. 1942)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian photographer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian photographer (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}