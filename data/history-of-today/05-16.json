{"date": "May 16", "url": "https://wikipedia.org/wiki/May_16", "data": {"Events": [{"year": "946", "text": "Emperor <PERSON><PERSON><PERSON> abdicates the throne in favor of his brother <PERSON><PERSON><PERSON> who becomes the 62nd emperor of Japan.", "html": "946 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> abdicates the throne in favor of his brother <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> who becomes the 62nd emperor of <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> abdicates the throne in favor of his brother <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> who becomes the 62nd emperor of <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>.", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>"}, {"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}]}, {"year": "1204", "text": "<PERSON>, Count of Flanders is crowned as the first Emperor of the Latin Empire.", "html": "1204 - <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Constantinople\" class=\"mw-redirect\" title=\"<PERSON> I of Constantinople\"><PERSON>, Count of Flanders</a> is crowned as the first <a href=\"https://wikipedia.org/wiki/Emperor\" title=\"Emperor\">Emperor</a> of the <a href=\"https://wikipedia.org/wiki/Latin_Empire\" title=\"Latin Empire\">Latin Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Constantinople\" class=\"mw-redirect\" title=\"<PERSON> I of Constantinople\"><PERSON>, Count of Flanders</a> is crowned as the first <a href=\"https://wikipedia.org/wiki/Emperor\" title=\"Emperor\">Emperor</a> of the <a href=\"https://wikipedia.org/wiki/Latin_Empire\" title=\"Latin Empire\">Latin Empire</a>.", "links": [{"title": "<PERSON> I of Constantinople", "link": "https://wikipedia.org/wiki/Baldwin_I_of_Constantinople"}, {"title": "Emperor", "link": "https://wikipedia.org/wiki/Emperor"}, {"title": "Latin Empire", "link": "https://wikipedia.org/wiki/Latin_Empire"}]}, {"year": "1364", "text": "Hundred Years' War: <PERSON> and a French army defeat the Anglo-Navarrese army of <PERSON> the <PERSON> at Cocherel.", "html": "1364 - <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and a French army defeat the Anglo-Navarrese army of <a href=\"https://wikipedia.org/wiki/Charles_the_Bad\" class=\"mw-redirect\" title=\"Charles the Bad\"><PERSON> the Bad</a> at <a href=\"https://wikipedia.org/wiki/Battle_of_Cocherel\" title=\"Battle of Cocherel\">Cocherel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and a French army defeat the Anglo-Navarrese army of <a href=\"https://wikipedia.org/wiki/Charles_the_Bad\" class=\"mw-redirect\" title=\"Charles the Bad\"><PERSON> Bad</a> at <a href=\"https://wikipedia.org/wiki/Battle_of_Cocherel\" title=\"Battle of Cocherel\">Cocherel</a>.", "links": [{"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bad"}, {"title": "Battle of Cocherel", "link": "https://wikipedia.org/wiki/Battle_of_Cocherel"}]}, {"year": "1426", "text": "Gov. <PERSON><PERSON><PERSON> of Mohnyin becomes King of Ava.", "html": "1426 - Gov. <a href=\"https://wikipedia.org/wiki/Mo<PERSON>yin_Thado\" title=\"<PERSON><PERSON>yi<PERSON> Thado\"><PERSON><PERSON><PERSON> of Mohnyin</a> becomes King of <a href=\"https://wikipedia.org/wiki/Ava_Kingdom\" class=\"mw-redirect\" title=\"Ava Kingdom\">Ava</a>.", "no_year_html": "Gov. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Thado\" title=\"<PERSON><PERSON>yi<PERSON> Thado\"><PERSON><PERSON><PERSON> of Mohnyin</a> becomes King of <a href=\"https://wikipedia.org/wiki/Ava_Kingdom\" class=\"mw-redirect\" title=\"Ava Kingdom\">Ava</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>o"}, {"title": "Ava Kingdom", "link": "https://wikipedia.org/wiki/Ava_Kingdom"}]}, {"year": "1527", "text": "The Florentines drive out the Medici for a second time and Florence re-establishes itself as a republic.", "html": "1527 - The Florentines drive out the <a href=\"https://wikipedia.org/wiki/Medici\" class=\"mw-redirect\" title=\"Medici\"><PERSON></a> for a second time and <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a> re-establishes itself as <a href=\"https://wikipedia.org/wiki/Republic_of_Florence\" title=\"Republic of Florence\">a republic</a>.", "no_year_html": "The Florentines drive out the <a href=\"https://wikipedia.org/wiki/Medici\" class=\"mw-redirect\" title=\"Medici\">Medici</a> for a second time and <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a> re-establishes itself as <a href=\"https://wikipedia.org/wiki/Republic_of_Florence\" title=\"Republic of Florence\">a republic</a>.", "links": [{"title": "Medici", "link": "https://wikipedia.org/wiki/Medici"}, {"title": "Florence", "link": "https://wikipedia.org/wiki/Florence"}, {"title": "Republic of Florence", "link": "https://wikipedia.org/wiki/Republic_of_Florence"}]}, {"year": "1532", "text": "Sir <PERSON> resigns as Lord Chancellor of England.", "html": "1532 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor</a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor</a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}]}, {"year": "1568", "text": "<PERSON>, Queen of Scots, flees to England.", "html": "1568 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, flees to England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, flees to England.", "links": [{"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}]}, {"year": "1584", "text": "<PERSON> becomes sixth governor-general of the Spanish colony of the Philippines.", "html": "1584 - <a href=\"https://wikipedia.org/wiki/Santiago_de_Vera\" title=\"Santiago de Vera\"><PERSON> de Vera</a> becomes sixth governor-general of the Spanish colony of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_de_Vera\" title=\"Santiago de Vera\"><PERSON> de Vera</a> becomes sixth governor-general of the Spanish colony of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "links": [{"title": "Santiago de Vera", "link": "https://wikipedia.org/wiki/Santiago_de_Vera"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1739", "text": "The Battle of Vasai concludes as the Marathas defeat the Portuguese army.", "html": "1739 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Vasai\" title=\"Battle of Vasai\">Battle of Vasai</a> concludes as the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\"><PERSON><PERSON><PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a> army.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Vasai\" title=\"Battle of Vasai\">Battle of Vasai</a> concludes as the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\"><PERSON><PERSON><PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a> army.", "links": [{"title": "Battle of Vasai", "link": "https://wikipedia.org/wiki/Battle_of_Vasai"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}, {"title": "Portuguese Empire", "link": "https://wikipedia.org/wiki/Portuguese_Empire"}]}, {"year": "1770", "text": "The 14-year-old <PERSON> marries 15-year-old <PERSON><PERSON><PERSON>, who later becomes king of France.", "html": "1770 - The 14-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries 15-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON><PERSON><PERSON></a>, who later becomes king of <a href=\"https://wikipedia.org/wiki/Early_modern_France\" class=\"mw-redirect\" title=\"Early modern France\">France</a>.", "no_year_html": "The 14-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries 15-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON><PERSON><PERSON></a>, who later becomes king of <a href=\"https://wikipedia.org/wiki/Early_modern_France\" class=\"mw-redirect\" title=\"Early modern France\">France</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}, {"title": "Early modern France", "link": "https://wikipedia.org/wiki/Early_modern_France"}]}, {"year": "1771", "text": "The Battle of Alamance, a pre-American Revolutionary War battle between local militia and a group of rebels called The \"Regulators\", occurs in present-day Alamance County, North Carolina.", "html": "1771 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Alamance\" title=\"Battle of Alamance\">Battle of Alamance</a>, a pre-<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolutionary War</a> battle between local <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militia</a> and a group of rebels called The \"<a href=\"https://wikipedia.org/wiki/War_of_the_Regulation\" class=\"mw-redirect\" title=\"War of the Regulation\">Regulators</a>\", occurs in present-day <a href=\"https://wikipedia.org/wiki/Alamance_County,_North_Carolina\" title=\"Alamance County, North Carolina\">Alamance County, North Carolina</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Alamance\" title=\"Battle of Alamance\">Battle of Alamance</a>, a pre-<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolutionary War</a> battle between local <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militia</a> and a group of rebels called The \"<a href=\"https://wikipedia.org/wiki/War_of_the_Regulation\" class=\"mw-redirect\" title=\"War of the Regulation\">Regulators</a>\", occurs in present-day <a href=\"https://wikipedia.org/wiki/Alamance_County,_North_Carolina\" title=\"Alamance County, North Carolina\">Alamance County, North Carolina</a>.", "links": [{"title": "Battle of Alamance", "link": "https://wikipedia.org/wiki/Battle_of_Alamance"}, {"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "Militia", "link": "https://wikipedia.org/wiki/Militia"}, {"title": "War of the Regulation", "link": "https://wikipedia.org/wiki/War_of_the_Regulation"}, {"title": "Alamance County, North Carolina", "link": "https://wikipedia.org/wiki/Alamance_County,_North_Carolina"}]}, {"year": "1811", "text": "Peninsular War: The allies Spain, Portugal and United Kingdom fight an inconclusive battle against the French at the Albuera. It is, in proportion to the numbers involved, the bloodiest battle of the war.", "html": "1811 - <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: The allies Spain, Portugal and United Kingdom fight an inconclusive battle against the French at the <a href=\"https://wikipedia.org/wiki/Battle_of_Albuera\" title=\"Battle of Albuera\">Albuera</a>. It is, in proportion to the numbers involved, the bloodiest battle of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: The allies Spain, Portugal and United Kingdom fight an inconclusive battle against the French at the <a href=\"https://wikipedia.org/wiki/Battle_of_Albuera\" title=\"Battle of Albuera\">Albuera</a>. It is, in proportion to the numbers involved, the bloodiest battle of the war.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "Battle of Albuera", "link": "https://wikipedia.org/wiki/Battle_of_Albuera"}]}, {"year": "1812", "text": "Imperial Russia signs the Treaty of Bucharest, ending the Russo-Turkish War. The Ottoman Empire cedes Bessarabia to Russia.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Imperial_Russia\" class=\"mw-redirect\" title=\"Imperial Russia\">Imperial Russia</a> signs the <a href=\"https://wikipedia.org/wiki/Treaty_of_Bucharest_(1812)\" title=\"Treaty of Bucharest (1812)\">Treaty of Bucharest</a>, ending the <a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1806%E2%80%9312)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1806-12)\">Russo-Turkish War</a>. The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> cedes <a href=\"https://wikipedia.org/wiki/Bessarabia\" title=\"Bessarabia\">Bessarabia</a> to Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Imperial_Russia\" class=\"mw-redirect\" title=\"Imperial Russia\">Imperial Russia</a> signs the <a href=\"https://wikipedia.org/wiki/Treaty_of_Bucharest_(1812)\" title=\"Treaty of Bucharest (1812)\">Treaty of Bucharest</a>, ending the <a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1806%E2%80%9312)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1806-12)\">Russo-Turkish War</a>. The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> cedes <a href=\"https://wikipedia.org/wiki/Bessarabia\" title=\"Bessarabia\">Bessarabia</a> to Russia.", "links": [{"title": "Imperial Russia", "link": "https://wikipedia.org/wiki/Imperial_Russia"}, {"title": "Treaty of Bucharest (1812)", "link": "https://wikipedia.org/wiki/Treaty_of_Bucharest_(1812)"}, {"title": "Russo-Turkish War (1806-12)", "link": "https://wikipedia.org/wiki/Russo-Turkish_War_(1806%E2%80%9312)"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Bessarabia", "link": "https://wikipedia.org/wiki/Bessarabia"}]}, {"year": "1822", "text": "Greek War of Independence: The Turks capture the Greek town of Souli.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turks</a> capture the Greek town of <a href=\"https://wikipedia.org/wiki/Souli\" title=\"Souli\">Souli</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turks</a> capture the Greek town of <a href=\"https://wikipedia.org/wiki/Souli\" title=\"Soul<PERSON>\">Souli</a>.", "links": [{"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Souli"}]}, {"year": "1832", "text": "<PERSON> discovers the rich silver outcrops of Chañarcillo sparking the Chilean silver rush.", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the rich silver outcrops of <a href=\"https://wikipedia.org/wiki/Cha%C3%B1arcillo\" title=\"Cha<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> sparking the <a href=\"https://wikipedia.org/wiki/Chilean_silver_rush\" title=\"Chilean silver rush\">Chilean silver rush</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the rich silver outcrops of <a href=\"https://wikipedia.org/wiki/Cha%C3%B1arcillo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> sparking the <a href=\"https://wikipedia.org/wiki/Chilean_silver_rush\" title=\"Chilean silver rush\">Chilean silver rush</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cha%C3%B1ar<PERSON><PERSON>"}, {"title": "Chilean silver rush", "link": "https://wikipedia.org/wiki/Chilean_silver_rush"}]}, {"year": "1834", "text": "The Battle of Asseiceira is fought; it was the final and decisive engagement of the Liberal Wars in Portugal.", "html": "1834 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Asseiceira\" title=\"Battle of Asseiceira\">Battle of Asseiceira</a> is fought; it was the final and decisive engagement of the <a href=\"https://wikipedia.org/wiki/Liberal_Wars\" title=\"Liberal Wars\">Liberal Wars</a> in Portugal.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Asseiceira\" title=\"Battle of Asseiceira\">Battle of Asseiceira</a> is fought; it was the final and decisive engagement of the <a href=\"https://wikipedia.org/wiki/Liberal_Wars\" title=\"Liberal Wars\">Liberal Wars</a> in Portugal.", "links": [{"title": "Battle of Asseiceira", "link": "https://wikipedia.org/wiki/Battle_of_Asseiceira"}, {"title": "Liberal Wars", "link": "https://wikipedia.org/wiki/Liberal_Wars"}]}, {"year": "1842", "text": "The first major wagon train heading for the Pacific Northwest sets out on the Oregon Trail from Elm Grove, Missouri, with 100 pioneers.", "html": "1842 - The first major <a href=\"https://wikipedia.org/wiki/Wagon_train\" title=\"Wagon train\">wagon train</a> heading for the <a href=\"https://wikipedia.org/wiki/Pacific_Northwest\" title=\"Pacific Northwest\">Pacific Northwest</a> sets out on the <a href=\"https://wikipedia.org/wiki/Oregon_Trail\" title=\"Oregon Trail\">Oregon Trail</a> from <a href=\"https://wikipedia.org/wiki/Elm_Grove,_Missouri\" class=\"mw-redirect\" title=\"Elm Grove, Missouri\">Elm Grove, Missouri</a>, with 100 pioneers.", "no_year_html": "The first major <a href=\"https://wikipedia.org/wiki/Wagon_train\" title=\"Wagon train\">wagon train</a> heading for the <a href=\"https://wikipedia.org/wiki/Pacific_Northwest\" title=\"Pacific Northwest\">Pacific Northwest</a> sets out on the <a href=\"https://wikipedia.org/wiki/Oregon_Trail\" title=\"Oregon Trail\">Oregon Trail</a> from <a href=\"https://wikipedia.org/wiki/Elm_Grove,_Missouri\" class=\"mw-redirect\" title=\"Elm Grove, Missouri\">Elm Grove, Missouri</a>, with 100 pioneers.", "links": [{"title": "Wagon train", "link": "https://wikipedia.org/wiki/Wagon_train"}, {"title": "Pacific Northwest", "link": "https://wikipedia.org/wiki/Pacific_Northwest"}, {"title": "Oregon Trail", "link": "https://wikipedia.org/wiki/Oregon_Trail"}, {"title": "Elm Grove, Missouri", "link": "https://wikipedia.org/wiki/Elm_Grove,_Missouri"}]}, {"year": "1863", "text": "American Civil War: During the Vicksburg campaign, the decisive Union victory by <PERSON> at the Battle of Champion Hill drives the Confederate army under <PERSON> back towards Vicksburg, Mississippi.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: During the <a href=\"https://wikipedia.org/wiki/Vicksburg_campaign\" title=\"Vicksburg campaign\">Vicksburg campaign</a>, the decisive <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> victory by <a href=\"https://wikipedia.org/wiki/Ulysses_S._Grant\" title=\"Ulysses <PERSON> Grant\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Champion_Hill\" title=\"Battle of Champion Hill\">Battle of Champion Hill</a> drives the <a href=\"https://wikipedia.org/wiki/Confederate_army\" class=\"mw-redirect\" title=\"Confederate army\">Confederate army</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> back towards <a href=\"https://wikipedia.org/wiki/Vicksburg,_Mississippi\" title=\"Vicksburg, Mississippi\">Vicksburg, Mississippi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: During the <a href=\"https://wikipedia.org/wiki/Vicksburg_campaign\" title=\"Vicksburg campaign\">Vicksburg campaign</a>, the decisive <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> victory by <a href=\"https://wikipedia.org/wiki/Ulysses_S._Grant\" title=\"Ulysses S<PERSON> Grant\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Champion_Hill\" title=\"Battle of Champion Hill\">Battle of Champion Hill</a> drives the <a href=\"https://wikipedia.org/wiki/Confederate_army\" class=\"mw-redirect\" title=\"Confederate army\">Confederate army</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> back towards <a href=\"https://wikipedia.org/wiki/Vicksburg,_Mississippi\" title=\"Vicksburg, Mississippi\">Vicksburg, Mississippi</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Vicksburg campaign", "link": "https://wikipedia.org/wiki/Vicksburg_campaign"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Battle of Champion Hill", "link": "https://wikipedia.org/wiki/Battle_of_Champion_Hill"}, {"title": "Confederate army", "link": "https://wikipedia.org/wiki/Confederate_army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vicksburg, Mississippi", "link": "https://wikipedia.org/wiki/Vicksburg,_Mississippi"}]}, {"year": "1866", "text": "The United States Congress establishes the nickel.", "html": "1866 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> establishes the <a href=\"https://wikipedia.org/wiki/Nickel_(United_States_coin)\" title=\"Nickel (United States coin)\">nickel</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> establishes the <a href=\"https://wikipedia.org/wiki/Nickel_(United_States_coin)\" title=\"Nickel (United States coin)\">nickel</a>.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Nickel (United States coin)", "link": "https://wikipedia.org/wiki/Nickel_(United_States_coin)"}]}, {"year": "1868", "text": "The United States Senate fails to convict President <PERSON> by one vote.", "html": "1868 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> fails to <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>\" title=\"Impeachment of <PERSON>\">convict</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> by one vote.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> fails to <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>\" title=\"Impeachment of <PERSON>\">convict</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> by one vote.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "Impeachment of <PERSON>", "link": "https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1874", "text": "A flood on the Mill River in Massachusetts destroys much of four villages and kills 139 people.", "html": "1874 - A flood on the <a href=\"https://wikipedia.org/wiki/Mill_River_(Northampton,_Massachusetts)\" title=\"Mill River (Northampton, Massachusetts)\">Mill River</a> in <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> destroys much of four villages and kills 139 people.", "no_year_html": "A flood on the <a href=\"https://wikipedia.org/wiki/Mill_River_(Northampton,_Massachusetts)\" title=\"Mill River (Northampton, Massachusetts)\">Mill River</a> in <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> destroys much of four villages and kills 139 people.", "links": [{"title": "Mill River (Northampton, Massachusetts)", "link": "https://wikipedia.org/wiki/Mill_River_(Northampton,_Massachusetts)"}, {"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}]}, {"year": "1877", "text": "The 16 May 1877 crisis occurs in France, ending with the dissolution of the National Assembly 22 June and affirming the interpretation of the Constitution of 1875 as a parliamentary rather than presidential system. The elections held in October 1877 led to the defeat of the royalists as a formal political movement in France.", "html": "1877 - The <a href=\"https://wikipedia.org/wiki/16_May_1877_crisis\" title=\"16 May 1877 crisis\">16 May 1877 crisis</a> occurs in <a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">France</a>, ending with the dissolution of the <a href=\"https://wikipedia.org/wiki/French_Parliament\" title=\"French Parliament\">National Assembly</a> 22 June and affirming the interpretation of the <a href=\"https://wikipedia.org/wiki/French_Constitutional_Laws_of_1875\" class=\"mw-redirect\" title=\"French Constitutional Laws of 1875\">Constitution of 1875</a> as a <a href=\"https://wikipedia.org/wiki/Parliamentary\" class=\"mw-redirect\" title=\"Parliamentary\">parliamentary</a> rather than <a href=\"https://wikipedia.org/wiki/Presidential_system\" title=\"Presidential system\">presidential system</a>. The elections held in October 1877 led to the defeat of the <a href=\"https://wikipedia.org/wiki/Monarchism_in_France\" title=\"Monarchism in France\">royalists</a> as a formal political movement in France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/16_May_1877_crisis\" title=\"16 May 1877 crisis\">16 May 1877 crisis</a> occurs in <a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">France</a>, ending with the dissolution of the <a href=\"https://wikipedia.org/wiki/French_Parliament\" title=\"French Parliament\">National Assembly</a> 22 June and affirming the interpretation of the <a href=\"https://wikipedia.org/wiki/French_Constitutional_Laws_of_1875\" class=\"mw-redirect\" title=\"French Constitutional Laws of 1875\">Constitution of 1875</a> as a <a href=\"https://wikipedia.org/wiki/Parliamentary\" class=\"mw-redirect\" title=\"Parliamentary\">parliamentary</a> rather than <a href=\"https://wikipedia.org/wiki/Presidential_system\" title=\"Presidential system\">presidential system</a>. The elections held in October 1877 led to the defeat of the <a href=\"https://wikipedia.org/wiki/Monarchism_in_France\" title=\"Monarchism in France\">royalists</a> as a formal political movement in France.", "links": [{"title": "16 May 1877 crisis", "link": "https://wikipedia.org/wiki/16_May_1877_crisis"}, {"title": "French Third Republic", "link": "https://wikipedia.org/wiki/French_Third_Republic"}, {"title": "French Parliament", "link": "https://wikipedia.org/wiki/French_Parliament"}, {"title": "French Constitutional Laws of 1875", "link": "https://wikipedia.org/wiki/French_Constitutional_Laws_of_1875"}, {"title": "Parliamentary", "link": "https://wikipedia.org/wiki/Parliamentary"}, {"title": "Presidential system", "link": "https://wikipedia.org/wiki/Presidential_system"}, {"title": "Monarchism in France", "link": "https://wikipedia.org/wiki/Monarchism_in_France"}]}, {"year": "1888", "text": "Nikola Tesla delivers a lecture describing the equipment which will allow efficient generation and use of alternating currents to transmit electric power over long distances.", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tesla\"><PERSON></a> delivers a lecture describing the equipment which will allow efficient generation and use of <a href=\"https://wikipedia.org/wiki/Alternating_current\" title=\"Alternating current\">alternating currents</a> to <a href=\"https://wikipedia.org/wiki/Electric_power_transmission\" title=\"Electric power transmission\">transmit electric power</a> over long distances.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Te<PERSON>\"><PERSON></a> delivers a lecture describing the equipment which will allow efficient generation and use of <a href=\"https://wikipedia.org/wiki/Alternating_current\" title=\"Alternating current\">alternating currents</a> to <a href=\"https://wikipedia.org/wiki/Electric_power_transmission\" title=\"Electric power transmission\">transmit electric power</a> over long distances.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Alternating current", "link": "https://wikipedia.org/wiki/Alternating_current"}, {"title": "Electric power transmission", "link": "https://wikipedia.org/wiki/Electric_power_transmission"}]}, {"year": "1891", "text": "The International Electrotechnical Exhibition opened in Frankfurt, Germany, featuring the world's first long-distance transmission of high-power, three-phase electric current (the most common form today).", "html": "1891 - The <a href=\"https://wikipedia.org/wiki/International_Electrotechnical_Exhibition\" title=\"International Electrotechnical Exhibition\">International Electrotechnical Exhibition</a> opened in <a href=\"https://wikipedia.org/wiki/Frankfurt\" title=\"Frankfurt\">Frankfurt</a>, Germany, featuring the world's first long-distance transmission of high-power, <a href=\"https://wikipedia.org/wiki/Three-phase_electric_power\" title=\"Three-phase electric power\">three-phase electric current</a> (the most common form today).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Electrotechnical_Exhibition\" title=\"International Electrotechnical Exhibition\">International Electrotechnical Exhibition</a> opened in <a href=\"https://wikipedia.org/wiki/Frankfurt\" title=\"Frankfurt\">Frankfurt</a>, Germany, featuring the world's first long-distance transmission of high-power, <a href=\"https://wikipedia.org/wiki/Three-phase_electric_power\" title=\"Three-phase electric power\">three-phase electric current</a> (the most common form today).", "links": [{"title": "International Electrotechnical Exhibition", "link": "https://wikipedia.org/wiki/International_Electrotechnical_Exhibition"}, {"title": "Frankfurt", "link": "https://wikipedia.org/wiki/Frankfurt"}, {"title": "Three-phase electric power", "link": "https://wikipedia.org/wiki/Three-phase_electric_power"}]}, {"year": "1916", "text": "The United Kingdom of Great Britain and Ireland and the French Third Republic sign the secret wartime Sykes-Picot Agreement partitioning former Ottoman territories such as Iraq and Syria.", "html": "1916 - The <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a> and the <a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">French Third Republic</a> sign the secret wartime <a href=\"https://wikipedia.org/wiki/Sykes-Picot_Agreement\" class=\"mw-redirect\" title=\"Sykes-Picot Agreement\">Sykes-Picot Agreement</a> <a href=\"https://wikipedia.org/wiki/Partitioning_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Partitioning of the Ottoman Empire\">partitioning former Ottoman territories</a> such as <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> and <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a> and the <a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">French Third Republic</a> sign the secret wartime <a href=\"https://wikipedia.org/wiki/Sykes-Picot_Agreement\" class=\"mw-redirect\" title=\"Sykes-Picot Agreement\">Sykes-Picot Agreement</a> <a href=\"https://wikipedia.org/wiki/Partitioning_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Partitioning of the Ottoman Empire\">partitioning former Ottoman territories</a> such as <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> and <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>.", "links": [{"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "French Third Republic", "link": "https://wikipedia.org/wiki/French_Third_Republic"}, {"title": "Sykes-Picot Agreement", "link": "https://wikipedia.org/wiki/Sykes-Picot_Agreement"}, {"title": "Partitioning of the Ottoman Empire", "link": "https://wikipedia.org/wiki/Partitioning_of_the_Ottoman_Empire"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}, {"year": "1918", "text": "The Sedition Act of 1918 is passed by the U.S. Congress, making criticism of the government during wartime an imprisonable offense. It will be repealed less than two years later.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Sedition_Act_of_1918\" title=\"Sedition Act of 1918\">Sedition Act of 1918</a> is passed by the U.S. Congress, making criticism of the government during wartime an imprisonable offense. It will be repealed less than two years later.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sedition_Act_of_1918\" title=\"Sedition Act of 1918\">Sedition Act of 1918</a> is passed by the U.S. Congress, making criticism of the government during wartime an imprisonable offense. It will be repealed less than two years later.", "links": [{"title": "Sedition Act of 1918", "link": "https://wikipedia.org/wiki/Sedition_Act_of_1918"}]}, {"year": "1919", "text": "A naval Curtiss NC-4 aircraft commanded by <PERSON> leaves Trepassey, Newfoundland, for Lisbon via the Azores on the first transatlantic flight.", "html": "1919 - A naval <a href=\"https://wikipedia.org/wiki/Curtiss_NC-4\" title=\"Curtiss NC-4\">Curtiss NC-4</a> aircraft commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Trepassey\" title=\"Trepassey\">Trepassey</a>, <a href=\"https://wikipedia.org/wiki/Newfoundland_and_Labrador\" title=\"Newfoundland and Labrador\">Newfoundland</a>, for <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> via the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a> on the first <a href=\"https://wikipedia.org/wiki/Transatlantic_flight\" title=\"Transatlantic flight\">transatlantic flight</a>.", "no_year_html": "A naval <a href=\"https://wikipedia.org/wiki/Curtiss_NC-4\" title=\"Curtiss NC-4\">Curtiss NC-4</a> aircraft commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Trepassey\" title=\"Trepassey\">Trepassey</a>, <a href=\"https://wikipedia.org/wiki/Newfoundland_and_Labrador\" title=\"Newfoundland and Labrador\">Newfoundland</a>, for <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> via the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a> on the first <a href=\"https://wikipedia.org/wiki/Transatlantic_flight\" title=\"Transatlantic flight\">transatlantic flight</a>.", "links": [{"title": "Curtiss NC-4", "link": "https://wikipedia.org/wiki/Curtiss_NC-4"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Trepass<PERSON>", "link": "https://wikipedia.org/wiki/Trepassey"}, {"title": "Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/Newfoundland_and_Labrador"}, {"title": "Lisbon", "link": "https://wikipedia.org/wiki/Lisbon"}, {"title": "Azores", "link": "https://wikipedia.org/wiki/Azores"}, {"title": "Transatlantic flight", "link": "https://wikipedia.org/wiki/Transatlantic_flight"}]}, {"year": "1920", "text": "In Rome, <PERSON> <PERSON> canonizes <PERSON> of Arc.", "html": "1920 - In Rome, <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_XV\" title=\"Pope Benedict XV\">Pope <PERSON></a> <a href=\"https://wikipedia.org/wiki/Canonization_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Canonization of Joan <PERSON> Arc\">canonizes <PERSON></a>.", "no_year_html": "In Rome, <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_XV\" title=\"Pope Benedict XV\">Pope <PERSON></a> <a href=\"https://wikipedia.org/wiki/Canonization_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Canonization of Joan <PERSON> Arc\">canonizes <PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Canonization of <PERSON> of Arc", "link": "https://wikipedia.org/wiki/Canonization_of_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "The first modern performance of <PERSON>'s opera Il ritorno d'Ulisse in patria occurred in Paris.", "html": "1925 - The first modern performance of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/Il_ritorno_d%27Ulis<PERSON>_in_patria\" title=\"Il ritorno d'Ulisse in patria\">Il ritorno d'Ulisse in patria</a></i> occurred in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "no_year_html": "The first modern performance of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/Il_ritorno_d%27Ulisse_in_patria\" title=\"Il ritorno d'Ulisse in patria\">Il ritorno d'Ulisse in patria</a></i> occurred in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Il ritorno d'Ulisse in patria", "link": "https://wikipedia.org/wiki/Il_ritorno_d%27Ulisse_in_patria"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "1929", "text": "In Hollywood, the first Academy Awards ceremony takes place.", "html": "1929 - In <a href=\"https://wikipedia.org/wiki/Hollywood,_Los_Angeles\" title=\"Hollywood, Los Angeles\">Hollywood</a>, the <a href=\"https://wikipedia.org/wiki/1st_Academy_Awards\" title=\"1st Academy Awards\">first</a> <a href=\"https://wikipedia.org/wiki/Academy_Awards\" title=\"Academy Awards\">Academy Awards</a> ceremony takes place.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Hollywood,_Los_Angeles\" title=\"Hollywood, Los Angeles\">Hollywood</a>, the <a href=\"https://wikipedia.org/wiki/1st_Academy_Awards\" title=\"1st Academy Awards\">first</a> <a href=\"https://wikipedia.org/wiki/Academy_Awards\" title=\"Academy Awards\">Academy Awards</a> ceremony takes place.", "links": [{"title": "Hollywood, Los Angeles", "link": "https://wikipedia.org/wiki/Hollywood,_Los_Angeles"}, {"title": "1st Academy Awards", "link": "https://wikipedia.org/wiki/1st_Academy_Awards"}, {"title": "Academy Awards", "link": "https://wikipedia.org/wiki/Academy_Awards"}]}, {"year": "1943", "text": "The Holocaust: The Warsaw Ghetto Uprising ends.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: The <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising\" title=\"Warsaw Ghetto Uprising\">Warsaw Ghetto Uprising</a> ends.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: The <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising\" title=\"Warsaw Ghetto Uprising\">Warsaw Ghetto Uprising</a> ends.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Warsaw Ghetto Uprising", "link": "https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising"}]}, {"year": "1943", "text": "Operation Chastise is undertaken by RAF Bomber Command with specially equipped Avro Lancasters to destroy the Mohne, Sorpe, and Eder dams in the Ruhr valley.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Operation_Chastise\" title=\"Operation Chastise\">Operation Chastise</a> is undertaken by RAF Bomber Command with specially equipped <a href=\"https://wikipedia.org/wiki/Avro_Lancaster\" title=\"Avro Lancaster\">Avro Lancasters</a> to destroy the Mohne, Sorpe, and Eder dams in the Ruhr valley.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Chastise\" title=\"Operation Chastise\">Operation Chastise</a> is undertaken by RAF Bomber Command with specially equipped <a href=\"https://wikipedia.org/wiki/Avro_Lancaster\" title=\"Avro Lancaster\">Avro Lancasters</a> to destroy the Mohne, Sorpe, and Eder dams in the Ruhr valley.", "links": [{"title": "Operation Chastise", "link": "https://wikipedia.org/wiki/Operation_Chastise"}, {"title": "Avro Lancaster", "link": "https://wikipedia.org/wiki/Avro_Lancaster"}]}, {"year": "1945", "text": "Beginning of the Levant Crisis between Britain and France in Syria. The latter try to quell nationalist protests but backs down after threat of military action by the British.", "html": "1945 - Beginning of the <a href=\"https://wikipedia.org/wiki/Levant_Crisis\" title=\"Levant Crisis\">Levant Crisis</a> between Britain and France in Syria. The latter try to quell nationalist protests but backs down after threat of military action by the British.", "no_year_html": "Beginning of the <a href=\"https://wikipedia.org/wiki/Levant_Crisis\" title=\"Levant Crisis\">Levant Crisis</a> between Britain and France in Syria. The latter try to quell nationalist protests but backs down after threat of military action by the British.", "links": [{"title": "Levant Crisis", "link": "https://wikipedia.org/wiki/Levant_Crisis"}]}, {"year": "1951", "text": "The first regularly scheduled transatlantic flights begin between Idlewild Airport (now John F Kennedy International Airport) in New York City and Heathrow Airport in London, operated by El Al Israel Airlines.", "html": "1951 - The first regularly scheduled <a href=\"https://wikipedia.org/wiki/Transatlantic_flight\" title=\"Transatlantic flight\">transatlantic flights</a> begin between Idlewild Airport (now <a href=\"https://wikipedia.org/wiki/John_F_Kennedy_International_Airport\" class=\"mw-redirect\" title=\"John F Kennedy International Airport\">John F Kennedy International Airport</a>) in New York City and <a href=\"https://wikipedia.org/wiki/Heathrow_Airport\" title=\"Heathrow Airport\">Heathrow Airport</a> in London, operated by <a href=\"https://wikipedia.org/wiki/El_Al_Israel_Airlines\" class=\"mw-redirect\" title=\"El Al Israel Airlines\">El Al Israel Airlines</a>.", "no_year_html": "The first regularly scheduled <a href=\"https://wikipedia.org/wiki/Transatlantic_flight\" title=\"Transatlantic flight\">transatlantic flights</a> begin between Idlewild Airport (now <a href=\"https://wikipedia.org/wiki/John_F_Kennedy_International_Airport\" class=\"mw-redirect\" title=\"John F Kennedy International Airport\">John F Kennedy International Airport</a>) in New York City and <a href=\"https://wikipedia.org/wiki/Heathrow_Airport\" title=\"Heathrow Airport\">Heathrow Airport</a> in London, operated by <a href=\"https://wikipedia.org/wiki/El_Al_Israel_Airlines\" class=\"mw-redirect\" title=\"El Al Israel Airlines\">El Al Israel Airlines</a>.", "links": [{"title": "Transatlantic flight", "link": "https://wikipedia.org/wiki/Transatlantic_flight"}, {"title": "<PERSON> International Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_International_Airport"}, {"title": "Heathrow Airport", "link": "https://wikipedia.org/wiki/Heathrow_Airport"}, {"title": "El Al Israel Airlines", "link": "https://wikipedia.org/wiki/El_Al_Israel_Airlines"}]}, {"year": "1954", "text": "Beginning of the Kengir uprising in the Gulag.", "html": "1954 - Beginning of the <a href=\"https://wikipedia.org/wiki/Kengir_uprising\" title=\"Kengir uprising\">Kengir uprising</a> in the <a href=\"https://wikipedia.org/wiki/Gulag\" title=\"Gulag\">Gulag</a>.", "no_year_html": "Beginning of the <a href=\"https://wikipedia.org/wiki/Kengir_uprising\" title=\"Kengir uprising\">Kengir uprising</a> in the <a href=\"https://wikipedia.org/wiki/Gulag\" title=\"Gulag\">Gulag</a>.", "links": [{"title": "Kengir uprising", "link": "https://wikipedia.org/wiki/Ken<PERSON>r_uprising"}, {"title": "Gulag", "link": "https://wikipedia.org/wiki/Gulag"}]}, {"year": "1959", "text": "The Tritons' Fountain in Valletta, Malta is turned on for the first time.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/Tritons%27_Fountain\" title=\"Tritons' Fountain\">Tritons' Fountain</a> in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta, Malta</a> is turned on for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tritons%27_Fountain\" title=\"Tritons' Fountain\">Tritons' Fountain</a> in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta, Malta</a> is turned on for the first time.", "links": [{"title": "Tritons' Fountain", "link": "https://wikipedia.org/wiki/Tritons%27_Fountain"}, {"title": "Valletta", "link": "https://wikipedia.org/wiki/Valletta"}]}, {"year": "1960", "text": "<PERSON> operates the first optical laser (a ruby laser), at Hughes Research Laboratories in Malibu, California.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> operates the first <a href=\"https://wikipedia.org/wiki/Optical_laser\" class=\"mw-redirect\" title=\"Optical laser\">optical laser</a> (a <a href=\"https://wikipedia.org/wiki/Ruby_laser\" title=\"Ruby laser\">ruby laser</a>), at <a href=\"https://wikipedia.org/wiki/Hughes_Research_Laboratories\" class=\"mw-redirect\" title=\"Hughes Research Laboratories\">Hughes Research Laboratories</a> in <a href=\"https://wikipedia.org/wiki/Malibu,_California\" title=\"Malibu, California\">Malibu, California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> operates the first <a href=\"https://wikipedia.org/wiki/Optical_laser\" class=\"mw-redirect\" title=\"Optical laser\">optical laser</a> (a <a href=\"https://wikipedia.org/wiki/Ruby_laser\" title=\"Ruby laser\">ruby laser</a>), at <a href=\"https://wikipedia.org/wiki/Hughes_Research_Laboratories\" class=\"mw-redirect\" title=\"Hughes Research Laboratories\">Hughes Research Laboratories</a> in <a href=\"https://wikipedia.org/wiki/Malibu,_California\" title=\"Malibu, California\">Malibu, California</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Optical laser", "link": "https://wikipedia.org/wiki/Optical_laser"}, {"title": "Ruby laser", "link": "https://wikipedia.org/wiki/Ruby_laser"}, {"title": "Hughes Research Laboratories", "link": "https://wikipedia.org/wiki/Hughes_Research_Laboratories"}, {"title": "Malibu, California", "link": "https://wikipedia.org/wiki/Malibu,_California"}]}, {"year": "1961", "text": "<PERSON> leads a coup d'état to overthrow the Second Republic of South Korea.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads a <a href=\"https://wikipedia.org/wiki/May_16_coup\" title=\"May 16 coup\">coup d'état</a> to overthrow the <a href=\"https://wikipedia.org/wiki/Second_Republic_of_South_Korea\" class=\"mw-redirect\" title=\"Second Republic of South Korea\">Second Republic of South Korea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads a <a href=\"https://wikipedia.org/wiki/May_16_coup\" title=\"May 16 coup\">coup d'état</a> to overthrow the <a href=\"https://wikipedia.org/wiki/Second_Republic_of_South_Korea\" class=\"mw-redirect\" title=\"Second Republic of South Korea\">Second Republic of South Korea</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "May 16 coup", "link": "https://wikipedia.org/wiki/May_16_coup"}, {"title": "Second Republic of South Korea", "link": "https://wikipedia.org/wiki/Second_Republic_of_South_Korea"}]}, {"year": "1966", "text": "The Chinese Communist Party issues the \"May 16 Notice\", marking the beginning of the Cultural Revolution.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> issues the \"<a href=\"https://wikipedia.org/wiki/May_16_Notice\" class=\"mw-redirect\" title=\"May 16 Notice\">May 16 Notice</a>\", marking the beginning of the <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> issues the \"<a href=\"https://wikipedia.org/wiki/May_16_Notice\" class=\"mw-redirect\" title=\"May 16 Notice\">May 16 Notice</a>\", marking the beginning of the <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>.", "links": [{"title": "Chinese Communist Party", "link": "https://wikipedia.org/wiki/Chinese_Communist_Party"}, {"title": "May 16 Notice", "link": "https://wikipedia.org/wiki/May_16_Notice"}, {"title": "Cultural Revolution", "link": "https://wikipedia.org/wiki/Cultural_Revolution"}]}, {"year": "1969", "text": "Venera program: Venera 5, a Soviet space probe, lands on Venus.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Venera_program\" class=\"mw-redirect\" title=\"Venera program\">Venera program</a>: <i><a href=\"https://wikipedia.org/wiki/Venera_5\" title=\"Venera 5\">Venera 5</a></i>, a Soviet <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a>, lands on <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venera_program\" class=\"mw-redirect\" title=\"Venera program\">Venera program</a>: <i><a href=\"https://wikipedia.org/wiki/Venera_5\" title=\"Venera 5\">Venera 5</a></i>, a Soviet <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a>, lands on <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>.", "links": [{"title": "Venera program", "link": "https://wikipedia.org/wiki/Venera_program"}, {"title": "Venera 5", "link": "https://wikipedia.org/wiki/Venera_5"}, {"title": "Space probe", "link": "https://wikipedia.org/wiki/Space_probe"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON> is elected president for life of Yugoslavia.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/President_for_life\" title=\"President for life\">president for life</a> of <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/President_for_life\" title=\"President for life\">president for life</a> of <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President for life", "link": "https://wikipedia.org/wiki/President_for_life"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}]}, {"year": "1972", "text": "An Antonov An-24 crashes into a kindergarten building in Svetlogorsk, killing 35.", "html": "1972 - An <a href=\"https://wikipedia.org/wiki/Antonov_An-24\" title=\"Antonov An-24\"><PERSON><PERSON> An-24</a> <a href=\"https://wikipedia.org/wiki/1972_Svetlogorsk_An-24_crash\" title=\"1972 Svetlogorsk An-24 crash\">crashes</a> into a <a href=\"https://wikipedia.org/wiki/Kindergarten\" title=\"Kindergarten\">kindergarten</a> building in <a href=\"https://wikipedia.org/wiki/Svetlogorsk,_Kaliningrad_Oblast\" title=\"Svetlogorsk, Kaliningrad Oblast\">Svetlogorsk</a>, killing 35.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Antonov_An-24\" title=\"Antonov An-24\"><PERSON><PERSON> An-24</a> <a href=\"https://wikipedia.org/wiki/1972_Svetlogorsk_An-24_crash\" title=\"1972 Svetlogorsk An-24 crash\">crashes</a> into a <a href=\"https://wikipedia.org/wiki/Kindergarten\" title=\"Kindergarten\">kindergarten</a> building in <a href=\"https://wikipedia.org/wiki/Svetlogorsk,_Kaliningrad_Oblast\" title=\"Svetlogorsk, Kaliningrad Oblast\">Svetlogorsk</a>, killing 35.", "links": [{"title": "Antonov An-24", "link": "https://wikipedia.org/wiki/Antonov_An-24"}, {"title": "1972 Svetlogorsk An-24 crash", "link": "https://wikipedia.org/wiki/1972_Svetlogorsk_An-24_crash"}, {"title": "Kindergarten", "link": "https://wikipedia.org/wiki/Kindergarten"}, {"title": "Svetlogorsk, Kaliningrad Oblast", "link": "https://wikipedia.org/wiki/Svetlogorsk,_Kaliningrad_Oblast"}]}, {"year": "1975", "text": "<PERSON><PERSON> from Japan becomes the first woman to reach the summit of Mount Everest.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a> becomes the first woman to reach the summit of <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a> becomes the first woman to reach the summit of <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}, {"title": "Mount Everest", "link": "https://wikipedia.org/wiki/Mount_Everest"}]}, {"year": "1988", "text": "A report by the Surgeon General of the United States <PERSON><PERSON> states that the addictive properties of nicotine are similar to those of heroin and cocaine.", "html": "1988 - A report by the <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> states that the <a href=\"https://wikipedia.org/wiki/Substance_use_disorder\" title=\"Substance use disorder\">addictive</a> properties of <a href=\"https://wikipedia.org/wiki/Nicotine\" title=\"Nicotine\">nicotine</a> are similar to those of <a href=\"https://wikipedia.org/wiki/Heroin\" title=\"Heroin\">heroin</a> and <a href=\"https://wikipedia.org/wiki/Cocaine\" title=\"Cocaine\">cocaine</a>.", "no_year_html": "A report by the <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> states that the <a href=\"https://wikipedia.org/wiki/Substance_use_disorder\" title=\"Substance use disorder\">addictive</a> properties of <a href=\"https://wikipedia.org/wiki/Nicotine\" title=\"Nicotine\">nicotine</a> are similar to those of <a href=\"https://wikipedia.org/wiki/Heroin\" title=\"Heroin\">heroin</a> and <a href=\"https://wikipedia.org/wiki/Cocaine\" title=\"Cocaine\">cocaine</a>.", "links": [{"title": "Surgeon General of the United States", "link": "https://wikipedia.org/wiki/Surgeon_General_of_the_United_States"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Substance use disorder", "link": "https://wikipedia.org/wiki/Substance_use_disorder"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nico<PERSON>"}, {"title": "<PERSON>in", "link": "https://wikipedia.org/wiki/Heroin"}, {"title": "Cocaine", "link": "https://wikipedia.org/wiki/Cocaine"}]}, {"year": "1991", "text": "Queen <PERSON> of the United Kingdom addresses a joint session of the United States Congress. She is the first British monarch to address the U.S. Congress.", "html": "1991 - Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"Elizabeth II\"><PERSON> II</a> of the United Kingdom addresses a joint session of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>. She is the first British monarch to address the U.S. Congress.", "no_year_html": "Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"Elizabeth II\"><PERSON> II</a> of the United Kingdom addresses a joint session of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>. She is the first British monarch to address the U.S. Congress.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, the President of Zaire, flees the country.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Mobut<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, the President of <a href=\"https://wikipedia.org/wiki/Zaire\" title=\"Zaire\">Zaire</a>, flees the country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the President of <a href=\"https://wikipedia.org/wiki/Zaire\" title=\"Zaire\">Zaire</a>, flees the country.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Zaire", "link": "https://wikipedia.org/wiki/Zaire"}]}, {"year": "2003", "text": "In Morocco, 33 civilians are killed and more than 100 people are injured in the Casablanca terrorist attacks.", "html": "2003 - In <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>, 33 civilians are killed and more than 100 people are injured in the <a href=\"https://wikipedia.org/wiki/2003_Casablanca_bombings\" title=\"2003 Casablanca bombings\">Casablanca terrorist attacks</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>, 33 civilians are killed and more than 100 people are injured in the <a href=\"https://wikipedia.org/wiki/2003_Casablanca_bombings\" title=\"2003 Casablanca bombings\">Casablanca terrorist attacks</a>.", "links": [{"title": "Morocco", "link": "https://wikipedia.org/wiki/Morocco"}, {"title": "2003 Casablanca bombings", "link": "https://wikipedia.org/wiki/2003_Casablanca_bombings"}]}, {"year": "2005", "text": "Kuwait permits women's suffrage in a 35-23 National Assembly vote.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a> permits <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_Kuwait\" title=\"Women's suffrage in Kuwait\">women's suffrage</a> in a 35-23 <a href=\"https://wikipedia.org/wiki/National_Assembly_(Kuwait)\" title=\"National Assembly (Kuwait)\">National Assembly</a> vote.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a> permits <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_Kuwait\" title=\"Women's suffrage in Kuwait\">women's suffrage</a> in a 35-23 <a href=\"https://wikipedia.org/wiki/National_Assembly_(Kuwait)\" title=\"National Assembly (Kuwait)\">National Assembly</a> vote.", "links": [{"title": "Kuwait", "link": "https://wikipedia.org/wiki/Kuwait"}, {"title": "Women's suffrage in Kuwait", "link": "https://wikipedia.org/wiki/Women%27s_suffrage_in_Kuwait"}, {"title": "National Assembly (Kuwait)", "link": "https://wikipedia.org/wiki/National_Assembly_(Kuwait)"}]}, {"year": "2011", "text": "STS-134 (ISS assembly flight ULF6), launched from the Kennedy Space Center on the 25th and final flight for Space Shuttle Endeavour.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/STS-134\" title=\"STS-134\">STS-134</a> (<a href=\"https://wikipedia.org/wiki/ISS_assembly_sequence\" class=\"mw-redirect\" title=\"ISS assembly sequence\">ISS assembly</a> flight ULF6), launched from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> on the 25th and final flight for <span class=\"nowrap\"><a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a></span> <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\"><i>Endeavour</i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/STS-134\" title=\"STS-134\">STS-134</a> (<a href=\"https://wikipedia.org/wiki/ISS_assembly_sequence\" class=\"mw-redirect\" title=\"ISS assembly sequence\">ISS assembly</a> flight ULF6), launched from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> on the 25th and final flight for <span class=\"nowrap\"><a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a></span> <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\"><i>Endeavour</i></a>.", "links": [{"title": "STS-134", "link": "https://wikipedia.org/wiki/STS-134"}, {"title": "ISS assembly sequence", "link": "https://wikipedia.org/wiki/ISS_assembly_sequence"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}, {"title": "Space Shuttle", "link": "https://wikipedia.org/wiki/Space_Shuttle"}, {"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}]}, {"year": "2014", "text": "Twelve people are killed in two explosions in the Gikomba market area of Nairobi, Kenya.", "html": "2014 - Twelve people are killed in <a href=\"https://wikipedia.org/wiki/2014_Gikomba_bombings\" class=\"mw-redirect\" title=\"2014 Gikomba bombings\">two explosions</a> in the Gikomba market area of <a href=\"https://wikipedia.org/wiki/Nairobi,_Kenya\" class=\"mw-redirect\" title=\"Nairobi, Kenya\">Nairobi, Kenya</a>.", "no_year_html": "Twelve people are killed in <a href=\"https://wikipedia.org/wiki/2014_Gikomba_bombings\" class=\"mw-redirect\" title=\"2014 Gikomba bombings\">two explosions</a> in the Gikomba market area of <a href=\"https://wikipedia.org/wiki/Nairobi,_Kenya\" class=\"mw-redirect\" title=\"Nairobi, Kenya\">Nairobi, Kenya</a>.", "links": [{"title": "2014 Gikomba bombings", "link": "https://wikipedia.org/wiki/2014_Gikomba_bombings"}, {"title": "Nairobi, Kenya", "link": "https://wikipedia.org/wiki/Nairobi,_Kenya"}]}], "Births": [{"year": "1418", "text": "<PERSON> of Cyprus, King of Cyprus and Armenia and also titular King of Jerusalem from 1432 to 1458 (probable; d. 1458)", "html": "1418 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> II of Cyprus\"><PERSON> of Cyprus</a>, King of Cyprus and Armenia and also titular King of Jerusalem from 1432 to 1458 (probable; d. 1458)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a>, King of Cyprus and Armenia and also titular King of Jerusalem from 1432 to 1458 (probable; d. 1458)", "links": [{"title": "<PERSON> of Cyprus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus"}]}, {"year": "1455", "text": "<PERSON> of Oettingen, German count (d. 1522)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Oettingen\" title=\"<PERSON> of Oettingen\"><PERSON> of Oettingen</a>, German count (d. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Oettingen\" title=\"<PERSON> of Oettingen\"><PERSON> of Oettingen</a>, German count (d. 1522)", "links": [{"title": "<PERSON> of Oettingen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1542", "text": "<PERSON> of Hanau-Lichtenberg, German noblewoman (d. 1580)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hanau-Lichtenberg\" title=\"<PERSON> of Hanau-Lichtenberg\"><PERSON> of Hanau-Lichtenberg</a>, German noblewoman (d. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hanau-Lichtenberg\" title=\"<PERSON> of Hanau-Lichtenberg\"><PERSON> of Hanau-Lichtenberg</a>, German noblewoman (d. 1580)", "links": [{"title": "<PERSON> of Hanau-Lichtenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_Hanau-<PERSON>"}]}, {"year": "1606", "text": "<PERSON>, British doctor (d. 1656)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British doctor (d. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British doctor (d. 1656)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1611", "text": "<PERSON> (d. 1689)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_XI\" title=\"Pope Innocent XI\">Pope <PERSON></a> (d. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Innocent_XI\" title=\"Pope Innocent XI\"><PERSON></a> (d. 1689)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_XI"}]}, {"year": "1641", "text": "<PERSON>, English economist and politician (d. 1691)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English economist and politician (d. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dudley_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English economist and politician (d. 1691)", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)"}]}, {"year": "1710", "text": "<PERSON>, 1st Earl <PERSON>, English politician, Lord <PERSON>eward of the Household (d. 1782)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON><PERSON>ard_of_the_Household\" class=\"mw-redirect\" title=\"Lord Steward of the Household\">Lord Steward of the Household</a> (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON><PERSON>ard_of_the_Household\" class=\"mw-redirect\" title=\"Lord Steward of the Household\">Lord <PERSON>eward of the Household</a> (d. 1782)", "links": [{"title": "<PERSON>, 1st Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>"}, {"title": "Lord St<PERSON>ard of the Household", "link": "https://wikipedia.org/wiki/Lord_<PERSON><PERSON><PERSON>_of_the_Household"}]}, {"year": "1718", "text": "<PERSON>, Italian mathematician and philosopher (d. 1799)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/Maria_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and philosopher (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and philosopher (d. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Gaetana_Agnesi"}]}, {"year": "1763", "text": "<PERSON>, French pharmacist and chemist (d. 1829)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pharmacist and chemist (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pharmacist and chemist (d. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, German poet and translator (d. 1866)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCckert\" title=\"<PERSON>\"><PERSON></a>, German poet and translator (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and translator (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Friedrich_R%C3%<PERSON><PERSON>t"}]}, {"year": "1801", "text": "<PERSON>, American lawyer and politician, 24th United States Secretary of State (d. 1872)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1804", "text": "<PERSON>, American educator who founded the first U.S. kindergarten (d. 1894)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Elizabeth <PERSON>\"><PERSON></a>, American educator who founded the first U.S. kindergarten (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Elizabeth Peabody\"><PERSON></a>, American educator who founded the first U.S. kindergarten (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, Estonian journalist and poet (d. 1890)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and poet (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and poet (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian mathematician and statistician (d. 1894)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>f<PERSON><PERSON>_<PERSON>eb<PERSON>hev\" title=\"<PERSON><PERSON><PERSON><PERSON>eb<PERSON>hev\"><PERSON><PERSON><PERSON><PERSON></a>, Russian mathematician and statistician (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>f<PERSON><PERSON>_<PERSON>he<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>eb<PERSON>hev\"><PERSON><PERSON><PERSON><PERSON></a>, Russian mathematician and statistician (d. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>hev"}]}, {"year": "1824", "text": "<PERSON>, American banker and politician, 22nd United States Vice President (d. 1920)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Vice_President\" class=\"mw-redirect\" title=\"United States Vice President\">United States Vice President</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Vice_President\" class=\"mw-redirect\" title=\"United States Vice President\">United States Vice President</a> (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Vice President", "link": "https://wikipedia.org/wiki/United_States_Vice_President"}]}, {"year": "1824", "text": "<PERSON>, American general (d. 1893)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, Dutch architect, designed the Amsterdam Centraal railway station and Rijksmuseum (d. 1921)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch architect, designed the <a href=\"https://wikipedia.org/wiki/Amsterdam_Centraal_railway_station\" class=\"mw-redirect\" title=\"Amsterdam Centraal railway station\">Amsterdam Centraal railway station</a> and <a href=\"https://wikipedia.org/wiki/Rijksmuseum\" title=\"Rijksmuseum\">Rijksmuseum</a> (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch architect, designed the <a href=\"https://wikipedia.org/wiki/Amsterdam_Centraal_railway_station\" class=\"mw-redirect\" title=\"Amsterdam Centraal railway station\">Amsterdam Centraal railway station</a> and <a href=\"https://wikipedia.org/wiki/Rijksmuseum\" title=\"Rijksmuseum\">Rijksmuseum</a> (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Amsterdam Centraal railway station", "link": "https://wikipedia.org/wiki/Amsterdam_Centraal_railway_station"}, {"title": "Rijksmuseum", "link": "https://wikipedia.org/wiki/Rijksmuseum"}]}, {"year": "1831", "text": "<PERSON>, Welsh-American physicist, co-invented the microphone (d. 1900)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American physicist, co-invented the <a href=\"https://wikipedia.org/wiki/Microphone\" title=\"Microphone\">microphone</a> (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American physicist, co-invented the <a href=\"https://wikipedia.org/wiki/Microphone\" title=\"Microphone\">microphone</a> (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Microphone", "link": "https://wikipedia.org/wiki/Microphone"}]}, {"year": "1859", "text": "<PERSON>, English golfer (d. 1932)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, English lepidopterist and diarist (d. 1940)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Lepidopterist\" class=\"mw-redirect\" title=\"Lepidopterist\">lepidopterist</a> and diarist (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Lepidopterist\" class=\"mw-redirect\" title=\"Lepidopterist\">lepidopterist</a> and diarist (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lepidopterist", "link": "https://wikipedia.org/wiki/Lepidopterist"}]}, {"year": "1876", "text": "<PERSON>, American biochemist and endocrinologist (d. 1948)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and endocrinologist (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and endocrinologist (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Swiss author and academic (d. 1962)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and academic (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and academic (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, American golfer (d. 1945)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Price\" title=\"Si<PERSON><PERSON> Price\"><PERSON><PERSON><PERSON></a>, American golfer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Price\" title=\"Si<PERSON><PERSON> Price\"><PERSON><PERSON><PERSON></a>, American golfer (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Price"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish politician, 3rd President of Turkey (d. 1986)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Cel%C3%A2l_Bayar\" title=\"Cel<PERSON><PERSON> Bayar\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cel%C3%A2l_Bayar\" title=\"Celâ<PERSON> Bayar\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cel%C3%A2l_Bayar"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1887", "text": "<PERSON>, Brazilian teacher and anarcha-feminist (d. 1945)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian teacher and anarcha-feminist (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian teacher and anarcha-feminist (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American microbiologist and instrument maker (d. 1971)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Royal_Rife\" title=\"Royal Rife\"><PERSON>ife</a>, American microbiologist and instrument maker (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Royal_Rife\" title=\"Royal Rife\"><PERSON> Rife</a>, American microbiologist and instrument maker (d. 1971)", "links": [{"title": "Royal Rife", "link": "https://wikipedia.org/wiki/Royal_Rife"}]}, {"year": "1890", "text": "<PERSON>, American ichthyologist (d. 1975)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ichthyologist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ichthyologist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, American actor (d. 1937)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American actor (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON> Perkins", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American journalist and writer (d. 1960)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and writer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and writer (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Israeli entomologist and academic (d. 1994)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Zvi_Sliternik\" title=\"Zvi Sliternik\"><PERSON><PERSON></a>, Israeli entomologist and academic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zvi_Sliternik\" title=\"<PERSON>vi Sliternik\"><PERSON><PERSON></a>, Israeli entomologist and academic (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zvi_Sliternik"}]}, {"year": "1898", "text": "<PERSON>, Polish-American painter (d. 1980)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, Serbian poet and academic (d. 1993)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian poet and academic (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian poet and academic (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Desanka_<PERSON>ks<PERSON>vi%C4%87"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Japanese director and screenwriter (d. 1956)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and screenwriter (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and screenwriter (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American inventor and manufacturer (d. 1992)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and manufacturer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and manufacturer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American actor (d. 1982)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Australian cricketer (d. 1991)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Canadian painter and educator (d. 1988)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and educator (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and educator (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Venezuelan lawyer, journalist, and author (d. 2001)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan lawyer, journalist, and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan lawyer, journalist, and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, German author and illustrator (d. 1996)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German author and illustrator (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German author and illustrator (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marg<PERSON>_Rey"}]}, {"year": "1907", "text": "<PERSON>, Irish hurdler (d. 2004)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurdler (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hurdler (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American actress and singer (d. 1960)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Italian race car driver (d. 1997)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Russian poet and author (d. 1975)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese monk and educator (d. 2014)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese monk and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese monk and educator (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>de"}]}, {"year": "1910", "text": "<PERSON>, Russian painter and educator (d. 1972)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American historian and author (d. 2008)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Studs_Terkel\" title=\"Studs Terkel\"><PERSON><PERSON> Terkel</a>, American historian and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Studs_Terkel\" title=\"Studs Terkel\"><PERSON><PERSON> Terkel</a>, American historian and author (d. 2008)", "links": [{"title": "Studs Terkel", "link": "https://wikipedia.org/wiki/Studs_Terkel"}]}, {"year": "1913", "text": "<PERSON>, Australian politician, 30th Premier of Queensland (d. 1991)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1913", "text": "<PERSON>, American singer, saxophonist, and clarinet player (d. 1987)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, saxophonist, and clarinet player (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Herman\"><PERSON></a>, American singer, saxophonist, and clarinet player (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American anthropologist and author (d. 2009)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Italian director and screenwriter (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli biophysicist and politician, 4th President of Israel (d. 2009)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli biophysicist and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli biophysicist and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Israel", "link": "https://wikipedia.org/wiki/President_of_Israel"}]}, {"year": "1917", "text": "<PERSON>, American sergeant and pilot (d. 2015)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and pilot (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and pilot (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American lawyer and politician (d. 1999)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Mexican author and photographer (d. 1986)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican author and photographer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican author and photographer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, English footballer and manager (d. 2000)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Wilf_<PERSON>\" title=\"Wilf <PERSON>\">Wil<PERSON></a>, English footballer and manager (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilf_<PERSON>\" title=\"Wilf <PERSON>\">Wil<PERSON></a>, English footballer and manager (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilf_Mannion"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, American pianist and entertainer (d. 1987)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Liberace\" title=\"Liberace\"><PERSON><PERSON><PERSON></a>, American pianist and entertainer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liberace\" title=\"Liberace\"><PERSON><PERSON><PERSON></a>, American pianist and entertainer (d. 1987)", "links": [{"title": "Liberace", "link": "https://wikipedia.org/wiki/Liberace"}]}, {"year": "1919", "text": "<PERSON>, Spanish ecologist and biologist (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish ecologist and biologist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish ecologist and biologist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, French actress (d. 1967)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actor, producer, and screenwriter (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American actor, producer, and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American actor, producer, and screenwriter (d. 2012)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1923", "text": "<PERSON>, American linguist and academic (d. 2000)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Victoria_Fromkin\" title=\"<PERSON>kin\"><PERSON></a>, American linguist and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Fromkin\" title=\"Victoria Fromkin\"><PERSON></a>, American linguist and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victoria_Fromkin"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American economist and academic, Nobel Prize laureate (d. 2000)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Economics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Economics"}]}, {"year": "1923", "text": "<PERSON>, English parapsychologist and author (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(parapsychologist)\" title=\"<PERSON> (parapsychologist)\"><PERSON></a>, English parapsychologist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(parapsychologist)\" title=\"<PERSON> (parapsychologist)\"><PERSON></a>, English parapsychologist and author (d. 2014)", "links": [{"title": "<PERSON> (parapsychologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(parapsychologist)"}]}, {"year": "1924", "text": "<PERSON>, American microbiologist (d. 1999)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, 1st President of the Gambia (d. 2019)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Dawda_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Jawara\"><PERSON><PERSON><PERSON></a>, 1st President of the Gambia (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dawda_<PERSON><PERSON>\" title=\"Dawda Jawara\"><PERSON><PERSON><PERSON></a>, 1st President of the Gambia (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dawda_Jawara"}]}, {"year": "1925", "text": "<PERSON>, American astronomer (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nancy Roman\"><PERSON></a>, American astronomer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nancy Roman\"><PERSON></a>, American astronomer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nancy_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Nigerian banker and economist (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian banker and economist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian banker and economist (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/N%C3%<PERSON>lton_Santos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%<PERSON>lton_Santos\" title=\"Nílton Santos\"><PERSON><PERSON><PERSON></a>, Brazilian footballer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American baseball player and coach (d. 1989)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer-songwriter (d. 1998)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American lawyer and politician (d. 2019)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian academic and politician", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(PQ_politician)\" title=\"<PERSON> (PQ politician)\"><PERSON></a>, Canadian academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(PQ_politician)\" title=\"<PERSON> (PQ politician)\"><PERSON></a>, Canadian academic and politician", "links": [{"title": "<PERSON> (PQ politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(PQ_politician)"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, American poet, essayist, and feminist (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet, essayist, and feminist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet, essayist, and feminist (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Indian scholar and politician, Indian Minister of External Affairs (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian scholar and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)\" title=\"Minister of External Affairs (India)\">Indian Minister of External Affairs</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian scholar and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)\" title=\"Minister of External Affairs (India)\">Indian Minister of External Affairs</a> (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of External Affairs (India)", "link": "https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)"}]}, {"year": "1930", "text": "<PERSON>, Austrian pianist and composer (d. 2000)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian footballer, coach, and manager (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Vujadin_Bo%C5%A1kov\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer, coach, and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vujadin_Bo%C5%A1kov\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer, coach, and manager (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vujadin_Bo%C5%A1kov"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Jewish-Czech Holocaust victim (d. 1944)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jewish-Czech Holocaust victim (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jewish-Czech Holocaust victim (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American soldier and politician, 85th Governor of Connecticut (d. 2023)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American soldier and politician, 85th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American soldier and politician, 85th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (d. 2023)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "Governor of Connecticut", "link": "https://wikipedia.org/wiki/Governor_of_Connecticut"}]}, {"year": "1934", "text": "<PERSON>, Welsh historian and author", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English general (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1936", "text": "<PERSON>, German cardinal (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American ballet dancer and actress (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballet dancer and actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballet dancer and actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English lawyer and politician (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American computer scientist and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Peruvian television host and sexologist (d. 2018)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian television host and sexologist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian television host and sexologist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Italian professor and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian professor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian professor and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Australian archbishop", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian archbishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>-<PERSON>, English lawyer and judge (d. 2015)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (d. 2015)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Baroness <PERSON>, English politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American politician and diplomat, 29th United States Ambassador to Germany", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 29th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Germany\" class=\"mw-redirect\" title=\"United States Ambassador to Germany\">United States Ambassador to Germany</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 29th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Germany\" class=\"mw-redirect\" title=\"United States Ambassador to Germany\">United States Ambassador to Germany</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dan_<PERSON>ats"}, {"title": "United States Ambassador to Germany", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Germany"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch actress, comedian, singer, writer and artist (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Wiet<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>iet<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch actress, comedian, singer, writer and artist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wiet<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch actress, comedian, singer, writer and artist (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wiet<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Panamanian-American drummer, composer, and bandleader", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian-American drummer, composer, and bandleader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian-American drummer, composer, and bandleader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Hungarian footballer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1944)\" title=\"<PERSON><PERSON> (footballer, born 1944)\"><PERSON><PERSON></a>, Hungarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1944)\" title=\"<PERSON><PERSON> (footballer, born 1944)\"><PERSON><PERSON></a>, Hungarian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1944)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(footballer,_born_1944)"}]}, {"year": "1944", "text": "<PERSON>, German Protestant theologian (d. 2024)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Protestant theologian (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Protestant theologian (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> T<PERSON>jo\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English sociologist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sociologist)\" title=\"<PERSON> (sociologist)\"><PERSON></a>, English sociologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sociologist)\" title=\"<PERSON> (sociologist)\"><PERSON></a>, English sociologist and academic", "links": [{"title": "<PERSON> (sociologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sociologist)"}]}, {"year": "1946", "text": "<PERSON>, English guitarist, songwriter and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American writer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Scottish drummer (d. 1999)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish drummer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish drummer (d. 1999)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Canadian religious leader (d. 2011)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Roch_Th%C3%A9riault\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian religious leader (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roch_Th%C3%A9ria<PERSON>\" title=\"<PERSON><PERSON>é<PERSON>\"><PERSON><PERSON></a>, Canadian religious leader (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roch_Th%C3%A9riault"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Danish actor, director, and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English talk show host and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English talk show host and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English talk show host and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Italian automobile and product designer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Enrico Fu<PERSON>\"><PERSON></a>, Italian automobile and product designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Enrico Fumia\"><PERSON></a>, Italian automobile and product designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Scottish engineer and politician (d. 2017)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and politician (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English historian and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON> <PERSON>, Belgian cyclist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American baseball player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1950", "text": "<PERSON>, Canadian singer-songwriter and guitarist (d. 2004)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bruce <PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bruce_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, French fashion designer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Canadian philosopher and theologian", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher and theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher and theologian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Irish-American actor and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 55th <PERSON><PERSON><PERSON>na (d. 2015)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 55th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 55th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>su"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1953", "text": "<PERSON>, Scottish politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Lord <PERSON>, Scottish judge and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>ool<PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish judge and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>ool<PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish judge and academic", "links": [{"title": "<PERSON>, Lord Wool<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Lord_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Canadian physician and astronaut", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian physician and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian physician and astronaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Soviet gymnast", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American baseball player and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English-born Irish singer-songwriter and actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, English-born Irish singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, English-born Irish singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hazel_O%27Connor"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish footballer and manager (d. 2012)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/P%C3%A1id%C3%AD_%C3%93_S%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A1id%C3%AD_%C3%93_S%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish footballer and manager (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A1id%C3%AD_%C3%93_S%C3%A9"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er\" title=\"<PERSON><PERSON>er\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Winger"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Dutch television host, news anchor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/1956\" title=\"1956\">1956</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hrijver\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch television host, news anchor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1956\" title=\"1956\">1956</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ijver\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch television host, news anchor", "links": [{"title": "1956", "link": "https://wikipedia.org/wiki/1956"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Schrijver"}]}, {"year": "1957", "text": "<PERSON>, American runner", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, 3rd Baron <PERSON>, English politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English politician", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Russian singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, 22nd Baron <PERSON> of Bletso, English lawyer and businessman", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_22nd_Baron_<PERSON>_John_of_Bletso\" title=\"<PERSON>, 22nd Baron <PERSON> John of Bletso\"><PERSON>, 22nd Baron <PERSON> John of Bletso</a>, English lawyer and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_22nd_Baron_<PERSON>_John_of_Bletso\" title=\"<PERSON>, 22nd Baron <PERSON> John of Bletso\"><PERSON>, 22nd Baron <PERSON> John of Bletso</a>, English lawyer and businessman", "links": [{"title": "<PERSON>, 22nd Baron <PERSON> John of Bletso", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_22nd_Baron_<PERSON>_<PERSON>_of_Bletso"}]}, {"year": "1957", "text": "<PERSON>, American ice hockey player and coach (d. 2014)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American baseball player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actress and singer-songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Nauruan politician, Nauruan Speaker of Parliament", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nauruan politician, <a href=\"https://wikipedia.org/wiki/List_of_Speakers_of_the_Parliament_of_Nauru\" class=\"mw-redirect\" title=\"List of Speakers of the Parliament of Nauru\">Nauruan Speaker of Parliament</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nauruan politician, <a href=\"https://wikipedia.org/wiki/List_of_Speakers_of_the_Parliament_of_Nauru\" class=\"mw-redirect\" title=\"List of Speakers of the Parliament of Nauru\">Nauruan Speaker of Parliament</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ragea"}, {"title": "List of Speakers of the Parliament of Nauru", "link": "https://wikipedia.org/wiki/List_of_Speakers_of_the_Parliament_of_Nauru"}]}, {"year": "1960", "text": "<PERSON>, playwright", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, playwright", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(playwright)"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Sri Lankan commander and politician (d. 1998)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(Sri_Lankan_politician)\" title=\"<PERSON><PERSON> (Sri Lankan politician)\"><PERSON><PERSON></a>, Sri Lankan commander and politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(Sri_Lankan_politician)\" title=\"<PERSON><PERSON> (Sri Lankan politician)\"><PERSON><PERSON></a>, Sri Lankan commander and politician (d. 1998)", "links": [{"title": "<PERSON><PERSON> (Sri Lankan politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_(Sri_Lankan_politician)"}]}, {"year": "1961", "text": "<PERSON>, Canadian actor and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American wrestler", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" class=\"mw-redirect\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" class=\"mw-redirect\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>(wrestler)"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, German long jumper", "html": "1962 - <a href=\"https://wikipedia.org/wiki/1962\" title=\"1962\">1962</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1962\" title=\"1962\">1962</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German long jumper", "links": [{"title": "1962", "link": "https://wikipedia.org/wiki/1962"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Anglo-American economist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-American economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-American economist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English theologian and academic", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English theologian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English theologian and academic", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_(theologian)"}]}, {"year": "1964", "text": "<PERSON>, American basketball player and actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and violinist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English comedian, actor, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American bass player, songwriter, author, and activist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player, songwriter, author, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player, songwriter, author, and activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Krist_Novoselic"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Estonian computer scientist, engineer, and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian computer scientist, engineer, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian computer scientist, engineer, and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Thur<PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"Thur<PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, <PERSON> of Trafford, British politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Trafford\" title=\"<PERSON>, <PERSON> of Trafford\"><PERSON>, Baroness <PERSON> of Trafford</a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Trafford\" title=\"<PERSON>, <PERSON> of Trafford\"><PERSON>, Baroness <PERSON> of Trafford</a>, British politician", "links": [{"title": "<PERSON>, <PERSON> of Trafford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Trafford"}]}, {"year": "1968", "text": "<PERSON>, American singer and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American journalist, co-founded The Daily Caller", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, co-founded <i><a href=\"https://wikipedia.org/wiki/The_Daily_Caller\" title=\"The Daily Caller\">The Daily Caller</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, co-founded <i><a href=\"https://wikipedia.org/wiki/The_Daily_Caller\" title=\"The Daily Caller\">The Daily Caller</a></i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Daily Caller", "link": "https://wikipedia.org/wiki/The_Daily_Caller"}]}, {"year": "1969", "text": "<PERSON>, American sprinter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON> (sprinter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sprinter)"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Argentinian tennis player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian singer-songwriter and actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actress)\" title=\"<PERSON> (Australian actress)\"><PERSON></a>, Australian singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actress)\" title=\"<PERSON> (Australian actress)\"><PERSON></a>, Australian singer-songwriter and actress", "links": [{"title": "<PERSON> (Australian actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actress)"}]}, {"year": "1971", "text": "<PERSON>, English rugby league player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, French rugby player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, New Zealand cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actress, reality television personality, and author", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Tori_Spelling\" title=\"<PERSON> Spelling\"><PERSON></a>, American actress, reality television personality, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tori_Spelling\" title=\"Tori Spelling\"><PERSON></a>, American actress, reality television personality, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tori_Spelling"}]}, {"year": "1974", "text": "<PERSON>, Italian singer-songwriter and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter and rapper", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Finnish musician, composer, and vocalist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish musician, composer, and vocalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish musician, composer, and vocalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian triathlete", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian triathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian-Dutch cricketer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Dutch cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Dutch cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, New Zealand actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Icelandic singer-songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Emil%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emil%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emil%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English motorcycle racer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Argentinian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Austrian politician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Spanish tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Llagostera_Vives\" title=\"Nuria Llagostera Vives\"><PERSON><PERSON> Llagostera V<PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Llagostera_Vives\" title=\"Nuria Llagostera Vives\"><PERSON><PERSON> Llagostera V<PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>uria <PERSON>", "link": "https://wikipedia.org/wiki/Nuria_Llagostera_Vives"}]}, {"year": "1981", "text": "<PERSON>, Portuguese footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1981)\" title=\"<PERSON> (footballer, born 1981)\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1981)\" title=\"<PERSON> (footballer, born 1981)\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON> (footballer, born 1981)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1981)"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Polish tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/%C5%81<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%81<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%81<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Argentinian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Dar%C3%ADo_Cvitanich\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dar%C3%ADo_Cvitanich\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dar%C3%ADo_Cvitanich"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Fleischmann\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Fleischmann\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_F<PERSON><PERSON>mann"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player (d. 2011)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, German footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>g"}]}, {"year": "1985", "text": "<PERSON>, Brazilian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Irish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Filipino model and architect", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>hamcey_Supsup\" class=\"mw-redirect\" title=\"<PERSON>hamcey Supsup\"><PERSON><PERSON><PERSON></a>, Filipino model and architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hamcey_Supsup\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Supsup\"><PERSON><PERSON><PERSON></a>, Filipino model and architect", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>up"}]}, {"year": "1987", "text": "<PERSON>-<PERSON>, English race car driver", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English race car driver", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Jes%C3%<PERSON><PERSON>_<PERSON>_(Mexican_footballer)\" title=\"<PERSON><PERSON><PERSON> (Mexican footballer)\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%<PERSON><PERSON>_<PERSON>_(Mexican_footballer)\" title=\"<PERSON><PERSON><PERSON> (Mexican footballer)\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (Mexican footballer)", "link": "https://wikipedia.org/wiki/Jes%C3%<PERSON><PERSON>_<PERSON>_(Mexican_footballer)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Lithuanian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gecevi%C4%8Dius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gecevi%C4%8Dius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Martynas_Gecevi%C4%8Dius"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Estonian tennis player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Jaak_P%C3%B5ldma\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaa<PERSON>_P%C3%B5ldma\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaak_P%C3%B5ldma"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Namibian model", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Behati_Prinsloo\" title=\"Behati Prinsloo\"><PERSON>hat<PERSON> Prinsloo</a>, Namibian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Behati_Prinsloo\" title=\"Behati Prinsloo\">Behat<PERSON> Prinsloo</a>, Namibian model", "links": [{"title": "Behati Prinsloo", "link": "https://wikipedia.org/wiki/Behati_Prinsloo"}]}, {"year": "1990", "text": "<PERSON>, Gibraltarian tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gibraltarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gibraltarian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, English actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Serbian sprinter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Darko_%C5%A0arovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Darko_%C5%A0arovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Darko_%C5%A0arovi%C4%87"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Bulgarian tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American internet celebrity", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American internet celebrity", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American internet celebrity", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American figure skater", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American singer and songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Norwegian biathlete", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B8\" title=\"<PERSON>\"><PERSON></a>, Norwegian biathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_B%C3%B8\" title=\"<PERSON>\"><PERSON></a>, Norwegian biathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_B%C3%B8"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Karol_Mets\" title=\"Karol Mets\"><PERSON>rol <PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karol_Mets\" title=\"Karol Mets\"><PERSON>rol <PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Korean singer-songwriter and actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/I<PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\">I<PERSON></a>, Korean singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON>_(singer)\" title=\"I<PERSON> (singer)\">I<PERSON></a>, Korean singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/I<PERSON>_(singer)"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Liechtenstein tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Liechtenstein tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Liechtenstein tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Australian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Dominican-American baseball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(infielder,_born_2000)\" class=\"mw-redirect\" title=\"<PERSON> (infielder, born 2000)\"><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AD<PERSON>_(infielder,_born_2000)\" class=\"mw-redirect\" title=\"<PERSON> (infielder, born 2000)\"><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON> (infielder, born 2000)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_(infielder,_born_2000)"}]}, {"year": "2002", "text": "<PERSON>, Dutch footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "290", "text": "Emperor <PERSON> of Jin, Chinese emperor (b. 236)", "html": "290 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Jin\" title=\"Emperor <PERSON> of Jin\">Emperor <PERSON> of Jin</a>, Chinese emperor (b. 236)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Jin\" title=\"Emperor <PERSON> of Jin\">Emperor <PERSON> of Jin</a>, Chinese emperor (b. 236)", "links": [{"title": "Emperor <PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Jin"}]}, {"year": "895", "text": "<PERSON><PERSON>, Chinese nobleman", "html": "895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese nobleman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>an"}]}, {"year": "934", "text": "<PERSON><PERSON>, eunuch official of Later Tang", "html": "934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, eunuch official of Later Tang", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, eunuch official of Later Tang", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Men<PERSON>_<PERSON>"}]}, {"year": "995", "text": "<PERSON><PERSON>, Japanese nobleman (b. 953)", "html": "995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_<PERSON>\" title=\"Fujiwara no Michitaka\"><PERSON><PERSON> no <PERSON></a>, Japanese nobleman (b. 953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Mi<PERSON>ka\" title=\"Fujiwara no Michitaka\"><PERSON><PERSON> no <PERSON></a>, Japanese nobleman (b. 953)", "links": [{"title": "<PERSON><PERSON> no <PERSON>", "link": "https://wikipedia.org/wiki/Fujiwara_<PERSON>_<PERSON>ka"}]}, {"year": "1115", "text": "<PERSON> of Arras, Flemish bishop", "html": "1115 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Arras\" title=\"<PERSON> of Arras\"><PERSON> of Arras</a>, Flemish bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Arras\" title=\"<PERSON> of Arras\"><PERSON> of Arras</a>, Flemish bishop", "links": [{"title": "<PERSON> of Arras", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1182", "text": "<PERSON>, Byzantine general (b. 1132)", "html": "1182 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine general (b. 1132)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine general (b. 1132)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1265", "text": "<PERSON>, English-French saint (b. 1165)", "html": "1265 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French saint (b. 1165)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stock\"><PERSON></a>, English-French saint (b. 1165)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1375", "text": "<PERSON>, Chinese military strategist, officer, statesman and poet (b. 1311)", "html": "1375 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese military strategist, officer, statesman and poet (b. 1311)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese military strategist, officer, statesman and poet (b. 1311)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1412", "text": "<PERSON><PERSON>, Duke of Milan (b. 1388)", "html": "1412 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Duke of Milan (b. 1388)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Duke of Milan (b. 1388)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1561", "text": "<PERSON>, Polish noble and statesman (b. 1488)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish noble and statesman (b. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish noble and statesman (b. 1488)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1620", "text": "<PERSON>, English sailor and navigator (b. 1564)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sailor,_born_1564)\" class=\"mw-redirect\" title=\"<PERSON> (sailor, born 1564)\"><PERSON></a>, English sailor and navigator (b. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(sailor,_born_1564)\" class=\"mw-redirect\" title=\"<PERSON> (sailor, born 1564)\"><PERSON></a>, English sailor and navigator (b. 1564)", "links": [{"title": "<PERSON> (sailor, born 1564)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sailor,_born_1564)"}]}, {"year": "1657", "text": "<PERSON>, Polish missionary and martyr (b. 1591)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish missionary and martyr (b. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish missionary and martyr (b. 1591)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1667", "text": "<PERSON>, 4th Earl of Southampton, English politician, Lord High Treasurer (b. 1607)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Southampton\" title=\"<PERSON>, 4th Earl of Southampton\"><PERSON>, 4th Earl of Southampton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_Southampton\" title=\"<PERSON>, 4th Earl of Southampton\"><PERSON>, 4th Earl of Southampton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1607)", "links": [{"title": "<PERSON>, 4th Earl of Southampton", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Southampton"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1669", "text": "<PERSON>, Italian painter and architect, designed the Sant<PERSON> Luca <PERSON> Martina (b. 1596)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and architect, designed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and architect, designed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1596)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1691", "text": "<PERSON>, German-American politician, 8th Colonial Governor of New York (b. 1640)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_New_York\" title=\"List of colonial governors of New York\">Colonial Governor of New York</a> (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_New_York\" title=\"List of colonial governors of New York\">Colonial Governor of New York</a> (b. 1640)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of colonial governors of New York", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_New_York"}]}, {"year": "1696", "text": "<PERSON> of Austria, Queen consort of Spain (b. 1634)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/Mariana_of_Austria\" title=\"Mariana of Austria\"><PERSON> of Austria</a>, Queen consort of Spain (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mariana_of_Austria\" title=\"Mariana of Austria\"><PERSON> of Austria</a>, Queen consort of Spain (b. 1634)", "links": [{"title": "Mariana of Austria", "link": "https://wikipedia.org/wiki/Mariana_of_Austria"}]}, {"year": "1703", "text": "<PERSON>, French author and academic (b. 1628)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and academic (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and academic (b. 1628)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, 4th Earl of Holderness, English politician, Secretary of State for the Southern Department (b. 1718)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Holderness\" title=\"<PERSON>, 4th Earl of Holderness\"><PERSON>, 4th Earl of Holderness</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Holderness\" title=\"<PERSON>, 4th Earl of Holderness\"><PERSON>, 4th Earl of Holderness</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1718)", "links": [{"title": "<PERSON>, 4th Earl of Holderness", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Holderness"}, {"title": "Secretary of State for the Southern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department"}]}, {"year": "1790", "text": "<PERSON>, 2nd Earl of Hardwicke, English politician, Lord Lieutenant of Cambridgeshire (b. 1720)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Hardwicke\" title=\"<PERSON>, 2nd Earl of Hardwicke\"><PERSON>, 2nd Earl of Hardwicke</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Cambridgeshire\" title=\"Lord Lieutenant of Cambridgeshire\">Lord Lieutenant of Cambridgeshire</a> (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Hardwicke\" title=\"<PERSON>, 2nd Earl of Hardwicke\"><PERSON>, 2nd Earl of Hardwicke</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Cambridgeshire\" title=\"Lord Lieutenant of Cambridgeshire\">Lord Lieutenant of Cambridgeshire</a> (b. 1720)", "links": [{"title": "<PERSON>, 2nd Earl of Hardwicke", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Hardwicke"}, {"title": "Lord Lieutenant of Cambridgeshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Cambridgeshire"}]}, {"year": "1818", "text": "<PERSON>, English author and playwright (b. 1775)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and playwright (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and playwright (b. 1775)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1823", "text": "<PERSON>, Scottish courtesan and spy (b. c. 1754)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish courtesan and spy (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1754</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish courtesan and spy (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1754</span>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, French mathematician and physicist (b. 1768)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and physicist (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and physicist (b. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, British priest, geologist and doctoral advisor to <PERSON> (b. 1796)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> He<PERSON>low\"><PERSON></a>, British priest, geologist and doctoral advisor to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>low\"><PERSON></a>, British priest, geologist and doctoral advisor to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, English politician (b. 1796)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American lawyer and politician, 13th Governor of Alabama (b. 1799)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (b. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Alabama", "link": "https://wikipedia.org/wiki/Governor_of_Alabama"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Estonian poet, linguist and theologist (b. 1843)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet, linguist and theologist (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet, linguist and theologist (b. 1843)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Romanian politician, 14th Prime Minister of Romania (b. 1821)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Ion_<PERSON>._Br%C4%83tianu\" title=\"Ion <PERSON>\"><PERSON></a>, Romanian politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ion_<PERSON>._Br%C4%83tianu\" title=\"<PERSON>\"><PERSON></a>, Romanian politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ion_C._Br%C4%83tianu"}, {"title": "Prime Minister of Romania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Romania"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, French Neo-Impressionist painter (b. 1856)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French Neo-Impressionist painter (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French Neo-Impressionist painter (b. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Swiss architect and politician (b. 1849)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss architect and politician (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss architect and politician (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American politician, 22nd United States Vice President (b. 1824)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Vice_President\" class=\"mw-redirect\" title=\"United States Vice President\">United States Vice President</a> (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Vice_President\" class=\"mw-redirect\" title=\"United States Vice President\">United States Vice President</a> (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Vice President", "link": "https://wikipedia.org/wiki/United_States_Vice_President"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON> <PERSON>, the 36th and last Sultan of the Ottoman Empire (b. 1861)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Mehm<PERSON>_VI\" title=\"Mehmed VI\"><PERSON><PERSON><PERSON> VI</a>, the 36th and last Sultan of the Ottoman Empire (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VI\" title=\"Mehmed VI\"><PERSON><PERSON><PERSON> VI</a>, the 36th and last Sultan of the Ottoman Empire (b. 1861)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Greek general and politician (b. 1860)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (b. 1860)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leon<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American engineer, co designed The Golden Gate Bridge (b. 1870)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, American engineer, co designed The <a href=\"https://wikipedia.org/wiki/Golden_Gate_Bridge\" title=\"Golden Gate Bridge\">Golden Gate Bridge</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, American engineer, co designed The <a href=\"https://wikipedia.org/wiki/Golden_Gate_Bridge\" title=\"Golden Gate Bridge\">Golden Gate Bridge</a> (b. 1870)", "links": [{"title": "<PERSON> (engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_(engineer)"}, {"title": "Golden Gate Bridge", "link": "https://wikipedia.org/wiki/Golden_Gate_Bridge"}]}, {"year": "1943", "text": "<PERSON>, German psychiatrist and academic (b. 1865)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and academic (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and academic (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_<PERSON>"}]}, {"year": "1943", "text": " <PERSON><PERSON>, black labrador retriever belonging to Wing Commander <PERSON> of the Royal Air Force, and the mascot of No. 617 Squadron.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Nigger_(dog)\" title=\"Nigger (dog)\"> <PERSON><PERSON></a>, black labrador retriever belonging to Wing Commander <PERSON> of the Royal Air Force, and the mascot of No. 617 Squadron.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ni<PERSON>_(dog)\" title=\"Nigger (dog)\"> <PERSON><PERSON></a>, black labrador retriever belonging to Wing Commander <PERSON> of the Royal Air Force, and the mascot of No. 617 Squadron.", "links": [{"title": "<PERSON><PERSON> (dog)", "link": "https://wikipedia.org/wiki/Nigger_(dog)"}]}, {"year": "1944", "text": "<PERSON>, American journalist, author, and playwright (b. 1866)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and playwright (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and playwright (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Aromanian activist, physician and politician (b. 1873)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Filip_<PERSON>%C8%99ea\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Aromanian activist, physician and politician (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Filip_Mi%C8%99ea\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Aromanian activist, physician and politician (b. 1873)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Filip_Mi%C8%99ea"}]}, {"year": "1946", "text": "<PERSON>, German chemist and businessman (b. 1890)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and businessman (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and businessman (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English biochemist and academic, Nobel Prize laureate (b. 1861)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Finnish politician (b. 1880)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Chinese general (b. 1903)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Belgian guitarist and composer  (b. 1910)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Re<PERSON>hardt\"><PERSON><PERSON><PERSON></a>, Belgian guitarist and composer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Re<PERSON>hardt\"><PERSON><PERSON><PERSON></a>, Belgian guitarist and composer (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Austrian conductor and manager (b. 1893)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian conductor and manager (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian conductor and manager (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American novelist, screenwriter, and critic(b. 1909)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter, and critic(b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter, and critic(b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American race car driver (b. 1921)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON> <PERSON><PERSON>, American candy-maker and businessman, created <PERSON>'s Peanut Butter Cups  (b. 1876)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Reese\"><PERSON><PERSON> <PERSON><PERSON></a>, American candy-maker and businessman, created <a href=\"https://wikipedia.org/wiki/Reese%27s_Peanut_Butter_Cups\" title=\"Reese's Peanut Butter Cups\"><PERSON>'s Peanut Butter Cups</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Reese\"><PERSON><PERSON> <PERSON><PERSON></a>, American candy-maker and businessman, created <a href=\"https://wikipedia.org/wiki/Reese%27s_Peanut_Butter_Cups\" title=\"Reese's Peanut Butter Cups\">Reese's Peanut Butter Cups</a> (b. 1876)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Reese's Peanut Butter Cups", "link": "https://wikipedia.org/wiki/Reese%27s_Peanut_Butter_Cups"}]}, {"year": "1957", "text": "<PERSON>, American federal agent (b. 1903)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American federal agent (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> N<PERSON>\"><PERSON></a>, American federal agent (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ess"}]}, {"year": "1961", "text": "<PERSON>, American lawyer and jurist (b. 1881)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Malian politician, 1st President of Mali (b. 1915)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Modibo_Ke%C3%AFta\" title=\"Modibo <PERSON>\"><PERSON><PERSON><PERSON></a>, Malian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Mali\" class=\"mw-redirect\" title=\"President of Mali\">President of Mali</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Modibo_Ke%C3%AFta\" title=\"Modi<PERSON>\"><PERSON><PERSON><PERSON></a>, Malian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Mali\" class=\"mw-redirect\" title=\"President of Mali\">President of Mali</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Modibo_Ke%C3%AFta"}, {"title": "President of Mali", "link": "https://wikipedia.org/wiki/President_of_Mali"}]}, {"year": "1979", "text": "<PERSON><PERSON> <PERSON>, American union leader and activist (b. 1889)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American union leader and activist (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American union leader and activist (b. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American pianist, composer, and bandleader (b. 1922)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, German physician and academic (b. 1905)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and academic (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and academic (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor, comedian, and screenwriter (b. 1949)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American playwright, screenwriter, novelist, and short story writer (b. 1913)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, screenwriter, novelist, and short story writer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, screenwriter, novelist, and short story writer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress (b. 1902)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1902)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Iranian poet and songwriter (b. 1939)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian poet and songwriter (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian poet and songwriter (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American singer, dancer, and actor (b. 1925)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American singer, dancer, and actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American singer, dancer, and actor (b. 1925)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1990", "text": "<PERSON>, American puppeteer, director, producer, and screenwriter, creator of The Muppets (b. 1936)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer, director, producer, and screenwriter, creator of <a href=\"https://wikipedia.org/wiki/The_Muppets\" title=\"The Muppets\">The Muppets</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer, director, producer, and screenwriter, creator of <a href=\"https://wikipedia.org/wiki/The_Muppets\" title=\"The Muppets\">The Muppets</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Muppets", "link": "https://wikipedia.org/wiki/The_Muppets"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American singer-songwriter and pianist (b. 1938)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marv_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, French actor (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American admiral (b. 1939)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American diplomat (b. 1903)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Elbridge_Durbrow\" title=\"Elbridge Durbrow\"><PERSON><PERSON></a>, American diplomat (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elbridge_Durbrow\" title=\"Elbridge Durbrow\"><PERSON><PERSON></a>, American diplomat (b. 1903)", "links": [{"title": "Elbridge Durbrow", "link": "https://wikipedia.org/wiki/Elbridge_<PERSON>row"}]}, {"year": "2002", "text": "<PERSON>, Australian soldier (b. 1899)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American lawyer and sports agent, founded IMG (b. 1930)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and sports agent, founded <a href=\"https://wikipedia.org/wiki/IMG_(company)\" title=\"IMG (company)\">IMG</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and sports agent, founded <a href=\"https://wikipedia.org/wiki/IMG_(company)\" title=\"IMG (company)\">IMG</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "IMG (company)", "link": "https://wikipedia.org/wiki/IMG_(company)"}]}, {"year": "2005", "text": "<PERSON>, American general (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American winemaker, co-founded the Opus One Winery (b. 1913)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American winemaker, co-founded the <a href=\"https://wikipedia.org/wiki/Opus_One_Winery\" title=\"Opus One Winery\">Opus One Winery</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American winemaker, co-founded the <a href=\"https://wikipedia.org/wiki/Opus_One_Winery\" title=\"Opus One Winery\">Opus One Winery</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Opus One Winery", "link": "https://wikipedia.org/wiki/Opus_One_Winery"}]}, {"year": "2010", "text": "<PERSON>, American singer-songwriter and producer (b. 1942)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American pianist, composer, and bandleader (b. 1918)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English author (b. 1917)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Australian footballer and coach (b. 1928)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rules_footballer)\" title=\"<PERSON> (Australian rules footballer)\"><PERSON></a>, Australian footballer and coach (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rules_footballer)\" title=\"<PERSON> (Australian rules footballer)\"><PERSON></a>, Australian footballer and coach (b. 1928)", "links": [{"title": "<PERSON> (Australian rules footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rules_footballer)"}]}, {"year": "2011", "text": "<PERSON>, English actor (b. 1932)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Japanese actor (b. 1934)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American author and academic (b. 1952)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American soldier and politician (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Filipino-American illustrator (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American illustrator (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American illustrator (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player (b. 1956)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American politician (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian football player (b. 1957)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Puerto Rican-American baseball player (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frankie_Libr%C3%A1n"}]}, {"year": "2013", "text": "<PERSON>, Swiss physicist and academic, Nobel Prize laureate (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2013", "text": "<PERSON>, British actor and comedian (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor and comedian (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor and comedian (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American race car driver (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American author and illustrator (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Zimbabwean-South African cricketer (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-South African cricketer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-South African cricketer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Italian cyclist (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Vito_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Favero\"><PERSON><PERSON></a>, Italian cyclist (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON>_<PERSON>avero"}]}, {"year": "2014", "text": "<PERSON>, American baseball player and manager (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American anthropologist and author (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, American director and producer (b. 1973)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bhargava\"><PERSON><PERSON><PERSON></a>, American director and producer (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bhargava\"><PERSON><PERSON><PERSON></a>, American director and producer (b. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>va"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Israeli rabbi and author (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli rabbi and author (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli rabbi and author (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Scottish Gaelic singer (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish Gaelic singer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish Gaelic singer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Dutch politician (b. 1937)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lauw\" title=\"<PERSON><PERSON>lauw\"><PERSON><PERSON></a>, Dutch politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lauw\" title=\"<PERSON><PERSON>lau<PERSON>\"><PERSON><PERSON></a>, Dutch politician (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lauw"}]}, {"year": "2019", "text": "<PERSON>, Australian politician, 23rd Prime Minister of Australia (b. 1929)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "2019", "text": "<PERSON><PERSON> <PERSON><PERSON>, Chinese-American architect (b. 1917)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Chinese-American architect (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON>ei\"><PERSON><PERSON> <PERSON><PERSON></a>, Chinese-American architect (b. 1917)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Brazilian lawyer, politician (b. 1980)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer, politician (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer, politician (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, American long-distance runner (b. 1932)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(runner)\" title=\"<PERSON><PERSON> (runner)\"><PERSON><PERSON></a>, American long-distance runner (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(runner)\" title=\"<PERSON><PERSON> (runner)\"><PERSON><PERSON></a>, American long-distance runner (b. 1932)", "links": [{"title": "<PERSON><PERSON> (runner)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(runner)"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, American actor (b. 1932)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American public speaker and businessman (b. 1958)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American public speaker and businessman (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American public speaker and businessman (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}