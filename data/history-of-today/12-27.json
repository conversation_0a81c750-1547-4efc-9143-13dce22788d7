{"date": "December 27", "url": "https://wikipedia.org/wiki/December_27", "data": {"Events": [{"year": "537", "text": "The second Hagia Sophia in Constantinople is consecrated.", "html": "537 - The second <a href=\"https://wikipedia.org/wiki/Hagia_Sophia\" title=\"Hagia Sophia\">Hagia Sophia</a> in Constantinople is consecrated.", "no_year_html": "The second <a href=\"https://wikipedia.org/wiki/Hagia_Sophia\" title=\"Hagia Sophia\">Hagia Sophia</a> in Constantinople is consecrated.", "links": [{"title": "Hagia Sophia", "link": "https://wikipedia.org/wiki/Hagia_Sophia"}]}, {"year": "1512", "text": "The Spanish Crown issues the Laws of Burgos, governing the conduct of settlers with regard to native Indians in the New World.", "html": "1512 - The Spanish Crown issues the <a href=\"https://wikipedia.org/wiki/Laws_of_Burgos\" title=\"Laws of Burgos\">Laws of Burgos</a>, governing the conduct of settlers with regard to <a href=\"https://wikipedia.org/wiki/Indigenous_peoples_of_the_Americas\" title=\"Indigenous peoples of the Americas\">native Indians</a> in the <a href=\"https://wikipedia.org/wiki/New_World\" title=\"New World\">New World</a>.", "no_year_html": "The Spanish Crown issues the <a href=\"https://wikipedia.org/wiki/Laws_of_Burgos\" title=\"Laws of Burgos\">Laws of Burgos</a>, governing the conduct of settlers with regard to <a href=\"https://wikipedia.org/wiki/Indigenous_peoples_of_the_Americas\" title=\"Indigenous peoples of the Americas\">native Indians</a> in the <a href=\"https://wikipedia.org/wiki/New_World\" title=\"New World\">New World</a>.", "links": [{"title": "Laws of Burgos", "link": "https://wikipedia.org/wiki/Laws_of_Burgos"}, {"title": "Indigenous peoples of the Americas", "link": "https://wikipedia.org/wiki/Indigenous_peoples_of_the_Americas"}, {"title": "New World", "link": "https://wikipedia.org/wiki/New_World"}]}, {"year": "1521", "text": "The Zwickau prophets arrive in Wittenberg, disturbing the peace and preaching the Apocalypse.", "html": "1521 - The <a href=\"https://wikipedia.org/wiki/Zwickau_prophets\" title=\"Zwickau prophets\">Zwickau prophets</a> arrive in <a href=\"https://wikipedia.org/wiki/Wittenberg\" title=\"Wittenberg\"><PERSON><PERSON><PERSON></a>, disturbing the peace and preaching the <a href=\"https://wikipedia.org/wiki/Apocalypse\" title=\"Apocalypse\">Apocalypse</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Zwickau_prophets\" title=\"Zwickau prophets\">Zwickau prophets</a> arrive in <a href=\"https://wikipedia.org/wiki/Wittenberg\" title=\"Wittenberg\"><PERSON><PERSON><PERSON></a>, disturbing the peace and preaching the <a href=\"https://wikipedia.org/wiki/Apocalypse\" title=\"Apocalypse\">Apocalypse</a>.", "links": [{"title": "Zwickau prophets", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_prophets"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Witte<PERSON>"}, {"title": "Apocalypse", "link": "https://wikipedia.org/wiki/Apocalypse"}]}, {"year": "1655", "text": "Second Northern War/the Deluge: Monks at the Jasna Góra Monastery in Częstochowa are successful in fending off a month-long siege.", "html": "1655 - <a href=\"https://wikipedia.org/wiki/Second_Northern_War\" class=\"mw-redirect\" title=\"Second Northern War\">Second Northern War</a>/the <a href=\"https://wikipedia.org/wiki/Deluge_(history)\" title=\"Deluge (history)\">Deluge</a>: Monks at the <a href=\"https://wikipedia.org/wiki/Jasna_G%C3%B3ra_Monastery\" title=\"Jasna Góra Monastery\">Jasna Góra Monastery</a> in <a href=\"https://wikipedia.org/wiki/Cz%C4%99stochowa\" title=\"Częstochowa\">Częstochowa</a> are successful in fending off a <a href=\"https://wikipedia.org/wiki/Siege_of_Jasna_G%C3%B3ra\" title=\"Siege of Jasna Góra\">month-long siege</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Northern_War\" class=\"mw-redirect\" title=\"Second Northern War\">Second Northern War</a>/the <a href=\"https://wikipedia.org/wiki/Deluge_(history)\" title=\"Deluge (history)\">Deluge</a>: Monks at the <a href=\"https://wikipedia.org/wiki/Jasna_G%C3%B3ra_Monastery\" title=\"Jasna Góra Monastery\">Jasna Góra Monastery</a> in <a href=\"https://wikipedia.org/wiki/Cz%C4%99stochowa\" title=\"Częstochowa\">Częstochowa</a> are successful in fending off a <a href=\"https://wikipedia.org/wiki/Siege_of_Jasna_G%C3%B3ra\" title=\"Siege of Jasna Góra\">month-long siege</a>.", "links": [{"title": "Second Northern War", "link": "https://wikipedia.org/wiki/Second_Northern_War"}, {"title": "Deluge (history)", "link": "https://wikipedia.org/wiki/Deluge_(history)"}, {"title": "Jasna Góra Monastery", "link": "https://wikipedia.org/wiki/Jasna_G%C3%B3ra_Monastery"}, {"title": "Częstochowa", "link": "https://wikipedia.org/wiki/Cz%C4%99stochowa"}, {"title": "Siege of Jasna Góra", "link": "https://wikipedia.org/wiki/Siege_of_Jasna_G%C3%B3ra"}]}, {"year": "1657", "text": "The Flushing Remonstrance articulates for the first time in North American history that freedom of religion is a fundamental right.", "html": "1657 - The <a href=\"https://wikipedia.org/wiki/Flushing_Remonstrance\" title=\"Flushing Remonstrance\">Flushing Remonstrance</a> articulates for the first time in North American history that <a href=\"https://wikipedia.org/wiki/Freedom_of_religion\" title=\"Freedom of religion\">freedom of religion</a> is a fundamental right.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Flushing_Remonstrance\" title=\"Flushing Remonstrance\">Flushing Remonstrance</a> articulates for the first time in North American history that <a href=\"https://wikipedia.org/wiki/Freedom_of_religion\" title=\"Freedom of religion\">freedom of religion</a> is a fundamental right.", "links": [{"title": "Flushing Remonstrance", "link": "https://wikipedia.org/wiki/Flushing_Remonstrance"}, {"title": "Freedom of religion", "link": "https://wikipedia.org/wiki/Freedom_of_religion"}]}, {"year": "1703", "text": "Portugal and England sign the Methuen Treaty which allows Portugal to export wines to England on favorable trade terms.", "html": "1703 - Portugal and England sign the <a href=\"https://wikipedia.org/wiki/Methuen_Treaty\" title=\"Methuen Treaty\">Methuen Treaty</a> which allows Portugal to export <a href=\"https://wikipedia.org/wiki/Wine\" title=\"Wine\">wines</a> to England on favorable trade terms.", "no_year_html": "Portugal and England sign the <a href=\"https://wikipedia.org/wiki/Methuen_Treaty\" title=\"Methuen Treaty\">Methuen Treaty</a> which allows Portugal to export <a href=\"https://wikipedia.org/wiki/Wine\" title=\"Wine\">wines</a> to England on favorable trade terms.", "links": [{"title": "Methuen Treaty", "link": "https://wikipedia.org/wiki/Methuen_Treaty"}, {"title": "Wine", "link": "https://wikipedia.org/wiki/Wine"}]}, {"year": "1814", "text": "War of 1812: The destruction of the schooner USS Carolina brings to an end Commodore <PERSON>'s makeshift fleet, which fought a series of delaying actions that contributed to <PERSON>'s victory at the Battle of New Orleans.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: The destruction of the schooner <a href=\"https://wikipedia.org/wiki/USS_Carolina_(1812)\" title=\"USS Carolina (1812)\">USS <i>Carolina</i></a> brings to an end Commodore <a href=\"https://wikipedia.org/wiki/<PERSON>(naval_officer)\" title=\"<PERSON> (naval officer)\"><PERSON></a>'s makeshift fleet, which fought a series of delaying actions that contributed to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_New_Orleans\" title=\"Battle of New Orleans\">Battle of New Orleans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: The destruction of the schooner <a href=\"https://wikipedia.org/wiki/USS_Carolina_(1812)\" title=\"USS Carolina (1812)\">USS <i>Carolina</i></a> brings to an end Commodore <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(naval_officer)\" title=\"<PERSON> (naval officer)\"><PERSON></a>'s makeshift fleet, which fought a series of delaying actions that contributed to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_New_Orleans\" title=\"Battle of New Orleans\">Battle of New Orleans</a>.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "USS Carolina (1812)", "link": "https://wikipedia.org/wiki/USS_Carolina_(1812)"}, {"title": "<PERSON> (naval officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(naval_officer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of New Orleans", "link": "https://wikipedia.org/wiki/Battle_of_New_Orleans"}]}, {"year": "1831", "text": "<PERSON> embarks on his journey aboard HMS Beagle, during which he will begin to formulate his theory of evolution.", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> embarks on <a href=\"https://wikipedia.org/wiki/Second_voyage_of_HMS_Beagle\" title=\"Second voyage of HMS Beagle\">his journey</a> aboard <a href=\"https://wikipedia.org/wiki/HMS_Beagle\" title=\"HMS Beagle\">HMS <i><PERSON>agle</i></a>, during which he will begin to formulate his theory of <a href=\"https://wikipedia.org/wiki/Evolution\" title=\"Evolution\">evolution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> embarks on <a href=\"https://wikipedia.org/wiki/Second_voyage_of_HMS_Beagle\" title=\"Second voyage of HMS Beagle\">his journey</a> aboard <a href=\"https://wikipedia.org/wiki/HMS_Beagle\" title=\"HMS Beagle\">HMS <i><PERSON>agle</i></a>, during which he will begin to formulate his theory of <a href=\"https://wikipedia.org/wiki/Evolution\" title=\"Evolution\">evolution</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Second voyage of HMS Beagle", "link": "https://wikipedia.org/wiki/Second_voyage_of_HMS_Beagle"}, {"title": "HMS Beagle", "link": "https://wikipedia.org/wiki/HMS_Beagle"}, {"title": "Evolution", "link": "https://wikipedia.org/wiki/Evolution"}]}, {"year": "1836", "text": "The worst ever avalanche in England occurs at Lewes, Sussex, killing eight people.", "html": "1836 - The worst ever <a href=\"https://wikipedia.org/wiki/Lewes_avalanche\" title=\"Lewes avalanche\">avalanche</a> in England occurs at <a href=\"https://wikipedia.org/wiki/Lewes\" title=\"Lewes\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sussex\" title=\"Sussex\">Sussex</a>, killing eight people.", "no_year_html": "The worst ever <a href=\"https://wikipedia.org/wiki/Lewes_avalanche\" title=\"Lewes avalanche\">avalanche</a> in England occurs at <a href=\"https://wikipedia.org/wiki/Le<PERSON>\" title=\"Lewes\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sussex\" title=\"Sussex\">Sussex</a>, killing eight people.", "links": [{"title": "Lewes avalanche", "link": "https://wikipedia.org/wiki/Lewes_avalanche"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le<PERSON>"}, {"title": "Sussex", "link": "https://wikipedia.org/wiki/Sussex"}]}, {"year": "1845", "text": "Ether anesthetic is used for childbirth for the first time by Dr. <PERSON> in Jefferson, Georgia.", "html": "1845 - <a href=\"https://wikipedia.org/wiki/Diethyl_ether\" title=\"Diethyl ether\">Ether</a> <a href=\"https://wikipedia.org/wiki/Anesthetic\" title=\"Anesthetic\">anesthetic</a> is used for childbirth for the first time by Dr<PERSON> in <a href=\"https://wikipedia.org/wiki/Jefferson,_Georgia\" title=\"Jefferson, Georgia\">Jefferson</a>, Georgia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diethyl_ether\" title=\"Diethyl ether\"><PERSON>ther</a> <a href=\"https://wikipedia.org/wiki/Anesthetic\" title=\"Anesthetic\">anesthetic</a> is used for childbirth for the first time by Dr<PERSON> in <a href=\"https://wikipedia.org/wiki/Jefferson,_Georgia\" title=\"Jefferson, Georgia\">Jefferson</a>, Georgia.", "links": [{"title": "Diethyl ether", "link": "https://wikipedia.org/wiki/Diethyl_ether"}, {"title": "Anesthetic", "link": "https://wikipedia.org/wiki/Anesthetic"}, {"title": "Jefferson, Georgia", "link": "https://wikipedia.org/wiki/Jefferson,_Georgia"}]}, {"year": "1845", "text": "Having coined the phrase \"manifest destiny\" the previous July, journalist <PERSON> argued in his newspaper New York Morning News that the United States had the right to claim the entire Oregon Country.", "html": "1845 - Having coined the phrase \"<a href=\"https://wikipedia.org/wiki/Manifest_destiny\" title=\"Manifest destiny\">manifest destiny</a>\" the previous July, journalist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_O%27Sullivan\" title=\"<PERSON>\"><PERSON></a> argued in his newspaper <i>New York Morning News</i> that the United States had the right to claim the entire <a href=\"https://wikipedia.org/wiki/Oregon_Country\" title=\"Oregon Country\">Oregon Country</a>.", "no_year_html": "Having coined the phrase \"<a href=\"https://wikipedia.org/wiki/Manifest_destiny\" title=\"Manifest destiny\">manifest destiny</a>\" the previous July, journalist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Sullivan\" title=\"<PERSON>\"><PERSON></a> argued in his newspaper <i>New York Morning News</i> that the United States had the right to claim the entire <a href=\"https://wikipedia.org/wiki/Oregon_Country\" title=\"Oregon Country\">Oregon Country</a>.", "links": [{"title": "Manifest destiny", "link": "https://wikipedia.org/wiki/Manifest_destiny"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._O%27Sullivan"}, {"title": "Oregon Country", "link": "https://wikipedia.org/wiki/Oregon_Country"}]}, {"year": "1911", "text": "\"Jana Gana Mana\", the national anthem of India, is first sung in the Calcutta Session of the Indian National Congress.", "html": "1911 - \"<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>\", the <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a> of India, is first sung in the Calcutta Session of the <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a>.", "no_year_html": "\"<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>\", the <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a> of India, is first sung in the Calcutta Session of the <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>a"}, {"title": "National anthem", "link": "https://wikipedia.org/wiki/National_anthem"}, {"title": "Indian National Congress", "link": "https://wikipedia.org/wiki/Indian_National_Congress"}]}, {"year": "1918", "text": "The Great Poland Uprising against the Germans begins.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Greater_Poland_Uprising_(1918%E2%80%9319)\" class=\"mw-redirect\" title=\"Greater Poland Uprising (1918-19)\">Great Poland Uprising</a> against the Germans begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Greater_Poland_Uprising_(1918%E2%80%9319)\" class=\"mw-redirect\" title=\"Greater Poland Uprising (1918-19)\">Great Poland Uprising</a> against the Germans begins.", "links": [{"title": "Greater Poland Uprising (1918-19)", "link": "https://wikipedia.org/wiki/Greater_Poland_Uprising_(1918%E2%80%9319)"}]}, {"year": "1918", "text": "Ukrainian War of Independence: The Revolutionary Insurgent Army of Ukraine occupies Yekaterinoslav and seizes seven airplanes from the UPRAF, establishing an Insurgent Air Fleet.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a> occupies <a href=\"https://wikipedia.org/wiki/Yekaterinoslav\" class=\"mw-redirect\" title=\"Yekaterinoslav\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> and seizes seven airplanes from the <a href=\"https://wikipedia.org/wiki/Ukrainian_People%27s_Republic_Air_Fleet\" title=\"Ukrainian People's Republic Air Fleet\">UPRAF</a>, establishing an <a href=\"https://wikipedia.org/wiki/Air_Fleet_of_the_Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Air Fleet of the Revolutionary Insurgent Army of Ukraine\">Insurgent Air Fleet</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a> occupies <a href=\"https://wikipedia.org/wiki/Yekaterinoslav\" class=\"mw-redirect\" title=\"Yekaterinoslav\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> and seizes seven airplanes from the <a href=\"https://wikipedia.org/wiki/Ukrainian_People%27s_Republic_Air_Fleet\" title=\"Ukrainian People's Republic Air Fleet\">UPRAF</a>, establishing an <a href=\"https://wikipedia.org/wiki/Air_Fleet_of_the_Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Air Fleet of the Revolutionary Insurgent Army of Ukraine\">Insurgent Air Fleet</a>.", "links": [{"title": "Ukrainian War of Independence", "link": "https://wikipedia.org/wiki/Ukrainian_War_of_Independence"}, {"title": "Revolutionary Insurgent Army of Ukraine", "link": "https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yekaterinoslav"}, {"title": "Ukrainian People's Republic Air Fleet", "link": "https://wikipedia.org/wiki/Ukrainian_People%27s_Republic_Air_Fleet"}, {"title": "Air Fleet of the Revolutionary Insurgent Army of Ukraine", "link": "https://wikipedia.org/wiki/Air_Fleet_of_the_Revolutionary_Insurgent_Army_of_Ukraine"}]}, {"year": "1922", "text": "Japanese aircraft carrier Hōsh<PERSON> becomes the first purpose-built aircraft carrier to be commissioned in the world.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_H%C5%8Dsh%C5%8D\" title=\"Japanese aircraft carrier Hōshō\">Japanese aircraft carrier <i>Hōshō</i></a> becomes the first purpose-built <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> to be commissioned in the world.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_H%C5%8Dsh%C5%8D\" title=\"Japanese aircraft carrier Hōshō\">Japanese aircraft carrier <i>Hōshō</i></a> becomes the first purpose-built <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> to be commissioned in the world.", "links": [{"title": "Japanese aircraft carrier Hōshō", "link": "https://wikipedia.org/wiki/Japanese_aircraft_carrier_H%C5%8Dsh%C5%8D"}, {"title": "Aircraft carrier", "link": "https://wikipedia.org/wiki/Aircraft_carrier"}]}, {"year": "1927", "text": "<PERSON> and <PERSON><PERSON>'s musical play Show Boat, considered to be the first true American musical play, opens at the Ziegfeld Theatre on Broadway.", "html": "1927 - <PERSON> and <PERSON>'s musical play <i><a href=\"https://wikipedia.org/wiki/Show_Boat\" title=\"Show Boat\">Show Boat</a></i>, considered to be the first true American musical play, opens at the <a href=\"https://wikipedia.org/wiki/Ziegfeld_Theatre_(1927)\" title=\"Ziegfeld Theatre (1927)\">Ziegfeld Theatre</a> on <a href=\"https://wikipedia.org/wiki/Broadway_theatre\" title=\"Broadway theatre\">Broadway</a>.", "no_year_html": "<PERSON> and <PERSON>'s musical play <i><a href=\"https://wikipedia.org/wiki/Show_Boat\" title=\"Show Boat\">Show Boat</a></i>, considered to be the first true American musical play, opens at the <a href=\"https://wikipedia.org/wiki/Ziegfeld_Theatre_(1927)\" title=\"Ziegfeld Theatre (1927)\">Ziegfeld Theatre</a> on <a href=\"https://wikipedia.org/wiki/Broadway_theatre\" title=\"Broadway theatre\">Broadway</a>.", "links": [{"title": "Show Boat", "link": "https://wikipedia.org/wiki/Show_Boat"}, {"title": "Ziegfeld Theatre (1927)", "link": "https://wikipedia.org/wiki/Ziegfeld_Theatre_(1927)"}, {"title": "Broadway theatre", "link": "https://wikipedia.org/wiki/Broadway_theatre"}]}, {"year": "1929", "text": "Soviet General Secretary <PERSON> orders the \"liquidation of the kulaks as a class\".", "html": "1929 - <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">Soviet General Secretary</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the \"<a href=\"https://wikipedia.org/wiki/Dekulakization\" title=\"Dekulakization\">liquidation of the kulaks as a class</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">Soviet General Secretary</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the \"<a href=\"https://wikipedia.org/wiki/Dekulakization\" title=\"Dekulakization\">liquidation of the kulaks as a class</a>\".", "links": [{"title": "General Secretary of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Dekulakization", "link": "https://wikipedia.org/wiki/Dekulakization"}]}, {"year": "1932", "text": "Radio City Music Hall, \"Showplace of the Nation\", opens in New York City.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Radio_City_Music_Hall\" title=\"Radio City Music Hall\">Radio City Music Hall</a>, \"Showplace of the Nation\", opens in New York City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Radio_City_Music_Hall\" title=\"Radio City Music Hall\">Radio City Music Hall</a>, \"Showplace of the Nation\", opens in New York City.", "links": [{"title": "Radio City Music Hall", "link": "https://wikipedia.org/wiki/Radio_City_Music_Hall"}]}, {"year": "1935", "text": "<PERSON> is ordained as the first female rabbi in the history of Judaism.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is ordained as the first female <a href=\"https://wikipedia.org/wiki/Rabbi\" title=\"Rabbi\">rabbi</a> in the history of <a href=\"https://wikipedia.org/wiki/Judaism\" title=\"Judaism\">Judaism</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is ordained as the first female <a href=\"https://wikipedia.org/wiki/Rabbi\" title=\"Rabbi\">rabbi</a> in the history of <a href=\"https://wikipedia.org/wiki/Judaism\" title=\"Judaism\">Judaism</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Rabbi", "link": "https://wikipedia.org/wiki/Rabbi"}, {"title": "Judaism", "link": "https://wikipedia.org/wiki/Judaism"}]}, {"year": "1939", "text": "The 7.8 Mw  Erzincan earthquake shakes eastern Turkey with a maximum Mercalli intensity of XI (Extreme). At least 32,700 people were killed.", "html": "1939 - The 7.8 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1939_Erzincan_earthquake\" title=\"1939 Erzincan earthquake\">Erzincan earthquake</a> shakes eastern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>). At least 32,700 people were killed.", "no_year_html": "The 7.8 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1939_Erzincan_earthquake\" title=\"1939 Erzincan earthquake\">Erzincan earthquake</a> shakes eastern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>). At least 32,700 people were killed.", "links": [{"title": "1939 Erzincan earthquake", "link": "https://wikipedia.org/wiki/1939_Erzincan_earthquake"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1939", "text": "Winter War: Finland holds off a Soviet attack in the Battle of Kelja.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a>: Finland holds off a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> attack in the <a href=\"https://wikipedia.org/wiki/Battle_of_Kelja\" title=\"Battle of Kelja\">Battle of Kelja</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a>: Finland holds off a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> attack in the <a href=\"https://wikipedia.org/wiki/Battle_of_Kelja\" title=\"Battle of Kelja\">Battle of Kelja</a>.", "links": [{"title": "Winter War", "link": "https://wikipedia.org/wiki/Winter_War"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Battle of Kelja", "link": "https://wikipedia.org/wiki/Battle_of_Kelja"}]}, {"year": "1945", "text": "The International Monetary Fund is created with the signing of an agreement by 29 nations.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/International_Monetary_Fund\" title=\"International Monetary Fund\">International Monetary Fund</a> is created with the signing of an agreement by 29 nations.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Monetary_Fund\" title=\"International Monetary Fund\">International Monetary Fund</a> is created with the signing of an agreement by 29 nations.", "links": [{"title": "International Monetary Fund", "link": "https://wikipedia.org/wiki/International_Monetary_Fund"}]}, {"year": "1949", "text": "Indonesian National Revolution: The Netherlands officially recognizes Indonesian independence. End of the Dutch East Indies.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Indonesian_National_Revolution\" title=\"Indonesian National Revolution\">Indonesian National Revolution</a>: The Netherlands officially recognizes <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> independence. End of the <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indonesian_National_Revolution\" title=\"Indonesian National Revolution\">Indonesian National Revolution</a>: The Netherlands officially recognizes <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> independence. End of the <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>.", "links": [{"title": "Indonesian National Revolution", "link": "https://wikipedia.org/wiki/Indonesian_National_Revolution"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Dutch East Indies", "link": "https://wikipedia.org/wiki/Dutch_East_Indies"}]}, {"year": "1966", "text": "The Cave of Swallows, the largest known cave shaft in the world, is discovered in Aquismón, San Luis Potosí, Mexico.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Cave_of_Swallows\" title=\"Cave of Swallows\">Cave of Swallows</a>, the largest known cave shaft in the world, is discovered in <a href=\"https://wikipedia.org/wiki/Aquism%C3%B3n\" title=\"Aquismón\">A<PERSON>món</a>, <a href=\"https://wikipedia.org/wiki/San_Luis_Potos%C3%AD\" title=\"San Luis Potosí\">San Luis Potosí</a>, Mexico.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cave_of_Swallows\" title=\"Cave of Swallows\">Cave of Swallows</a>, the largest known cave shaft in the world, is discovered in <a href=\"https://wikipedia.org/wiki/Aquism%C3%B3n\" title=\"Aquism<PERSON>\">A<PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/San_Luis_Potos%C3%AD\" title=\"San Luis Potosí\">San Luis Potosí</a>, Mexico.", "links": [{"title": "Cave of Swallows", "link": "https://wikipedia.org/wiki/Cave_of_Swallows"}, {"title": "Aquismón", "link": "https://wikipedia.org/wiki/Aquism%C3%B3n"}, {"title": "San Luis Potosí", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%AD"}]}, {"year": "1968", "text": "Apollo program: Apollo 8 splashes down in the Pacific Ocean, ending the first orbital crewed mission to the Moon.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_8\" title=\"Apollo 8\">Apollo 8</a> splashes down in the Pacific Ocean, ending the first orbital crewed mission to the Moon.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_8\" title=\"Apollo 8\">Apollo 8</a> splashes down in the Pacific Ocean, ending the first orbital crewed mission to the Moon.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 8", "link": "https://wikipedia.org/wiki/Apollo_8"}]}, {"year": "1968", "text": "North Central Airlines Flight 458 crashes at O'Hare International Airport, killing 28.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/North_Central_Airlines_Flight_458\" title=\"North Central Airlines Flight 458\">North Central Airlines Flight 458</a> crashes at <a href=\"https://wikipedia.org/wiki/O%27Hare_International_Airport\" title=\"O'Hare International Airport\">O'Hare International Airport</a>, killing 28.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North_Central_Airlines_Flight_458\" title=\"North Central Airlines Flight 458\">North Central Airlines Flight 458</a> crashes at <a href=\"https://wikipedia.org/wiki/O%27Hare_International_Airport\" title=\"O'Hare International Airport\">O'Hare International Airport</a>, killing 28.", "links": [{"title": "North Central Airlines Flight 458", "link": "https://wikipedia.org/wiki/North_Central_Airlines_Flight_458"}, {"title": "O'Hare International Airport", "link": "https://wikipedia.org/wiki/O%27Hare_International_Airport"}]}, {"year": "1978", "text": "Spain becomes a democracy after 40 years of fascist dictatorship.", "html": "1978 - Spain <a href=\"https://wikipedia.org/wiki/Spanish_transition_to_democracy\" title=\"Spanish transition to democracy\">becomes a democracy</a> after 40 years of fascist <a href=\"https://wikipedia.org/wiki/Dictatorship\" title=\"Dictatorship\">dictatorship</a>.", "no_year_html": "Spain <a href=\"https://wikipedia.org/wiki/Spanish_transition_to_democracy\" title=\"Spanish transition to democracy\">becomes a democracy</a> after 40 years of fascist <a href=\"https://wikipedia.org/wiki/Dictatorship\" title=\"Dictatorship\">dictatorship</a>.", "links": [{"title": "Spanish transition to democracy", "link": "https://wikipedia.org/wiki/Spanish_transition_to_democracy"}, {"title": "Dictatorship", "link": "https://wikipedia.org/wiki/Dictatorship"}]}, {"year": "1983", "text": "<PERSON> <PERSON> visits <PERSON><PERSON><PERSON> in Rebibbia's prison and personally forgives him for the 1981 attack on him in St. Peter's Square.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> visits <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C4%9Fca\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Rebibbia\" title=\"Rebibbia\">Rebibbia</a>'s prison and personally forgives him for the <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>\" title=\"Attempted assassination of Pope <PERSON>\">1981 attack</a> on him in <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Square\" title=\"St. Peter's Square\">St. Peter's Square</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> visits <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C4%9Fca\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Rebibbia\" title=\"Rebibbia\">Rebibbia</a>'s prison and personally forgives him for the <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>\" title=\"Attempted assassination of <PERSON> <PERSON>\">1981 attack</a> on him in <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Square\" title=\"St. Peter's Square\">St. Peter's Square</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_A%C4%9Fca"}, {"title": "Rebibbia", "link": "https://wikipedia.org/wiki/Rebibbia"}, {"title": "Attempted assassination of Pope <PERSON>", "link": "https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "St. Peter's Square", "link": "https://wikipedia.org/wiki/St._Peter%27s_Square"}]}, {"year": "1985", "text": "Palestinian guerrillas kill eighteen people inside the airports of Rome, Italy, and Vienna, Austria.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> guerrillas <a href=\"https://wikipedia.org/wiki/Rome_and_Vienna_airport_attacks\" class=\"mw-redirect\" title=\"Rome and Vienna airport attacks\">kill eighteen people</a> inside the airports of <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a>, Italy, and <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, Austria.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> guerrillas <a href=\"https://wikipedia.org/wiki/Rome_and_Vienna_airport_attacks\" class=\"mw-redirect\" title=\"Rome and Vienna airport attacks\">kill eighteen people</a> inside the airports of <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a>, Italy, and <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, Austria.", "links": [{"title": "Palestinians", "link": "https://wikipedia.org/wiki/Palestinians"}, {"title": "Rome and Vienna airport attacks", "link": "https://wikipedia.org/wiki/Rome_and_Vienna_airport_attacks"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}, {"title": "Vienna", "link": "https://wikipedia.org/wiki/Vienna"}]}, {"year": "1989", "text": "The Romanian Revolution concludes, as the last minor street confrontations and stray shootings abruptly end in the country's capital, Bucharest.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/Romanian_Revolution\" class=\"mw-redirect\" title=\"Romanian Revolution\">Romanian Revolution</a> concludes, as the last minor street confrontations and stray shootings abruptly end in the country's capital, <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Romanian_Revolution\" class=\"mw-redirect\" title=\"Romanian Revolution\">Romanian Revolution</a> concludes, as the last minor street confrontations and stray shootings abruptly end in the country's capital, <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>.", "links": [{"title": "Romanian Revolution", "link": "https://wikipedia.org/wiki/Romanian_Revolution"}, {"title": "Bucharest", "link": "https://wikipedia.org/wiki/Bucharest"}]}, {"year": "1991", "text": "Scandinavian Airlines System Flight 751 crashes in Gottröra in the Norrtälje Municipality in Sweden, injuring 92.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Scandinavian_Airlines_System_Flight_751\" title=\"Scandinavian Airlines System Flight 751\">Scandinavian Airlines System Flight 751</a> crashes in <a href=\"https://wikipedia.org/wiki/Gottr%C3%B6ra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON>öra</a> in the <a href=\"https://wikipedia.org/wiki/Norrt%C3%A4lje_Municipality\" title=\"Norrtälje Municipality\">Norrtälje Municipality</a> in <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a>, injuring 92.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scandinavian_Airlines_System_Flight_751\" title=\"Scandinavian Airlines System Flight 751\">Scandinavian Airlines System Flight 751</a> crashes in <a href=\"https://wikipedia.org/wiki/Gottr%C3%B6ra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>ra</a> in the <a href=\"https://wikipedia.org/wiki/Norrt%C3%A4lje_Municipality\" title=\"Norrtälje Municipality\">Norrtälje Municipality</a> in <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a>, injuring 92.", "links": [{"title": "Scandinavian Airlines System Flight 751", "link": "https://wikipedia.org/wiki/Scandinavian_Airlines_System_Flight_751"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gottr%C3%B6ra"}, {"title": "Norrtälje Municipality", "link": "https://wikipedia.org/wiki/Norrt%C3%A4lje_Municipality"}, {"title": "Sweden", "link": "https://wikipedia.org/wiki/Sweden"}]}, {"year": "1996", "text": "Taliban forces retake the strategic Bagram Airfield which solidifies their buffer zone around Kabul, Afghanistan.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> forces retake the strategic <a href=\"https://wikipedia.org/wiki/Bagram_Airfield\" title=\"Bagram Airfield\">Bagram Airfield</a> which solidifies their buffer zone around <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> forces retake the strategic <a href=\"https://wikipedia.org/wiki/Bagram_Airfield\" title=\"Bagram Airfield\">Bagram Airfield</a> which solidifies their buffer zone around <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "links": [{"title": "Taliban", "link": "https://wikipedia.org/wiki/Taliban"}, {"title": "Bagram Airfield", "link": "https://wikipedia.org/wiki/Bagram_Airfield"}, {"title": "Kabul", "link": "https://wikipedia.org/wiki/Kabul"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "1997", "text": "Protestant paramilitary leader <PERSON> is assassinated in Northern Ireland, United Kingdom.", "html": "1997 - Protestant paramilitary leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(loyalist)\" title=\"<PERSON> (loyalist)\"><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, United Kingdom.", "no_year_html": "Protestant paramilitary leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(loyalist)\" title=\"<PERSON> (loyalist)\"><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, United Kingdom.", "links": [{"title": "<PERSON> (loyalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(loyalist)"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "1999", "text": "Burger King and the U.S. Consumer Product Safety Commission order a recall of plastic Poké Ball containers after they are determined to be a choking hazard.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Burger_King\" title=\"Burger King\">Burger King</a> and the <a href=\"https://wikipedia.org/wiki/U.S._Consumer_Product_Safety_Commission\" title=\"U.S. Consumer Product Safety Commission\">U.S. Consumer Product Safety Commission</a> order a <a href=\"https://wikipedia.org/wiki/Burger_King_Pok%C3%A9mon_container_recall\" title=\"Burger King Pokémon container recall\">recall</a> of plastic <a href=\"https://wikipedia.org/wiki/Gameplay_of_Pok%C3%A9mon\" class=\"mw-redirect\" title=\"Gameplay of Pokémon\">Poké Ball</a> containers after they are determined to be a choking hazard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Burger_King\" title=\"Burger King\">Burger King</a> and the <a href=\"https://wikipedia.org/wiki/U.S._Consumer_Product_Safety_Commission\" title=\"U.S. Consumer Product Safety Commission\">U.S. Consumer Product Safety Commission</a> order a <a href=\"https://wikipedia.org/wiki/Burger_King_Pok%C3%A9mon_container_recall\" title=\"Burger King Pokémon container recall\">recall</a> of plastic <a href=\"https://wikipedia.org/wiki/Gameplay_of_Pok%C3%A9mon\" class=\"mw-redirect\" title=\"Gameplay of Pokémon\">Poké Ball</a> containers after they are determined to be a choking hazard.", "links": [{"title": "Burger King", "link": "https://wikipedia.org/wiki/<PERSON>_King"}, {"title": "U.S. Consumer Product Safety Commission", "link": "https://wikipedia.org/wiki/U.S._Consumer_Product_Safety_Commission"}, {"title": "Burger King Pokémon container recall", "link": "https://wikipedia.org/wiki/Burger_King_Pok%C3%A9mon_container_recall"}, {"title": "Gameplay of Pokémon", "link": "https://wikipedia.org/wiki/Gameplay_of_Pok%C3%A9mon"}]}, {"year": "2002", "text": "Two truck bombs kill 72 and wound 200 at the pro-Moscow headquarters of the Chechen government in Grozny, Chechnya, Russia.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/2002_Grozny_truck_bombing\" title=\"2002 Grozny truck bombing\">Two truck bombs</a> kill 72 and wound 200 at the pro-Moscow headquarters of the Chechen government in <a href=\"https://wikipedia.org/wiki/Grozny\" title=\"Grozny\">Grozny</a>, <a href=\"https://wikipedia.org/wiki/Chechnya\" title=\"Chechnya\">Chechnya</a>, Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2002_Grozny_truck_bombing\" title=\"2002 Grozny truck bombing\">Two truck bombs</a> kill 72 and wound 200 at the pro-Moscow headquarters of the Chechen government in <a href=\"https://wikipedia.org/wiki/Grozny\" title=\"Grozny\">Grozny</a>, <a href=\"https://wikipedia.org/wiki/Chechnya\" title=\"Chechnya\">Chechnya</a>, Russia.", "links": [{"title": "2002 Grozny truck bombing", "link": "https://wikipedia.org/wiki/2002_Grozny_truck_bombing"}, {"title": "Grozny", "link": "https://wikipedia.org/wiki/Grozny"}, {"title": "Chechnya", "link": "https://wikipedia.org/wiki/Chechnya"}]}, {"year": "2004", "text": "Radiation from an explosion on the magnetar SGR 1806-20 reaches Earth. It is the brightest extrasolar event known to have been witnessed on the planet.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Radiation\" title=\"Radiation\">Radiation</a> from an explosion on the <a href=\"https://wikipedia.org/wiki/Magnetar\" title=\"Magnetar\">magnetar</a> <a href=\"https://wikipedia.org/wiki/SGR_1806-20\" class=\"mw-redirect\" title=\"SGR 1806-20\">SGR 1806-20</a> reaches Earth. It is the brightest <a href=\"https://wikipedia.org/wiki/Extrasolar\" class=\"mw-redirect\" title=\"Extrasolar\">extrasolar</a> event known to have been witnessed on the planet.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Radiation\" title=\"Radiation\">Radiation</a> from an explosion on the <a href=\"https://wikipedia.org/wiki/Magnetar\" title=\"Magnetar\">magnetar</a> <a href=\"https://wikipedia.org/wiki/SGR_1806-20\" class=\"mw-redirect\" title=\"SGR 1806-20\">SGR 1806-20</a> reaches Earth. It is the brightest <a href=\"https://wikipedia.org/wiki/Extrasolar\" class=\"mw-redirect\" title=\"Extrasolar\">extrasolar</a> event known to have been witnessed on the planet.", "links": [{"title": "Radiation", "link": "https://wikipedia.org/wiki/Radiation"}, {"title": "Magnetar", "link": "https://wikipedia.org/wiki/Magnetar"}, {"title": "SGR 1806-20", "link": "https://wikipedia.org/wiki/SGR_1806-20"}, {"title": "Extrasolar", "link": "https://wikipedia.org/wiki/Extrasolar"}]}, {"year": "2007", "text": "Former Pakistani prime minister <PERSON><PERSON><PERSON> is assassinated in a shooting incident.", "html": "2007 - Former Pakistani <a href=\"https://wikipedia.org/wiki/Prime_minister\" title=\"Prime minister\">prime minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"Assassination of <PERSON><PERSON><PERSON>\">is assassinated</a> in a shooting incident.", "no_year_html": "Former Pakistani <a href=\"https://wikipedia.org/wiki/Prime_minister\" title=\"Prime minister\">prime minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"Assassination of <PERSON><PERSON><PERSON>\">is assassinated</a> in a shooting incident.", "links": [{"title": "Prime minister", "link": "https://wikipedia.org/wiki/Prime_minister"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Assassination of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2007", "text": "Riots erupt in Mombasa, Kenya, after <PERSON><PERSON> is declared the winner of the presidential election, triggering a political, economic, and humanitarian crisis.", "html": "2007 - Riots erupt in <a href=\"https://wikipedia.org/wiki/Mombasa,_Kenya\" class=\"mw-redirect\" title=\"Mombasa, Kenya\">Mombasa, Kenya</a>, after <a href=\"https://wikipedia.org/wiki/M<PERSON>_Ki<PERSON>ki\" title=\"<PERSON><PERSON> Ki<PERSON>ki\"><PERSON><PERSON></a> is declared the winner of the <a href=\"https://wikipedia.org/wiki/Kenyan_presidential_election,_2007\" class=\"mw-redirect\" title=\"Kenyan presidential election, 2007\">presidential election</a>, triggering a <a href=\"https://wikipedia.org/wiki/2007%E2%80%9308_Kenyan_crisis\" class=\"mw-redirect\" title=\"2007-08 Kenyan crisis\">political, economic, and humanitarian crisis</a>.", "no_year_html": "Riots erupt in <a href=\"https://wikipedia.org/wiki/Mombasa,_Kenya\" class=\"mw-redirect\" title=\"Mombasa, Kenya\">Mombasa, Kenya</a>, after <a href=\"https://wikipedia.org/wiki/M<PERSON>_Kibaki\" title=\"<PERSON><PERSON> Ki<PERSON>\"><PERSON><PERSON></a> is declared the winner of the <a href=\"https://wikipedia.org/wiki/Kenyan_presidential_election,_2007\" class=\"mw-redirect\" title=\"Kenyan presidential election, 2007\">presidential election</a>, triggering a <a href=\"https://wikipedia.org/wiki/2007%E2%80%9308_Kenyan_crisis\" class=\"mw-redirect\" title=\"2007-08 Kenyan crisis\">political, economic, and humanitarian crisis</a>.", "links": [{"title": "Mombasa, Kenya", "link": "https://wikipedia.org/wiki/Mombasa,_Kenya"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mwai_Kibaki"}, {"title": "Kenyan presidential election, 2007", "link": "https://wikipedia.org/wiki/Kenyan_presidential_election,_2007"}, {"title": "2007-08 Kenyan crisis", "link": "https://wikipedia.org/wiki/2007%E2%80%9308_Kenyan_crisis"}]}, {"year": "2008", "text": "Operation Cast Lead: Israel launches three-week operation on Gaza.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Gaza_War_(2008%E2%80%9309)\" class=\"mw-redirect\" title=\"Gaza War (2008-09)\">Operation Cast Lead</a>: Israel launches three-week operation on Gaza.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gaza_War_(2008%E2%80%9309)\" class=\"mw-redirect\" title=\"Gaza War (2008-09)\">Operation Cast Lead</a>: Israel launches three-week operation on Gaza.", "links": [{"title": "Gaza War (2008-09)", "link": "https://wikipedia.org/wiki/Gaza_War_(2008%E2%80%9309)"}]}, {"year": "2009", "text": "Iranian election protests: On the Day of Ashura in Tehran, Iran, government security forces fire upon demonstrators.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/2009_Iranian_presidential_election_protests\" title=\"2009 Iranian presidential election protests\">Iranian election protests</a>: On the <a href=\"https://wikipedia.org/wiki/Day_of_Ashura\" class=\"mw-redirect\" title=\"Day of Ashura\">Day of Ashura</a> in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>, government security forces <a href=\"https://wikipedia.org/wiki/Ashura_protests\" title=\"Ashura protests\">fire upon</a> demonstrators.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2009_Iranian_presidential_election_protests\" title=\"2009 Iranian presidential election protests\">Iranian election protests</a>: On the <a href=\"https://wikipedia.org/wiki/Day_of_Ashura\" class=\"mw-redirect\" title=\"Day of Ashura\">Day of Ashura</a> in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>, government security forces <a href=\"https://wikipedia.org/wiki/Ashura_protests\" title=\"Ashura protests\">fire upon</a> demonstrators.", "links": [{"title": "2009 Iranian presidential election protests", "link": "https://wikipedia.org/wiki/2009_Iranian_presidential_election_protests"}, {"title": "Day of Ashura", "link": "https://wikipedia.org/wiki/Day_of_Ashura"}, {"title": "Tehran", "link": "https://wikipedia.org/wiki/Tehran"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Ashura protests", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_protests"}]}, {"year": "2019", "text": "Bek Air Flight 2100 crashes during takeoff from Almaty International Airport in Almaty, Kazakhstan, killing 13.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Bek_Air_Flight_2100\" title=\"Bek Air Flight 2100\">Bek Air Flight 2100</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Almaty_International_Airport\" title=\"Almaty International Airport\">Almaty International Airport</a> in <a href=\"https://wikipedia.org/wiki/Almaty\" title=\"Almaty\">Almaty</a>, <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>, killing 13.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bek_Air_Flight_2100\" title=\"Bek Air Flight 2100\">Bek Air Flight 2100</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Almaty_International_Airport\" title=\"Almaty International Airport\">Almaty International Airport</a> in <a href=\"https://wikipedia.org/wiki/Almaty\" title=\"Almaty\">Almaty</a>, <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>, killing 13.", "links": [{"title": "Bek Air Flight 2100", "link": "https://wikipedia.org/wiki/Bek_Air_Flight_2100"}, {"title": "Almaty International Airport", "link": "https://wikipedia.org/wiki/Almaty_International_Airport"}, {"title": "Almaty", "link": "https://wikipedia.org/wiki/Almaty"}, {"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}]}], "Births": [{"year": "1350", "text": "<PERSON> of Aragon (d. 1395)", "html": "1350 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (d. 1395)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (d. 1395)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}]}, {"year": "1390", "text": "<PERSON>, claimant to the English throne (d. 1411)", "html": "1390 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, claimant to the English throne (d. 1411)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, claimant to the English throne (d. 1411)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1459", "text": "<PERSON>, King of Poland (d. 1501)", "html": "1459 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/King_of_Poland\" class=\"mw-redirect\" title=\"King of Poland\">King of Poland</a> (d. 1501)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/King_of_Poland\" class=\"mw-redirect\" title=\"King of Poland\">King of Poland</a> (d. 1501)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "King of Poland", "link": "https://wikipedia.org/wiki/King_of_Poland"}]}, {"year": "1481", "text": "<PERSON>, Margrave of Brandenburg-Bayreuth, Margrave of Bayreuth (d. 1527)", "html": "1481 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Bayreuth\" class=\"mw-redirect\" title=\"<PERSON>, Margrave of Brandenburg-Bayreuth\"><PERSON>, Margrave of Brandenburg-Bayreuth</a>, <PERSON>grave of Bayreuth (d. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Bayreuth\" class=\"mw-redirect\" title=\"<PERSON>, Margrave of Brandenburg-Bayreuth\"><PERSON>, Margrave of Brandenburg-Bayreuth</a>, <PERSON><PERSON> of Bayreuth (d. 1527)", "links": [{"title": "<PERSON>, Margrave of Brandenburg-Bayreuth", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Bayreuth"}]}, {"year": "1493", "text": "<PERSON>, German theologian (d. 1573)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1573)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1566", "text": "<PERSON>, Bohemian physician, politician and philosopher (d. 1621)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bohemian physician, politician and philosopher (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bohemian physician, politician and philosopher (d. 1621)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1571", "text": "<PERSON>, German mathematician, astronomer, and astrologer (d. 1630)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, astronomer, and astrologer (d. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, astronomer, and astrologer (d. 1630)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1572", "text": "<PERSON>, Czech poet, playwright, and composer (d. 1622)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech poet, playwright, and composer (d. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech poet, playwright, and composer (d. 1622)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1584", "text": "<PERSON>, Duke of Pomerania (d. 1625)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a> (d. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a> (d. 1625)", "links": [{"title": "<PERSON>, Duke of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania"}]}, {"year": "1595", "text": "<PERSON><PERSON><PERSON>, hetman of Ukraine (d. 1657)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, hetman of Ukraine (d. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, hetman of Ukraine (d. 1657)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1622", "text": "<PERSON><PERSON><PERSON>, Polish philosopher (d. 1700)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>of<PERSON>_<PERSON>\" title=\"<PERSON>of<PERSON> R<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish philosopher (d. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>of<PERSON>_<PERSON>\" title=\"<PERSON>of<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish philosopher (d. 1700)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teof<PERSON>_<PERSON>"}]}, {"year": "1633", "text": "<PERSON>, French missionary (d. 1714)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary (d. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1636", "text": "<PERSON>, English gentleman, Member of Parliament (d. 1717)", "html": "1636 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gentleman, Member of Parliament (d. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gentleman, Member of Parliament (d. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1637", "text": "<PERSON><PERSON>, Venetian writer (d. 1719)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/Petar_<PERSON>veli%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venetian writer (d. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venetian writer (d. 1719)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Petar_Kanaveli%C4%87"}]}, {"year": "1645", "text": "<PERSON>, Swiss architect (d. 1713)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss architect (d. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss architect (d. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1655", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English politician (d. 1727)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/Abstrupus_Danby\" title=\"Abstrupus Danby\">Abstrup<PERSON></a>, English politician (d. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abstrupus_Dan<PERSON>\" title=\"Abstrupus Danby\">Abstrup<PERSON></a>, English politician (d. 1727)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abstrupus_Danby"}]}, {"year": "1660", "text": "<PERSON>, Italian Capuchin mystic (d. 1727)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Capuchin mystic (d. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Capuchin mystic (d. 1727)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1663", "text": "<PERSON>, German painter (d. 1731)", "html": "1663 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1731)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON><PERSON>, English priest and theologian (d. 1750)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English priest and theologian (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English priest and theologian (d. 1750)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1689", "text": "<PERSON>, Encyclopedia editor, professor (d. 1733)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Encyclopedia editor, professor (d. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Encyclopedia editor, professor (d. 1733)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "(baptised) 1692", "text": "<PERSON>, British Royal Navy officer and Member of Parliament (d. 1752)", "html": "(baptised) 1692 - (baptised) <a href=\"https://wikipedia.org/wiki/1692\" title=\"1692\">1692</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, British Royal Navy officer and Member of Parliament (d. 1752)", "no_year_html": "(baptised) <a href=\"https://wikipedia.org/wiki/1692\" title=\"1692\">1692</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, British Royal Navy officer and Member of Parliament (d. 1752)", "links": [{"title": "1692", "link": "https://wikipedia.org/wiki/1692"}, {"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1697", "text": "<PERSON><PERSON><PERSON>, Irish legal writer (d. 1754)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/Sollom_Emlyn\" title=\"Sollom Emlyn\"><PERSON><PERSON><PERSON></a>, Irish legal writer (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sollo<PERSON>_Emlyn\" title=\"Sollo<PERSON> Emlyn\"><PERSON><PERSON><PERSON></a>, Irish legal writer (d. 1754)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sollom_Emlyn"}]}, {"year": "1705", "text": "Prince <PERSON> of Anhalt-Dessau, German prince of the House of Ascania (d. 1781)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_of_Anhalt-Dessau\" title=\"Prince <PERSON> of Anhalt-Dessau\">Prince <PERSON> of Anhalt-Dessau</a>, German prince of the House of Ascania (d. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_of_Anhalt-Dessau\" title=\"Prince <PERSON> of Anhalt-Dessau\">Prince <PERSON> of Anhalt-Dessau</a>, German prince of the House of Ascania (d. 1781)", "links": [{"title": "Prince <PERSON> of Anhalt-Dessau", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON><PERSON>_of_Anhalt-Dessau"}]}, {"year": "1713", "text": "<PERSON>, Italian architect and engineer (d. 1770)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and engineer (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and engineer (d. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1714", "text": "<PERSON>, English preacher and saint (d. 1770)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English preacher and saint (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English preacher and saint (d. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, French general (d. 1794)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1721", "text": "<PERSON>, Dutch philosopher and author (d. 1790)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher and author (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher and author (d. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "(baptised) 1761", "text": "<PERSON>, Russian field marshal and politician, Governor-General of Finland (d. 1818)", "html": "(baptised) 1761 - (baptised) <a href=\"https://wikipedia.org/wiki/1761\" title=\"1761\">1761</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> de <PERSON></a>, Russian field marshal and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Finland\" title=\"Governor-General of Finland\">Governor-General of Finland</a> (d. 1818)", "no_year_html": "(baptised) <a href=\"https://wikipedia.org/wiki/1761\" title=\"1761\">1761</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> de <PERSON>\"><PERSON> de <PERSON></a>, Russian field marshal and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Finland\" title=\"Governor-General of Finland\">Governor-General of Finland</a> (d. 1818)", "links": [{"title": "1761", "link": "https://wikipedia.org/wiki/1761"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor-General of Finland", "link": "https://wikipedia.org/wiki/Governor-General_of_Finland"}]}, {"year": "1773", "text": "<PERSON>, English engineer and politician (d. 1857)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "<PERSON><PERSON>, Russian general (d. 1811)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (d. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (d. 1811)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON>, Indian poet (d. 1869)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet (d. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>b"}]}, {"year": "1797", "text": "<PERSON>, American theologian (d. 1878)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian activist (d. 1839)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian activist (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian activist (d. 1839)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON><PERSON><PERSON>, Greek poet and politician, Foreign Minister of Greece (d. 1892)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and politician, <a href=\"https://wikipedia.org/wiki/List_of_foreign_ministers_of_Greece\" title=\"List of foreign ministers of Greece\">Foreign Minister of Greece</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and politician, <a href=\"https://wikipedia.org/wiki/List_of_foreign_ministers_of_Greece\" title=\"List of foreign ministers of Greece\">Foreign Minister of Greece</a> (d. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of foreign ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_foreign_ministers_of_Greece"}]}, {"year": "1822", "text": "<PERSON>, French chemist and microbiologist (d. 1895)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and microbiologist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and microbiologist (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, English-Canadian journalist and politician, 5th Prime Minister of Canada (d. 1917)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1827", "text": "<PERSON><PERSON>, Polish-born politician, writer, historian and member of the Imperial Council of Austria (d. 1900)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-born politician, writer, historian and member of the Imperial Council of Austria (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-born politician, writer, historian and member of the Imperial Council of Austria (d. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON>"}]}, {"year": "1832", "text": "<PERSON>, Russian businessman and philanthropist, founded the Tretyakov Gallery (d. 1897)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Tretyakov_Gallery\" title=\"Tretyakov Gallery\">Tretyakov Gallery</a> (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Tretyakov_Gallery\" title=\"Tretyakov Gallery\">Tretyakov Gallery</a> (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tretyakov Gallery", "link": "https://wikipedia.org/wiki/Tretyakov_Gallery"}]}, {"year": "1838", "text": "<PERSON>, Norwegian priest, social reformer, politician, and newspaper editor (d. 1900)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1838)\" title=\"<PERSON> (born 1838)\"><PERSON></a>, Norwegian priest, social reformer, politician, and newspaper editor (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1838)\" title=\"<PERSON> (born 1838)\"><PERSON></a>, Norwegian priest, social reformer, politician, and newspaper editor (d. 1900)", "links": [{"title": "<PERSON> (born 1838)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1838)"}]}, {"year": "1858", "text": "<PERSON>, Chilean lawyer and politician, 17th President of Chile (d. 1930)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1863", "text": "<PERSON>, American lawyer and politician, 27th Governor of Illinois (d. 1941)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Governor of Illinois</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Governor of Illinois</a> (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>_Emmerson"}, {"title": "Governor of Illinois", "link": "https://wikipedia.org/wiki/Governor_of_Illinois"}]}, {"year": "1864", "text": "<PERSON><PERSON><PERSON>, French painter and illustrator (d. 1940)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and illustrator (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and illustrator (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Finnish politician (d. 1938)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, English-American actor (d. 1954)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Sydney_Greenstreet\" title=\"Sydney Greenstreet\">Sydney Greenstreet</a>, English-American actor (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Greenstreet\" title=\"Sydney Greenstreet\">Sydney Greenstreet</a>, English-American actor (d. 1954)", "links": [{"title": "Sydney Greenstreet", "link": "https://wikipedia.org/wiki/Sydney_Greenstreet"}]}, {"year": "1882", "text": "<PERSON>, British modernist poet and artist (d. 1966)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lo<PERSON>\"><PERSON></a>, British modernist poet and artist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lo<PERSON>\"><PERSON></a>, British modernist poet and artist (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>y"}]}, {"year": "1883", "text": "<PERSON>, Canadian-American businessman and philanthropist (d. 1979)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman and philanthropist (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman and philanthropist (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON>, German actress, director, and screenwriter (d. 1954)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress, director, and screenwriter (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, German actress, director, and screenwriter (d. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Canadian captain and pilot (d. 1917)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain and pilot (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain and pilot (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American author and theorist (d. 1956)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and theorist (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and theorist (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Belgian cyclist (d. 1952)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, German author and playwright (d. 1977)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, Japanese politician (d. 1960)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Inejiro_Asanuma\" class=\"mw-redirect\" title=\"Inejiro Asanuma\"><PERSON><PERSON><PERSON></a>, Japanese politician (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inejiro_<PERSON>anuma\" class=\"mw-redirect\" title=\"Inejiro Asanuma\"><PERSON><PERSON><PERSON></a>, Japanese politician (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Inej<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German racing driver (d. 1978)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, German-American actress and singer (d. 1992)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American actress and singer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American actress and singer (d. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, English actress (d. 1987)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, French racing driver and engineer (d. 1983)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver and engineer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver and engineer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Bonnet"}]}, {"year": "1905", "text": "<PERSON>, American actor and comedian (d. 1974)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Arquette\" title=\"Cliff Arquette\"><PERSON></a>, American actor and comedian (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Arquette\" title=\"Cliff Arquette\"><PERSON></a>, American actor and comedian (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cliff_Arquette"}]}, {"year": "1906", "text": "<PERSON>, American pianist, composer, and actor (d. 1972)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Oscar_Levant\" title=\"Oscar Levant\"><PERSON></a>, American pianist, composer, and actor (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oscar_Levant\" title=\"Oscar Levant\"><PERSON></a>, American pianist, composer, and actor (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oscar_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Turkish poet (d. 1958)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t_%C3%87elebi\" title=\"<PERSON><PERSON> <PERSON> Çelebi\"><PERSON><PERSON></a>, Turkish poet (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t_%C3%87elebi\" title=\"<PERSON><PERSON> Çelebi\"><PERSON><PERSON> <PERSON><PERSON></a>, Turkish poet (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asaf_Halet_%C3%87elebi"}]}, {"year": "1907", "text": "<PERSON>, German journalist and author (d. 1999)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, English author (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(romance_novelist)\" class=\"mw-redirect\" title=\"<PERSON> (romance novelist)\"><PERSON></a>, English author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(romance_novelist)\" class=\"mw-redirect\" title=\"<PERSON> (romance novelist)\"><PERSON></a>, English author (d. 1991)", "links": [{"title": "<PERSON> (romance novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(romance_novelist)"}]}, {"year": "1907", "text": "<PERSON>, American baseball player and activist (d. 2002)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and activist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and activist (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Dutch conductor and composer (d. 1978)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch conductor and composer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch conductor and composer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, English skier and author (d. 2000)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(skier)\" title=\"<PERSON> (skier)\"><PERSON></a>, English skier and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(skier)\" title=\"<PERSON> (skier)\"><PERSON></a>, English skier and author (d. 2000)", "links": [{"title": "<PERSON> (skier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(skier)"}]}, {"year": "1910", "text": "<PERSON>, American poet and educator (d. 1970)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, English-Canadian singer and actress (d. 2006)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian singer and actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian singer and actress (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Canadian poet and novelist (d. 1986)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_author)\" title=\"<PERSON> (Canadian author)\"><PERSON></a>, Canadian poet and novelist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_author)\" title=\"<PERSON> (Canadian author)\"><PERSON></a>, Canadian poet and novelist (d. 1986)", "links": [{"title": "<PERSON> (Canadian author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_author)"}]}, {"year": "1915", "text": "<PERSON>, American gynecologist, author, and academic (d. 2001)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Masters\"><PERSON></a>, American gynecologist, author, and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William Masters\"><PERSON></a>, American gynecologist, author, and academic (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Hungarian-Cypriot footballer and manager (d. 1999)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gel<PERSON>%C3%A9r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Cypriot footballer and manager (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Cypriot footballer and manager (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Zsengell%C3%A9r"}]}, {"year": "1916", "text": "<PERSON>, German pilot (d. 1953)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actress (d. 1968)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American saxophonist and clarinet player (d. 2015)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Sri Lankan lawyer and academic (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer and academic (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Finnish soldier and author (d. 2009)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish soldier and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish soldier and author (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Onni_Palaste"}]}, {"year": "1918", "text": "<PERSON>, American captain and illustrator (d. 2012)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and illustrator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and illustrator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American general and pilot (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American jockey and trainer (d. 2005)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and trainer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and trainer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English countertenor (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English countertenor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English countertenor (d. 2013)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1923", "text": "<PERSON>, Polish-Canadian painter and educator (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian painter and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian painter and educator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, South African politician (d. 2018)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American computer scientist and engineer (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American soldier, lawyer, and politician (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French actor, singer, director, and producer (d. 2020)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, singer, director, and producer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, singer, director, and producer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor, director, and producer (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English engineer and politician (d. 2011)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, 1st Chief Minister of Uttarakhand (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Uttarakhand\" class=\"mw-redirect\" title=\"Chief Minister of Uttarakhand\">Chief Minister of Uttarakhand</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Uttarakhand\" class=\"mw-redirect\" title=\"Chief Minister of Uttarakhand\">Chief Minister of Uttarakhand</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(politician)"}, {"title": "Chief Minister of Uttarakhand", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Uttarakhand"}]}, {"year": "1927", "text": "<PERSON>, American baseball player, obstetrician, and gynecologist (d. 1984)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, obstetrician, and gynecologist (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, obstetrician, and gynecologist (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American anthropologist and academic (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, English-born American novelist and essayist (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, English-born American novelist and essayist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON><PERSON>\" title=\"Wil<PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, English-born American novelist and essayist (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American guitarist and songwriter (d. 2016)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist and songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist and songwriter (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American golfer (d. 1997)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Ukrainian gymnast and coach", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian gymnast and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian gymnast and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, <PERSON> of Plaistow, English businessman", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Plaistow\" title=\"<PERSON>, Baron <PERSON> of Plaistow\"><PERSON>, Baron <PERSON> of Plaistow</a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Plaistow\" title=\"<PERSON>, Baron <PERSON> of Plaistow\"><PERSON>, Baron <PERSON> of Plaistow</a>, English businessman", "links": [{"title": "<PERSON>, Baron <PERSON> of Plaistow", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Plaistow"}]}, {"year": "1935", "text": "<PERSON>, English bishop", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_(bishop)"}]}, {"year": "1936", "text": "<PERSON>, Australian blood plasma donor (d. 2025)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blood_donor)\" title=\"<PERSON> (blood donor)\"><PERSON></a>, Australian blood plasma donor (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blood_donor)\" title=\"<PERSON> (blood donor)\"><PERSON></a>, Australian blood plasma donor (d. 2025)", "links": [{"title": "<PERSON> (blood donor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blood_donor)"}]}, {"year": "1936", "text": "<PERSON>, English cricketer (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 2014)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1936", "text": "<PERSON>, Estonian swimmer and coach", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian swimmer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian swimmer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actor (d. 2024)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English cricketer and umpire (d. 2009)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(umpire)\" title=\"<PERSON> (umpire)\"><PERSON></a>, English cricketer and umpire (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(umpire)\" title=\"<PERSON> (umpire)\"><PERSON></a>, English cricketer and umpire (d. 2009)", "links": [{"title": "<PERSON> (umpire)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(umpire)"}]}, {"year": "1941", "text": "<PERSON>, American basketball player and coach", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miles_Aiken"}]}, {"year": "1941", "text": "<PERSON>, English singer-songwriter and keyboard player (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and keyboard player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and keyboard player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American basketball player and coach", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American baseball player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1942", "text": "<PERSON>, American politician, 53rd Mayor of Boston (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 53rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Boston\" title=\"Mayor of Boston\">Mayor of Boston</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 53rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Boston\" title=\"Mayor of Boston\">Mayor of Boston</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Boston", "link": "https://wikipedia.org/wiki/Mayor_of_Boston"}]}, {"year": "1942", "text": "<PERSON>, American basketball player and coach", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American journalist and author (d. 2019)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Spanish singer-songwriter and guitarist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English songwriter and producer (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and producer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American baseball player and coach", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English guitarist, songwriter, and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Foreigner_guitarist)\" title=\"<PERSON> (Foreigner guitarist)\"><PERSON></a>, English guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Foreigner_guitarist)\" title=\"<PERSON> (Foreigner guitarist)\"><PERSON></a>, English guitarist, songwriter, and producer", "links": [{"title": "<PERSON> (Foreigner guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Foreigner_guitarist)"}]}, {"year": "1946", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Irish footballer and manager (d. 2024)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>-<PERSON>, English journalist and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, English journalist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, English journalist and producer", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/Janet_<PERSON>-Porter"}]}, {"year": "1946", "text": "<PERSON>, English journalist and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Toy<PERSON>bee\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Toynbee\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American wrestler and coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American wrestler and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American wrestler and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English footballer and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Belgian runner", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, French-Russian actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Russian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Russian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Japanese director, producer, and critic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese director, producer, and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese director, producer, and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Greek singer-songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Italian footballer and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American drummer and songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Mexican economist and politician, 54th President of Mexico", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican economist and politician, 54th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican economist and politician, 54th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1952", "text": "<PERSON>, Canadian farmer and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian farmer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian farmer and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1952", "text": "<PERSON>, Scottish singer-songwriter, guitarist, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American basketball player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> Benson", "link": "https://wikipedia.org/wiki/<PERSON>_Benson"}]}, {"year": "1954", "text": "<PERSON><PERSON>, English director, producer, and production manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English director, producer, and production manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English director, producer, and production manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Singaporean politician and 5th Senior Minister of Singapore", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean politician and 5th <a href=\"https://wikipedia.org/wiki/Senior_Minister_of_Singapore\" title=\"Senior Minister of Singapore\">Senior Minister of Singapore</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Singaporean politician and 5th <a href=\"https://wikipedia.org/wiki/Senior_Minister_of_Singapore\" title=\"Senior Minister of Singapore\">Senior Minister of Singapore</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>an"}, {"title": "Senior Minister of Singapore", "link": "https://wikipedia.org/wiki/Senior_Minister_of_Singapore"}]}, {"year": "1955", "text": "<PERSON>, American race car driver", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American journalist and author (d. 2001)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Romanian runner", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Melinte"}]}, {"year": "1958", "text": "<PERSON>, American golfer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American astrologer, historian, and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astrologer, historian, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astrologer, historian, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American football player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, English actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27Abo\" title=\"<PERSON><PERSON> d'<PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27Abo\" title=\"<PERSON><PERSON> d'<PERSON>bo\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mary<PERSON>_d%27Abo"}]}, {"year": "1960", "text": "<PERSON>, American conductor and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian golfer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1961", "text": "<PERSON>, German lawyer and politician, 15th Vice-Chancellor of Germany (d. 2016)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice-Chancellor of Germany", "link": "https://wikipedia.org/wiki/Vice-Chancellor_of_Germany"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Singaporean journalist and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American golfer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Argentine-French director and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Gaspar_No%C3%A9\" title=\"<PERSON>par No<PERSON>\"><PERSON><PERSON></a>, Argentine-French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gaspar_No%C3%A9\" title=\"Gaspar No<PERSON>\"><PERSON><PERSON></a>, Argentine-French director and screenwriter", "links": [{"title": "Gaspar <PERSON>", "link": "https://wikipedia.org/wiki/Gaspar_No%C3%A9"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Indian film actor and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Khan\"><PERSON><PERSON></a>, Indian film actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Khan\"><PERSON><PERSON></a>, Indian film actor and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English director and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1966", "text": "<PERSON>, American football player, wrestler and actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, wrestler and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American model and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, French racing driver", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American professional wrestler and actress (d. 2016)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Chyna\" title=\"<PERSON>yna\"><PERSON><PERSON></a>, American professional wrestler and actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chyna\" title=\"<PERSON>yna\"><PERSON><PERSON></a>, American professional wrestler and actress (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chyna"}]}, {"year": "1969", "text": "<PERSON>, American author and journalist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American football player and radio host", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Japanese pilot and astronaut", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese pilot and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese pilot and astronaut", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Scottish footballer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English guitarist and educator", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Govan\"><PERSON></a>, English guitarist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Govan\"><PERSON></a>, English guitarist and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American television journalist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Guthrie\" title=\"<PERSON> Guthrie\"><PERSON></a>, American television journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Guthrie\" title=\"<PERSON> Guthrie\"><PERSON></a>, American television journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Savannah_Guthrie"}]}, {"year": "1972", "text": "<PERSON>, Welsh rugby union player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby union player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby union player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American basketball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American guitarist and songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Dutch pianist and composer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch pianist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Czech high jumper", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Jank%C5%AF\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Jank%C5%AF\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech high jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Jank%C5%AF"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Japanese-American actor and visual effects designer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American actor and visual effects designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American actor and visual effects designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American ice hockey player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Latvian race walker and therapist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Aigar<PERSON>_Fadejevs\" title=\"<PERSON>gars Fadejevs\"><PERSON><PERSON><PERSON></a>, Latvian race walker and therapist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Fadejevs\" title=\"<PERSON>gars Fadejevs\"><PERSON><PERSON><PERSON></a>, Latvian race walker and therapist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ai<PERSON>s_Fadejevs"}]}, {"year": "1975", "text": "<PERSON>, American actress (d. 1988)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Heather_O%27Rourke"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Greek footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Polish mountaineer (d. 2009)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Cuban basketball player and hurdler", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Daim%C3%AD_Pern%C3%ADa\" title=\"<PERSON>m<PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban basketball player and hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Daim%C3%AD_Pern%C3%ADa\" title=\"<PERSON>m<PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban basketball player and hurdler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Daim%C3%AD_Pern%C3%ADa"}]}, {"year": "1976", "text": "<PERSON>, Canadian-Italian ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Italian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Italian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>llister\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>llister\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/De<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Canadian weightlifter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian weightlifter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian weightlifter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English footballer and manager", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Palmer\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Swiss wrestler", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Estonian journalist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Australian actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American-Haitian runner", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Haitian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Haitian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American illustrator", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Erin_E<PERSON>_Stead\" title=\"<PERSON> E<PERSON> Stead\"><PERSON></a>, American illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erin_E._Stead\" title=\"Erin E<PERSON> Stead\"><PERSON></a>, American illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Stead"}]}, {"year": "1983", "text": "<PERSON>, New Zealand rugby union player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby union player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American high jumper", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(high_jumper)\" title=\"<PERSON> (high jumper)\"><PERSON></a>, American high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(high_jumper)\" title=\"<PERSON> (high jumper)\"><PERSON></a>, American high jumper", "links": [{"title": "<PERSON> (high jumper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(high_jumper)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Latvian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C4%BCotkins\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Latvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C4%BCotkins\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Latvian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andrejs_Perep%C4%BCotkins"}]}, {"year": "1984", "text": "<PERSON>, French tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Belgian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>lly\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian racing driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_d%27Ambrosio\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_d%27Ambrosio\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian racing driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_d%27Ambrosio"}]}, {"year": "1985", "text": "<PERSON><PERSON>, French footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1985", "text": "<PERSON>, Canadian-American ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ny"}]}, {"year": "1986", "text": "<PERSON>, Australian snowboarder", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bright\" title=\"<PERSON> Bright\"><PERSON></a>, Australian snowboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bright\" title=\"<PERSON> Bright\"><PERSON></a>, Australian snowboarder", "links": [{"title": "Torah Bright", "link": "https://wikipedia.org/wiki/<PERSON>_Bright"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Jamaican sprinter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Jamaican sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Jamaican sprinter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English model", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Mexican basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Mexican basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Mexican basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A9rrez_(basketball)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Icelandic actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Jamaican-English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican-English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican-English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON>, South Korean singer and actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-yeon\" title=\"<PERSON>-yeon\"><PERSON>yeon</a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-yeon\" title=\"<PERSON>-yeon\"><PERSON>-yeon</a>, South Korean singer and actor", "links": [{"title": "<PERSON>yeon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-yeon"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Peruvian tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_V%C3%A1rgas_<PERSON>vo\" title=\"<PERSON>\"><PERSON></a>, Peruvian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rgas_<PERSON>vo\" title=\"<PERSON>\"><PERSON></a>, Peruvian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_V%C3%A1rgas_Calvo"}]}, {"year": "1990", "text": "<PERSON>, Finnish figure skater", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Canadian tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American wrestler", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league_born_1991)\" class=\"mw-redirect\" title=\"<PERSON> (rugby league born 1991)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league_born_1991)\" class=\"mw-redirect\" title=\"<PERSON> (rugby league born 1991)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league born 1991)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league_born_1991)"}]}, {"year": "1991", "text": "<PERSON>, Scottish triathlete and long-distance runner", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish triathlete and long-distance runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish triathlete and long-distance runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Scottish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)\" title=\"<PERSON> (footballer, born 1991)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)\" title=\"<PERSON> (footballer, born 1991)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer, born 1991)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)"}]}, {"year": "1992", "text": "<PERSON>, Estonian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Estonian decathlete", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian decathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Is<PERSON>_Palaz%C3%B3n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Is<PERSON>_<PERSON>z%C3%B3n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isi_Palaz%C3%B3n"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, French-American actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Timoth%C3%A9e_Chalamet\" title=\"<PERSON><PERSON><PERSON> Chalam<PERSON>\"><PERSON><PERSON><PERSON></a>, French-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Timoth%C3%A9e_Chalamet\" title=\"Timoth<PERSON> Chalam<PERSON>\"><PERSON><PERSON><PERSON></a>, French-American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Timoth%C3%A9e_Chalamet"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Ivorian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Estonian chess player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Danish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Thai actor and singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Vachirawit_Chivaaree\" title=\"Vachirawit Chivaaree\">Vachiraw<PERSON> Chi<PERSON></a>, Thai actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vachirawit_Chivaaree\" title=\"Vachirawit Chivaaree\">Vachiraw<PERSON> Chi<PERSON></a>, Thai actor and singer", "links": [{"title": "Vachirawit Chi<PERSON>e", "link": "https://wikipedia.org/wiki/Vachirawit_Chivaaree"}]}, {"year": "1997", "text": "<PERSON>, Croatian tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Lu<PERSON>_Garza\" title=\"Luka Garza\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu<PERSON>_Garza\" title=\"Luka Garza\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "Lu<PERSON> Garza", "link": "https://wikipedia.org/wiki/Luka_Garza"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Spanish footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)"}]}], "Deaths": [{"year": "683", "text": "<PERSON><PERSON> of Tang, 3rd emperor of the Chinese Tang dynasty (b. 628)", "html": "683 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON>zong of Tang\"><PERSON><PERSON> of Tang</a>, 3rd emperor of the Chinese <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> (b. 628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON>zong of Tang\"><PERSON><PERSON> of Tang</a>, 3rd emperor of the Chinese <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> (b. 628)", "links": [{"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_<PERSON>_Tang"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "870", "text": "<PERSON><PERSON><PERSON> of Paris, Frankish bishop", "html": "870 - <a href=\"https://wikipedia.org/wiki/Aeneas_of_Paris\" title=\"Aeneas of Paris\"><PERSON><PERSON><PERSON> of Paris</a>, Frankish bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>as_of_Paris\" title=\"Aeneas of Paris\"><PERSON><PERSON><PERSON> of Paris</a>, Frankish bishop", "links": [{"title": "Aeneas of Paris", "link": "https://wikipedia.org/wiki/Aeneas_of_Paris"}]}, {"year": "975", "text": "<PERSON><PERSON><PERSON>, bishop of Utrecht (b. 897)", "html": "975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Utrecht\" title=\"<PERSON><PERSON><PERSON> of Utrecht\"><PERSON><PERSON><PERSON></a>, bishop of Utrecht (b. 897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Utrecht\" title=\"<PERSON><PERSON><PERSON> of Utrecht\"><PERSON><PERSON><PERSON></a>, bishop of Utrecht (b. 897)", "links": [{"title": "<PERSON><PERSON><PERSON> of Utrecht", "link": "https://wikipedia.org/wiki/Balderic_of_Utrecht"}]}, {"year": "1003", "text": "<PERSON> of Blois, French duchess and regent", "html": "1003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Blois\" title=\"<PERSON> of Blois\"><PERSON> of Blois</a>, French duchess and regent", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Blois\"><PERSON> of Blois</a>, French duchess and regent", "links": [{"title": "Emma of Blois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1005", "text": "<PERSON><PERSON> the Younger, Byzantine abbot (b. 910)", "html": "1005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Younger\" title=\"<PERSON><PERSON> the Younger\"><PERSON><PERSON> the Younger</a>, Byzantine abbot (b. 910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Younger\" title=\"<PERSON><PERSON> the Younger\"><PERSON><PERSON> the Younger</a>, Byzantine abbot (b. 910)", "links": [{"title": "<PERSON><PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_the_Younger"}]}, {"year": "1076", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Grand Prince of Kiev (b. 1027)", "html": "1076 - <a href=\"https://wikipedia.org/wiki/Sviatoslav_II_of_Kiev\" title=\"Sviatoslav II of Kiev\"><PERSON>via<PERSON><PERSON> <PERSON></a>, Grand Prince of Kiev (b. 1027)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sviatoslav_II_of_Kiev\" title=\"Sviatoslav II of Kiev\">Svia<PERSON><PERSON> <PERSON></a>, Grand Prince of Kiev (b. 1027)", "links": [{"title": "Sviatoslav II of Kiev", "link": "https://wikipedia.org/wiki/Sviatoslav_II_of_Kiev"}]}, {"year": "1087", "text": "<PERSON><PERSON> of Savoy, Holy Roman Empress (b. 1051)", "html": "1087 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Savoy\" title=\"<PERSON><PERSON> of Savoy\"><PERSON><PERSON> of Savoy</a>, Holy Roman Empress (b. 1051)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Savoy\" title=\"<PERSON><PERSON> of Savoy\"><PERSON><PERSON> of Savoy</a>, Holy Roman Empress (b. 1051)", "links": [{"title": "<PERSON><PERSON> of Savoy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Savoy"}]}, {"year": "1381", "text": "<PERSON>, 3rd Earl of March, English politician (b. 1352)", "html": "1381 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_March\" title=\"<PERSON>, 3rd Earl of March\"><PERSON>, 3rd Earl of March</a>, English politician (b. 1352)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_March\" title=\"<PERSON>, 3rd Earl of <PERSON>\"><PERSON>, 3rd Earl of March</a>, English politician (b. 1352)", "links": [{"title": "<PERSON>, 3rd Earl of March", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_<PERSON>"}]}, {"year": "1518", "text": "<PERSON><PERSON><PERSON> II, sultan of the Bahmani Sultanate (b. c. 1470)", "html": "1518 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Bahmani_II\" title=\"<PERSON><PERSON><PERSON> Bahmani II\"><PERSON><PERSON><PERSON></a>, sultan of the <a href=\"https://wikipedia.org/wiki/Bahmani_Sultanate\" class=\"mw-redirect\" title=\"Bahmani Sultanate\">Bahmani Sultanate</a> (b. c. 1470)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>hmani_II\" title=\"<PERSON><PERSON><PERSON>i II\"><PERSON><PERSON><PERSON></a>, sultan of the <a href=\"https://wikipedia.org/wiki/Bahmani_Sultanate\" class=\"mw-redirect\" title=\"Bahmani Sultanate\">Bahmani Sultanate</a> (b. c. 1470)", "links": [{"title": "<PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_II"}, {"title": "Bahmani Sultanate", "link": "https://wikipedia.org/wiki/Bahmani_Sultanate"}]}, {"year": "1543", "text": "<PERSON>, margrave of Brandenburg-Ansbach (b. 1484)", "html": "1543 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Mar<PERSON>_of_Brandenburg-Ansbach\" title=\"<PERSON>, Margrave of Brandenburg-Ansbach\"><PERSON></a>, margrave of Brandenburg-Ansbach (b. 1484)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Ansbach\" title=\"<PERSON>, Margrave of Brandenburg-Ansbach\"><PERSON></a>, margrave of Brandenburg-Ansbach (b. 1484)", "links": [{"title": "<PERSON>, Margrave of Brandenburg-Ansbach", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Ansbach"}]}, {"year": "1548", "text": "<PERSON>, Italian lawyer and jurist (b. 1502)", "html": "1548 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and jurist (b. 1502)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and jurist (b. 1502)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1603", "text": "<PERSON>, English minister and theologian (b. 1535)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English minister and theologian (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English minister and theologian (b. 1535)", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)"}]}, {"year": "1637", "text": "<PERSON>, Italian banker (b. 1564)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian banker (b. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian banker (b. 1564)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1641", "text": "<PERSON>, Dutch diplomat (b. 1572)[citation needed]", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch diplomat (b. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch diplomat (b. 1572)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1642", "text": "<PERSON>, Dutch bishop (b. 1585)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch bishop (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Graeff\"><PERSON></a>, Dutch bishop (b. 1585)", "links": [{"title": "<PERSON> op den Graeff", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1656", "text": "<PERSON>, English Jesuit missionary (b. 1579)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit)\" title=\"<PERSON> (Jesuit)\"><PERSON></a>, English Jesuit missionary (b. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit)\" title=\"<PERSON> (Jesuit)\"><PERSON></a>, English Jesuit missionary (b. 1579)", "links": [{"title": "<PERSON> (Jesuit)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit)"}]}, {"year": "1660", "text": "<PERSON><PERSON>, English politician (b. 1591)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bagot\"><PERSON><PERSON></a>, English politician (b. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bagot\"><PERSON><PERSON></a>, English politician (b. 1591)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1663", "text": "<PERSON> of France, Duchess of Savoy (b. 1606)[citation needed]", "html": "1663 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France\" title=\"Christine of France\"><PERSON> of France</a>, Duchess of <a href=\"https://wikipedia.org/wiki/Duchy_of_Savoy\" title=\"Duchy of Savoy\">Savoy</a> (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France\" title=\"Christine of France\"><PERSON> of France</a>, Duchess of <a href=\"https://wikipedia.org/wiki/Duchy_of_Savoy\" title=\"Duchy of Savoy\">Savoy</a> (b. 1606)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_France"}, {"title": "Duchy of Savoy", "link": "https://wikipedia.org/wiki/Duchy_of_Savoy"}]}, {"year": "1672", "text": "<PERSON>, French philosopher (b. 1618)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher (b. <a href=\"https://wikipedia.org/wiki/1618\" title=\"1618\">1618</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher (b. <a href=\"https://wikipedia.org/wiki/1618\" title=\"1618\">1618</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "1618", "link": "https://wikipedia.org/wiki/1618"}]}, {"year": "1683", "text": "<PERSON> of Savoy, Queen consort of Portugal (b. 1646)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Francis<PERSON>_of_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a>, <a href=\"https://wikipedia.org/wiki/Queen_consort_of_Portugal\" class=\"mw-redirect\" title=\"Queen consort of Portugal\">Queen consort of Portugal</a> (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a>, <a href=\"https://wikipedia.org/wiki/Queen_consort_of_Portugal\" class=\"mw-redirect\" title=\"Queen consort of Portugal\">Queen consort of Portugal</a> (b. 1646)", "links": [{"title": "<PERSON> of Savoy", "link": "https://wikipedia.org/wiki/Maria_Francisca_of_Savoy"}, {"title": "Queen consort of Portugal", "link": "https://wikipedia.org/wiki/Queen_consort_of_Portugal"}]}, {"year": "1689", "text": "<PERSON><PERSON><PERSON><PERSON>, English clergyman (b. 1622)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English clergyman (b. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English clergyman (b. 1622)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1693", "text": "<PERSON>, French prelate (b. 1621)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1693)\" title=\"<PERSON> (died 1693)\"><PERSON></a>, French prelate (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1693)\" title=\"<PERSON> (died 1693)\"><PERSON></a>, French prelate (b. 1621)", "links": [{"title": "<PERSON> (died 1693)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(died_1693)"}]}, {"year": "1694", "text": "<PERSON>, naval officer in the Dutch (b. 1634)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, naval officer in the Dutch (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, naval officer in the Dutch (b. 1634)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, Prussian field marshal and politician (b. 1635)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian field marshal and politician (b. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian field marshal and politician (b. 1635)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1707", "text": "<PERSON>, French monk and scholar (b. 1632)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French monk and scholar (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French monk and scholar (b. 1632)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1707", "text": "<PERSON>, 3rd Earl of Scarsdale, English earl, politician (b. 1654)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Scarsdale\" title=\"<PERSON>, 3rd Earl of Scarsdale\"><PERSON>, 3rd Earl of Scarsdale</a>, English earl, politician (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Scarsdale\" title=\"<PERSON>, 3rd Earl of Scarsdale\"><PERSON>, 3rd Earl of Scarsdale</a>, English earl, politician (b. 1654)", "links": [{"title": "<PERSON>, 3rd Earl of Scarsdale", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Scarsdale"}]}, {"year": "1737", "text": "<PERSON>, English printer (b. 1663)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(1663%E2%80%931737)\" title=\"<PERSON> (1663-1737)\"><PERSON></a>, English printer (b. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1663%E2%80%931737)\" title=\"<PERSON> (1663-1737)\"><PERSON></a>, English printer (b. 1663)", "links": [{"title": "<PERSON> (1663-1737)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1663%E2%80%931737)"}]}, {"year": "1743", "text": "<PERSON><PERSON><PERSON><PERSON>, French painter (b. 1659)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/Hyacinthe_Rigaud\" title=\"Hyacinthe Rigaud\"><PERSON><PERSON><PERSON><PERSON></a>, French painter (b. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hyacinthe_Rigaud\" title=\"Hyacinthe Rigaud\"><PERSON><PERSON><PERSON><PERSON></a>, French painter (b. 1659)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hyacinthe_Rigaud"}]}, {"year": "1771", "text": "<PERSON>, French engineer, invented the <PERSON><PERSON> tube (b. 1695)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer, invented the <a href=\"https://wikipedia.org/wiki/Pit<PERSON>_tube\" title=\"Pitot tube\">Pitot tube</a> (b. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer, invented the <a href=\"https://wikipedia.org/wiki/Pit<PERSON>_tube\" title=\"Pitot tube\">Pitot tube</a> (b. 1695)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pitot tube", "link": "https://wikipedia.org/wiki/Pitot_tube"}]}, {"year": "1776", "text": "<PERSON>, Hessian colonel (b. c. 1726)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hessian colonel (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1726</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hessian colonel (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1726</span>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON>, Lord <PERSON>, Scottish judge and philosopher (b. 1697)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON>\" title=\"Henry <PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish judge and philosopher (b. 1697)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON>\" title=\"Henry Home, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish judge and philosopher (b. 1697)", "links": [{"title": "Henry <PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1800", "text": "<PERSON>, Scottish minister and author (b. 1718)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and author (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and author (b. 1718)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, English religious leader (b. 1750)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English religious leader (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English religious leader (b. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, English essayist and poet (b. 1775)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist and poet (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist and poet (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, American soldier and politician (b. 1793)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, French pianist and composer (b. 1785)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Bo%C3%ABly\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Bo%C3%ABly\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Bo%C3%ABly"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Norwegian explorer (b. 1871)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian explorer (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian explorer (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eivind_Astrup"}]}, {"year": "1896", "text": "<PERSON>, English businessman and politician (b. 1816)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(industrialist)\" title=\"<PERSON> (industrialist)\"><PERSON></a>, English businessman and politician (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(industrialist)\" title=\"<PERSON> (industrialist)\"><PERSON></a>, English businessman and politician (b. 1816)", "links": [{"title": "<PERSON> (industrialist)", "link": "https://wikipedia.org/wiki/<PERSON>(industrialist)"}]}, {"year": "1900", "text": "<PERSON>, 1st Baron <PERSON>, English engineer and businessman, founded <PERSON> W<PERSON> (b. 1810)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>worth\"><PERSON></a> (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>worth\"><PERSON></a> (b. 1810)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>worth"}]}, {"year": "1914", "text": "<PERSON>, American chemist and engineer (b. 1863)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, American chemist and engineer (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, American chemist and engineer (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Russian-Greek composer and politician, Governor of Taganrog (b. 1846)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Greek composer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Taganrog\" title=\"Governor of Taganrog\">Governor of Taganrog</a> (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Greek composer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Taganrog\" title=\"Governor of Taganrog\">Governor of Taganrog</a> (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Achilles_Alferaki"}, {"title": "Governor of Taganrog", "link": "https://wikipedia.org/wiki/Governor_of_Taganrog"}]}, {"year": "1923", "text": "<PERSON><PERSON>, French architect and engineer, co-designed the Eiffel Tower (b. 1832)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French architect and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Eiffel_Tower\" title=\"Eiffel Tower\">Eiffel Tower</a> (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French architect and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Eiffel_Tower\" title=\"Eiffel Tower\">Eiffel Tower</a> (b. 1832)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Eiffel Tower", "link": "https://wikipedia.org/wiki/Eiffel_Tower"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Swedish nurse and healthcare activist (b. 1866)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish nurse and healthcare activist (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish nurse and healthcare activist (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ag<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Turkish poet, academic, and politician (b. 1873)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet, academic, and politician (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet, academic, and politician (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American geneticist and academic (b. 1889)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Polish-Russian poet and critic (b. 1891)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Russian poet and critic (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Russian poet and critic (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON><PERSON>_<PERSON>delstam"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American novelist, short story writer, and playwright (b. 1874)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Zona_Gale\" title=\"Zona Gale\"><PERSON><PERSON></a>, American novelist, short story writer, and playwright (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zona_Gale\" title=\"Zona Gale\"><PERSON><PERSON></a>, American novelist, short story writer, and playwright (b. 1874)", "links": [{"title": "Zona Gale", "link": "https://wikipedia.org/wiki/Zona_Gale"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, American painter (b. 1877)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_Cuneo\" title=\"<PERSON><PERSON><PERSON> C<PERSON>\"><PERSON><PERSON><PERSON></a>, American painter (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_C<PERSON>\" title=\"<PERSON><PERSON><PERSON> C<PERSON>\"><PERSON><PERSON><PERSON></a>, American painter (b. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Estonian general and politician, 10th Estonian Minister of War (b. 1887)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>ts_Kurvi<PERSON>\" title=\"<PERSON><PERSON> Ku<PERSON>\"><PERSON><PERSON></a>, Estonian general and politician, 10th <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_War\" class=\"mw-redirect\" title=\"Estonian Minister of War\">Estonian Minister of War</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ku<PERSON>\" title=\"<PERSON><PERSON> Ku<PERSON>\"><PERSON><PERSON></a>, Estonian general and politician, 10th <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_War\" class=\"mw-redirect\" title=\"Estonian Minister of War\">Estonian Minister of War</a> (b. 1887)", "links": [{"title": "Ants <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r<PERSON>ts"}, {"title": "Estonian Minister of War", "link": "https://wikipedia.org/wiki/Estonian_Minister_of_War"}]}, {"year": "1950", "text": "<PERSON>, German-American painter and sculptor (b. 1884)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and sculptor (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and sculptor (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Australian priest, author, and educator (b. 1878)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian priest, author, and educator (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian priest, author, and educator (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish soldier and politician, 6th Prime Minister of Turkey (b. 1887)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/%C5%9E%C3%BCkr%C3%BC_Saraco%C4%9Flu\" title=\"Şükrü Saracoğlu\"><PERSON>ük<PERSON><PERSON></a>, Turkish soldier and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%9E%C3%BCkr%C3%BC_Saraco%C4%9Flu\" title=\"Şükrü Saracoğlu\">Şükr<PERSON></a>, Turkish soldier and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1887)", "links": [{"title": "Şükrü Saracoğlu", "link": "https://wikipedia.org/wiki/%C5%9E%C3%BCkr%C3%BC_Saraco%C4%9Flu"}, {"title": "Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Turkey"}]}, {"year": "1953", "text": "<PERSON>, Polish poet and author (b. 1894)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish poet and author (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish poet and author (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English admiral, Victoria Cross recipient (b. 1881)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1956", "text": "<PERSON>, Irish priest and lexicographer (b. 1870)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and lexicographer (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and lexicographer (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, German painter (b. 1901)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian historian and politician, 14th Prime Minister of Canada, Nobel Prize laureate (b. 1897)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1974", "text": "<PERSON>, Russian physicist and mathematician (b. 1898)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and mathematician (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fock\"><PERSON></a>, Russian physicist and mathematician (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>ock"}]}, {"year": "1974", "text": "<PERSON>, American author (b. 1908)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1951)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1951)", "links": [{"title": "<PERSON> (American musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Algerian colonel and politician, 2nd President of Algeria (b. 1932)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Boumedie<PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian colonel and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Boumedie<PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian colonel and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON> Boume<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Boumediene"}, {"title": "President of Algeria", "link": "https://wikipedia.org/wiki/President_of_Algeria"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1937)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Afghan educator and politician, 2nd General Secretary of the People's Democratic Party of Afghanistan (b. 1929)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Afghan educator and politician, 2nd General Secretary of the <a href=\"https://wikipedia.org/wiki/People%27s_Democratic_Party_of_Afghanistan\" title=\"People's Democratic Party of Afghanistan\">People's Democratic Party of Afghanistan</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Afghan educator and politician, 2nd General Secretary of the <a href=\"https://wikipedia.org/wiki/People%27s_Democratic_Party_of_Afghanistan\" title=\"People's Democratic Party of Afghanistan\">People's Democratic Party of Afghanistan</a> (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>in"}, {"title": "People's Democratic Party of Afghanistan", "link": "https://wikipedia.org/wiki/People%27s_Democratic_Party_of_Afghanistan"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, pianist, and actor (b. 1899)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, pianist, and actor (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, pianist, and actor (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American pilot, astronaut, and politician (b. 1931)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, astronaut, and politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, astronaut, and politician (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, French racing driver (b. 1946)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, French racing driver (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, French racing driver (b. 1946)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1986", "text": "<PERSON>, English-American historian and journalist (b. 1904)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American historian and journalist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American historian and journalist (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American historian and author (b. 1892)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and author (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and author (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, New Zealand writer and political activist (b. 1897)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Rewi_Alley\" title=\"Rewi Alley\"><PERSON><PERSON> <PERSON></a>, New Zealand writer and political activist (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Rewi Alley\"><PERSON><PERSON> <PERSON></a>, New Zealand writer and political activist (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rewi_Alley"}]}, {"year": "1988", "text": "<PERSON>, American director and producer (b. 1929)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American novelist, poet, and educator (b. 1902)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and educator (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and educator (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Estonian chess player and philologist (b. 1902)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player and philologist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player and philologist (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Estonian footballer (b. 1911)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Belgian racing driver (b. 1918)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Pilette\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Pilette"}]}, {"year": "1994", "text": "<PERSON>, English author and critic (b. 1909)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fanny_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Filipino lawyer and jurist (b. 1902)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Filipino lawyer and jurist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Filipino lawyer and jurist (b. 1902)", "links": [{"title": "J. B<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Ukrainian-American pianist (b. 1909)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American pianist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American pianist (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Armenian chess player and composer (b. 1910)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian chess player and composer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian chess player and composer (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American journalist and essayist (b. 1914)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and essayist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and essayist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Northern Irish loyalist leader (b. 1960)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(loyalist)\" title=\"<PERSON> (loyalist)\"><PERSON></a>, Northern Irish loyalist leader (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(loyalist)\" title=\"<PERSON> (loyalist)\"><PERSON></a>, Northern Irish loyalist leader (b. 1960)", "links": [{"title": "<PERSON> (loyalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(loyalist)"}]}, {"year": "1999", "text": "<PERSON>, American author and screenwriter (b. 1950)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and screenwriter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and screenwriter (b. 1950)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "2002", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1921)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English actor (b. 1934)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Puerto Rican-American baseball player (b. 1962)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Calder%C3%B3n_(baseball)\" title=\"<PERSON><PERSON> (baseball)\"><PERSON><PERSON></a>, Puerto Rican-American baseball player (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Calder%C3%B3n_(baseball)\" title=\"<PERSON><PERSON> (baseball)\"><PERSON><PERSON></a>, Puerto Rican-American baseball player (b. 1962)", "links": [{"title": "<PERSON><PERSON> (baseball)", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_Calder%C3%B3n_(baseball)"}]}, {"year": "2004", "text": "<PERSON>, American guitarist (b. 1930)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Pakistani politician, Prime Minister of Pakistan (b. 1953)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Polish director and screenwriter (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish director and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish director and screenwriter (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Estonian author and poet (b. 1920)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and poet (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and poet (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1939)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Mexican-American sculptor (b. 1938)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, Mexican-American sculptor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, Mexican-American sculptor (b. 1938)", "links": [{"title": "<PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/<PERSON>(sculptor)"}]}, {"year": "2009", "text": "<PERSON>,  Ukrainian-Russian composer and educator (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian composer and educator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian composer and educator (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Brazilian footballer and manager (b. 1973)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Cat%C3%AA\" title=\"Cat<PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cat%C3%AA\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager (b. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cat%C3%AA"}]}, {"year": "2011", "text": "<PERSON>, English soldier, philosopher, and academic (b. 1925)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, philosopher, and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, philosopher, and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American painter and educator (b. 1928)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Canadian-American ice hockey player and coach (b. 1929)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1929)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "2012", "text": "<PERSON>, Jr., American actor, producer, and screenwriter (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor, producer, and screenwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor, producer, and screenwriter (b. 1921)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2012", "text": "<PERSON>, Jamaican singer, keyboard player, and producer (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cha<PERSON>\"><PERSON></a>, Jamaican singer, keyboard player, and producer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer, keyboard player, and producer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ers"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Chinese-American physicist and engineer (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-American physicist and engineer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-American physicist and engineer (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_Li"}]}, {"year": "2012", "text": "<PERSON>, Scottish astronomer and academic (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish astronomer and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish astronomer and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Jr., American general and engineer (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and engineer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and engineer (b. 1934)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2012", "text": "<PERSON>, American race car driver (b. 1947)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Walt<PERSON>\" title=\"<PERSON> Walther\"><PERSON></a>, American race car driver (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Walther\" title=\"Salt Walther\"><PERSON></a>, American race car driver (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salt_Walther"}]}, {"year": "2013", "text": "<PERSON>, English-Scottish biologist and academic (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish biologist and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish biologist and academic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Lebanese economist and politician, Lebanese Minister of Finance (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Lebanon)\" title=\"Ministry of Finance (Lebanon)\">Lebanese Minister of Finance</a> (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Lebanon)\" title=\"Ministry of Finance (Lebanon)\">Lebanese Minister of Finance</a> (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Finance (Lebanon)", "link": "https://wikipedia.org/wiki/Ministry_of_Finance_(Lebanon)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American soprano and educator (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27Angelo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soprano and educator (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27Angelo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soprano and educator (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gianna_D%27Angelo"}]}, {"year": "2013", "text": "<PERSON>, Canadian colonel, lawyer, and politician (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel, lawyer, and politician (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel, lawyer, and politician (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Indian actor, philanthropist and a popular television presenter (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sheikh\"><PERSON><PERSON><PERSON> <PERSON></a>, Indian actor, philanthropist and a popular television presenter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sheikh\"><PERSON><PERSON><PERSON> Sheikh</a>, Indian actor, philanthropist and a popular television presenter (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American-Israeli religious leader, founded the African Hebrew Israelites of Jerusalem (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>Ben-Israel\" title=\"Ben Ammi Ben-Israel\"><PERSON></a>, American-Israeli religious leader, founded the <a href=\"https://wikipedia.org/wiki/African_Hebrew_Israelites_of_Jerusalem\" class=\"mw-redirect\" title=\"African Hebrew Israelites of Jerusalem\">African Hebrew Israelites of Jerusalem</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>Ben-Israel\" title=\"Ben Ammi Ben-Israel\"><PERSON>Israel</a>, American-Israeli religious leader, founded the <a href=\"https://wikipedia.org/wiki/African_Hebrew_Israelites_of_Jerusalem\" class=\"mw-redirect\" title=\"African Hebrew Israelites of Jerusalem\">African Hebrew Israelites of Jerusalem</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Ben-Israel"}, {"title": "African Hebrew Israelites of Jerusalem", "link": "https://wikipedia.org/wiki/African_Hebrew_Israelites_of_Jerusalem"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Ecuadorian poet and academic (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/U<PERSON><PERSON>_E<PERSON>rella\" title=\"U<PERSON><PERSON> Estrella\"><PERSON><PERSON><PERSON></a>, Ecuadorian poet and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>\" title=\"U<PERSON><PERSON> Estrella\"><PERSON><PERSON><PERSON></a>, Ecuadorian poet and academic (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ulises_E<PERSON>rella"}]}, {"year": "2014", "text": "<PERSON>, Hong Kong accountant and businessman (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong accountant and businessman (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong accountant and businessman (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Belgian bacteriologist and politician (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian bacteriologist and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian bacteriologist and politician (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Norwegian-American skier (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American skier (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American skier (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American baseball player and sportscaster (b. 1958)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, American painter and sculptor (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>ls<PERSON>_Kelly\" title=\"Ells<PERSON> Kelly\"><PERSON><PERSON><PERSON></a>, American painter and sculptor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ells<PERSON>_Kelly\" title=\"Ellsworth Kelly\"><PERSON><PERSON><PERSON></a>, American painter and sculptor (b. 1923)", "links": [{"title": "Ellsworth Kelly", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kelly"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, American basketball player and minister (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lemon\" title=\"Meadowlark Lemon\"><PERSON><PERSON><PERSON></a>, American basketball player and minister (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lemon\" title=\"Meadowlar<PERSON> Lemon\"><PERSON><PERSON><PERSON></a>, American basketball player and minister (b. 1932)", "links": [{"title": "Meadowlark Lemon", "link": "https://wikipedia.org/wiki/Meadowlark_Lemon"}]}, {"year": "2015", "text": "<PERSON>, Salvadoran footballer (b. 1982)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran footballer (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran footballer (b. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English-Australian singer-songwriter (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_singer)\" class=\"mw-redirect\" title=\"<PERSON> (Australian singer)\"><PERSON></a>, English-Australian singer-songwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_singer)\" class=\"mw-redirect\" title=\"<PERSON> (Australian singer)\"><PERSON></a>, English-Australian singer-songwriter (b. 1947)", "links": [{"title": "<PERSON> (Australian singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_singer)"}]}, {"year": "2016", "text": "<PERSON>, American actress, screenwriter, author, producer, and speaker (b. 1956)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, screenwriter, author, producer, and speaker (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, screenwriter, author, producer, and speaker (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Sri Lankan politician (b. 1933)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Ratnas<PERSON>_Wickremanayake\" title=\"Ratnasiri Wickremanayake\"><PERSON><PERSON><PERSON> Wickrem<PERSON></a>, Sri Lankan politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ratnas<PERSON>_Wickremanayake\" title=\"Ratnasiri Wickremanayake\"><PERSON><PERSON><PERSON></a>, Sri Lankan politician (b. 1933)", "links": [{"title": "Ratnasiri Wickremanayake", "link": "https://wikipedia.org/wiki/Ratnasiri_Wickremanayake"}]}, {"year": "2018", "text": "<PERSON>, Polish resistance fighter (b. 1922)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish resistance fighter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish resistance fighter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American League of Legends player (b. 1995)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Remilia\" title=\"Re<PERSON><PERSON>\"><PERSON></a>, American <i>League of Legends</i> player (b. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Remilia\" title=\"Re<PERSON><PERSON>\"><PERSON></a>, American <i>League of Legends</i> player (b. 1995)", "links": [{"title": "Remilia", "link": "https://wikipedia.org/wiki/Remilia"}]}, {"year": "2023", "text": "<PERSON>, South Korean actor (b. 1975)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Austrian firearm engineer and founder of Glock (b. 1929)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian firearm engineer and founder of <a href=\"https://wikipedia.org/wiki/Glock_Ges.m.b.H.\" title=\"Glock Ges.m.b.H.\">Glock</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian firearm engineer and founder of <a href=\"https://wikipedia.org/wiki/Glock_Ges.m.b.H.\" title=\"Glock Ges.m.b.H.\"><PERSON><PERSON></a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>lock Ges.m.b.H.", "link": "https://wikipedia.org/wiki/Glock_Ges.m.b.H."}]}, {"year": "2024", "text": "<PERSON>, American sportscaster (b. 1946)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Argentinian-English actress (b. 1951)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-English actress (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-English actress (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American director, producer, and screenwriter (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}