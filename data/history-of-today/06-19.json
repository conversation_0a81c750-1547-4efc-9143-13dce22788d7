{"date": "June 19", "url": "https://wikipedia.org/wiki/June_19", "data": {"Events": [{"year": "325", "text": "The original Nicene Creed is adopted at the First Council of Nicaea.", "html": "325 - The original <a href=\"https://wikipedia.org/wiki/Nicene_Creed\" title=\"Nicene Creed\"><PERSON><PERSON></a> is adopted at the <a href=\"https://wikipedia.org/wiki/First_Council_of_Nicaea\" title=\"First Council of Nicaea\">First Council of Nicaea</a>.", "no_year_html": "The original <a href=\"https://wikipedia.org/wiki/Nicene_Creed\" title=\"Nicene Creed\"><PERSON><PERSON></a> is adopted at the <a href=\"https://wikipedia.org/wiki/First_Council_of_Nicaea\" title=\"First Council of Nicaea\">First Council of Nicaea</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "First Council of Nicaea", "link": "https://wikipedia.org/wiki/First_Council_of_Nicaea"}]}, {"year": "1179", "text": "The Battle of Kalvskinnet takes place outside Nidaros (now Trondheim), Norway. Earl <PERSON> is killed, and the battle changes the tide of the civil wars.", "html": "1179 - The Battle of Kalvskinnet takes place outside Nidaros (now <a href=\"https://wikipedia.org/wiki/Trondheim\" title=\"Trondheim\">Trondheim</a>), Norway. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Earl\">Earl</a> <a href=\"https://wikipedia.org/wiki/Erling_<PERSON>\" title=\"Erling <PERSON>\"><PERSON><PERSON><PERSON></a> is killed, and the battle changes the tide of the <a href=\"https://wikipedia.org/wiki/Civil_war_era_in_Norway\" title=\"Civil war era in Norway\">civil wars</a>.", "no_year_html": "The Battle of Kalvskinnet takes place outside Nidaros (now <a href=\"https://wikipedia.org/wiki/Trondheim\" title=\"Trondheim\">Trondheim</a>), Norway. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Earl\">Earl</a> <a href=\"https://wikipedia.org/wiki/Erling_<PERSON>\" title=\"Erling S<PERSON>ke\"><PERSON><PERSON><PERSON></a> is killed, and the battle changes the tide of the <a href=\"https://wikipedia.org/wiki/Civil_war_era_in_Norway\" title=\"Civil war era in Norway\">civil wars</a>.", "links": [{"title": "Trondheim", "link": "https://wikipedia.org/wiki/Trondheim"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Earl"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erling_<PERSON>ke"}, {"title": "Civil war era in Norway", "link": "https://wikipedia.org/wiki/Civil_war_era_in_Norway"}]}, {"year": "1306", "text": "The <PERSON> of Pembroke's army defeats <PERSON>'s Scottish army at the Battle of Methven.", "html": "1306 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_2nd_Earl_<PERSON>_Pembroke\" title=\"<PERSON><PERSON><PERSON> <PERSON>, 2nd Earl of Pembroke\">Earl of Pembroke</a>'s army defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> Scottish army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Methven\" title=\"Battle of Methven\">Battle of Methven</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_2nd_Earl_<PERSON>_Pembroke\" title=\"<PERSON><PERSON><PERSON> <PERSON>, 2nd Earl of Pembroke\">Earl of Pembroke</a>'s army defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> Scottish army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Methven\" title=\"Battle of Methven\">Battle of Methven</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>, 2nd Earl of Pembroke", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Methven", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>ven"}]}, {"year": "1586", "text": "English colonists leave Roanoke Island, after failing to establish England's first permanent settlement in North America.", "html": "1586 - English colonists leave <a href=\"https://wikipedia.org/wiki/Roanoke_Island\" title=\"Roanoke Island\">Roanoke Island</a>, after failing to establish <a href=\"https://wikipedia.org/wiki/Roanoke_Colony\" title=\"Roanoke Colony\">England's first permanent settlement</a> in North America.", "no_year_html": "English colonists leave <a href=\"https://wikipedia.org/wiki/Roanoke_Island\" title=\"Roanoke Island\">Roanoke Island</a>, after failing to establish <a href=\"https://wikipedia.org/wiki/Roanoke_Colony\" title=\"Roanoke Colony\">England's first permanent settlement</a> in North America.", "links": [{"title": "Roanoke Island", "link": "https://wikipedia.org/wiki/Roanoke_Island"}, {"title": "Roanoke Colony", "link": "https://wikipedia.org/wiki/Roanoke_Colony"}]}, {"year": "1718", "text": "At least 73,000 people died in the 1718 Tongwei-Gansu earthquake due to landslides in the Qing dynasty.", "html": "1718 - At least 73,000 people died in the <a href=\"https://wikipedia.org/wiki/1718_Tongwei%E2%80%93Gansu_earthquake\" title=\"1718 Tongwei-Gansu earthquake\">1718 Tongwei-Gansu earthquake</a> due to landslides in the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>.", "no_year_html": "At least 73,000 people died in the <a href=\"https://wikipedia.org/wiki/1718_Tongwei%E2%80%93Gansu_earthquake\" title=\"1718 Tongwei-Gansu earthquake\">1718 Tongwei-Gansu earthquake</a> due to landslides in the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>.", "links": [{"title": "1718 Tongwei-Gansu earthquake", "link": "https://wikipedia.org/wiki/1718_Tongwei%E2%80%93Gansu_earthquake"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}]}, {"year": "1785", "text": "The Boston King's Chapel adopts <PERSON>'s revised prayer book, without the Nicene Creed, establishing it as the first Unitarian congregation in the United States.", "html": "1785 - The Boston <a href=\"https://wikipedia.org/wiki/King%27s_Chapel\" title=\"King's Chapel\">King's Chapel</a> adopts <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(clergyman)\" title=\"<PERSON> (clergyman)\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Book_of_Common_Prayer_(Unitarian)\" title=\"Book of Common Prayer (Unitarian)\">revised prayer book</a>, without the Nicene Creed, establishing it as the first Unitarian congregation in the United States.", "no_year_html": "The Boston <a href=\"https://wikipedia.org/wiki/King%27s_Chapel\" title=\"King's Chapel\">King's Chapel</a> adopts <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(clergyman)\" title=\"<PERSON> (clergyman)\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Book_of_Common_Prayer_(Unitarian)\" title=\"Book of Common Prayer (Unitarian)\">revised prayer book</a>, without the Nicene Creed, establishing it as the first Unitarian congregation in the United States.", "links": [{"title": "King's Chapel", "link": "https://wikipedia.org/wiki/King%27s_Chapel"}, {"title": "<PERSON> (clergyman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clergyman)"}, {"title": "Book of Common Prayer (Unitarian)", "link": "https://wikipedia.org/wiki/Book_of_Common_Prayer_(Unitarian)"}]}, {"year": "1800", "text": "War of the Second Coalition Battle of Höchstädt results in a French victory over Austria.", "html": "1800 - <a href=\"https://wikipedia.org/wiki/War_of_the_Second_Coalition\" title=\"War of the Second Coalition\">War of the Second Coalition</a> <a href=\"https://wikipedia.org/wiki/Battle_of_H%C3%B6chst%C3%A4dt_(1800)\" title=\"Battle of Höchstädt (1800)\">Battle of Höchstädt</a> results in a French victory over Austria.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Second_Coalition\" title=\"War of the Second Coalition\">War of the Second Coalition</a> <a href=\"https://wikipedia.org/wiki/Battle_of_H%C3%B6chst%C3%A4dt_(1800)\" title=\"Battle of Höchstädt (1800)\">Battle of Höchstädt</a> results in a French victory over Austria.", "links": [{"title": "War of the Second Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Second_Coalition"}, {"title": "Battle of Höchstädt (1800)", "link": "https://wikipedia.org/wiki/Battle_of_H%C3%B6chst%C3%A4dt_(1800)"}]}, {"year": "1811", "text": "The Carlton House Fête is held in London to celebrate the establishment of the Regency era.", "html": "1811 - The <a href=\"https://wikipedia.org/wiki/Carlton_House_F%C3%AAte\" title=\"Carlton House Fête\">Carlton House Fête</a> is held in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> to celebrate the establishment of the <a href=\"https://wikipedia.org/wiki/Regency_era\" title=\"Regency era\">Regency era</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Carlton_House_F%C3%AAte\" title=\"Carlton House Fête\">Carlton House Fête</a> is held in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> to celebrate the establishment of the <a href=\"https://wikipedia.org/wiki/Regency_era\" title=\"Regency era\">Regency era</a>.", "links": [{"title": "Carlton House Fête", "link": "https://wikipedia.org/wiki/Carlton_House_F%C3%AAte"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}, {"title": "Regency era", "link": "https://wikipedia.org/wiki/Regency_era"}]}, {"year": "1816", "text": "Battle of Seven Oaks between North West Company and Hudson's Bay Company, near Winnipeg, Manitoba, Canada.", "html": "1816 - <a href=\"https://wikipedia.org/wiki/Battle_of_Seven_Oaks\" title=\"Battle of Seven Oaks\">Battle of Seven Oaks</a> between <a href=\"https://wikipedia.org/wiki/North_West_Company\" title=\"North West Company\">North West Company</a> and <a href=\"https://wikipedia.org/wiki/Hudson%27s_Bay_Company\" title=\"Hudson's Bay Company\">Hudson's Bay Company</a>, near <a href=\"https://wikipedia.org/wiki/Winnipeg\" title=\"Winnipeg\">Winnipeg</a>, <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a>, Canada.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Seven_Oaks\" title=\"Battle of Seven Oaks\">Battle of Seven Oaks</a> between <a href=\"https://wikipedia.org/wiki/North_West_Company\" title=\"North West Company\">North West Company</a> and <a href=\"https://wikipedia.org/wiki/Hudson%27s_Bay_Company\" title=\"Hudson's Bay Company\">Hudson's Bay Company</a>, near <a href=\"https://wikipedia.org/wiki/Winnipeg\" title=\"Winnipeg\">Winnipeg</a>, <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a>, Canada.", "links": [{"title": "Battle of Seven Oaks", "link": "https://wikipedia.org/wiki/Battle_of_Seven_Oaks"}, {"title": "North West Company", "link": "https://wikipedia.org/wiki/North_West_Company"}, {"title": "Hudson's Bay Company", "link": "https://wikipedia.org/wiki/Hudson%27s_Bay_Company"}, {"title": "Winnipeg", "link": "https://wikipedia.org/wiki/Winnipeg"}, {"title": "Manitoba", "link": "https://wikipedia.org/wiki/Manitoba"}]}, {"year": "1821", "text": "Decisive defeat of the Filiki Eteria by the Ottomans at Drăgășani (in Wallachia).", "html": "1821 - Decisive defeat of the <a href=\"https://wikipedia.org/wiki/Filiki_Eteria\" title=\"Filiki Eteria\">Filiki Eteria</a> by the Ottomans at <a href=\"https://wikipedia.org/wiki/Dr%C4%83g%C4%83%C8%99ani\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> (in <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a>).", "no_year_html": "Decisive defeat of the <a href=\"https://wikipedia.org/wiki/Filiki_Eteria\" title=\"Filiki Eteria\">Filiki Eteria</a> by the Ottomans at <a href=\"https://wikipedia.org/wiki/Dr%C4%83g%C4%83%C8%99ani\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> (in <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a>).", "links": [{"title": "Filiki Eteria", "link": "https://wikipedia.org/wiki/Filiki_Eteria"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dr%C4%83g%C4%83%C8%99ani"}, {"title": "Wallachia", "link": "https://wikipedia.org/wiki/Wallachia"}]}, {"year": "1846", "text": "The first officially recorded, organized baseball game is played under <PERSON>'s rules on Hoboken, New Jersey's Elysian Fields with the New York Base Ball Club defeating the Knickerbockers 23-1. <PERSON><PERSON><PERSON> umpired.", "html": "1846 - The first officially recorded, organized <a href=\"https://wikipedia.org/wiki/History_of_baseball_in_the_United_States\" title=\"History of baseball in the United States\">baseball</a> game is played under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s rules on <a href=\"https://wikipedia.org/wiki/Hoboken,_New_Jersey\" title=\"Hoboken, New Jersey\">Hoboken, New Jersey</a>'s <PERSON><PERSON> Fields with the New York Base Ball Club defeating the Knickerbockers 23-1. <PERSON><PERSON><PERSON> umpired.", "no_year_html": "The first officially recorded, organized <a href=\"https://wikipedia.org/wiki/History_of_baseball_in_the_United_States\" title=\"History of baseball in the United States\">baseball</a> game is played under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s rules on <a href=\"https://wikipedia.org/wiki/Hoboken,_New_Jersey\" title=\"Hoboken, New Jersey\">Hoboken, New Jersey</a>'s <PERSON><PERSON> Fields with the New York Base Ball Club defeating the Knickerbockers 23-1. <PERSON><PERSON><PERSON> umpired.", "links": [{"title": "History of baseball in the United States", "link": "https://wikipedia.org/wiki/History_of_baseball_in_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Hoboken, New Jersey", "link": "https://wikipedia.org/wiki/Hoboken,_New_Jersey"}]}, {"year": "1850", "text": "Princess <PERSON> of the Netherlands marries Crown Prince <PERSON> of Sweden-Norway.", "html": "1850 - Princess <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Netherlands\" title=\"<PERSON> of the Netherlands\"><PERSON> of the Netherlands</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Charles XV of Sweden\">Crown Prince <PERSON></a> of <a href=\"https://wikipedia.org/wiki/Sweden%E2%80%93Norway\" class=\"mw-redirect\" title=\"Sweden-Norway\">Sweden-Norway</a>.", "no_year_html": "Princess <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Netherlands\" title=\"<PERSON> of the Netherlands\"><PERSON> of the Netherlands</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Charles XV of Sweden\">Crown Prince <PERSON></a> of <a href=\"https://wikipedia.org/wiki/Sweden%E2%80%93Norway\" class=\"mw-redirect\" title=\"Sweden-Norway\">Sweden-Norway</a>.", "links": [{"title": "Louise of the Netherlands", "link": "https://wikipedia.org/wiki/<PERSON>_of_the_Netherlands"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Charles_XV_of_Sweden"}, {"title": "Sweden-Norway", "link": "https://wikipedia.org/wiki/Sweden%E2%80%93Norway"}]}, {"year": "1862", "text": "Congress prohibits slavery in all current and future United States territories, and President <PERSON> quickly signs the legislation.", "html": "1862 - Congress prohibits slavery in all current and future United States territories, and President <PERSON> quickly signs the legislation.", "no_year_html": "Congress prohibits slavery in all current and future United States territories, and President <PERSON> quickly signs the legislation.", "links": []}, {"year": "1865", "text": "Over two years after the Emancipation Proclamation, slaves in Galveston, Texas, United States, are officially informed of their freedom. The anniversary was officially celebrated in Texas and other states as Juneteenth. On June 17, 2021, Juneteenth officially became a federal holiday in the United States.", "html": "1865 - Over two years after the <a href=\"https://wikipedia.org/wiki/Emancipation_Proclamation\" title=\"Emancipation Proclamation\">Emancipation Proclamation</a>, slaves in <a href=\"https://wikipedia.org/wiki/Galveston,_Texas\" title=\"Galveston, Texas\">Galveston, Texas</a>, United States, are officially informed of their freedom. The anniversary was officially celebrated in <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> and other states as <a href=\"https://wikipedia.org/wiki/Juneteenth\" title=\"Juneteenth\">Juneteenth</a>. On June 17, 2021, Juneteenth officially became a <a href=\"https://wikipedia.org/wiki/Federal_holidays_in_the_United_States\" title=\"Federal holidays in the United States\">federal holiday in the United States</a>.", "no_year_html": "Over two years after the <a href=\"https://wikipedia.org/wiki/Emancipation_Proclamation\" title=\"Emancipation Proclamation\">Emancipation Proclamation</a>, slaves in <a href=\"https://wikipedia.org/wiki/Galveston,_Texas\" title=\"Galveston, Texas\">Galveston, Texas</a>, United States, are officially informed of their freedom. The anniversary was officially celebrated in <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> and other states as <a href=\"https://wikipedia.org/wiki/Juneteenth\" title=\"Juneteenth\">Juneteenth</a>. On June 17, 2021, Juneteenth officially became a <a href=\"https://wikipedia.org/wiki/Federal_holidays_in_the_United_States\" title=\"Federal holidays in the United States\">federal holiday in the United States</a>.", "links": [{"title": "Emancipation Proclamation", "link": "https://wikipedia.org/wiki/Emancipation_Proclamation"}, {"title": "Galveston, Texas", "link": "https://wikipedia.org/wiki/Galveston,_Texas"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "Juneteenth", "link": "https://wikipedia.org/wiki/Juneteenth"}, {"title": "Federal holidays in the United States", "link": "https://wikipedia.org/wiki/Federal_holidays_in_the_United_States"}]}, {"year": "1867", "text": "<PERSON> of the Second Mexican Empire is executed by a firing squad in Querétaro, Querétaro.", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Mexico\" title=\"Maximilian I of Mexico\"><PERSON> I</a> of the <a href=\"https://wikipedia.org/wiki/Second_Mexican_Empire\" title=\"Second Mexican Empire\">Second Mexican Empire</a> is executed by a <a href=\"https://wikipedia.org/wiki/Execution_by_firing_squad\" title=\"Execution by firing squad\">firing squad</a> in <a href=\"https://wikipedia.org/wiki/Quer%C3%A9taro,_Quer%C3%A9taro\" class=\"mw-redirect\" title=\"Querétaro, Querétaro\">Querétaro, Querétaro</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Mexico\" title=\"Maximilian I of Mexico\"><PERSON> I</a> of the <a href=\"https://wikipedia.org/wiki/Second_Mexican_Empire\" title=\"Second Mexican Empire\">Second Mexican Empire</a> is executed by a <a href=\"https://wikipedia.org/wiki/Execution_by_firing_squad\" title=\"Execution by firing squad\">firing squad</a> in <a href=\"https://wikipedia.org/wiki/Quer%C3%A9taro,_Quer%C3%A9taro\" class=\"mw-redirect\" title=\"Querétaro, Querétaro\">Querétaro, Querétaro</a>.", "links": [{"title": "<PERSON> I of Mexico", "link": "https://wikipedia.org/wiki/Maximilian_I_of_Mexico"}, {"title": "Second Mexican Empire", "link": "https://wikipedia.org/wiki/Second_Mexican_Empire"}, {"title": "Execution by firing squad", "link": "https://wikipedia.org/wiki/Execution_by_firing_squad"}, {"title": "Querétaro, Querétaro", "link": "https://wikipedia.org/wiki/Quer%C3%A9taro,_Quer%C3%A9taro"}]}, {"year": "1875", "text": "The Herzegovinian rebellion against the Ottoman Empire begins.", "html": "1875 - The <a href=\"https://wikipedia.org/wiki/Herzegovina_Uprising_(1875%E2%80%9377)\" class=\"mw-redirect\" title=\"Herzegovina Uprising (1875-77)\">Herzegovinian rebellion</a> against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Herzegovina_Uprising_(1875%E2%80%9377)\" class=\"mw-redirect\" title=\"Herzegovina Uprising (1875-77)\">Herzegovinian rebellion</a> against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> begins.", "links": [{"title": "Herzegovina Uprising (1875-77)", "link": "https://wikipedia.org/wiki/Herzegovina_Uprising_(1875%E2%80%9377)"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1903", "text": "<PERSON>, at the time a radical Socialist, is arrested by Bern police for advocating a violent general strike.", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, at the time a radical Socialist, is arrested by <a href=\"https://wikipedia.org/wiki/Bern\" title=\"Bern\">Bern</a> police for advocating a violent general strike.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benito <PERSON>\"><PERSON></a>, at the time a radical Socialist, is arrested by <a href=\"https://wikipedia.org/wiki/Bern\" title=\"Bern\">Bern</a> police for advocating a violent general strike.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bern", "link": "https://wikipedia.org/wiki/Bern"}]}, {"year": "1910", "text": "The first Father's Day is celebrated in Spokane, Washington.", "html": "1910 - The first <a href=\"https://wikipedia.org/wiki/Father%27s_Day\" title=\"Father's Day\">Father's Day</a> is celebrated in <a href=\"https://wikipedia.org/wiki/Spokane,_Washington\" title=\"Spokane, Washington\">Spokane, Washington</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Father%27s_Day\" title=\"Father's Day\">Father's Day</a> is celebrated in <a href=\"https://wikipedia.org/wiki/Spokane,_Washington\" title=\"Spokane, Washington\">Spokane, Washington</a>.", "links": [{"title": "Father's Day", "link": "https://wikipedia.org/wiki/Father%27s_Day"}, {"title": "Spokane, Washington", "link": "https://wikipedia.org/wiki/Spokane,_Washington"}]}, {"year": "1913", "text": "Natives Land Act, 1913 in South Africa implemented.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Natives_Land_Act,_1913\" title=\"Natives Land Act, 1913\">Natives Land Act, 1913</a> in South Africa implemented.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Natives_Land_Act,_1913\" title=\"Natives Land Act, 1913\">Natives Land Act, 1913</a> in South Africa implemented.", "links": [{"title": "Natives Land Act, 1913", "link": "https://wikipedia.org/wiki/Natives_Land_Act,_1913"}]}, {"year": "1921", "text": "The village of Knockcroghery, Ireland, is burned by British forces.", "html": "1921 - The village of <a href=\"https://wikipedia.org/wiki/Knockcroghery\" title=\"Knockcroghery\">Knockcroghery</a>, <a href=\"https://wikipedia.org/wiki/Ireland\" title=\"Ireland\">Ireland</a>, is burned by British forces.", "no_year_html": "The village of <a href=\"https://wikipedia.org/wiki/Knockcroghery\" title=\"Knockcroghery\">Knockcroghery</a>, <a href=\"https://wikipedia.org/wiki/Ireland\" title=\"Ireland\">Ireland</a>, is burned by British forces.", "links": [{"title": "Knockcroghery", "link": "https://wikipedia.org/wiki/Knockcroghery"}, {"title": "Ireland", "link": "https://wikipedia.org/wiki/Ireland"}]}, {"year": "1934", "text": "The Communications Act of 1934 establishes the United States' Federal Communications Commission (FCC).", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/Communications_Act_of_1934\" title=\"Communications Act of 1934\">Communications Act of 1934</a> establishes the United States' <a href=\"https://wikipedia.org/wiki/Federal_Communications_Commission\" title=\"Federal Communications Commission\">Federal Communications Commission</a> (FCC).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Communications_Act_of_1934\" title=\"Communications Act of 1934\">Communications Act of 1934</a> establishes the United States' <a href=\"https://wikipedia.org/wiki/Federal_Communications_Commission\" title=\"Federal Communications Commission\">Federal Communications Commission</a> (FCC).", "links": [{"title": "Communications Act of 1934", "link": "https://wikipedia.org/wiki/Communications_Act_of_1934"}, {"title": "Federal Communications Commission", "link": "https://wikipedia.org/wiki/Federal_Communications_Commission"}]}, {"year": "1943", "text": "The Philadelphia Eagles and Pittsburgh Steelers of the NFL merge for one season due to player shortages caused by World War II.", "html": "1943 - The <a href=\"https://wikipedia.org/wiki/Philadelphia_Eagles\" title=\"Philadelphia Eagles\">Philadelphia Eagles</a> and <a href=\"https://wikipedia.org/wiki/Pittsburgh_Steelers\" title=\"Pittsburgh Steelers\">Pittsburgh Steelers</a> of the <a href=\"https://wikipedia.org/wiki/NFL\" class=\"mw-redirect\" title=\"NFL\">NFL</a> <a href=\"https://wikipedia.org/wiki/Steagles\" title=\"Steagles\">merge for one season</a> due to player shortages caused by World War II.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Philadelphia_Eagles\" title=\"Philadelphia Eagles\">Philadelphia Eagles</a> and <a href=\"https://wikipedia.org/wiki/Pittsburgh_Steelers\" title=\"Pittsburgh Steelers\">Pittsburgh Steelers</a> of the <a href=\"https://wikipedia.org/wiki/NFL\" class=\"mw-redirect\" title=\"NFL\">NFL</a> <a href=\"https://wikipedia.org/wiki/Steagles\" title=\"Steagles\">merge for one season</a> due to player shortages caused by World War II.", "links": [{"title": "Philadelphia Eagles", "link": "https://wikipedia.org/wiki/Philadelphia_Eagles"}, {"title": "Pittsburgh Steelers", "link": "https://wikipedia.org/wiki/Pittsburgh_Steelers"}, {"title": "NFL", "link": "https://wikipedia.org/wiki/NFL"}, {"title": "St<PERSON>gles", "link": "https://wikipedia.org/wiki/Steagles"}]}, {"year": "1947", "text": "Pan Am Flight 121 crashes in the Syrian Desert near Mayadin, Syria, killing 15 and injuring 21.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_121\" title=\"Pan Am Flight 121\">Pan Am Flight 121</a> crashes in the <a href=\"https://wikipedia.org/wiki/Syrian_Desert\" title=\"Syrian Desert\">Syrian Desert</a> near <a href=\"https://wikipedia.org/wiki/<PERSON>din\" title=\"Mayadin\"><PERSON><PERSON></a>, Syria, killing 15 and injuring 21.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_121\" title=\"Pan Am Flight 121\">Pan Am Flight 121</a> crashes in the <a href=\"https://wikipedia.org/wiki/Syrian_Desert\" title=\"Syrian Desert\">Syrian Desert</a> near <a href=\"https://wikipedia.org/wiki/<PERSON>din\" title=\"Mayadin\"><PERSON><PERSON></a>, Syria, killing 15 and injuring 21.", "links": [{"title": "Pan Am Flight 121", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_121"}, {"title": "Syrian Desert", "link": "https://wikipedia.org/wiki/Syrian_Desert"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1953", "text": "Cold War: <PERSON> and <PERSON> are executed at Sing Sing, in New York.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> are executed at <a href=\"https://wikipedia.org/wiki/Sing_Sing\" title=\"Sing Sing\"><PERSON> Sing</a>, in New York.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> are executed at <a href=\"https://wikipedia.org/wiki/Sing_Sing\" title=\"Sing Sing\">Sing Sing</a>, in New York.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}, {"title": "Sing Sing", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "Charlotte Motor Speedway holds its first NASCAR race, the inaugural World 600.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Charlotte_Motor_Speedway\" title=\"Charlotte Motor Speedway\">Charlotte Motor Speedway</a> holds its first <a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> race, the inaugural <a href=\"https://wikipedia.org/wiki/World_600\" class=\"mw-redirect\" title=\"World 600\">World 600</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charlotte_Motor_Speedway\" title=\"Charlotte Motor Speedway\">Charlotte Motor Speedway</a> holds its first <a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> race, the inaugural <a href=\"https://wikipedia.org/wiki/World_600\" class=\"mw-redirect\" title=\"World 600\">World 600</a>.", "links": [{"title": "Charlotte Motor Speedway", "link": "https://wikipedia.org/wiki/Charlotte_Motor_Speedway"}, {"title": "NASCAR", "link": "https://wikipedia.org/wiki/NASCAR"}, {"title": "World 600", "link": "https://wikipedia.org/wiki/World_600"}]}, {"year": "1961", "text": "Kuwait declares independence from the United Kingdom.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a> declares independence from the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a> declares independence from the United Kingdom.", "links": [{"title": "Kuwait", "link": "https://wikipedia.org/wiki/Kuwait"}]}, {"year": "1964", "text": "The Civil Rights Act of 1964 is approved after surviving an 83-day filibuster in the United States Senate.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/Civil_Rights_Act_of_1964\" title=\"Civil Rights Act of 1964\">Civil Rights Act of 1964</a> is approved after surviving an 83-day <a href=\"https://wikipedia.org/wiki/Filibuster\" title=\"Filibuster\">filibuster</a> in the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Civil_Rights_Act_of_1964\" title=\"Civil Rights Act of 1964\">Civil Rights Act of 1964</a> is approved after surviving an 83-day <a href=\"https://wikipedia.org/wiki/Filibuster\" title=\"Filibuster\">filibuster</a> in the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a>.", "links": [{"title": "Civil Rights Act of 1964", "link": "https://wikipedia.org/wiki/Civil_Rights_Act_of_1964"}, {"title": "Filibuster", "link": "https://wikipedia.org/wiki/Filibuster"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON> becomes Prime Minister of South Vietnam at the head of a military junta; General <PERSON><PERSON><PERSON><PERSON> becomes the figurehead chief of state.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Cao_K%E1%BB%B3\" title=\"Nguyễ<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> becomes Prime Minister of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> at the head of a military junta; General <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87u\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON>uy<PERSON><PERSON></a> becomes the figurehead chief of state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Cao_K%E1%BB%B3\" title=\"Nguyễ<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> becomes Prime Minister of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> at the head of a military junta; General <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87u\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> becomes the figurehead chief of state.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Cao_K%E1%BB%B3"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87u"}]}, {"year": "1978", "text": "<PERSON>'s first comic strip, originally published locally as <PERSON> in 1976, goes into nationwide syndication.", "html": "1978 - <i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s</i> first <a href=\"https://wikipedia.org/wiki/Comic_strip\" title=\"Comic strip\">comic strip</a>, originally published locally as <i><PERSON></i> in 1976, goes into nationwide <a href=\"https://wikipedia.org/wiki/Print_syndication\" title=\"Print syndication\">syndication</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s</i> first <a href=\"https://wikipedia.org/wiki/Comic_strip\" title=\"Comic strip\">comic strip</a>, originally published locally as <i><PERSON></i> in 1976, goes into nationwide <a href=\"https://wikipedia.org/wiki/Print_syndication\" title=\"Print syndication\">syndication</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Garfield"}, {"title": "Comic strip", "link": "https://wikipedia.org/wiki/Comic_strip"}, {"title": "Print syndication", "link": "https://wikipedia.org/wiki/Print_syndication"}]}, {"year": "1985", "text": "Members of the Revolutionary Party of Central American Workers, dressed as Salvadoran soldiers, attack the Zona Rosa area of San Salvador.", "html": "1985 - Members of the <a href=\"https://wikipedia.org/wiki/Revolutionary_Party_of_Central_American_Workers\" title=\"Revolutionary Party of Central American Workers\">Revolutionary Party of Central American Workers</a>, dressed as <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">Salvadoran</a> soldiers, <a href=\"https://wikipedia.org/wiki/1985_Zona_Rosa_attacks\" title=\"1985 Zona Rosa attacks\">attack the Zona Rosa</a> area of <a href=\"https://wikipedia.org/wiki/San_Salvador\" title=\"San Salvador\">San Salvador</a>.", "no_year_html": "Members of the <a href=\"https://wikipedia.org/wiki/Revolutionary_Party_of_Central_American_Workers\" title=\"Revolutionary Party of Central American Workers\">Revolutionary Party of Central American Workers</a>, dressed as <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">Salvadoran</a> soldiers, <a href=\"https://wikipedia.org/wiki/1985_Zona_Rosa_attacks\" title=\"1985 Zona Rosa attacks\">attack the Zona Rosa</a> area of <a href=\"https://wikipedia.org/wiki/San_Salvador\" title=\"San Salvador\">San Salvador</a>.", "links": [{"title": "Revolutionary Party of Central American Workers", "link": "https://wikipedia.org/wiki/Revolutionary_Party_of_Central_American_Workers"}, {"title": "El Salvador", "link": "https://wikipedia.org/wiki/El_Salvador"}, {"title": "1985 Zona Rosa attacks", "link": "https://wikipedia.org/wiki/1985_Zona_Rosa_attacks"}, {"title": "San Salvador", "link": "https://wikipedia.org/wiki/San_Salvador"}]}, {"year": "1987", "text": "Basque separatist group ETA commits one of its most violent attacks, in which a bomb is set off in a supermarket, Hipercor, killing 21 and injuring 45.", "html": "1987 - Basque separatist group <a href=\"https://wikipedia.org/wiki/ETA_(separatist_group)\" title=\"ETA (separatist group)\">ETA</a> commits one of its <a href=\"https://wikipedia.org/wiki/1987_Hipercor_bombing\" class=\"mw-redirect\" title=\"1987 Hipercor bombing\">most violent attacks</a>, in which a bomb is set off in a supermarket, <a href=\"https://wikipedia.org/wiki/Hipercor\" title=\"Hipercor\">Hipercor</a>, killing 21 and injuring 45.", "no_year_html": "Basque separatist group <a href=\"https://wikipedia.org/wiki/ETA_(separatist_group)\" title=\"ETA (separatist group)\">ETA</a> commits one of its <a href=\"https://wikipedia.org/wiki/1987_Hipercor_bombing\" class=\"mw-redirect\" title=\"1987 Hipercor bombing\">most violent attacks</a>, in which a bomb is set off in a supermarket, <a href=\"https://wikipedia.org/wiki/Hipercor\" title=\"Hipercor\">Hipercor</a>, killing 21 and injuring 45.", "links": [{"title": "ETA (separatist group)", "link": "https://wikipedia.org/wiki/ETA_(separatist_group)"}, {"title": "1987 Hipercor bombing", "link": "https://wikipedia.org/wiki/1987_Hipercor_bombing"}, {"title": "Hipercor", "link": "https://wikipedia.org/wiki/Hipercor"}]}, {"year": "1987", "text": "Aeroflot Flight N-528 crashes at Berdiansk Airport in present-day Ukraine, killing eight people.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_N-528\" title=\"Aeroflot Flight N-528\">Aeroflot Flight N-528</a> crashes at <a href=\"https://wikipedia.org/wiki/Berdiansk_Airport\" title=\"Berdiansk Airport\">Berdiansk Airport</a> in present-day Ukraine, killing eight people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_N-528\" title=\"Aeroflot Flight N-528\">Aeroflot Flight N-528</a> crashes at <a href=\"https://wikipedia.org/wiki/Berdiansk_Airport\" title=\"Berdiansk Airport\">Berdiansk Airport</a> in present-day Ukraine, killing eight people.", "links": [{"title": "Aeroflot Flight N-528", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_N-528"}, {"title": "Berdiansk Airport", "link": "https://wikipedia.org/wiki/Berdiansk_Airport"}]}, {"year": "1988", "text": "<PERSON> <PERSON> II canonizes 117 Vietnamese Martyrs.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> canonizes <a href=\"https://wikipedia.org/wiki/Vietnamese_Martyrs\" title=\"Vietnamese Martyrs\">117 Vietnamese Martyrs</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> canonizes <a href=\"https://wikipedia.org/wiki/Vietnamese_Martyrs\" title=\"Vietnamese Martyrs\">117 Vietnamese Martyrs</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vietnamese Martyrs", "link": "https://wikipedia.org/wiki/Vietnamese_Martyrs"}]}, {"year": "1990", "text": "The current international law defending indigenous peoples, Indigenous and Tribal Peoples Convention, 1989, is ratified for the first time by Norway.", "html": "1990 - The current international law defending indigenous peoples, <a href=\"https://wikipedia.org/wiki/Indigenous_and_Tribal_Peoples_Convention,_1989\" title=\"Indigenous and Tribal Peoples Convention, 1989\">Indigenous and Tribal Peoples Convention, 1989</a>, is ratified for the first time by Norway.", "no_year_html": "The current international law defending indigenous peoples, <a href=\"https://wikipedia.org/wiki/Indigenous_and_Tribal_Peoples_Convention,_1989\" title=\"Indigenous and Tribal Peoples Convention, 1989\">Indigenous and Tribal Peoples Convention, 1989</a>, is ratified for the first time by Norway.", "links": [{"title": "Indigenous and Tribal Peoples Convention, 1989", "link": "https://wikipedia.org/wiki/Indigenous_and_Tribal_Peoples_Convention,_1989"}]}, {"year": "1990", "text": "The Communist Party of the Russian Soviet Federative Socialist Republic is founded in Moscow.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/Communist_Party_of_the_Russian_Soviet_Federative_Socialist_Republic\" title=\"Communist Party of the Russian Soviet Federative Socialist Republic\">Communist Party of the Russian Soviet Federative Socialist Republic</a> is founded in Moscow.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Communist_Party_of_the_Russian_Soviet_Federative_Socialist_Republic\" title=\"Communist Party of the Russian Soviet Federative Socialist Republic\">Communist Party of the Russian Soviet Federative Socialist Republic</a> is founded in Moscow.", "links": [{"title": "Communist Party of the Russian Soviet Federative Socialist Republic", "link": "https://wikipedia.org/wiki/Communist_Party_of_the_Russian_Soviet_Federative_Socialist_Republic"}]}, {"year": "1991", "text": "The last Soviet army units in Hungary are withdrawn.", "html": "1991 - The last <a href=\"https://wikipedia.org/wiki/Southern_Group_of_Forces\" title=\"Southern Group of Forces\">Soviet army units in Hungary</a> are withdrawn.", "no_year_html": "The last <a href=\"https://wikipedia.org/wiki/Southern_Group_of_Forces\" title=\"Southern Group of Forces\">Soviet army units in Hungary</a> are withdrawn.", "links": [{"title": "Southern Group of Forces", "link": "https://wikipedia.org/wiki/Southern_Group_of_Forces"}]}, {"year": "2005", "text": "Following a series of Michelin tire failures during the United States Grand Prix weekend at Indianapolis, and without an agreement being reached, 14 cars from seven teams in Michelin tires withdrew after completing the formation lap, leaving only six cars from three teams on Bridgestone tires to race.", "html": "2005 - Following a series of <a href=\"https://wikipedia.org/wiki/Michelin\" title=\"Michelin\">Michelin</a> <a href=\"https://wikipedia.org/wiki/Tire\" title=\"Tire\">tire</a> failures during the <a href=\"https://wikipedia.org/wiki/2005_United_States_Grand_Prix\" title=\"2005 United States Grand Prix\">United States Grand Prix weekend</a> at <a href=\"https://wikipedia.org/wiki/Indianapolis_Motor_Speedway\" title=\"Indianapolis Motor Speedway\">Indianapolis</a>, and without an agreement being reached, 14 cars from seven teams in Michelin tires withdrew after completing the formation lap, leaving only six cars from three teams on <a href=\"https://wikipedia.org/wiki/Bridgestone\" title=\"Bridgestone\">Bridgestone</a> tires to race.", "no_year_html": "Following a series of <a href=\"https://wikipedia.org/wiki/Michelin\" title=\"Michelin\">Michelin</a> <a href=\"https://wikipedia.org/wiki/Tire\" title=\"Tire\">tire</a> failures during the <a href=\"https://wikipedia.org/wiki/2005_United_States_Grand_Prix\" title=\"2005 United States Grand Prix\">United States Grand Prix weekend</a> at <a href=\"https://wikipedia.org/wiki/Indianapolis_Motor_Speedway\" title=\"Indianapolis Motor Speedway\">Indianapolis</a>, and without an agreement being reached, 14 cars from seven teams in Michelin tires withdrew after completing the formation lap, leaving only six cars from three teams on <a href=\"https://wikipedia.org/wiki/Bridgestone\" title=\"Bridgestone\">Bridgestone</a> tires to race.", "links": [{"title": "<PERSON>in", "link": "https://wikipedia.org/wiki/<PERSON>in"}, {"title": "Tire", "link": "https://wikipedia.org/wiki/Tire"}, {"title": "2005 United States Grand Prix", "link": "https://wikipedia.org/wiki/2005_United_States_Grand_Prix"}, {"title": "Indianapolis Motor Speedway", "link": "https://wikipedia.org/wiki/Indianapolis_Motor_Speedway"}, {"title": "Bridgestone", "link": "https://wikipedia.org/wiki/Bridgestone"}]}, {"year": "2007", "text": "The al-Khilani Mosque bombing in Baghdad leaves 78 people dead and another 218 injured.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/2007_al-Khilani_Mosque_bombing\" class=\"mw-redirect\" title=\"2007 al-Khilani Mosque bombing\">al-Khilani Mosque bombing</a> in Baghdad leaves 78 people dead and another 218 injured.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2007_al-Khilani_Mosque_bombing\" class=\"mw-redirect\" title=\"2007 al-Khilani Mosque bombing\">al-Khilani Mosque bombing</a> in Baghdad leaves 78 people dead and another 218 injured.", "links": [{"title": "2007 al-Khilani Mosque bombing", "link": "https://wikipedia.org/wiki/2007_al-Khilani_Mosque_bombing"}]}, {"year": "2009", "text": "Mass riots involving over 10,000 people and 10,000 police officers break out in Shishou, China, over the dubious circumstances surrounding the death of a local chef.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_incident\" title=\"Shishou incident\">Mass riots</a> involving over 10,000 people and 10,000 police officers break out in <a href=\"https://wikipedia.org/wiki/<PERSON>shou\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, China, over the dubious circumstances surrounding the death of a local chef.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_incident\" title=\"Shishou incident\">Mass riots</a> involving over 10,000 people and 10,000 police officers break out in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ou\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, China, over the dubious circumstances surrounding the death of a local chef.", "links": [{"title": "<PERSON><PERSON><PERSON> incident", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_incident"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2009", "text": "War in North-West Pakistan: The Pakistani Armed Forces open Operation Rah-<PERSON>-Nijat against the Taliban and other Islamist rebels in the South Waziristan area of the Federally Administered Tribal Areas.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/War_in_North-West_Pakistan\" class=\"mw-redirect\" title=\"War in North-West Pakistan\">War in North-West Pakistan</a>: The <a href=\"https://wikipedia.org/wiki/Pakistani_Armed_Forces\" class=\"mw-redirect\" title=\"Pakistani Armed Forces\">Pakistani Armed Forces</a> open <a href=\"https://wikipedia.org/wiki/Operation_Rah-e-Nijat\" title=\"Operation Rah-e-Nijat\">Operation Rah-e-Nijat</a> against the <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> and other <a href=\"https://wikipedia.org/wiki/Islamist\" class=\"mw-redirect\" title=\"Islamist\">Islamist</a> rebels in the <a href=\"https://wikipedia.org/wiki/South_Waziristan\" class=\"mw-redirect\" title=\"South Waziristan\">South Waziristan</a> area of the <a href=\"https://wikipedia.org/wiki/Federally_Administered_Tribal_Areas\" title=\"Federally Administered Tribal Areas\">Federally Administered Tribal Areas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_in_North-West_Pakistan\" class=\"mw-redirect\" title=\"War in North-West Pakistan\">War in North-West Pakistan</a>: The <a href=\"https://wikipedia.org/wiki/Pakistani_Armed_Forces\" class=\"mw-redirect\" title=\"Pakistani Armed Forces\">Pakistani Armed Forces</a> open <a href=\"https://wikipedia.org/wiki/Operation_Rah-e-Nijat\" title=\"Operation Rah-e-Nijat\">Operation Rah-e-Nijat</a> against the <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> and other <a href=\"https://wikipedia.org/wiki/Islamist\" class=\"mw-redirect\" title=\"Islamist\">Islamist</a> rebels in the <a href=\"https://wikipedia.org/wiki/South_Waziristan\" class=\"mw-redirect\" title=\"South Waziristan\">South Waziristan</a> area of the <a href=\"https://wikipedia.org/wiki/Federally_Administered_Tribal_Areas\" title=\"Federally Administered Tribal Areas\">Federally Administered Tribal Areas</a>.", "links": [{"title": "War in North-West Pakistan", "link": "https://wikipedia.org/wiki/War_in_North-West_Pakistan"}, {"title": "Pakistani Armed Forces", "link": "https://wikipedia.org/wiki/Pakistani_Armed_Forces"}, {"title": "Operation Rah-<PERSON><PERSON>Ni<PERSON>t", "link": "https://wikipedia.org/wiki/Operation_<PERSON><PERSON>-<PERSON>-<PERSON><PERSON>t"}, {"title": "Taliban", "link": "https://wikipedia.org/wiki/Taliban"}, {"title": "Islamist", "link": "https://wikipedia.org/wiki/Islamist"}, {"title": "South Waziristan", "link": "https://wikipedia.org/wiki/South_Waziristan"}, {"title": "Federally Administered Tribal Areas", "link": "https://wikipedia.org/wiki/Federally_Administered_Tribal_Areas"}]}, {"year": "2012", "text": "WikiLeaks founder <PERSON> requests asylum in London's Ecuadorian Embassy for fear of extradition to the US after publication of previously classified documents including footage of civilian killings by the US army.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a> founder <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> requests asylum in London's Ecuadorian Embassy for fear of <a href=\"https://wikipedia.org/wiki/Extradition\" title=\"Extradition\">extradition</a> to the US after publication of previously <a href=\"https://wikipedia.org/wiki/Classified_information\" title=\"Classified information\">classified documents</a> including footage of <a href=\"https://wikipedia.org/wiki/July_12,_2007,_Baghdad_airstrike\" title=\"July 12, 2007, Baghdad airstrike\">civilian killings by the US army</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a> founder <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> requests asylum in London's Ecuadorian Embassy for fear of <a href=\"https://wikipedia.org/wiki/Extradition\" title=\"Extradition\">extradition</a> to the US after publication of previously <a href=\"https://wikipedia.org/wiki/Classified_information\" title=\"Classified information\">classified documents</a> including footage of <a href=\"https://wikipedia.org/wiki/July_12,_2007,_Baghdad_airstrike\" title=\"July 12, 2007, Baghdad airstrike\">civilian killings by the US army</a>.", "links": [{"title": "WikiLeaks", "link": "https://wikipedia.org/wiki/WikiLeaks"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Extradition", "link": "https://wikipedia.org/wiki/Extradition"}, {"title": "Classified information", "link": "https://wikipedia.org/wiki/Classified_information"}, {"title": "July 12, 2007, Baghdad airstrike", "link": "https://wikipedia.org/wiki/July_12,_2007,_Baghdad_airstrike"}]}, {"year": "2018", "text": "The 10,000,000th United States Patent is issued.", "html": "2018 - The 10,000,000th United States <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">Patent</a> is issued.", "no_year_html": "The 10,000,000th United States <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">Patent</a> is issued.", "links": [{"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON> is fatally shot in East Pittsburgh by East Pittsburgh Police Officer <PERSON> after being involved in a near-fatal drive-by shooting.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Shooting_of_Ant<PERSON>_<PERSON>_Jr.\" class=\"mw-redirect\" title=\"Shooting of Ant<PERSON> Rose Jr.\">Antwon Rose II</a> is fatally shot in <a href=\"https://wikipedia.org/wiki/East_Pittsburgh\" class=\"mw-redirect\" title=\"East Pittsburgh\">East Pittsburgh</a> by East Pittsburgh Police Officer <PERSON> after being involved in a near-fatal drive-by shooting.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shooting_of_<PERSON><PERSON><PERSON>_<PERSON>_Jr.\" class=\"mw-redirect\" title=\"Shooting of <PERSON>t<PERSON> Rose Jr.\">An<PERSON><PERSON> Rose II</a> is fatally shot in <a href=\"https://wikipedia.org/wiki/East_Pittsburgh\" class=\"mw-redirect\" title=\"East Pittsburgh\">East Pittsburgh</a> by East Pittsburgh Police Officer <PERSON> after being involved in a near-fatal drive-by shooting.", "links": [{"title": "Shooting of <PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/Shooting_of_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "East Pittsburgh", "link": "https://wikipedia.org/wiki/East_Pittsburgh"}]}, {"year": "2020", "text": "Animal rights advocate <PERSON> is run over and killed by a transport truck outside of a pig slaughterhouse in Burlington, Ontario.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/2020\" title=\"2020\">2020</a> - Animal rights advocate <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>\" title=\"Death of <PERSON>\"><PERSON></a> is run over and killed by a transport truck outside of a pig slaughterhouse in Burlington, Ontario.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2020\" title=\"2020\">2020</a> - Animal rights advocate <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>\" title=\"Death of <PERSON>\"><PERSON></a> is run over and killed by a transport truck outside of a pig slaughterhouse in Burlington, Ontario.", "links": [{"title": "2020", "link": "https://wikipedia.org/wiki/2020"}, {"title": "Death of <PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>"}]}], "Births": [{"year": "1301", "text": "<PERSON>, shōgun of Japan (d. 1333)", "html": "1301 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, <i><a href=\"https://wikipedia.org/wiki/Sh%C5%8Dgun\" class=\"mw-redirect\" title=\"Shōgun\">shōgun</a></i> of Japan (d. 1333)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, <i><a href=\"https://wikipedia.org/wiki/Sh%C5%8Dgun\" class=\"mw-redirect\" title=\"Shōgun\">shōgun</a></i> of Japan (d. 1333)", "links": [{"title": "Prince <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sh%C5%8Dgun"}]}, {"year": "1417", "text": "<PERSON><PERSON><PERSON><PERSON>, lord of Rimini (d. 1468)", "html": "1417 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Mal<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, lord of <a href=\"https://wikipedia.org/wiki/Rimini\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 1468)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, lord of <a href=\"https://wikipedia.org/wiki/Rimini\" title=\"Rim<PERSON>\"><PERSON><PERSON><PERSON></a> (d. 1468)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rimini"}]}, {"year": "1566", "text": "<PERSON> and I of the United Kingdom (d. 1625)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_<PERSON>\" title=\"<PERSON> VI and I\"><PERSON> VI and I</a> of the United Kingdom (d. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_<PERSON>\" title=\"<PERSON> VI and I\"><PERSON> VI and I</a> of the United Kingdom (d. 1625)", "links": [{"title": "James <PERSON> and I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_I"}]}, {"year": "1590", "text": "<PERSON>, British colonial governor (d. 1678)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)\" class=\"mw-redirect\" title=\"<PERSON> (governor)\"><PERSON></a>, British colonial governor (d. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)\" class=\"mw-redirect\" title=\"<PERSON> (governor)\"><PERSON></a>, British colonial governor (d. 1678)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}]}, {"year": "1595", "text": "<PERSON><PERSON><PERSON><PERSON>, sixth Sikh guru (d. 1644)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>bin<PERSON>\" title=\"<PERSON>\">Hargobind</a>, sixth <a href=\"https://wikipedia.org/wiki/Sikh_gurus\" title=\"Sikh gurus\">Sikh guru</a> (d. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Hargobind</a>, sixth <a href=\"https://wikipedia.org/wiki/Sikh_gurus\" title=\"Sikh gurus\">Sikh guru</a> (d. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Sikh gurus", "link": "https://wikipedia.org/wiki/Sikh_gurus"}]}, {"year": "1598", "text": "<PERSON>, Archbishop of Canterbury (d. 1677)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Archbishop of Canterbury (d. 1677)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Archbishop of Canterbury (d. 1677)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1606", "text": "<PERSON>, 1st Duke of Hamilton, Scottish soldier and politician, Lord Chancellor of Scotland (d. 1649)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_<PERSON>_Hamilton\" title=\"<PERSON>, 1st Duke of Hamilton\"><PERSON>, 1st Duke of Hamilton</a>, Scottish soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland\" title=\"Lord Chancellor of Scotland\">Lord Chancellor of Scotland</a> (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Duke of Hamilton\"><PERSON>, 1st Duke of Hamilton</a>, Scottish soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland\" title=\"Lord Chancellor of Scotland\">Lord Chancellor of Scotland</a> (d. 1649)", "links": [{"title": "<PERSON>, 1st Duke of Hamilton", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lord Chancellor of Scotland", "link": "https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland"}]}, {"year": "1623", "text": "<PERSON><PERSON><PERSON>, French mathematician and physicist (d. 1662)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and physicist (d. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and physicist (d. 1662)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1633", "text": "<PERSON>, Dutch author and theologian (d. 1712)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and theologian (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and theologian (d. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1701", "text": "<PERSON>, French violinist and composer (d. 1775)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Rebel\" title=\"François Rebel\"><PERSON></a>, French violinist and composer (d. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Rebel\" title=\"François Rebel\"><PERSON></a>, French violinist and composer (d. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Rebel"}]}, {"year": "1731", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese sculptor (d. 1822)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese sculptor (d. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese sculptor (d. 1822)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, Uruguayan general and politician (d. 1850)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Gervasio_Artigas\" title=\"<PERSON>io Artiga<PERSON>\"><PERSON></a>, Uruguayan general and politician (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Gervasio_Artigas\" title=\"<PERSON> G<PERSON>vasio Artigas\"><PERSON></a>, Uruguayan general and politician (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Gervasio_Artigas"}]}, {"year": "1771", "text": "<PERSON>, French mathematician and philosopher (d. 1859)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "<PERSON>, American lawyer and politician (d. 1842)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(congressman)\" title=\"<PERSON> (congressman)\"><PERSON></a>, American lawyer and politician (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(congressman)\" title=\"<PERSON> (congressman)\"><PERSON></a>, American lawyer and politician (d. 1842)", "links": [{"title": "<PERSON> (congressman)", "link": "https://wikipedia.org/wiki/<PERSON>_(congressman)"}]}, {"year": "1783", "text": "<PERSON>, German chemist and pharmacist (d. 1841)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON>rner\" title=\"<PERSON>\"><PERSON></a>, German chemist and pharmacist (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON>rner\" title=\"<PERSON>\"><PERSON></a>, German chemist and pharmacist (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Friedrich_Sert%C3%BCrner"}]}, {"year": "1793", "text": "<PERSON>, American businessman and philanthropist (d. 1882)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON>, Scottish-English surgeon (d. 1860)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, Scottish-English surgeon (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, Scottish-English surgeon (d. 1860)", "links": [{"title": "<PERSON> (surgeon)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surgeon)"}]}, {"year": "1797", "text": "<PERSON>, Australian explorer  (d. 1873)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian explorer (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hamilton Hume\"><PERSON></a>, Australian explorer (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, Dutch-Canadian painter (d. 1872)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Canadian painter (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Canadian painter (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, American shipbuilder and philanthropist, founded the Webb Institute (d. 1899)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shipbuilder and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Webb_Institute\" title=\"Webb Institute\">Webb Institute</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shipbuilder and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Webb_Institute\" title=\"Webb Institute\">Webb Institute</a> (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Webb Institute", "link": "https://wikipedia.org/wiki/Webb_Institute"}]}, {"year": "1833", "text": "<PERSON>, American editorial writer, club-woman, philanthropist, and suffragette (d. 1904)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American editorial writer, club-woman, philanthropist, and suffragette (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American editorial writer, club-woman, philanthropist, and suffragette (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, English pastor and author (d. 1892)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pastor and author (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pastor and author (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, German entomologist and academic (d. 1917)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Georg <PERSON>\"><PERSON></a>, German entomologist and academic (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Georg <PERSON>\"><PERSON></a>, German entomologist and academic (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American philanthropist (d. 1929)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian journalist and politician (d. 1904)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A9ophas_Beausoleil\" title=\"Cléop<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A9ophas_Beausoleil\" title=\"C<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (d. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cl%C3%A9ophas_Beausoleil"}]}, {"year": "1846", "text": "<PERSON>, Italian astronomer and academic (d. 1928)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and academic (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and academic (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, American historian and politician, 24th United States Assistant Secretary of State (d. 1932)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician, 24th <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician, 24th <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Assistant Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State"}]}, {"year": "1851", "text": "<PERSON>, English-Australian cricketer (d. 1890)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>winter"}]}, {"year": "1851", "text": "<PERSON><PERSON>, English physicist, engineer, and academic (d. 1916)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Silva<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English physicist, engineer, and academic (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Silvanus P<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English physicist, engineer, and academic (d. 1916)", "links": [{"title": "Silvanus <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_P<PERSON>_Thompson"}]}, {"year": "1854", "text": "<PERSON>, Italian composer and academic (d. 1893)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and academic (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and academic (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish mathematician and theorist (d. 1933)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish mathematician and theorist (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish mathematician and theorist (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Mellin"}]}, {"year": "1855", "text": "<PERSON>, American lawyer and politician (d. 1917)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, American poet and librarian (d. 1911)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and librarian (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and librarian (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, 1st <PERSON>, Scottish-English field marshal (d. 1928)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, Scottish-English field marshal (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, Scottish-English field marshal (d. 1928)", "links": [{"title": "<PERSON>, 1st Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON>, French geologist and paleontologist (d. 1927)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Haug\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French geologist and paleontologist (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Haug\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French geologist and paleontologist (d. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Haug"}]}, {"year": "1861", "text": "<PERSON>, Filipino journalist, author, and poet (d. 1896)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Rizal\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist, author, and poet (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Rizal\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist, author, and poet (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Rizal"}]}, {"year": "1865", "text": "<PERSON>, English actress (d. 1948)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/May_Whit<PERSON>\" title=\"May Whit<PERSON>\">May <PERSON><PERSON><PERSON></a>, English actress (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_Whit<PERSON>\" title=\"May Whit<PERSON>\">May <PERSON><PERSON><PERSON></a>, English actress (d. 1948)", "links": [{"title": "May <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/May_Whitty"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, Hungarian hurdler, jumper, and physician (d. 1932)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian hurdler, jumper, and physician (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian hurdler, jumper, and physician (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alajos_Szokolyi"}]}, {"year": "1872", "text": "<PERSON>, English-American gardener and botanist (d. 1963)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American gardener and botanist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American gardener and botanist (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON>, Danish physicist and engineer (d. 1941)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Danish physicist and engineer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Danish physicist and engineer (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Scottish-English engineer (d. 1941)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English engineer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English engineer (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American actor (d. 1961)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, American illustrator (d. 1966)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American illustrator (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American illustrator (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American horse breeder (d. 1970)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American horse breeder (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Phipps\"><PERSON></a>, American horse breeder (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French painter and historian (d. 1974)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and historian (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and historian (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Des<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON>, American lawyer and politician (d. 1940)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 1940)", "links": [{"title": "<PERSON><PERSON> Hamilton", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Canadian soldier and pilot (d. 1970)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier and pilot (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier and pilot (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German photographer and activist (d. 1968)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer and activist (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer and activist (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, English journalist and politician (d. 1974)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and politician (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and politician (d. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American wife of <PERSON> (d. 1986)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VIII\"><PERSON></a> (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate (d. 1967)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Cyril <PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Cyril <PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1897", "text": "<PERSON>, American comedian (d. 1975)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Canadian-American violinist and bandleader (d. 1977)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American violinist and bandleader (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American violinist and bandleader (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American-French sculptor and academic (d. 1977)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French sculptor and academic (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French sculptor and academic (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American baseball player (d. 1941)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English cricketer and coach (d. 1965)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, German lawyer (d. 1938)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, American actress (d. 1994)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German-Irish biochemist and academic, Nobel Prize laureate (d. 1979)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"Ernst Boris <PERSON>\"><PERSON></a>, German-Irish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"Ernst <PERSON> Chain\"><PERSON></a>, German-Irish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Swedish footballer (d. 1975)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Knut_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German SS officer (d. 1984)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1907", "text": "<PERSON>, Canadian 10th General of the Salvation Army (d. 1985)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian 10th <a href=\"https://wikipedia.org/wiki/General_of_the_Salvation_Army\" class=\"mw-redirect\" title=\"General of the Salvation Army\">General of the Salvation Army</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian 10th <a href=\"https://wikipedia.org/wiki/General_of_the_Salvation_Army\" class=\"mw-redirect\" title=\"General of the Salvation Army\">General of the Salvation Army</a> (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General of the Salvation Army", "link": "https://wikipedia.org/wiki/General_of_the_Salvation_Army"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Japanese author (d. 1948)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Latvian basketball player (d. 1948)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/R%C5%ABdolfs_Jurci%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian basketball player (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C5%ABdolfs_Jurci%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian basketball player (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C5%ABdolfs_Jurci%C5%86%C5%A1"}]}, {"year": "1910", "text": "<PERSON>, English race car driver, founded the Allard Company (d. 1966)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Sydney_Allard\" title=\"Sydney Allard\"><PERSON> Allard</a>, English race car driver, founded the <a href=\"https://wikipedia.org/wiki/Allard_Motor_Company\" title=\"Allard Motor Company\">Allard Company</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Allard\" title=\"Sydney Allard\">Sydney Allard</a>, English race car driver, founded the <a href=\"https://wikipedia.org/wiki/Allard_Motor_Company\" title=\"Allard Motor Company\">Allard Company</a> (d. 1966)", "links": [{"title": "Sydney Allard", "link": "https://wikipedia.org/wiki/Sydney_Allard"}, {"title": "Allard Motor Company", "link": "https://wikipedia.org/wiki/Allard_Motor_Company"}]}, {"year": "1910", "text": "<PERSON>, American chemist and engineer, Nobel Prize laureate (d. 1985)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1910", "text": "<PERSON>, American lawyer and jurist (d. 1982)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>as\"><PERSON></a>, American lawyer and jurist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fortas\"><PERSON></a>, American lawyer and jurist (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Abe_Fortas"}]}, {"year": "1912", "text": "<PERSON>, American baseball player and manager (d. 2008)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American soprano and actress (d. 2005)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Virginia_MacWatters\" title=\"Virginia MacWatters\"><PERSON></a>, American soprano and actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_MacWatters\" title=\"Virginia MacWatters\"><PERSON></a>, American soprano and actress (d. 2005)", "links": [{"title": "Virginia MacWatters", "link": "https://wikipedia.org/wiki/Virginia_MacWatters"}]}, {"year": "1913", "text": "<PERSON><PERSON>, American swimmer (d. 1970)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American swimmer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American swimmer (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American journalist and politician (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American bluegrass singer-songwriter, guitarist, and mandolin player (d. 1979)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bluegrass singer-songwriter, guitarist, and mandolin player (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bluegrass singer-songwriter, guitarist, and mandolin player (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actor (d. 1994)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American publisher and agent (d. 2004)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and agent (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and agent (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Zimbabwean guerrilla leader and politician, Vice President of Zimbabwe (d. 1999)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean guerrilla leader and politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_Zimbabwe\" class=\"mw-redirect\" title=\"Vice President of Zimbabwe\">Vice President of Zimbabwe</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean guerrilla leader and politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_Zimbabwe\" class=\"mw-redirect\" title=\"Vice President of Zimbabwe\">Vice President of Zimbabwe</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of Zimbabwe", "link": "https://wikipedia.org/wiki/Vice_President_of_Zimbabwe"}]}, {"year": "1919", "text": "<PERSON>, American film critic (d. 2001)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film critic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film critic (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, French actor, director, and screenwriter (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French-American actor and singer (d. 2015)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor and singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor and singer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Danish physicist and academic, Nobel Prize laureate (d. 2009)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bohr\"><PERSON><PERSON></a>, Danish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bohr\"><PERSON><PERSON></a>, Danish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hr"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1922", "text": "<PERSON>, American educator and diplomat, 8th United States Ambassador to Togo  (d. 2022)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and diplomat, 8th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Togo\" class=\"mw-redirect\" title=\"United States Ambassador to Togo\">United States Ambassador to Togo</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and diplomat, 8th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Togo\" class=\"mw-redirect\" title=\"United States Ambassador to Togo\">United States Ambassador to Togo</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to Togo", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Togo"}]}, {"year": "1923", "text": "<PERSON>, Australian footballer and coach (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American mathematician and inventor", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mathematician and inventor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mathematician and inventor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Argentine general and human rights violator (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Men%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Argentine general and human rights violator (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Men%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Argentine general and human rights violator (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luciano_Benjam%C3%ADn_Men%C3%A9ndez"}]}, {"year": "1928", "text": "<PERSON>, American singer and guitarist (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and guitarist (d. 2020)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(musician)"}]}, {"year": "1928", "text": "<PERSON>, American actress (d. 2000)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American actress (d. 2024)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Soviet philosopher, psychologist, and author (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet philosopher, psychologist, and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet philosopher, psychologist, and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Italian actress, twin sister to <PERSON><PERSON> (d. 1971)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress, twin sister to <PERSON><PERSON> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress, twin sister to <PERSON><PERSON> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Angeli"}]}, {"year": "1932", "text": "<PERSON>, Spanish author and illustrator (d. 2011)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>chi<PERSON>_Grau\" title=\"<PERSON>\"><PERSON></a>, Spanish author and illustrator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Sanchis_Grau\" title=\"<PERSON>\"><PERSON></a>, Spanish author and illustrator (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>_Grau"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Italian actress, twin sister to <PERSON> (d. 2023)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress, twin sister to <PERSON> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress, twin sister to <PERSON> (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Kazakh engineer and astronaut (d. 1971)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakh engineer and astronaut (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakh engineer and astronaut (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Haitian politician, 12th Prime Minister of Haiti (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a> (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>e"}, {"title": "Prime Minister of Haiti", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Haiti"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American soprano and actress", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1937", "text": "<PERSON>, French philosopher and author (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Gluck<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Gluck<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>n"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, American football player and wrestler (d. 2002)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Wahoo_<PERSON>cDani<PERSON>\" title=\"Wahoo <PERSON>cDani<PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and wrestler (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wahoo_<PERSON>c<PERSON>ani<PERSON>\" title=\"Wahoo <PERSON>cDani<PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and wrestler (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wahoo_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, German footballer and manager (d. 2016)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>d_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American minister and theologian", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American minister and theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American minister and theologian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech economist and politician, 2nd President of the Czech Republic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech economist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Czech_Republic\" title=\"President of the Czech Republic\">President of the Czech Republic</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech economist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Czech_Republic\" title=\"President of the Czech Republic\">President of the Czech Republic</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of the Czech Republic", "link": "https://wikipedia.org/wiki/President_of_the_Czech_Republic"}]}, {"year": "1942", "text": "<PERSON><PERSON>, New Zealand director and producer (d. 2010)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ta\" title=\"Merata Mita\"><PERSON><PERSON></a>, New Zealand director and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mita\" title=\"Merata Mita\"><PERSON><PERSON></a>, New Zealand director and producer (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ta"}]}, {"year": "1944", "text": "<PERSON>, Brazilian singer, composer, writer and poet", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Chico_Buarque\" title=\"Chico Buarque\"><PERSON></a>, Brazilian singer, composer, writer and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chico_Buarque\" title=\"Chico Buarque\"><PERSON></a>, Brazilian singer, composer, writer and poet", "links": [{"title": "Chico Buarque", "link": "https://wikipedia.org/wiki/Chico_Buarque"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Serbian-Bosnian politician and convicted war criminal, 1st President of Republika Srpska", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d%C5%BEi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian-Bosnian politician and convicted war criminal, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Republika_Srpska\" class=\"mw-redirect\" title=\"List of Presidents of Republika Srpska\">President of Republika Srpska</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%BEi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian-Bosnian politician and convicted war criminal, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Republika_Srpska\" class=\"mw-redirect\" title=\"List of Presidents of Republika Srpska\">President of Republika Srpska</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Radovan_Karad%C5%BEi%C4%87"}, {"title": "List of Presidents of Republika Srpska", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Republika_Srpska"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Burmese politician, Nobel Prize laureate", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Aung_San_Su<PERSON>_<PERSON>\" title=\"Aung San Suu Kyi\"><PERSON>ng San Su<PERSON></a>, Burmese politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aung_San_<PERSON>_<PERSON>\" title=\"Aung San Suu Kyi\"><PERSON>ng San Suu K<PERSON></a>, Burmese politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "links": [{"title": "Aung San Suu Kyi", "link": "https://wikipedia.org/wiki/Aung_San_Suu_K<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1945", "text": "<PERSON>, American short story writer, memoirist, and novelist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, memoirist, and novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, memoirist, and novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, British keyboardist (d. 2002)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British keyboardist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British keyboardist (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English footballer and manager", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Indian-English novelist and essayist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Sal<PERSON>_Rushdie\" title=\"Salman Rushdie\"><PERSON><PERSON></a>, Indian-English novelist and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rushdie\" title=\"Salman Rushdie\"><PERSON><PERSON></a>, Indian-English novelist and essayist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian philosopher and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English singer-songwriter (d. 1974)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Drake\"><PERSON></a>, English singer-songwriter (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Phylici<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ylici<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Phylicia_<PERSON>d"}]}, {"year": "1950", "text": "<PERSON>, American archaeologist and historian", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and historian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and musician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Egyptian terrorist (d. 2022)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian terrorist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian terrorist (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Italian cyclist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Moser\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English politician, Secretary of State for Defence", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Defence\" title=\"Secretary of State for Defence\">Secretary of State for Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Defence\" title=\"Secretary of State for Defence\">Secretary of State for Defence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Defence", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Defence"}]}, {"year": "1954", "text": "<PERSON>, English lawyer and politician, Solicitor General for England and Wales", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales\" title=\"Solicitor General for England and Wales\">Solicitor General for England and Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales\" title=\"Solicitor General for England and Wales\">Solicitor General for England and Wales</a>", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(British_politician)"}, {"title": "Solicitor General for England and Wales", "link": "https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales"}]}, {"year": "1954", "text": "<PERSON>, American music producer and fraudster (d. 2016)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American music producer and fraudster (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American music producer and fraudster (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, New Zealand-Australian journalist and television presenter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_presenter)\" title=\"<PERSON> (TV presenter)\"><PERSON></a>, New Zealand-Australian journalist and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_presenter)\" title=\"<PERSON> (TV presenter)\"><PERSON></a>, New Zealand-Australian journalist and television presenter", "links": [{"title": "<PERSON> (TV presenter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_presenter)"}]}, {"year": "1955", "text": "<PERSON>, New Zealand runner", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, New Zealand runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, New Zealand runner", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Swedish politician, 39th Swedish Minister of Foreign Affairs (d. 2003)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, 39th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)\" title=\"Minister for Foreign Affairs (Sweden)\">Swedish Minister of Foreign Affairs</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, 39th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)\" title=\"Minister for Foreign Affairs (Sweden)\">Swedish Minister of Foreign Affairs</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)"}]}, {"year": "1957", "text": "<PERSON>, American journalist and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican insurgent and EZLN leader", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Subcomandante_Marcos\" title=\"Subcomandante Marcos\">Subcomanda<PERSON></a>, Mexican insurgent and <a href=\"https://wikipedia.org/wiki/EZLN\" class=\"mw-redirect\" title=\"EZLN\">EZLN</a> leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Subcomandante_Marcos\" title=\"Subcomandante Marcos\">Subcomanda<PERSON></a>, Mexican insurgent and <a href=\"https://wikipedia.org/wiki/EZLN\" class=\"mw-redirect\" title=\"EZLN\">EZLN</a> leader", "links": [{"title": "Subcom<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Subcomanda<PERSON>_<PERSON>"}, {"title": "EZLN", "link": "https://wikipedia.org/wiki/EZLN"}]}, {"year": "1958", "text": "<PERSON>, Russian-American ice hockey player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1958)\" title=\"<PERSON> (ice hockey, born 1958)\"><PERSON></a>, Russian-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1958)\" title=\"<PERSON> (ice hockey, born 1958)\"><PERSON></a>, Russian-American ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey, born 1958)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1958)"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter and trumpet player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and trumpet player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and trumpet player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, German lawyer and politician, 10th President of Germany", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Germany\" title=\"President of Germany\">President of Germany</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Germany\" title=\"President of Germany\">President of Germany</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Germany", "link": "https://wikipedia.org/wiki/President_of_Germany"}]}, {"year": "1960", "text": "<PERSON>, English economist and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American runner and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English guitarist, songwriter, and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American golfer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter, dancer, actress, and presenter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, actress, and presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, actress, and presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English tennis player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, English tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, English tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Indian actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>ish_Vidyarthi\" title=\"Ashish Vidyarthi\"><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ish_<PERSON>idyarthi\" title=\"Ashish Vidyarthi\"><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ashish_Vidyarthi"}]}, {"year": "1963", "text": "<PERSON>, American radio host and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Russian hurdler", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian hurdler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yo<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English rugby player, lieutenant, and pilot", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player, lieutenant, and pilot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player, lieutenant, and pilot", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American soccer player and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, former Prime Minister of the United Kingdom and former Mayor of London", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> and former <a href=\"https://wikipedia.org/wiki/Mayor_of_London\" title=\"Mayor of London\">Mayor of London</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> and former <a href=\"https://wikipedia.org/wiki/Mayor_of_London\" title=\"Mayor of London\">Mayor of London</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "Mayor of London", "link": "https://wikipedia.org/wiki/Mayor_of_London"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, German heptathlete", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German heptathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English actress and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey referee", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966]", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1966] - 1966] - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "1966] - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian skier and businessman", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B8rn_D%C3%A6hlie\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian skier and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B8rn_D%C3%A6hlie\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian skier and businessman", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B8rn_D%C3%A6hlie"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Australian footballer and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian footballer and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American philosopher and academic", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON> \"<PERSON>\" <PERSON>, American film and television actress (d. 2001)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON> \"<PERSON>\" <PERSON></a>, American film and television actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON> \"<PERSON><PERSON></a>, American film and television actress (d. 2001)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Indian politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American sprinter and football player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Watts"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Spanish footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1971)\" title=\"<PERSON> (footballer, born 1971)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1971)\" title=\"<PERSON> (footballer, born 1971)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1971)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1971)"}]}, {"year": "1972", "text": "<PERSON>, French actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Russian race walker", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian race walker", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American soccer player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Japanese singer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Bangladeshi member of parliament", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi member of parliament", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi member of parliament", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English actor and model", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American businessman, co-founded Foursquare", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Foursquare_(company)\" title=\"Foursquare (company)\">Foursquare</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Foursquare_(company)\" title=\"Foursquare (company)\">Foursquare</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Foursquare (company)", "link": "https://wikipedia.org/wiki/Foursquare_(company)"}]}, {"year": "1976", "text": "<PERSON>, English footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, German basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Zoe_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Dominican baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Brazilian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Kl%C3%A9berson\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Kl%C3%A9berson\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Kl%C3%A9berson"}]}, {"year": "1980", "text": "<PERSON>, Irish cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Irish cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Irish cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1980", "text": "<PERSON>, Scottish footballer and manager", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer_born_1980)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (footballer born 1980)\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer_born_1980)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (footballer born 1980)\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON> (footballer born 1980)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer_born_1980)"}]}, {"year": "1981", "text": "<PERSON>, Saudi Arabian long jumper", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, New Zealand swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Moss_Burmester\" title=\"Moss Burmester\"><PERSON></a>, New Zealand swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moss_Burmester\" title=\"Moss Burmester\"><PERSON></a>, New Zealand swimmer", "links": [{"title": "Moss Burmester", "link": "https://wikipedia.org/wiki/Moss_Burmester"}]}, {"year": "1982", "text": "<PERSON>, Russian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian motorcycle racer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Irish actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Dutch field hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Wieke_<PERSON>stra\" title=\"Wieke Dijkstra\"><PERSON><PERSON><PERSON></a>, Dutch field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wieke_<PERSON>\" title=\"Wieke Dijkstra\"><PERSON><PERSON><PERSON></a>, Dutch field hockey player", "links": [{"title": "<PERSON><PERSON><PERSON> Dijkstra", "link": "https://wikipedia.org/wiki/Wieke_Dijkstra"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Cypriot sport shooter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot sport shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot sport shooter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andri_Eleftheriou"}]}, {"year": "1985", "text": "<PERSON>, Japanese golfer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mi<PERSON>\"><PERSON></a>, Japanese golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Argentinian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Ethiopian runner", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>re Tune\"><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Tune\"><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ne"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Bulgarian sumo wrestler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Aoiyama_K%C5%8Dsuke\" title=\"<PERSON><PERSON><PERSON> Kōsuke\"><PERSON><PERSON><PERSON></a>, Bulgarian sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>yama_K%C5%8Dsuke\" title=\"<PERSON><PERSON><PERSON> Kōsuke\"><PERSON><PERSON><PERSON></a>, Bulgarian sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aoiyama_K%C5%8Dsuke"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Cuban pole vaulter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/L%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban pole vaulter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A1zaro_Borges"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ra<PERSON>rd Mendenhall\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>denhall\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>denhall"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Swedish sprinter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, South African-English cricketer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-English cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(linebacker)\" title=\"<PERSON><PERSON> <PERSON><PERSON> (linebacker)\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(linebacker)\" title=\"<PERSON><PERSON> <PERSON><PERSON> (linebacker)\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> (linebacker)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(linebacker)"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, English YouTuber", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (entertainer)\"><PERSON><PERSON><PERSON><PERSON></a>, English YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (entertainer)\"><PERSON><PERSON><PERSON><PERSON></a>, English YouTuber", "links": [{"title": "<PERSON><PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)"}]}, {"year": "1999", "text": "<PERSON>, American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Poole"}]}, {"year": "2004", "text": "<PERSON>, English actress", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "404", "text": "<PERSON><PERSON>, Jin-dynasty warlord and emperor of Huan Chu (b. 369)", "html": "404 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jin-dynasty warlord and emperor of Huan Chu (b. 369)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jin-dynasty warlord and emperor of Huan Chu (b. 369)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "626", "text": "<PERSON><PERSON> no <PERSON>, Japanese son of <PERSON><PERSON> <PERSON> (b. 551)", "html": "626 - <a href=\"https://wikipedia.org/wiki/Soga_no_Umako\" title=\"Soga no Umako\"><PERSON><PERSON> no Umako</a>, Japanese son of <a href=\"https://wikipedia.org/wiki/Soga_no_Iname\" title=\"Soga no Iname\">Soga no Iname</a> (b. 551)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soga_no_Umako\" title=\"Soga no Umako\"><PERSON><PERSON> no Umako</a>, Japanese son of <a href=\"https://wikipedia.org/wiki/Soga_no_Iname\" title=\"Soga no Iname\">Soga no Iname</a> (b. 551)", "links": [{"title": "Soga no Umako", "link": "https://wikipedia.org/wiki/Soga_no_Umako"}, {"title": "<PERSON>ga no Iname", "link": "https://wikipedia.org/wiki/Soga_no_Iname"}]}, {"year": "930", "text": "<PERSON>, chancellor of Later Liang (b. 862)", "html": "930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, chancellor of <a href=\"https://wikipedia.org/wiki/Later_Liang_(Five_Dynasties)\" title=\"<PERSON> Liang (Five Dynasties)\"><PERSON></a> (b. 862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Qing\" title=\"<PERSON> Qing\"><PERSON></a>, chancellor of <a href=\"https://wikipedia.org/wiki/Later_Liang_(Five_Dynasties)\" title=\"<PERSON> Liang (Five Dynasties)\"><PERSON></a> (b. 862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Liang (Five Dynasties)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Five_Dynasties)"}]}, {"year": "1027", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian mystic and saint (b. 951)", "html": "1027 - <a href=\"https://wikipedia.org/wiki/Romuald\" title=\"Romuald\"><PERSON><PERSON>ual<PERSON></a>, Italian mystic and saint (b. 951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Romuald\" title=\"Romuald\"><PERSON><PERSON>ual<PERSON></a>, Italian mystic and saint (b. 951)", "links": [{"title": "<PERSON><PERSON><PERSON>d", "link": "https://wikipedia.org/wiki/Romuald"}]}, {"year": "1185", "text": "<PERSON><PERSON>, Japanese soldier (b. 1147)", "html": "1185 - <a href=\"https://wikipedia.org/wiki/Taira_no_Mu<PERSON>mori\" title=\"Taira no Munemori\"><PERSON><PERSON> no <PERSON></a>, Japanese soldier (b. 1147)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taira_no_Mu<PERSON>mori\" title=\"Taira no Munemori\"><PERSON><PERSON> no <PERSON></a>, Japanese soldier (b. 1147)", "links": [{"title": "<PERSON>ra no Munemori", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>mori"}]}, {"year": "1282", "text": "<PERSON>, Welsh princess (b. 1252)", "html": "1282 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh princess (b. 1252)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh princess (b. 1252)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1312", "text": "<PERSON><PERSON>, 1st Earl of Cornwall, English politician (b. 1284)", "html": "1312 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_1st_Earl_of_Cornwall\" title=\"<PERSON><PERSON>, 1st Earl of Cornwall\"><PERSON><PERSON>, 1st Earl of Cornwall</a>, English politician (b. 1284)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_1st_Earl_of_Cornwall\" title=\"<PERSON><PERSON>, 1st Earl of Cornwall\"><PERSON><PERSON>, 1st Earl of Cornwall</a>, English politician (b. 1284)", "links": [{"title": "<PERSON><PERSON>, 1st Earl of Cornwall", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_1st_Earl_of_Cornwall"}]}, {"year": "1341", "text": "<PERSON>, Italian nun and saint (b. 1270)", "html": "1341 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian nun and saint (b. 1270)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian nun and saint (b. 1270)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1364", "text": "<PERSON><PERSON> of Montcada, queen consort and regent of Aragon  (b. 1292)", "html": "1364 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Montcada\" title=\"<PERSON><PERSON> of Montcada\"><PERSON><PERSON> of Montcada</a>, queen consort and regent of Aragon (b. 1292)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Montcada\" title=\"<PERSON><PERSON> of Montcada\"><PERSON><PERSON> of Montcada</a>, queen consort and regent of Aragon (b. 1292)", "links": [{"title": "Elisenda of Montcada", "link": "https://wikipedia.org/wiki/Elisenda_of_Montcada"}]}, {"year": "1504", "text": "<PERSON>, German astronomer and humanist (b. 1430)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and humanist (b. 1430)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and humanist (b. 1430)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1542", "text": "<PERSON>, Swiss theologian and reformer (b. 1482)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and reformer (b. 1482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and reformer (b. 1482)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1545", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian Lutheran lawyer and jurist (b. 1509)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian Lutheran lawyer and jurist (b. 1509)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian Lutheran lawyer and jurist (b. 1509)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1567", "text": "<PERSON> of Brandenburg, Duchess of Mecklenburg (b. 1507)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Brandenburg,_Duchess_of_Mecklenburg\" title=\"<PERSON> of Brandenburg, Duchess of Mecklenburg\"><PERSON> of Brandenburg, Duchess of Mecklenburg</a> (b. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Brandenburg,_Duchess_of_Mecklenburg\" title=\"<PERSON> of Brandenburg, Duchess of Mecklenburg\"><PERSON> of Brandenburg, Duchess of Mecklenburg</a> (b. 1507)", "links": [{"title": "<PERSON> of Brandenburg, Duchess of Mecklenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brandenburg,_Duchess_of_Mecklenburg"}]}, {"year": "1608", "text": "<PERSON><PERSON><PERSON>, Italian lawyer and jurist (b. 1551)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/Alberico_Gentili\" title=\"Alberico Gentili\"><PERSON><PERSON><PERSON></a>, Italian lawyer and jurist (b. 1551)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alberico_Gentili\" title=\"Alberico Gentili\"><PERSON><PERSON><PERSON></a>, Italian lawyer and jurist (b. 1551)", "links": [{"title": "Alberico Gentili", "link": "https://wikipedia.org/wiki/Alberico_Gentili"}]}, {"year": "1650", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss-German engraver and publisher (b. 1593)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/Matth%C3%A4<PERSON>_Merian\" class=\"mw-redirect\" title=\"<PERSON><PERSON>ä<PERSON> Me<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss-German engraver and publisher (b. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matth%C3%A4<PERSON>_Merian\" class=\"mw-redirect\" title=\"Matt<PERSON>ä<PERSON> Merian\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss-German engraver and publisher (b. 1593)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matth%C3%A4us_Merian"}]}, {"year": "1747", "text": "<PERSON>, Italian composer and educator (b. 1669)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1669)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1747", "text": "<PERSON><PERSON>, Persian leader (b. 1688)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Persian leader (b. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Persian leader (b. 1688)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, German organist and composer (b. 1702)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1702)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, American soldier and politician, 10th Colonial Governor of Maryland (b. 1690)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Sr<PERSON>\"><PERSON></a>, American soldier and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Maryland\" title=\"List of colonial governors of Maryland\">Colonial Governor of Maryland</a> (b. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sr<PERSON>\"><PERSON></a>, American soldier and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Maryland\" title=\"List of colonial governors of Maryland\">Colonial Governor of Maryland</a> (b. 1690)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "List of colonial governors of Maryland", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Maryland"}]}, {"year": "1786", "text": "<PERSON><PERSON>, American general (b. 1742)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general (b. 1742)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French painter and educator (b. 1724)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7<PERSON>_<PERSON>gren%C3%A9e\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter and educator (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A7ois_<PERSON>gren%C3%A9e\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter and educator (b. 1724)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7<PERSON>_<PERSON>gren%C3%A9e"}]}, {"year": "1820", "text": "<PERSON>, English botanist and author (b. 1743)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and author (b. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and author (b. 1743)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, French zoologist and biologist (b. 1772)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French zoologist and biologist (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French zoologist and biologist (b. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, English-Australian politician, 4th Premier of Victoria (b. 1822)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1864", "text": "<PERSON>, American soldier (b. 1843)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, Greek-Romanian businessman and philanthropist (b. 1800)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Romanian businessman and philanthropist (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Romanian businessman and philanthropist (b. 1800)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Unconstitutional president of Mexico, 1859-1860 (b. 1832)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Unconstitutional president of Mexico, 1859-1860 (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Unconstitutional president of Mexico, 1859-1860 (b. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n"}]}, {"year": "1867", "text": "<PERSON> of Mexico (b. 1832)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Mexico\" title=\"Maximilian I of Mexico\"><PERSON> of Mexico</a> (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Mexico\" title=\"<PERSON> I of Mexico\"><PERSON> of Mexico</a> (b. 1832)", "links": [{"title": "<PERSON> I of Mexico", "link": "https://wikipedia.org/wiki/Maximilian_I_of_Mexico"}]}, {"year": "1874", "text": "<PERSON>, Moravian palaeontologist and ornithologist (b. 1838)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moravian palaeontologist and ornithologist (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moravian palaeontologist and ornithologist (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_Stoliczka"}]}, {"year": "1884", "text": "<PERSON>, Argentinian-French politician and diplomat (b. 1810)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Juan <PERSON>\"><PERSON></a>, Argentinian-French politician and diplomat (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Juan <PERSON>\"><PERSON></a>, Argentinian-French politician and diplomat (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English cardinal (b. 1832)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (b. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Italian fighter pilot (b. 1888)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fighter pilot (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fighter pilot (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Mexican poet and author (b. 1888)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_L%C3%B3pez_Velarde\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and author (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_L%C3%B3pez_Velarde\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and author (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_L%C3%B3pez_Velarde"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 19th <PERSON><PERSON><PERSON><PERSON> (b. 1874)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Hitachiyama_Taniemon\" title=\"Hitachiyama Taniemon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 19th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hitachiyama_Taniemon\" title=\"Hitachiyama Taniemon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 19th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hitachiyama_<PERSON>mon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1932", "text": "<PERSON>, South African journalist and activist (b. 1876)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Sol_Plaatje\" title=\"Sol Plaatje\"><PERSON></a>, South African journalist and activist (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sol_Plaatje\" title=\"Sol Plaatje\"><PERSON></a>, South African journalist and activist (b. 1876)", "links": [{"title": "Sol <PERSON>", "link": "https://wikipedia.org/wiki/Sol_Plaatje"}]}, {"year": "1937", "text": "<PERSON><PERSON> <PERSON><PERSON>, Scottish novelist and playwright (b. 1860)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish novelist and playwright (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish novelist and playwright (b. 1860)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American social worker and activist (b. 1878)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social worker and activist (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social worker and activist (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, French composer and conductor (b. 1900)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON> <PERSON><PERSON>, Swiss botanist and anthropologist (b. 1862)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"C<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Swiss botanist and anthropologist (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Swiss botanist and anthropologist (b. 1862)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, German jurist and politician (b. 1885)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and politician (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and politician (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Indian philosopher and academic (b. 1885)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian philosopher and academic (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian philosopher and academic (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Greek poet and playwright (b. 1884)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek poet and playwright (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek poet and playwright (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American spy (b. 1915)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American spy (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American spy (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American spy (b. 1918)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American spy (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American spy (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American businessman (b. 1874)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American film director and actor (b. 1894)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director and actor (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director and actor (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor and comedian (b. 1886)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wynn"}]}, {"year": "1968", "text": "<PERSON>, American bishop (b. 1898)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Haitian writer (b. 1916)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian writer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian writer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American mob boss (b. 1908)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mob boss (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mob boss (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_<PERSON>cana"}]}, {"year": "1977", "text": "<PERSON>, Iranian sociologist and philosopher (b. 1933)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian sociologist and philosopher (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian sociologist and philosopher (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American explorer and scholar, founded Relationship counseling (b. 1888)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer and scholar, founded <a href=\"https://wikipedia.org/wiki/Relationship_counseling\" class=\"mw-redirect\" title=\"Relationship counseling\">Relationship counseling</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer and scholar, founded <a href=\"https://wikipedia.org/wiki/Relationship_counseling\" class=\"mw-redirect\" title=\"Relationship counseling\">Relationship counseling</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Relationship counseling", "link": "https://wikipedia.org/wiki/Relationship_counseling"}]}, {"year": "1981", "text": "<PERSON>, Chinese-American band manager (b. 1955)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American band manager (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American band manager (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Indian scientist and physician who created India's first, and the world's second, child using in-vitro fertilisation (b. 1931)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(physician)\" title=\"<PERSON><PERSON><PERSON> (physician)\"><PERSON><PERSON><PERSON></a>, Indian scientist and physician who created India's first, and the world's second, child using in-vitro fertilisation (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(physician)\" title=\"<PERSON><PERSON><PERSON> (physician)\"><PERSON><PERSON><PERSON></a>, Indian scientist and physician who created India's first, and the world's second, child using in-vitro fertilisation (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_(physician)"}]}, {"year": "1984", "text": "<PERSON>, American painter and educator (b. 1908)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player (b. 1963)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bias"}]}, {"year": "1987", "text": "<PERSON>, American author (b. 1896)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Canadian biochemist and academic (b. 1922)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian biochemist and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian biochemist and academic (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American lawyer and politician (b. 1918)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Estonian author and poet (b. 1906)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and poet (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and poet (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Alver"}]}, {"year": "1990", "text": "<PERSON>, American trade union leader, co-founded United Automobile Workers (b. 1911)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trade union leader, co-founded <a href=\"https://wikipedia.org/wiki/United_Automobile_Workers\" class=\"mw-redirect\" title=\"United Automobile Workers\">United Automobile Workers</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trade union leader, co-founded <a href=\"https://wikipedia.org/wiki/United_Automobile_Workers\" class=\"mw-redirect\" title=\"United Automobile Workers\">United Automobile Workers</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Automobile Workers", "link": "https://wikipedia.org/wiki/United_Automobile_Workers"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, New Zealand writer (b. 1905)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand writer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand writer (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actress (b. 1900)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, British novelist, playwright, and poet, Nobel Prize laureate (b. 1911)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, playwright, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, playwright, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1995", "text": "<PERSON>, Burmese-English captain and pilot (b. 1914)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, Burmese-English captain and pilot (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, Burmese-English captain and pilot (b. 1914)", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>(RAF_officer)"}]}, {"year": "2001", "text": "<PERSON>, American lawyer, jurist, and politician (b. 1912)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, jurist, and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, jurist, and politician (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Australian director and producer (b. 1916)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director and producer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director and producer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, journalist and newspaper editor (b. 1915)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>patrick\"><PERSON></a>, journalist and newspaper editor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Clayton Kirkpatrick\"><PERSON></a>, journalist and newspaper editor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Mexican singer-songwriter, actor, producer, and screenwriter (b. 1919)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter, actor, producer, and screenwriter (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter, actor, producer, and screenwriter (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Mexican-American painter and educator (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American painter and educator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American painter and educator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American football player and coach (b. 1947)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Israeli journalist and author (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Ze%27ev_Schiff\" title=\"<PERSON><PERSON>'ev Schiff\"><PERSON><PERSON>'ev <PERSON></a>, Israeli journalist and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ze%27ev_Schiff\" title=\"<PERSON><PERSON>'ev Schiff\"><PERSON><PERSON>'ev <PERSON></a>, Israeli journalist and author (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ze%27ev_Schiff"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Bengali journalist, founded <PERSON><PERSON> (b. 1934)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bengali journalist, founded <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Bartaman\"><PERSON><PERSON></a></i> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bengali journalist, founded <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Bart<PERSON>\"><PERSON><PERSON></a></i> (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aman"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Japanese engineer and surveyor (b. 1895)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese engineer and surveyor (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese engineer and surveyor (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Sudanese-American basketball player and activist (b. 1962)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bo<PERSON>\" title=\"Man<PERSON> Bol\"><PERSON><PERSON></a>, Sudanese-American basketball player and activist (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bol\"><PERSON><PERSON></a>, Sudanese-American basketball player and activist (b. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bol"}]}, {"year": "2010", "text": "<PERSON>, Baron <PERSON>, English philosopher and academic (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English philosopher and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English philosopher and academic (b. 1925)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Mexican writer, journalist and political activist (b. 1938)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1is\" title=\"<PERSON>\"><PERSON></a>, Mexican writer, journalist and political activist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1is\" title=\"<PERSON>\"><PERSON></a>, Mexican writer, journalist and political activist (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Monsiv%C3%A1is"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American soldier and politician, 32nd Governor of Nebraska (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_Nebraska\" class=\"mw-redirect\" title=\"Governor of Nebraska\">Governor of Nebraska</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_Nebraska\" class=\"mw-redirect\" title=\"Governor of Nebraska\">Governor of Nebraska</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Nebraska", "link": "https://wikipedia.org/wiki/Governor_of_Nebraska"}]}, {"year": "2013", "text": "<PERSON>, American author (b. 1966)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actor (b. 1961)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Hungarian politician, 37th Prime Minister of Hungary (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian politician, 37th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian politician, 37th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1932)", "links": [{"title": "G<PERSON><PERSON> Horn", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Horn"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "2013", "text": "<PERSON>, American football player and sportscaster (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster (b. 1952)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Czech singer-songwriter and pianist  (b. 1965)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech singer-songwriter and pianist (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech singer-songwriter and pianist (b. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Filip_<PERSON>ol"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, German general (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German general (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German general (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American songwriter (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Ivorian footballer (b. 1985)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_(footballer,_born_1985)\" title=\"<PERSON> (footballer, born 1985)\"><PERSON></a>, Ivorian footballer (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ibrahim_<PERSON>%C3%A9_(footballer,_born_1985)\" title=\"<PERSON> (footballer, born 1985)\"><PERSON></a>, Ivorian footballer (b. 1985)", "links": [{"title": "<PERSON> (footballer, born 1985)", "link": "https://wikipedia.org/wiki/Ibrahim_Tour%C3%A9_(footballer,_born_1985)"}]}, {"year": "2015", "text": "<PERSON>, American novelist and short-story writer (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short-story writer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short-story writer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1989)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American college student detained in North Korea (b. 1994)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American college student detained in North Korea (b. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American college student detained in North Korea (b. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Otto_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, western lowland gorilla and user of American Sign Language (b. 1971)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(gorilla)\" title=\"Koko (gorilla)\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Western_lowland_gorilla\" title=\"Western lowland gorilla\">western lowland gorilla</a> and user of <a href=\"https://wikipedia.org/wiki/American_Sign_Language\" title=\"American Sign Language\">American Sign Language</a> (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(gorilla)\" title=\"<PERSON><PERSON> (gorilla)\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Western_lowland_gorilla\" title=\"Western lowland gorilla\">western lowland gorilla</a> and user of <a href=\"https://wikipedia.org/wiki/American_Sign_Language\" title=\"American Sign Language\">American Sign Language</a> (b. 1971)", "links": [{"title": "<PERSON><PERSON> (gorilla)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(gorilla)"}, {"title": "Western lowland gorilla", "link": "https://wikipedia.org/wiki/Western_lowland_gorilla"}, {"title": "American Sign Language", "link": "https://wikipedia.org/wiki/American_Sign_Language"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, American YouTuber and streamer (b. 1990)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Etika\" title=\"Etika\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/YouTuber\" title=\"YouTuber\">YouTuber</a> and <a href=\"https://wikipedia.org/wiki/Online_streamer\" title=\"Online streamer\">streamer</a> (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Etika\" title=\"Etika\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/YouTuber\" title=\"YouTuber\">YouTuber</a> and <a href=\"https://wikipedia.org/wiki/Online_streamer\" title=\"Online streamer\">streamer</a> (b. 1990)", "links": [{"title": "Etika", "link": "https://wikipedia.org/wiki/Etika"}, {"title": "YouTuber", "link": "https://wikipedia.org/wiki/YouTuber"}, {"title": "Online streamer", "link": "https://wikipedia.org/wiki/Online_streamer"}]}]}}