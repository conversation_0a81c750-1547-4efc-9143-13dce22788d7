{"date": "May 3", "url": "https://wikipedia.org/wiki/May_3", "data": {"Events": [{"year": "752", "text": "Mayan king <PERSON> of Yaxchilan in modern-day Chiapas, Mexico, assumes the throne.", "html": "752 - Mayan king <a href=\"https://wikipedia.org/wiki/Yaxun_B%CA%BCalam_IV\" title=\"Yaxun Bʼalam IV\">Bird Jaguar IV</a> of <a href=\"https://wikipedia.org/wiki/Yaxchilan\" title=\"Yax<PERSON><PERSON>\">Yax<PERSON><PERSON></a> in modern-day <a href=\"https://wikipedia.org/wiki/Chiapas\" title=\"Chiapas\">Chiapas</a>, Mexico, assumes the throne.", "no_year_html": "Mayan king <a href=\"https://wikipedia.org/wiki/Yaxun_B%CA%BCalam_IV\" title=\"Yaxun Bʼalam IV\">Bird Jaguar IV</a> of <a href=\"https://wikipedia.org/wiki/Yaxchilan\" title=\"Yaxchi<PERSON>\">Yax<PERSON><PERSON></a> in modern-day <a href=\"https://wikipedia.org/wiki/Chiapas\" title=\"Chiapas\">Chiapas</a>, Mexico, assumes the throne.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yaxun_B%CA%BCalam_IV"}, {"title": "Yaxchilan", "link": "https://wikipedia.org/wiki/Yaxchilan"}, {"title": "Chiapas", "link": "https://wikipedia.org/wiki/Chiapas"}]}, {"year": "1481", "text": "The largest of three earthquakes strikes the island of Rhodes and causes an estimated 30,000 casualties.", "html": "1481 - The <a href=\"https://wikipedia.org/wiki/1481_Rhodes_earthquake\" title=\"1481 Rhodes earthquake\">largest of three earthquakes</a> strikes the island of <a href=\"https://wikipedia.org/wiki/Rhodes\" title=\"Rhodes\">Rhodes</a> and causes an estimated 30,000 casualties.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1481_Rhodes_earthquake\" title=\"1481 Rhodes earthquake\">largest of three earthquakes</a> strikes the island of <a href=\"https://wikipedia.org/wiki/Rhodes\" title=\"Rhodes\">Rhodes</a> and causes an estimated 30,000 casualties.", "links": [{"title": "1481 Rhodes earthquake", "link": "https://wikipedia.org/wiki/1481_Rhodes_earthquake"}, {"title": "Rhodes", "link": "https://wikipedia.org/wiki/Rhodes"}]}, {"year": "1491", "text": "Kongo monarch <PERSON><PERSON><PERSON> is baptised by Portuguese missionaries, adopting the baptismal name of <PERSON>.", "html": "1491 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kongo\" title=\"Kingdom of Kongo\">Kong<PERSON></a> monarch <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is baptised by <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a> missionaries, adopting the baptismal name of <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_I_of_Kongo\" title=\"João I of Kongo\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_Kongo\" title=\"Kingdom of Kongo\">Kong<PERSON></a> monarch <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Nzinga\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is baptised by <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a> missionaries, adopting the baptismal name of <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_I_of_Kongo\" title=\"<PERSON> of Kongo\"><PERSON></a>.", "links": [{"title": "Kingdom of Kongo", "link": "https://wikipedia.org/wiki/Kingdom_of_Kongo"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Portuguese Empire", "link": "https://wikipedia.org/wiki/Portuguese_Empire"}, {"title": "<PERSON> of Kongo", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_I_of_Kongo"}]}, {"year": "1568", "text": "Angered by the brutal onslaught of Spanish troops at Fort Caroline, a French force burns the San Mateo fort and massacres hundreds of Spaniards.", "html": "1568 - Angered by the brutal onslaught of Spanish troops at <a href=\"https://wikipedia.org/wiki/Fort_Caroline\" title=\"Fort Caroline\">Fort Caroline</a>, a French force burns the San Mateo fort and massacres hundreds of Spaniards.", "no_year_html": "Angered by the brutal onslaught of Spanish troops at <a href=\"https://wikipedia.org/wiki/Fort_Caroline\" title=\"Fort Caroline\">Fort Caroline</a>, a French force burns the San Mateo fort and massacres hundreds of Spaniards.", "links": [{"title": "Fort Caroline", "link": "https://wikipedia.org/wiki/Fort_Caroline"}]}, {"year": "1616", "text": "Treaty of Loudun ends a French civil war.", "html": "1616 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Loudun\" title=\"Treaty of Loudun\">Treaty of Loudun</a> ends a French civil war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Loudun\" title=\"Treaty of Loudun\">Treaty of Loudun</a> ends a French civil war.", "links": [{"title": "Treaty of Loudun", "link": "https://wikipedia.org/wiki/Treaty_of_Loudun"}]}, {"year": "1715", "text": "A total solar eclipse is visible across northern Europe and northern Asia, as predicted by <PERSON> to within four minutes accuracy.", "html": "1715 - A <a href=\"https://wikipedia.org/wiki/Solar_eclipse_of_May_3,_1715\" title=\"Solar eclipse of May 3, 1715\">total solar eclipse</a> is visible across northern Europe and northern Asia, as predicted by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to within four minutes accuracy.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Solar_eclipse_of_May_3,_1715\" title=\"Solar eclipse of May 3, 1715\">total solar eclipse</a> is visible across northern Europe and northern Asia, as predicted by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to within four minutes accuracy.", "links": [{"title": "Solar eclipse of May 3, 1715", "link": "https://wikipedia.org/wiki/Solar_eclipse_of_May_3,_1715"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1791", "text": "The Constitution of May 3 (the first modern constitution in Europe) is proclaimed by the Sejm of Polish-Lithuanian Commonwealth.", "html": "1791 - The <a href=\"https://wikipedia.org/wiki/Constitution_of_May_3,_1791\" class=\"mw-redirect\" title=\"Constitution of May 3, 1791\">Constitution of May 3</a> (the first modern <a href=\"https://wikipedia.org/wiki/Constitution\" title=\"Constitution\">constitution</a> in Europe) is proclaimed by the <a href=\"https://wikipedia.org/wiki/Sejm\" title=\"Sejm\">Sejm</a> of <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Constitution_of_May_3,_1791\" class=\"mw-redirect\" title=\"Constitution of May 3, 1791\">Constitution of May 3</a> (the first modern <a href=\"https://wikipedia.org/wiki/Constitution\" title=\"Constitution\">constitution</a> in Europe) is proclaimed by the <a href=\"https://wikipedia.org/wiki/Sejm\" title=\"Sejm\">Sejm</a> of <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a>.", "links": [{"title": "Constitution of May 3, 1791", "link": "https://wikipedia.org/wiki/Constitution_of_May_3,_1791"}, {"title": "Constitution", "link": "https://wikipedia.org/wiki/Constitution"}, {"title": "Sejm", "link": "https://wikipedia.org/wiki/Sejm"}, {"title": "Polish-Lithuanian Commonwealth", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth"}]}, {"year": "1802", "text": "Washington, D.C. is incorporated as a city after Congress abolishes the Board of Commissioners, the District's founding government. The \"City of Washington\" is given a mayor-council form of government.", "html": "1802 - <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a> is incorporated as a city after <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> abolishes the Board of Commissioners, the District's founding government. The \"City of Washington\" is given a <a href=\"https://wikipedia.org/wiki/Mayor%E2%80%93council_government\" title=\"Mayor-council government\">mayor-council</a> form of government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a> is incorporated as a city after <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> abolishes the Board of Commissioners, the District's founding government. The \"City of Washington\" is given a <a href=\"https://wikipedia.org/wiki/Mayor%E2%80%93council_government\" title=\"Mayor-council government\">mayor-council</a> form of government.", "links": [{"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Mayor-council government", "link": "https://wikipedia.org/wiki/Mayor%E2%80%93council_government"}]}, {"year": "1808", "text": "Finnish War: Sweden loses the fortress of Sveaborg to Russia.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: Sweden loses the fortress of <a href=\"https://wikipedia.org/wiki/Sveaborg\" class=\"mw-redirect\" title=\"Sveaborg\">Sveaborg</a> to Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: Sweden loses the fortress of <a href=\"https://wikipedia.org/wiki/Sveaborg\" class=\"mw-redirect\" title=\"Sveaborg\">Sveaborg</a> to Russia.", "links": [{"title": "Finnish War", "link": "https://wikipedia.org/wiki/Finnish_War"}, {"title": "Sveaborg", "link": "https://wikipedia.org/wiki/Sveaborg"}]}, {"year": "1808", "text": "Peninsular War: The Madrid rebels who rose up on May 2 are executed near Príncipe Pío hill.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: The <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a> rebels who <a href=\"https://wikipedia.org/wiki/Dos_de_Mayo_Uprising\" title=\"Dos de Mayo Uprising\">rose up</a> on <a href=\"https://wikipedia.org/wiki/May_2\" title=\"May 2\">May 2</a> are executed near <a href=\"https://wikipedia.org/wiki/Pr%C3%ADncipe_P%C3%ADo_(hill)\" title=\"Príncipe Pío (hill)\">Príncipe <PERSON>ío</a> hill.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: The <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a> rebels who <a href=\"https://wikipedia.org/wiki/Dos_de_Mayo_Uprising\" title=\"Dos de Mayo Uprising\">rose up</a> on <a href=\"https://wikipedia.org/wiki/May_2\" title=\"May 2\">May 2</a> are executed near <a href=\"https://wikipedia.org/wiki/Pr%C3%ADncipe_P%C3%ADo_(hill)\" title=\"Príncipe Pío (hill)\">Príncipe Pío</a> hill.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "Madrid", "link": "https://wikipedia.org/wiki/Madrid"}, {"title": "Dos de Mayo Uprising", "link": "https://wikipedia.org/wiki/Dos_de_Mayo_Uprising"}, {"title": "May 2", "link": "https://wikipedia.org/wiki/May_2"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (hill)", "link": "https://wikipedia.org/wiki/Pr%C3%ADncipe_P%C3%ADo_(hill)"}]}, {"year": "1815", "text": "Neapolitan War: <PERSON>, King of Naples, is defeated by the Austrians at the Battle of Tolentino, the decisive engagement of the war.", "html": "1815 - <a href=\"https://wikipedia.org/wiki/Neapolitan_War\" title=\"Neapolitan War\">Neapolitan War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Naples\" title=\"Kingdom of Naples\">Naples</a>, is defeated by the Austrians at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tolentino\" title=\"Battle of Tolentino\">Battle of Tolentino</a>, the decisive engagement of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Neapolitan_War\" title=\"Neapolitan War\">Neapolitan War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Naples\" title=\"Kingdom of Naples\">Naples</a>, is defeated by the Austrians at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tolentino\" title=\"Battle of Tolentino\">Battle of Tolentino</a>, the decisive engagement of the war.", "links": [{"title": "Neapolitan War", "link": "https://wikipedia.org/wiki/Neapolitan_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kingdom of Naples", "link": "https://wikipedia.org/wiki/Kingdom_of_Naples"}, {"title": "Battle of Tolentino", "link": "https://wikipedia.org/wiki/Battle_of_Tolentino"}]}, {"year": "1830", "text": "The Canterbury and Whitstable Railway is opened; it is the first steam-hauled passenger railway to issue season tickets and include a tunnel.", "html": "1830 - The <a href=\"https://wikipedia.org/wiki/Canterbury_and_Whitstable_Railway\" title=\"Canterbury and Whitstable Railway\">Canterbury and Whitstable Railway</a> is opened; it is the first steam-hauled passenger railway to issue season tickets and include a tunnel.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Canterbury_and_Whitstable_Railway\" title=\"Canterbury and Whitstable Railway\">Canterbury and Whitstable Railway</a> is opened; it is the first steam-hauled passenger railway to issue season tickets and include a tunnel.", "links": [{"title": "Canterbury and Whitstable Railway", "link": "https://wikipedia.org/wiki/Canterbury_and_Whitstable_Railway"}]}, {"year": "1837", "text": "The University of Athens is founded in Athens, Greece.", "html": "1837 - The <a href=\"https://wikipedia.org/wiki/National_and_Kapodistrian_University_of_Athens\" title=\"National and Kapodistrian University of Athens\">University of Athens</a> is founded in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, Greece.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_and_Kapodistrian_University_of_Athens\" title=\"National and Kapodistrian University of Athens\">University of Athens</a> is founded in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, Greece.", "links": [{"title": "National and Kapodistrian University of Athens", "link": "https://wikipedia.org/wiki/National_and_Kapodistrian_University_of_Athens"}, {"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}]}, {"year": "1848", "text": "The boar-crested Anglo-Saxon Benty Grange helmet is discovered in a barrow on the Benty Grange farm in Derbyshire.", "html": "1848 - The boar-crested <a href=\"https://wikipedia.org/wiki/Anglo-Saxon\" class=\"mw-redirect\" title=\"Anglo-Saxon\">Anglo-Saxon</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_helmet\" title=\"<PERSON><PERSON> helmet\"><PERSON><PERSON> helmet</a> is discovered in a <a href=\"https://wikipedia.org/wiki/Tumulus\" title=\"Tumulus\">barrow</a> on the <a href=\"https://wikipedia.org/wiki/Benty_Grange\" title=\"Benty Grange\">Benty Grange</a> farm in <a href=\"https://wikipedia.org/wiki/Derbyshire\" title=\"Derbyshire\">Derbyshire</a>.", "no_year_html": "The boar-crested <a href=\"https://wikipedia.org/wiki/Anglo-Saxon\" class=\"mw-redirect\" title=\"Anglo-Saxon\">Anglo-Saxon</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Grange_helmet\" title=\"<PERSON><PERSON> helmet\"><PERSON><PERSON> helmet</a> is discovered in a <a href=\"https://wikipedia.org/wiki/Tumulus\" title=\"Tumulus\">barrow</a> on the <a href=\"https://wikipedia.org/wiki/Benty_Grange\" title=\"Benty Grange\">Benty Grange</a> farm in <a href=\"https://wikipedia.org/wiki/Derbyshire\" title=\"Derbyshire\">Derbyshire</a>.", "links": [{"title": "Anglo-Saxon", "link": "https://wikipedia.org/wiki/Anglo-Saxon"}, {"title": "<PERSON><PERSON> helmet", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Grange_helmet"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>mulus"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Derbyshire", "link": "https://wikipedia.org/wiki/Derbyshire"}]}, {"year": "1849", "text": "The May Uprising in Dresden begins: The last of the German revolutions of 1848-49.", "html": "1849 - The <a href=\"https://wikipedia.org/wiki/May_Uprising_in_Dresden\" title=\"May Uprising in Dresden\">May Uprising in Dresden</a> begins: The last of the German revolutions of 1848-49.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/May_Uprising_in_Dresden\" title=\"May Uprising in Dresden\">May Uprising in Dresden</a> begins: The last of the German revolutions of 1848-49.", "links": [{"title": "May Uprising in Dresden", "link": "https://wikipedia.org/wiki/May_Uprising_in_Dresden"}]}, {"year": "1855", "text": "American adventurer <PERSON> departs from San Francisco with about 60 men to conquer Nicaragua.", "html": "1855 - American adventurer <a href=\"https://wikipedia.org/wiki/<PERSON>_(filibuster)\" title=\"<PERSON> (filibuster)\"><PERSON></a> departs from <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> with about 60 men to conquer <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "no_year_html": "American adventurer <a href=\"https://wikipedia.org/wiki/<PERSON>_(filibuster)\" title=\"<PERSON> (filibuster)\"><PERSON></a> departs from <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> with about 60 men to conquer <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "links": [{"title": "<PERSON> (filibuster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filibuster)"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}, {"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}]}, {"year": "1901", "text": "The Great Fire of 1901 begins in Jacksonville, Florida.", "html": "1901 - The <a href=\"https://wikipedia.org/wiki/Great_Fire_of_1901\" title=\"Great Fire of 1901\">Great Fire of 1901</a> begins in <a href=\"https://wikipedia.org/wiki/Jacksonville,_Florida\" title=\"Jacksonville, Florida\">Jacksonville, Florida</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Fire_of_1901\" title=\"Great Fire of 1901\">Great Fire of 1901</a> begins in <a href=\"https://wikipedia.org/wiki/Jacksonville,_Florida\" title=\"Jacksonville, Florida\">Jacksonville, Florida</a>.", "links": [{"title": "Great Fire of 1901", "link": "https://wikipedia.org/wiki/Great_Fire_of_1901"}, {"title": "Jacksonville, Florida", "link": "https://wikipedia.org/wiki/Jacksonville,_Florida"}]}, {"year": "1913", "text": "<PERSON>, the first full-length Indian feature film, is released, marking the beginning of the Indian film industry.", "html": "1913 - <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Raja <PERSON></a></i>, the first full-length Indian <a href=\"https://wikipedia.org/wiki/Feature_film\" title=\"Feature film\">feature film</a>, is released, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Cinema_of_India\" title=\"Cinema of India\">Indian film industry</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Raja <PERSON>\">Raja <PERSON></a></i>, the first full-length Indian <a href=\"https://wikipedia.org/wiki/Feature_film\" title=\"Feature film\">feature film</a>, is released, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Cinema_of_India\" title=\"Cinema of India\">Indian film industry</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Feature film", "link": "https://wikipedia.org/wiki/Feature_film"}, {"title": "Cinema of India", "link": "https://wikipedia.org/wiki/Cinema_of_India"}]}, {"year": "1920", "text": "A Bolshevik coup fails in the Democratic Republic of Georgia.", "html": "1920 - A <a href=\"https://wikipedia.org/wiki/1920_Georgian_coup_attempt\" title=\"1920 Georgian coup attempt\">Bolshevik coup</a> fails in the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Georgia\" title=\"Democratic Republic of Georgia\">Democratic Republic of Georgia</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1920_Georgian_coup_attempt\" title=\"1920 Georgian coup attempt\">Bolshevik coup</a> fails in the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Georgia\" title=\"Democratic Republic of Georgia\">Democratic Republic of Georgia</a>.", "links": [{"title": "1920 Georgian coup attempt", "link": "https://wikipedia.org/wiki/1920_Georgian_coup_attempt"}, {"title": "Democratic Republic of Georgia", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_Georgia"}]}, {"year": "1921", "text": "Ireland is partitioned under British law by the Government of Ireland Act 1920, creating Northern Ireland and Southern Ireland.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Partition_of_Ireland\" title=\"Partition of Ireland\">Ireland is partitioned</a> under British law by the <a href=\"https://wikipedia.org/wiki/Government_of_Ireland_Act_1920\" title=\"Government of Ireland Act 1920\">Government of Ireland Act 1920</a>, creating <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> and <a href=\"https://wikipedia.org/wiki/Southern_Ireland_(1921%E2%80%9322)\" class=\"mw-redirect\" title=\"Southern Ireland (1921-22)\">Southern Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Partition_of_Ireland\" title=\"Partition of Ireland\">Ireland is partitioned</a> under British law by the <a href=\"https://wikipedia.org/wiki/Government_of_Ireland_Act_1920\" title=\"Government of Ireland Act 1920\">Government of Ireland Act 1920</a>, creating <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> and <a href=\"https://wikipedia.org/wiki/Southern_Ireland_(1921%E2%80%9322)\" class=\"mw-redirect\" title=\"Southern Ireland (1921-22)\">Southern Ireland</a>.", "links": [{"title": "Partition of Ireland", "link": "https://wikipedia.org/wiki/Partition_of_Ireland"}, {"title": "Government of Ireland Act 1920", "link": "https://wikipedia.org/wiki/Government_of_Ireland_Act_1920"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "Southern Ireland (1921-22)", "link": "https://wikipedia.org/wiki/Southern_Ireland_(1921%E2%80%9322)"}]}, {"year": "1921", "text": "West Virginia becomes the first state to legislate a broad sales tax, but does not implement it until a number of years later due to enforcement issues.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/West_Virginia\" title=\"West Virginia\">West Virginia</a> becomes the first state to legislate a broad <a href=\"https://wikipedia.org/wiki/Sales_tax\" title=\"Sales tax\">sales tax</a>, but does not implement it until a number of years later due to enforcement issues.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/West_Virginia\" title=\"West Virginia\">West Virginia</a> becomes the first state to legislate a broad <a href=\"https://wikipedia.org/wiki/Sales_tax\" title=\"Sales tax\">sales tax</a>, but does not implement it until a number of years later due to enforcement issues.", "links": [{"title": "West Virginia", "link": "https://wikipedia.org/wiki/West_Virginia"}, {"title": "Sales tax", "link": "https://wikipedia.org/wiki/Sales_tax"}]}, {"year": "1928", "text": "The Jinan incident begins with the deaths of twelve Japanese civilians by Chinese forces in Jinan, China, which leads to Japanese retaliation and the deaths of over 2,000 Chinese civilians in the following days.", "html": "1928 - The <a href=\"https://wikipedia.org/wiki/Jinan_incident\" title=\"Jinan incident\">Jinan incident</a> begins with the deaths of twelve Japanese civilians by Chinese forces in Jinan, China, which leads to Japanese retaliation and the deaths of over 2,000 Chinese civilians in the following days.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Jinan_incident\" title=\"Jinan incident\">Jinan incident</a> begins with the deaths of twelve Japanese civilians by Chinese forces in Jinan, China, which leads to Japanese retaliation and the deaths of over 2,000 Chinese civilians in the following days.", "links": [{"title": "Jinan incident", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_incident"}]}, {"year": "1939", "text": "The All India Forward Bloc is formed by <PERSON><PERSON><PERSON>.", "html": "1939 - The <a href=\"https://wikipedia.org/wiki/All_India_Forward_Bloc\" title=\"All India Forward Bloc\">All India Forward Bloc</a> is formed by <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/All_India_Forward_Bloc\" title=\"All India Forward Bloc\">All India Forward Bloc</a> is formed by <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "All India Forward Bloc", "link": "https://wikipedia.org/wiki/All_India_Forward_Bloc"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "World War II: Japanese naval troops invade Tulagi Island in the Solomon Islands during the first part of Operation Mo that results in the Battle of the Coral Sea between Japanese forces and forces from the United States and Australia.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> naval troops <a href=\"https://wikipedia.org/wiki/Invasion_of_Tulagi_(May_1942)\" title=\"Invasion of Tulagi (May 1942)\">invade Tulagi</a> Island in the <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a> during the first part of <a href=\"https://wikipedia.org/wiki/Operation_Mo\" title=\"Operation Mo\">Operation Mo</a> that results in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Coral_Sea\" title=\"Battle of the Coral Sea\">Battle of the Coral Sea</a> between Japanese forces and forces from the United States and Australia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> naval troops <a href=\"https://wikipedia.org/wiki/Invasion_of_Tulagi_(May_1942)\" title=\"Invasion of Tulagi (May 1942)\">invade Tulagi</a> Island in the <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a> during the first part of <a href=\"https://wikipedia.org/wiki/Operation_Mo\" title=\"Operation Mo\">Operation Mo</a> that results in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Coral_Sea\" title=\"Battle of the Coral Sea\">Battle of the Coral Sea</a> between Japanese forces and forces from the United States and Australia.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Invasion of Tulagi (May 1942)", "link": "https://wikipedia.org/wiki/Invasion_of_Tulagi_(May_1942)"}, {"title": "Solomon Islands", "link": "https://wikipedia.org/wiki/Solomon_Islands"}, {"title": "Operation Mo", "link": "https://wikipedia.org/wiki/Operation_Mo"}, {"title": "Battle of the Coral Sea", "link": "https://wikipedia.org/wiki/Battle_of_the_Coral_Sea"}]}, {"year": "1945", "text": "World War II: Sinking of the prison ships Cap Arcona, Thielbek and Deutschland by the Royal Air Force in  Lübeck Bay.", "html": "1945 - World War II: Sinking of the <a href=\"https://wikipedia.org/wiki/Prison_ship\" title=\"Prison ship\">prison ships</a> <i><a href=\"https://wikipedia.org/wiki/SS_Cap_Arcona_(1927)\" class=\"mw-redirect\" title=\"SS Cap Arcona (1927)\">Cap Arcona</a></i>, <i><a href=\"https://wikipedia.org/wiki/SS_Thielbek_(1940)\" title=\"SS Thielbek (1940)\"><PERSON><PERSON><PERSON><PERSON></a></i> and <i><a href=\"https://wikipedia.org/wiki/SS_Deutschland_(1923)\" title=\"SS Deutschland (1923)\">Deutschland</a></i> by the <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> in <a href=\"https://wikipedia.org/wiki/L%C3%BCbeck_Bay\" class=\"mw-redirect\" title=\"Lübeck Bay\">Lübeck Bay</a>.", "no_year_html": "World War II: Sinking of the <a href=\"https://wikipedia.org/wiki/Prison_ship\" title=\"Prison ship\">prison ships</a> <i><a href=\"https://wikipedia.org/wiki/SS_Cap_Arcona_(1927)\" class=\"mw-redirect\" title=\"SS Cap Arcona (1927)\">Cap Arcona</a></i>, <i><a href=\"https://wikipedia.org/wiki/SS_Thielbek_(1940)\" title=\"SS Thielbek (1940)\"><PERSON><PERSON><PERSON><PERSON></a></i> and <i><a href=\"https://wikipedia.org/wiki/SS_Deutschland_(1923)\" title=\"SS Deutschland (1923)\">Deutschland</a></i> by the <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> in <a href=\"https://wikipedia.org/wiki/L%C3%BCbeck_Bay\" class=\"mw-redirect\" title=\"Lübeck Bay\">Lübeck Bay</a>.", "links": [{"title": "Prison ship", "link": "https://wikipedia.org/wiki/Prison_ship"}, {"title": "SS Cap Arcona (1927)", "link": "https://wikipedia.org/wiki/SS_Cap_Arcona_(1927)"}, {"title": "SS Thielbek (1940)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_(1940)"}, {"title": "SS Deutschland (1923)", "link": "https://wikipedia.org/wiki/SS_Deutschland_(1923)"}, {"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}, {"title": "Lübeck Bay", "link": "https://wikipedia.org/wiki/L%C3%BCbeck_Bay"}]}, {"year": "1947", "text": "New post-war Japanese constitution goes into effect.", "html": "1947 - New post-war <a href=\"https://wikipedia.org/wiki/Constitution_of_Japan\" title=\"Constitution of Japan\">Japanese constitution</a> goes into effect.", "no_year_html": "New post-war <a href=\"https://wikipedia.org/wiki/Constitution_of_Japan\" title=\"Constitution of Japan\">Japanese constitution</a> goes into effect.", "links": [{"title": "Constitution of Japan", "link": "https://wikipedia.org/wiki/Constitution_of_Japan"}]}, {"year": "1948", "text": "The U.S. Supreme Court rules in <PERSON> v<PERSON> that covenants prohibiting the sale of real estate to blacks and other minorities are legally unenforceable.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON> v<PERSON>\"><PERSON> v<PERSON></a></i> that covenants prohibiting the sale of <a href=\"https://wikipedia.org/wiki/Real_estate\" title=\"Real estate\">real estate</a> to blacks and other minorities are legally unenforceable.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON> v<PERSON>\"><PERSON> v<PERSON></a></i> that covenants prohibiting the sale of <a href=\"https://wikipedia.org/wiki/Real_estate\" title=\"Real estate\">real estate</a> to blacks and other minorities are legally unenforceable.", "links": [{"title": "U.S. Supreme Court", "link": "https://wikipedia.org/wiki/U.S._Supreme_Court"}, {"title": "<PERSON> v. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Real estate", "link": "https://wikipedia.org/wiki/Real_estate"}]}, {"year": "1951", "text": "London's Royal Festival Hall opens with the Festival of Britain.", "html": "1951 - London's <a href=\"https://wikipedia.org/wiki/Royal_Festival_Hall\" title=\"Royal Festival Hall\">Royal Festival Hall</a> opens with the <a href=\"https://wikipedia.org/wiki/Festival_of_Britain\" title=\"Festival of Britain\">Festival of Britain</a>.", "no_year_html": "London's <a href=\"https://wikipedia.org/wiki/Royal_Festival_Hall\" title=\"Royal Festival Hall\">Royal Festival Hall</a> opens with the <a href=\"https://wikipedia.org/wiki/Festival_of_Britain\" title=\"Festival of Britain\">Festival of Britain</a>.", "links": [{"title": "Royal Festival Hall", "link": "https://wikipedia.org/wiki/Royal_Festival_Hall"}, {"title": "Festival of Britain", "link": "https://wikipedia.org/wiki/Festival_of_Britain"}]}, {"year": "1951", "text": "The United States Senate Committee on Armed Services and United States Senate Committee on Foreign Relations begin their closed door hearings into the relief of <PERSON> by U.S. President <PERSON>.", "html": "1951 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate_Committee_on_Armed_Services\" title=\"United States Senate Committee on Armed Services\">United States Senate Committee on Armed Services</a> and <a href=\"https://wikipedia.org/wiki/United_States_Senate_Committee_on_Foreign_Relations\" title=\"United States Senate Committee on Foreign Relations\">United States Senate Committee on Foreign Relations</a> begin their closed door hearings into the <a href=\"https://wikipedia.org/wiki/Relief_of_<PERSON>_<PERSON>\" title=\"Relief of <PERSON>\">relief of <PERSON></a> by U.S. President <PERSON>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate_Committee_on_Armed_Services\" title=\"United States Senate Committee on Armed Services\">United States Senate Committee on Armed Services</a> and <a href=\"https://wikipedia.org/wiki/United_States_Senate_Committee_on_Foreign_Relations\" title=\"United States Senate Committee on Foreign Relations\">United States Senate Committee on Foreign Relations</a> begin their closed door hearings into the <a href=\"https://wikipedia.org/wiki/Relief_of_<PERSON>_<PERSON>\" title=\"Relief of <PERSON>\">relief of <PERSON></a> by U.S. President <PERSON>.", "links": [{"title": "United States Senate Committee on Armed Services", "link": "https://wikipedia.org/wiki/United_States_Senate_Committee_on_Armed_Services"}, {"title": "United States Senate Committee on Foreign Relations", "link": "https://wikipedia.org/wiki/United_States_Senate_Committee_on_Foreign_Relations"}, {"title": "Relief of <PERSON>", "link": "https://wikipedia.org/wiki/Relief_of_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "Lieutenant Colonels <PERSON> and <PERSON> of the United States land a plane at the North Pole.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Lieutenant_Colonel\" class=\"mw-redirect\" title=\"Lieutenant Colonel\">Lieutenant Colonels</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of the United States land a plane at the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lieutenant_Colonel\" class=\"mw-redirect\" title=\"Lieutenant Colonel\">Lieutenant Colonels</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of the United States land a plane at the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a>.", "links": [{"title": "Lieutenant Colonel", "link": "https://wikipedia.org/wiki/Lieutenant_Colonel"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "North Pole", "link": "https://wikipedia.org/wiki/North_Pole"}]}, {"year": "1952", "text": "The Kentucky Derby is televised nationally for the first time, on the CBS network.", "html": "1952 - The <a href=\"https://wikipedia.org/wiki/Kentucky_Derby\" title=\"Kentucky Derby\">Kentucky Derby</a> is televised nationally for the first time, on the <a href=\"https://wikipedia.org/wiki/CBS\" title=\"CBS\">CBS</a> network.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kentucky_Derby\" title=\"Kentucky Derby\">Kentucky Derby</a> is televised nationally for the first time, on the <a href=\"https://wikipedia.org/wiki/CBS\" title=\"CBS\">CBS</a> network.", "links": [{"title": "Kentucky Derby", "link": "https://wikipedia.org/wiki/Kentucky_Derby"}, {"title": "CBS", "link": "https://wikipedia.org/wiki/CBS"}]}, {"year": "1953", "text": "Two men are rescued from a semitrailer that crashed over the side of the Pit River Bridge before it fell into the Sacramento River. Amateur photographer <PERSON> photographs \"Rescue on Pit River Bridge\", the first and only winning submission for the Pulitzer Prize for Photography to have been taken by a woman.", "html": "1953 - Two men are rescued from a <a href=\"https://wikipedia.org/wiki/Semitrailer\" class=\"mw-redirect\" title=\"Semitrailer\">semitrailer</a> that crashed over the side of the <a href=\"https://wikipedia.org/wiki/Pit_River_Bridge\" title=\"Pit River Bridge\">Pit River Bridge</a> before it fell into the <a href=\"https://wikipedia.org/wiki/Sacramento_River\" title=\"Sacramento River\">Sacramento River</a>. Amateur photographer <a href=\"https://wikipedia.org/wiki/Virginia_Schau\" title=\"Virginia Schau\">Virginia Schau</a> photographs \"Rescue on Pit River Bridge\", the first and only winning submission for the <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Photography\" title=\"Pulitzer Prize for Photography\">Pulitzer Prize for Photography</a> to have been taken by a woman.", "no_year_html": "Two men are rescued from a <a href=\"https://wikipedia.org/wiki/Semitrailer\" class=\"mw-redirect\" title=\"Semitrailer\">semitrailer</a> that crashed over the side of the <a href=\"https://wikipedia.org/wiki/Pit_River_Bridge\" title=\"Pit River Bridge\">Pit River Bridge</a> before it fell into the <a href=\"https://wikipedia.org/wiki/Sacramento_River\" title=\"Sacramento River\">Sacramento River</a>. Amateur photographer <a href=\"https://wikipedia.org/wiki/Virginia_Schau\" title=\"Virginia Schau\">Virginia Schau</a> photographs \"Rescue on Pit River Bridge\", the first and only winning submission for the <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Photography\" title=\"Pulitzer Prize for Photography\">Pulitzer Prize for Photography</a> to have been taken by a woman.", "links": [{"title": "Semitrailer", "link": "https://wikipedia.org/wiki/Semitrailer"}, {"title": "Pit River Bridge", "link": "https://wikipedia.org/wiki/Pit_River_Bridge"}, {"title": "Sacramento River", "link": "https://wikipedia.org/wiki/Sacramento_River"}, {"title": "Virginia Schau", "link": "https://wikipedia.org/wiki/Virginia_Schau"}, {"title": "Pulitzer Prize for Photography", "link": "https://wikipedia.org/wiki/Pulitzer_Prize_for_Photography"}]}, {"year": "1957", "text": "<PERSON>, the owner of the Brooklyn Dodgers, agrees to move the team from Brooklyn to Los Angeles.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, the owner of the <a href=\"https://wikipedia.org/wiki/Brooklyn_Dodgers\" title=\"Brooklyn Dodgers\">Brooklyn Dodgers</a>, agrees to move the team from <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a> to Los Angeles.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, the owner of the <a href=\"https://wikipedia.org/wiki/Brooklyn_Dodgers\" title=\"Brooklyn Dodgers\">Brooklyn Dodgers</a>, agrees to move the team from <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a> to Los Angeles.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Walter_O%27Malley"}, {"title": "Brooklyn Dodgers", "link": "https://wikipedia.org/wiki/Brooklyn_Dodgers"}, {"title": "Brooklyn", "link": "https://wikipedia.org/wiki/Brooklyn"}]}, {"year": "1963", "text": "The police force in Birmingham, Alabama switches tactics and responds with violent force to stop the \"Birmingham campaign\" protesters. Images of the violent suppression are transmitted worldwide, bringing new-found attention to the civil rights movement.", "html": "1963 - The police force in <a href=\"https://wikipedia.org/wiki/Birmingham,_Alabama\" title=\"Birmingham, Alabama\">Birmingham, Alabama</a> switches tactics and <a href=\"https://wikipedia.org/wiki/Birmingham_campaign#Fire_hoses_and_police_dogs\" title=\"Birmingham campaign\">responds with violent force</a> to stop the \"<a href=\"https://wikipedia.org/wiki/Birmingham_campaign\" title=\"Birmingham campaign\">Birmingham campaign</a>\" protesters. Images of the violent suppression are transmitted worldwide, bringing new-found attention to the <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">civil rights movement</a>.", "no_year_html": "The police force in <a href=\"https://wikipedia.org/wiki/Birmingham,_Alabama\" title=\"Birmingham, Alabama\">Birmingham, Alabama</a> switches tactics and <a href=\"https://wikipedia.org/wiki/Birmingham_campaign#Fire_hoses_and_police_dogs\" title=\"Birmingham campaign\">responds with violent force</a> to stop the \"<a href=\"https://wikipedia.org/wiki/Birmingham_campaign\" title=\"Birmingham campaign\">Birmingham campaign</a>\" protesters. Images of the violent suppression are transmitted worldwide, bringing new-found attention to the <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">civil rights movement</a>.", "links": [{"title": "Birmingham, Alabama", "link": "https://wikipedia.org/wiki/Birmingham,_Alabama"}, {"title": "Birmingham campaign", "link": "https://wikipedia.org/wiki/Birmingham_campaign#Fire_hoses_and_police_dogs"}, {"title": "Birmingham campaign", "link": "https://wikipedia.org/wiki/Birmingham_campaign"}, {"title": "Civil rights movement", "link": "https://wikipedia.org/wiki/Civil_rights_movement"}]}, {"year": "1968", "text": "Eighty-five people are killed when Braniff International Airways Flight 352 crashes near Dawson, Texas.", "html": "1968 - Eighty-five people are killed when <a href=\"https://wikipedia.org/wiki/Braniff_International_Airways_Flight_352\" title=\"Braniff International Airways Flight 352\">Braniff International Airways Flight 352</a> crashes near <a href=\"https://wikipedia.org/wiki/<PERSON>,_Texas\" title=\"Dawson, Texas\">Dawson, Texas</a>.", "no_year_html": "Eighty-five people are killed when <a href=\"https://wikipedia.org/wiki/Braniff_International_Airways_Flight_352\" title=\"Braniff International Airways Flight 352\">Braniff International Airways Flight 352</a> crashes near <a href=\"https://wikipedia.org/wiki/<PERSON>,_Texas\" title=\"Dawson, Texas\">Dawson, Texas</a>.", "links": [{"title": "Braniff International Airways Flight 352", "link": "https://wikipedia.org/wiki/Braniff_International_Airways_Flight_352"}, {"title": "Dawson, Texas", "link": "https://wikipedia.org/wiki/<PERSON>,_Texas"}]}, {"year": "1971", "text": "<PERSON> becomes First Secretary of the Socialist Unity Party of Germany, remaining in power until 1989.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Leadership_of_East_Germany#Leaders_of_the_Socialist_Unity_Party_of_Germany_(SED)\" class=\"mw-redirect\" title=\"Leadership of East Germany\">First Secretary of the Socialist Unity Party of Germany</a>, remaining in power until 1989.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Leadership_of_East_Germany#Leaders_of_the_Socialist_Unity_Party_of_Germany_(SED)\" class=\"mw-redirect\" title=\"Leadership of East Germany\">First Secretary of the Socialist Unity Party of Germany</a>, remaining in power until 1989.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Leadership of East Germany", "link": "https://wikipedia.org/wiki/Leadership_of_East_Germany#Leaders_of_the_Socialist_Unity_Party_of_Germany_(SED)"}]}, {"year": "1978", "text": "The first unsolicited bulk commercial email (which would later become known as \"spam\") is sent by a Digital Equipment Corporation marketing representative to every ARPANET address on the west coast of the United States.", "html": "1978 - The first unsolicited bulk commercial <a href=\"https://wikipedia.org/wiki/Email\" title=\"Email\">email</a> (which would later become known as \"<a href=\"https://wikipedia.org/wiki/Spam_(electronic)\" class=\"mw-redirect\" title=\"Spam (electronic)\">spam</a>\") is sent by a <a href=\"https://wikipedia.org/wiki/Digital_Equipment_Corporation\" title=\"Digital Equipment Corporation\">Digital Equipment Corporation</a> marketing representative to every <a href=\"https://wikipedia.org/wiki/ARPANET\" title=\"ARPANET\">ARPANET</a> address on the west coast of the United States.", "no_year_html": "The first unsolicited bulk commercial <a href=\"https://wikipedia.org/wiki/Email\" title=\"Email\">email</a> (which would later become known as \"<a href=\"https://wikipedia.org/wiki/Spam_(electronic)\" class=\"mw-redirect\" title=\"Spam (electronic)\">spam</a>\") is sent by a <a href=\"https://wikipedia.org/wiki/Digital_Equipment_Corporation\" title=\"Digital Equipment Corporation\">Digital Equipment Corporation</a> marketing representative to every <a href=\"https://wikipedia.org/wiki/ARPANET\" title=\"ARPANET\">ARPANET</a> address on the west coast of the United States.", "links": [{"title": "Email", "link": "https://wikipedia.org/wiki/Email"}, {"title": "Spam (electronic)", "link": "https://wikipedia.org/wiki/Spam_(electronic)"}, {"title": "Digital Equipment Corporation", "link": "https://wikipedia.org/wiki/Digital_Equipment_Corporation"}, {"title": "ARPANET", "link": "https://wikipedia.org/wiki/ARPANET"}]}, {"year": "1979", "text": "<PERSON> wins the United Kingdom general election. The following day, she becomes the first female British Prime Minister.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the United Kingdom <a href=\"https://wikipedia.org/wiki/1979_United_Kingdom_general_election\" title=\"1979 United Kingdom general election\">general election</a>. The following day, she becomes the first female <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">British Prime Minister</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the United Kingdom <a href=\"https://wikipedia.org/wiki/1979_United_Kingdom_general_election\" title=\"1979 United Kingdom general election\">general election</a>. The following day, she becomes the first female <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">British Prime Minister</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1979 United Kingdom general election", "link": "https://wikipedia.org/wiki/1979_United_Kingdom_general_election"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1986", "text": "Twenty-one people are killed and forty-one are injured after a bomb explodes on Air Lanka Flight 512 at Colombo airport in Sri Lanka.", "html": "1986 - Twenty-one people are killed and forty-one are injured after a bomb explodes on <a href=\"https://wikipedia.org/wiki/Air_Lanka_Flight_512\" title=\"Air Lanka Flight 512\">Air Lanka Flight 512</a> at Colombo airport in Sri Lanka.", "no_year_html": "Twenty-one people are killed and forty-one are injured after a bomb explodes on <a href=\"https://wikipedia.org/wiki/Air_Lanka_Flight_512\" title=\"Air Lanka Flight 512\">Air Lanka Flight 512</a> at Colombo airport in Sri Lanka.", "links": [{"title": "Air Lanka Flight 512", "link": "https://wikipedia.org/wiki/Air_Lanka_Flight_512"}]}, {"year": "1987", "text": "A crash by <PERSON> at the Talladega Superspeedway, Alabama fencing at the start-finish line would lead NASCAR to develop the restrictor plate for the following season both at Daytona International Speedway and Talladega.", "html": "1987 - A crash by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Talladega_Superspeedway\" title=\"Talladega Superspeedway\">Talladega Superspeedway</a>, <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> fencing at the start-finish line would lead <a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> to develop the <a href=\"https://wikipedia.org/wiki/Restrictor_plate\" title=\"Restrictor plate\">restrictor plate</a> for the following season both at <a href=\"https://wikipedia.org/wiki/Daytona_International_Speedway\" title=\"Daytona International Speedway\">Daytona International Speedway</a> and Talladega.", "no_year_html": "A crash by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Talladega_Superspeedway\" title=\"Talladega Superspeedway\">Talladega Superspeedway</a>, <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> fencing at the start-finish line would lead <a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> to develop the <a href=\"https://wikipedia.org/wiki/Restrictor_plate\" title=\"Restrictor plate\">restrictor plate</a> for the following season both at <a href=\"https://wikipedia.org/wiki/Daytona_International_Speedway\" title=\"Daytona International Speedway\">Daytona International Speedway</a> and Talladega.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Talladega Superspeedway", "link": "https://wikipedia.org/wiki/Talladega_Superspeedway"}, {"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "NASCAR", "link": "https://wikipedia.org/wiki/NASCAR"}, {"title": "Restrictor plate", "link": "https://wikipedia.org/wiki/Restrictor_plate"}, {"title": "Daytona International Speedway", "link": "https://wikipedia.org/wiki/Daytona_International_Speedway"}]}, {"year": "1999", "text": "The southwestern portion of Oklahoma City is devastated by an F5 tornado, killing forty-five people, injuring 665, and causing $1 billion in damage. The tornado is one of 66 from the 1999 Oklahoma tornado outbreak. This tornado also produces the highest wind speed ever recorded, measured at 301 +/- 20 mph (484 +/- 32 km/h). In meteorology, the term \"May 3\" is synonymous with the F5 tornado.", "html": "1999 - The southwestern portion of <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City</a> is devastated by an <a href=\"https://wikipedia.org/wiki/1999_Bridge_Creek%E2%80%93Moore_tornado\" title=\"1999 Bridge Creek-Moore tornado\">F5 tornado</a>, killing forty-five people, injuring 665, and causing $1 billion in damage. The tornado is one of 66 from the <a href=\"https://wikipedia.org/wiki/1999_Oklahoma_tornado_outbreak\" title=\"1999 Oklahoma tornado outbreak\">1999 Oklahoma tornado outbreak</a>. This tornado also produces the highest wind speed ever recorded, measured at 301 +/- 20 mph (484 +/- 32 km/h). In meteorology, the term \"May 3\" is synonymous with the F5 tornado.", "no_year_html": "The southwestern portion of <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City</a> is devastated by an <a href=\"https://wikipedia.org/wiki/1999_Bridge_Creek%E2%80%93Moore_tornado\" title=\"1999 Bridge Creek-Moore tornado\">F5 tornado</a>, killing forty-five people, injuring 665, and causing $1 billion in damage. The tornado is one of 66 from the <a href=\"https://wikipedia.org/wiki/1999_Oklahoma_tornado_outbreak\" title=\"1999 Oklahoma tornado outbreak\">1999 Oklahoma tornado outbreak</a>. This tornado also produces the highest wind speed ever recorded, measured at 301 +/- 20 mph (484 +/- 32 km/h). In meteorology, the term \"May 3\" is synonymous with the F5 tornado.", "links": [{"title": "Oklahoma City", "link": "https://wikipedia.org/wiki/Oklahoma_City"}, {"title": "1999 Bridge Creek-Moore tornado", "link": "https://wikipedia.org/wiki/1999_Bridge_Creek%E2%80%93Moore_tornado"}, {"title": "1999 Oklahoma tornado outbreak", "link": "https://wikipedia.org/wiki/1999_Oklahoma_tornado_outbreak"}]}, {"year": "1999", "text": "Infiltration of Pakistani soldiers on Indian side results in the Kargil War.", "html": "1999 - Infiltration of Pakistani soldiers on Indian side results in the <a href=\"https://wikipedia.org/wiki/Kargil_War\" title=\"Kargil War\">Kargil War</a>.", "no_year_html": "Infiltration of Pakistani soldiers on Indian side results in the <a href=\"https://wikipedia.org/wiki/Kargil_War\" title=\"Kargil War\">Kargil War</a>.", "links": [{"title": "Kargil War", "link": "https://wikipedia.org/wiki/Kargil_War"}]}, {"year": "2000", "text": "The sport of geocaching begins, with the first cache placed and the coordinates from a GPS posted on Usenet.", "html": "2000 - The sport of <a href=\"https://wikipedia.org/wiki/Geocaching\" title=\"Geocaching\">geocaching</a> begins, with the first cache placed and the coordinates from a <a href=\"https://wikipedia.org/wiki/Global_Positioning_System\" title=\"Global Positioning System\">GPS</a> posted on <a href=\"https://wikipedia.org/wiki/Usenet\" title=\"Usenet\">Usenet</a>.", "no_year_html": "The sport of <a href=\"https://wikipedia.org/wiki/Geocaching\" title=\"Geocaching\">geocaching</a> begins, with the first cache placed and the coordinates from a <a href=\"https://wikipedia.org/wiki/Global_Positioning_System\" title=\"Global Positioning System\">GPS</a> posted on <a href=\"https://wikipedia.org/wiki/Usenet\" title=\"Usenet\">Usenet</a>.", "links": [{"title": "Geocaching", "link": "https://wikipedia.org/wiki/Geocaching"}, {"title": "Global Positioning System", "link": "https://wikipedia.org/wiki/Global_Positioning_System"}, {"title": "Usenet", "link": "https://wikipedia.org/wiki/Usenet"}]}, {"year": "2001", "text": "The United States loses its seat on the U.N. Human Rights Commission for the first time since the commission was formed in 1947.", "html": "2001 - The United States loses its seat on the <a href=\"https://wikipedia.org/wiki/U.N._Human_Rights_Commission\" class=\"mw-redirect\" title=\"U.N. Human Rights Commission\">U.N. Human Rights Commission</a> for the first time since the commission was formed in <a href=\"https://wikipedia.org/wiki/1947\" title=\"1947\">1947</a>.", "no_year_html": "The United States loses its seat on the <a href=\"https://wikipedia.org/wiki/U.N._Human_Rights_Commission\" class=\"mw-redirect\" title=\"U.N. Human Rights Commission\">U.N. Human Rights Commission</a> for the first time since the commission was formed in <a href=\"https://wikipedia.org/wiki/1947\" title=\"1947\">1947</a>.", "links": [{"title": "U.N. Human Rights Commission", "link": "https://wikipedia.org/wiki/U.N._Human_Rights_Commission"}, {"title": "1947", "link": "https://wikipedia.org/wiki/1947"}]}, {"year": "2006", "text": "Armavia Flight 967 crashes into the Black Sea near Sochi International Airport in Sochi, Russia, killing 113 people.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Armavia_Flight_967\" title=\"Armavia Flight 967\">Armavia Flight 967</a> crashes into the <a href=\"https://wikipedia.org/wiki/Black_Sea\" title=\"Black Sea\">Black Sea</a> near <a href=\"https://wikipedia.org/wiki/Sochi_International_Airport\" title=\"Sochi International Airport\">Sochi International Airport</a> in <a href=\"https://wikipedia.org/wiki/Sochi\" title=\"Sochi\">Sochi</a>, Russia, killing 113 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armavia_Flight_967\" title=\"Armavia Flight 967\">Armavia Flight 967</a> crashes into the <a href=\"https://wikipedia.org/wiki/Black_Sea\" title=\"Black Sea\">Black Sea</a> near <a href=\"https://wikipedia.org/wiki/Sochi_International_Airport\" title=\"Sochi International Airport\">Sochi International Airport</a> in <a href=\"https://wikipedia.org/wiki/Sochi\" title=\"Sochi\">Sochi</a>, Russia, killing 113 people.", "links": [{"title": "Armavia Flight 967", "link": "https://wikipedia.org/wiki/Armavia_Flight_967"}, {"title": "Black Sea", "link": "https://wikipedia.org/wiki/Black_Sea"}, {"title": "Sochi International Airport", "link": "https://wikipedia.org/wiki/Sochi_International_Airport"}, {"title": "Sochi", "link": "https://wikipedia.org/wiki/Sochi"}]}, {"year": "2007", "text": "The three-year-old British girl <PERSON> disappears in Praia da Luz, Portugal, starting \"the most heavily reported missing-person case in modern history\".", "html": "2007 - The three-year-old British girl <a href=\"https://wikipedia.org/wiki/Disappearance_of_<PERSON>_<PERSON>\" title=\"Disappearance of <PERSON>\"><PERSON></a> disappears in Praia da Luz, Portugal, starting \"the most heavily reported missing-person case in modern history\".", "no_year_html": "The three-year-old British girl <a href=\"https://wikipedia.org/wiki/Disappearance_of_<PERSON>_<PERSON>\" title=\"Disappearance of <PERSON>\"><PERSON></a> disappears in Praia da Luz, Portugal, starting \"the most heavily reported missing-person case in modern history\".", "links": [{"title": "Disappearance of <PERSON>", "link": "https://wikipedia.org/wiki/Disappearance_of_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "Two gunmen launch an attempted attack on an anti-Islam event in Garland, Texas, which was held in response to the Charlie <PERSON> shooting.", "html": "2015 - Two gunmen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Center_attack\" title=\"<PERSON> Center attack\">launch an attempted attack</a> on an anti-Islam event in <a href=\"https://wikipedia.org/wiki/Garland,_Texas\" title=\"Garland, Texas\">Garland, Texas</a>, which was held in response to the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_shooting\" title=\"<PERSON> shooting\"><i><PERSON></i> shooting</a>.", "no_year_html": "Two gunmen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Center_attack\" title=\"<PERSON> attack\">launch an attempted attack</a> on an anti-Islam event in <a href=\"https://wikipedia.org/wiki/Garland,_Texas\" title=\"Garland, Texas\">Garland, Texas</a>, which was held in response to the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_shooting\" title=\"<PERSON> shooting\"><i><PERSON></i> shooting</a>.", "links": [{"title": "Curtis <PERSON>well Center attack", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Center_attack"}, {"title": "Garland, Texas", "link": "https://wikipedia.org/wiki/Garland,_Texas"}, {"title": "<PERSON> shooting", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_shooting"}]}, {"year": "2016", "text": "Eighty-eight thousand people are evacuated from their homes in Fort McMurray, Alberta, Canada as a wildfire rips through the community, destroying approximately 2,400 homes and buildings.", "html": "2016 - Eighty-eight thousand people are evacuated from their homes in <a href=\"https://wikipedia.org/wiki/Fort_McMurray\" title=\"Fort McMurray\">Fort McMurray</a>, Alberta, Canada as <a href=\"https://wikipedia.org/wiki/2016_Fort_McMurray_Wildfire\" class=\"mw-redirect\" title=\"2016 Fort McMurray Wildfire\">a wildfire rips through the community</a>, destroying approximately 2,400 homes and buildings.", "no_year_html": "Eighty-eight thousand people are evacuated from their homes in <a href=\"https://wikipedia.org/wiki/Fort_McMurray\" title=\"Fort McMurray\">Fort McMurray</a>, Alberta, Canada as <a href=\"https://wikipedia.org/wiki/2016_Fort_McMurray_Wildfire\" class=\"mw-redirect\" title=\"2016 Fort McMurray Wildfire\">a wildfire rips through the community</a>, destroying approximately 2,400 homes and buildings.", "links": [{"title": "Fort McMurray", "link": "https://wikipedia.org/wiki/Fort_Mc<PERSON>"}, {"title": "2016 Fort McMurray Wildfire", "link": "https://wikipedia.org/wiki/2016_Fort_Mc<PERSON><PERSON><PERSON>_Wildfire"}]}, {"year": "2023", "text": "Nine students and a security guard are killed in the Belgrade school shooting, the first attack of its kind in Serbia.", "html": "2023 - Nine students and a security guard are killed in the <a href=\"https://wikipedia.org/wiki/Belgrade_school_shooting\" title=\"Belgrade school shooting\">Belgrade school shooting</a>, the first attack of its kind in <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>.", "no_year_html": "Nine students and a security guard are killed in the <a href=\"https://wikipedia.org/wiki/Belgrade_school_shooting\" title=\"Belgrade school shooting\">Belgrade school shooting</a>, the first attack of its kind in <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>.", "links": [{"title": "Belgrade school shooting", "link": "https://wikipedia.org/wiki/Belgrade_school_shooting"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}]}], "Births": [{"year": "490", "text": "<PERSON><PERSON><PERSON>, ruler of Palenque (d. 565)", "html": "490 - <a href=\"https://wikipedia.org/wiki/K%CA%<PERSON><PERSON>_<PERSON>_<PERSON>tam_I\" title=\"Kʼan Joy Chitam I\"><PERSON><PERSON><PERSON></a>, ruler of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Palenque\">Pa<PERSON><PERSON></a> (d. 565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%CA%<PERSON><PERSON>_<PERSON>_Chitam_I\" title=\"Kʼan Joy <PERSON>tam I\"><PERSON><PERSON><PERSON></a>, ruler of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Palenque\">Pa<PERSON>que</a> (d. 565)", "links": [{"title": "<PERSON><PERSON>an <PERSON>", "link": "https://wikipedia.org/wiki/K%CA%<PERSON><PERSON>_<PERSON>_<PERSON>_I"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Palenque"}]}, {"year": "612", "text": "<PERSON>, Byzantine emperor (d. 641)", "html": "612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_(Byzantine_emperor)\" class=\"mw-redirect\" title=\"<PERSON> III (Byzantine emperor)\"><PERSON> III</a>, Byzantine emperor (d. 641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_(Byzantine_emperor)\" class=\"mw-redirect\" title=\"<PERSON> III (Byzantine emperor)\"><PERSON> III</a>, Byzantine emperor (d. 641)", "links": [{"title": "<PERSON> (Byzantine emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_III_(Byzantine_emperor)"}]}, {"year": "1238", "text": "<PERSON>, Italian saint (d. 1314)", "html": "1238 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bicchieri\" title=\"<PERSON> Bicchieri\"><PERSON></a>, Italian saint (d. 1314)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bicchieri\" title=\"Emilia Bicchieri\"><PERSON></a>, Italian saint (d. 1314)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emilia_B<PERSON>chieri"}]}, {"year": "1276", "text": "<PERSON>, Count of Évreux, son of King <PERSON> of France (d. 1319)", "html": "1276 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_%C3%89vreux\" title=\"<PERSON>, Count of Évreux\"><PERSON>, Count of Évreux</a>, son of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1319)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_%C3%89vreux\" title=\"<PERSON>, Count of Évreux\"><PERSON>, Count of Évreux</a>, son of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1319)", "links": [{"title": "<PERSON>, Count of Évreux", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_%C3%89vreux"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1415", "text": "<PERSON><PERSON>, Duchess of York (d. 1495)", "html": "1415 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_York\" title=\"<PERSON><PERSON>, Duchess of York\"><PERSON><PERSON>, Duchess of York</a> (d. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_York\" title=\"<PERSON><PERSON>, Duchess of York\"><PERSON><PERSON>, Duchess of York</a> (d. 1495)", "links": [{"title": "<PERSON><PERSON>, Duchess of York", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_York"}]}, {"year": "1428", "text": "<PERSON>, Spanish cardinal (d. 1495)", "html": "1428 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_de_Mendoza\" title=\"<PERSON>\"><PERSON></a>, Spanish cardinal (d. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_de_Mendoza\" title=\"<PERSON>\"><PERSON></a>, Spanish cardinal (d. 1495)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gonz%C3%<PERSON><PERSON><PERSON>_de_Mendoza"}]}, {"year": "1446", "text": "<PERSON> York (d. 1503)", "html": "1446 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of York\"><PERSON> York</a> (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of York\"><PERSON> York</a> (d. 1503)", "links": [{"title": "<PERSON> of York", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1461", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian cardinal (d. 1521)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1521)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>io"}]}, {"year": "1469", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian historian and philosopher (d. 1527)", "html": "1469 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian historian and philosopher (d. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian historian and philosopher (d. 1527)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1479", "text": "<PERSON>, Duke of Mecklenburg (d. 1552)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON>, Duke of Mecklenburg</a> (d. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON>, Duke of Mecklenburg</a> (d. 1552)", "links": [{"title": "<PERSON>, Duke of Mecklenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Mecklenburg"}]}, {"year": "1481", "text": "<PERSON><PERSON> <PERSON>, Spanish abbess of the Franciscan Third Order Regular (d. 1534)", "html": "1481 - <a href=\"https://wikipedia.org/wiki/Juana_de_la_Cruz_V%C3%<PERSON><PERSON><PERSON>_Guti%C3%A9rrez\" title=\"Juana de la Cruz Vázquez Gutiérrez\"><PERSON><PERSON> <PERSON> la Cruz <PERSON></a>, Spanish abbess of the Franciscan Third Order Regular (d. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan<PERSON>_de_la_Cruz_V%C3%A1<PERSON><PERSON>_Guti%C3%A9rrez\" title=\"Juana de la Cruz Vázquez Gutiérrez\"><PERSON><PERSON> <PERSON> Cruz <PERSON>á<PERSON></a>, Spanish abbess of the Franciscan Third Order Regular (d. 1534)", "links": [{"title": "Juana de la Cruz Vázquez <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_de_la_Cruz_V%C3%<PERSON><PERSON>quez_Guti%C3%A9rrez"}]}, {"year": "1536", "text": "<PERSON>, German theologian (d. 1603)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1603)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1632", "text": "<PERSON>, French-Canadian nurse and candidate for sainthood, founded the Hôtel-Dieu de Québec (d. 1668)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_St._Augustine\" title=\"<PERSON> of St. Augustine\"><PERSON> of St. Augustine</a>, French-Canadian nurse and candidate for sainthood, founded the <a href=\"https://wikipedia.org/wiki/H%C3%B4tel-Dieu_de_Qu%C3%A9bec\" title=\"Hôtel-Dieu de Québec\">Hôtel-Dieu de Québec</a> (d. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Augustine\" title=\"<PERSON> of St. Augustine\"><PERSON> of St. Augustine</a>, French-Canadian nurse and candidate for sainthood, founded the <a href=\"https://wikipedia.org/wiki/H%C3%B4tel-Dieu_de_Qu%C3%A9bec\" title=\"Hôtel-Dieu de Québec\">Hôtel-Dieu de Québec</a> (d. 1668)", "links": [{"title": "<PERSON> of St. Augustine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_St._Augustine"}, {"title": "Hôtel-Dieu de Québec", "link": "https://wikipedia.org/wiki/H%C3%B4tel-Dieu_de_Qu%C3%A9bec"}]}, {"year": "1662", "text": "<PERSON><PERSON><PERSON><PERSON>, German architect, designed the Pillnitz Castle (d. 1736)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/Matth%C3%A4<PERSON>_<PERSON>_P%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/Pillnitz_Castle\" title=\"Pillnitz Castle\">Pillnitz Castle</a> (d. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matth%C3%A4<PERSON>_<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/Pillnitz_Castle\" title=\"Pillnitz Castle\">Pillnitz Castle</a> (d. 1736)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matth%C3%A4<PERSON>_<PERSON>_P%C3%B6<PERSON><PERSON>"}, {"title": "Pillnitz Castle", "link": "https://wikipedia.org/wiki/Pillnitz_Castle"}]}, {"year": "1678", "text": "<PERSON><PERSON>, Spanish corsair (d. 1747)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/Amaro_Pargo\" title=\"Amaro Pargo\"><PERSON><PERSON></a>, Spanish corsair (d. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amaro_<PERSON>rgo\" title=\"Amaro Pargo\"><PERSON><PERSON></a>, Spanish corsair (d. 1747)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amar<PERSON>_<PERSON>rgo"}]}, {"year": "1695", "text": "<PERSON>, French physicist and engineer, invented the <PERSON><PERSON> tube (d. 1771)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and engineer, invented the <a href=\"https://wikipedia.org/wiki/<PERSON>_tube\" title=\"Pitot tube\">Pitot tube</a> (d. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and engineer, invented the <a href=\"https://wikipedia.org/wiki/<PERSON>_tube\" title=\"Pitot tube\">Pitot tube</a> (d. 1771)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pitot tube", "link": "https://wikipedia.org/wiki/Pitot_tube"}]}, {"year": "1729", "text": "<PERSON><PERSON><PERSON>, Czech composer (d. 1774)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech composer (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech composer (d. 1774)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON>, German playwright and author (d. 1819)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/August_von_<PERSON>\" title=\"August von <PERSON>\">August von <PERSON></a>, German playwright and author (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_von_<PERSON>\" title=\"August <PERSON>\">August von <PERSON></a>, German playwright and author (d. 1819)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "Princess <PERSON><PERSON><PERSON><PERSON> of France (d. 1794)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/Princess_%C3%89lisabeth_of_France\" class=\"mw-redirect\" title=\"Princess <PERSON><PERSON><PERSON><PERSON> of France\">Princess <PERSON><PERSON><PERSON><PERSON> of France</a> (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_%C3%89lisabeth_of_France\" class=\"mw-redirect\" title=\"Princess <PERSON><PERSON><PERSON><PERSON> of France\">Princess <PERSON><PERSON><PERSON><PERSON> of France</a> (d. 1794)", "links": [{"title": "Princess <PERSON><PERSON><PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/Princess_%C3%89lisabeth_of_France"}]}, {"year": "1768", "text": "<PERSON>, Scottish chemist and businessman (d. 1838)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and businessman (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and businessman (d. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1783", "text": "<PERSON>, Peruvian soldier and politician, 1st President of Peru and 2nd President of North Peru (d. 1858)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_la_Riva_Ag%C3%BCero\" title=\"<PERSON>güero\"><PERSON></a>, Peruvian soldier and politician, 1st President of Peru and 2nd President of North Peru (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_la_Riva_Ag%C3%BCero\" title=\"<PERSON> Agüero\"><PERSON></a>, Peruvian soldier and politician, 1st President of Peru and 2nd President of North Peru (d. 1858)", "links": [{"title": "<PERSON> la Riva Agüero", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_la_Riva_Ag%C3%BCero"}]}, {"year": "1814", "text": "<PERSON>, Canadian lawyer and politician, 4th Lieutenant Governor of Nova Scotia (d. 1892)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Nova_Scotia\" title=\"Lieutenant Governor of Nova Scotia\">Lieutenant Governor of Nova Scotia</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Nova_Scotia\" title=\"Lieutenant Governor of Nova Scotia\">Lieutenant Governor of Nova Scotia</a> (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Nova Scotia", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Nova_Scotia"}]}, {"year": "1826", "text": "<PERSON> of Sweden (d. 1872)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XV_of_Sweden\" class=\"mw-redirect\" title=\"Charles XV of Sweden\"><PERSON> of Sweden</a> (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XV_of_Sweden\" class=\"mw-redirect\" title=\"Charles XV of Sweden\"><PERSON> of Sweden</a> (d. 1872)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Charles_XV_of_Sweden"}]}, {"year": "1844", "text": "<PERSON>, English talent agent and composer (d. 1901)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27O<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English talent agent and composer (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27O<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English talent agent and composer (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_D%27Oyl<PERSON>_<PERSON><PERSON>"}]}, {"year": "1849", "text": "<PERSON>, Danish-American journalist and photographer (d. 1914)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American journalist and photographer (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American journalist and photographer (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, German soldier and politician, Chancellor of Germany (d. 1929)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BClow"}, {"title": "Chancellor of Germany (German Reich)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)"}]}, {"year": "1854", "text": "<PERSON>, American baseball player and manager (d. 1933)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, American executive in Major League Baseball (d. 1931)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\"><PERSON></a>, American executive in <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\"><PERSON></a>, American executive in <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>"}, {"title": "Major League Baseball", "link": "https://wikipedia.org/wiki/Major_League_Baseball"}]}, {"year": "1860", "text": "<PERSON><PERSON>, Italian mathematician and physicist (d. 1940)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Vito_Volterra\" title=\"Vito Volterra\"><PERSON><PERSON></a>, Italian mathematician and physicist (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vito_Volterra\" title=\"Vito Volterra\"><PERSON><PERSON></a>, Italian mathematician and physicist (d. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vito_Volterra"}]}, {"year": "1867", "text": "<PERSON>, American boxer (d. 1894)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON> <PERSON><PERSON>, English cricketer (d. 1944)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_Hearne\" title=\"J. <PERSON><PERSON> Hearne\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_Hearne\" title=\"J. T. Hearne\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer (d. 1944)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>ne"}]}, {"year": "1870", "text": "Princess <PERSON> of Schleswig-Holstein (d. 1948)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Schleswig-Holstein\" title=\"Princess <PERSON> of Schleswig-Holstein\">Princess <PERSON> of Schleswig-Holstein</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Schleswig-Holstein\" title=\"Princess <PERSON> of Schleswig-Holstein\">Princess <PERSON> of Schleswig-Holstein</a> (d. 1948)", "links": [{"title": "Princess <PERSON> of Schleswig-Holstein", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Schleswig-Holstein"}]}, {"year": "1871", "text": "<PERSON><PERSON>, American criminal (d. 1937)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American criminal (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American criminal (d. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, German-Ukrainian general and politician, Hetman of Ukraine (d. 1945)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Ukrainian general and politician, <a href=\"https://wikipedia.org/wiki/Hetman_of_Ukraine\" class=\"mw-redirect\" title=\"Hetman of Ukraine\">Hetman of Ukraine</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Ukrainian general and politician, <a href=\"https://wikipedia.org/wiki/Hetman_of_Ukraine\" class=\"mw-redirect\" title=\"Hetman of Ukraine\">Hetman of Ukraine</a> (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pavlo_Skoropadskyi"}, {"title": "<PERSON><PERSON> of Ukraine", "link": "https://wikipedia.org/wiki/Hetman_of_Ukraine"}]}, {"year": "1874", "text": "<PERSON>, French businessman and publisher, founded <PERSON><PERSON> (d. 1934)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Co<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and publisher, founded <a href=\"https://wikipedia.org/wiki/Coty\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Co<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and publisher, founded <a href=\"https://wikipedia.org/wiki/Co<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Coty"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Coty"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON>, Swedish oceanographer and academic (d. 1954)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Vagn_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Vag<PERSON> <PERSON><PERSON><PERSON><PERSON>\">V<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Swedish oceanographer and academic (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vagn_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Vag<PERSON> <PERSON><PERSON><PERSON><PERSON>\">Vag<PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Swedish oceanographer and academic (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vagn_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, German psychoanalyst and author (d. 1925)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychoanalyst and author (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychoanalyst and author (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Australian businessman and soldier, co-founded Qantas  (d. 1950)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and soldier, co-founded <a href=\"https://wikipedia.org/wiki/Qantas\" title=\"Qantas\">Qantas</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and soldier, co-founded <a href=\"https://wikipedia.org/wiki/Qantas\" title=\"Qantas\">Qantas</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Qantas", "link": "https://wikipedia.org/wiki/Qantas"}]}, {"year": "1886", "text": "<PERSON>, French organist and composer (d. 1971)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Dupr%C3%A9"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Greek actress (d. 1954)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actress (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actress (d. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mari<PERSON>_<PERSON>ouli"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, American actress (d. 1981)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, German-Canadian Olympic soccer player (d. 1972)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Canadian Olympic soccer player (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Canadian Olympic soccer player (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Polish poet and critic (d. 1969)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and critic (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and critic (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON>, American baseball pitcher (d. 1963)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/E<PERSON>_R<PERSON>ey\" title=\"Eppa Rixey\"><PERSON><PERSON></a>, American baseball pitcher (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rixey\" title=\"Eppa Rixey\"><PERSON><PERSON></a>, American baseball pitcher (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>_<PERSON>ixey"}]}, {"year": "1892", "text": "<PERSON>, English physicist and academic, Nobel Prize laureate (d. 1975)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1892", "text": "<PERSON>, Canadian-American economist and academic (d. 1970)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American economist and academic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American economist and academic (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Georgian author (d. 1975)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian author (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian author (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dia"}]}, {"year": "1895", "text": "<PERSON>, Dutch philosopher, theologian, and apologist (d. 1987)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher, theologian, and apologist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher, theologian, and apologist (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, German soldier and pilot (d. 1917)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6der\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6der\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_Allmenr%C3%B6der"}]}, {"year": "1896", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer, jurist, and politician, Indian Minister of Defence (d. 1974)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer, jurist, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer, jurist, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a> (d. 1974)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Defence (India)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(India)"}]}, {"year": "1896", "text": "<PERSON><PERSON>, English author and playwright (d. 1990)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and playwright (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and playwright (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Canadian lawyer and politician, 20th Solicitor General of Canada (d. 1989)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Canada\" title=\"Solicitor General of Canada\">Solicitor General of Canada</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Canada\" title=\"Solicitor General of Canada\">Solicitor General of Canada</a> (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Solicitor General of Canada", "link": "https://wikipedia.org/wiki/Solicitor_General_of_Canada"}]}, {"year": "1898", "text": "<PERSON><PERSON>, American educator and activist (d. 1987)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Septima_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Septima Poinsette Clark\">Sept<PERSON> Po<PERSON><PERSON></a>, American educator and activist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Septima_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Septima Poinsette Clark\">Septima Po<PERSON><PERSON></a>, American educator and activist (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Septima_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Ukrainian-Israeli educator and politician, 4th Prime Minister of Israel (d. 1978)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Israeli educator and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Israeli educator and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}]}, {"year": "1902", "text": "<PERSON>, German-French physicist and poet, Nobel Prize laureate (d. 1984)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French physicist and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French physicist and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1903", "text": "<PERSON>, American singer and actor (d. 1977)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American baseball pitcher and coach (d. 1986)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Red_Ruffing\" title=\"Red Ruffing\"><PERSON></a>, American baseball pitcher and coach (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Ruffing\" title=\"Red Ruffing\"><PERSON>uff<PERSON></a>, American baseball pitcher and coach (d. 1986)", "links": [{"title": "Red Ruffing", "link": "https://wikipedia.org/wiki/Red_Ruffing"}]}, {"year": "1906", "text": "<PERSON>, American actress (d. 1987)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American journalist and author (d. 1975)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American screenwriter and producer (d. 2011)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American organist and composer (d. 1980)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and composer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and composer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Fox"}]}, {"year": "1912", "text": "<PERSON>, American poet, novelist and memoirist (d. 1995)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/May_<PERSON>\" title=\"May <PERSON>\"><PERSON></a>, American poet, novelist and memoirist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_<PERSON>\" title=\"May <PERSON>\"><PERSON></a>, American poet, novelist and memoirist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/May_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American playwright and novelist (d. 1973)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and novelist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and novelist (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ge"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, French journalist, author, and poet (d. 2018)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist, author, and poet (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist, author, and poet (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Canadian wrestler and trainer, founded Stampede Wrestling (d. 2003)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Stu_Hart\" title=\"Stu Hart\"><PERSON><PERSON></a>, Canadian wrestler and trainer, founded <a href=\"https://wikipedia.org/wiki/Stampede_Wrestling\" title=\"Stampede Wrestling\">Stampede Wrestling</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stu_Hart\" title=\"Stu Hart\"><PERSON><PERSON></a>, Canadian wrestler and trainer, founded <a href=\"https://wikipedia.org/wiki/Stampede_Wrestling\" title=\"Stampede Wrestling\">Stampede Wrestling</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>u_Hart"}, {"title": "Stampede Wrestling", "link": "https://wikipedia.org/wiki/Stampede_Wrestling"}]}, {"year": "1917", "text": "<PERSON>, American screenwriter and librettist (d. 2006)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and librettist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and librettist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Finnish-American actor (d. 2016)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish-American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish-American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Macedonian politician and first president of the Republic of Macedonia (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian politician and first president of the Republic of Macedonia (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian politician and first president of the Republic of Macedonia (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English footballer and manager (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (d. 2003)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1919", "text": "<PERSON>, American singer-songwriter, guitarist, and activist (d. 2014)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/1919\" title=\"1919\">1919</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and activist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1919\" title=\"1919\">1919</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and activist (d. 2014)", "links": [{"title": "1919", "link": "https://wikipedia.org/wiki/1919"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American pianist and composer (d. 2001)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American pianist and composer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American pianist and composer (d. 2001)", "links": [{"title": "<PERSON> (pianist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(pianist)"}]}, {"year": "1921", "text": "<PERSON>, American boxer (d. 1989)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Sugar Ray Robinson\"><PERSON></a>, American boxer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Sugar Ray Robinson\"><PERSON></a>, American boxer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English footballer and journalist (d. 2000)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and journalist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and journalist (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American lieutenant, lawyer, and politician (d. 2019)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, German-Israeli author and poet (d. 2000)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Israeli author and poet (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Israeli author and poet (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English race car driver, founded Tyrrell Racing (d. 2001)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver, founded <a href=\"https://wikipedia.org/wiki/<PERSON>rrell_Racing\" title=\"Tyrrell Racing\">Tyrrell Racing</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver, founded <a href=\"https://wikipedia.org/wiki/Tyrrell_Racing\" title=\"Tyrrell Racing\">Tyrrell Racing</a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tyrrell Racing", "link": "https://wikipedia.org/wiki/<PERSON>rrell_Racing"}]}, {"year": "1928", "text": "<PERSON>, American singer-songwriter (d. 2003)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, French mathematician (d. 2001)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"Jacques-Louis Lions\"><PERSON><PERSON><PERSON></a>, French mathematician (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"Jacques-Louis Lions\"><PERSON><PERSON><PERSON></a>, French mathematician (d. 2001)", "links": [{"title": "Jacques-Louis Lions", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Argentinian poet and author (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian poet and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian poet and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actor and historian (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and historian (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and historian (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter, producer, and actor (d. 2006)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1934", "text": "<PERSON>, English boxer and sportscaster (d. 2011)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer and sportscaster (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer and sportscaster (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Egyptian-French singer-songwriter and guitarist (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer-songwriter and guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American singer and actor", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American businessman, founded the Ronco Company  (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Ronco\" title=\"Ronco\">Ronco Company</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Ronco\" title=\"Ronco\">Ronco Company</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ronco", "link": "https://wikipedia.org/wiki/<PERSON>co"}]}, {"year": "1938", "text": "<PERSON>, Egyptian terrorist (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian terrorist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian terrorist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American engineer, businessman, and philanthropist (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, businessman, and philanthropist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, businessman, and philanthropist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Dutch footballer and manager", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Clemens_Westerhof\" title=\"Clemens Westerhof\"><PERSON><PERSON><PERSON>er<PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clemens_Westerhof\" title=\"Clemens Westerhof\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Westerhof"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Georgian chess player, Women's World Champion, 1962-1978", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian chess player, <a href=\"https://wikipedia.org/wiki/Women%27s_world_chess_champion\" class=\"mw-redirect\" title=\"Women's world chess champion\">Women's World Champion</a>, 1962-1978", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian chess player, <a href=\"https://wikipedia.org/wiki/Women%27s_world_chess_champion\" class=\"mw-redirect\" title=\"Women's world chess champion\">Women's World Champion</a>, 1962-1978", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Women's world chess champion", "link": "https://wikipedia.org/wiki/Women%27s_world_chess_champion"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Czech gymnast and coach (d. 2016)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/V%C4%9Bra_%C4%8C%C3%A1slavsk%C3%A1\" title=\"<PERSON><PERSON>ra <PERSON>\"><PERSON><PERSON><PERSON></a>, Czech gymnast and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C4%9Bra_%C4%8C%C3%A1slavsk%C3%A1\" title=\"<PERSON><PERSON>ra <PERSON>\"><PERSON><PERSON><PERSON></a>, Czech gymnast and coach (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C4%9Bra_%C4%8C%C3%A1slavsk%C3%A1"}]}, {"year": "1942", "text": "<PERSON>, American soldier and politician, 32nd Governor of Idaho", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_Idaho\" class=\"mw-redirect\" title=\"Governor of Idaho\">Governor of Idaho</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_Idaho\" class=\"mw-redirect\" title=\"Governor of Idaho\">Governor of Idaho</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Idaho", "link": "https://wikipedia.org/wiki/Governor_of_Idaho"}]}, {"year": "1943", "text": "<PERSON>, American lawyer and politician, 31st Governor of Idaho", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/Governor_of_Idaho\" class=\"mw-redirect\" title=\"Governor of Idaho\">Governor of Idaho</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/Governor_of_Idaho\" class=\"mw-redirect\" title=\"Governor of Idaho\">Governor of Idaho</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Idaho", "link": "https://wikipedia.org/wiki/Governor_of_Idaho"}]}, {"year": "1943", "text": "<PERSON>, Mexican boxer (d. 1985)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican boxer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican boxer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American football player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American sportscaster (d. 2024)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian magician (d. 2000)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian magician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian magician (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, British-American academic and geographer (d. 2008)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American academic and geographer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American academic and geographer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English physician and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American academic and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Welsh singer-songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Cross\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Indian politician, 21st Chief Minister of Rajasthan", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Ash<PERSON>_<PERSON>\" title=\"Ashok Gehlot\"><PERSON><PERSON></a>, Indian politician, 21st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Rajasthan\" class=\"mw-redirect\" title=\"Chief Minister of Rajasthan\">Chief Minister of Rajasthan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ash<PERSON>_<PERSON>\" title=\"Ashok Gehlot\"><PERSON><PERSON></a>, Indian politician, 21st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Rajasthan\" class=\"mw-redirect\" title=\"Chief Minister of Rajasthan\">Chief Minister of Rajasthan</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lot"}, {"title": "Chief Minister of Rajasthan", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Rajasthan"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Russian author and publicist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author and publicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author and publicist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tolstaya"}]}, {"year": "1952", "text": "<PERSON>, American pastor and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American cardinal", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, British geneticist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British geneticist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British geneticist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian cricketer, coach, and sportscaster (d. 2004)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, coach, and sportscaster (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, coach, and sportscaster (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Taiwanese-American ice hockey player and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American author and illustrator", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Danish-English comedian, writer, and broadcaster", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-English comedian, writer, and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-English comedian, writer, and broadcaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>vig"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Indian activist and politician, 16th Chief Minister of Madhya Pradesh", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist and politician, 16th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Madhya_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Madhya Pradesh\">Chief Minister of Madhya Pradesh</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist and politician, 16th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Madhya_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Madhya Pradesh\">Chief Minister of Madhya Pradesh</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Chief Minister of Madhya Pradesh", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Madhya_Pradesh"}]}, {"year": "1959", "text": "<PERSON>, English actor, director, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>-<PERSON>, English sprinter and educator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English sprinter and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English sprinter and educator", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American lawyer and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Kurdish activist and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kurdish activist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kurdish activist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American basketball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Pakistani-Scottish journalist and academic", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani-Scottish journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani-Scottish journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mona_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American drummer and songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian-American ice hockey player and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Syrian patriarch", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>rem_II\" title=\"<PERSON><PERSON><PERSON>phrem II\"><PERSON><PERSON><PERSON></a>, Syrian patriarch", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_II\" title=\"<PERSON><PERSON><PERSON>phrem II\"><PERSON><PERSON><PERSON></a>, Syrian patriarch", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_II"}]}, {"year": "1965", "text": "<PERSON>, Northern Irish director, writer, cinematographer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_critic)\" class=\"mw-redirect\" title=\"<PERSON> (film critic)\"><PERSON></a>, Northern Irish director, writer, cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_critic)\" class=\"mw-redirect\" title=\"<PERSON> (film critic)\"><PERSON></a>, Northern Irish director, writer, cinematographer", "links": [{"title": "<PERSON> (film critic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_critic)"}]}, {"year": "1965", "text": "<PERSON>, Danish footballer and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Russian businessman", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian rugby league coach and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league coach and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league coach and manager", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1967", "text": "<PERSON>, Canadian producer, writer, director, actor, and comedian", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hot<PERSON>\"><PERSON></a>, Canadian producer, writer, director, actor, and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hotz\"><PERSON></a>, Canadian producer, writer, director, actor, and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, British politician, the first elected MP for the UK Independence Party", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, the first elected MP for the <a href=\"https://wikipedia.org/wiki/UK_Independence_Party\" title=\"UK Independence Party\">UK Independence Party</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, the first elected MP for the <a href=\"https://wikipedia.org/wiki/UK_Independence_Party\" title=\"UK Independence Party\">UK Independence Party</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "UK Independence Party", "link": "https://wikipedia.org/wiki/UK_Independence_Party"}]}, {"year": "1972", "text": "<PERSON>, English lawyer and politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Welsh sprinter and television host", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sprinter and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sprinter and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American television journalist and host", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist and host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist and host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress and model", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Sri Lankan politician (d. 2024)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan politician (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian footballer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_footballer)"}]}, {"year": "1976", "text": "<PERSON>, Australian footballer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_footballer)"}]}, {"year": "1977", "text": "<PERSON>, American country music singer-songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian baseball player and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Tyronn_Lue\" title=\"Tyronn Lue\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tyronn_Lue\" title=\"Tyronn Lue\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "Tyronn Lue", "link": "https://wikipedia.org/wiki/Tyronn_Lue"}]}, {"year": "1977", "text": "<PERSON>, American soccer player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English-American singer-songwriter and guitarist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician,_born_1978)\" class=\"mw-redirect\" title=\"<PERSON> (musician, born 1978)\"><PERSON></a>, English-American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician,_born_1978)\" class=\"mw-redirect\" title=\"<PERSON> (musician, born 1978)\"><PERSON></a>, English-American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician, born 1978)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician,_born_1978)"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lawrence <PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lawrence_<PERSON>s\" title=\"Lawrence Tynes\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lawrence_Tynes"}]}, {"year": "1982", "text": "<PERSON>, Ukrainian-American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Dutch footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer (d. 2015)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/M%C3%A1rton_F%C3%BCl%C3%B6p\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1rton_F%C3%BCl%C3%B6p\" title=\"<PERSON><PERSON><PERSON>l<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1rton_F%C3%BCl%C3%B6p"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Belgian politician", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, French actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Klement<PERSON>f\" title=\"<PERSON><PERSON> Klementieff\"><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lement<PERSON>f\" title=\"<PERSON><PERSON> Klementieff\"><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>m_<PERSON>f"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Turkish actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_S%C3%B6nmez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_S%C3%B6nmez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Damla_S%C3%B6nmez"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Irish mixed martial artist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, New Zealand rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Hungarian swimmer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Katinka_Hossz%C3%BA\" title=\"Katink<PERSON> Ho<PERSON>zú\"><PERSON><PERSON><PERSON></a>, Hungarian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Katinka_Hossz%C3%BA\" title=\"Katinka Ho<PERSON>zú\"><PERSON><PERSON><PERSON></a>, Hungarian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Katinka_Hossz%C3%BA"}]}, {"year": "1990", "text": "<PERSON>, American actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Harvey_Guill%C3%A9n"}]}, {"year": "1990", "text": "<PERSON>, American golfer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian cricketer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, South Korean musician", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Russian chess player (d. 2016)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American baseball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Austin_Meadows\" title=\"Austin Meadows\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Meadows\" title=\"Austin Meadows\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_Meadows"}]}, {"year": "1996", "text": "<PERSON>, American runner", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1996", "text": "<PERSON>, Nigerian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Lithuanian-American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>is\" title=\"<PERSON>antas <PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>as <PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Domantas_<PERSON>is"}]}, {"year": "1996", "text": "<PERSON>, American actor", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>r"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American football player (d. 2022)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON> , English cricketer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON> </a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON> </a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "2001", "text": "<PERSON>, American actress and singer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>z\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>z\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>z"}]}], "Deaths": [{"year": "678", "text": "<PERSON><PERSON><PERSON>, Japanese princess", "html": "678 - <a href=\"https://wikipedia.org/wiki/Princess_T%C5%8Dchi\" title=\"Princess <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese princess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_T%C5%8Dchi\" title=\"Princess <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese princess", "links": [{"title": "Princess <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Princess_T%C5%8Dchi"}]}, {"year": "738", "text": "Uaxacla<PERSON><PERSON>, Mayan ruler (ajaw)", "html": "738 - <a href=\"https://wikipedia.org/wiki/Uaxaclajuun_Ub%27aah_K%27awiil\" class=\"mw-redirect\" title=\"Uaxaclajuun Ub'aah K'awiil\">Uaxaclajuun Ub'aah <PERSON></a>, Mayan ruler (<i><a href=\"https://wikipedia.org/wiki/Ajaw\" title=\"Ajaw\">ajaw</a></i>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uaxaclajuun_Ub%27aah_K%27awiil\" class=\"mw-redirect\" title=\"Uaxaclajuun Ub'aah K'awiil\">Uaxaclajuun Ub'aah <PERSON></a>, Mayan ruler (<i><a href=\"https://wikipedia.org/wiki/Ajaw\" title=\"Ajaw\">ajaw</a></i>)", "links": [{"title": "Uaxaclajuun <PERSON>", "link": "https://wikipedia.org/wiki/Uaxaclajuun_Ub%27aah_K%27awiil"}, {"title": "Ajaw", "link": "https://wikipedia.org/wiki/Ajaw"}]}, {"year": "1152", "text": "<PERSON> of Boulogne (b. 1105)", "html": "1152 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Boulogne\" class=\"mw-redirect\" title=\"<PERSON> of Boulogne\"><PERSON> of Boulogne</a> (b. 1105)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Boulogne\" class=\"mw-redirect\" title=\"<PERSON> of Boulogne\"><PERSON> of Boulogne</a> (b. 1105)", "links": [{"title": "<PERSON> of Boulogne", "link": "https://wikipedia.org/wiki/<PERSON>_of_Boulogne"}]}, {"year": "1270", "text": "<PERSON><PERSON><PERSON> IV of Hungary (b. 1206)", "html": "1270 - <a href=\"https://wikipedia.org/wiki/B%C3%A9la_IV_of_Hungary\" title=\"Béla IV of Hungary\">Béla IV of Hungary</a> (b. 1206)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9la_IV_of_Hungary\" title=\"Béla IV of Hungary\">Béla IV of Hungary</a> (b. 1206)", "links": [{"title": "Béla IV of Hungary", "link": "https://wikipedia.org/wiki/B%C3%A9la_IV_of_Hungary"}]}, {"year": "1294", "text": "<PERSON>, Duke of Brabant (b. 1252)", "html": "1294 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (b. 1252)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (b. 1252)", "links": [{"title": "<PERSON>, Duke of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1330", "text": "<PERSON><PERSON>, Emperor of Trebizond (b. 1282)", "html": "1330 - <a href=\"https://wikipedia.org/wiki/Alexios_II_of_Trebizond\" title=\"Alexios II of Trebizond\">Alexios II Megas Komnenos</a>, <a href=\"https://wikipedia.org/wiki/Empire_of_Trebizond\" title=\"Empire of Trebizond\">Emperor of Trebizond</a> (b. 1282)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alexios_II_of_Trebizond\" title=\"Alexios II of Trebizond\">Alexios II Megas Komnenos</a>, <a href=\"https://wikipedia.org/wiki/Empire_of_Trebizond\" title=\"Empire of Trebizond\">Emperor of Trebizond</a> (b. 1282)", "links": [{"title": "Alexios II of Trebizond", "link": "https://wikipedia.org/wiki/Alexios_II_of_Trebizond"}, {"title": "Empire of Trebizond", "link": "https://wikipedia.org/wiki/Empire_of_Trebizond"}]}, {"year": "1410", "text": "Antipop<PERSON>", "html": "1410 - <a href=\"https://wikipedia.org/wiki/Antipope_Alexander_V\" title=\"Antipope Alexander V\">Antipope Alexander V</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antipope_Alexander_V\" title=\"Antipope Alexander V\">Antipope Alexander V</a>", "links": [{"title": "Antipop<PERSON>", "link": "https://wikipedia.org/wiki/Antipop<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1481", "text": "<PERSON><PERSON><PERSON> the Conqueror, Ottoman sultan (b. 1432)", "html": "1481 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Conqueror\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Conqueror\"><PERSON><PERSON><PERSON> the Conqueror</a>, Ottoman sultan (b. 1432)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Conqueror\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Conqueror\"><PERSON><PERSON><PERSON> the Conqueror</a>, Ottoman sultan (b. 1432)", "links": [{"title": "<PERSON><PERSON><PERSON> the Conqueror", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Conqueror"}]}, {"year": "1501", "text": "<PERSON>, 9th Baron <PERSON> of Chartley, English Baron (b. 1463)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_9th_Baron_<PERSON>_of_Chartley\" title=\"<PERSON>, 9th Baron <PERSON> of Chartley\"><PERSON>, 9th Baron <PERSON> of Chartley</a>, English Baron (b. 1463)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_9th_Baron_<PERSON>_of_Chartley\" title=\"<PERSON>, 9th Baron <PERSON> of Chartley\"><PERSON>, 9th Baron <PERSON> of Chartley</a>, English Baron (b. 1463)", "links": [{"title": "<PERSON>, 9th Baron <PERSON> of Chartley", "link": "https://wikipedia.org/wiki/<PERSON>,_9th_Baron_<PERSON>_of_Chartley"}]}, {"year": "1524", "text": "<PERSON>, 3rd Earl of Kent, English peer (b. 1481)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Kent\" title=\"<PERSON>, 3rd Earl of Kent\"><PERSON>, 3rd Earl of Kent</a>, English peer (b. 1481)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Kent\" title=\"<PERSON>, 3rd Earl of Kent\"><PERSON>, 3rd Earl of Kent</a>, English peer (b. 1481)", "links": [{"title": "<PERSON>, 3rd Earl of Kent", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Kent"}]}, {"year": "1534", "text": "<PERSON><PERSON> <PERSON>, Spanish Roman Catholic nun and venerable (b. 1481)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/Juan<PERSON>_de_la_Cruz_Vazquez_Gutierrez\" class=\"mw-redirect\" title=\"Juana de la Cruz Vazquez Gutierrez\"><PERSON><PERSON> <PERSON> Cruz Vaz<PERSON></a>, Spanish <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> nun and venerable (b. 1481)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan<PERSON>_<PERSON>_la_Cruz_Vazquez_Gutierrez\" class=\"mw-redirect\" title=\"Juana de la Cruz Vazquez Gutierrez\">Juan<PERSON> <PERSON> la Cruz Vazquez <PERSON></a>, Spanish <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> nun and venerable (b. 1481)", "links": [{"title": "Juana de la Cruz Vazquez <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_Cruz_Vazquez_<PERSON>"}, {"title": "Roman Catholic", "link": "https://wikipedia.org/wiki/Roman_Catholic"}]}, {"year": "1589", "text": "<PERSON>, Duke of Brunswick-Lüneburg (b. 1528)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> (b. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> (b. 1528)", "links": [{"title": "<PERSON>, Duke of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1606", "text": "<PERSON>, English priest and author (b. 1555)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1555)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1621", "text": "<PERSON>, English Tudor gentlewoman (b. 1541)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1621)\" title=\"<PERSON> (died 1621)\"><PERSON></a>, English Tudor gentlewoman (b. 1541)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1621)\" title=\"<PERSON> (died 1621)\"><PERSON></a>, English Tudor gentlewoman (b. 1541)", "links": [{"title": "<PERSON> (died 1621)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1621)"}]}, {"year": "1679", "text": "<PERSON>, Scottish archbishop (b. 1613)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Scottish archbishop (b. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Scottish archbishop (b. 1613)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1693", "text": "<PERSON>, duc <PERSON>, French courtier (b. 1607)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_duc_de_<PERSON>\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc de <PERSON></a>, French courtier (b. 1607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_duc_de_<PERSON>\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc de <PERSON></a>, French courtier (b. 1607)", "links": [{"title": "<PERSON>, duc de <PERSON>-Simon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, Czech-Austrian violinist and composer (b. 1644)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian violinist and composer (b. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian violinist and composer (b. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON> the Younger, American lawyer, academic, and politician (b. 1662)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the <PERSON>\"><PERSON> the Younger</a>, American lawyer, academic, and politician (b. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, American lawyer, academic, and politician (b. 1662)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON>, Scottish minister and author (b. 1680)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and author (b. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and author (b. 1680)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, English-American captain and politician, 5th Governor of Restored Proprietary Government (b. 1692)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American captain and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Maryland\" title=\"List of colonial governors of Maryland\">Governor of Restored Proprietary Government</a> (b. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American captain and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Maryland\" title=\"List of colonial governors of Maryland\">Governor of Restored Proprietary Government</a> (b. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Maryland", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Maryland"}]}, {"year": "1758", "text": "<PERSON> (b. 1675)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> (b. 1675)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, French-English author (b. 1679)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English author (b. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English author (b. 1679)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, Italian philosopher, poet, and critic (b. 1712)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher, poet, and critic (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher, poet, and critic (b. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, American mathematician, physicist, and astronomer (b. 1714)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(educator)\" title=\"<PERSON> (educator)\"><PERSON></a>, American mathematician, physicist, and astronomer (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(educator)\" title=\"<PERSON> (educator)\"><PERSON></a>, American mathematician, physicist, and astronomer (b. 1714)", "links": [{"title": "<PERSON> (educator)", "link": "https://wikipedia.org/wiki/<PERSON>_(educator)"}]}, {"year": "1793", "text": "<PERSON>, German historian and theologian (b. 1720)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and theologian (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and theologian (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON>, Italian composer (b. 1771)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer (b. 1771)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON>, French composer and critic (b. 1803)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and critic (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and critic (b. 1803)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON>, Arab-French servant to <PERSON> (b. 1788)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Louis-%C3%89tienne_Saint-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Arab-French servant to <PERSON> (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis-%C3%89tienne_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Arab-French servant to <PERSON> (b. 1788)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-%C3%89tien<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Austrian-Greek general and army minister (b. 1806)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Smolents\" title=\"Leonidas Smolents\"><PERSON><PERSON> Smolents</a>, Austrian-Greek general and army minister (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Smolents\" title=\"Leonidas Smolents\"><PERSON><PERSON> Smolents</a>, Austrian-Greek general and army minister (b. 1806)", "links": [{"title": "Leonidas Smolents", "link": "https://wikipedia.org/wiki/Leonidas_Smolents"}]}, {"year": "1910", "text": "<PERSON>, American pathologist (b. 1871)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pathologist (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pathologist (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Irish rebel (b. 1858)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, Irish rebel (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, Irish rebel (b. 1858)", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)"}]}, {"year": "1916", "text": "<PERSON>, Irish poet and rebel (b. 1878)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and rebel (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and rebel (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Irish teacher and rebel leader (b. 1879)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish teacher and rebel leader (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish teacher and rebel leader (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Chinese businessman and missionary (b. 1863)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese businessman and missionary (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese businessman and missionary (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American educator (b. 1854)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Belgian race car driver (b. 1883)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>\" title=\"Théod<PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, Belgian race car driver (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>\" title=\"Th<PERSON><PERSON><PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, Belgian race car driver (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_Pilette"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, French engineer, designed the Ader Avion III (b. 1841)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A9ment_Ader\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French engineer, designed the <a href=\"https://wikipedia.org/wiki/Ader_Avion_III\" title=\"Ader Avion III\">Ader Avion III</a> (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A9ment_Ader\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French engineer, designed the <a href=\"https://wikipedia.org/wiki/Ader_Avion_III\" title=\"Ader Avion III\">Ader Avion III</a> (b. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cl%C3%A9ment_Ader"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American journalist and author (b. 1874)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Charles_Fort\" title=\"Charles Fort\"><PERSON></a>, American journalist and author (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_Fort\" title=\"Charles Fort\"><PERSON></a>, American journalist and author (b. 1874)", "links": [{"title": "Charles Fort", "link": "https://wikipedia.org/wiki/Charles_Fort"}]}, {"year": "1935", "text": "<PERSON>, American illustrator (b. 1863)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Illustrator\" title=\"Illustrator\">illustrator</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Illustrator\" title=\"Illustrator\">illustrator</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Illustrator", "link": "https://wikipedia.org/wiki/Illustrator"}]}, {"year": "1939", "text": "<PERSON>, French author and poet (b. 1873)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Danish politician, 24th Prime Minister of Denmark (b. 1873)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a> (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Denmark", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Denmark"}]}, {"year": "1943", "text": "<PERSON>, American engineer (b. 1875)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(auto_racing)\" title=\"<PERSON> (auto racing)\"><PERSON></a>, American engineer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(auto_racing)\" title=\"<PERSON> (auto racing)\"><PERSON></a>, American engineer (b. 1875)", "links": [{"title": "<PERSON> (auto racing)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(auto_racing)"}]}, {"year": "1948", "text": "<PERSON>, Finnish assassin of <PERSON><PERSON><PERSON> (b. 1876)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish assassin of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish assassin of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lt"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English footballer and cricketer (b. 1888)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and cricketer (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and cricketer (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English cricketer (b. 1889)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1889)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Indian academic and politician, 3rd President of India (b. 1897)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON></a>, Indian academic and politician, 3rd President of India (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON></a>, Indian academic and politician, 3rd President of India (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(politician)"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Turkish footballer, coach, and pilot (b. 1918)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Cemil_G%C3%BCrgen_Erlert%C3%BCrk\" title=\"Ce<PERSON>l Gürgen E<PERSON>ürk\"><PERSON><PERSON><PERSON>ü<PERSON></a>, Turkish footballer, coach, and pilot (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cemil_G%C3%BCrgen_Erlert%C3%BCrk\" title=\"Ce<PERSON>l Gürgen Erlertürk\"><PERSON><PERSON><PERSON>ü<PERSON></a>, Turkish footballer, coach, and pilot (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cemil_G%C3%BCrgen_Erlert%C3%BCrk"}]}, {"year": "1972", "text": "<PERSON>, Australian lawyer and diplomat, Australian High Commissioner to Canada (b. 1898)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Australian lawyer and diplomat, Australian High Commissioner to Canada (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Australian lawyer and diplomat, Australian High Commissioner to Canada (b. 1898)", "links": [{"title": "<PERSON> (lawyer)", "link": "https://wikipedia.org/wiki/<PERSON>(lawyer)"}]}, {"year": "1972", "text": "<PERSON>, American runner and coach (b. 1883)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor (b. 1904)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American journalist (b. 1914)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Indian actress (b. 1929)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Narg<PERSON>\" title=\"<PERSON>rg<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rg<PERSON>\" title=\"<PERSON>rg<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nargis"}]}, {"year": "1986", "text": "<PERSON>, American actor (b. 1914)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Italian singer, actress, dancer, and model (b. 1933)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer, actress, dancer, and model (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer, actress, dancer, and model (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dalida"}]}, {"year": "1988", "text": "<PERSON>, Russian mathematician and academic (b. 1908)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American trans woman (b. 1926)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Transgender\" title=\"Transgender\">trans woman</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Transgender\" title=\"Transgender\">trans woman</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Transgender", "link": "https://wikipedia.org/wiki/Transgender"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Polish-American novelist and screenwriter (b. 1933)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American novelist and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American novelist and screenwriter (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kosi%C5%84ski"}]}, {"year": "1992", "text": "<PERSON>, American actor, dancer, and politician (b. 1902)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and politician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and politician (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Greek guitarist, composer, and educator (b. 1921)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek guitarist, composer, and educator (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek guitarist, composer, and educator (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American baseball player (b. 1924)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actor (b. 1924)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American rapist and triple murderer (b. 1947)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapist and triple murderer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapist and triple murderer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, French race car driver (b. 1976)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French race car driver (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French race car driver (b. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish guitarist and composer (b. 1927)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Narciso_<PERSON><PERSON>\" title=\"Narc<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish guitarist and composer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Narciso_<PERSON><PERSON>\" title=\"Narcis<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish guitarist and composer (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narcis<PERSON>_<PERSON>es"}]}, {"year": "1998", "text": "<PERSON>, American actor (b. 1908)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American baseball player and manager (b. 1927)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian-American ice hockey player (b. 1967)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English cricketer (b. 1920)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Hungarian glass designer (b. 1901)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/J%C3%BAlia_B%C3%A1thory\" title=\"<PERSON><PERSON><PERSON> Báthory\"><PERSON><PERSON><PERSON></a>, Hungarian glass designer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BAlia_B%C3%A1thory\" title=\"<PERSON><PERSON><PERSON> Báthory\"><PERSON><PERSON><PERSON></a>, Hungarian glass designer (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BAlia_B%C3%A1thory"}]}, {"year": "2000", "text": "<PERSON>, American cardinal (b. 1920)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Con<PERSON>_(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, American cardinal (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, American cardinal (b. 1920)", "links": [{"title": "<PERSON> (cardinal)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(cardinal)"}]}, {"year": "2002", "text": "<PERSON>, Baroness <PERSON> of Blackburn, English politician, First Secretary of State (b. 1910)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Barbara_Castle,_Baroness_Castle_of_Blackburn\" class=\"mw-redirect\" title=\"Barbara Castle, Baroness Castle of Blackburn\"><PERSON> Castle, Baroness Castle of Blackburn</a>, English politician, <a href=\"https://wikipedia.org/wiki/First_Secretary_of_State\" title=\"First Secretary of State\">First Secretary of State</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barbara_Castle,_Baroness_Castle_of_Blackburn\" class=\"mw-redirect\" title=\"Barbara Castle, Baroness Castle of Blackburn\"><PERSON>, Baroness Castle of Blackburn</a>, English politician, <a href=\"https://wikipedia.org/wiki/First_Secretary_of_State\" title=\"First Secretary of State\">First Secretary of State</a> (b. 1910)", "links": [{"title": "Barbara Castle, Baroness Castle of Blackburn", "link": "https://wikipedia.org/wiki/Barbara_Castle,_Baroness_Castle_of_Blackburn"}, {"title": "First Secretary of State", "link": "https://wikipedia.org/wiki/First_Secretary_of_State"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Russian pianist, composer, and conductor (b. 1928)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist, composer, and conductor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist, composer, and conductor (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American model and actress (b. 1932)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English race car driver (b. 1917)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American baseball player, coach, and manager (b. 1928)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Dutch painter, sculptor, and poet (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter, sculptor, and poet (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter, sculptor, and poet (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Indian politician (b. 1949)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>jan\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American colonel, baseball player, and author (b. 1932)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, baseball player, and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, baseball player, and author (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss illustrator (b. 1913)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>vater\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, Swiss illustrator (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>r\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, Swiss illustrator (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American captain, pilot, and astronaut (b. 1923)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Japanese politician (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/K<PERSON>_<PERSON>\" title=\"Knock Yokoyama\"><PERSON><PERSON></a>, Japanese politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON>_<PERSON>\" title=\"Knock Yokoyama\"><PERSON><PERSON></a>, Japanese politician (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Spanish engineer and politician, Prime Minister of Spain (b. 1926)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Sotel<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish engineer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Sotel<PERSON>\" title=\"<PERSON><PERSON>Sotel<PERSON>\"><PERSON><PERSON></a>, Spanish engineer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON>Sotelo", "link": "https://wikipedia.org/wiki/Leopoldo_Calvo-Sotelo"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Canadian pianist (b. 1928)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_Mo<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian pianist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian pianist (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON><PERSON>set"}]}, {"year": "2009", "text": "<PERSON>, Indian author and critic (b. 1931)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ar\" title=\"<PERSON> Ba<PERSON> Shewalkar\"><PERSON></a>, Indian author and critic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ar\" title=\"<PERSON> Ba<PERSON> Shewalkar\"><PERSON></a>, Indian author and critic (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ar"}]}, {"year": "2010", "text": "<PERSON>, American accordion player (b. 1947)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Roy Carrier\"><PERSON></a>, American accordion player (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English soldier and author (b. 1920)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, English soldier and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, English soldier and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Peter_O%27Donnell"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, German-American engineer (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American engineer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American engineer (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American actor, television director, producer and executive (b. 1922)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, television director, producer and executive (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, television director, producer and executive (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Georgian footballer and manager (b. 1936)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian footballer and manager (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian footballer and manager (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ser<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Greek actor and director (b. 1927)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Thanasis_Veggos\" title=\"Thanasis Veggos\"><PERSON><PERSON></a>, Greek actor and director (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thanasis_Veggos\" title=\"Thanasis Veggos\"><PERSON><PERSON></a>, Greek actor and director (b. 1927)", "links": [{"title": "Thanasis Veggos", "link": "https://wikipedia.org/wiki/Thanasis_Veggos"}]}, {"year": "2012", "text": "<PERSON>, Panamanian politician, 30th President of Panama (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian politician, 30th <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian politician, 30th <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Panama", "link": "https://wikipedia.org/wiki/President_of_Panama"}]}, {"year": "2012", "text": "<PERSON>, German-Australian composer, conductor, and critic (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian composer, conductor, and critic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian composer, conductor, and critic (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American engineer and academic (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Jamaican-American saxophonist and flute player (b. 1943)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-American saxophonist and flute player (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-American saxophonist and flute player (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American swimmer and soldier (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer and soldier (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer and soldier (b. 1924)", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>(swimmer)"}]}, {"year": "2013", "text": "<PERSON>, Australian tennis player and sportscaster (b. 1958)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and sportscaster (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and sportscaster (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American pharmacist, co-invented <PERSON><PERSON><PERSON> (b. 1909)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacist, co-invented <a href=\"https://wikipedia.org/wiki/Or<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacist, co-invented <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>el"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1960)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, American football player (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rouse\"><PERSON></a>, American football player (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Croatian politician, 11th Minister of Defence for Croatia (b. 1958)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>ukeli%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician, 11th <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Croatia)\" title=\"Ministry of Defence (Croatia)\">Minister of Defence for Croatia</a> (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician, 11th <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Croatia)\" title=\"Ministry of Defence (Croatia)\">Minister of Defence for Croatia</a> (b. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Branko_Vukeli%C4%87"}, {"title": "Ministry of Defence (Croatia)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Croatia)"}]}, {"year": "2014", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "2014", "text": "<PERSON>, Mexican painter (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" title=\"Francisco Icaza\"><PERSON></a>, Mexican painter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>za\" title=\"Francisco Icaza\"><PERSON></a>, Mexican painter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American educator and politician (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Georgian director and screenwriter (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian director and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian director and screenwriter (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Welsh rugby player (b. 1986)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Welsh rugby player (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Welsh rugby player (b. 1986)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "2015", "text": "<PERSON>, American golfer and coach (b. 1915)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer and coach (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer and coach (b. 1915)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "2016", "text": "<PERSON>, Canadian politician (b. 1937)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, Yugoslav singer-songwriter  (b. 1950)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Stojakovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslav</a> singer-songwriter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Stojakovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslav</a> singer-songwriter (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jadranka_Stojakovi%C4%87"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Israeli actress, singer and model (b. 1942)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli actress, singer and model (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli actress, singer and model (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Moldovan animated film director (b. 1926)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Victoria_Barb%C4%83\" title=\"Victoria Barbă\"><PERSON></a>, Moldovan animated film director (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Barb%C4%83\" title=\"Victoria Barbă\"><PERSON></a>, Moldovan animated film director (b. 1926)", "links": [{"title": "Victoria Barbă", "link": "https://wikipedia.org/wiki/Victoria_Barb%C4%83"}]}, {"year": "2021", "text": "<PERSON>, American R&B vocalist (b. 1933)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Price\"><PERSON></a>, American R&amp;B vocalist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Price\"><PERSON></a>, American R&amp;B vocalist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Price"}]}, {"year": "2024", "text": "<PERSON>, American military aviator and officer (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military aviator and officer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military aviator and officer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}