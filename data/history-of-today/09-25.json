{"date": "September 25", "url": "https://wikipedia.org/wiki/September_25", "data": {"Events": [{"year": "275", "text": "For the last time, the Roman Senate chooses an emperor; they elect 75-year-old <PERSON><PERSON>.", "html": "275 - For the last time, the Roman Senate chooses an emperor; they elect 75-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>dius <PERSON>\"><PERSON></a>.", "no_year_html": "For the last time, the Roman Senate chooses an emperor; they elect 75-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>dius <PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "762", "text": "Led by <PERSON>, the Hasanid branch of the Alids begins the Alid Revolt against the Abbasid Caliphate.", "html": "762 - Led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> al-<PERSON>fs al-Z<PERSON>\"><PERSON></a>, the Hasanid branch of the Alids begins the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(762%E2%80%9363)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (762-63)\"><PERSON><PERSON></a> against the Abbasid Caliphate.", "no_year_html": "Led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> al-<PERSON> al-<PERSON>\"><PERSON></a>, the Hasanid branch of the Alids begins the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(762%E2%80%9363)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (762-63)\"><PERSON><PERSON></a> against the Abbasid Caliphate.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> (762-63)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(762%E2%80%9363)"}]}, {"year": "1066", "text": "In the Battle of Stamford Bridge, <PERSON>, the invading King of Norway, is defeated by King <PERSON> of England.", "html": "1066 - In the <a href=\"https://wikipedia.org/wiki/Battle_of_Stamford_Bridge\" title=\"Battle of Stamford Bridge\">Battle of Stamford Bridge</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the invading King of Norway, is defeated by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"Harold II of England\"><PERSON> of England</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Battle_of_Stamford_Bridge\" title=\"Battle of Stamford Bridge\">Battle of Stamford Bridge</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the invading King of Norway, is defeated by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"Harold II of England\"><PERSON> of England</a>.", "links": [{"title": "Battle of Stamford Bridge", "link": "https://wikipedia.org/wiki/Battle_of_Stamford_Bridge"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1237", "text": "England and Scotland sign the Treaty of York, establishing the location of their common border.", "html": "1237 - England and Scotland sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_York\" title=\"Treaty of York\">Treaty of York</a>, establishing the location of their common border.", "no_year_html": "England and Scotland sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_York\" title=\"Treaty of York\">Treaty of York</a>, establishing the location of their common border.", "links": [{"title": "Treaty of York", "link": "https://wikipedia.org/wiki/Treaty_of_York"}]}, {"year": "1396", "text": "Ottoman Emperor <PERSON><PERSON><PERSON> defeats a Christian army at the Battle of Nicopolis.", "html": "1396 - Ottoman Emperor <PERSON><PERSON><PERSON> I defeats a Christian army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Nicopolis\" title=\"Battle of Nicopolis\">Battle of Nicopolis</a>.", "no_year_html": "Ottoman Emperor <PERSON><PERSON><PERSON> I defeats a Christian army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Nicopolis\" title=\"Battle of Nicopolis\">Battle of Nicopolis</a>.", "links": [{"title": "Battle of Nicopolis", "link": "https://wikipedia.org/wiki/Battle_of_Nicopolis"}]}, {"year": "1513", "text": "Spanish explorer <PERSON><PERSON> reaches what would become known as the Pacific Ocean.", "html": "1513 - Spanish explorer <a href=\"https://wikipedia.org/wiki/Vasco_N%C3%BA%C3%B1ez_de_Balboa\" title=\"Vasco Núñez de Balboa\">Vasco Núñez de Balboa</a> reaches what would become known as the Pacific Ocean.", "no_year_html": "Spanish explorer <a href=\"https://wikipedia.org/wiki/Vasco_N%C3%BA%C3%B1ez_de_Balboa\" title=\"Vasco Núñez de Balboa\">Vasco Núñez de Balboa</a> reaches what would become known as the Pacific Ocean.", "links": [{"title": "Vasco Núñez de Balboa", "link": "https://wikipedia.org/wiki/Vasco_N%C3%BA%C3%B1ez_de_Balboa"}]}, {"year": "1555", "text": "The Peace of Augsburg is signed by Emperor <PERSON> and the princes of the Schmalkaldic League.", "html": "1555 - The <a href=\"https://wikipedia.org/wiki/Peace_of_Augsburg\" title=\"Peace of Augsburg\">Peace of Augsburg</a> is signed by Emperor <PERSON> and the princes of the Schmalkaldic League.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peace_of_Augsburg\" title=\"Peace of Augsburg\">Peace of Augsburg</a> is signed by Emperor <PERSON> and the princes of the Schmalkaldic League.", "links": [{"title": "Peace of Augsburg", "link": "https://wikipedia.org/wiki/Peace_of_Augsburg"}]}, {"year": "1690", "text": "Publick Occurrences Both Forreign and Domestick, the first newspaper to appear in the Americas, is published for the first and only time.", "html": "1690 - <i><a href=\"https://wikipedia.org/wiki/Publick_Occurrences_Both_Forreign_and_Domestick\" title=\"Publick Occurrences Both Forreign and Domestick\">Publick Occurrences Both Forreign and Domestick</a></i>, the first newspaper to appear in the Americas, is published for the first and only time.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Publick_Occurrences_Both_Forreign_and_Domestick\" title=\"Publick Occurrences Both Forreign and Domestick\">Publick Occurrences Both Forreign and Domestick</a></i>, the first newspaper to appear in the Americas, is published for the first and only time.", "links": [{"title": "Publick Occurrences Both Forreign and Domestick", "link": "https://wikipedia.org/wiki/Publick_Occurrences_Both_Forreign_and_Domestick"}]}, {"year": "1768", "text": "Unification of Nepal", "html": "1768 - <a href=\"https://wikipedia.org/wiki/Unification_of_Nepal\" title=\"Unification of Nepal\">Unification of Nepal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Unification_of_Nepal\" title=\"Unification of Nepal\">Unification of Nepal</a>", "links": [{"title": "Unification of Nepal", "link": "https://wikipedia.org/wiki/Unification_of_Nepal"}]}, {"year": "1775", "text": "American Revolution: <PERSON> surrenders to British forces after attempting to capture Montreal during the Battle of Longue-Pointe.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <PERSON> surrenders to British forces after attempting to capture Montreal during the <a href=\"https://wikipedia.org/wiki/Battle_of_Longue-Pointe\" title=\"Battle of Longue-Pointe\">Battle of Longue-Pointe</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <PERSON> surrenders to British forces after attempting to capture Montreal during the <a href=\"https://wikipedia.org/wiki/Battle_of_Longue-Pointe\" title=\"Battle of Longue-Pointe\">Battle of Longue-Pointe</a>.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "Battle of Longue-Pointe", "link": "https://wikipedia.org/wiki/Battle_of_Longue-Pointe"}]}, {"year": "1775", "text": "American Revolution: <PERSON>'s expedition to Quebec sets off.", "html": "1775 - American Revolution: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_expedition_to_Quebec\" title=\"<PERSON>'s expedition to Quebec\"><PERSON>'s expedition to Quebec</a> sets off.", "no_year_html": "American Revolution: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_expedition_to_Quebec\" title=\"<PERSON>'s expedition to Quebec\"><PERSON>'s expedition to Quebec</a> sets off.", "links": [{"title": "<PERSON>'s expedition to Quebec", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_expedition_to_Quebec"}]}, {"year": "1786", "text": "The mine of Huancavelica in the Peruvian Andes collapses killing more than hundred people. The event was a major setback for quicksilver production in the Spanish Empire.", "html": "1786 - <a href=\"https://wikipedia.org/wiki/1786\" title=\"1786\">1786</a> - The mine of <a href=\"https://wikipedia.org/wiki/Huancavelica\" title=\"Huancavelica\">Huancavelica</a> in the Peruvian Andes collapses killing more than hundred people. The event was a major setback for <a href=\"https://wikipedia.org/wiki/Mercury_(element)\" title=\"Mercury (element)\">quicksilver</a> production in the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1786\" title=\"1786\">1786</a> - The mine of <a href=\"https://wikipedia.org/wiki/Huancavelica\" title=\"Huancavelica\">Huancavelica</a> in the Peruvian Andes collapses killing more than hundred people. The event was a major setback for <a href=\"https://wikipedia.org/wiki/Mercury_(element)\" title=\"Mercury (element)\">quicksilver</a> production in the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a>.", "links": [{"title": "1786", "link": "https://wikipedia.org/wiki/1786"}, {"title": "Huancavelica", "link": "https://wikipedia.org/wiki/Huancavelica"}, {"title": "Mercury (element)", "link": "https://wikipedia.org/wiki/Mercury_(element)"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}]}, {"year": "1789", "text": "The United States Congress passes twelve constitutional amendments: the ten known as the Bill of Rights, the (unratified) Congressional Apportionment Amendment, and the Congressional Compensation Amendment.", "html": "1789 - The United States Congress passes twelve constitutional amendments: the ten known as the <a href=\"https://wikipedia.org/wiki/United_States_Bill_of_Rights\" title=\"United States Bill of Rights\">Bill of Rights</a>, the (unratified) <a href=\"https://wikipedia.org/wiki/Congressional_Apportionment_Amendment\" title=\"Congressional Apportionment Amendment\">Congressional Apportionment Amendment</a>, and the <a href=\"https://wikipedia.org/wiki/Twenty-seventh_Amendment_to_the_United_States_Constitution\" title=\"Twenty-seventh Amendment to the United States Constitution\">Congressional Compensation Amendment</a>.", "no_year_html": "The United States Congress passes twelve constitutional amendments: the ten known as the <a href=\"https://wikipedia.org/wiki/United_States_Bill_of_Rights\" title=\"United States Bill of Rights\">Bill of Rights</a>, the (unratified) <a href=\"https://wikipedia.org/wiki/Congressional_Apportionment_Amendment\" title=\"Congressional Apportionment Amendment\">Congressional Apportionment Amendment</a>, and the <a href=\"https://wikipedia.org/wiki/Twenty-seventh_Amendment_to_the_United_States_Constitution\" title=\"Twenty-seventh Amendment to the United States Constitution\">Congressional Compensation Amendment</a>.", "links": [{"title": "United States Bill of Rights", "link": "https://wikipedia.org/wiki/United_States_Bill_of_Rights"}, {"title": "Congressional Apportionment Amendment", "link": "https://wikipedia.org/wiki/Congressional_Apportionment_Amendment"}, {"title": "Twenty-seventh Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Twenty-seventh_Amendment_to_the_United_States_Constitution"}]}, {"year": "1790", "text": "Four Great Anhui Troupes introduce Anhui opera to Beijing in honor of the Qianlong Emperor's eightieth birthday.", "html": "1790 - Four Great Anhui Troupes introduce <a href=\"https://wikipedia.org/wiki/Huiju\" class=\"mw-redirect\" title=\"Huiju\">Anhui opera</a> to Beijing in honor of the Qi<PERSON>long Emperor's eightieth birthday.", "no_year_html": "Four Great Anhui Troupes introduce <a href=\"https://wikipedia.org/wiki/Huiju\" class=\"mw-redirect\" title=\"Huiju\">Anhui opera</a> to Beijing in honor of the Qi<PERSON><PERSON> Emperor's eightieth birthday.", "links": [{"title": "Huiju", "link": "https://wikipedia.org/wiki/Huiju"}]}, {"year": "1804", "text": "The Teton Sioux (a subdivision of the Lakota) demand one of the boats from the Lewis and Clark Expedition as a toll for allowing the expedition to move further upriver.", "html": "1804 - The Teton Sioux (a subdivision of the Lakota) demand one of the boats from the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Expedition\" title=\"Lewis and Clark Expedition\"><PERSON> and Clark Expedition</a> as a toll for allowing the expedition to move further upriver.", "no_year_html": "The Teton Sioux (a subdivision of the Lakota) demand one of the boats from the <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_Expedition\" title=\"Lewis and Clark Expedition\"><PERSON> and <PERSON> Expedition</a> as a toll for allowing the expedition to move further upriver.", "links": [{"title": "<PERSON> and Clark Expedition", "link": "https://wikipedia.org/wiki/<PERSON>_and_Clark_Expedition"}]}, {"year": "1868", "text": "The Imperial Russian steam frigate <PERSON> is shipwrecked off Jutland while carrying Grand Duke <PERSON> of Russia.", "html": "1868 - The <a href=\"https://wikipedia.org/wiki/Russian_frigate_<PERSON>\" title=\"Russian frigate <PERSON>\">Imperial Russian steam frigate <i><PERSON></i></a> is shipwrecked off Jutland while carrying <a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Russian_frigate_<PERSON>\" title=\"Russian frigate <PERSON>\">Imperial Russian steam frigate <i><PERSON></i></a> is shipwrecked off Jutland while carrying <a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a>.", "links": [{"title": "Russian frigate <PERSON>", "link": "https://wikipedia.org/wiki/Russian_frigate_<PERSON>_<PERSON>"}, {"title": "Grand Duke <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1890", "text": "The United States Congress establishes Sequoia National Park.", "html": "1890 - The United States Congress establishes <a href=\"https://wikipedia.org/wiki/Sequoia_National_Park\" title=\"Sequoia National Park\">Sequoia National Park</a>.", "no_year_html": "The United States Congress establishes <a href=\"https://wikipedia.org/wiki/Sequoia_National_Park\" title=\"Sequoia National Park\">Sequoia National Park</a>.", "links": [{"title": "Sequoia National Park", "link": "https://wikipedia.org/wiki/Sequoia_National_Park"}]}, {"year": "1906", "text": "<PERSON> demonstrates the Telekino in the Bilbao Abra (Spain), guiding an electric boat from the shore with people on board, which was controlled at a distance over 2 km (1.2 mi), in what is considered to be the origin of modern wireless remote-control operation principles.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> demonstrates the <i>Telekino</i> in the <a href=\"https://wikipedia.org/wiki/Bilbao_Abra\" title=\"Bilbao Abra\">Bilbao Abra</a> (<a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>), guiding an electric boat from the shore with people on board, which was controlled at a distance over 2 km (1.2 mi), in what is considered to be the origin of modern wireless remote-control operation principles.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> demonstrates the <i>Telekino</i> in the <a href=\"https://wikipedia.org/wiki/Bilbao_Abra\" title=\"Bilbao Abra\">Bilbao Abra</a> (<a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>), guiding an electric boat from the shore with people on board, which was controlled at a distance over 2 km (1.2 mi), in what is considered to be the origin of modern wireless remote-control operation principles.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bilbao Abra", "link": "https://wikipedia.org/wiki/Bilbao_Abra"}, {"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}]}, {"year": "1911", "text": "An explosion of badly degraded propellant charges on board the French battleship Liberté detonates the forward ammunition magazines and destroys the ship.", "html": "1911 - An explosion of badly degraded propellant charges on board the <a href=\"https://wikipedia.org/wiki/French_battleship_Libert%C3%A9\" title=\"French battleship <PERSON><PERSON><PERSON>\">French battleship <i>Liberté</i></a> detonates the forward ammunition magazines and destroys the ship.", "no_year_html": "An explosion of badly degraded propellant charges on board the <a href=\"https://wikipedia.org/wiki/French_battleship_Libert%C3%A9\" title=\"French battleship <PERSON><PERSON><PERSON>\">French battleship <i>Liberté</i></a> detonates the forward ammunition magazines and destroys the ship.", "links": [{"title": "French battleship Liberté", "link": "https://wikipedia.org/wiki/French_battleship_<PERSON>bert%C3%A9"}]}, {"year": "1912", "text": "Columbia University Graduate School of Journalism is founded in New York City.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Columbia_University_Graduate_School_of_Journalism\" title=\"Columbia University Graduate School of Journalism\">Columbia University Graduate School of Journalism</a> is founded in New York City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Columbia_University_Graduate_School_of_Journalism\" title=\"Columbia University Graduate School of Journalism\">Columbia University Graduate School of Journalism</a> is founded in New York City.", "links": [{"title": "Columbia University Graduate School of Journalism", "link": "https://wikipedia.org/wiki/Columbia_University_Graduate_School_of_Journalism"}]}, {"year": "1915", "text": "World War I: The Second Battle of Champagne begins.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Champagne\" title=\"Second Battle of Champagne\">Second Battle of Champagne</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Champagne\" title=\"Second Battle of Champagne\">Second Battle of Champagne</a> begins.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Second Battle of Champagne", "link": "https://wikipedia.org/wiki/Second_Battle_of_Champagne"}]}, {"year": "1918", "text": "World War I: The end of the Battle of Megiddo, the climax of the British Army's Sinai and Palestine campaign under General <PERSON>.", "html": "1918 - World War I: The end of the <a href=\"https://wikipedia.org/wiki/Battle_of_Megiddo_(1918)\" title=\"Battle of Megiddo (1918)\">Battle of Megiddo</a>, the climax of the British Army's <a href=\"https://wikipedia.org/wiki/Sinai_and_Palestine_campaign\" title=\"Sinai and Palestine campaign\">Sinai and Palestine campaign</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "World War I: The end of the <a href=\"https://wikipedia.org/wiki/Battle_of_Megiddo_(1918)\" title=\"Battle of Megiddo (1918)\">Battle of Megiddo</a>, the climax of the British Army's <a href=\"https://wikipedia.org/wiki/Sinai_and_Palestine_campaign\" title=\"Sinai and Palestine campaign\">Sinai and Palestine campaign</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Battle of Megiddo (1918)", "link": "https://wikipedia.org/wiki/Battle_of_Megiddo_(1918)"}, {"title": "Sinai and Palestine campaign", "link": "https://wikipedia.org/wiki/Sinai_and_Palestine_campaign"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "The international Convention to Suppress the Slave Trade and Slavery is first signed.", "html": "1926 - The international <a href=\"https://wikipedia.org/wiki/1926_Slavery_Convention\" title=\"1926 Slavery Convention\">Convention to Suppress the Slave Trade and Slavery</a> is first signed.", "no_year_html": "The international <a href=\"https://wikipedia.org/wiki/1926_Slavery_Convention\" title=\"1926 Slavery Convention\">Convention to Suppress the Slave Trade and Slavery</a> is first signed.", "links": [{"title": "1926 Slavery Convention", "link": "https://wikipedia.org/wiki/1926_Slavery_Convention"}]}, {"year": "1937", "text": "Second Sino-Japanese War: The Chinese Eighth Route Army gains a minor, but morale-boosting victory in the Battle of Pingxingguan.", "html": "1937 - Second Sino-Japanese War: The Chinese Eighth Route Army gains a minor, but morale-boosting victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Pingxingguan\" title=\"Battle of Pingxingguan\">Battle of Pingxingguan</a>.", "no_year_html": "Second Sino-Japanese War: The Chinese Eighth Route Army gains a minor, but morale-boosting victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Pingxingguan\" title=\"Battle of Pingxingguan\">Battle of Pingxingguan</a>.", "links": [{"title": "Battle of Pingxingguan", "link": "https://wikipedia.org/wiki/Battle_of_Pingxingguan"}]}, {"year": "1944", "text": "World War II: Surviving elements of the British 1st Airborne Division withdraw from Arnhem via Oosterbeek.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Surviving elements of the British 1st Airborne Division <a href=\"https://wikipedia.org/wiki/Operation_Berlin_(Arnhem)\" title=\"Operation Berlin (Arnhem)\">withdraw from Arnhem</a> via Oosterbeek.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Surviving elements of the British 1st Airborne Division <a href=\"https://wikipedia.org/wiki/Operation_Berlin_(Arnhem)\" title=\"Operation Berlin (Arnhem)\">withdraw from Arnhem</a> via Oosterbeek.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Berlin (Arnhem)", "link": "https://wikipedia.org/wiki/Operation_Berlin_(Arnhem)"}]}, {"year": "1955", "text": "The Royal Jordanian Air Force is founded.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Royal_Jordanian_Air_Force\" title=\"Royal Jordanian Air Force\">Royal Jordanian Air Force</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Jordanian_Air_Force\" title=\"Royal Jordanian Air Force\">Royal Jordanian Air Force</a> is founded.", "links": [{"title": "Royal Jordanian Air Force", "link": "https://wikipedia.org/wiki/Royal_Jordanian_Air_Force"}]}, {"year": "1956", "text": "TAT-1, the first submarine transatlantic telephone cable system, is inaugurated.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/TAT-1\" title=\"TAT-1\">TAT-1</a>, the first submarine transatlantic telephone cable system, is inaugurated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TAT-1\" title=\"TAT-1\">TAT-1</a>, the first submarine transatlantic telephone cable system, is inaugurated.", "links": [{"title": "TAT-1", "link": "https://wikipedia.org/wiki/TAT-1"}]}, {"year": "1957", "text": "Central High School in Little Rock, Arkansas, is integrated by the use of United States Army troops.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Little_Rock_Central_High_School\" title=\"Little Rock Central High School\">Central High School</a> in Little Rock, Arkansas, is integrated by the use of United States Army troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Little_Rock_Central_High_School\" title=\"Little Rock Central High School\">Central High School</a> in Little Rock, Arkansas, is integrated by the use of United States Army troops.", "links": [{"title": "Little Rock Central High School", "link": "https://wikipedia.org/wiki/Little_Rock_Central_High_School"}]}, {"year": "1959", "text": "<PERSON>, Prime Minister of Sri Lanka, is mortally wounded by a Buddhist monk, <PERSON><PERSON><PERSON><PERSON>, and dies the next day.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Solomon_Bandaranaike\" class=\"mw-redirect\" title=\"Solomon Bandaranaike\"><PERSON></a>, Prime Minister of Sri Lanka, is mortally wounded by a Buddhist monk, <a href=\"https://wikipedia.org/wiki/Talduwe_Somarama\" title=\"Talduwe Somarama\"><PERSON><PERSON><PERSON><PERSON></a>, and dies the next day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solomon_Bandaranaike\" class=\"mw-redirect\" title=\"Solomon Bandaranaike\"><PERSON></a>, Prime Minister of Sri Lanka, is mortally wounded by a Buddhist monk, <a href=\"https://wikipedia.org/wiki/Talduwe_Somarama\" title=\"Talduwe Somarama\"><PERSON><PERSON><PERSON><PERSON></a>, and dies the next day.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Solomon_Bandaranaike"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taldu<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "The People's Democratic Republic of Algeria is formally proclaimed. <PERSON><PERSON><PERSON> is elected President of the provisional government.", "html": "1962 - The People's Democratic Republic of Algeria is formally proclaimed. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elected President of the provisional government.", "no_year_html": "The People's Democratic Republic of Algeria is formally proclaimed. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elected President of the provisional government.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "The North Yemen Civil War begins when <PERSON> dethrones the newly crowned <PERSON> and declares Yemen a republic under his presidency.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/North_Yemen_Civil_War\" class=\"mw-redirect\" title=\"North Yemen Civil War\">North Yemen Civil War</a> begins when <PERSON> dethrones the newly crowned <PERSON> and declares Yemen a republic under his presidency.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/North_Yemen_Civil_War\" class=\"mw-redirect\" title=\"North Yemen Civil War\">North Yemen Civil War</a> begins when <PERSON> dethrones the newly crowned <PERSON> and declares Yemen a republic under his presidency.", "links": [{"title": "North Yemen Civil War", "link": "https://wikipedia.org/wiki/North_Yemen_Civil_War"}]}, {"year": "1963", "text": "Lord <PERSON> releases the UK government's official report on the Profumo affair.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\">Lord <PERSON></a> releases the UK government's official report on the <a href=\"https://wikipedia.org/wiki/Profumo_affair\" title=\"Profumo affair\">Profumo affair</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\">Lord <PERSON></a> releases the UK government's official report on the <a href=\"https://wikipedia.org/wiki/Profumo_affair\" title=\"Profumo affair\">Profumo affair</a>.", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Profumo affair", "link": "https://wikipedia.org/wiki/Profumo_affair"}]}, {"year": "1964", "text": "The Mozambican War of Independence against Portugal begins.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/Mozambican_War_of_Independence\" title=\"Mozambican War of Independence\">Mozambican War of Independence</a> against Portugal begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mozambican_War_of_Independence\" title=\"Mozambican War of Independence\">Mozambican War of Independence</a> against Portugal begins.", "links": [{"title": "Mozambican War of Independence", "link": "https://wikipedia.org/wiki/Mozambican_War_of_Independence"}]}, {"year": "1969", "text": "The charter establishing the Organisation of Islamic Cooperation is signed.", "html": "1969 - The charter establishing the <a href=\"https://wikipedia.org/wiki/Organisation_of_Islamic_Cooperation\" title=\"Organisation of Islamic Cooperation\">Organisation of Islamic Cooperation</a> is signed.", "no_year_html": "The charter establishing the <a href=\"https://wikipedia.org/wiki/Organisation_of_Islamic_Cooperation\" title=\"Organisation of Islamic Cooperation\">Organisation of Islamic Cooperation</a> is signed.", "links": [{"title": "Organisation of Islamic Cooperation", "link": "https://wikipedia.org/wiki/Organisation_of_Islamic_Cooperation"}]}, {"year": "1974", "text": "Dr. <PERSON> performs first ulnar collateral ligament replacement surgery (better known as <PERSON> surgery) on baseball player <PERSON>.", "html": "1974 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs first <a href=\"https://wikipedia.org/wiki/Ulnar_collateral_ligament_reconstruction\" title=\"Ulnar collateral ligament reconstruction\">ulnar collateral ligament replacement</a> surgery (better known as <PERSON> surgery) on baseball player <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> John\"><PERSON></a>.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs first <a href=\"https://wikipedia.org/wiki/Ulnar_collateral_ligament_reconstruction\" title=\"Ulnar collateral ligament reconstruction\">ulnar collateral ligament replacement</a> surgery (better known as <PERSON> surgery) on baseball player <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ulnar collateral ligament reconstruction", "link": "https://wikipedia.org/wiki/Ulnar_collateral_ligament_reconstruction"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "About 4,200 people take part in the first running of the Chicago Marathon.", "html": "1977 - About 4,200 people take part in the first running of the <a href=\"https://wikipedia.org/wiki/Chicago_Marathon\" title=\"Chicago Marathon\">Chicago Marathon</a>.", "no_year_html": "About 4,200 people take part in the first running of the <a href=\"https://wikipedia.org/wiki/Chicago_Marathon\" title=\"Chicago Marathon\">Chicago Marathon</a>.", "links": [{"title": "Chicago Marathon", "link": "https://wikipedia.org/wiki/Chicago_Marathon"}]}, {"year": "1978", "text": "PSA Flight 182, a Boeing 727, collides in mid-air with a Cessna 172 and crashes in San Diego, killing all 135 aboard Flight 182, both occupants of the Cessna, as well as seven people on the ground. ", "html": "1978 - <a href=\"https://wikipedia.org/wiki/PSA_Flight_182\" class=\"mw-redirect\" title=\"PSA Flight 182\">PSA Flight 182</a>, a Boeing 727, collides in mid-air with a Cessna 172 and crashes in San Diego, killing all 135 aboard Flight 182, both occupants of the Cessna, as well as seven people on the ground. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/PSA_Flight_182\" class=\"mw-redirect\" title=\"PSA Flight 182\">PSA Flight 182</a>, a Boeing 727, collides in mid-air with a Cessna 172 and crashes in San Diego, killing all 135 aboard Flight 182, both occupants of the Cessna, as well as seven people on the ground. ", "links": [{"title": "PSA Flight 182", "link": "https://wikipedia.org/wiki/PSA_Flight_182"}]}, {"year": "1981", "text": "Belize joins the United Nations.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Belize\" title=\"Belize\">Belize</a> joins the United Nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Belize\" title=\"Belize\">Belize</a> joins the United Nations.", "links": [{"title": "Belize", "link": "https://wikipedia.org/wiki/Belize"}]}, {"year": "1983", "text": "Thirty-eight IRA prisoners, armed with six handguns, hijack a prison meals lorry and smash their way out of the Maze Prison.", "html": "1983 - Thirty-eight IRA prisoners, armed with six handguns, hijack a prison meals lorry and smash their way <a href=\"https://wikipedia.org/wiki/Maze_Prison_escape\" title=\"Maze Prison escape\">out of the Maze Prison</a>.", "no_year_html": "Thirty-eight IRA prisoners, armed with six handguns, hijack a prison meals lorry and smash their way <a href=\"https://wikipedia.org/wiki/Maze_Prison_escape\" title=\"Maze Prison escape\">out of the Maze Prison</a>.", "links": [{"title": "Maze Prison escape", "link": "https://wikipedia.org/wiki/Maze_Prison_escape"}]}, {"year": "1985", "text": "3 civilians killed by alleged supporters of the Palestine Liberation Organization in Larnaca yacht killings. ", "html": "1985 - 3 civilians killed by alleged supporters of the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a> in <a href=\"https://wikipedia.org/wiki/Larnaca_yacht_killings\" title=\"Larnaca yacht killings\">Larnaca yacht killings</a>. ", "no_year_html": "3 civilians killed by alleged supporters of the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a> in <a href=\"https://wikipedia.org/wiki/Larnaca_yacht_killings\" title=\"Larnaca yacht killings\">Larnaca yacht killings</a>. ", "links": [{"title": "Palestine Liberation Organization", "link": "https://wikipedia.org/wiki/Palestine_Liberation_Organization"}, {"title": "Larnaca yacht killings", "link": "https://wikipedia.org/wiki/Larnaca_yacht_killings"}]}, {"year": "1987", "text": "Fijian Governor-General <PERSON><PERSON> is overthrown in a coup d'état led by Lieutenant colonel <PERSON><PERSON><PERSON>.", "html": "1987 - Fijian Governor-General <a href=\"https://wikipedia.org/wiki/Penaia_Ganilau\" title=\"Penaia Ganilau\"><PERSON><PERSON> Ganilau</a> is overthrown in a <a href=\"https://wikipedia.org/wiki/1987_Fijian_coups_d%27%C3%A9tat#September_coup\" title=\"1987 Fijian coups d'état\">coup d'état</a> led by Lieutenant colonel <a href=\"https://wikipedia.org/wiki/Sitiveni_Rabuka\" title=\"Sitiveni Rabuka\">Sitive<PERSON> Rabu<PERSON></a>.", "no_year_html": "Fijian Governor-General <a href=\"https://wikipedia.org/wiki/Penaia_Ganilau\" title=\"Penaia Ganilau\">Penaia Ganilau</a> is overthrown in a <a href=\"https://wikipedia.org/wiki/1987_Fijian_coups_d%27%C3%A9tat#September_coup\" title=\"1987 Fijian coups d'état\">coup d'état</a> led by Lieutenant colonel <a href=\"https://wikipedia.org/wiki/Sitiveni_Rabuka\" title=\"Sitiveni Rabuka\">Sitiveni Rabuka</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Penaia_<PERSON>lau"}, {"title": "1987 Fijian coups d'état", "link": "https://wikipedia.org/wiki/1987_Fijian_coups_d%27%C3%A9tat#September_coup"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sitiveni_Rabuka"}]}, {"year": "1992", "text": "NASA launches the Mars Observer. Eleven months later, the probe would fail while preparing for orbital insertion.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the <i><a href=\"https://wikipedia.org/wiki/Mars_Observer\" title=\"Mars Observer\">Mars Observer</a></i>. Eleven months later, the probe would fail while preparing for orbital insertion.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the <i><a href=\"https://wikipedia.org/wiki/Mars_Observer\" title=\"Mars Observer\">Mars Observer</a></i>. Eleven months later, the probe would fail while preparing for orbital insertion.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mars Observer", "link": "https://wikipedia.org/wiki/Mars_Observer"}]}, {"year": "1997", "text": "NASA launches Space Shuttle Atlantis on STS-86 to the Mir space station.", "html": "1997 - NASA launches <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> on <a href=\"https://wikipedia.org/wiki/STS-86\" title=\"STS-86\">STS-86</a> to the <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i> space station.", "no_year_html": "NASA launches <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> on <a href=\"https://wikipedia.org/wiki/STS-86\" title=\"STS-86\">STS-86</a> to the <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i> space station.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-86", "link": "https://wikipedia.org/wiki/STS-86"}, {"title": "Mir", "link": "https://wikipedia.org/wiki/Mir"}]}, {"year": "1998", "text": "PauknAir Flight 4101, a British Aerospace 146, crashes near Melilla Airport in Melilla, Spain, killing 38 people.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/PauknAir_Flight_4101\" title=\"PauknAir Flight 4101\">PauknAir Flight 4101</a>, a <a href=\"https://wikipedia.org/wiki/British_Aerospace_146\" title=\"British Aerospace 146\">British Aerospace 146</a>, crashes near <a href=\"https://wikipedia.org/wiki/Melilla_Airport\" title=\"Melilla Airport\">Melilla Airport</a> in <a href=\"https://wikipedia.org/wiki/Melilla\" title=\"Melilla\">Melilla</a>, Spain, killing 38 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/PauknAir_Flight_4101\" title=\"PauknAir Flight 4101\">PauknAir Flight 4101</a>, a <a href=\"https://wikipedia.org/wiki/British_Aerospace_146\" title=\"British Aerospace 146\">British Aerospace 146</a>, crashes near <a href=\"https://wikipedia.org/wiki/Melilla_Airport\" title=\"Melilla Airport\">Melilla Airport</a> in <a href=\"https://wikipedia.org/wiki/Melilla\" title=\"Melilla\">Melilla</a>, Spain, killing 38 people.", "links": [{"title": "PauknAir Flight 4101", "link": "https://wikipedia.org/wiki/PauknAir_Flight_4101"}, {"title": "British Aerospace 146", "link": "https://wikipedia.org/wiki/British_Aerospace_146"}, {"title": "Melilla Airport", "link": "https://wikipedia.org/wiki/Melilla_Airport"}, {"title": "Melilla", "link": "https://wikipedia.org/wiki/<PERSON>illa"}]}, {"year": "2003", "text": "The 8.3 Mw  Hokkaidō earthquake strikes just offshore Hokkaidō, Japan.", "html": "2003 - The 8.3 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2003_Hokkaid%C5%8D_earthquake\" class=\"mw-redirect\" title=\"2003 Hokkaidō earthquake\">Hokkaidō earthquake</a> strikes just offshore Hokkaidō, Japan.", "no_year_html": "The 8.3 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2003_Hokkaid%C5%8D_earthquake\" class=\"mw-redirect\" title=\"2003 Hokkaidō earthquake\">Hokkaidō earthquake</a> strikes just offshore Hokkaidō, Japan.", "links": [{"title": "2003 Hokkaidō earthquake", "link": "https://wikipedia.org/wiki/2003_Hokkaid%C5%8D_earthquake"}]}, {"year": "2018", "text": "<PERSON> is sentenced to three to ten years in prison for aggravated sexual assault.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to three to ten years in prison for <a href=\"https://wikipedia.org/wiki/Aggravated_sexual_assault\" title=\"Aggravated sexual assault\">aggravated sexual assault</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to three to ten years in prison for <a href=\"https://wikipedia.org/wiki/Aggravated_sexual_assault\" title=\"Aggravated sexual assault\">aggravated sexual assault</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Aggravated sexual assault", "link": "https://wikipedia.org/wiki/Aggravated_sexual_assault"}]}], "Births": [{"year": "1358", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (d. 1408)", "html": "1358 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yo<PERSON>su\" title=\"Ash<PERSON><PERSON> Yoshimitsu\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (d. 1408)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yo<PERSON>su\" title=\"<PERSON><PERSON><PERSON> Yoshimitsu\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (d. 1408)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1403", "text": "<PERSON> of Anjou (d. 1434)", "html": "1403 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (d. 1434)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (d. 1434)", "links": [{"title": "<PERSON> of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Anjou"}]}, {"year": "1525", "text": "<PERSON>, English explorer and navigator (d. 1584)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/Steven_Borough\" class=\"mw-redirect\" title=\"Steven Borough\">Steven <PERSON></a>, English explorer and navigator (d. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Steven_Borough\" class=\"mw-redirect\" title=\"Steven Borough\">Steven Borough</a>, English explorer and navigator (d. 1584)", "links": [{"title": "Steven <PERSON>", "link": "https://wikipedia.org/wiki/Steven_<PERSON>"}]}, {"year": "1528", "text": "<PERSON>, Duke of Brunswick-Harburg (d. 1603)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Harburg\" title=\"<PERSON>, Duke of Brunswick-Harburg\"><PERSON>, Duke of Brunswick-Harburg</a> (d. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Harburg\" title=\"<PERSON>, Duke of Brunswick-Harburg\"><PERSON>, Duke of Brunswick-Harburg</a> (d. 1603)", "links": [{"title": "<PERSON>, Duke of Brunswick-Harburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Harburg"}]}, {"year": "1529", "text": "<PERSON><PERSON><PERSON><PERSON>, Count of Schwarzburg-Arnstadt (d. 1583)", "html": "1529 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>L<PERSON>,_Count_of_Schwarzburg-Arnstadt\" title=\"<PERSON><PERSON><PERSON><PERSON>, Count of Schwarzburg-Arnstadt\"><PERSON><PERSON><PERSON><PERSON>, Count of Schwarzburg-Arnstadt</a> (d. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_XLI,_Count_of_Schwarzburg-Arnstadt\" title=\"<PERSON><PERSON><PERSON><PERSON>, Count of Schwarzburg-Arnstadt\"><PERSON><PERSON><PERSON><PERSON>, Count of Schwarzburg-Arnstadt</a> (d. 1583)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Count of Schwarzburg-Arnstadt", "link": "https://wikipedia.org/wiki/G%C3%BCnther_XLI,_Count_of_Schwarzburg-Arnstadt"}]}, {"year": "1599", "text": "<PERSON>, Swiss-Italian architect, designed the San Carlo alle Quattro Fontane and Sant'Agnese in Agone (d. 1667)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Italian architect, designed the <a href=\"https://wikipedia.org/wiki/<PERSON>_Carlo_alle_Quattro_Font<PERSON>\" title=\"San Carlo alle Quattro Fontane\">San Carlo alle Quattro Fontane</a> and <a href=\"https://wikipedia.org/wiki/Sant%27Agne<PERSON>_in_Agone\" title=\"Sant'Agnese in Agone\"><PERSON>'Agnes<PERSON> in Agone</a> (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Italian architect, designed the <a href=\"https://wikipedia.org/wiki/<PERSON>_Carlo_alle_Quattro_Fontane\" title=\"San Carlo alle Quattro Fontane\">San Carlo alle Quattro Fontane</a> and <a href=\"https://wikipedia.org/wiki/Sant%27Agne<PERSON>_in_Agone\" title=\"Sant'Agnese in Agone\">Sant'Agnese in Agone</a> (d. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "San Carlo alle Quattro Fontane", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_all<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Sant'Agnese in Agone", "link": "https://wikipedia.org/wiki/Sant%27Agne<PERSON>_in_Agone"}]}, {"year": "1636", "text": "<PERSON>, Prince of Dietrichstein, German prince (d. 1698)", "html": "1636 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Dietrichstein\" title=\"<PERSON>, Prince of Dietrichstein\"><PERSON>, Prince of Dietrichstein</a>, German prince (d. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Dietrichstein\" title=\"<PERSON>, Prince of Dietrichstein\"><PERSON>, Prince of Dietrichstein</a>, German prince (d. 1698)", "links": [{"title": "<PERSON>, Prince of Dietrichstein", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_<PERSON>stein"}]}, {"year": "1644", "text": "<PERSON>, Danish astronomer and instrument maker (d. 1710)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/Ole_R%C3%B8mer\" title=\"<PERSON>\"><PERSON></a>, Danish astronomer and instrument maker (d. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ole_R%C3%B8mer\" title=\"<PERSON>\"><PERSON></a>, Danish astronomer and instrument maker (d. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ole_R%C3%B8mer"}]}, {"year": "1663", "text": "<PERSON>, German organist and composer (d. 1711)", "html": "1663 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1711)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON><PERSON><PERSON>, French composer and theorist (d. 1764)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and theorist (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and theorist (d. 1764)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1694", "text": "<PERSON>, English politician, Prime Minister of the United Kingdom (d. 1754)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1711", "text": "<PERSON><PERSON><PERSON> Emperor of China (d. 1799)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Qianlong Emperor\">Qianlong Emperor</a> of China (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qianlong_Emperor\" title=\"Qianlong Emperor\">Qianlong Emperor</a> of China (d. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON> Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}]}, {"year": "1738", "text": "<PERSON>, American lawyer and politician, 7th Governor of Delaware (d. 1789)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)\" class=\"mw-redirect\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(governor)\" class=\"mw-redirect\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (d. 1789)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}, {"title": "Governor of Delaware", "link": "https://wikipedia.org/wiki/Governor_of_Delaware"}]}, {"year": "1741", "text": "<PERSON><PERSON>, Czech violinist, composer, and director (d. 1805)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech violinist, composer, and director (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech violinist, composer, and director (d. 1805)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1744", "text": "<PERSON> of Prussia (d. 1797)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (d. 1797)", "links": [{"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia"}]}, {"year": "1758", "text": "<PERSON><PERSON>, Austrian pianist and composer (d. 1820)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian pianist and composer (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian pianist and composer (d. 1820)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON>, 2nd Baron <PERSON>, Anglo-Irish politician and peer (d. 1827)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, Anglo-Irish politician and peer (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, Anglo-Irish politician and peer (d. 1827)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, English sailor (d. 1793)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fletcher Christian\"><PERSON></a>, English sailor (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fletcher Christian\"><PERSON></a>, English sailor (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, French general and politician, 2nd Prime Minister of France (d. 1822)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON></a>, French general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON></a>, French general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1822)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1771", "text": "<PERSON><PERSON>, Russian general and politician (d. 1829)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and politician (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and politician (d. 1829)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON><PERSON><PERSON>, Italian entomologist and author (d. 1856)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bassi\"><PERSON><PERSON><PERSON></a>, Italian entomologist and author (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bassi\"><PERSON><PERSON><PERSON></a>, Italian entomologist and author (d. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bassi"}]}, {"year": "1782", "text": "<PERSON>, Irish author and playwright (d. 1824)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author and playwright (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author and playwright (d. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1798", "text": "<PERSON><PERSON><PERSON>, French geologist and engineer (d. 1874)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C3%89<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French geologist and engineer (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C3%89<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French geologist and engineer (d. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C3%89<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, German lawyer and politician, 3rd Mayor of Marburg (d. 1893)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Marburg\" class=\"mw-redirect\" title=\"Mayor of Marburg\">Mayor of Marburg</a> (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Marburg\" class=\"mw-redirect\" title=\"Mayor of Marburg\">Mayor of Marburg</a> (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August_<PERSON>"}, {"title": "Mayor of Marburg", "link": "https://wikipedia.org/wiki/Mayor_of_Marburg"}]}, {"year": "1825", "text": "<PERSON>, American lawyer and politician (d. 1888)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, Swiss lawyer and politician, President of the National Council (d. 1879)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland\" class=\"mw-redirect\" title=\"List of Presidents of the National Council of Switzerland\">President of the National Council</a> (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland\" class=\"mw-redirect\" title=\"List of Presidents of the National Council of Switzerland\">President of the National Council</a> (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of the National Council of Switzerland", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland"}]}, {"year": "1839", "text": "<PERSON>, German palaeontologist and geologist (d. 1904)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German palaeontologist and geologist (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German palaeontologist and geologist (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, French organist and composer (d. 1897)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/L%C3%A9on_Bo%C3%<PERSON>llmann\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9on_Bo%C3%ABllmann\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Bo%C3%ABllmann"}]}, {"year": "1862", "text": "<PERSON>, English-Australian carpenter and politician, 7th Prime Minister of Australia (d. 1952)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian carpenter and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian carpenter and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1865", "text": "<PERSON>, French artist (d. 1937)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, American biologist, geneticist, and embryologist, Nobel Prize laureate (d. 1945)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, geneticist, and embryologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, geneticist, and embryologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, Russian general (d. 1938)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"Yevgeny <PERSON>\">Ye<PERSON><PERSON></a>, Russian general (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Ye<PERSON><PERSON></a>, Russian general (d. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican general and President (d. 1945)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Plutarco_El%C3%ADas_Calles\" title=\"Plutarco Elías Calles\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican general and President (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Plutarco_El%C3%ADas_Calles\" title=\"Plutarco Elías Calles\"><PERSON>lutar<PERSON></a>, Mexican general and President (d. 1945)", "links": [{"title": "Plutarco <PERSON>", "link": "https://wikipedia.org/wiki/Plutarco_El%C3%ADas_Calles"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Filipino lawyer and politician, 4th Governor of Rizal (d. 1963)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Lope K. Santos\"><PERSON><PERSON></a>, Filipino lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor_of_Rizal\" title=\"Governor of Rizal\">Governor of Rizal</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Lope K. Santos\"><PERSON><PERSON></a>, Filipino lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor_of_Rizal\" title=\"Governor of Rizal\">Governor of Rizal</a> (d. 1963)", "links": [{"title": "Lope K. Santos", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>._<PERSON>"}, {"title": "Governor of Rizal", "link": "https://wikipedia.org/wiki/Governor_of_Rizal"}]}, {"year": "1881", "text": "<PERSON>, Chinese author and critic (d. 1936)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese author and critic (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese author and critic (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1884", "text": "<PERSON>, Russian ballet dancer and choreographer (d. 1951)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian ballet dancer and choreographer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian ballet dancer and choreographer (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, German actress (d. 1978)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ralph\" title=\"<PERSON> Ralph\"><PERSON></a>, German actress (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Scottish author and translator (d. 1930)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish author and translator (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish author and translator (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Swedish mathematician and statistician (d. 1985)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r\" title=\"<PERSON>\"><PERSON></a>, Swedish mathematician and statistician (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r\" title=\"<PERSON>\"><PERSON></a>, Swedish mathematician and statistician (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Italian journalist and politician, 7th President of Italy (d. 1990)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}]}, {"year": "1897", "text": "<PERSON>, American novelist and short story writer, Nobel Prize laureate (d. 1962)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1898", "text": "<PERSON>, Ukrainian-American painter and educator (d. 1980)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American painter and educator (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American painter and educator (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet and songwriter (d. 1981)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Udumal<PERSON>_<PERSON>_<PERSON>\" title=\"Udumal<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and songwriter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Udumal<PERSON>_<PERSON>_<PERSON>\" title=\"Udumal<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and songwriter (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Udumalai_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Estonian soldier, lawyer, and politician (d. 1937)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian soldier, lawyer, and politician (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian soldier, lawyer, and politician (d. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k"}]}, {"year": "1901", "text": "<PERSON>, French director and screenwriter (d. 1999)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Australian footballer (d. 1968)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Gordon_Coventry\" title=\"Gordon <PERSON>\"><PERSON></a>, Australian footballer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gordon_Coventry\" title=\"Gordon Coventry\"><PERSON></a>, Australian footballer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gordon_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Latvian-American painter and educator (d. 1970)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-American painter and educator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-American painter and educator (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian composer, pianist, and music critic (d. 1962)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Volfgangs_D%C4%81rzi%C5%86%C5%A1\" title=\"Volfgangs Dārziņš\">Volfgangs <PERSON></a>, Latvian composer, pianist, and music critic (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Volfgangs_D%C4%81rzi%C5%86%C5%A1\" title=\"Volfgangs Dārziņš\">Volfgangs <PERSON></a>, Latvian composer, pianist, and music critic (d. 1962)", "links": [{"title": "Volfgangs Dārziņš", "link": "https://wikipedia.org/wiki/Volfgangs_D%C4%81rzi%C5%86%C5%A1"}]}, {"year": "1906", "text": "<PERSON>, English painter, cartographer, and author (d. 1996)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter, cartographer, and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter, cartographer, and author (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Russian pianist and composer (d. 1975)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist and composer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pianist and composer (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, French director and screenwriter (d. 1977)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American football player and coach (d. 1980)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Trinidadian historian and politician, 1st Prime Minister of Trinidad and Tobago (d. 1981)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian historian and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"Prime Minister of Trinidad and Tobago\">Prime Minister of Trinidad and Tobago</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian historian and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"Prime Minister of Trinidad and Tobago\">Prime Minister of Trinidad and Tobago</a> (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Trinidad and Tobago", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago"}]}, {"year": "1914", "text": "<PERSON>, English naval officer and cricketer (d. 2020)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English naval officer and cricketer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English naval officer and cricketer (d. 2020)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1916", "text": "<PERSON>, Australian author and playwright (d. 2010)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Australian author and playwright (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Australian author and playwright (d. 2010)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Indian economist, sociologist, and journalist (d. 1968)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Upadhyaya\" title=\"<PERSON><PERSON><PERSON> Upadhyaya\"><PERSON><PERSON><PERSON></a>, Indian economist, sociologist, and journalist (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Upadhyaya\" title=\"<PERSON><PERSON><PERSON> Upadhyaya\"><PERSON><PERSON><PERSON></a>, Indian economist, sociologist, and journalist (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Upadhyaya"}]}, {"year": "1917", "text": "<PERSON>, American baseball player and sportscaster (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Ukrainian-Russian actor, director, and screenwriter (d. 1994)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian actor, director, and screenwriter (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian actor, director, and screenwriter (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Indian engineer (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian engineer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian engineer (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1921", "text": "<PERSON>, New Zealand sergeant, accountant, and politician, 31st Prime Minister of New Zealand (d. 1992)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Mu<PERSON>\"><PERSON></a>, New Zealand sergeant, accountant, and politician, 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Mu<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Mu<PERSON>\"><PERSON></a>, New Zealand sergeant, accountant, and politician, 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1922", "text": "<PERSON>, Nauruian educator and politician, 1st President of Nauru (d. 1992)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> DeRoburt\"><PERSON></a>, Nauruian educator and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Hammer DeRoburt\"><PERSON></a>, Nauruian educator and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_DeRob<PERSON>"}, {"title": "President of Nauru", "link": "https://wikipedia.org/wiki/President_of_Nauru"}]}, {"year": "1923", "text": "<PERSON>, American author and academic (d. 2001)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American saxophonist, clarinet player, and composer (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, American saxophonist, clarinet player, and composer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, American saxophonist, clarinet player, and composer (d. 2011)", "links": [{"title": "<PERSON> (jazz musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)"}]}, {"year": "1924", "text": "<PERSON>, English actor and director (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American baseball player (d. 1996)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Webb"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Italian model, actress, and director, Miss Italy 1946 (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian model, actress, and director, <a href=\"https://wikipedia.org/wiki/Miss_Italia\" title=\"Miss Italia\">Miss Italy 1946</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian model, actress, and director, <a href=\"https://wikipedia.org/wiki/Miss_Italia\" title=\"Miss Italia\">Miss Italy 1946</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Silvana_Pampanini"}, {"title": "Miss Italia", "link": "https://wikipedia.org/wiki/Miss_Italia"}]}, {"year": "1926", "text": "<PERSON>, American pastor and author (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American actor (d. 1991)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Aldo_Ray\" title=\"Aldo Ray\"><PERSON><PERSON></a>, American actor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aldo_Ray\" title=\"Aldo Ray\"><PERSON><PERSON></a>, American actor (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aldo_Ray"}]}, {"year": "1927", "text": "<PERSON>, American basketball player and coach (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (d. 2010)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_(basketball)"}]}, {"year": "1927", "text": "<PERSON>, English conductor and educator (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and educator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English actor and screenwriter (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Italian ballerina and actress (d. 2004)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian ballerina and actress (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian ballerina and actress (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Delia_<PERSON>ala"}]}, {"year": "1929", "text": "<PERSON>, American journalist, producer, and author (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, producer, and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, producer, and author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Italian fashion designer, founded Cerruti (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Ni<PERSON>_Cerr<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian fashion designer, founded <a href=\"https://wikipedia.org/wiki/Cerruti_1881\" title=\"Cerruti 1881\">Cerruti</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ni<PERSON>_Cerr<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian fashion designer, founded <a href=\"https://wikipedia.org/wiki/Cerruti_1881\" title=\"Cerruti 1881\">Cerruti</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nino_Cerruti"}, {"title": "Cerruti 1881", "link": "https://wikipedia.org/wiki/Cerruti_1881"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American author, poet, illustrator, and songwriter (d. 1999)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, poet, illustrator, and songwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, poet, illustrator, and songwriter (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian journalist and poet (d. 2005)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ashi\" title=\"Man<PERSON><PERSON><PERSON> Atashi\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian journalist and poet (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ashi\" title=\"Man<PERSON><PERSON><PERSON> Atashi\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian journalist and poet (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manouchehr_Atashi"}]}, {"year": "1931", "text": "<PERSON>, English mathematician and scholar", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and scholar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian pianist and composer (d. 1982)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Welsh footballer and manager (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Spanish lawyer and politician, 1st Prime Minister of Spain (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Adolfo_Su%C3%A1rez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolfo_Su%C3%A1rez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adolfo_Su%C3%A1rez"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American basketball player, coach, and sportscaster", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player, coach, and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Canadian folk singer-songwriter and musician (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian folk singer-songwriter and musician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian folk singer-songwriter and musician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American toy creator and author, created <PERSON> (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American toy creator and author, created <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>pin\"><PERSON></a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American toy creator and author, created <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>pin\"><PERSON></a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1936", "text": "<PERSON>, South African-American actress, singer, and dancer (d. 1996)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American actress, singer, and dancer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American actress, singer, and dancer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>e"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Malian general and politician 2nd President of Mali (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Traor%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malian general and politician 2nd <a href=\"https://wikipedia.org/wiki/President_of_Mali\" class=\"mw-redirect\" title=\"President of Mali\">President of Mali</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>or%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malian general and politician 2nd <a href=\"https://wikipedia.org/wiki/President_of_Mali\" class=\"mw-redirect\" title=\"President of Mali\">President of Mali</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Moussa_Traor%C3%A9"}, {"title": "President of Mali", "link": "https://wikipedia.org/wiki/President_of_Mali"}]}, {"year": "1937", "text": "<PERSON>, American computer scientist and lawyer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English runner and businessman (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and businessman (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and businessman (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Greenlandic priest and politician, 1st Prime Minister of Greenland (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greenlandic priest and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greenland\" title=\"Prime Minister of Greenland\">Prime Minister of Greenland</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greenlandic priest and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greenland\" title=\"Prime Minister of Greenland\">Prime Minister of Greenland</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Greenland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greenland"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Estonian politician (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Enn_<PERSON>\" title=\"Enn <PERSON>\"><PERSON><PERSON></a>, Estonian politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Enn_<PERSON>\" title=\"Enn <PERSON>\"><PERSON><PERSON></a>, Estonian politician (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enn_Tarto"}]}, {"year": "1939", "text": "<PERSON>, English lawyer and politician, Secretary of State for Business, Innovation and Skills (d. 2015)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Business, Innovation and Skills</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Business, Innovation and Skills</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Business, Innovation and Skills", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Indian actor, director, and producer (d. 2009)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Indian_actor)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (Indian actor)\"><PERSON><PERSON><PERSON></a>, Indian actor, director, and producer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Indian_actor)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (Indian actor)\"><PERSON><PERSON><PERSON></a>, Indian actor, director, and producer (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON> (Indian actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Indian_actor)"}]}, {"year": "1939", "text": "<PERSON>, American lawyer and politician, Mayor of Cincinnati", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Cincinnati\" title=\"List of mayors of Cincinnati\">Mayor of Cincinnati</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Cincinnati\" title=\"List of mayors of Cincinnati\">Mayor of Cincinnati</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of mayors of Cincinnati", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Cincinnati"}]}, {"year": "1940", "text": "<PERSON>, Indian-English explorer, historian, and author (d. 2020)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English explorer, historian, and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English explorer, historian, and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>in"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, <PERSON>, English academic and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON><PERSON>, Baroness <PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON><PERSON>, Baroness <PERSON></a>, English academic and politician", "links": [{"title": "<PERSON><PERSON><PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Argentinian boxer (d. 1976)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian boxer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian boxer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor and producer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, French race car driver", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English pianist and educator (d. 2015)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz)\" title=\"<PERSON> (jazz)\"><PERSON></a>, English pianist and educator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(jazz)\" title=\"<PERSON> (jazz)\"><PERSON></a>, English pianist and educator (d. 2015)", "links": [{"title": "<PERSON> (jazz)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz)"}]}, {"year": "1942", "text": "<PERSON>, American singer (d. 2008)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American lieutenant, academic, and politician, 22nd United States Secretary of Defense", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, academic, and politician, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, academic, and politician, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "1943", "text": "<PERSON>, American keyboard player (d. 2006)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American keyboard player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American keyboard player (d. 2006)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American poet and novelist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and novelist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Dominican cricketer (d. 2009)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican cricketer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican cricketer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American lawyer and politician, 29th California State Treasurer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/California_State_Treasurer\" title=\"California State Treasurer\">California State Treasurer</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/California_State_Treasurer\" title=\"California State Treasurer\">California State Treasurer</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "California State Treasurer", "link": "https://wikipedia.org/wiki/California_State_Treasurer"}]}, {"year": "1945", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2014)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Indian cricketer and coach (d. 2023)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer and coach (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer and coach (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, English actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Felicity <PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fe<PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "Felicity <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (d. 1998)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Polish mountaineer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American golfer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Iranian footballer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American bass player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Greek politician and diplomat (d. 1999)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician and diplomat (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician and diplomat (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>dio<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American model and actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter and producer (d. 2013)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actress and screenwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Romanian musical composer and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Vasile_%C8%98irli\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian musical composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasile_%C8%98irli\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian musical composer and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vasile_%C8%98irli"}]}, {"year": "1948", "text": "<PERSON>, Russian businessman", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Spanish director, producer, and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>d%C3%B3var\" title=\"<PERSON>\"><PERSON></a>, Spanish director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3var\" title=\"<PERSON>\"><PERSON></a>, Spanish director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Almod%C3%B3var"}]}, {"year": "1949", "text": "<PERSON>, American tennis player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American saxophonist and composer (d. 2015)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American actor, singer, and director", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, singer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, singer, and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"E<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "E<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Polish cyclist and trainer (d. 2013)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Szozda\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish cyclist and trainer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Szozda\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish cyclist and trainer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_Szozda"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Israeli singer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, American drummer and songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Drummond\"><PERSON><PERSON><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English bishop", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actor, singer, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American basketball player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Scottish-Australian actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American wrestler and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "bell hooks, American author and activist (d. 2021)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Bell_hooks\" title=\"Bell hooks\">bell hooks</a>, American author and activist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bell_hooks\" title=\"Bell hooks\">bell hooks</a>, American author and activist (d. 2021)", "links": [{"title": "Bell hooks", "link": "https://wikipedia.org/wiki/<PERSON>_hooks"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American poet, playwright, and activist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Cherr%C3%ADe_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American poet, playwright, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cherr%C3%ADe_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American poet, playwright, and activist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cherr%C3%ADe_Moraga"}]}, {"year": "1952", "text": "<PERSON>, American actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor, producer, and activist (d. 2004)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and activist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and activist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English mandolin player, keyboard player, and composer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Mandolin\" title=\"Mandolin\">mandolin</a> player, keyboard player, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Mandolin\" title=\"Mandolin\">mandolin</a> player, keyboard player, and composer", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}, {"title": "Mandolin", "link": "https://wikipedia.org/wiki/Mandolin"}]}, {"year": "1953", "text": "<PERSON>, American novelist, short story writer, poet", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American football player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/1954\" title=\"1954\">1954</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1954\" title=\"1954\">1954</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "1954", "link": "https://wikipedia.org/wiki/1954"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sylvester_<PERSON>room"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Dutch physician and academic (d. 2014)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Spanish footballer and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Belgian footballer (d. 1985)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>o_Fornaciari\" title=\"<PERSON>uc<PERSON>o Fornaciari\"><PERSON><PERSON><PERSON><PERSON></a>, Italian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>o_Fornaciari\" title=\"<PERSON>ucchero Fornaciari\"><PERSON><PERSON><PERSON><PERSON>nac<PERSON></a>, Italian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zucchero_Fornaciari"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Brazilian sailor and explorer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian sailor and explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian sailor and explorer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American author and activist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English bass player, songwriter, and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON> <PERSON>, American computer scientist, engineer, and author, founded the Thinking Machines Corporation", "html": "1956 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American computer scientist, engineer, and author, founded the <a href=\"https://wikipedia.org/wiki/Thinking_Machines_Corporation\" title=\"Thinking Machines Corporation\">Thinking Machines Corporation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American computer scientist, engineer, and author, founded the <a href=\"https://wikipedia.org/wiki/Thinking_Machines_Corporation\" title=\"Thinking Machines Corporation\">Thinking Machines Corporation</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Thinking Machines Corporation", "link": "https://wikipedia.org/wiki/Thinking_Machines_Corporation"}]}, {"year": "1956", "text": "<PERSON>, American special effects designer and television host, founded M5 Industries", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American special effects designer and television host, founded <a href=\"https://wikipedia.org/wiki/M5_Industries\" title=\"M5 Industries\">M5 Industries</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American special effects designer and television host, founded <a href=\"https://wikipedia.org/wiki/M5_Industries\" title=\"M5 Industries\">M5 Industries</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "M5 Industries", "link": "https://wikipedia.org/wiki/M5_Industries"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Croatian Protestant theologian and public intellectual", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>f\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian Protestant theologian and public intellectual", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>f\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian Protestant theologian and public intellectual", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miroslav_Volf"}]}, {"year": "1957", "text": "<PERSON>, Russian general (d. 2014)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American keyboard player, composer, and conductor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, South Korean director, producer, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, South Korean director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, South Korean director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-il"}]}, {"year": "1960", "text": "<PERSON>, Ukrainian footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Turkish actor, director, producer, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lantu%C4%9F\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%9F\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mehmet_Aslantu%C4%9F"}]}, {"year": "1961", "text": "<PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, British journalist and presenter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, British journalist and presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, British journalist and presenter", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)"}]}, {"year": "1961", "text": "<PERSON>, Australian cricketer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Tunisian-French psychologist and journalist (d. 2010)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Kalt<PERSON><PERSON>_<PERSON>\" title=\"Ka<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tunisian-French psychologist and journalist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lt<PERSON><PERSON>_<PERSON>\" title=\"Ka<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tunisian-French psychologist and journalist (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kalt<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Polish footballer and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wdow<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Donovan\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American journalist and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English race car driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German organist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Irish actress and singer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German novelist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, German novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, German novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A9"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter and voice actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and voice actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian businessman", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American football player, actor, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Matt_<PERSON>lia"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Canadian voice actress and singer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American basketball player and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, French actress and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, South African cricketer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American director and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Spanish footballer and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_Mart%C3%ADn_V%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian footballer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Russian pianist and educator", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian pianist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian pianist and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American football player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, English-Canadian drummer and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>d\"><PERSON><PERSON></a>, English-Canadian drummer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>d\"><PERSON><PERSON></a>, English-Canadian drummer and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d"}]}, {"year": "1968", "text": "<PERSON>, American economist and academic", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_List\" title=\"John <PERSON>\"><PERSON></a>, American economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, American economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_List"}]}, {"year": "1968", "text": "<PERSON>, American actor, producer, and rapper", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian footballer and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, South African cricketer (d. 2002)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American journalist and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor, comedian, musician and political commentator", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, musician and political commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, musician and political commentator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American baseball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Welsh actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American football player and psychiatrist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and psychiatrist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and psychiatrist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Greek basketball player and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bo<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1971", "text": "<PERSON>, American football player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, English jockey", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English jockey", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian singer-songwriter, guitarist, and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_September\" title=\"<PERSON> September\"><PERSON> September</a>, Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_September\" title=\"<PERSON> September\"><PERSON> September</a>, Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> September", "link": "https://wikipedia.org/wiki/Douglas_September"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"Tija<PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"Tija<PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tija<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English politician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American actress, singer, model, and beauty queen", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, model, and beauty queen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, model, and beauty queen", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, French footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American scholar and diplomat (d. 2008)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, American scholar and diplomat (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, American scholar and diplomat (d. 2008)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)"}]}, {"year": "1974", "text": "<PERSON>, English footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English-American singer and guitarist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English-American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English-American singer and guitarist", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_(guitarist)"}]}, {"year": "1974", "text": "<PERSON>, German fashion designer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian rugby league player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American football player (d. 2019)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Mexican triathlete", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican triathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Jamaican hurdler", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican hurdler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Italian skier", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English entertainer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English entertainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American football player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Santigold\" title=\"Santigold\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santigold\" title=\"Santigold\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "Santigold", "link": "https://wikipedia.org/wiki/Santigold"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>un<PERSON> Billups\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>un<PERSON> Billups\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Puerto Rican-American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>na"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>all\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>all"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1977", "text": "<PERSON>, American illustrator", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Finnish ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Puerto Rican-American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Wil_<PERSON><PERSON>\" title=\"Wil <PERSON>eves\"><PERSON><PERSON></a>, Puerto Rican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil_<PERSON><PERSON>\" title=\"Wil <PERSON>eves\"><PERSON><PERSON></a>, Puerto Rican-American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil_<PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian-New Zealand singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-New Zealand singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-New Zealand singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Cameroonian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Jamaican footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, English model and actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>d\"><PERSON><PERSON></a>, English model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Puerto Rican baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1eiro\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1eiro\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Pi%C3%B1eiro"}]}, {"year": "1979", "text": "<PERSON>, American BMX rider (d. 2012)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(BMX_rider)\" title=\"<PERSON> (BMX rider)\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/BMX\" title=\"BMX\">BMX</a> rider (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(BMX_rider)\" title=\"<PERSON> (BMX rider)\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/BMX\" title=\"BMX\">BMX</a> rider (d. 2012)", "links": [{"title": "<PERSON> (BMX rider)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(BMX_rider)"}, {"title": "BMX", "link": "https://wikipedia.org/wiki/BMX"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American mixed martial artist and wrestler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mixed martial artist and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mixed martial artist and wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Welsh footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, French tennis player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_<PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper, songwriter, producer, and actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/T.I.\" title=\"T.I.\">T.<PERSON>.</a>, American rapper, songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T.I.\" title=\"T.I.\">T.<PERSON>.</a>, American rapper, songwriter, producer, and actor", "links": [{"title": "T.I.", "link": "https://wikipedia.org/wiki/T.I."}]}, {"year": "1981", "text": "<PERSON><PERSON>, American baseball player and manager", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, South Korean actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, South Korean actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bin"}]}, {"year": "1983", "text": "<PERSON>, American actor, rapper, producer, and screenwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, rapper, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, rapper, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Terrance_Pennington\" title=\"Terrance Pennington\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Terrance_Pennington\" title=\"Terrance Pennington\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Terrance_Pennington"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Jamaican singer-songwriter and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>eri<PERSON>_<PERSON>\" title=\"<PERSON>eri<PERSON>\">Ch<PERSON><PERSON></a>, Jamaican singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>eri<PERSON>_<PERSON>\" title=\"<PERSON>eri<PERSON>\">Ch<PERSON><PERSON></a>, Jamaican singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cheri<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Ivory_Latta\" title=\"Ivory Latta\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ivory_Latta\" title=\"Ivory Latta\"><PERSON></a>, American basketball player", "links": [{"title": "Ivory Latta", "link": "https://wikipedia.org/wiki/Ivory_Latta"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Argentinian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Mat%C3%ADas_Si<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mat%C3%ADas_Si<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mat%C3%<PERSON><PERSON>_Si<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor and comedian", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/G%C3%B6khan_G%C3%BCle%C3%A7\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B6khan_G%C3%BCle%C3%A7\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6khan_G%C3%BCle%C3%A7"}]}, {"year": "1985", "text": "<PERSON>, German footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Egyptian-Canadian tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-Canadian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27H<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27H<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27H<PERSON>_(footballer)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, German rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Mart<PERSON>_Strauch\" title=\"Marten Strauch\"><PERSON><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart<PERSON>_Strauch\" title=\"Marten Strauch\"><PERSON><PERSON></a>, German rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Strauch"}]}, {"year": "1986", "text": "<PERSON>, American drummer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1986", "text": "<PERSON>-young, South Korean actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-young\" title=\"<PERSON>-young\"><PERSON>-<PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-young\" title=\"<PERSON>-young\"><PERSON>-young</a>, South Korean actress", "links": [{"title": "<PERSON>-young", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-young"}]}, {"year": "1987", "text": "<PERSON>, Romanian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Turkish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Jordan_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Gavaris"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Curaçaoan footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Curaçaoan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Curaçaoan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Japanese figure skater", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mao Asada\"><PERSON></a>, Japanese figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Russian model, actress, and photographer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian model, actress, and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian model, actress, and photographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rokous"}]}, {"year": "1991", "text": "<PERSON>, American actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Italian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Calle_J%C3%A4rnkrok\" title=\"Calle Järnkrok\"><PERSON><PERSON> Järn<PERSON>rok</a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Calle_J%C3%A4rnkrok\" title=\"Calle Järnkrok\"><PERSON><PERSON> Jä<PERSON></a>, Swedish ice hockey player", "links": [{"title": "Calle Järnkrok", "link": "https://wikipedia.org/wiki/Calle_J%C3%A4rnkrok"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Swiss race car driver", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Zo%C3%ABl_Amberg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zo%C3%ABl_Amberg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zo%C3%ABl_Amberg"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>od_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Australian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Massimo_<PERSON>o"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American figure skater", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Spanish singer-songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Rosal%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rosal%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish singer-songwriter", "links": [{"title": "Rosalía", "link": "https://wikipedia.org/wiki/Rosal%C3%ADa"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Russian ice dancer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Australian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Egyptian-American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Australian race car driver", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Chinese tennis player ", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, Chinese tennis player ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, Chinese tennis player ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "2001", "text": "<PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American actress", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "1066", "text": "<PERSON>, Norwegian king (b. 1015)", "html": "1066 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian king (b. 1015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian king (b. 1015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1066", "text": "<PERSON>, Norwegian princess", "html": "1066 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian princess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian princess", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Haraldsdotter"}]}, {"year": "1066", "text": "<PERSON><PERSON><PERSON>, English son of <PERSON><PERSON>, Earl of Wessex (b. c. 1029)", "html": "1066 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Earl_of_Wessex\" title=\"<PERSON><PERSON>, Earl of Wessex\"><PERSON><PERSON>, Earl of Wessex</a> (b. c. 1029)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Earl_of_Wessex\" title=\"<PERSON><PERSON>, Earl of Wessex\"><PERSON><PERSON>, Earl of Wessex</a> (b. c. 1029)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>, Earl of Wessex", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Earl_of_Wessex"}]}, {"year": "1086", "text": "<PERSON>, Duke of Aquitaine (b. 1025)", "html": "1086 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON>, Duke of Aquitaine</a> (b. 1025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON>, Duke of Aquitaine</a> (b. 1025)", "links": [{"title": "<PERSON>, Duke of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine"}]}, {"year": "1087", "text": "<PERSON>, French nobleman (b. c. 1025)", "html": "1087 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French nobleman (b. c. 1025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French nobleman (b. c. 1025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1333", "text": "<PERSON>, Japanese shōgun (b. 1301)", "html": "1333 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Japanese shō<PERSON> (b. 1301)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Japanese shō<PERSON> (b. 1301)", "links": [{"title": "Prince <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1367", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese poet (b. 1290)", "html": "1367 - <a href=\"https://wikipedia.org/wiki/Jakushitsu_Genk%C5%8D\" title=\"Jakushits<PERSON>ō\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese poet (b. 1290)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jakushitsu_Genk%C5%8D\" title=\"Jakushitsu Genkō\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese poet (b. 1290)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jakushitsu_Genk%C5%8D"}]}, {"year": "1396", "text": "<PERSON>, French knight (b. 1330)", "html": "1396 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French knight (b. 1330)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French knight (b. 1330)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1396", "text": "<PERSON>, French general and admiral (b. 1341)", "html": "1396 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and admiral (b. 1341)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and admiral (b. 1341)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1496", "text": "<PERSON><PERSON>, Italian soldier and politician (b. 1447)", "html": "1496 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soldier and politician (b. 1447)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soldier and politician (b. 1447)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1506", "text": "<PERSON> of Castile (b. 1478)", "html": "1506 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" class=\"mw-redirect\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> (b. 1478)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" class=\"mw-redirect\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> (b. 1478)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile"}]}, {"year": "1534", "text": "<PERSON> (b. 1478)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_VII\" title=\"Pope Clement VII\"><PERSON> <PERSON></a> (b. 1478)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_VII\" title=\"<PERSON> Clement VII\"><PERSON></a> (b. 1478)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1536", "text": "<PERSON>, Dutch author and poet (b. 1511)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and poet (b. 1511)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and poet (b. 1511)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1550", "text": "<PERSON>, German bishop (b. 1490)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bishop (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bishop (b. 1490)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1588", "text": "<PERSON><PERSON><PERSON>, German Gnesio-Lutheran theologian (b. 1527)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Heshusius\"><PERSON><PERSON><PERSON></a>, German Gnesio-Lutheran theologian (b. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Heshusius\"><PERSON><PERSON><PERSON></a>, German Gnesio-Lutheran theologian (b. 1527)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1602", "text": "<PERSON><PERSON><PERSON>, German physician, scholar, and reformer (b. 1525)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician, scholar, and reformer (b. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician, scholar, and reformer (b. 1525)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aspar_<PERSON>cer"}]}, {"year": "1615", "text": "<PERSON><PERSON><PERSON>, English noblewoman and woman of letters (b. 1575)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English noblewoman and woman of letters (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English noblewoman and woman of letters (b. 1575)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1617", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (b. 1572)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Y%C5%8Dzei\" title=\"Emperor <PERSON>-Yōzei\">Emperor <PERSON><PERSON>Y<PERSON><PERSON><PERSON></a> of Japan (b. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Go-Y%C5%8Dzei\" title=\"Emperor Go-Yōzei\">Emperor <PERSON><PERSON>Yō<PERSON><PERSON></a> of Japan (b. 1572)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Go-Y%C5%8Dzei"}]}, {"year": "1617", "text": "<PERSON>, Spanish priest, philosopher, and theologian (b. 1548)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/Francisco_Su%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Spanish priest, philosopher, and theologian (b. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Su%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Spanish priest, philosopher, and theologian (b. 1548)", "links": [{"title": "Francisco <PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Su%C3%A1rez"}]}, {"year": "1621", "text": "<PERSON>, English writer (b. 1561)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer (b. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer (b. 1561)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1626", "text": "<PERSON><PERSON>, English bishop and scholar (b. 1555)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English bishop and scholar (b. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English bishop and scholar (b. 1555)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1630", "text": "<PERSON><PERSON><PERSON>, 1st Marquis of the Balbases, Italian general and politician, Governor of the Duchy of Milan (b. 1569)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Marquis_of_the_Balbases\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, 1st Marquis of the Balbases\"><PERSON><PERSON><PERSON>, 1st Marquis of the Balbases</a>, Italian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_the_Duchy_of_Milan\" class=\"mw-redirect\" title=\"List of Governors of the Duchy of Milan\">Governor of the Duchy of Milan</a> (b. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Marquis_of_the_Balbases\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, 1st Marquis of the Balbases\"><PERSON><PERSON><PERSON>, 1st Marquis of the Balbases</a>, Italian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_the_Duchy_of_Milan\" class=\"mw-redirect\" title=\"List of Governors of the Duchy of Milan\">Governor of the Duchy of Milan</a> (b. 1569)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Marquis of the Balbases", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Marquis_of_the_Balbases"}, {"title": "List of Governors of the Duchy of Milan", "link": "https://wikipedia.org/wiki/List_of_Governors_of_the_Duchy_of_Milan"}]}, {"year": "1665", "text": "Archduchess <PERSON> of Austria (b. 1610)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria_(1610%E2%80%931665)\" class=\"mw-redirect\" title=\"Archduchess <PERSON> of Austria (1610-1665)\">Archduchess <PERSON> of Austria</a> (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria_(1610%E2%80%931665)\" class=\"mw-redirect\" title=\"Archduchess <PERSON> of Austria (1610-1665)\">Archduchess <PERSON> of Austria</a> (b. 1610)", "links": [{"title": "Archduchess <PERSON> of Austria (1610-1665)", "link": "https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria_(1610%E2%80%931665)"}]}, {"year": "1703", "text": "<PERSON>, 1st Duke of Argyll, Scottish general (b. 1658)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Argyll\" title=\"<PERSON>, 1st Duke of Argyll\"><PERSON>, 1st Duke of Argyll</a>, Scottish general (b. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Argyll\" title=\"<PERSON>, 1st Duke of Argyll\"><PERSON>, 1st Duke of Argyll</a>, Scottish general (b. 1658)", "links": [{"title": "<PERSON>, 1st Duke of Argyll", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Argyll"}]}, {"year": "1774", "text": "<PERSON>, Canadian-English general (b. 1714)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English general (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English general (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, Swiss mathematician, physicist, and astronomer (b. 1728)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician, physicist, and astronomer (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician, physicist, and astronomer (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1791", "text": "<PERSON>, American soldier and publisher (b. 1719)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_Revolutionary_printer)\" class=\"mw-redirect\" title=\"<PERSON> (American Revolutionary printer)\"><PERSON></a>, American soldier and publisher (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_Revolutionary_printer)\" class=\"mw-redirect\" title=\"<PERSON> (American Revolutionary printer)\"><PERSON></a>, American soldier and publisher (b. 1719)", "links": [{"title": "<PERSON> (American Revolutionary printer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_Revolutionary_printer)"}]}, {"year": "1792", "text": "<PERSON>, Danish politician and diplomat (b. 1710)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish politician and diplomat (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish politician and diplomat (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, French pastor (b. 1718)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pastor (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pastor (b. 1718)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish singer, harpsichord player, and composer (b. 1783)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish singer, <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player, and composer (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish singer, <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player, and composer (b. 1783)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1849", "text": "<PERSON>, Austrian composer (b. 1804)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Johann <PERSON> I\"><PERSON></a>, Austrian composer (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Johann Strauss I\"><PERSON></a>, Austrian composer (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, American rancher, co-developed the Goodnight-Loving Trail (b. 1812)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Loving\" title=\"Oliver Loving\"><PERSON></a>, American rancher, co-developed the <a href=\"https://wikipedia.org/wiki/Goodnight%E2%80%93Loving_Trail\" title=\"Goodnight-Loving Trail\">Goodnight-Loving Trail</a> (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Loving\" title=\"Oliver Loving\"><PERSON></a>, American rancher, co-developed the <a href=\"https://wikipedia.org/wiki/Goodnight%E2%80%93Loving_Trail\" title=\"Goodnight-Loving Trail\">Goodnight-Loving Trail</a> (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Goodnight-Loving Trail", "link": "https://wikipedia.org/wiki/Goodnight%E2%80%93Loving_Trail"}]}, {"year": "1893", "text": "<PERSON>, German author (b. 1817)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois\" title=\"<PERSON>\"><PERSON></a>, German author (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois\" title=\"<PERSON>\"><PERSON></a>, German author (b. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Canadian journalist and politician, 11th Premier of Québec (b. 1832)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/F%C3%A9<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/List_of_Quebec_premiers\" class=\"mw-redirect\" title=\"List of Quebec premiers\">Premier of Québec</a> (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/List_of_Quebec_premiers\" class=\"mw-redirect\" title=\"List of Quebec premiers\">Premier of Québec</a> (b. 1832)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix-<PERSON>_<PERSON>and"}, {"title": "List of Quebec premiers", "link": "https://wikipedia.org/wiki/List_of_Quebec_premiers"}]}, {"year": "1900", "text": "<PERSON>, American general and politician, 15th Governor of Illinois (b. 1817)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American general and politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Governor of Illinois</a> (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American general and politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Governor of Illinois</a> (b. 1817)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(politician)"}, {"title": "Governor of Illinois", "link": "https://wikipedia.org/wiki/Governor_of_Illinois"}]}, {"year": "1901", "text": "<PERSON>, English general and politician, Governor of Malta (b. 1835)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Arthur_Fremantle\" class=\"mw-redirect\" title=\"Arthur Fremantle\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Malta\" class=\"mw-redirect\" title=\"List of Governors of Malta\">Governor of Malta</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arthur_Fremantle\" class=\"mw-redirect\" title=\"Arthur Fremantle\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Malta\" class=\"mw-redirect\" title=\"List of Governors of Malta\">Governor of Malta</a> (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arthur_Fremantle"}, {"title": "List of Governors of Malta", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Malta"}]}, {"year": "1905", "text": "<PERSON>, French educator and politician (b. 1853)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French educator and politician (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French educator and politician (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Irish Republican Brotherhood volunteer and rebel commander. Died as a result of forced feeding while on hunger strike. (b. 1885)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican_Brotherhood\" title=\"Irish Republican Brotherhood\">Irish Republican Brotherhood</a> volunteer and rebel commander. Died as a result of forced feeding while on hunger strike. (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican_Brotherhood\" title=\"Irish Republican Brotherhood\">Irish Republican Brotherhood</a> volunteer and rebel commander. Died as a result of forced feeding while on hunger strike. (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish Republican Brotherhood", "link": "https://wikipedia.org/wiki/Irish_Republican_Brotherhood"}]}, {"year": "1918", "text": "<PERSON>, Russian general (b. 1857)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English songwriter and bandleader (b. 1862)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and bandleader (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and bandleader (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American cartoonist, created <PERSON> Kid and <PERSON> (b. 1863)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/The_Yellow_Kid\" title=\"The Yellow Kid\">The Yellow Kid</a></i> and <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/The_Yellow_Kid\" title=\"The Yellow Kid\">The Yellow Kid</a></i> and <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "The Yellow Kid", "link": "https://wikipedia.org/wiki/The_Yellow_Kid"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American baseball player and manager (b. 1879)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American journalist and author (b. 1885)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lardner\"><PERSON></a>, American journalist and author (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lardner\"><PERSON></a>, American journalist and author (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Ukrainian intelligence agent (b. 1893)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lev Zadov\"><PERSON></a>, Ukrainian intelligence agent (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lev_<PERSON>adov\" title=\"Lev Zadov\"><PERSON></a>, Ukrainian intelligence agent (b. 1893)", "links": [{"title": "Lev Zadov", "link": "https://wikipedia.org/wiki/Lev_Zadov"}]}, {"year": "1939", "text": "<PERSON>, Turkish soldier and politician (b. 1885)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C5%9F\" title=\"<PERSON>\"><PERSON></a>, Turkish soldier and politician (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C5%9F\" title=\"<PERSON>\"><PERSON></a>, Turkish soldier and politician (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>%C5%9F"}]}, {"year": "1941", "text": "<PERSON><PERSON> <PERSON>, American polo player, golfer, and race car driver (b. 1867)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Foxhall_P<PERSON>_<PERSON>\" title=\"Foxhall P<PERSON> Keene\">Fox<PERSON> <PERSON></a>, American polo player, golfer, and race car driver (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Foxhall_P._<PERSON>\" title=\"Foxhall P<PERSON> Keene\">Fox<PERSON> <PERSON></a>, American polo player, golfer, and race car driver (b. 1867)", "links": [{"title": "Foxhall P<PERSON>", "link": "https://wikipedia.org/wiki/Foxhall_P._Keene"}]}, {"year": "1943", "text": "<PERSON>, Scottish-Canadian soccer player (b. 1880)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"Alexander Hall (soccer)\"><PERSON></a>, Scottish-Canadian soccer player (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"Alexander Hall (soccer)\"><PERSON></a>, Scottish-Canadian soccer player (b. 1880)", "links": [{"title": "<PERSON> (soccer)", "link": "https://wikipedia.org/wiki/Alexander_Hall_(soccer)"}]}, {"year": "1946", "text": "<PERSON>, Austrian physician (b. 1879)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>-born American swimmer (b. 1909)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> Swedish-born American swimmer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> Swedish-born American swimmer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American psychologist and academic (b. 1878)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American author and educator (b. 1873)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Emily Post\"><PERSON></a>, American author and educator (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emily_<PERSON>\" title=\"Emily Post\"><PERSON></a>, American author and educator (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor and singer (b. 1897)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" class=\"mw-redirect\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor and singer (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" class=\"mw-redirect\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor and singer (b. 1897)", "links": [{"title": "<PERSON> (American actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(American_actor)"}]}, {"year": "1968", "text": "<PERSON>, German eugenicist and academic (b. 1891)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_K._G%C3%BCnt<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German eugenicist and academic (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_K._G%C3%BCnt<PERSON>\" title=\"Hans <PERSON>\"><PERSON></a>, German eugenicist and academic (b. 1891)", "links": [{"title": "<PERSON> F. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._K._G%C3%BCnther"}]}, {"year": "1968", "text": "<PERSON>, American author and screenwriter (b. 1903)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> W<PERSON>rich\"><PERSON></a>, American author and screenwriter (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>rich\"><PERSON></a>, American author and screenwriter (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cornell_Woolrich"}]}, {"year": "1970", "text": "<PERSON>, German-Swiss author and translator (b. 1898)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss author and translator (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss author and translator (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American captain, jurist, and politician (b. 1886)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, jurist, and politician (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, jurist, and politician (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Argentine poet (b. 1936)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine poet (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine poet (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English drummer and songwriter (b. 1948)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and songwriter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and songwriter (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Russian-American director, producer, and screenwriter (b. 1895)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American director, producer, and screenwriter (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American director, producer, and screenwriter (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Estonian author and poet (b. 1883)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Marie <PERSON>\"><PERSON></a>, Estonian author and poet (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian author and poet (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON> of Belgium (b. 1901)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium\" title=\"Leopold III of Belgium\"><PERSON> of Belgium</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Belgium\" title=\"Leopold III of Belgium\"><PERSON> of Belgium</a> (b. 1901)", "links": [{"title": "<PERSON> of Belgium", "link": "https://wikipedia.org/wiki/Leopold_III_of_Belgium"}]}, {"year": "1984", "text": "<PERSON>, Canadian-American actor (b. 1897)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Indian-Canadian trade union leader and activist (b. 1917)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Canadian trade union leader and activist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Canadian\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Canadian trade union leader and activist (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian union leader and politician (b. 1909)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Nova_Scotia_politician)\" title=\"<PERSON> (Nova Scotia politician)\"><PERSON></a>, Canadian union leader and politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Nova_Scotia_politician)\" title=\"<PERSON> (Nova Scotia politician)\"><PERSON></a>, Canadian union leader and politician (b. 1909)", "links": [{"title": "<PERSON> (Nova Scotia politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Nova_Scotia_politician)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Russian physicist and chemist, Nobel Prize laureate (b. 1896)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1986", "text": "<PERSON>, Norwegian linguist and academic (b. 1909)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, Norwegian linguist and academic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, Norwegian linguist and academic (b. 1909)", "links": [{"title": "<PERSON> (linguist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)"}]}, {"year": "1987", "text": "<PERSON>, American actress (b. 1906)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Welsh actor and playwright (b. 1905)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh actor and playwright (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh actor and playwright (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American farmer and businessman (b. 1937)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and businessman (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and businessman (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Estonian-American orientalist and scholar (b. 1909)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Arthur_V%C3%B5%C3%B5bus\" title=\"<PERSON>\"><PERSON></a>, Estonian-American orientalist and scholar (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arthur_V%C3%B5%C3%B5bus\" title=\"<PERSON>\"><PERSON></a>, Estonian-American orientalist and scholar (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arthur_V%C3%B5%C3%B5bus"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian accountant and politician, 3rd Chief Minister of West Bengal (b. 1897)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian accountant and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian accountant and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Minister of West Bengal", "link": "https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal"}]}, {"year": "1991", "text": "<PERSON>, German SS captain, known as the \"<PERSON> of Lyon\" (b. 1913)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Klaus Barbie\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> captain, known as the \"<PERSON> of Lyon\" (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Klaus Barbie\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> captain, known as the \"<PERSON> of Lyon\" (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1991", "text": "<PERSON><PERSON>, French actress and producer (b. 1912)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Viviane_Romance\" title=\"Viviane Romance\"><PERSON><PERSON></a>, French actress and producer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viviane_Romance\" title=\"Viviane Romance\"><PERSON><PERSON></a>, French actress and producer (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Serbian musician (b. 1961)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian musician (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian musician (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vdovi%C4%87"}]}, {"year": "1995", "text": "<PERSON>, Welsh footballer and manager (b. 1928)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American dentist and author (b. 1891)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and author (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and author (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian singer and actress (b. 1916)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer and actress (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer and actress (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_<PERSON>geon"}]}, {"year": "1997", "text": "<PERSON>, French pianist, composer, and conductor (b. 1912)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7aix\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and conductor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7aix\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and conductor (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7aix"}]}, {"year": "1999", "text": "<PERSON>, American author (b. 1930)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Iraqi translator and politician (b. 1953)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi translator and politician (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi translator and politician (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American director, producer, and screenwriter (b. 1934)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Italian-American economist and academic, Nobel Prize laureate (b. 1918)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Economics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Economics"}]}, {"year": "2003", "text": "<PERSON>, American writer and literary editor (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and literary editor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and literary editor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American actor, director, and screenwriter (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "Madeline<PERSON><PERSON>, Canadian businesswoman and philanthropist (b. 1956)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businesswoman and philanthropist (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businesswoman and philanthropist (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American golfer (b. 1939)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Russian-American psychologist and ecologist (b. 1917)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Urie_Bronfenbrenner\" title=\"Urie Bronfenbrenner\"><PERSON><PERSON> Bronfenbrenner</a>, Russian-American psychologist and ecologist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rie_Bronfenbrenner\" title=\"Urie Bronfenbrenner\"><PERSON><PERSON> Bronfenbrenner</a>, Russian-American psychologist and ecologist (b. 1917)", "links": [{"title": "Urie Bronfenbrenner", "link": "https://wikipedia.org/wiki/<PERSON>rie_Bronfenbrenner"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Pakistani linguist and critic (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Pakistani linguist and critic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Pakistani linguist and critic (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, American psychiatrist and author (b. 1936)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American psychiatrist and author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American psychiatrist and author (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Austrian lawyer and politician (b. 1921)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American target shooter and author (b. 1920)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American target shooter and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American target shooter and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American author and poet (b. 1957)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Palestinian physician and politician (b. 1919)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian physician and politician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian physician and politician (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, German-American art dealer (b. 1924)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American art dealer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American art dealer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>ich"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Nauruan politician, 23rd President of Nauru (b. 1932)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>og_G<PERSON>\" title=\"Derog Gioura\"><PERSON><PERSON></a>, Nauruan politician, 23rd <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>\" title=\"Derog Gioura\"><PERSON><PERSON></a>, Nauruan politician, 23rd <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (b. 1932)", "links": [{"title": "Derog Gioura", "link": "https://wikipedia.org/wiki/Derog_G<PERSON>ura"}, {"title": "President of Nauru", "link": "https://wikipedia.org/wiki/President_of_Nauru"}]}, {"year": "2009", "text": "<PERSON>, Spanish pianist (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Canadian actor, director, and screenwriter (b. 1946)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Kenyan environmentalist and activist, Nobel Prize laureate (b. 1940)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Maathai\"><PERSON><PERSON></a>, Kenyan environmentalist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Maathai\"><PERSON><PERSON></a>, Kenyan environmentalist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "2012", "text": "<PERSON>, American composer and songwriter (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and songwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and songwriter (b. 1927)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "2012", "text": "<PERSON>, English footballer and manager (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (b. 1932)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "2012", "text": "<PERSON>, English historian and academic (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Mexican academic and politician (b. 1962)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican academic and politician (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican academic and politician (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American singer (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English footballer, coach, and manager (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and manager (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and manager (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, South Korean author and screenwriter (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> In-ho\"><PERSON></a>, South Korean author and screenwriter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> In-ho\"><PERSON></a>, South Korean author and screenwriter (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American poet and academic (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American guitarist and composer (b. 1915)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Italian-Argentinian lawyer and politician (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Argentinian lawyer and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Argentinian lawyer and politician (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Canadian psychiatrist and academic, co-founded Haven Institute (Gabriola Island, Canada) (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian psychiatrist and academic, co-founded <a href=\"https://wikipedia.org/wiki/Haven_Institute_(Gabriola_Island,_Canada)\" title=\"Haven Institute (Gabriola Island, Canada)\">Haven Institute (Gabriola Island, Canada)</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian psychiatrist and academic, co-founded <a href=\"https://wikipedia.org/wiki/Haven_Institute_(Gabriola_Island,_Canada)\" title=\"Haven Institute (Gabriola Island, Canada)\">Haven Institute (Gabriola Island, Canada)</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Haven Institute (Gabriola Island, Canada)", "link": "https://wikipedia.org/wiki/Haven_Institute_(Gabriola_Island,_Canada)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Haitian-Canadian educator and politician (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ulrick_Ch%C3%A9rubin\" title=\"<PERSON><PERSON><PERSON>é<PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian-Canadian educator and politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ulrick_Ch%C3%A9rubin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian-Canadian educator and politician (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ulrick_Ch%C3%A9rubin"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Bosnian lawyer, judge, and politician (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Sulejman_Tihi%C4%87\" title=\"<PERSON><PERSON><PERSON> Tihić\"><PERSON><PERSON><PERSON></a>, Bosnian lawyer, judge, and politician (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sulejman_Tihi%C4%87\" title=\"Sulej<PERSON> Tihić\"><PERSON><PERSON><PERSON></a>, Bosnian lawyer, judge, and politician (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sulejman_Tihi%C4%87"}]}, {"year": "2014", "text": "<PERSON>, English high jumper (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English high jumper (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English high jumper (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Italian Roman Catholic prelate (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Roman Catholic prelate (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Roman Catholic prelate (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American general (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (b. 1929)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}]}, {"year": "2015", "text": "<PERSON>, American baseball player and manager (b. 1944)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (b. 1944)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Israeli journalist (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli journalist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli journalist (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Cuban-American baseball player (b. 1992)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Fern%C3%A1<PERSON><PERSON>_(right-handed_pitcher)\" title=\"<PERSON> (right-handed pitcher)\"><PERSON></a>, Cuban-American baseball player (b. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Fern%C3%A1<PERSON><PERSON>_(right-handed_pitcher)\" title=\"<PERSON> (right-handed pitcher)\"><PERSON></a>, Cuban-American baseball player (b. 1992)", "links": [{"title": "<PERSON> (right-handed pitcher)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Fern%C3%A1nde<PERSON>_(right-handed_pitcher)"}]}, {"year": "2016", "text": "<PERSON>, American golfer (b. 1929)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Palmer\"><PERSON></a>, American golfer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Jordanian writer and political activist (b. 1960)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jordanian writer and political activist (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jordanian writer and political activist (b. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Czech actor (b. 1936)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Jan_T%C5%99%C3%ADska\" title=\"<PERSON>\"><PERSON></a>, Czech actor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_T%C5%99%C3%ADska\" title=\"<PERSON>\"><PERSON></a>, Czech actor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_T%C5%99%C3%ADska"}]}, {"year": "2023", "text": "<PERSON>, Scottish actor (b. 1933)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}]}}