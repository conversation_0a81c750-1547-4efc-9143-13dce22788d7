{"date": "October 22", "url": "https://wikipedia.org/wiki/October_22", "data": {"Events": [{"year": "451", "text": "The Chalcedonian Creed, regarding the divine and human nature of <PERSON>, is adopted by the Council of Chalcedon, an ecumenical council.", "html": "451 - The <a href=\"https://wikipedia.org/wiki/Chalcedonian_Creed\" class=\"mw-redirect\" title=\"Chalcedonian Creed\">Chalcedonian Creed</a>, regarding the divine and human nature of <PERSON>, is adopted by the <a href=\"https://wikipedia.org/wiki/Council_of_Chalcedon\" title=\"Council of Chalcedon\">Council of Chalcedon</a>, an ecumenical council.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chalcedonian_Creed\" class=\"mw-redirect\" title=\"Chalcedonian Creed\">Chalcedonian Creed</a>, regarding the divine and human nature of <PERSON>, is adopted by the <a href=\"https://wikipedia.org/wiki/Council_of_Chalcedon\" title=\"Council of Chalcedon\">Council of Chalcedon</a>, an ecumenical council.", "links": [{"title": "Chalcedonian Creed", "link": "https://wikipedia.org/wiki/Chalcedonian_Creed"}, {"title": "Council of Chalcedon", "link": "https://wikipedia.org/wiki/Council_of_Chalcedon"}]}, {"year": "794", "text": "Japanese Emperor <PERSON><PERSON><PERSON> relocates his empire's capital to Heian-kyō (now Kyoto).", "html": "794 - Japanese <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> relocates his empire's capital to <a href=\"https://wikipedia.org/wiki/Heian-ky%C5%8D\" title=\"Heian-kyō\">Heian-kyō</a> (now Kyoto).", "no_year_html": "Japanese <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> relocates his empire's capital to <a href=\"https://wikipedia.org/wiki/Heian-ky%C5%8D\" title=\"Heian-kyō\">Heian-kyō</a> (now Kyoto).", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>"}, {"title": "Heian-kyō", "link": "https://wikipedia.org/wiki/Heian-ky%C5%8D"}]}, {"year": "906", "text": "Abbasid general <PERSON> ibn <PERSON> leads a raid against the Byzantine Empire, taking 4,000-5,000 captives.", "html": "906 - Abbasid general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a> leads a raid against the Byzantine Empire, taking 4,000-5,000 captives.", "no_year_html": "Abbasid general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a> leads a raid against the Byzantine Empire, taking 4,000-5,000 captives.", "links": [{"title": "<PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1383", "text": "The male line of the Portuguese House of Burgundy becomes extinct with the death of King <PERSON>, leaving only his daughter <PERSON>. Rival claimants begin a period of civil war and disorder.", "html": "1383 - The male line of the <a href=\"https://wikipedia.org/wiki/Portuguese_House_of_Burgundy\" title=\"Portuguese House of Burgundy\">Portuguese House of Burgundy</a> becomes extinct with the death of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON></a>, leaving only his daughter <PERSON>. Rival claimants begin a period of <a href=\"https://wikipedia.org/wiki/1383%E2%80%9385_Crisis\" class=\"mw-redirect\" title=\"1383-85 Crisis\">civil war</a> and disorder.", "no_year_html": "The male line of the <a href=\"https://wikipedia.org/wiki/Portuguese_House_of_Burgundy\" title=\"Portuguese House of Burgundy\">Portuguese House of Burgundy</a> becomes extinct with the death of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON></a>, leaving only his daughter <PERSON>. Rival claimants begin a period of <a href=\"https://wikipedia.org/wiki/1383%E2%80%9385_Crisis\" class=\"mw-redirect\" title=\"1383-85 Crisis\">civil war</a> and disorder.", "links": [{"title": "Portuguese House of Burgundy", "link": "https://wikipedia.org/wiki/Portuguese_House_of_Burgundy"}, {"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}, {"title": "1383-85 Crisis", "link": "https://wikipedia.org/wiki/1383%E2%80%9385_Crisis"}]}, {"year": "1721", "text": "The Russian Empire is proclaimed by Tsar <PERSON> after the Swedish defeat in the Great Northern War.", "html": "1721 - The <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a> is proclaimed by Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> I</a> after the <a href=\"https://wikipedia.org/wiki/Swedish_Empire\" title=\"Swedish Empire\">Swedish</a> defeat in the <a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a> is proclaimed by Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> I</a> after the <a href=\"https://wikipedia.org/wiki/Swedish_Empire\" title=\"Swedish Empire\">Swedish</a> defeat in the <a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>.", "links": [{"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}, {"title": "Swedish Empire", "link": "https://wikipedia.org/wiki/Swedish_Empire"}, {"title": "Great Northern War", "link": "https://wikipedia.org/wiki/Great_Northern_War"}]}, {"year": "1724", "text": "<PERSON><PERSON> <PERSON><PERSON> leads the first performance of <PERSON><PERSON><PERSON><PERSON> dich, o liebe Seele (Adorn yourself, O dear soul) in Leipzig on the 20th Sunday after Trinity, based on the communion hymn of the same name.", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> leads the first performance of <i><a href=\"https://wikipedia.org/wiki/Schm%C3%<PERSON><PERSON>_dich,_o_liebe_Seele,_BWV_180\" title=\"<PERSON><PERSON>ücke dich, o liebe Seele, BWV 180\"><PERSON><PERSON><PERSON><PERSON> dich, o liebe Seele</a></i> (Adorn yourself, O dear soul) in <a href=\"https://wikipedia.org/wiki/Leipzig\" title=\"Leipzig\">Leipzig</a> on the 20th Sunday after <a href=\"https://wikipedia.org/wiki/Trinity_Sunday\" title=\"Trinity Sunday\">Trinity</a>, based on <a href=\"https://wikipedia.org/wiki/Schm%C3%<PERSON><PERSON>_dich,_o_liebe_Seele\" title=\"Schmücke dich, o liebe Seele\">the communion hymn of the same name</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> leads the first performance of <i><a href=\"https://wikipedia.org/wiki/Schm%C3%<PERSON><PERSON>_dich,_o_liebe_Seele,_BWV_180\" title=\"Schmücke dich, o liebe Seele, BWV 180\"><PERSON><PERSON>ücke dich, o liebe Seele</a></i> (Adorn yourself, O dear soul) in <a href=\"https://wikipedia.org/wiki/Leipzig\" title=\"Leipzig\">Leipzig</a> on the 20th Sunday after <a href=\"https://wikipedia.org/wiki/Trinity_Sunday\" title=\"Trinity Sunday\">Trinity</a>, based on <a href=\"https://wikipedia.org/wiki/Schm%C3%<PERSON>cke_dich,_o_liebe_Seele\" title=\"Schmücke dich, o liebe Seele\">the communion hymn of the same name</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> dich, o liebe Seele, BWV 180", "link": "https://wikipedia.org/wiki/Schm%C3%<PERSON><PERSON>_dich,_o_liebe_<PERSON>le,_BWV_180"}, {"title": "Leipzig", "link": "https://wikipedia.org/wiki/Leipzig"}, {"title": "Trinity Sunday", "link": "https://wikipedia.org/wiki/Trinity_Sunday"}, {"title": "<PERSON><PERSON><PERSON><PERSON> dich, o liebe Seele", "link": "https://wikipedia.org/wiki/Schm%C3%<PERSON><PERSON>_dich,_o_liebe_<PERSON>le"}]}, {"year": "1730", "text": "Construction of the Ladoga Canal is completed in Russia.", "html": "1730 - Construction of the <a href=\"https://wikipedia.org/wiki/Ladoga_Canal\" title=\"Ladoga Canal\">Ladoga Canal</a> is completed in Russia.", "no_year_html": "Construction of the <a href=\"https://wikipedia.org/wiki/Ladoga_Canal\" title=\"Ladoga Canal\">Ladoga Canal</a> is completed in Russia.", "links": [{"title": "Ladoga Canal", "link": "https://wikipedia.org/wiki/Ladoga_Canal"}]}, {"year": "1739", "text": "The War of Jenkins' Ear begins with the first attack on La Guaira.", "html": "1739 - The <a href=\"https://wikipedia.org/wiki/War_of_Jenkins%27_Ear\" title=\"War of Jenkins' Ear\">War of Jenkins' Ear</a> begins with the first attack on <a href=\"https://wikipedia.org/wiki/La_Guaira\" title=\"La Guaira\">La Guaira</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/War_of_Jenkins%27_Ear\" title=\"War of Jenkins' Ear\">War of Jenkins' Ear</a> begins with the first attack on <a href=\"https://wikipedia.org/wiki/La_Guaira\" title=\"La Guaira\">La Guaira</a>.", "links": [{"title": "War of Jenkins' Ear", "link": "https://wikipedia.org/wiki/War_of_Jenkins%27_Ear"}, {"title": "La Guaira", "link": "https://wikipedia.org/wiki/La_Guaira"}]}, {"year": "1746", "text": "The College of New Jersey (later renamed Princeton University) receives its charter.", "html": "1746 - The College of New Jersey (later renamed <a href=\"https://wikipedia.org/wiki/Princeton_University\" title=\"Princeton University\">Princeton University</a>) receives its charter.", "no_year_html": "The College of New Jersey (later renamed <a href=\"https://wikipedia.org/wiki/Princeton_University\" title=\"Princeton University\">Princeton University</a>) receives its charter.", "links": [{"title": "Princeton University", "link": "https://wikipedia.org/wiki/Princeton_University"}]}, {"year": "1777", "text": "American Revolutionary War: American defenders of Fort Mercer on the Delaware River repulse repeated Hessian attacks in the Battle of Red Bank.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: American defenders of <a href=\"https://wikipedia.org/wiki/Fort_Mercer\" title=\"Fort Mercer\">Fort Mercer</a> on the Delaware River repulse repeated Hessian attacks in the <a href=\"https://wikipedia.org/wiki/Battle_of_Red_Bank\" title=\"Battle of Red Bank\">Battle of Red Bank</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: American defenders of <a href=\"https://wikipedia.org/wiki/Fort_Mercer\" title=\"Fort Mercer\">Fort Mercer</a> on the Delaware River repulse repeated Hessian attacks in the <a href=\"https://wikipedia.org/wiki/Battle_of_Red_Bank\" title=\"Battle of Red Bank\">Battle of Red Bank</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Fort Mercer", "link": "https://wikipedia.org/wiki/Fort_Mercer"}, {"title": "Battle of Red Bank", "link": "https://wikipedia.org/wiki/Battle_of_Red_Bank"}]}, {"year": "1790", "text": "Northwest Indian War: Native American forces defeat the United States, ending the Harmar Campaign.", "html": "1790 - <a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>: Native American forces defeat the United States, ending the <a href=\"https://wikipedia.org/wiki/Harmar_Campaign\" class=\"mw-redirect\" title=\"Harmar Campaign\">Harmar Campaign</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>: Native American forces defeat the United States, ending the <a href=\"https://wikipedia.org/wiki/Harmar_Campaign\" class=\"mw-redirect\" title=\"Harmar Campaign\">Harmar Campaign</a>.", "links": [{"title": "Northwest Indian War", "link": "https://wikipedia.org/wiki/Northwest_Indian_War"}, {"title": "Harmar Campaign", "link": "https://wikipedia.org/wiki/<PERSON>rmar_Campaign"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON> makes the first recorded parachute jump, from 1,000 metres (3,300 ft) above Paris.", "html": "1797 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> makes the first recorded <a href=\"https://wikipedia.org/wiki/Parachute\" title=\"Parachute\">parachute</a> jump, from 1,000 metres (3,300 ft) above Paris.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> makes the first recorded <a href=\"https://wikipedia.org/wiki/Parachute\" title=\"Parachute\">parachute</a> jump, from 1,000 metres (3,300 ft) above Paris.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>_<PERSON>"}, {"title": "Parachute", "link": "https://wikipedia.org/wiki/Parachute"}]}, {"year": "1836", "text": "<PERSON> is inaugurated as the first President of the Republic of Texas.", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Sam_Houston\" title=\"Sam Houston\"><PERSON></a> is inaugurated as the first President of the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sam_Houston\" title=\"Sam Houston\"><PERSON></a> is inaugurated as the first President of the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_Houston"}, {"title": "Republic of Texas", "link": "https://wikipedia.org/wiki/Republic_of_Texas"}]}, {"year": "1844", "text": "The Millerites (followers of Baptist preacher <PERSON>) anticipate the end of the world in conjunction with the Second Advent of Christ. The following day becomes known as the Great Disappointment.", "html": "1844 - The Millerites (followers of <a href=\"https://wikipedia.org/wiki/Baptist\" class=\"mw-redirect\" title=\"Baptist\">Baptist</a> preacher <a href=\"https://wikipedia.org/wiki/<PERSON>(preacher)\" title=\"<PERSON> (preacher)\"><PERSON></a>) anticipate the end of the world in conjunction with the <a href=\"https://wikipedia.org/wiki/Second_Advent_of_Christ\" class=\"mw-redirect\" title=\"Second Advent of Christ\">Second Advent of Christ</a>. The following day becomes known as the <a href=\"https://wikipedia.org/wiki/Great_Disappointment\" title=\"Great Disappointment\">Great Disappointment</a>.", "no_year_html": "The Millerites (followers of <a href=\"https://wikipedia.org/wiki/Baptist\" class=\"mw-redirect\" title=\"Baptist\">Baptist</a> preacher <a href=\"https://wikipedia.org/wiki/<PERSON>(preacher)\" title=\"<PERSON> (preacher)\"><PERSON></a>) anticipate the end of the world in conjunction with the <a href=\"https://wikipedia.org/wiki/Second_Advent_of_Christ\" class=\"mw-redirect\" title=\"Second Advent of Christ\">Second Advent of Christ</a>. The following day becomes known as the <a href=\"https://wikipedia.org/wiki/Great_Disappointment\" title=\"Great Disappointment\">Great Disappointment</a>.", "links": [{"title": "Baptist", "link": "https://wikipedia.org/wiki/Baptist"}, {"title": "<PERSON> (preacher)", "link": "https://wikipedia.org/wiki/<PERSON>_(preacher)"}, {"title": "Second Advent of Christ", "link": "https://wikipedia.org/wiki/Second_Advent_of_Christ"}, {"title": "Great Disappointment", "link": "https://wikipedia.org/wiki/Great_Disappointment"}]}, {"year": "1859", "text": "Spain declares war on Morocco.", "html": "1859 - Spain declares <a href=\"https://wikipedia.org/wiki/Hispano-Moroccan_War_(1859%E2%80%9360)\" class=\"mw-redirect\" title=\"Hispano-Moroccan War (1859-60)\">war</a> on <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>.", "no_year_html": "Spain declares <a href=\"https://wikipedia.org/wiki/Hispano-Moroccan_War_(1859%E2%80%9360)\" class=\"mw-redirect\" title=\"Hispano-Moroccan War (1859-60)\">war</a> on <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>.", "links": [{"title": "Hispano-Moroccan War (1859-60)", "link": "https://wikipedia.org/wiki/Hispano-Moroccan_War_(1859%E2%80%9360)"}, {"title": "Morocco", "link": "https://wikipedia.org/wiki/Morocco"}]}, {"year": "1866", "text": "A plebiscite ratifies the annexation of Veneto and Mantua to Italy, which had occurred three days before on October 19.", "html": "1866 - A plebiscite ratifies the annexation of <a href=\"https://wikipedia.org/wiki/Veneto\" title=\"Veneto\">Veneto</a> and <a href=\"https://wikipedia.org/wiki/Mantua\" title=\"Mantua\">Mantua</a> to Italy, which had occurred three days before on October 19.", "no_year_html": "A plebiscite ratifies the annexation of <a href=\"https://wikipedia.org/wiki/Veneto\" title=\"Veneto\">Veneto</a> and <a href=\"https://wikipedia.org/wiki/Mantua\" title=\"Mantua\">Mantua</a> to Italy, which had occurred three days before on October 19.", "links": [{"title": "Veneto", "link": "https://wikipedia.org/wiki/Veneto"}, {"title": "Mantu<PERSON>", "link": "https://wikipedia.org/wiki/Mantua"}]}, {"year": "1877", "text": "The Blantyre mining disaster in Scotland kills 207 miners.", "html": "1877 - The <a href=\"https://wikipedia.org/wiki/Blantyre_mining_disaster\" title=\"Blantyre mining disaster\">Blantyre mining disaster</a> in Scotland kills 207 miners.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Blantyre_mining_disaster\" title=\"Blantyre mining disaster\">Blantyre mining disaster</a> in Scotland kills 207 miners.", "links": [{"title": "Blantyre mining disaster", "link": "https://wikipedia.org/wiki/Blantyre_mining_disaster"}]}, {"year": "1879", "text": "Using a filament of carbonized thread, <PERSON> tests the first practical electric incandescent light bulb (lasting 13.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}1⁄2 hours before burning out).", "html": "1879 - Using a filament of carbonized thread, <PERSON> tests the first practical electric <a href=\"https://wikipedia.org/wiki/Incandescent_light_bulb\" title=\"Incandescent light bulb\">incandescent light bulb</a> (lasting 13<style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\"><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> hours before burning out).", "no_year_html": "Using a filament of carbonized thread, <PERSON> tests the first practical electric <a href=\"https://wikipedia.org/wiki/Incandescent_light_bulb\" title=\"Incandescent light bulb\">incandescent light bulb</a> (lasting 13<style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\"><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> hours before burning out).", "links": [{"title": "Incandescent light bulb", "link": "https://wikipedia.org/wiki/Incandescent_light_bulb"}]}, {"year": "1883", "text": "The Metropolitan Opera House in New York City opens with a performance of <PERSON><PERSON><PERSON>'s <PERSON>.", "html": "1883 - The <a href=\"https://wikipedia.org/wiki/Metropolitan_Opera_House_(39th_Street)\" title=\"Metropolitan Opera House (39th Street)\">Metropolitan Opera House</a> in New York City opens with a performance of <a href=\"https://wikipedia.org/wiki/Gounod\" class=\"mw-redirect\" title=\"<PERSON>uno<PERSON>\"><PERSON><PERSON><PERSON></a>'s <i><PERSON></i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Metropolitan_Opera_House_(39th_Street)\" title=\"Metropolitan Opera House (39th Street)\">Metropolitan Opera House</a> in New York City opens with a performance of <a href=\"https://wikipedia.org/wiki/Gounod\" class=\"mw-redirect\" title=\"<PERSON>uno<PERSON>\"><PERSON><PERSON><PERSON></a>'s <i><PERSON></i>.", "links": [{"title": "Metropolitan Opera House (39th Street)", "link": "https://wikipedia.org/wiki/Metropolitan_Opera_House_(39th_Street)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>d"}]}, {"year": "1884", "text": "The International Meridian Conference designates the Royal Observatory, Greenwich as the world's prime meridian.", "html": "1884 - The <a href=\"https://wikipedia.org/wiki/International_Meridian_Conference\" title=\"International Meridian Conference\">International Meridian Conference</a> designates the <a href=\"https://wikipedia.org/wiki/Royal_Observatory,_Greenwich\" title=\"Royal Observatory, Greenwich\">Royal Observatory, Greenwich</a> as the world's prime meridian.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Meridian_Conference\" title=\"International Meridian Conference\">International Meridian Conference</a> designates the <a href=\"https://wikipedia.org/wiki/Royal_Observatory,_Greenwich\" title=\"Royal Observatory, Greenwich\">Royal Observatory, Greenwich</a> as the world's prime meridian.", "links": [{"title": "International Meridian Conference", "link": "https://wikipedia.org/wiki/International_Meridian_Conference"}, {"title": "Royal Observatory, Greenwich", "link": "https://wikipedia.org/wiki/Royal_Observatory,_Greenwich"}]}, {"year": "1895", "text": "In Paris an express train derails after overrunning the buffer stop, crossing almost 30 metres (100 ft) of concourse before crashing through a wall and falling 10 metres (33 ft) to the road below.", "html": "1895 - In Paris an express train <a href=\"https://wikipedia.org/wiki/Montparnasse_derailment\" title=\"Montparnasse derailment\">derails</a> after overrunning the buffer stop, crossing almost 30 metres (100 ft) of concourse before crashing through a wall and falling 10 metres (33 ft) to the road below.", "no_year_html": "In Paris an express train <a href=\"https://wikipedia.org/wiki/Montparnasse_derailment\" title=\"Montparnasse derailment\">derails</a> after overrunning the buffer stop, crossing almost 30 metres (100 ft) of concourse before crashing through a wall and falling 10 metres (33 ft) to the road below.", "links": [{"title": "Montparnasse derailment", "link": "https://wikipedia.org/wiki/Mont<PERSON><PERSON><PERSON>_derailment"}]}, {"year": "1907", "text": "A run on the stock of the Knickerbocker Trust Company sets events in motion that will spark the Panic of 1907.", "html": "1907 - A run on the stock of the <a href=\"https://wikipedia.org/wiki/Knickerbocker_Trust_Company\" title=\"Knickerbocker Trust Company\">Knickerbocker Trust Company</a> sets events in motion that will spark the <a href=\"https://wikipedia.org/wiki/Panic_of_1907\" title=\"Panic of 1907\">Panic of 1907</a>.", "no_year_html": "A run on the stock of the <a href=\"https://wikipedia.org/wiki/Knickerbocker_Trust_Company\" title=\"Knickerbocker Trust Company\">Knickerbocker Trust Company</a> sets events in motion that will spark the <a href=\"https://wikipedia.org/wiki/Panic_of_1907\" title=\"Panic of 1907\">Panic of 1907</a>.", "links": [{"title": "Knickerbocker Trust Company", "link": "https://wikipedia.org/wiki/Knickerbocker_Trust_Company"}, {"title": "Panic of 1907", "link": "https://wikipedia.org/wiki/Panic_of_1907"}]}, {"year": "1910", "text": "<PERSON><PERSON> (the first felon to be arrested with the help of radio) is convicted of poisoning his wife.", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>wley Harvey Crippen\"><PERSON><PERSON></a> (the first felon to be arrested with the help of <a href=\"https://wikipedia.org/wiki/Radio\" title=\"Radio\">radio</a>) is convicted of poisoning his wife.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Hawley Harvey Crippen\"><PERSON><PERSON>rippen</a> (the first felon to be arrested with the help of <a href=\"https://wikipedia.org/wiki/Radio\" title=\"Radio\">radio</a>) is convicted of poisoning his wife.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Radio", "link": "https://wikipedia.org/wiki/Radio"}]}, {"year": "1923", "text": "The royalist <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> coup d'état attempt fails in Greece, discrediting the monarchy and paving the way for the establishment of the Second Hellenic Republic.", "html": "1923 - The royalist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ulos%E2%80%93Gargalidis_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>-Gargalidis coup d'état attempt\"><PERSON><PERSON><PERSON>-<PERSON>galid<PERSON> coup d'état attempt</a> fails in Greece, discrediting the monarchy and paving the way for the establishment of the <a href=\"https://wikipedia.org/wiki/Second_Hellenic_Republic\" title=\"Second Hellenic Republic\">Second Hellenic Republic</a>.", "no_year_html": "The royalist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ulos%E2%80%93Gargalidis_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>-Gargalidis coup d'état attempt\"><PERSON><PERSON><PERSON>-<PERSON>galid<PERSON> coup d'état attempt</a> fails in Greece, discrediting the monarchy and paving the way for the establishment of the <a href=\"https://wikipedia.org/wiki/Second_Hellenic_Republic\" title=\"Second Hellenic Republic\">Second Hellenic Republic</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> coup d'état attempt", "link": "https://wikipedia.org/wiki/Leonardopoulos%E2%80%93Gargalidis_coup_d%27%C3%A9tat_attempt"}, {"title": "Second Hellenic Republic", "link": "https://wikipedia.org/wiki/Second_Hellenic_Republic"}]}, {"year": "1934", "text": "In East Liverpool, Ohio, FBI agents shoot and kill notorious bank robber <PERSON>.", "html": "1934 - In <a href=\"https://wikipedia.org/wiki/East_Liverpool,_Ohio\" title=\"East Liverpool, Ohio\">East Liverpool, Ohio</a>, FBI agents shoot and kill notorious bank robber <a href=\"https://wikipedia.org/wiki/Pretty_Boy_Floyd\" title=\"Pretty Boy Floyd\">Pretty Boy Floyd</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/East_Liverpool,_Ohio\" title=\"East Liverpool, Ohio\">East Liverpool, Ohio</a>, FBI agents shoot and kill notorious bank robber <a href=\"https://wikipedia.org/wiki/Pretty_Boy_Floyd\" title=\"Pretty Boy Floyd\">Pretty Boy Floyd</a>.", "links": [{"title": "East Liverpool, Ohio", "link": "https://wikipedia.org/wiki/East_Liverpool,_Ohio"}, {"title": "Pretty Boy Floyd", "link": "https://wikipedia.org/wiki/Pretty_Boy_Floyd"}]}, {"year": "1936", "text": "<PERSON><PERSON>, captain of the Girl Pat is convicted of its theft and imprisoned, having caused a media sensation when it went missing.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, captain of the <i><a href=\"https://wikipedia.org/wiki/Girl_Pat\" title=\"Girl Pat\"><PERSON></a></i> is convicted of its theft and imprisoned, having caused a media sensation when it went missing.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, captain of the <i><a href=\"https://wikipedia.org/wiki/Girl_Pat\" title=\"Girl Pat\"><PERSON></a></i> is convicted of its theft and imprisoned, having caused a media sensation when it went missing.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Girl Pat", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "World War II: French resistance member <PERSON> and 29 other hostages are executed by the Germans in retaliation for the death of a German officer.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: French resistance member <a href=\"https://wikipedia.org/wiki/<PERSON>_M%C3%B4quet\" title=\"<PERSON>\"><PERSON></a> and 29 other hostages are executed by the Germans in retaliation for the death of a German officer.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: French resistance member <a href=\"https://wikipedia.org/wiki/Guy_M%C3%B4quet\" title=\"<PERSON>\"><PERSON></a> and 29 other hostages are executed by the Germans in retaliation for the death of a German officer.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guy_M%C3%B4quet"}]}, {"year": "1943", "text": "World War II: In the second firestorm raid on Germany, the British Royal Air Force conducts an air raid on the town of Kassel, killing 10,000 and rendering 150,000 homeless.", "html": "1943 - World War II: In the second firestorm raid on Germany, the British Royal Air Force <a href=\"https://wikipedia.org/wiki/Bombing_of_Kassel_in_World_War_II\" title=\"Bombing of Kassel in World War II\">conducts an air raid</a> on the town of <a href=\"https://wikipedia.org/wiki/Kassel\" title=\"Kassel\">Kassel</a>, killing 10,000 and rendering 150,000 homeless.", "no_year_html": "World War II: In the second firestorm raid on Germany, the British Royal Air Force <a href=\"https://wikipedia.org/wiki/Bombing_of_Kassel_in_World_War_II\" title=\"Bombing of Kassel in World War II\">conducts an air raid</a> on the town of <a href=\"https://wikipedia.org/wiki/Kassel\" title=\"Kassel\">Kassel</a>, killing 10,000 and rendering 150,000 homeless.", "links": [{"title": "Bombing of Kassel in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Kassel_in_World_War_II"}, {"title": "Kassel", "link": "https://wikipedia.org/wiki/Kassel"}]}, {"year": "1946", "text": "Over twenty-two hundred engineers and technicians from eastern Germany are forced to relocate to the Soviet Union, along with their families and equipment.", "html": "1946 - Over twenty-two hundred engineers and technicians from eastern Germany are forced to <a href=\"https://wikipedia.org/wiki/Operation_Osoaviakhim\" title=\"Operation Osoaviakhim\">relocate to the Soviet Union</a>, along with their families and equipment.", "no_year_html": "Over twenty-two hundred engineers and technicians from eastern Germany are forced to <a href=\"https://wikipedia.org/wiki/Operation_Osoaviakhim\" title=\"Operation Osoaviakhim\">relocate to the Soviet Union</a>, along with their families and equipment.", "links": [{"title": "Operation Osoaviakhim", "link": "https://wikipedia.org/wiki/Operation_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1962", "text": "Cuban Missile Crisis: President <PERSON>, after internal counsel from <PERSON>, announces that American reconnaissance planes have discovered Soviet nuclear weapons in Cuba, and that he has ordered a naval \"quarantine\" of the Communist nation.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Cuban_Missile_Crisis\" title=\"Cuban Missile Crisis\">Cuban Missile Crisis</a>: President <PERSON>, after internal counsel from <PERSON>, announces that American reconnaissance planes have discovered Soviet nuclear weapons in Cuba, and that he has ordered a naval \"quarantine\" of the Communist nation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cuban_Missile_Crisis\" title=\"Cuban Missile Crisis\">Cuban Missile Crisis</a>: President <PERSON>, after internal counsel from <PERSON>, announces that American reconnaissance planes have discovered Soviet nuclear weapons in Cuba, and that he has ordered a naval \"quarantine\" of the Communist nation.", "links": [{"title": "Cuban Missile Crisis", "link": "https://wikipedia.org/wiki/Cuban_Missile_Crisis"}]}, {"year": "1963", "text": "A BAC One-Eleven prototype airliner crashes in UK with the loss of all on board.", "html": "1963 - A <a href=\"https://wikipedia.org/wiki/BAC_One-Eleven\" title=\"BAC One-Eleven\">BAC One-Eleven</a> prototype airliner <a href=\"https://wikipedia.org/wiki/1963_BAC_One-Eleven_test_crash\" title=\"1963 BAC One-Eleven test crash\">crashes in UK with the loss of all on board</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/BAC_One-Eleven\" title=\"BAC One-Eleven\">BAC One-Eleven</a> prototype airliner <a href=\"https://wikipedia.org/wiki/1963_BAC_One-Eleven_test_crash\" title=\"1963 BAC One-Eleven test crash\">crashes in UK with the loss of all on board</a>.", "links": [{"title": "BAC One-Eleven", "link": "https://wikipedia.org/wiki/BAC_One-Eleven"}, {"title": "1963 BAC One-Eleven test crash", "link": "https://wikipedia.org/wiki/1963_BAC_One-Eleven_test_crash"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON> is awarded the Nobel Prize in Literature, though he does not accept the prize.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize in Literature</a>, though he does not accept the prize.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize in Literature</a>, though he does not accept the prize.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1975", "text": "The Soviet uncrewed space mission Venera 9 lands on Venus.", "html": "1975 - The Soviet uncrewed space mission <a href=\"https://wikipedia.org/wiki/Venera_9\" title=\"Venera 9\">Venera 9</a> lands on Venus.", "no_year_html": "The Soviet uncrewed space mission <a href=\"https://wikipedia.org/wiki/Venera_9\" title=\"Venera 9\">Venera 9</a> lands on Venus.", "links": [{"title": "Venera 9", "link": "https://wikipedia.org/wiki/Venera_9"}]}, {"year": "1981", "text": "The US Federal Labor Relations Authority votes to decertify the Professional Air Traffic Controllers Organization (PATCO) for its strike the previous August.", "html": "1981 - The US <a href=\"https://wikipedia.org/wiki/Federal_Labor_Relations_Authority\" title=\"Federal Labor Relations Authority\">Federal Labor Relations Authority</a> votes to decertify the <a href=\"https://wikipedia.org/wiki/Professional_Air_Traffic_Controllers_Organization_(1968)\" title=\"Professional Air Traffic Controllers Organization (1968)\">Professional Air Traffic Controllers Organization</a> (PATCO) for its strike the previous August.", "no_year_html": "The US <a href=\"https://wikipedia.org/wiki/Federal_Labor_Relations_Authority\" title=\"Federal Labor Relations Authority\">Federal Labor Relations Authority</a> votes to decertify the <a href=\"https://wikipedia.org/wiki/Professional_Air_Traffic_Controllers_Organization_(1968)\" title=\"Professional Air Traffic Controllers Organization (1968)\">Professional Air Traffic Controllers Organization</a> (PATCO) for its strike the previous August.", "links": [{"title": "Federal Labor Relations Authority", "link": "https://wikipedia.org/wiki/Federal_Labor_Relations_Authority"}, {"title": "Professional Air Traffic Controllers Organization (1968)", "link": "https://wikipedia.org/wiki/Professional_Air_Traffic_Controllers_Organization_(1968)"}]}, {"year": "1983", "text": "Two correctional officers are killed by inmates at the United States Penitentiary in Marion, Illinois. The incident inspires the Supermax model of prisons.", "html": "1983 - Two correctional officers are killed by inmates at the United States Penitentiary in <a href=\"https://wikipedia.org/wiki/Marion,_Illinois\" title=\"Marion, Illinois\">Marion, Illinois</a>. The incident inspires the <a href=\"https://wikipedia.org/wiki/Supermax\" class=\"mw-redirect\" title=\"Supermax\">Supermax</a> model of prisons.", "no_year_html": "Two correctional officers are killed by inmates at the United States Penitentiary in <a href=\"https://wikipedia.org/wiki/Marion,_Illinois\" title=\"Marion, Illinois\">Marion, Illinois</a>. The incident inspires the <a href=\"https://wikipedia.org/wiki/Supermax\" class=\"mw-redirect\" title=\"Supermax\">Supermax</a> model of prisons.", "links": [{"title": "Marion, Illinois", "link": "https://wikipedia.org/wiki/Marion,_Illinois"}, {"title": "Supermax", "link": "https://wikipedia.org/wiki/Supermax"}]}, {"year": "1987", "text": "<PERSON>' opera <PERSON> in China premiered at the Houston Grand Opera.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON>'</a> opera <i><a href=\"https://wikipedia.org/wiki/<PERSON>_in_China\" title=\"<PERSON> in China\"><PERSON> in China</a></i> premiered at the <a href=\"https://wikipedia.org/wiki/Houston_Grand_Opera\" title=\"Houston Grand Opera\">Houston Grand Opera</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON>'</a> opera <i><a href=\"https://wikipedia.org/wiki/<PERSON>_in_China\" title=\"<PERSON> in China\"><PERSON> in China</a></i> premiered at the <a href=\"https://wikipedia.org/wiki/Houston_Grand_Opera\" title=\"Houston Grand Opera\">Houston Grand Opera</a>.", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}, {"title": "<PERSON> in China", "link": "https://wikipedia.org/wiki/<PERSON>_in_China"}, {"title": "Houston Grand Opera", "link": "https://wikipedia.org/wiki/Houston_Grand_Opera"}]}, {"year": "1992", "text": "Space Shuttle Columbia launches on STS-52 to deploy the LAGEOS-2 satellite and microgravity experiments.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-52\" title=\"STS-52\">STS-52</a> to deploy the <a href=\"https://wikipedia.org/wiki/LAGEOS\" title=\"LAGEOS\">LAGEOS</a>-2 satellite and microgravity experiments.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-52\" title=\"STS-52\">STS-52</a> to deploy the <a href=\"https://wikipedia.org/wiki/LAGEOS\" title=\"LAGEOS\">LAGEOS</a>-2 satellite and microgravity experiments.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-52", "link": "https://wikipedia.org/wiki/STS-52"}, {"title": "LAGEOS", "link": "https://wikipedia.org/wiki/LAGEOS"}]}, {"year": "1997", "text": "Danish fugitive <PERSON><PERSON> kills two police officers, Chief Constable <PERSON><PERSON> and Senior Constable <PERSON><PERSON>, in Ullanlinna, Helsinki, Finland during his prison escape.", "html": "1997 - Danish fugitive <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> kills two police officers, Chief Constable <PERSON><PERSON> and Senior Constable <PERSON><PERSON>, in <a href=\"https://wikipedia.org/wiki/Ullanlinna\" title=\"Ullanlinna\">Ullanlinna</a>, <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> during his prison escape.", "no_year_html": "Danish fugitive <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> kills two police officers, Chief Constable <PERSON><PERSON> and Senior Constable <PERSON><PERSON>, in <a href=\"https://wikipedia.org/wiki/Ullanlinna\" title=\"Ullanlinna\">Ullanlinna</a>, <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> during his prison escape.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>linna", "link": "https://wikipedia.org/wiki/Ullanlinna"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}]}, {"year": "1999", "text": "<PERSON>, an official in the <PERSON><PERSON> government during World War II, is jailed for crimes against humanity.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, an official in the <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\"><PERSON><PERSON></a> government during World War II, is jailed for crimes against humanity.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, an official in the <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\"><PERSON><PERSON></a> government during World War II, is jailed for crimes against humanity.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vichy_France"}]}, {"year": "2005", "text": "Tropical Storm Alpha forms in the Atlantic Basin, making the 2005 Atlantic hurricane season the most active Atlantic hurricane season until surpassed by the 2020 season.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Tropical_Storm_Alpha_(2005)\" title=\"Tropical Storm Alpha (2005)\">Tropical Storm Alpha</a> forms in the Atlantic Basin, making the <a href=\"https://wikipedia.org/wiki/2005_Atlantic_hurricane_season\" title=\"2005 Atlantic hurricane season\">2005 Atlantic hurricane season</a> the most active Atlantic hurricane season until surpassed by the <a href=\"https://wikipedia.org/wiki/2020_Atlantic_hurricane_season\" title=\"2020 Atlantic hurricane season\">2020 season</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tropical_Storm_Alpha_(2005)\" title=\"Tropical Storm Alpha (2005)\">Tropical Storm Alpha</a> forms in the Atlantic Basin, making the <a href=\"https://wikipedia.org/wiki/2005_Atlantic_hurricane_season\" title=\"2005 Atlantic hurricane season\">2005 Atlantic hurricane season</a> the most active Atlantic hurricane season until surpassed by the <a href=\"https://wikipedia.org/wiki/2020_Atlantic_hurricane_season\" title=\"2020 Atlantic hurricane season\">2020 season</a>.", "links": [{"title": "Tropical Storm Alpha (2005)", "link": "https://wikipedia.org/wiki/Tropical_Storm_Alpha_(2005)"}, {"title": "2005 Atlantic hurricane season", "link": "https://wikipedia.org/wiki/2005_Atlantic_hurricane_season"}, {"title": "2020 Atlantic hurricane season", "link": "https://wikipedia.org/wiki/2020_Atlantic_hurricane_season"}]}, {"year": "2005", "text": "Bellview Airlines Flight 210 crashes in Nigeria, killing all 117 people on board.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Bellview_Airlines_Flight_210\" title=\"Bellview Airlines Flight 210\">Bellview Airlines Flight 210</a> crashes in Nigeria, killing all 117 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bellview_Airlines_Flight_210\" title=\"Bellview Airlines Flight 210\">Bellview Airlines Flight 210</a> crashes in Nigeria, killing all 117 people on board.", "links": [{"title": "Bellview Airlines Flight 210", "link": "https://wikipedia.org/wiki/Bellview_Airlines_Flight_210"}]}, {"year": "2006", "text": "A Panama Canal expansion proposal is approved by 77.8% of voters in a national referendum.", "html": "2006 - A <a href=\"https://wikipedia.org/wiki/Panama_Canal_expansion_project\" title=\"Panama Canal expansion project\">Panama Canal expansion</a> proposal is approved by 77.8% of voters in a <a href=\"https://wikipedia.org/wiki/2006_Panama_Canal_expansion_referendum\" title=\"2006 Panama Canal expansion referendum\">national referendum</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Panama_Canal_expansion_project\" title=\"Panama Canal expansion project\">Panama Canal expansion</a> proposal is approved by 77.8% of voters in a <a href=\"https://wikipedia.org/wiki/2006_Panama_Canal_expansion_referendum\" title=\"2006 Panama Canal expansion referendum\">national referendum</a>.", "links": [{"title": "Panama Canal expansion project", "link": "https://wikipedia.org/wiki/Panama_Canal_expansion_project"}, {"title": "2006 Panama Canal expansion referendum", "link": "https://wikipedia.org/wiki/2006_Panama_Canal_expansion_referendum"}]}, {"year": "2007", "text": "A raid on Anuradhapura Air Force Base is carried out by 21 Tamil Tiger commandos, with all except one dying in this attack. Eight Sri Lanka Air Force planes are destroyed and ten damaged.", "html": "2007 - A <a href=\"https://wikipedia.org/wiki/Raid_on_Anuradhapura_Air_Force_Base\" title=\"Raid on Anuradhapura Air Force Base\">raid on Anuradhapura Air Force Base</a> is carried out by 21 Tamil Tiger commandos, with all except one dying in this attack. Eight Sri Lanka Air Force planes are destroyed and ten damaged.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Raid_on_Anuradhapura_Air_Force_Base\" title=\"Raid on Anuradhapura Air Force Base\">raid on Anuradhapura Air Force Base</a> is carried out by 21 Tamil Tiger commandos, with all except one dying in this attack. Eight Sri Lanka Air Force planes are destroyed and ten damaged.", "links": [{"title": "Raid on Anuradhapura Air Force Base", "link": "https://wikipedia.org/wiki/Raid_on_Anuradhapura_Air_Force_Base"}]}, {"year": "2008", "text": "India launches its first uncrewed lunar probe mission Chandrayaan-1.", "html": "2008 - India launches its first uncrewed <a href=\"https://wikipedia.org/wiki/Lunar_probe\" class=\"mw-redirect\" title=\"Lunar probe\">lunar probe</a> mission <a href=\"https://wikipedia.org/wiki/Chandrayaan-1\" title=\"<PERSON><PERSON>an-1\"><PERSON>yaan-1</a>.", "no_year_html": "India launches its first uncrewed <a href=\"https://wikipedia.org/wiki/Lunar_probe\" class=\"mw-redirect\" title=\"Lunar probe\">lunar probe</a> mission <a href=\"https://wikipedia.org/wiki/<PERSON>yaan-1\" title=\"<PERSON><PERSON>an-1\"><PERSON><PERSON><PERSON>-1</a>.", "links": [{"title": "Lunar probe", "link": "https://wikipedia.org/wiki/Lunar_probe"}, {"title": "Chandrayaan-1", "link": "https://wikipedia.org/wiki/<PERSON>yaan-1"}]}, {"year": "2012", "text": "Cyclist <PERSON> is formally stripped of his seven Tour de France titles after being charged for doping.", "html": "2012 - Cyclist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is formally stripped of his seven <a href=\"https://wikipedia.org/wiki/Tour_de_France\" title=\"Tour de France\">Tour de France</a> titles after being charged for <a href=\"https://wikipedia.org/wiki/Doping_in_sport\" title=\"Doping in sport\">doping</a>.", "no_year_html": "Cyclist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is formally stripped of his seven <a href=\"https://wikipedia.org/wiki/Tour_de_France\" title=\"Tour de France\">Tour de France</a> titles after being charged for <a href=\"https://wikipedia.org/wiki/Doping_in_sport\" title=\"Doping in sport\">doping</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tour de France", "link": "https://wikipedia.org/wiki/Tour_de_France"}, {"title": "Doping in sport", "link": "https://wikipedia.org/wiki/Doping_in_sport"}]}, {"year": "2013", "text": "The Australian Capital Territory becomes the first Australian jurisdiction to legalize same-sex marriage with the Marriage Equality (Same Sex) Act 2013.", "html": "2013 - The <a href=\"https://wikipedia.org/wiki/Australian_Capital_Territory\" title=\"Australian Capital Territory\">Australian Capital Territory</a> becomes the first Australian jurisdiction to legalize <a href=\"https://wikipedia.org/wiki/Same-sex_marriage\" title=\"Same-sex marriage\">same-sex marriage</a> with the <a href=\"https://wikipedia.org/wiki/Marriage_Equality_(Same_Sex)_Act_2013\" title=\"Marriage Equality (Same Sex) Act 2013\">Marriage Equality (Same Sex) Act 2013</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Australian_Capital_Territory\" title=\"Australian Capital Territory\">Australian Capital Territory</a> becomes the first Australian jurisdiction to legalize <a href=\"https://wikipedia.org/wiki/Same-sex_marriage\" title=\"Same-sex marriage\">same-sex marriage</a> with the <a href=\"https://wikipedia.org/wiki/Marriage_Equality_(Same_Sex)_Act_2013\" title=\"Marriage Equality (Same Sex) Act 2013\">Marriage Equality (Same Sex) Act 2013</a>.", "links": [{"title": "Australian Capital Territory", "link": "https://wikipedia.org/wiki/Australian_Capital_Territory"}, {"title": "Same-sex marriage", "link": "https://wikipedia.org/wiki/Same-sex_marriage"}, {"title": "Marriage Equality (Same Sex) Act 2013", "link": "https://wikipedia.org/wiki/Marriage_Equality_(Same_Sex)_Act_2013"}]}, {"year": "2014", "text": "<PERSON> attacks the Parliament of Canada, killing a soldier and injuring three other people.", "html": "2014 - <PERSON> <a href=\"https://wikipedia.org/wiki/2014_shootings_at_Parliament_Hill,_Ottawa\" title=\"2014 shootings at Parliament Hill, Ottawa\">attacks</a> the Parliament of Canada, killing a soldier and injuring three other people.", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/2014_shootings_at_Parliament_Hill,_Ottawa\" title=\"2014 shootings at Parliament Hill, Ottawa\">attacks</a> the Parliament of Canada, killing a soldier and injuring three other people.", "links": [{"title": "2014 shootings at Parliament Hill, Ottawa", "link": "https://wikipedia.org/wiki/2014_shootings_at_Parliament_Hill,_Ottawa"}]}, {"year": "2019", "text": "Same-sex marriage is legalised, and abortion is decriminalised in Northern Ireland as a result of the Northern Ireland Assembly not being restored.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Same-sex_marriage\" title=\"Same-sex marriage\">Same-sex marriage</a> is legalised, and <a href=\"https://wikipedia.org/wiki/Abortion\" title=\"Abortion\">abortion</a> is decriminalised in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> as a result of the <a href=\"https://wikipedia.org/wiki/Northern_Ireland_Assembly\" title=\"Northern Ireland Assembly\">Northern Ireland Assembly</a> not being restored.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Same-sex_marriage\" title=\"Same-sex marriage\">Same-sex marriage</a> is legalised, and <a href=\"https://wikipedia.org/wiki/Abortion\" title=\"Abortion\">abortion</a> is decriminalised in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> as a result of the <a href=\"https://wikipedia.org/wiki/Northern_Ireland_Assembly\" title=\"Northern Ireland Assembly\">Northern Ireland Assembly</a> not being restored.", "links": [{"title": "Same-sex marriage", "link": "https://wikipedia.org/wiki/Same-sex_marriage"}, {"title": "Abortion", "link": "https://wikipedia.org/wiki/Abortion"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "Northern Ireland Assembly", "link": "https://wikipedia.org/wiki/Northern_Ireland_Assembly"}]}], "Births": [{"year": "955", "text": "<PERSON><PERSON>, king of Wuyue (d. 991)", "html": "955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yue"}]}, {"year": "1071", "text": "<PERSON>, Duke of Aquitaine (d. 1126)", "html": "1071 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON>, Duke of Aquitaine</a> (d. 1126)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON>, Duke of Aquitaine</a> (d. 1126)", "links": [{"title": "<PERSON>, Duke of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine"}]}, {"year": "1197", "text": "<PERSON><PERSON><PERSON>, Japanese emperor (d. 1242)", "html": "1197 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1242)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1242)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1511", "text": "<PERSON><PERSON>, German astronomer and mathematician (d. 1553)", "html": "1511 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Reinhold\" title=\"Era<PERSON> Reinhold\"><PERSON><PERSON></a>, German astronomer and mathematician (d. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Re<PERSON>hold\" title=\"Erasmus Reinhold\"><PERSON><PERSON></a>, German astronomer and mathematician (d. 1553)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>inhold"}]}, {"year": "1587", "text": "<PERSON>, German mathematician and philosopher (d. 1657)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and philosopher (d. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and philosopher (d. 1657)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1592", "text": "<PERSON>, Count of Pori (d. 1657)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Pori\" class=\"mw-redirect\" title=\"<PERSON>, Count of Pori\"><PERSON>, Count of Pori</a> (d. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Pori\" class=\"mw-redirect\" title=\"<PERSON>, Count of Pori\"><PERSON>, Count of Pori</a> (d. 1657)", "links": [{"title": "<PERSON>, Count of Pori", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1659", "text": "<PERSON>, German chemist and physician (d. 1734)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and physician (d. 1734)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and physician (d. 1734)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1689", "text": "<PERSON>, Portuguese king (d. 1750)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/John_V_of_Portugal\" title=\"John V of Portugal\"><PERSON></a>, Portuguese king (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_V_of_Portugal\" title=\"John V of Portugal\"><PERSON></a>, Portuguese king (d. 1750)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/John_V_of_Portugal"}]}, {"year": "1701", "text": "<PERSON>, Holy Roman Empress (d. 1756)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Empress\" title=\"<PERSON>, Holy Roman Empress\"><PERSON>, Holy Roman Empress</a> (d. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Empress\" title=\"<PERSON>, Holy Roman Empress\"><PERSON>, Holy Roman Empress</a> (d. 1756)", "links": [{"title": "<PERSON>, Holy Roman Empress", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Empress"}]}, {"year": "1729", "text": "<PERSON>, German pastor and botanist (d. 1798)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor and botanist (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor and botanist (d. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON><PERSON><PERSON>, Dutch historian and bookseller (d. 1816)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> van <PERSON> A<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch historian and bookseller (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> van <PERSON> A<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch historian and bookseller (d. 1816)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>a"}]}, {"year": "1778", "text": "<PERSON>, Spanish jurist and politician (d. 1848)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">jurist</a> and politician (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">jurist</a> and politician (d. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jurist"}]}, {"year": "1781", "text": "<PERSON>, <PERSON><PERSON><PERSON> of France (d. 1789)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_of_France\" title=\"<PERSON>, <PERSON><PERSON><PERSON> of France\"><PERSON>, <PERSON><PERSON><PERSON> of France</a> (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_of_France\" title=\"<PERSON>, <PERSON><PERSON><PERSON> of France\"><PERSON>, <PERSON><PERSON><PERSON> of France</a> (d. 1789)", "links": [{"title": "<PERSON>, <PERSON><PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_of_France"}]}, {"year": "1783", "text": "<PERSON>, Ottoman-French polymath and naturalist (d. 1840)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman-French polymath and naturalist (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman-French polymath and naturalist (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON><PERSON>, American lawyer, jurist, and statesman, Texas Attorney General (d. 1889)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer, jurist, and statesman, <a href=\"https://wikipedia.org/wiki/Texas_Attorney_General\" title=\"Texas Attorney General\">Texas Attorney General</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer, jurist, and statesman, <a href=\"https://wikipedia.org/wiki/Texas_Attorney_General\" title=\"Texas Attorney General\">Texas Attorney General</a> (d. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Texas Attorney General", "link": "https://wikipedia.org/wiki/Texas_Attorney_General"}]}, {"year": "1811", "text": "<PERSON>, Hungarian pianist and composer (d. 1886)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian pianist and composer (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian pianist and composer (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_Liszt"}]}, {"year": "1818", "text": "<PERSON><PERSON><PERSON>, French poet and author (d. 1894)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/Le<PERSON><PERSON>_de_<PERSON>\" title=\"Leconte de Lisle\"><PERSON><PERSON><PERSON></a>, French poet and author (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le<PERSON><PERSON>_de_<PERSON>\" title=\"Leconte de Lisle\"><PERSON><PERSON><PERSON></a>, French poet and author (d. 1894)", "links": [{"title": "Leconte de Lisle", "link": "https://wikipedia.org/wiki/Leconte_de_Lisle"}]}, {"year": "1821", "text": "<PERSON><PERSON>, American businessman (d. 1900)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Huntington\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Huntington\"><PERSON><PERSON></a>, American businessman (d. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Potter_Huntington"}]}, {"year": "1832", "text": "<PERSON>, Czech composer and conductor (d. 1903)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Czech composer and conductor (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\"><PERSON></a>, Czech composer and conductor (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>"}]}, {"year": "1843", "text": "<PERSON>-<PERSON>, English classical scholar, academic administrator, translator, and author (d. 1916)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English classical scholar, academic administrator, translator, and author (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English classical scholar, academic administrator, translator, and author (d. 1916)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, French actress and manager (d. 1923)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and manager (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and manager (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, Canadian M<PERSON>tis scholar and politician (d. 1885)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian <PERSON><PERSON><PERSON> scholar and politician (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian <PERSON><PERSON><PERSON> scholar and politician (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>"}]}, {"year": "1847", "text": "<PERSON><PERSON>, South African Boer general (d. 1914)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Koos_de_la_Rey\" title=\"Koos de la Rey\"><PERSON><PERSON> <PERSON> Rey</a>, South African Boer general (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_la_Rey\" title=\"Koos de la Rey\"><PERSON><PERSON> <PERSON></a>, South African Boer general (d. 1914)", "links": [{"title": "Koos de la Rey", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_la_Rey"}]}, {"year": "1850", "text": "<PERSON>, Australian politician, 20th Premier of South Australia (d. 1908)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1858", "text": "<PERSON> Victoria of Schleswig-Holstein (d. 1921)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Augusta_Victoria_of_Schleswig-Holstein\" title=\"Augusta Victoria of Schleswig-Holstein\">Augusta Victoria of Schleswig-Holstein</a> (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta_Victoria_of_Schleswig-Holstein\" title=\"Augusta Victoria of Schleswig-Holstein\">Augusta Victoria of Schleswig-Holstein</a> (d. 1921)", "links": [{"title": "Augusta Victoria of Schleswig-Holstein", "link": "https://wikipedia.org/wiki/Augusta_Victoria_of_Schleswig-Holstein"}]}, {"year": "1859", "text": "<PERSON> <PERSON> of Bavaria (d. 1949)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Bavaria\" title=\"Prince <PERSON> of Bavaria\">Prince <PERSON> of Bavaria</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Bavaria\" title=\"Prince <PERSON> of Bavaria\">Prince <PERSON> of Bavaria</a> (d. 1949)", "links": [{"title": "Prince <PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Bavaria"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, Estonian painter and illustrator (d. 1943)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian painter and illustrator (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian painter and illustrator (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Russian author and poet, Nobel Prize laureate (d. 1953)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1870", "text": "Lord <PERSON>, English author and poet (d. 1945)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English author and poet (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English author and poet (d. 1945)", "links": [{"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Finnish linguist and diplomat (d. 1950)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish linguist and diplomat (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish linguist and diplomat (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Indian philosopher and educator (d. 1906)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Tirtha\" title=\"Rama Tirtha\"><PERSON> T<PERSON></a>, Indian philosopher and educator (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Tirtha\" title=\"Rama Tirtha\"><PERSON> T<PERSON></a>, Indian philosopher and educator (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Tirtha"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French Catholic missionary and botanist (d. 1914)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>\" title=\"Th<PERSON>od<PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, French Catholic missionary and botanist (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>\" title=\"Th<PERSON>od<PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, French Catholic missionary and botanist (d. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>beig"}]}, {"year": "1875", "text": "<PERSON>, Dutch economist and politician (d. 1962)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch economist and politician (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch economist and politician (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 1958)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1881", "text": "<PERSON>, German geophysicist and seismologist (d. 1908)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geophysicist and seismologist (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geophysicist and seismologist (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, French-English illustrator (d. 1953)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English illustrator (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English illustrator (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON> <PERSON><PERSON>, American painter and illustrator (d. 1945)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/N._C<PERSON>_Wyeth\" title=\"N. C<PERSON> Wyeth\"><PERSON><PERSON> <PERSON><PERSON></a>, American painter and illustrator (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N._C._Wyeth\" title=\"N. C<PERSON> Wyeth\"><PERSON><PERSON> <PERSON><PERSON></a>, American painter and illustrator (d. 1945)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_Wyeth"}]}, {"year": "1885", "text": "<PERSON>, Italian tenor and actor (d. 1969)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Swedish minister (d. 1970)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lutheran_minister)\" title=\"<PERSON> (Lutheran minister)\"><PERSON></a>, Swedish minister (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lutheran_minister)\" title=\"<PERSON> (Lutheran minister)\"><PERSON></a>, Swedish minister (d. 1970)", "links": [{"title": "<PERSON> (Lutheran minister)", "link": "https://wikipedia.org/wiki/<PERSON>_(Lutheran_minister)"}]}, {"year": "1887", "text": "<PERSON>, American journalist and poet (d. 1920)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and poet (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and poet (d. 1920)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_(journalist)"}]}, {"year": "1893", "text": "<PERSON>, Estonian astronomer and astrophysicist (d. 1985)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96pik\" title=\"<PERSON>\"><PERSON></a>, Estonian astronomer and astrophysicist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96pik\" title=\"<PERSON>\"><PERSON></a>, Estonian astronomer and astrophysicist (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_%C3%96pik"}]}, {"year": "1893", "text": "<PERSON>, Spanish footballer (d. 1955)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Spanish footballer (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Spanish footballer (d. 1955)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1894", "text": "<PERSON>, Chinese actor and singer (d. 1961)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese actor and singer (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese actor and singer (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}]}, {"year": "1895", "text": "<PERSON>, professional baseball player (d. 1966)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, professional baseball player (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, professional baseball player (d. 1966)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1896", "text": "<PERSON>, American biochemist and academic (d. 1988)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, American biochemist and academic (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, American biochemist and academic (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Portuguese film director and playwright (d. 1967)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Leit%C3%A3<PERSON>_de_Barros\" title=\"<PERSON>\"><PERSON></a>, Portuguese film director and playwright (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Leit%C3%A3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese film director and playwright (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Leit%C3%A3o_de_Barr<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American author and illustrator (d. 1958)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish poet and philologist (d. 1990)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/D%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish poet and <a href=\"https://wikipedia.org/wiki/Philologist\" class=\"mw-redirect\" title=\"Philologist\">philologist</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish poet and <a href=\"https://wikipedia.org/wiki/Philologist\" class=\"mw-redirect\" title=\"Philologist\">philologist</a> (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Philologist", "link": "https://wikipedia.org/wiki/Philologist"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, Salvadoran writer and painter (d. 1975)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Salarru%C3%A9\" title=\"Sal<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Salvadoran writer and painter (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salarru%C3%A9\" title=\"Sal<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Salvadoran writer and painter (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Salarru%C3%A9"}]}, {"year": "1903", "text": "<PERSON>, American geneticist and academic, Nobel Prize laureate (d. 1989)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>le"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1903", "text": "C<PERSON>, American comedian and vaudevillian (d. 1952)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian and vaudevillian (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian and vaudevillian (d. 1952)", "links": [{"title": "Curly <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American actress, singer, and producer (d. 1965)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and producer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and producer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Argentine football player (d. 1973)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Sa%C3%BAl_Calandra\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine football player (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sa%C3%BAl_Calandra\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine football player (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sa%C3%BAl_Calandra"}]}, {"year": "1904", "text": "<PERSON>, American physicist and radio engineer (d. 1950)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and radio engineer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and radio engineer (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Hungarian-French pianist and composer (d. 1969)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-French pianist and composer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-French pianist and composer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Dutch composer and educator (d. 1970)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch composer and educator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch composer and educator (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Cuban mathematician and lawyer (d. 1978)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Aurel<PERSON>_Baldor\" title=\"Aurel<PERSON> Baldo<PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban mathematician and lawyer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aurel<PERSON>_Baldor\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban mathematician and lawyer (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurelio_Baldor"}]}, {"year": "1907", "text": "<PERSON>, American baseball player (d. 1967)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, German tenor (d. 1981)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/G%C3%BCnther_Treptow\" title=\"G<PERSON><PERSON><PERSON> Treptow\"><PERSON><PERSON><PERSON><PERSON></a>, German tenor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%BCnther_Treptow\" title=\"G<PERSON><PERSON><PERSON> Treptow\"><PERSON><PERSON><PERSON><PERSON></a>, German tenor (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCnther_Treptow"}]}, {"year": "1908", "text": "<PERSON>, American journalist and author (d. 2003)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(columnist)\" title=\"<PERSON> (columnist)\"><PERSON></a>, American journalist and author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(columnist)\" title=\"<PERSON> (columnist)\"><PERSON></a>, American journalist and author (d. 2003)", "links": [{"title": "<PERSON> (columnist)", "link": "https://wikipedia.org/wiki/<PERSON>(columnist)"}]}, {"year": "1908", "text": "<PERSON>, Spanish cartoonist (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Escobar_Saliente\" title=\"<PERSON>\"><PERSON></a>, Spanish cartoonist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Escobar_Saliente\" title=\"<PERSON>\"><PERSON></a>, Spanish cartoonist (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Escobar_Saliente"}]}, {"year": "1913", "text": "<PERSON>, Hungarian-American photographer and journalist (d. 1954)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American photographer and journalist (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American photographer and journalist (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Vietnamese emperor  (d. 1997)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i\" title=\"Bảo Đại\"><PERSON><PERSON><PERSON></a>, Vietnamese emperor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i\" title=\"Bảo Đại\"><PERSON><PERSON><PERSON></a>, Vietnamese emperor (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Swiss lawyer and politician, 63rd President of the Swiss Confederation (d. 2002)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss lawyer and politician, 63rd <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss lawyer and politician, 63rd <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "President of the Swiss Confederation", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_Confederation"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Belarusian-Israeli civil servant and politician, 7th Prime Minister of Israel (d. 2012)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian-Israeli civil servant and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian-Israeli civil servant and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}]}, {"year": "1917", "text": "<PERSON>, British-American actress (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American baseball player, coach, and manager (d. 1976)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, British novelist, poet, playwright, Nobel Prize laureate (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, poet, playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, poet, playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1920", "text": "<PERSON>, American psychologist and author (d. 1996)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French singer-songwriter and guitarist (d. 1981)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Russian mathematician and computer scientist (d. 1986)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and computer scientist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and computer scientist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Estonian sergeant (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian sergeant (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian sergeant (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, German footballer and coach (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American basketball player and coach (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Martin\"><PERSON></a>, American basketball player and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Martin\"><PERSON></a>, American basketball player and coach (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Hawaiian genealogist, author, and hula expert (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hawaiian genealogist, author, and hula expert (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hawaiian genealogist, author, and hula expert (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American painter and illustrator (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, South African minister and politician (d. 2005)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African minister and politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African minister and politician (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American pianist, composer and arranger (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer and arranger (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer and arranger (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Brazilian director, producer, and screenwriter (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director, producer, and screenwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director, producer, and screenwriter (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, 2nd Baron <PERSON>, English director and producer (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English director and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English director and producer (d. 2015)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Russian footballer (d. 1990)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Argentine human rights activist", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine human rights activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine human rights activist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Spanish singer (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Guardiola\" title=\"<PERSON>\"><PERSON></a>, Spanish singer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Guardiola\" title=\"<PERSON>\"><PERSON></a>, Spanish singer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Guardiola"}]}, {"year": "1931", "text": "<PERSON>, American police officer and author (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Ann_Rule\" title=\"Ann Rule\"><PERSON></a>, American police officer and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ann_Rule\" title=\"Ann Rule\"><PERSON></a>, American police officer and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Rule"}]}, {"year": "1933", "text": "<PERSON>, Argentine philosopher and martyr (d. 1974)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine philosopher and martyr (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine philosopher and martyr (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Austrian footballer and manager (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, New Zealand opera singer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand opera singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand opera singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>-<PERSON><PERSON>, English soldier, author, and explorer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, author, and explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, author, and explorer", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English architect and academic", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect and academic", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>(architect)"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Serbian metropolitan (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian metropolitan (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian metropolitan (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Pavlovi%C4%87"}]}, {"year": "1936", "text": "<PERSON>, American political activist and author, co-founder of the Black Panther Party", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political activist and author, co-founder of the <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political activist and author, co-founder of the <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Black Panther Party", "link": "https://wikipedia.org/wiki/Black_Panther_Party"}]}, {"year": "1937", "text": "<PERSON>, Argentine singer-songwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Egyptian-Greek composer (d. 1982)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Man<PERSON>_Lo%C3%AFzos\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-Greek composer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Lo%C3%AFzos\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-Greek composer (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manos_Lo%C3%AFzos"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Sri Lankan historian and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan historian and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>a"}]}, {"year": "1938", "text": "<PERSON>, English actor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actor, comedian and producer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Argentine footballer and manager (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON>, Mozambican politician, 2nd President of Mozambique", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mozambican politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Mozambique\" class=\"mw-redirect\" title=\"President of Mozambique\">President of Mozambique</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mozambican politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Mozambique\" class=\"mw-redirect\" title=\"President of Mozambique\">President of Mozambique</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Mozambique", "link": "https://wikipedia.org/wiki/President_of_Mozambique"}]}, {"year": "1939", "text": "<PERSON>, English footballer (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actor and singer (d. 2025)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (d. 2025)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1941", "text": "<PERSON>, English-American actor (d. 2014)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-American actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-American actor (d. 2014)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1966)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actress and singer (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Puerto Rican wrestler (d. 2019)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican wrestler (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican wrestler (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American-Canadian wrestler and coach (d. 2007)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Co<PERSON>\"><PERSON></a>, American-Canadian wrestler and coach (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Co<PERSON>\"><PERSON></a>, American-Canadian wrestler and coach (d. 2007)", "links": [{"title": "Allen Coage", "link": "https://wikipedia.org/wiki/Allen_Coage"}]}, {"year": "1943", "text": "<PERSON>, American actress (d. 2015)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Dutch director, producer, and cinematographer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director, producer, and cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director, producer, and cinematographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, French actress and singer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Zanzibari politician, 2nd Chief Minister of Zanzibar (d. 2021)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Zanzibari politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Zanzibar\" class=\"mw-redirect\" title=\"Chief Minister of Zanzibar\">Chief Minister of Zanzibar</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Zanzibari politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Zanzibar\" class=\"mw-redirect\" title=\"Chief Minister of Zanzibar\">Chief Minister of Zanzibar</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Minister of Zanzibar", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Zanzibar"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Canadian actor and game show host", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actor and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actor and game show host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English long jumper", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Barbadian-English horse trainer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian-English horse trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian-English horse trainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2020)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leslie_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian educator and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Zambian footballer (d. 1993)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian footballer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian footballer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Indian-American physician and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-American physician and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-American physician and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, South African mezzo-soprano (d. 2012)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African mezzo-soprano (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African mezzo-soprano (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, English journalist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Ecuadorian politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian lawyer and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American lawyer and politician, 62nd Governor of Mississippi", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Mississippi\" title=\"Governor of Mississippi\">Governor of Mississippi</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Mississippi\" title=\"Governor of Mississippi\">Governor of Mississippi</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Mississippi", "link": "https://wikipedia.org/wiki/Governor_of_Mississippi"}]}, {"year": "1947", "text": "<PERSON>, Indian poet (d. 2011)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian poet (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian poet (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English cricketer, coach, and umpire (d. 2021)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, coach, and umpire (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, coach, and umpire (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and actor (d. 1990)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"St<PERSON> Bators\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"St<PERSON> Bators\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 1990)", "links": [{"title": "Stiv Bators", "link": "https://wikipedia.org/wiki/Stiv_Bators"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek politician, Greek Minister of Employment (d. 2015)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Labour,_Social_Security_and_Welfare_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Labour, Social Security and Welfare (Greece)\">Greek Minister of Employment</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Labour,_Social_Security_and_Welfare_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Labour, Social Security and Welfare (Greece)\">Greek Minister of Employment</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Labour, Social Security and Welfare (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Labour,_Social_Security_and_Welfare_(Greece)"}]}, {"year": "1949", "text": "<PERSON>, German flute player, composer, and conductor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German flute player, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German flute player, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, French footballer and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Ars%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ars%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ars%C3%A8ne_<PERSON>ger"}]}, {"year": "1950", "text": "<PERSON>, Guyanese politician, 8th President of Guyana", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Guyana", "link": "https://wikipedia.org/wiki/President_of_Guyana"}]}, {"year": "1952", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dash\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Mexican politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English author and educator (d. 2014)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian rugby league player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1956", "text": "<PERSON>, Argentine photographer (d. 2003)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine photographer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine photographer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German jumper", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Argentine musician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American drummer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Finnish journalist and author (d. 2005)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Arto_<PERSON>n\" title=\"Art<PERSON>\"><PERSON><PERSON></a>, Finnish journalist and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arto_<PERSON>n\" title=\"Art<PERSON>\"><PERSON><PERSON></a>, Finnish journalist and author (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arto_Salminen"}]}, {"year": "1959", "text": "<PERSON>, American composer and songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American bass player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American singer-songwriter and bass player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American tennis player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor and comedian", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American figure skater", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian basketball player (d. 1993)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Dra%C5%BEen_Petrovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian basketball player (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dra%C5%BEen_Petrovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian basketball player (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dra%C5%BEen_Petrovi%C4%87"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>M<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>M<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "TobyMac", "link": "https://wikipedia.org/wiki/TobyMac"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Venezuelan chef", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Sumito_Est%C3%A9vez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan chef", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sumito_Est%C3%A9vez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan chef", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sumito_Est%C3%A9vez"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Italian actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vale<PERSON>_Golino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vale<PERSON>_Golino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valeria_Golino"}]}, {"year": "1965", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1965", "text": "<PERSON><PERSON> <PERSON><PERSON>, Scottish comedian, journalist, and author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish comedian, journalist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish comedian, journalist, and author", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American football player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Polish singer-songwriter, guitarist, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>otr_Wiwczarek\" title=\"<PERSON><PERSON><PERSON> Wiw<PERSON>arek\"><PERSON><PERSON><PERSON></a>, Polish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>otr_Wiwczarek\" title=\"<PERSON>ot<PERSON> Wiw<PERSON>arek\"><PERSON><PERSON><PERSON></a>, Polish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Piotr_Wiwczarek"}]}, {"year": "1966", "text": "<PERSON>, Russian-Japanese boxer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Japanese boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Japanese boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, New York City-born Puerto Rican Salsa romántica singer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New York City-born Puerto Rican Salsa romántica singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New York City-born Puerto Rican Salsa romántica singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Italian composer and conductor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Di Vittorio\"><PERSON></a>, Italian composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Di Vittorio\"><PERSON></a>, Italian composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Baroness <PERSON> of Bow, British business executive and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_King_of_Bow\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baroness King of Bow\"><PERSON><PERSON>, Baroness <PERSON> of Bow</a>, British business executive and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_King_of_Bow\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baroness King of Bow\"><PERSON><PERSON>, Baroness King of Bow</a>, British business executive and politician", "links": [{"title": "<PERSON><PERSON>, Baroness King of Bow", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_King,_Baroness_King_of_Bow"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, Austrian skier (d. 1994)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian skier (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian skier (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Honduran-American comedian, actor, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON>-American comedian, actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON>ran-American comedian, actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player, coach, and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American lawyer and political consultant", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and political consultant", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and political consultant", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Quintal\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Quintal\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9phane_Quintal"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Jamaican singer-songwriter and DJ", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Jamaican singer-songwriter and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Jamaican singer-songwriter and DJ", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>ha<PERSON>_(musician)"}]}, {"year": "1969", "text": "<PERSON>, Venezuelan politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>asco"}]}, {"year": "1969", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Belgian singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Spanish musician and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Coque_Malla\" title=\"Coque Malla\"><PERSON><PERSON></a>, Spanish musician and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Coque_Malla\" title=\"Coque Malla\"><PERSON><PERSON></a>, Spanish musician and actor", "links": [{"title": "Coque Malla", "link": "https://wikipedia.org/wiki/Co<PERSON>_Malla"}]}, {"year": "1970", "text": "<PERSON>, Dutch footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress, director, and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Argentine politician and economist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine politician and economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine politician and economist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, South African tennis player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian basketball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Korn%C3%A9l_D%C3%A1vid\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korn%C3%A9l_D%C3%A1vid\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Korn%C3%A9l_D%C3%A1vid"}]}, {"year": "1971", "text": "<PERSON>, Spanish runner", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, Spanish runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, Spanish runner", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(athlete)"}]}, {"year": "1971", "text": "<PERSON>, American filmmaker", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American filmmaker", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(filmmaker)"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, English-American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/1972\" title=\"1972\">1972</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1972\" title=\"1972\">1972</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-American actress", "links": [{"title": "1972", "link": "https://wikipedia.org/wiki/1972"}, {"title": "Sa<PERSON>ron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Argentine criminal", "html": "1972 - <a href=\"https://wikipedia.org/wiki/V%C3%ADctor_Salda%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine criminal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON>ctor_Salda%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine criminal", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%ADctor_Salda%C3%B1o"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, American wrestler", "html": "1973 - <a href=\"https://wikipedia.org/wiki/D%27L<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%27L<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%27Lo_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmen_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Spanish footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Palop\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Palop\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Palop"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Dutch swimmer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Miroslav_%C5%A0atan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miros<PERSON>_%C5%A0atan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miroslav_%C5%A0atan"}]}, {"year": "1975", "text": "<PERSON>, Argentinian footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Cardetti"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>gado"}]}, {"year": "1976", "text": "<PERSON>, Australian race walker", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON> (racewalker)\"><PERSON></a>, Australian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON> (racewalker)\"><PERSON></a>, Australian race walker", "links": [{"title": "<PERSON> (racewalker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racewalker)"}]}, {"year": "1976", "text": "Laid<PERSON>, Dutch DJ and music producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Laidback_<PERSON>\" title=\"Laidback <PERSON>\">Laidback <PERSON></a>, Dutch DJ and music producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Laidback_<PERSON>\" title=\"Laidback <PERSON>\">Laidback <PERSON></a>, Dutch DJ and music producer", "links": [{"title": "Laidback <PERSON>", "link": "https://wikipedia.org/wiki/Laidback_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American basketball player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Zambian footballer (d. 2007)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>s<PERSON>_Nsofwa\" title=\"<PERSON><PERSON><PERSON> Nsofwa\"><PERSON><PERSON><PERSON></a>, Zambian footballer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>s<PERSON>_Nsofwa\" title=\"<PERSON><PERSON><PERSON> Nsofwa\"><PERSON><PERSON><PERSON></a>, Zambian footballer (d. 2007)", "links": [{"title": "Cha<PERSON>we <PERSON>", "link": "https://wikipedia.org/wiki/Chaswe_Nsofwa"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Pakistani-English cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani-English cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Irish singer-songwriter, guitarist, producer, and footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, guitarist, producer, and footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, guitarist, producer, and footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Donnell\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Donnell\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luke_O%27Donnell"}]}, {"year": "1981", "text": "<PERSON>, American actor and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Dominican baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Robinson_Can%C3%B3\" title=\"Robinson Canó\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Robinson_Can%C3%B3\" title=\"Robinson Canó\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robinson_Can%C3%B3"}]}, {"year": "1982", "text": "<PERSON>, German footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Day\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Day\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Day"}]}, {"year": "1982", "text": "<PERSON>, Australian cyclist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, South Korean singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Byul\" title=\"By<PERSON>\"><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byul\" title=\"By<PERSON>\"><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Byul"}]}, {"year": "1983", "text": "<PERSON>, German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_M%C3%BC<PERSON>_(footballer)"}]}, {"year": "1983", "text": "<PERSON>, British singer and actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Plan_<PERSON>_(musician)\" title=\"<PERSON> <PERSON> (musician)\">Plan B</a>, British singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Plan_B_(musician)\" title=\"<PERSON> <PERSON> (musician)\">Plan B</a>, British singer and actor", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Argentine rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Australian basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Al<PERSON><PERSON>_Mari%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleks_Mari%C4%87"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>str%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antti_Pihlstr%C3%B6m"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American singer-songwriter and drummer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and drummer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "Chancellor, South Korean-American musician", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Chancellor_(musician)\" title=\"Chancellor (musician)\">Chancellor</a>, South Korean-American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chancellor_(musician)\" title=\"Chancellor (musician)\">Chancellor</a>, South Korean-American musician", "links": [{"title": "Chancellor (musician)", "link": "https://wikipedia.org/wiki/Chancellor_(musician)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Japanese actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian soccer player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Romanian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/%C8%98te<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C8%98te<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C8%98te<PERSON>_<PERSON>du"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(football_forward)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (football forward)\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(football_forward)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (football forward)\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (football forward)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(football_forward)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Ethiopian runner", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Tiki_Gelana\" title=\"Tiki Gelana\">Tiki Gelana</a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tiki_Gelana\" title=\"Tiki Gelana\">Tiki Gela<PERSON></a>, Ethiopian runner", "links": [{"title": "Tiki Gelana", "link": "https://wikipedia.org/wiki/Tiki_Gelana"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Lithuanian singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, South Korean actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ha-sun\" title=\"<PERSON>-sun\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ha-sun\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Park_Ha-sun"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Taiwanese actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English diver", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Indian actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Parine<PERSON> Chopra\"><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Parine<PERSON> Chopra\"><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Chopra"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Mexican footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>do"}]}, {"year": "1988", "text": "<PERSON>, Estonian figure skater", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Estonian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Estonian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American rapper and singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/JPEGMafia\" title=\"JPEGMafia\">JPEGMaf<PERSON></a>, American rapper and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/JPEGMafia\" title=\"JPEGMafia\">JPEGMaf<PERSON></a>, American rapper and singer", "links": [{"title": "JPEGMafia", "link": "https://wikipedia.org/wiki/JPEGMafia"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON> <PERSON>, British-American rapper", "html": "1992 - <a href=\"https://wikipedia.org/wiki/21_<PERSON>\" title=\"21 Savage\">21 <PERSON></a>, British-American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/21_<PERSON>\" title=\"21 Savage\">21 <PERSON></a>, British-American rapper", "links": [{"title": "21 Savage", "link": "https://wikipedia.org/wiki/21_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, British-American YouTuber", "html": "1992 - <a href=\"https://wikipedia.org/wiki/SSSniperWolf\" title=\"SSSniperWolf\">SSSniperWolf</a>, British-American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SSSniperWolf\" title=\"SSSniperWolf\">SSSniperWolf</a>, British-American YouTuber", "links": [{"title": "SSSniperWolf", "link": "https://wikipedia.org/wiki/SSSniperWolf"}]}, {"year": "1992", "text": "<PERSON>, American actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sofia_Vass<PERSON>eva"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Charalambos_L<PERSON>ogiannis\" class=\"mw-redirect\" title=\"<PERSON>ral<PERSON><PERSON> L<PERSON>ogian<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charalam<PERSON>_L<PERSON>ogiannis\" class=\"mw-redirect\" title=\"Charalambos Lykogiannis\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Charalambos_L<PERSON>nis"}]}, {"year": "1994", "text": "<PERSON>, American baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Burnes\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Burnes\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Swiss footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, South Korean rapper, singer-songwriter and producer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>.I_(rapper)\" title=\"<PERSON><PERSON><PERSON> (rapper)\"><PERSON><PERSON><PERSON></a>, South Korean rapper, singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>.<PERSON>_(rapper)\" title=\"<PERSON><PERSON><PERSON> (rapper)\"><PERSON><PERSON><PERSON></a>, South Korean rapper, singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>.<PERSON>_(rapper)"}]}, {"year": "1996", "text": "<PERSON>, Norwegian ski runner", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8sflot_Kl%C3%A6bo\" title=\"<PERSON> Klæbo\"><PERSON></a>, Norwegian ski runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8sflot_Kl%C3%A6bo\" title=\"<PERSON> Klæbo\"><PERSON></a>, Norwegian ski runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Johannes_H%C3%B8sflot_Kl%C3%A6bo"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American rapper", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ch\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ch"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>dom<PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dom<PERSON>\" title=\"<PERSON><PERSON> Perdomo\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gerald<PERSON>_Perdomo"}]}, {"year": "2000", "text": "<PERSON>, American rapper and record producer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Baby_<PERSON><PERSON>\" title=\"<PERSON> Ke<PERSON>\"><PERSON></a>, American rapper and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baby_<PERSON><PERSON>\" title=\"<PERSON> Ke<PERSON>\"><PERSON></a>, American rapper and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American football player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Spanish footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "726", "text": "<PERSON><PERSON><PERSON><PERSON>, a Maya ruler of Dos Pilas", "html": "726 - <a href=\"https://wikipedia.org/wiki/Itzamnaaj_K%27awiil\" class=\"mw-redirect\" title=\"Itzamnaaj K'awiil\">Itzamnaaj K'awiil</a>, a <a href=\"https://wikipedia.org/wiki/Maya_rulers\" class=\"mw-redirect\" title=\"Maya rulers\">Maya</a> ruler of <a href=\"https://wikipedia.org/wiki/Dos_Pilas\" title=\"Do<PERSON> Pilas\"><PERSON><PERSON>las</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Itzamnaaj_K%27awiil\" class=\"mw-redirect\" title=\"Itzamnaaj K'awiil\">Itzamnaaj K'awiil</a>, a <a href=\"https://wikipedia.org/wiki/Maya_rulers\" class=\"mw-redirect\" title=\"Maya rulers\">Maya</a> ruler of <a href=\"https://wikipedia.org/wiki/Do<PERSON>_Pilas\" title=\"Do<PERSON> Pilas\"><PERSON><PERSON>las</a>", "links": [{"title": "Itzamnaaj <PERSON>awi<PERSON>", "link": "https://wikipedia.org/wiki/Itzamnaaj_K%27awiil"}, {"title": "Maya rulers", "link": "https://wikipedia.org/wiki/Maya_rulers"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "741", "text": "<PERSON>, Frankish political and military leader (b. 688)", "html": "741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Frankish political and military leader (b. 688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Frankish political and military leader (b. 688)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "842", "text": "<PERSON><PERSON>, Japanese prince (b. 792)", "html": "842 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>\" title=\"Prince <PERSON>\"><PERSON><PERSON></a>, Japanese prince (b. 792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>\" title=\"Prince <PERSON>\"><PERSON><PERSON></a>, Japanese prince (b. 792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1383", "text": "<PERSON> of Portugal, Portuguese king (b. 1345)", "html": "1383 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a>, Portuguese king (b. 1345)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a>, Portuguese king (b. 1345)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1493", "text": "<PERSON>, 1st Earl of Morton, Scottish earl (b. 1426)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Morton\" title=\"<PERSON>, 1st Earl of Morton\"><PERSON>, 1st Earl of Morton</a>, Scottish earl (b. 1426)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Morton\" title=\"<PERSON>, 1st Earl of Morton\"><PERSON>, 1st Earl of Morton</a>, Scottish earl (b. 1426)", "links": [{"title": "<PERSON>, 1st Earl of Morton", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Morton"}]}, {"year": "1565", "text": "<PERSON>, French book collector (b. 1479)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>vi%C3%A8res\" title=\"<PERSON> de Servières\"><PERSON></a>, French book collector (b. 1479)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>vi%C3%A8res\" title=\"<PERSON> de Servières\"><PERSON></a>, French book collector (b. 1479)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8res"}]}, {"year": "1604", "text": "<PERSON>, Spanish theologian (b. 1528)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/Domingo_B%C3%A1%C3%B1ez\" title=\"<PERSON>\"><PERSON></a>, Spanish theologian (b. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Domingo_B%C3%A1%C3%B1ez\" title=\"<PERSON>\"><PERSON></a>, Spanish theologian (b. 1528)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Domingo_B%C3%A1%C3%B1ez"}]}, {"year": "1626", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (b. 1561)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/Kikkawa_Hiroie\" title=\"Kikka<PERSON> Hiroie\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kikka<PERSON>_<PERSON>\" title=\"Kikka<PERSON> Hiroie\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1561)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kikka<PERSON>_<PERSON>e"}]}, {"year": "1708", "text": "<PERSON>, Dutch theologian and academic (b. 1636)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theologian and academic (b. 1636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theologian and academic (b. 1636)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON>, Prince of Orange (b. 1711)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (b. 1711)", "links": [{"title": "<PERSON>, Prince of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange"}]}, {"year": "1792", "text": "<PERSON>, French astronomer (b. 1725)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1725)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, Uruguayan revolutionary general and politician, President of Uruguay (b. 1784)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan revolutionary general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Uruguay\" class=\"mw-redirect\" title=\"List of Presidents of Uruguay\">President of Uruguay</a> (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan revolutionary general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Uruguay\" class=\"mw-redirect\" title=\"List of Presidents of Uruguay\">President of Uruguay</a> (b. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of Uruguay", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Uruguay"}]}, {"year": "1859", "text": "<PERSON>, German violinist and composer (b. 1784)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (b. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Australian cricketer and footballer (b. 1856)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and footballer (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and footballer (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Irish British novelist and soldier (b. 1818)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish British novelist and soldier (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish British novelist and soldier (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, English politician (b. 1835)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Austrian physiologist and physician (b. 1846)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physiologist and physician (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physiologist and physician (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, German geologist and paleontologist (b. 1817)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German geologist and paleontologist (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German geologist and paleontologist (b. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, French painter (b. 1839)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_C%C3%A<PERSON><PERSON><PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 17th <PERSON><PERSON><PERSON><PERSON> (b. 1866)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Konishiki_Yasokichi_I\" title=\"Konishiki Yasokichi I\"><PERSON><PERSON><PERSON> Yasokichi <PERSON></a>, Japanese sumo wrestler, the 17th <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yoko<PERSON>na (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Konishiki_Yasokichi_I\" title=\"Konishiki Yasokichi I\">Ko<PERSON><PERSON> Yasokichi I</a>, Japanese sumo wrestler, the 17th <a href=\"https://wikipedia.org/wiki/Yo<PERSON><PERSON>na_(sumo)\" class=\"mw-redirect\" title=\"Yoko<PERSON>na (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON> Yasokichi I", "link": "https://wikipedia.org/wiki/Konishiki_<PERSON><PERSON><PERSON><PERSON>_I"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (sumo)", "link": "https://wikipedia.org/wiki/Yokozuna_(sumo)"}]}, {"year": "1917", "text": "<PERSON>, English-American boxer (b. 1863)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American boxer (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American boxer (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, founder of the Indian Journal of Medical Research and later Director-General of the Indian Medical Service (b. 1857)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON></a>, founder of the <i><a href=\"https://wikipedia.org/wiki/Indian_Journal_of_Medical_Research\" title=\"Indian Journal of Medical Research\">Indian Journal of Medical Research</a></i> and later Director-General of the Indian Medical Service (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON></a>, founder of the <i><a href=\"https://wikipedia.org/wiki/Indian_Journal_of_Medical_Research\" title=\"Indian Journal of Medical Research\">Indian Journal of Medical Research</a></i> and later Director-General of the Indian Medical Service (b. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Indian Journal of Medical Research", "link": "https://wikipedia.org/wiki/Indian_Journal_of_Medical_Research"}]}, {"year": "1928", "text": "<PERSON>, Scottish-Australian lawyer and politician, 5th Prime Minister of Australia (b. 1862)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1934", "text": "<PERSON>, American gangster (b. 1904)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Pretty_Boy_Floyd\" title=\"Pretty Boy Floyd\">Pretty Boy Floyd</a>, American gangster (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pretty_Boy_Floyd\" title=\"Pretty Boy Floyd\">Pretty Boy Floyd</a>, American gangster (b. 1904)", "links": [{"title": "Pretty Boy Floyd", "link": "https://wikipedia.org/wiki/Pretty_Boy_Floyd"}]}, {"year": "1935", "text": "<PERSON>, Irish-English lawyer and politician, Attorney General for England and Wales (b. 1854)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attorney General for England and Wales", "link": "https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Italian physician (b. 1847)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Ettore_March<PERSON>\" title=\"Ettore March<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physician (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ettore_March<PERSON>\" title=\"Ettore March<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physician (b. 1847)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ettore_Marchiafava"}]}, {"year": "1941", "text": "<PERSON>, French militant (b. 1924)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B4quet\" title=\"<PERSON>\"><PERSON></a>, French militant (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B4quet\" title=\"<PERSON>\"><PERSON></a>, French militant (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guy_M%C3%B4quet"}]}, {"year": "1952", "text": "<PERSON>, Swiss psychiatrist, geneticist, and eugenicist (b. 1874)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Ernst_<PERSON>%C3%BCdin\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist, geneticist, and eugenicist (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCdin\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist, geneticist, and eugenicist (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_R%C3%BCdin"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>,  Bangladeshi-Indian author and poet (b. 1899)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi-Indian author and poet (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi-Indian author and poet (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English activist (b. 1872)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian politician, 29th Premier of New South Wales (b. 1891)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 29th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 29th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1965", "text": "<PERSON><PERSON>,  English singer and actress (b. 1883)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer and actress (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer and actress (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, New Zealand poet, writer, theologian, and social commentator. (b. 1926)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand poet, writer, theologian, and social commentator. (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand poet, writer, theologian, and social commentator. (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Catalan cellist and conductor (b. 1876)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catalan cellist and conductor (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catalan cellist and conductor (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, French composer and educator (b. 1887)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Japanese psychiatrist and author (b. 1914)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese psychiatrist and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese psychiatrist and author (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American poet of the Pacific Northwest (b. 1923)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet of the <a href=\"https://wikipedia.org/wiki/Pacific_Northwest\" title=\"Pacific Northwest\">Pacific Northwest</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet of the <a href=\"https://wikipedia.org/wiki/Pacific_Northwest\" title=\"Pacific Northwest\">Pacific Northwest</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pacific Northwest", "link": "https://wikipedia.org/wiki/Pacific_Northwest"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Romanian soprano and educator (b. 1894)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>ior<PERSON>_<PERSON>\" title=\"<PERSON>ior<PERSON> Ursuleac\"><PERSON><PERSON><PERSON></a>, Romanian soprano and educator (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ior<PERSON>_<PERSON>\" title=\"<PERSON>ior<PERSON> U<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian soprano and educator (b. 1894)", "links": [{"title": "Viorica <PERSON>", "link": "https://wikipedia.org/wiki/Vior<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American musician, comedian, and reporter (b. 1947)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, comedian, and reporter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, comedian, and reporter (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Norwegian guitarist and composer (b. 1943)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Thorgeir_Stub%C3%B8\" title=\"<PERSON>ge<PERSON> Stubø\"><PERSON><PERSON><PERSON></a>, Norwegian guitarist and composer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thorgeir_Stub%C3%B8\" title=\"<PERSON>ge<PERSON> Stubø\"><PERSON><PERSON><PERSON></a>, Norwegian guitarist and composer (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thorgeir_Stub%C3%B8"}]}, {"year": "1986", "text": "<PERSON>, Chinese general and politician, Head of State of the People's Republic of China (b. 1897)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, <a href=\"https://wikipedia.org/wiki/Head_of_State_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Head of State of the People's Republic of China\">Head of State of the People's Republic of China</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, <a href=\"https://wikipedia.org/wiki/Head_of_State_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Head of State of the People's Republic of China\">Head of State of the People's Republic of China</a> (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Head of State of the People's Republic of China", "link": "https://wikipedia.org/wiki/Head_of_State_of_the_People%27s_Republic_of_China"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian-American physiologist and biochemist, Nobel Prize laureate (b. 1893)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-Gy%C3%B6rgyi\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American physiologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%B6rgyi\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American physiologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1893)", "links": [{"title": "<PERSON>Gyö<PERSON>", "link": "https://wikipedia.org/wiki/Albert_Szent-Gy%C3%B6rgyi"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Italian-French actor (b. 1919)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-French actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-French actor (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American author (b. 1915)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, English singer-songwriter, producer, actor, and playwright (b. 1915)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter, producer, actor, and playwright (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter, producer, actor, and playwright (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American kidnapping victim (b. 1978)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American kidnapping victim (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American kidnapping victim (b. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Algerian-French philosopher and academic (b. 1918)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French philosopher and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French philosopher and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Japanese singer and actor (b. 1924)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actor (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ga"}]}, {"year": "1992", "text": "<PERSON>, American sportscaster (b. 1908)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Barber\" title=\"Red Barber\"><PERSON></a>, American sportscaster (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Barber\" title=\"Red Barber\"><PERSON></a>, American sportscaster (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Barber"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American actor (b. 1939)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Little\"><PERSON><PERSON><PERSON></a>, American actor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Little\"><PERSON><PERSON><PERSON></a>, American actor (b. 1939)", "links": [{"title": "Cleavon Little", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Little"}]}, {"year": "1993", "text": "<PERSON><PERSON>, English racing driver and engineer (b. 1930)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Innes_Ireland\" title=\"Innes Ireland\"><PERSON>es Ireland</a>, English racing driver and engineer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Innes_Ireland\" title=\"Innes Ireland\">Innes Ireland</a>, English racing driver and engineer (b. 1930)", "links": [{"title": "Innes Ireland", "link": "https://wikipedia.org/wiki/Innes_Ireland"}]}, {"year": "1995", "text": "<PERSON>, English novelist, poet, critic (b. 1922)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, poet, critic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, poet, critic (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American actress and singer (b. 1910)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Russian animator, director, and screenwriter (b. 1905)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian animator, director, and screenwriter (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian animator, director, and screenwriter (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Austrian motorcycle racer and journalist (b. 1922)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian motorcycle racer and journalist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian motorcycle racer and journalist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American intelligence agent and diplomat, 8th Director of Central Intelligence (b. 1913)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American intelligence agent and diplomat, 8th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American intelligence agent and diplomat, 8th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Director of Central Intelligence", "link": "https://wikipedia.org/wiki/Director_of_Central_Intelligence"}]}, {"year": "2002", "text": "<PERSON><PERSON> of Albania, Hungarian noblewoman and Queen of Albania (b. 1915)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Albania\" title=\"<PERSON><PERSON> of Albania\"><PERSON><PERSON> of Albania</a>, Hungarian noblewoman and Queen of Albania (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Albania\" title=\"<PERSON><PERSON> of Albania\"><PERSON><PERSON> of Albania</a>, Hungarian noblewoman and Queen of Albania (b. 1915)", "links": [{"title": "<PERSON><PERSON> of Albania", "link": "https://wikipedia.org/wiki/Geraldine_of_Albania"}]}, {"year": "2005", "text": "<PERSON><PERSON>, French-American painter and sculptor (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American painter and sculptor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American painter and sculptor (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an"}]}, {"year": "2005", "text": "<PERSON>, Irish-American actor and producer (b. 1953)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, Irish-American actor and producer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, Irish-American actor and producer (b. 1953)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}]}, {"year": "2006", "text": "<PERSON>, Canadian-American actor (b. 1922)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_actor)\" title=\"<PERSON> (Canadian actor)\"><PERSON></a>, Canadian-American actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_actor)\" title=\"<PERSON> (Canadian actor)\"><PERSON></a>, Canadian-American actor (b. 1922)", "links": [{"title": "<PERSON> (Canadian actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_actor)"}]}, {"year": "2007", "text": "<PERSON><PERSON>, French pianist and journalist (b. 1904)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/%C3%88ve_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French pianist and journalist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%88ve_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French pianist and journalist (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%88ve_<PERSON><PERSON>e"}]}, {"year": "2009", "text": "<PERSON>, American-Australian actor, singer, and talk show host (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Don Lane\"><PERSON></a>, American-Australian actor, singer, and talk show host (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Don_Lane\" title=\"Don Lane\"><PERSON></a>, American-Australian actor, singer, and talk show host (b. 1933)", "links": [{"title": "Don Lane", "link": "https://wikipedia.org/wiki/Don_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, American comedian and actor (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Soupy_Sales\" title=\"Soupy Sales\">Soup<PERSON> <PERSON></a>, American comedian and actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soupy_Sales\" title=\"Soupy Sales\">Soupy Sales</a>, American comedian and actor (b. 1926)", "links": [{"title": "Soupy Sales", "link": "https://wikipedia.org/wiki/Soupy_Sales"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Japanese Go player (b. 1920)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/E<PERSON>_Sakata\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/Go_(game)\" title=\"Go (game)\">Go</a> player (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sa<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/Go_(game)\" title=\"Go (game)\">Go</a> player (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eio_Sakata"}, {"title": "Go (game)", "link": "https://wikipedia.org/wiki/Go_(game)"}]}, {"year": "2011", "text": "<PERSON> bin <PERSON>, Saudi Arabian prince (b. 1930)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Sultan_bin_<PERSON>azi<PERSON>\" title=\"Sultan bin Abdulaziz\">Sultan bin Abdul<PERSON></a>, Saudi Arabian prince (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sultan_bin_Abdulazi<PERSON>\" title=\"Sultan bin Abdulaziz\">Sultan bin <PERSON></a>, Saudi Arabian prince (b. 1930)", "links": [{"title": "<PERSON> bin <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_bin_<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and judge (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English talk show host (b. 1946)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_presenter)\" title=\"<PERSON> (TV presenter)\"><PERSON></a>, English talk show host (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_presenter)\" title=\"<PERSON> (TV presenter)\"><PERSON></a>, English talk show host (b. 1946)", "links": [{"title": "<PERSON> (TV presenter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_presenter)"}]}, {"year": "2012", "text": "<PERSON>, American dancer, singer, and author (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer, singer, and author (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer, singer, and author (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Canadian pianist and educator (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian pianist and educator (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian pianist and educator (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Hungarian historian and politician, Minister of Defence of Hungary (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Lajos_F%C3%BCr\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian historian and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_of_Hungary\" class=\"mw-redirect\" title=\"Minister of Defence of Hungary\">Minister of Defence of Hungary</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_F%C3%BCr\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian historian and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_of_Hungary\" class=\"mw-redirect\" title=\"Minister of Defence of Hungary\">Minister of Defence of Hungary</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lajos_F%C3%BCr"}, {"title": "Minister of Defence of Hungary", "link": "https://wikipedia.org/wiki/Minister_of_Defence_of_Hungary"}]}, {"year": "2013", "text": "<PERSON>, American author and screenwriter (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and screenwriter (b. 1933)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "2013", "text": "<PERSON>, American general and pilot (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Risner\"><PERSON></a>, American general and pilot (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Risner\"><PERSON></a>, American general and pilot (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English footballer and soldier (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and soldier (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and soldier (b. 1934)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "2014", "text": "John<PERSON><PERSON>, American religious leader and author (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American religious leader and author (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American religious leader and author (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Indian director and cinematographer (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cinematographer)\" title=\"<PERSON><PERSON> (cinematographer)\"><PERSON><PERSON></a>, Indian director and cinematographer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cinematographer)\" title=\"<PERSON><PERSON> (cinematographer)\"><PERSON><PERSON></a>, Indian director and cinematographer (b. 1941)", "links": [{"title": "<PERSON><PERSON> (cinematographer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cinematographer)"}]}, {"year": "2014", "text": "<PERSON>, English microbiologist, author, and academic (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(microbiologist)\" title=\"<PERSON> (microbiologist)\"><PERSON></a>, English microbiologist, author, and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(microbiologist)\" title=\"<PERSON> (microbiologist)\"><PERSON></a>, English microbiologist, author, and academic (b. 1922)", "links": [{"title": "<PERSON> (microbiologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(microbiologist)"}]}, {"year": "2015", "text": "<PERSON>, Dutch civil servant and politician (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch civil servant and politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch civil servant and politician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and politician (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/%C3%87etin_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%87etin_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and politician (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%87etin_Altan"}]}, {"year": "2015", "text": "<PERSON>, American illustrator (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American dermatologist and author (b. 1945)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dermatologist and author (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dermatologist and author (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American sergeant (b. 1975)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, British comic book artist (b. 1962)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British comic book artist (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British comic book artist (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, American writer (b. 1929)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Australian musician, songwriter and record producer (b. 1946)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rock_musician)\" title=\"<PERSON> (rock musician)\"><PERSON></a>, Australian musician, songwriter and record producer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rock_musician)\" title=\"<PERSON> (rock musician)\"><PERSON></a>, Australian musician, songwriter and record producer (b. 1946)", "links": [{"title": "<PERSON> (rock musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rock_musician)"}]}, {"year": "2017", "text": "<PERSON>, American astronaut (b. 1932)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actor (b. 1955)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American global health researcher (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Cash\"><PERSON></a>, American global health researcher (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American global health researcher (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "Grizzly 399, American grizzly bear (b. 1996)", "html": "2024 - <i><a href=\"https://wikipedia.org/wiki/Grizzly_399\" title=\"Grizzly 399\">Grizzly 399</a></i>, American grizzly bear (b. 1996)", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Grizzly_399\" title=\"Grizzly 399\">Grizzly 399</a></i>, American grizzly bear (b. 1996)", "links": [{"title": "Grizzly 399", "link": "https://wikipedia.org/wiki/Grizzly_399"}]}, {"year": "2024", "text": "<PERSON>, Peruvian philosopher, theologian and priest (b. 1928)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rrez\" title=\"<PERSON>\"><PERSON></a>, Peruvian philosopher, theologian and priest (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rrez\" title=\"<PERSON>\"><PERSON></a>, Peruvian philosopher, theologian and priest (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gustavo_Guti%C3%A9rrez"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, American film producer and author (b. 1950)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American film producer and author (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American film producer and author (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ynda_<PERSON><PERSON>t"}]}, {"year": "2024", "text": "<PERSON>, Mexican baseball player, coach, and sportscaster (b. 1960)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player, coach, and sportscaster (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player, coach, and sportscaster (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nzuel<PERSON>"}]}]}}