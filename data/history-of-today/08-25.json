{"date": "August 25", "url": "https://wikipedia.org/wiki/August_25", "data": {"Events": [{"year": "766", "text": "Emperor <PERSON> humiliates nineteen high-ranking officials, after discovering a plot against him. He executes the leaders, <PERSON> and his brother <PERSON><PERSON><PERSON><PERSON>.", "html": "766 - Emperor <a href=\"https://wikipedia.org/wiki/Constantine_V\" title=\"Constantine V\"><PERSON> V</a> humiliates nineteen high-ranking officials, after discovering a plot against him. He executes the leaders, <a href=\"https://wikipedia.org/wiki/Constantine_Podopagouros\" title=\"<PERSON>\"><PERSON></a> and his brother <a href=\"https://wikipedia.org/wiki/Strategios_Podopagouros\" title=\"Strategios Podopagouros\">Strategios</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_V\" title=\"Constantine V\"><PERSON> V</a> humiliates nineteen high-ranking officials, after discovering a plot against him. He executes the leaders, <a href=\"https://wikipedia.org/wiki/Constantine_Podopagouros\" title=\"<PERSON>\"><PERSON></a> and his brother <a href=\"https://wikipedia.org/wiki/Strategios_Podopagouros\" title=\"Strategios Podopagouros\">Strategios</a>.", "links": [{"title": "Constantine V", "link": "https://wikipedia.org/wiki/Constantine_V"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Constantine_Podopagouros"}, {"title": "Strategios Podopagouros", "link": "https://wikipedia.org/wiki/Strategios_Podopagouros"}]}, {"year": "1248", "text": "The Dutch city of Ommen receives city rights and fortification rights from <PERSON>, the Archbishop of Utrecht.", "html": "1248 - The Dutch city of <a href=\"https://wikipedia.org/wiki/Ommen\" title=\"Ommen\">Ommen</a> receives <a href=\"https://wikipedia.org/wiki/City_rights_in_the_Low_Countries\" title=\"City rights in the Low Countries\">city rights</a> and fortification rights from <PERSON>, the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Utrecht\" title=\"Roman Catholic Archdiocese of Utrecht\">Archbishop of Utrecht</a>.", "no_year_html": "The Dutch city of <a href=\"https://wikipedia.org/wiki/Ommen\" title=\"Ommen\">Ommen</a> receives <a href=\"https://wikipedia.org/wiki/City_rights_in_the_Low_Countries\" title=\"City rights in the Low Countries\">city rights</a> and fortification rights from <PERSON>, the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Utrecht\" title=\"Roman Catholic Archdiocese of Utrecht\">Archbishop of Utrecht</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ommen"}, {"title": "City rights in the Low Countries", "link": "https://wikipedia.org/wiki/City_rights_in_the_Low_Countries"}, {"title": "Roman Catholic Archdiocese of Utrecht", "link": "https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Utrecht"}]}, {"year": "1258", "text": "Regent <PERSON> and his brothers are killed during a coup headed by the aristocratic faction under <PERSON>, paving the way for its leader to ultimately usurp the throne of the Empire of Nicaea.", "html": "1258 - Regent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his brothers are killed during a coup headed by the aristocratic faction under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, paving the way for its leader to ultimately usurp the throne of the <a href=\"https://wikipedia.org/wiki/Empire_of_Nicaea\" title=\"Empire of Nicaea\">Empire of Nicaea</a>.", "no_year_html": "Regent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his brothers are killed during a coup headed by the aristocratic faction under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, paving the way for its leader to ultimately usurp the throne of the <a href=\"https://wikipedia.org/wiki/Empire_of_Nicaea\" title=\"Empire of Nicaea\">Empire of Nicaea</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Empire of Nicaea", "link": "https://wikipedia.org/wiki/Empire_of_Nicaea"}]}, {"year": "1270", "text": "<PERSON>, although suffering from dysentery, becomes King of France following the death of his father <PERSON>, during the Eighth Crusade. His uncle, <PERSON> of Naples, is forced to begin peace negotiations with <PERSON>, <PERSON>fsid Sultan of Tunis.", "html": "1270 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> III</a>, although suffering from dysentery, becomes King of France following the death of his father <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IX of France\"><PERSON> IX</a>, during the <a href=\"https://wikipedia.org/wiki/Eighth_Crusade\" title=\"Eighth Crusade\">Eighth Crusade</a>. His uncle, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" class=\"mw-redirect\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a>, is forced to begin peace negotiations with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Muhammad <PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> of Tunis.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> III</a>, although suffering from dysentery, becomes King of France following the death of his father <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> IX</a>, during the <a href=\"https://wikipedia.org/wiki/Eighth_Crusade\" title=\"Eighth Crusade\">Eighth Crusade</a>. His uncle, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" class=\"mw-redirect\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a>, is forced to begin peace negotiations with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> of Tunis.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IX_of_France"}, {"title": "Eighth Crusade", "link": "https://wikipedia.org/wiki/Eighth_Crusade"}, {"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Naples"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1537", "text": "The Honourable Artillery Company, the oldest surviving regiment in the British Army, and the second most senior, is formed.", "html": "1537 - The <a href=\"https://wikipedia.org/wiki/Honourable_Artillery_Company\" title=\"Honourable Artillery Company\">Honourable Artillery Company</a>, the oldest surviving <a href=\"https://wikipedia.org/wiki/Regiment\" title=\"Regiment\">regiment</a> in the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>, and the second most senior, is formed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Honourable_Artillery_Company\" title=\"Honourable Artillery Company\">Honourable Artillery Company</a>, the oldest surviving <a href=\"https://wikipedia.org/wiki/Regiment\" title=\"Regiment\">regiment</a> in the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>, and the second most senior, is formed.", "links": [{"title": "Honourable Artillery Company", "link": "https://wikipedia.org/wiki/Honourable_Artillery_Company"}, {"title": "Regiment", "link": "https://wikipedia.org/wiki/Regiment"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}]}, {"year": "1543", "text": "<PERSON><PERSON><PERSON><PERSON> and a few companions become the first Europeans to visit Japan.", "html": "1543 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and a few companions become the first Europeans to visit Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and a few companions become the first Europeans to visit Japan.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_<PERSON>ta"}]}, {"year": "1580", "text": "War of the Portuguese Succession: Spanish victory at the Battle of Alcântara brings about the Iberian Union.", "html": "1580 - <a href=\"https://wikipedia.org/wiki/War_of_the_Portuguese_Succession\" title=\"War of the Portuguese Succession\">War of the Portuguese Succession</a>: Spanish victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_Alc%C3%A2ntara_(1580)\" title=\"Battle of Alcântara (1580)\">Battle of Alcântara</a> brings about the <a href=\"https://wikipedia.org/wiki/Iberian_Union\" title=\"Iberian Union\">Iberian Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Portuguese_Succession\" title=\"War of the Portuguese Succession\">War of the Portuguese Succession</a>: Spanish victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_Alc%C3%A2ntara_(1580)\" title=\"Battle of Alcântara (1580)\">Battle of Alcântara</a> brings about the <a href=\"https://wikipedia.org/wiki/Iberian_Union\" title=\"Iberian Union\">Iberian Union</a>.", "links": [{"title": "War of the Portuguese Succession", "link": "https://wikipedia.org/wiki/War_of_the_Portuguese_Succession"}, {"title": "Battle of Alcântara (1580)", "link": "https://wikipedia.org/wiki/Battle_of_Alc%C3%A2ntara_(1580)"}, {"title": "Iberian Union", "link": "https://wikipedia.org/wiki/Iberian_Union"}]}, {"year": "1609", "text": "<PERSON> demonstrates his first telescope to Venetian lawmakers.", "html": "1609 - <a href=\"https://wikipedia.org/wiki/Galileo_Galilei\" title=\"Galileo Galilei\"><PERSON></a> demonstrates his first telescope to <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetian</a> lawmakers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Galilei\" title=\"Galileo Galilei\"><PERSON></a> demonstrates his first telescope to <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetian</a> lawmakers.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gali<PERSON>i"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}]}, {"year": "1630", "text": "Portuguese forces are defeated by the Kingdom of Kandy at the Battle of Randeniwela in Sri Lanka.", "html": "1630 - Portuguese forces are defeated by the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kandy\" title=\"Kingdom of Kandy\">Kingdom of Kandy</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Randeniwela\" title=\"Battle of Randeniwela\">Battle of Randeniwela</a> in <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>.", "no_year_html": "Portuguese forces are defeated by the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kandy\" title=\"Kingdom of Kandy\">Kingdom of Kandy</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Randeniwela\" title=\"Battle of Randeniwela\">Battle of Randeniwela</a> in <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>.", "links": [{"title": "Kingdom of Kandy", "link": "https://wikipedia.org/wiki/Kingdom_of_Kandy"}, {"title": "Battle of Randeniwela", "link": "https://wikipedia.org/wiki/Battle_of_Randeniwela"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}]}, {"year": "1758", "text": "Seven Years' War: <PERSON> of <PERSON> defeats the Russian army at the Battle of Zorndorf.", "html": "1758 - <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Prussia\" class=\"mw-redirect\" title=\"Frederick II of Prussia\"><PERSON> of Prussia</a> defeats the Russian army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Zorndorf\" title=\"Battle of Zorndorf\">Battle of Zorndorf</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Prussia\" class=\"mw-redirect\" title=\"Frederick II of Prussia\"><PERSON> of Prussia</a> defeats the Russian army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Zorndorf\" title=\"Battle of Zorndorf\">Battle of Zorndorf</a>.", "links": [{"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Frederick_II_of_Prussia"}, {"title": "Battle of Zorndorf", "link": "https://wikipedia.org/wiki/Battle_of_Zorndorf"}]}, {"year": "1814", "text": "War of 1812: On the second day of the Burning of Washington, British troops torch the Library of Congress, United States Treasury, Department of War, and other public buildings.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: On the second day of the <a href=\"https://wikipedia.org/wiki/Burning_of_Washington\" title=\"Burning of Washington\">Burning of Washington</a>, British troops torch the <a href=\"https://wikipedia.org/wiki/Library_of_Congress\" title=\"Library of Congress\">Library of Congress</a>, <a href=\"https://wikipedia.org/wiki/United_States_Treasury\" class=\"mw-redirect\" title=\"United States Treasury\">United States Treasury</a>, <a href=\"https://wikipedia.org/wiki/United_States_Department_of_War\" title=\"United States Department of War\">Department of War</a>, and other public buildings.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: On the second day of the <a href=\"https://wikipedia.org/wiki/Burning_of_Washington\" title=\"Burning of Washington\">Burning of Washington</a>, British troops torch the <a href=\"https://wikipedia.org/wiki/Library_of_Congress\" title=\"Library of Congress\">Library of Congress</a>, <a href=\"https://wikipedia.org/wiki/United_States_Treasury\" class=\"mw-redirect\" title=\"United States Treasury\">United States Treasury</a>, <a href=\"https://wikipedia.org/wiki/United_States_Department_of_War\" title=\"United States Department of War\">Department of War</a>, and other public buildings.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "Burning of Washington", "link": "https://wikipedia.org/wiki/Burning_of_Washington"}, {"title": "Library of Congress", "link": "https://wikipedia.org/wiki/Library_of_Congress"}, {"title": "United States Treasury", "link": "https://wikipedia.org/wiki/United_States_Treasury"}, {"title": "United States Department of War", "link": "https://wikipedia.org/wiki/United_States_Department_of_War"}]}, {"year": "1823", "text": "American fur trapper <PERSON> is mauled by a grizzly bear while on an expedition in South Dakota.", "html": "1823 - American fur trapper <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is mauled by a <a href=\"https://wikipedia.org/wiki/Grizzly_bear\" title=\"Grizzly bear\">grizzly bear</a> while on an expedition in <a href=\"https://wikipedia.org/wiki/South_Dakota\" title=\"South Dakota\">South Dakota</a>.", "no_year_html": "American fur trapper <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is mauled by a <a href=\"https://wikipedia.org/wiki/Grizzly_bear\" title=\"Grizzly bear\">grizzly bear</a> while on an expedition in <a href=\"https://wikipedia.org/wiki/South_Dakota\" title=\"South Dakota\">South Dakota</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Grizzly bear", "link": "https://wikipedia.org/wiki/Grizzly_bear"}, {"title": "South Dakota", "link": "https://wikipedia.org/wiki/South_Dakota"}]}, {"year": "1825", "text": "The Thirty-Three Orientals declare the independence of Uruguay from Brazil.", "html": "1825 - The <a href=\"https://wikipedia.org/wiki/Thirty-Three_Orientals\" title=\"Thirty-Three Orientals\">Thirty-Three Orientals</a> declare the independence of Uruguay from Brazil.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Thirty-Three_Orientals\" title=\"Thirty-Three Orientals\">Thirty-Three Orientals</a> declare the independence of Uruguay from Brazil.", "links": [{"title": "Thirty-Three Orientals", "link": "https://wikipedia.org/wiki/Thirty-Three_Orientals"}]}, {"year": "1830", "text": "The Belgian Revolution begins.", "html": "1830 - The <a href=\"https://wikipedia.org/wiki/Belgian_Revolution\" title=\"Belgian Revolution\">Belgian Revolution</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Belgian_Revolution\" title=\"Belgian Revolution\">Belgian Revolution</a> begins.", "links": [{"title": "Belgian Revolution", "link": "https://wikipedia.org/wiki/Belgian_Revolution"}]}, {"year": "1835", "text": "The first Great Moon Hoax article is published in The New York Sun, announcing the discovery of life and civilization on the Moon.", "html": "1835 - The first <a href=\"https://wikipedia.org/wiki/Great_Moon_Hoax\" title=\"Great Moon Hoax\">Great Moon Hoax</a> article is published in <i><a href=\"https://wikipedia.org/wiki/The_Sun_(New_York)\" class=\"mw-redirect\" title=\"The Sun (New York)\">The New York Sun</a></i>, announcing the discovery of life and civilization on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Great_Moon_Hoax\" title=\"Great Moon Hoax\">Great Moon Hoax</a> article is published in <i><a href=\"https://wikipedia.org/wiki/The_Sun_(New_York)\" class=\"mw-redirect\" title=\"The Sun (New York)\">The New York Sun</a></i>, announcing the discovery of life and civilization on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "links": [{"title": "Great Moon Hoax", "link": "https://wikipedia.org/wiki/Great_Moon_Hoax"}, {"title": "The Sun (New York)", "link": "https://wikipedia.org/wiki/The_Sun_(New_York)"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}]}, {"year": "1875", "text": "Captain <PERSON> becomes the first person to swim across the English Channel, traveling from Dover, England, to Calais, France, in 21 hours and 45 minutes.", "html": "1875 - Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to swim across the English Channel, traveling from Dover, England, to Calais, France, in 21 hours and 45 minutes.", "no_year_html": "Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to swim across the English Channel, traveling from Dover, England, to Calais, France, in 21 hours and 45 minutes.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1883", "text": "France and Viet Nam sign the Treaty of Huế, recognizing a French protectorate over Annam and Tonkin.", "html": "1883 - <a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">France</a> and <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty\" title=\"Nguyễn dynasty\">Viet Nam</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Hu%E1%BA%BF_(1883)\" title=\"Treaty of Huế (1883)\">Treaty of Huế</a>, recognizing a French protectorate over Annam and Tonkin.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Third_Republic\" title=\"French Third Republic\">France</a> and <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty\" title=\"Nguyễn dynasty\">Viet Nam</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Hu%E1%BA%BF_(1883)\" title=\"Treaty of Huế (1883)\">Treaty of Huế</a>, recognizing a French protectorate over Annam and Tonkin.", "links": [{"title": "French Third Republic", "link": "https://wikipedia.org/wiki/French_Third_Republic"}, {"title": "Nguyễn dynasty", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty"}, {"title": "Treaty of Huế (1883)", "link": "https://wikipedia.org/wiki/Treaty_of_Hu%E1%BA%BF_(1883)"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON> discovers the infectious agent of the bubonic plague and publishes his findings in The Lancet.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Kitasato_Shibasabur%C5%8D\" title=\"Kitasato Shibasaburō\"><PERSON><PERSON><PERSON></a> discovers the infectious agent of the <a href=\"https://wikipedia.org/wiki/Bubonic_plague\" title=\"Bubonic plague\">bubonic plague</a> and publishes his findings in <i><a href=\"https://wikipedia.org/wiki/The_Lancet\" title=\"The Lancet\">The Lancet</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kitasato_Shi<PERSON>abur%C5%8D\" title=\"Kitasato Shibasaburō\"><PERSON><PERSON><PERSON></a> discovers the infectious agent of the <a href=\"https://wikipedia.org/wiki/Bubonic_plague\" title=\"Bubonic plague\">bubonic plague</a> and publishes his findings in <i><a href=\"https://wikipedia.org/wiki/The_Lancet\" title=\"The Lancet\">The Lancet</a></i>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kitasato_Shibasabur%C5%8D"}, {"title": "Bubonic plague", "link": "https://wikipedia.org/wiki/Bubonic_plague"}, {"title": "The Lancet", "link": "https://wikipedia.org/wiki/The_Lancet"}]}, {"year": "1904", "text": "Russo-Japanese War: The Battle of Liaoyang begins.", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Liaoyang\" title=\"Battle of Liaoyang\">Battle of Liaoyang</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Liaoyang\" title=\"Battle of Liaoyang\">Battle of Liaoyang</a> begins.", "links": [{"title": "Russo-Japanese War", "link": "https://wikipedia.org/wiki/Russo-Japanese_War"}, {"title": "Battle of Liaoyang", "link": "https://wikipedia.org/wiki/Battle_of_Liaoyang"}]}, {"year": "1912", "text": "The Kuomintang is founded for the first time in Peking.", "html": "1912 - The <a href=\"https://wikipedia.org/wiki/Kuomintang#Founding_and_Sun_Yat-sen_era\" title=\"Kuomintang\">Kuomintang</a> is founded for the first time in <a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Peking</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kuomintang#Founding_and_Sun_Yat-sen_era\" title=\"Kuomintang\">Kuomintang</a> is founded for the first time in <a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Peking</a>.", "links": [{"title": "Kuomintang", "link": "https://wikipedia.org/wiki/Kuomintang#Founding_and_Sun_Yat-sen_era"}, {"title": "Beijing", "link": "https://wikipedia.org/wiki/Beijing"}]}, {"year": "1914", "text": "World War I: Japan declares war on Austria-Hungary.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a> declares war on <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a> declares war on <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}]}, {"year": "1914", "text": "World War I: The library of the Catholic University of Leuven is deliberately destroyed by the German Army. Hundreds of thousands of irreplaceable volumes and Gothic and Renaissance manuscripts are lost.", "html": "1914 - World War I: The library of the Catholic University of Leuven is <a href=\"https://wikipedia.org/wiki/Sack_of_Louvain\" title=\"Sack of Louvain\">deliberately destroyed</a> by the German Army. Hundreds of thousands of irreplaceable volumes and Gothic and Renaissance manuscripts are lost.", "no_year_html": "World War I: The library of the Catholic University of Leuven is <a href=\"https://wikipedia.org/wiki/Sack_of_Louvain\" title=\"Sack of Louvain\">deliberately destroyed</a> by the German Army. Hundreds of thousands of irreplaceable volumes and Gothic and Renaissance manuscripts are lost.", "links": [{"title": "Sack of Louvain", "link": "https://wikipedia.org/wiki/Sack_of_<PERSON><PERSON><PERSON>"}]}, {"year": "1916", "text": "The United States National Park Service is created.", "html": "1916 - The United States <a href=\"https://wikipedia.org/wiki/National_Park_Service\" title=\"National Park Service\">National Park Service</a> is created.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/National_Park_Service\" title=\"National Park Service\">National Park Service</a> is created.", "links": [{"title": "National Park Service", "link": "https://wikipedia.org/wiki/National_Park_Service"}]}, {"year": "1920", "text": "Polish-Soviet War: Battle of Warsaw, which began on August 13, ends with the Red Army's defeat.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1920)\" title=\"Battle of Warsaw (1920)\">Battle of Warsaw</a>, which began on <a href=\"https://wikipedia.org/wiki/August_13\" title=\"August 13\">August 13</a>, ends with the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>'s defeat.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1920)\" title=\"Battle of Warsaw (1920)\">Battle of Warsaw</a>, which began on <a href=\"https://wikipedia.org/wiki/August_13\" title=\"August 13\">August 13</a>, ends with the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>'s defeat.", "links": [{"title": "Polish-Soviet War", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War"}, {"title": "Battle of Warsaw (1920)", "link": "https://wikipedia.org/wiki/Battle_of_Warsaw_(1920)"}, {"title": "August 13", "link": "https://wikipedia.org/wiki/August_13"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1933", "text": "The Diexi earthquake strikes Mao County, Sichuan, China and kills 9,000 people.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/1933_Diexi_earthquake\" title=\"1933 Diexi earthquake\">Diexi earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Mao_County\" title=\"Mao County\">Mao County</a>, <a href=\"https://wikipedia.org/wiki/Sichuan\" title=\"Sichuan\">Sichuan</a>, China and kills 9,000 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1933_Diexi_earthquake\" title=\"1933 Diexi earthquake\">Diexi earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Mao_County\" title=\"Mao County\">Mao County</a>, <a href=\"https://wikipedia.org/wiki/Sichuan\" title=\"Sichuan\">Sichuan</a>, China and kills 9,000 people.", "links": [{"title": "1933 Diexi earthquake", "link": "https://wikipedia.org/wiki/1933_Diexi_earthquake"}, {"title": "Mao County", "link": "https://wikipedia.org/wiki/Mao_County"}, {"title": "Sichuan", "link": "https://wikipedia.org/wiki/Sichuan"}]}, {"year": "1933", "text": "Nazi Germany and the Zionist Federation of Germany signed the Haavara Agreement. The agreement was a major factor in breaking the anti-Nazi boycott of 1933 and facilitated Jewish emigration from Germany and into Palestine.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> and the <a href=\"https://wikipedia.org/wiki/Zionist_Federation_of_Germany\" title=\"Zionist Federation of Germany\">Zionist Federation of Germany</a> signed the <a href=\"https://wikipedia.org/wiki/Haavara_Agreement\" title=\"Haavara Agreement\">Haavara Agreement</a>. The agreement was a major factor in breaking the <a href=\"https://wikipedia.org/wiki/1933_anti-Nazi_boycott\" title=\"1933 anti-Nazi boycott\">anti-Nazi boycott of 1933</a> and facilitated Jewish emigration from Germany and into <a href=\"https://wikipedia.org/wiki/Palestine\" title=\"Palestine\">Palestine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> and the <a href=\"https://wikipedia.org/wiki/Zionist_Federation_of_Germany\" title=\"Zionist Federation of Germany\">Zionist Federation of Germany</a> signed the <a href=\"https://wikipedia.org/wiki/Haavara_Agreement\" title=\"Haavara Agreement\">Haavara Agreement</a>. The agreement was a major factor in breaking the <a href=\"https://wikipedia.org/wiki/1933_anti-Nazi_boycott\" title=\"1933 anti-Nazi boycott\">anti-Nazi boycott of 1933</a> and facilitated Jewish emigration from Germany and into <a href=\"https://wikipedia.org/wiki/Palestine\" title=\"Palestine\">Palestine</a>.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Zionist Federation of Germany", "link": "https://wikipedia.org/wiki/Zionist_Federation_of_Germany"}, {"title": "Haavara Agreement", "link": "https://wikipedia.org/wiki/Haavara_Agreement"}, {"title": "1933 anti-Nazi boycott", "link": "https://wikipedia.org/wiki/1933_anti-Nazi_boycott"}, {"title": "Palestine", "link": "https://wikipedia.org/wiki/Palestine"}]}, {"year": "1939", "text": "The Irish Republican Army carries out the 1939 Coventry bombing in which five civilians were killed.", "html": "1939 - The <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> carries out the <a href=\"https://wikipedia.org/wiki/1939_Coventry_bombing\" title=\"1939 Coventry bombing\">1939 Coventry bombing</a> in which five civilians were killed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> carries out the <a href=\"https://wikipedia.org/wiki/1939_Coventry_bombing\" title=\"1939 Coventry bombing\">1939 Coventry bombing</a> in which five civilians were killed.", "links": [{"title": "Irish Republican Army", "link": "https://wikipedia.org/wiki/Irish_Republican_Army"}, {"title": "1939 Coventry bombing", "link": "https://wikipedia.org/wiki/1939_Coventry_bombing"}]}, {"year": "1939", "text": "The United Kingdom and Poland form a military alliance in which the UK promises to defend Poland in case of invasion by a foreign power.", "html": "1939 - The United Kingdom and <a href=\"https://wikipedia.org/wiki/Second_Polish_Republic\" title=\"Second Polish Republic\">Poland</a> form a <a href=\"https://wikipedia.org/wiki/Anglo-Polish_military_alliance\" class=\"mw-redirect\" title=\"Anglo-Polish military alliance\">military alliance</a> in which the UK promises to defend Poland in case of invasion by a foreign power.", "no_year_html": "The United Kingdom and <a href=\"https://wikipedia.org/wiki/Second_Polish_Republic\" title=\"Second Polish Republic\">Poland</a> form a <a href=\"https://wikipedia.org/wiki/Anglo-Polish_military_alliance\" class=\"mw-redirect\" title=\"Anglo-Polish military alliance\">military alliance</a> in which the UK promises to defend Poland in case of invasion by a foreign power.", "links": [{"title": "Second Polish Republic", "link": "https://wikipedia.org/wiki/Second_Polish_Republic"}, {"title": "Anglo-Polish military alliance", "link": "https://wikipedia.org/wiki/Anglo-Polish_military_alliance"}]}, {"year": "1940", "text": "World War II: The first Bombing of Berlin by the British Royal Air Force.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The first <a href=\"https://wikipedia.org/wiki/Bombing_of_Berlin_in_World_War_II\" title=\"Bombing of Berlin in World War II\">Bombing of Berlin</a> by the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">British</a> <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The first <a href=\"https://wikipedia.org/wiki/Bombing_of_Berlin_in_World_War_II\" title=\"Bombing of Berlin in World War II\">Bombing of Berlin</a> by the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">British</a> <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Bombing of Berlin in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Berlin_in_World_War_II"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}]}, {"year": "1941", "text": "World War II: Anglo-Soviet invasion of Iran: The United Kingdom and the Soviet Union jointly stage an invasion of the Imperial State of Iran.", "html": "1941 - World War II: <a href=\"https://wikipedia.org/wiki/Anglo-Soviet_invasion_of_Iran\" title=\"Anglo-Soviet invasion of Iran\">Anglo-Soviet invasion of Iran</a>: The <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> jointly stage an invasion of the <a href=\"https://wikipedia.org/wiki/Pahlavi_Iran\" title=\"Pahlavi Iran\">Imperial State of Iran</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Anglo-Soviet_invasion_of_Iran\" title=\"Anglo-Soviet invasion of Iran\">Anglo-Soviet invasion of Iran</a>: The <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> jointly stage an invasion of the <a href=\"https://wikipedia.org/wiki/Pahlavi_Iran\" title=\"Pahlavi Iran\">Imperial State of Iran</a>.", "links": [{"title": "Anglo-Soviet invasion of Iran", "link": "https://wikipedia.org/wiki/Anglo-Soviet_invasion_of_Iran"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Pahlavi Iran", "link": "https://wikipedia.org/wiki/Pahlavi_Iran"}]}, {"year": "1942", "text": "World War II: Second day of the Battle of the Eastern Solomons; a Japanese naval transport convoy headed towards Guadalcanal is turned back by an Allied air attack.", "html": "1942 - World War II: Second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Eastern_Solomons\" title=\"Battle of the Eastern Solomons\">Battle of the Eastern Solomons</a>; a <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> naval transport convoy headed towards <a href=\"https://wikipedia.org/wiki/Guadalcanal\" title=\"Guadalcanal\">Guadalcanal</a> is turned back by an <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> air attack.", "no_year_html": "World War II: Second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Eastern_Solomons\" title=\"Battle of the Eastern Solomons\">Battle of the Eastern Solomons</a>; a <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> naval transport convoy headed towards <a href=\"https://wikipedia.org/wiki/Guadalcanal\" title=\"Guadalcanal\">Guadalcanal</a> is turned back by an <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> air attack.", "links": [{"title": "Battle of the Eastern Solomons", "link": "https://wikipedia.org/wiki/Battle_of_the_Eastern_Solomons"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Guadalcanal", "link": "https://wikipedia.org/wiki/Guadalcanal"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}]}, {"year": "1942", "text": "World War II: Battle of Milne Bay: Japanese marines assault Allied airfields at Milne Bay, New Guinea, initiating the Battle of Milne Bay.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Milne_Bay\" title=\"Battle of Milne Bay\">Battle of Milne Bay</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> marines assault <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> airfields at <a href=\"https://wikipedia.org/wiki/Milne_Bay\" title=\"Milne Bay\">Milne Bay</a>, <a href=\"https://wikipedia.org/wiki/New_Guinea\" title=\"New Guinea\">New Guinea</a>, initiating the <a href=\"https://wikipedia.org/wiki/Battle_of_Milne_Bay\" title=\"Battle of Milne Bay\">Battle of Milne Bay</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Milne_Bay\" title=\"Battle of Milne Bay\">Battle of Milne Bay</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> marines assault <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> airfields at <a href=\"https://wikipedia.org/wiki/Milne_Bay\" title=\"Milne Bay\">Milne Bay</a>, <a href=\"https://wikipedia.org/wiki/New_Guinea\" title=\"New Guinea\">New Guinea</a>, initiating the <a href=\"https://wikipedia.org/wiki/Battle_of_Milne_Bay\" title=\"Battle of Milne Bay\">Battle of Milne Bay</a>.", "links": [{"title": "Battle of Milne Bay", "link": "https://wikipedia.org/wiki/Battle_of_Milne_Bay"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "Milne Bay", "link": "https://wikipedia.org/wiki/Milne_Bay"}, {"title": "New Guinea", "link": "https://wikipedia.org/wiki/New_Guinea"}, {"title": "Battle of Milne Bay", "link": "https://wikipedia.org/wiki/Battle_of_Milne_Bay"}]}, {"year": "1944", "text": "World War II: Paris is liberated by the Allies.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Liberation_of_Paris\" title=\"Liberation of Paris\">Paris is liberated</a> by the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allies</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Liberation_of_Paris\" title=\"Liberation of Paris\">Paris is liberated</a> by the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allies</a>.", "links": [{"title": "Liberation of Paris", "link": "https://wikipedia.org/wiki/Liberation_of_Paris"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}]}, {"year": "1945", "text": "Ten days after World War II ends with Japan announcing its surrender, armed supporters of the Chinese Communist Party kill U.S. intelligence officer <PERSON>, regarded by some of the American right as the first victim of the Cold War.", "html": "1945 - Ten days after World War II ends with Japan <a href=\"https://wikipedia.org/wiki/Victory_over_Japan_Day\" title=\"Victory over Japan Day\">announcing its surrender</a>, armed supporters of the <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> kill U.S. intelligence officer <a href=\"https://wikipedia.org/wiki/<PERSON>(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, regarded by some of the <a href=\"https://wikipedia.org/wiki/Conservatism_in_the_United_States\" title=\"Conservatism in the United States\">American right</a> as the first victim of the <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>.", "no_year_html": "Ten days after World War II ends with Japan <a href=\"https://wikipedia.org/wiki/Victory_over_Japan_Day\" title=\"Victory over Japan Day\">announcing its surrender</a>, armed supporters of the <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> kill U.S. intelligence officer <a href=\"https://wikipedia.org/wiki/<PERSON>(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, regarded by some of the <a href=\"https://wikipedia.org/wiki/Conservatism_in_the_United_States\" title=\"Conservatism in the United States\">American right</a> as the first victim of the <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>.", "links": [{"title": "Victory over Japan Day", "link": "https://wikipedia.org/wiki/Victory_over_Japan_Day"}, {"title": "Chinese Communist Party", "link": "https://wikipedia.org/wiki/Chinese_Communist_Party"}, {"title": "<PERSON> (missionary)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(missionary)"}, {"title": "Conservatism in the United States", "link": "https://wikipedia.org/wiki/Conservatism_in_the_United_States"}, {"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}]}, {"year": "1945", "text": "The August Revolution ends as Emperor <PERSON><PERSON><PERSON> abdicates, ending the Nguyễn dynasty.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/August_Revolution\" title=\"August Revolution\">August Revolution</a> ends as Emperor <a href=\"https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i\" title=\"Bảo Đại\"><PERSON><PERSON><PERSON></a> abdicates, ending the <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty\" title=\"Nguyễn dynasty\">Nguyễn dynasty</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/August_Revolution\" title=\"August Revolution\">August Revolution</a> ends as Emperor <a href=\"https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i\" title=\"Bảo Đại\"><PERSON><PERSON><PERSON></a> abdicates, ending the <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty\" title=\"Nguyễn dynasty\">Nguyễn dynasty</a>.", "links": [{"title": "August Revolution", "link": "https://wikipedia.org/wiki/August_Revolution"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%E1%BA%A3o_%C4%90%E1%BA%A1i"}, {"title": "Nguyễn dynasty", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty"}]}, {"year": "1948", "text": "The House Un-American Activities Committee holds first-ever televised congressional hearing: \"Confrontation Day\" between <PERSON><PERSON><PERSON> and <PERSON><PERSON>.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Un-American Activities Committee</a> holds first-ever televised congressional hearing: \"Confrontation Day\" between <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hiss\" title=\"<PERSON><PERSON> Hiss\"><PERSON><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Un-American Activities Committee</a> holds first-ever televised congressional hearing: \"Confrontation Day\" between <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hiss\" title=\"<PERSON><PERSON> Hiss\"><PERSON><PERSON></a>.", "links": [{"title": "House Un-American Activities Committee", "link": "https://wikipedia.org/wiki/House_Un-American_Activities_Committee"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hiss"}]}, {"year": "1950", "text": "To avert a threatened strike during the Korean War, President <PERSON> orders Secretary of the Army <PERSON> to seize control of the nation's railroads.", "html": "1950 - To avert a threatened strike during the Korean War, President <PERSON> orders Secretary of the Army <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to seize control of the nation's railroads.", "no_year_html": "To avert a threatened strike during the Korean War, President <PERSON> orders Secretary of the Army <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to seize control of the nation's railroads.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "The world's first publicly marketed instant noodles, Chikin Ramen, are introduced by Taiwanese-Japanese businessman <PERSON><PERSON><PERSON>.", "html": "1958 - The world's first publicly marketed <a href=\"https://wikipedia.org/wiki/Instant_noodles\" title=\"Instant noodles\">instant noodles</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Chikin_Ramen\" title=\"Nissin Chikin Ramen\"><PERSON><PERSON> Ramen</a>, are introduced by <a href=\"https://wikipedia.org/wiki/Taiwanese_people\" title=\"Taiwanese people\">Taiwanese</a>-<a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japanese</a> businessman <a href=\"https://wikipedia.org/wiki/Momofuku_Ando\" title=\"Momofuku Ando\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "The world's first publicly marketed <a href=\"https://wikipedia.org/wiki/Instant_noodles\" title=\"Instant noodles\">instant noodles</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Chikin_Ramen\" title=\"Nissin Chikin Ramen\"><PERSON><PERSON> Ramen</a>, are introduced by <a href=\"https://wikipedia.org/wiki/Taiwanese_people\" title=\"Taiwanese people\">Taiwanese</a>-<a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japanese</a> businessman <a href=\"https://wikipedia.org/wiki/Momofuku_Ando\" title=\"Momofuku Ando\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Instant noodles", "link": "https://wikipedia.org/wiki/Instant_noodles"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>en"}, {"title": "Taiwanese people", "link": "https://wikipedia.org/wiki/Taiwanese_people"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>of<PERSON>_<PERSON>o"}]}, {"year": "1960", "text": "The Games of the XVII Olympiad commence in Rome, Italy.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/1960_Summer_Olympics\" title=\"1960 Summer Olympics\">Games of the XVII Olympiad</a> commence in <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1960_Summer_Olympics\" title=\"1960 Summer Olympics\">Games of the XVII Olympiad</a> commence in <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>.", "links": [{"title": "1960 Summer Olympics", "link": "https://wikipedia.org/wiki/1960_Summer_Olympics"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}]}, {"year": "1961", "text": "President <PERSON><PERSON><PERSON> of Brazil resigns after just seven months in power, initiating a political crisis that culminates in a military coup in 1964.", "html": "1961 - President <a href=\"https://wikipedia.org/wiki/J%C3%A2nio_Quadros\" title=\"<PERSON>ânio Quadros\"><PERSON><PERSON><PERSON></a> of Brazil resigns after just seven months in power, initiating a political crisis that culminates in <a href=\"https://wikipedia.org/wiki/1964_Brazilian_coup_d%27%C3%A9tat\" title=\"1964 Brazilian coup d'état\">a military coup in 1964</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/J%C3%A2nio_Quadros\" title=\"<PERSON>ânio Quadros\"><PERSON><PERSON><PERSON></a> of Brazil resigns after just seven months in power, initiating a political crisis that culminates in <a href=\"https://wikipedia.org/wiki/1964_Brazilian_coup_d%27%C3%A9tat\" title=\"1964 Brazilian coup d'état\">a military coup in 1964</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A2nio_Quadros"}, {"title": "1964 Brazilian coup d'état", "link": "https://wikipedia.org/wiki/1964_Brazilian_coup_d%27%C3%A9tat"}]}, {"year": "1967", "text": "<PERSON>, founder of the American Nazi Party, is assassinated by a former member of his group.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/American_Nazi_Party\" title=\"American Nazi Party\">American Nazi Party</a>, is assassinated by a former member of his group.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>well\"><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/American_Nazi_Party\" title=\"American Nazi Party\">American Nazi Party</a>, is assassinated by a former member of his group.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>well"}, {"title": "American Nazi Party", "link": "https://wikipedia.org/wiki/American_Nazi_Party"}]}, {"year": "1980", "text": "Zimbabwe joins the United Nations.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a> joins the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a> joins the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Zimbabwe", "link": "https://wikipedia.org/wiki/Zimbabwe"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1981", "text": "Voyager 2 spacecraft makes its closest approach to Saturn.", "html": "1981 - <i><a href=\"https://wikipedia.org/wiki/Voyager_2\" title=\"Voyager 2\">Voyager 2</a></i> spacecraft makes its closest approach to <a href=\"https://wikipedia.org/wiki/Saturn\" title=\"Saturn\">Saturn</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Voyager_2\" title=\"Voyager 2\">Voyager 2</a></i> spacecraft makes its closest approach to <a href=\"https://wikipedia.org/wiki/Saturn\" title=\"Saturn\">Saturn</a>.", "links": [{"title": "Voyager 2", "link": "https://wikipedia.org/wiki/Voyager_2"}, {"title": "Saturn", "link": "https://wikipedia.org/wiki/Saturn"}]}, {"year": "1985", "text": "Bar Harbor Airlines Flight 1808 crashes near Auburn/Lewiston Municipal Airport in Auburn, Maine, killing all eight people on board including peace activist and child actress <PERSON>.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Bar_Harbor_Airlines_Flight_1808\" title=\"Bar Harbor Airlines Flight 1808\">Bar Harbor Airlines Flight 1808</a> crashes near <a href=\"https://wikipedia.org/wiki/Auburn/Lewiston_Municipal_Airport\" title=\"Auburn/Lewiston Municipal Airport\">Auburn/Lewiston Municipal Airport</a> in <a href=\"https://wikipedia.org/wiki/Auburn,_Maine\" title=\"Auburn, Maine\">Auburn, Maine</a>, killing all eight people on board including peace activist and child actress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bar_Harbor_Airlines_Flight_1808\" title=\"Bar Harbor Airlines Flight 1808\">Bar Harbor Airlines Flight 1808</a> crashes near <a href=\"https://wikipedia.org/wiki/Auburn/Lewiston_Municipal_Airport\" title=\"Auburn/Lewiston Municipal Airport\">Auburn/Lewiston Municipal Airport</a> in <a href=\"https://wikipedia.org/wiki/Auburn,_Maine\" title=\"Auburn, Maine\">Auburn, Maine</a>, killing all eight people on board including peace activist and child actress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Bar Harbor Airlines Flight 1808", "link": "https://wikipedia.org/wiki/Bar_Harbor_Airlines_Flight_1808"}, {"title": "Auburn/Lewiston Municipal Airport", "link": "https://wikipedia.org/wiki/Auburn/Lewiston_Municipal_Airport"}, {"title": "Auburn, Maine", "link": "https://wikipedia.org/wiki/Auburn,_Maine"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "Voyager 2 spacecraft makes its closest approach to Neptune, the last planet in the Solar System at the time, due to Pluto being within Neptune's orbit from 1979 to 1999.", "html": "1989 - <i><a href=\"https://wikipedia.org/wiki/Voyager_2\" title=\"Voyager 2\">Voyager 2</a></i> spacecraft makes its closest approach to <a href=\"https://wikipedia.org/wiki/Neptune\" title=\"Neptune\">Neptune</a>, the last planet in the <a href=\"https://wikipedia.org/wiki/Solar_System\" title=\"Solar System\">Solar System</a> at the time, due to Pluto being within Neptune's orbit from 1979 to 1999.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Voyager_2\" title=\"Voyager 2\">Voyager 2</a></i> spacecraft makes its closest approach to <a href=\"https://wikipedia.org/wiki/Neptune\" title=\"Neptune\">Neptune</a>, the last planet in the <a href=\"https://wikipedia.org/wiki/Solar_System\" title=\"Solar System\">Solar System</a> at the time, due to Pluto being within Neptune's orbit from 1979 to 1999.", "links": [{"title": "Voyager 2", "link": "https://wikipedia.org/wiki/Voyager_2"}, {"title": "Neptune", "link": "https://wikipedia.org/wiki/Neptune"}, {"title": "Solar System", "link": "https://wikipedia.org/wiki/Solar_System"}]}, {"year": "1989", "text": "Pakistan International Airlines Flight 404, carrying 54 people, disappears over the Himalayas after takeoff from Gilgit Airport in Pakistan. The aircraft was never found.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_404\" title=\"Pakistan International Airlines Flight 404\">Pakistan International Airlines Flight 404</a>, carrying 54 people, disappears over the <a href=\"https://wikipedia.org/wiki/Himalayas\" title=\"Himalayas\">Himalayas</a> after takeoff from <a href=\"https://wikipedia.org/wiki/Gilgit_Airport\" title=\"Gilgit Airport\">Gilgit Airport</a> in Pakistan. The aircraft was never found.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_404\" title=\"Pakistan International Airlines Flight 404\">Pakistan International Airlines Flight 404</a>, carrying 54 people, disappears over the <a href=\"https://wikipedia.org/wiki/Himalayas\" title=\"Himalayas\">Himalayas</a> after takeoff from <a href=\"https://wikipedia.org/wiki/Gilgit_Airport\" title=\"Gilgit Airport\">Gilgit Airport</a> in Pakistan. The aircraft was never found.", "links": [{"title": "Pakistan International Airlines Flight 404", "link": "https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_404"}, {"title": "Himalayas", "link": "https://wikipedia.org/wiki/Himalayas"}, {"title": "Gilgit Airport", "link": "https://wikipedia.org/wiki/Gilgit_Airport"}]}, {"year": "1991", "text": "Belarus gains its independence from the Soviet Union.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a> gains its independence from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a> gains its independence from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Belarus", "link": "https://wikipedia.org/wiki/Belarus"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1991", "text": "The Battle of Vukovar begins. An 87-day siege of Vukovar by the Yugoslav People's Army (JNA), supported by various Serb paramilitary forces, between August and November 1991 (during the Croatian War of Independence).", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Vukovar\" title=\"Battle of Vukovar\">Battle of Vukovar</a> begins. An 87-day siege of Vukovar by the <a href=\"https://wikipedia.org/wiki/Yugoslav_People%27s_Army\" title=\"Yugoslav People's Army\">Yugoslav People's Army</a> (JNA), supported by various Serb paramilitary forces, between August and November 1991 (during the <a href=\"https://wikipedia.org/wiki/Croatian_War_of_Independence\" title=\"Croatian War of Independence\">Croatian War of Independence</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Vukovar\" title=\"Battle of Vukovar\">Battle of Vukovar</a> begins. An 87-day siege of Vukovar by the <a href=\"https://wikipedia.org/wiki/Yugoslav_People%27s_Army\" title=\"Yugoslav People's Army\">Yugoslav People's Army</a> (JNA), supported by various Serb paramilitary forces, between August and November 1991 (during the <a href=\"https://wikipedia.org/wiki/Croatian_War_of_Independence\" title=\"Croatian War of Independence\">Croatian War of Independence</a>).", "links": [{"title": "Battle of Vukovar", "link": "https://wikipedia.org/wiki/Battle_of_Vukovar"}, {"title": "Yugoslav People's Army", "link": "https://wikipedia.org/wiki/Yugoslav_People%27s_Army"}, {"title": "Croatian War of Independence", "link": "https://wikipedia.org/wiki/Croatian_War_of_Independence"}]}, {"year": "1991", "text": "<PERSON><PERSON> announces the first version of what will become Linux.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Torvalds\"><PERSON><PERSON></a> announces the first version of what will become <a href=\"https://wikipedia.org/wiki/Linux\" title=\"Linux\">Linux</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Torvalds\"><PERSON><PERSON></a> announces the first version of what will become <a href=\"https://wikipedia.org/wiki/Linux\" title=\"Linux\">Linux</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Linux", "link": "https://wikipedia.org/wiki/Linux"}]}, {"year": "1997", "text": "<PERSON><PERSON>, the former East German leader, is convicted of a shoot-to-kill policy at the Berlin Wall.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Egon_Krenz\" title=\"Egon Krenz\"><PERSON><PERSON></a>, the former East German leader, is convicted of a <a href=\"https://wikipedia.org/wiki/Schie%C3%9Fbe<PERSON>hl\" title=\"Schie<PERSON>be<PERSON>hl\">shoot-to-kill policy</a> at the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egon_Krenz\" title=\"Egon Krenz\"><PERSON><PERSON></a>, the former East German leader, is convicted of a <a href=\"https://wikipedia.org/wiki/Schie%C3%9Fbe<PERSON>hl\" title=\"Schie<PERSON>be<PERSON>hl\">shoot-to-kill policy</a> at the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>.", "links": [{"title": "Egon K<PERSON>z", "link": "https://wikipedia.org/wiki/Egon_Krenz"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Schie%C3%9Fbefehl"}, {"title": "Berlin Wall", "link": "https://wikipedia.org/wiki/Berlin_Wall"}]}, {"year": "2001", "text": "American singer <PERSON><PERSON><PERSON> and several members of her entourage are killed as their overloaded aircraft crashes shortly after takeoff from Marsh Harbour Airport, Bahamas.", "html": "2001 - American singer <a href=\"https://wikipedia.org/wiki/Aaliyah\" title=\"Aaliyah\"><PERSON><PERSON><PERSON></a> and several members of her entourage are killed as their overloaded aircraft <a href=\"https://wikipedia.org/wiki/2001_Marsh_Harbour_Cessna_402_crash\" title=\"2001 Marsh Harbour Cessna 402 crash\">crashes</a> shortly after takeoff from <a href=\"https://wikipedia.org/wiki/Marsh_Harbour_Airport\" title=\"Marsh Harbour Airport\">Marsh Harbour Airport</a>, <a href=\"https://wikipedia.org/wiki/Bahamas\" class=\"mw-redirect\" title=\"Bahamas\">Bahamas</a>.", "no_year_html": "American singer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"Aaliyah\"><PERSON><PERSON><PERSON></a> and several members of her entourage are killed as their overloaded aircraft <a href=\"https://wikipedia.org/wiki/2001_Marsh_Harbour_Cessna_402_crash\" title=\"2001 Marsh Harbour Cessna 402 crash\">crashes</a> shortly after takeoff from <a href=\"https://wikipedia.org/wiki/Marsh_Harbour_Airport\" title=\"Marsh Harbour Airport\">Marsh Harbour Airport</a>, <a href=\"https://wikipedia.org/wiki/Bahamas\" class=\"mw-redirect\" title=\"Bahamas\">Bahamas</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>yah"}, {"title": "2001 Marsh Harbour Cessna 402 crash", "link": "https://wikipedia.org/wiki/2001_Marsh_Harbour_Cessna_402_crash"}, {"title": "Marsh Harbour Airport", "link": "https://wikipedia.org/wiki/Marsh_Harbour_Airport"}, {"title": "Bahamas", "link": "https://wikipedia.org/wiki/Bahamas"}]}, {"year": "2003", "text": "NASA successfully launches the Spitzer Space Telescope into space.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> successfully launches the <a href=\"https://wikipedia.org/wiki/Spitzer_Space_Telescope\" title=\"Spitzer Space Telescope\">Spitzer Space Telescope</a> into space.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> successfully launches the <a href=\"https://wikipedia.org/wiki/Spitzer_Space_Telescope\" title=\"Spitzer Space Telescope\">Spitzer Space Telescope</a> into space.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Spitzer Space Telescope", "link": "https://wikipedia.org/wiki/Spitzer_Space_Telescope"}]}, {"year": "2005", "text": "Hurricane Katrina makes landfall in Florida.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Hurricane_Katrina\" title=\"Hurricane Katrina\">Hurricane Katrina</a> <a href=\"https://wikipedia.org/wiki/Effects_of_Hurricane_Katrina_in_Florida\" class=\"mw-redirect\" title=\"Effects of Hurricane Katrina in Florida\">makes landfall</a> in <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Katrina\" title=\"Hurricane Katrina\">Hurricane Katrina</a> <a href=\"https://wikipedia.org/wiki/Effects_of_Hurricane_Katrina_in_Florida\" class=\"mw-redirect\" title=\"Effects of Hurricane Katrina in Florida\">makes landfall</a> in <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>.", "links": [{"title": "Hurricane Katrina", "link": "https://wikipedia.org/wiki/Hurricane_Katrina"}, {"title": "Effects of Hurricane Katrina in Florida", "link": "https://wikipedia.org/wiki/Effects_of_Hurricane_Katrina_in_Florida"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}]}, {"year": "2006", "text": "Former Prime Minister of Ukraine <PERSON><PERSON><PERSON> is sentenced to nine years imprisonment for money laundering, wire fraud, and extortion.", "html": "2006 - Former Prime Minister of Ukraine <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is sentenced to nine years imprisonment for money laundering, wire fraud, and extortion.", "no_year_html": "Former Prime Minister of Ukraine <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is sentenced to nine years imprisonment for money laundering, wire fraud, and extortion.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "A Filair Let L-410 Turbolet crashes on approach to Bandundu Airport, killing 20.", "html": "2010 - A <a href=\"https://wikipedia.org/wiki/Filair\" title=\"<PERSON>lair\"><PERSON>lair</a> <a href=\"https://wikipedia.org/wiki/Let_L-410_Turbolet\" title=\"Let L-410 Turbolet\">Let L-410 Turbolet</a> <a href=\"https://wikipedia.org/wiki/2010_Filair_Let_L-410_crash\" title=\"2010 Filair Let L-410 crash\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Bandundu_Airport\" title=\"Bandundu Airport\">Bandundu Airport</a>, killing 20.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/<PERSON>lair\" title=\"<PERSON>lair\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Let_L-410_Turbolet\" title=\"Let L-410 Turbolet\">Let L-410 Turbolet</a> <a href=\"https://wikipedia.org/wiki/2010_Filair_Let_L-410_crash\" title=\"2010 Filair Let L-410 crash\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Bandundu_Airport\" title=\"Bandundu Airport\">Bandundu Airport</a>, killing 20.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Filair"}, {"title": "Let L-410 Turbolet", "link": "https://wikipedia.org/wiki/Let_L-410_Turbolet"}, {"title": "2010 Filair Let L-410 crash", "link": "https://wikipedia.org/wiki/2010_Fi<PERSON>_Let_L-410_crash"}, {"title": "Bandundu Airport", "link": "https://wikipedia.org/wiki/Bandundu_Airport"}]}, {"year": "2011", "text": "Fifty-two people are killed during an arson attack caused by members of the drug cartel Los Zetas.", "html": "2011 - Fifty-two people are killed during an <a href=\"https://wikipedia.org/wiki/2011_Monterrey_casino_attack\" title=\"2011 Monterrey casino attack\">arson attack</a> caused by members of the drug cartel <a href=\"https://wikipedia.org/wiki/Los_Zetas\" title=\"Los Zetas\">Los Zetas</a>.", "no_year_html": "Fifty-two people are killed during an <a href=\"https://wikipedia.org/wiki/2011_Monterrey_casino_attack\" title=\"2011 Monterrey casino attack\">arson attack</a> caused by members of the drug cartel <a href=\"https://wikipedia.org/wiki/Los_Zetas\" title=\"Los Zetas\">Los Zetas</a>.", "links": [{"title": "2011 Monterrey casino attack", "link": "https://wikipedia.org/wiki/2011_Monterrey_casino_attack"}, {"title": "Los Zetas", "link": "https://wikipedia.org/wiki/Los_Zetas"}]}, {"year": "2012", "text": "Voyager 1 spacecraft enters interstellar space, becoming the first man-made object to do so.", "html": "2012 - <i><a href=\"https://wikipedia.org/wiki/Voyager_1\" title=\"Voyager 1\">Voyager 1</a></i> spacecraft enters <a href=\"https://wikipedia.org/wiki/Interstellar_space\" class=\"mw-redirect\" title=\"Interstellar space\">interstellar space</a>, becoming the first man-made object to do so.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Voyager_1\" title=\"Voyager 1\">Voyager 1</a></i> spacecraft enters <a href=\"https://wikipedia.org/wiki/Interstellar_space\" class=\"mw-redirect\" title=\"Interstellar space\">interstellar space</a>, becoming the first man-made object to do so.", "links": [{"title": "Voyager 1", "link": "https://wikipedia.org/wiki/Voyager_1"}, {"title": "Interstellar space", "link": "https://wikipedia.org/wiki/Interstellar_space"}]}, {"year": "2017", "text": "Hurricane <PERSON> makes landfall in Texas as a powerful Category 4 hurricane, the strongest hurricane to make landfall in the United States since 2004.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Hurricane_Harvey\" title=\"Hurricane Harvey\">Hurricane <PERSON></a> makes landfall in Texas as a powerful <a href=\"https://wikipedia.org/wiki/Saffir%E2%80%93Simpson_scale#Category_4\" title=\"Saffir-Simpson scale\">Category 4 hurricane</a>, the strongest hurricane to make landfall in the United States since <a href=\"https://wikipedia.org/wiki/Hurricane_Charley\" title=\"Hurricane Charley\">2004</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Harvey\" title=\"Hurricane Harvey\">Hurricane <PERSON></a> makes landfall in Texas as a powerful <a href=\"https://wikipedia.org/wiki/Saffir%E2%80%93Simpson_scale#Category_4\" title=\"Saffir-Simpson scale\">Category 4 hurricane</a>, the strongest hurricane to make landfall in the United States since <a href=\"https://wikipedia.org/wiki/Hurricane_Charley\" title=\"Hurricane Charley\">2004</a>.", "links": [{"title": "Hurricane Harvey", "link": "https://wikipedia.org/wiki/Hurricane_Harvey"}, {"title": "Saffir-Simpson scale", "link": "https://wikipedia.org/wiki/Saffir%E2%80%93Simpson_scale#Category_4"}, {"title": "Hurricane Charley", "link": "https://wikipedia.org/wiki/Hurricane_Charley"}]}, {"year": "2017", "text": "Conflict in Rakhine State (2016-present): One hundred seventy people are killed in at least 26 separate attacks carried out by the Arakan Rohingya Salvation Army, leading to the governments of Myanmar and Malaysia designating the group as a terrorist organisation.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Conflict_in_Rakhine_State_(2016%E2%80%93present)\" title=\"Conflict in Rakhine State (2016-present)\">Conflict in Rakhine State (2016-present)</a>: One hundred seventy people are killed in at least 26 separate attacks carried out by the <a href=\"https://wikipedia.org/wiki/Arakan_Rohingya_Salvation_Army\" title=\"Arakan Rohingya Salvation Army\">Arakan Rohingya Salvation Army</a>, leading to the governments of <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a> and <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> designating the group as a terrorist organisation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Conflict_in_Rakhine_State_(2016%E2%80%93present)\" title=\"Conflict in Rakhine State (2016-present)\">Conflict in Rakhine State (2016-present)</a>: One hundred seventy people are killed in at least 26 separate attacks carried out by the <a href=\"https://wikipedia.org/wiki/Arakan_Rohingya_Salvation_Army\" title=\"Arakan Rohingya Salvation Army\">Arakan Rohingya Salvation Army</a>, leading to the governments of <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a> and <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> designating the group as a terrorist organisation.", "links": [{"title": "Conflict in Rakhine State (2016-present)", "link": "https://wikipedia.org/wiki/Conflict_in_Rakhine_State_(2016%E2%80%93present)"}, {"title": "Arakan Rohingya Salvation Army", "link": "https://wikipedia.org/wiki/Arakan_Rohingya_Salvation_Army"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}]}], "Births": [{"year": "1467", "text": "<PERSON>, 2nd Duke of Alburquerque, Spanish duke (d. 1526)", "html": "1467 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fern%C3%<PERSON><PERSON><PERSON>_<PERSON>_la_Cueva,_2nd_Duke_of_Alburquerque\" title=\"<PERSON>, 2nd Duke of Alburquerque\"><PERSON>, 2nd Duke of Alburquerque</a>, Spanish duke (d. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_la_Cueva,_2nd_Duke_of_Alburquerque\" title=\"<PERSON>, 2nd Duke of Alburquerque\"><PERSON>, 2nd Duke of Alburquerque</a>, Spanish duke (d. 1526)", "links": [{"title": "<PERSON>, 2nd Duke of Alburquerque", "link": "https://wikipedia.org/wiki/Francisco_Fern%C3%<PERSON><PERSON><PERSON>_<PERSON>_la_Cueva,_2nd_Duke_of_Alburquerque"}]}, {"year": "1491", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian cardinal (d. 1550)", "html": "1491 - <a href=\"https://wikipedia.org/wiki/Innocenzo_Cybo\" title=\"Innocenzo Cybo\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Innocenzo_Cybo\" title=\"Innocenzo Cybo\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1550)", "links": [{"title": "Innocenzo <PERSON>", "link": "https://wikipedia.org/wiki/Innocenzo_Cybo"}]}, {"year": "1509", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Italian cardinal and statesman (d. 1572)", "html": "1509 - <a href=\"https://wikipedia.org/wiki/Ippolito_II_d%27Este\" title=\"Ippolito II d'Este\"><PERSON><PERSON><PERSON><PERSON> <PERSON> d'Este</a>, Italian cardinal and statesman (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ippolito_II_d%27Este\" title=\"Ippolito II d'Este\"><PERSON><PERSON><PERSON><PERSON> <PERSON> d'Este</a>, Italian cardinal and statesman (d. 1572)", "links": [{"title": "Ippolito II d'Este", "link": "https://wikipedia.org/wiki/Ippolito_II_d%27Este"}]}, {"year": "1530", "text": "<PERSON> the Terrible, Russian ruler (d. 1584)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Terrible\" title=\"<PERSON> the Terrible\"><PERSON> the Terrible</a>, Russian ruler (d. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Terrible\" title=\"<PERSON> the Terrible\"><PERSON> the Terrible</a>, Russian ruler (d. 1584)", "links": [{"title": "<PERSON> the Terrible", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1540", "text": "Lady <PERSON>, English noblewoman (d. 1568)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Lady <PERSON>\">Lady <PERSON></a>, English noblewoman (d. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Lady <PERSON>\">Lady <PERSON></a>, English noblewoman (d. 1568)", "links": [{"title": "Lady <PERSON>", "link": "https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>"}]}, {"year": "1561", "text": "<PERSON>, Dutch astronomer and mathematician (d. 1632)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and mathematician (d. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and mathematician (d. 1632)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON>, Count of Hanau-Münzenberg, German noble (d. 1638)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-M%C3%BCnzenberg\" title=\"<PERSON>, Count of Hanau-Münzenberg\"><PERSON>, Count of Hanau-Münzenberg</a>, German noble (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-M%C3%BCnzenberg\" title=\"<PERSON>, Count of Hanau-Münzenberg\"><PERSON>, Count of Hanau-Münzenberg</a>, German noble (d. 1638)", "links": [{"title": "<PERSON>, Count of Hanau-Münzenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-M%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1624", "text": "<PERSON>, French priest (d. 1709)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_de_la_Chaise\" title=\"<PERSON>\"><PERSON></a>, French priest (d. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_de_la_Chaise\" title=\"<PERSON>\"><PERSON></a>, French priest (d. 1709)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_de_la_Chaise"}]}, {"year": "1662", "text": "<PERSON> the Younger, American lawyer, academic, and politician (d. 1724)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the <PERSON>\"><PERSON> the Younger</a>, American lawyer, academic, and politician (d. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, American lawyer, academic, and politician (d. 1724)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1707", "text": "<PERSON> of Spain (d. 1724)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> I of Spain\"><PERSON> of Spain</a> (d. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (d. 1724)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Louis_<PERSON>_of_Spain"}]}, {"year": "1724", "text": "<PERSON>, English painter and academic (d. 1806)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (d. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1741", "text": "<PERSON>, German theologian and author (d. 1792)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON>, German poet, philosopher, and critic (d. 1803)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, philosopher, and critic (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, philosopher, and critic (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON>, Austrian organist and composer (d. 1810)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (d. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1767", "text": "<PERSON>, French soldier and politician (d. 1794)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>Just\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>Just\" title=\"<PERSON>Just\"><PERSON></a>, French soldier and politician (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1776", "text": "<PERSON>, English admiral (d. 1853)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "<PERSON> of Bavaria, King of Bavaria (d. 1868)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/Ludwig_<PERSON>_of_Bavaria\" title=\"<PERSON> I of Bavaria\"><PERSON> of Bavaria</a>, King of Bavaria (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludwig_I_of_Bavaria\" title=\"<PERSON> I of Bavaria\"><PERSON> of Bavaria</a>, King of Bavaria (d. 1868)", "links": [{"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/Ludwig_I_of_Bavaria"}]}, {"year": "1793", "text": "<PERSON>, American writer, critic, editor, lecturer, and activist (d. 1876)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, critic, editor, lecturer, and activist (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, critic, editor, lecturer, and activist (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, American carpenter and piano builder (d. 1876)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American carpenter and piano builder (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American carpenter and piano builder (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON><PERSON>, Romanian-Austrian poet and author (d. 1850)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-Austrian poet and author (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-Austrian poet and author (d. 1850)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON><PERSON>, Duke of Caxias (d. 1880)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Lu%C3%<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Lima_<PERSON>_<PERSON>,_Duke_of_Caxias\" title=\"<PERSON><PERSON>, Duke of Caxias\"><PERSON><PERSON>, Duke of Caxias</a> (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Lima_<PERSON>_<PERSON>,_Duke_of_Caxias\" title=\"<PERSON><PERSON>, Duke of Caxias\"><PERSON><PERSON>, Duke of Caxias</a> (d. 1880)", "links": [{"title": "<PERSON><PERSON>, Duke of Caxias", "link": "https://wikipedia.org/wiki/Lu%C3%<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_<PERSON>_of_Caxias"}]}, {"year": "1812", "text": "<PERSON><PERSON>, Russian organic chemist (d. 1880)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian organic chemist (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian organic chemist (d. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French nun and saint, founded the Religious of the Assumption (d. 1898)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A9nie_de_J%C3%A9sus\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>us\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Religious_of_the_Assumption\" title=\"Religious of the Assumption\">Religious of the Assumption</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A9nie_de_J%C3%A9sus\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Religious_of_the_Assumption\" title=\"Religious of the Assumption\">Religious of the Assumption</a> (d. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>ug%C3%A9nie_de_J%C3%A9sus"}, {"title": "Religious of the Assumption", "link": "https://wikipedia.org/wiki/Religious_of_the_Assumption"}]}, {"year": "1829", "text": "<PERSON>, Italian pianist and composer (d. 1909)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist and composer (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist and composer (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON><PERSON>, American short story writer and poet (d. 1902)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American short story writer and poet (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American short story writer and poet (d. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1840", "text": "<PERSON>, American businessman (d. 1893)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, Swiss physician and academic, Nobel Prize laureate (d. 1917)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1845", "text": "<PERSON> of Bavaria, King of Bavaria (d. 1886)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Bavaria\" title=\"<PERSON> II of Bavaria\"><PERSON> of Bavaria</a>, King of Bavaria (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludwig_II_of_Bavaria\" title=\"<PERSON> II of Bavaria\"><PERSON> of Bavaria</a>, King of Bavaria (d. 1886)", "links": [{"title": "<PERSON> II of Bavaria", "link": "https://wikipedia.org/wiki/Ludwig_II_of_Bavaria"}]}, {"year": "1850", "text": "<PERSON>, French physiologist and occultist, Nobel Prize laureate (d. 1935)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physiologist and occultist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physiologist and occultist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1867", "text": "<PERSON>, American lawyer and diplomat, United States Ambassador to Germany (d. 1951)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Germany\" class=\"mw-redirect\" title=\"United States Ambassador to Germany\">United States Ambassador to Germany</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Germany\" class=\"mw-redirect\" title=\"United States Ambassador to Germany\">United States Ambassador to Germany</a> (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to Germany", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Germany"}]}, {"year": "1869", "text": "<PERSON>, British-Irish decathlete (d. 1951)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Irish decathlete (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Irish decathlete (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Norwegian actress (d. 1963)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian actress (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian actress (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American businessman, co-founded the Lionel Corporation (d. 1965)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the Lionel Corporation (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the Lionel Corporation (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, English footballer and manager (d. 1935)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Irish journalist and politician, 2nd President of Ireland (d. 1966)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Se%C3%A1n_T._O%27Kelly\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish journalist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%A1n_T._O%27Kelly\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish journalist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%A1n_T._O%27Kelly"}, {"title": "President of Ireland", "link": "https://wikipedia.org/wiki/President_of_Ireland"}]}, {"year": "1889", "text": "<PERSON>, Australian politician, 26th Premier of New South Wales (d. 1969)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1891", "text": "<PERSON>, Belarusian-Israeli poet and translator (d. 1956)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Israeli poet and translator (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Israeli poet and translator (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American dentist (d. 1962)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American dentist (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American dentist (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, German mathematician and academic (d. 1975)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, English cricketer (d. 1973)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1898)\" title=\"<PERSON> (cricketer, born 1898)\"><PERSON></a>, English cricketer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1898)\" title=\"<PERSON> (cricketer, born 1898)\"><PERSON></a>, English cricketer (d. 1973)", "links": [{"title": "<PERSON> (cricketer, born 1898)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer,_born_1898)"}]}, {"year": "1899", "text": "<PERSON>, American historian and author (d. 1978)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Scottish architect (d. 1970) ", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish architect (d. 1970) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Scottish architect (d. 1970) ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German physician and biochemist, Nobel Prize laureate (d. 1981)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1902", "text": "<PERSON>, German-American composer and educator (d. 1972)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American composer and educator (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American composer and educator (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Hungarian-American chess player, created the Elo rating system (d. 1992)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Arpad_Elo\" title=\"Arp<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American chess player, created the <a href=\"https://wikipedia.org/wiki/Elo_rating_system\" title=\"Elo rating system\">Elo rating system</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arpad_Elo\" title=\"Arpad <PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American chess player, created the <a href=\"https://wikipedia.org/wiki/Elo_rating_system\" title=\"Elo rating system\">Elo rating system</a> (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arpad_Elo"}, {"title": "Elo rating system", "link": "https://wikipedia.org/wiki/Elo_rating_system"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Polish nun and saint (d. 1938)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish nun and saint (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish nun and saint (d. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English cricketer (d. 1979)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1906)\" title=\"<PERSON> (cricketer, born 1906)\"><PERSON></a>, English cricketer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1906)\" title=\"<PERSON> (cricketer, born 1906)\"><PERSON></a>, English cricketer (d. 1979)", "links": [{"title": "<PERSON> (cricketer, born 1906)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1906)"}]}, {"year": "1909", "text": "<PERSON>, Canadian-American actress, singer, and dancer (d. 1993)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress, singer, and dancer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress, singer, and dancer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Keeler"}]}, {"year": "1909", "text": "<PERSON>, English actor and producer (d. 1971)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American baseball player (d. 2010)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 2010)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1910", "text": "<PERSON>, American painter, sculptor, and poet (d. 2012)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, sculptor, and poet (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, sculptor, and poet (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Vietnamese general and politician, 3rd Minister of Defence for Vietnam (d. 2013)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/V%C3%B5_Nguy%C3%AAn_Gi%C3%A1p\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Vietnam)\" title=\"Minister of Defence (Vietnam)\">Minister of Defence for Vietnam</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%B5_Nguy%C3%AAn_Gi%C3%A1p\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Vietnam)\" title=\"Minister of Defence (Vietnam)\">Minister of Defence for Vietnam</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%B5_Nguy%C3%AAn_Gi%C3%A1p"}, {"title": "Minister of Defence (Vietnam)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Vietnam)"}]}, {"year": "1912", "text": "<PERSON>, German politician (d. 1994)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actor (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American illustrator and animator (d. 1973)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and animator (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and animator (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actor (d. 2008)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American pediatrician and virologist, Nobel Prize laureate (d. 2003)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pediatrician and virologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pediatrician and virologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Japanese lieutenant and pilot (d. 2000)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Sabur%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lieutenant and pilot (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sabur%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lieutenant and pilot (d. 2000)", "links": [{"title": "Saburō <PERSON>", "link": "https://wikipedia.org/wiki/Sabur%C5%8D_Sakai"}]}, {"year": "1917", "text": "<PERSON>, American actor, director, and producer (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American pianist, composer, and conductor (d. 1990)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English actor (d. 1985)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American bandleader and educator (d. 2010)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and educator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and educator (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American lawyer, and politician, 45th Governor of Alabama (d. 1998)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Alabama", "link": "https://wikipedia.org/wiki/Governor_of_Alabama"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Dutch Olympic medalist (d. 2017)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>aa<PERSON>_<PERSON>i<PERSON>\" title=\"<PERSON><PERSON><PERSON> Rijk<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch Olympic medalist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>aa<PERSON>_<PERSON>i<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Rijk<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch Olympic medalist (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aa<PERSON>_<PERSON>s"}]}, {"year": "1921", "text": "<PERSON>, Canadian television personality and game show host (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Monty Hall\"><PERSON></a>, Canadian television personality and game show host (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Monty Hall\"><PERSON></a>, Canadian television personality and game show host (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Canadian businessman and politician, 20th Canadian Minister of Labour (d. 1999)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 20th <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 20th <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Labour (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Labour_(Canada)"}]}, {"year": "1921", "text": "<PERSON>, Northern Irish-Canadian author and screenwriter (d. 1999)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Northern Irish-Canadian author and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Northern Irish-Canadian author and screenwriter (d. 1999)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>(novelist)"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Colombian-Mexican author and poet (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian-Mexican author and poet (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian-Mexican author and poet (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Canadian lawyer and judge (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and judge (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and judge (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian tennis player and coach (d. 2006)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Zsuzsa_K%C3%B6rm%C3%B6czy\" title=\"Zsuz<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian tennis player and coach (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zsuzsa_K%C3%B6rm%C3%B6czy\" title=\"Zsuzsa <PERSON>örmöczy\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian tennis player and coach (d. 2006)", "links": [{"title": "Zsuzsa <PERSON>y", "link": "https://wikipedia.org/wiki/Zsuzsa_K%C3%B6rm%C3%B6czy"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Australian journalist and author (d. 2004)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian journalist and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian journalist and author (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, German film and culture academic (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German film and culture academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German film and culture academic (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Lithuanian basketball player and coach (d. 2001)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player and coach (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player and coach (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>as"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American tennis player and golfer (d. 2003)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player and golfer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player and golfer (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Australian swimmer (d. 1999)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, American football player (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22K<PERSON><PERSON>%22_<PERSON><PERSON>\" class=\"mw-redirect\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American football player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22K<PERSON><PERSON>%22_<PERSON><PERSON>\" class=\"mw-redirect\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American football player (d. 2018)", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22K<PERSON><PERSON>%22_<PERSON><PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American baseball player, coach, and manager (d. 2004)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American composer and academic (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, German-American physicist, engineer, and academic, Nobel Prize laureate (d. 2024)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist, engineer, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist, engineer, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1930", "text": "<PERSON>, Scottish actor and producer (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and producer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and producer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian economist and geographer (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6<PERSON>_<PERSON>_(geographer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (geographer)\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian economist and geographer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6<PERSON>_<PERSON>_(geographer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (geographer)\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian economist and geographer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (geographer)", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_<PERSON><PERSON><PERSON>_(geographer)"}]}, {"year": "1930", "text": "<PERSON>, Canadian actor (d. 2003)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, English academic and diplomat, British Permanent Representative to the United Nations (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tickell\" title=\"C<PERSON><PERSON> Tickell\"><PERSON><PERSON><PERSON></a>, English academic and diplomat, <a href=\"https://wikipedia.org/wiki/Permanent_Representative_of_the_United_Kingdom_to_the_United_Nations\" title=\"Permanent Representative of the United Kingdom to the United Nations\">British Permanent Representative to the United Nations</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tickell\" title=\"<PERSON><PERSON><PERSON> Tickell\"><PERSON><PERSON><PERSON></a>, English academic and diplomat, <a href=\"https://wikipedia.org/wiki/Permanent_Representative_of_the_United_Kingdom_to_the_United_Nations\" title=\"Permanent Representative of the United Kingdom to the United Nations\">British Permanent Representative to the United Nations</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tickell"}, {"title": "Permanent Representative of the United Kingdom to the United Nations", "link": "https://wikipedia.org/wiki/Permanent_Representative_of_the_United_Kingdom_to_the_United_Nations"}]}, {"year": "1931", "text": "<PERSON>, American actor and television host (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and television host (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and television host (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regis_Philbin"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Soviet aviator and cosmonaut (d. 2005)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(cosmonaut)\" title=\"<PERSON><PERSON><PERSON> (cosmonaut)\"><PERSON><PERSON><PERSON></a>, Soviet aviator and cosmonaut (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(cosmonaut)\" title=\"<PERSON><PERSON><PERSON> (cosmonaut)\"><PERSON><PERSON><PERSON></a>, Soviet aviator and cosmonaut (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON> (cosmonaut)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(cosmonaut)"}]}, {"year": "1933", "text": "<PERSON>, American journalist and author (d. 2018)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American saxophonist and composer (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American actor", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Canadian judge and politician, Deputy Premier of Quebec", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian judge and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_Quebec\" title=\"Deputy Premier of Quebec\">Deputy Premier of Quebec</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian judge and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_Quebec\" title=\"Deputy Premier of Quebec\">Deputy Premier of Quebec</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Premier of Quebec", "link": "https://wikipedia.org/wiki/Deputy_Premier_of_Quebec"}]}, {"year": "1934", "text": "<PERSON>, Filipino journalist and politician (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and politician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American poet", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian businessman, founded the Image Institute of Technology & Management (d. 2009)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Giri<PERSON>rilal_Kedia\" title=\"<PERSON><PERSON><PERSON>rilal Kedia\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman, founded the <a href=\"https://wikipedia.org/wiki/Image_Institute_of_Technology_%26_Management\" title=\"Image Institute of Technology &amp; Management\">Image Institute of Technology &amp; Management</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giri<PERSON>rilal_Kedia\" title=\"<PERSON><PERSON>dharilal Kedia\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman, founded the <a href=\"https://wikipedia.org/wiki/Image_Institute_of_Technology_%26_Management\" title=\"Image Institute of Technology &amp; Management\">Image Institute of Technology &amp; Management</a> (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giridharilal_Kedia"}, {"title": "Image Institute of Technology & Management", "link": "https://wikipedia.org/wiki/Image_Institute_of_Technology_%26_Management"}]}, {"year": "1937", "text": "<PERSON>, Australian television host and singer (d. 2019)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and singer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and singer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American author", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Virginia_<PERSON><PERSON>_<PERSON>\" title=\"Virginia Euwer <PERSON>\"><PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_<PERSON><PERSON>_<PERSON>\" title=\"Virginia Euwer <PERSON>\"><PERSON><PERSON></a>, American author", "links": [{"title": "Virginia Euwer Wolff", "link": "https://wikipedia.org/wiki/Virginia_Eu<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actor (d. 2015)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English journalist and author", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English-American actor, director, and producer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Brazilian-American director, producer, and screenwriter (d. 2024)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American director, producer, and screenwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American director, producer, and screenwriter (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, German boxer and actor (d. 2004)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Ho<PERSON>urg\"><PERSON></a>, German boxer and actor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German boxer and actor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Italian footballer and coach (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and coach (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, German footballer (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BC<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer (d. 2021)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Ludwig_M%C3%BC<PERSON>_(footballer)"}]}, {"year": "1942", "text": "<PERSON>, American lawyer, and politician, 82nd Governor of Georgia", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nathan Deal\"><PERSON></a>, American lawyer, and politician, 82nd <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nathan Deal\"><PERSON></a>, American lawyer, and politician, 82nd <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Georgia", "link": "https://wikipedia.org/wiki/Governor_of_Georgia"}]}, {"year": "1942", "text": "<PERSON>, Irish poet and television presenter (d. 2025)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and television presenter (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and television presenter (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_Ingoldsby"}]}, {"year": "1942", "text": "<PERSON>, Canadian wrestler (d. 2017)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian historian and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Black\"><PERSON></a>, Canadian historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian ice hockey player, coach, and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, British lawyer and judge", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Belgian cartoonist (d. 2011)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cartoonist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cartoonist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American screenwriter and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American baseball player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gers\" title=\"<PERSON><PERSON> Fingers\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gers\" title=\"<PERSON><PERSON> Fingers\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gers"}]}, {"year": "1946", "text": "<PERSON>, American poet and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American football player and sportscaster (d. 2015)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American author and illustrator", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, British jazz pianist and composer (d. 2020)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British jazz pianist and composer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British jazz pianist and composer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American singer and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ledward Kaapana\"><PERSON><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ledward Kaapana\"><PERSON><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Greek chemist and biologist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek chemist and biologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek chemist and biologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, British novelist (d. 2023)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch banker and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Ri<PERSON>man_Groenink\" title=\"Rijkman Groenink\"><PERSON><PERSON><PERSON><PERSON> Groenink</a>, Dutch banker and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ri<PERSON>man_Groenink\" title=\"Rijkman Groenink\"><PERSON><PERSON><PERSON><PERSON> Groenink</a>, Dutch banker and academic", "links": [{"title": "Rijkman Groenink", "link": "https://wikipedia.org/wiki/<PERSON>i<PERSON><PERSON>_Gro<PERSON>nk"}]}, {"year": "1949", "text": "<PERSON>, American actor and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1949", "text": "<PERSON>, Israeli-American singer-songwriter, producer, and actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American singer-songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer and songwriter (d. 2009)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American bassist, composer, and producer (d. 2011)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist, composer, and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist, composer, and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English heavy metal singer-songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English heavy metal singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English heavy metal singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Brazilian-American lawyer and radio host", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American lawyer and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American lawyer and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Turkmen footballer and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Kurban_Be<PERSON>ev\" title=\"Kurban Be<PERSON>ev\"><PERSON><PERSON></a>, Turkmen footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kurban_Berdyev\" title=\"Kurban Berdyev\"><PERSON><PERSON></a>, Turkmen footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English keyboard player, songwriter, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Sri Lankan cricketer and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Elvis_Costello\" title=\"Elvis Costello\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elvis_Costello\" title=\"Elvis Costello\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, <PERSON> of Tankerness, Scottish lawyer and politician, First Minister of Scotland", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Tankerness\" title=\"<PERSON>, Baron <PERSON> of Tankerness\"><PERSON>, Baron <PERSON> of Tankerness</a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Tankerness\" title=\"<PERSON>, Baron <PERSON> of Tankerness\"><PERSON>, Baron <PERSON> of Tankerness</a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a>", "links": [{"title": "<PERSON>, <PERSON> of Tankerness", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Tankerness"}, {"title": "First Minister of Scotland", "link": "https://wikipedia.org/wiki/First_Minister_of_Scotland"}]}, {"year": "1955", "text": "<PERSON>, Scottish guitarist (d. 2004)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, German businessman and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/G<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, German businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, German businessman and politician", "links": [{"title": "<PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/Gerd_M%C3%<PERSON><PERSON>_(politician)"}]}, {"year": "1956", "text": "<PERSON>, English songwriter and record producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Japanese footballer, coach, and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ada"}]}, {"year": "1956", "text": "<PERSON>, Finnish race car driver (d. 1986)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish race car driver (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish race car driver (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON><PERSON><PERSON> (cricketer)\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON><PERSON><PERSON> (cricketer)\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1957", "text": "<PERSON>, English actor and director", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American ice hockey player and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American author and illustrator (d. 2023)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American lawyer and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1959", "text": "<PERSON>, Brazilian volleyball coach and player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian volleyball coach and player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian volleyball coach and player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American author and illustrator", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(illustrator)\" title=\"<PERSON> (illustrator)\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(illustrator)\" title=\"<PERSON> (illustrator)\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON> (illustrator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(illustrator)"}]}, {"year": "1959", "text": "<PERSON>, American soprano and actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Austrian footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter, guitarist, and actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Taslima_Nasrin\" title=\"Taslima Nasrin\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taslima_Nasrin\" title=\"Taslima Nasrin\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi author", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taslima_Nasrin"}]}, {"year": "1962", "text": "<PERSON>, American competition swimmer and Olympic champion", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American competition swimmer and Olympic champion", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American competition swimmer and Olympic champion", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Northern Irish rock guitarist and songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish rock guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish rock guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, German footballer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Slovenian lawyer and politician, 8th Prime Minister of Slovenia", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovenia\" title=\"Prime Minister of Slovenia\">Prime Minister of Slovenia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovenia\" title=\"Prime Minister of Slovenia\">Prime Minister of Slovenia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miro_Cerar"}, {"title": "Prime Minister of Slovenia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Slovenia"}]}, {"year": "1963", "text": "<PERSON>, American rapper and producer (d. 2021)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Shock_G\" title=\"Shock G\"><PERSON></a>, American rapper and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shock_G\" title=\"Shock G\"><PERSON></a>, American rapper and producer (d. 2021)", "links": [{"title": "Shock G", "link": "https://wikipedia.org/wiki/Shock_G"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Estonian lawyer and diplomat", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Tii<PERSON>_<PERSON>\" title=\"Tiina <PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tii<PERSON>_<PERSON>\" title=\"<PERSON>ii<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and diplomat", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tiina_Intelmann"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Malaysian mathematician and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian mathematician and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian mathematician and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Russian-American mathematician and academic", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American football player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American video game designer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Indian cricketer and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer (d. 1993)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mia_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American baseball player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert <PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert Belle\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American keyboard player, songwriter, and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American hip-hop DJ", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Terminator_X\" title=\"Terminator X\">Term<PERSON> X</a>, American hip-hop DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Terminator_X\" title=\"Terminator X\">Terminator X</a>, American hip-hop DJ", "links": [{"title": "Terminator X", "link": "https://wikipedia.org/wiki/Terminator_X"}]}, {"year": "1967", "text": "<PERSON>, English actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter, musician, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>y"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Japanese actress, model, and race car driver", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actress, model, and race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actress, model, and race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Scottish singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Spider_One\" title=\"Spider One\"><PERSON> One</a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spider_One\" title=\"Spider One\"><PERSON> One</a>, American singer-songwriter and producer", "links": [{"title": "Spider One", "link": "https://wikipedia.org/wiki/Spider_One"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American chef, author, and television host", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American chef, author, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American chef, author, and television host", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Japanese singer-songwriter and bass player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and bass player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>eda"}]}, {"year": "1969", "text": "<PERSON>, Norwegian-Russian pianist and composer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Russian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Russian pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian actor and television personality", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Scottish golfer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Indian cricketer, coach, and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>k <PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>k <PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer, coach, and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vivek_Razdan"}]}, {"year": "1970", "text": "<PERSON>, American baseball player and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American tennis player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American basketball player and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Papua New Guinean-Australian rugby league player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean-Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean-Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, German model and fashion designer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German model and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German model and fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jason Death\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Death\" title=\"Jason Death\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, German director, producer, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Fatih_Ak%C4%B1n\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fatih_Ak%C4%B1n\" class=\"mw-redirect\" title=\"Fatih <PERSON>kın\"><PERSON><PERSON></a>, German director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fatih_Ak%C4%B1n"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Dominican baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian rugby league player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Australian swimmer and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian swimmer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian swimmer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Thomas"}]}, {"year": "1976", "text": "<PERSON>, American basketball player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Swedish actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>g%C3%A5rd\" title=\"<PERSON>\"><PERSON></a>, Swedish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A5rd\" title=\"<PERSON>\"><PERSON></a>, Swedish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexander_Skarsg%C3%A5rd"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American actor, producer, and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, German rugby player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" class=\"mw-redirect\" title=\"<PERSON> (rugby)\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" class=\"mw-redirect\" title=\"<PERSON> (rugby)\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German historian and politician (d. 2015)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F<PERSON>er\" title=\"<PERSON>\"><PERSON></a>, German historian and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Mi%C3%9F<PERSON>er"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Namibian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Namibian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Namibian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, French tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, South Korean footballer (d. 2011)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1983", "text": "<PERSON>, English race car driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Russian-American model and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Serbian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ovi%C4%87"}]}, {"year": "1987", "text": "<PERSON>, American model and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Scottish singer-songwriter and guitarist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1987", "text": "<PERSON>, Australian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Brazilian-American golfer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Angela Park\"><PERSON></a>, Brazilian-American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Angela Park\"><PERSON></a>, Brazilian-American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Angela_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Georgian mixed martial artist and kickboxer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/G<PERSON>_<PERSON>dze\" title=\"Giga Chikadze\"><PERSON><PERSON></a>, Georgian mixed martial artist and kickboxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giga_Chi<PERSON>dze\" title=\"Giga Chikadze\"><PERSON><PERSON></a>, Georgian mixed martial artist and kickboxer", "links": [{"title": "Giga Chikadze", "link": "https://wikipedia.org/wiki/Giga_Chi<PERSON>dze"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Mexican footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Muncy\" title=\"<PERSON> Muncy\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Muncy\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ncy"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Japanese singer and actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Swiss footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Ricardo_Rodr%C3%<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Latvian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>kal<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, South Korean singer and actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/On<PERSON>_<PERSON>-wu\" title=\"<PERSON>g <PERSON>-wu\"><PERSON><PERSON>-wu</a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/On<PERSON>_<PERSON>-wu\" title=\"<PERSON>g <PERSON>-wu\"><PERSON><PERSON>-wu</a>, South Korean singer and actor", "links": [{"title": "<PERSON><PERSON>wu", "link": "https://wikipedia.org/wiki/Ong_<PERSON><PERSON>-wu"}]}, {"year": "1995", "text": "<PERSON><PERSON>, South Korean musician", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oon"}]}, {"year": "1998", "text": "<PERSON> <PERSON>, American actress and singer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/China_<PERSON>_<PERSON>\" title=\"<PERSON> Anne <PERSON>\"><PERSON> <PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a>, American actress and singer", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/China_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Slovak alpine ski racer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak alpine ski racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak alpine ski racer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a_Jan%C4%8Dov%C3%A1"}]}, {"year": "2004", "text": "<PERSON><PERSON>, French-Nigerien sabre fencer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Nigerien sabre fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Nigerien sabre fencer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "79", "text": "<PERSON><PERSON><PERSON> the Elder, Roman commander and philosopher (b. 23)", "html": "79 - AD 79 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Elder\" title=\"<PERSON><PERSON><PERSON> the Elder\"><PERSON><PERSON><PERSON> the Elder</a>, Roman commander and philosopher (b. 23)", "no_year_html": "AD 79 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Elder\" title=\"<PERSON><PERSON><PERSON> the Elder\"><PERSON><PERSON><PERSON> the Elder</a>, Roman commander and philosopher (b. 23)", "links": [{"title": "<PERSON><PERSON><PERSON> the Elder", "link": "https://wikipedia.org/wiki/Plin<PERSON>_the_<PERSON>"}]}, {"year": "274", "text": "<PERSON>, Jin Dynasty empress (b. 238)", "html": "274 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Empress <PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Jin_Dynasty_(265-420)\" class=\"mw-redirect\" title=\"Jin Dynasty (265-420)\">Jin Dynasty</a> empress (b. 238)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Empress <PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Jin_Dynasty_(265-420)\" class=\"mw-redirect\" title=\"Jin Dynasty (265-420)\">Jin Dynasty</a> empress (b. 238)", "links": [{"title": "Empress <PERSON>", "link": "https://wikipedia.org/wiki/Empress_<PERSON>_<PERSON>"}, {"title": "Jin Dynasty (265-420)", "link": "https://wikipedia.org/wiki/Jin_Dynasty_(265-420)"}]}, {"year": "306", "text": "Saint <PERSON><PERSON><PERSON>,  Christian hermit and martyr from Tarragona", "html": "306 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ma<PERSON>\" title=\"Saint <PERSON>\">Saint <PERSON></a>, Christian hermit and martyr from <a href=\"https://wikipedia.org/wiki/Tarragona\" title=\"Tarragona\">Tarragona</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Ma<PERSON>\" title=\"Saint <PERSON>\">Saint <PERSON></a>, Christian hermit and martyr from <a href=\"https://wikipedia.org/wiki/Tarragona\" title=\"Tarragona\">Tarragona</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ma<PERSON>"}, {"title": "Tarragona", "link": "https://wikipedia.org/wiki/Tarragona"}]}, {"year": "383", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 359)", "html": "383 - <a href=\"https://wikipedia.org/wiki/<PERSON>rat<PERSON>\" title=\"<PERSON>rat<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 359)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>rat<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 359)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ian"}]}, {"year": "471", "text": "<PERSON><PERSON><PERSON>, patriarch of Constantinople", "html": "471 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>dius_of_Constantinople\" title=\"<PERSON><PERSON>dius of Constantinople\"><PERSON><PERSON><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople\" title=\"Ecumenical Patriarch of Constantinople\">patriarch of Constantinople</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Constantinople\" title=\"<PERSON><PERSON>dius of Constantinople\"><PERSON><PERSON><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople\" title=\"Ecumenical Patriarch of Constantinople\">patriarch of Constantinople</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Gennadius_of_Constantinople"}, {"title": "Ecumenical Patriarch of Constantinople", "link": "https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople"}]}, {"year": "766", "text": "<PERSON>, Byzantine official", "html": "766 - <a href=\"https://wikipedia.org/wiki/Constantine_Podopagouros\" title=\"<PERSON>\"><PERSON></a>, Byzantine official", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_Podopagouros\" title=\"<PERSON>\"><PERSON></a>, Byzantine official", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Constantine_Podopagouros"}]}, {"year": "766", "text": "<PERSON><PERSON><PERSON><PERSON>, Byzantine general", "html": "766 - <a href=\"https://wikipedia.org/wiki/Strategios_Podopagouros\" title=\"Strategios Podopagouros\">Strate<PERSON>s Podopagouros</a>, Byzantine general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Strategios_Podopagouros\" title=\"Strategios Podopagouros\">Strategios Podopagouros</a>, Byzantine general", "links": [{"title": "Strategios Podopagouros", "link": "https://wikipedia.org/wiki/Strategios_Podopagouros"}]}, {"year": "985", "text": "<PERSON> of Haldensleben, German margrave", "html": "985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Haldensleben,_<PERSON><PERSON>_of_the_Nordmark\" title=\"<PERSON> of Haldensleben, Mar<PERSON> of the Nordmark\"><PERSON> of Haldensleben</a>, German <a href=\"https://wikipedia.org/wiki/Margrave\" title=\"<PERSON><PERSON>\">margrave</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Haldensleben,_Margrave_of_the_Nordmark\" title=\"<PERSON> of Haldensleben, Mar<PERSON> of the Nordmark\"><PERSON> of Haldensleben</a>, German <a href=\"https://wikipedia.org/wiki/<PERSON>grave\" title=\"<PERSON><PERSON>\">margrave</a>", "links": [{"title": "<PERSON> of Haldensleben, Margrave of the Nordmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Haldensleben,_<PERSON><PERSON>_of_the_Nordmark"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>grave"}]}, {"year": "1091", "text": "<PERSON><PERSON><PERSON><PERSON>, military leader", "html": "1091 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, military leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, military leader", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>s<PERSON><PERSON>_<PERSON>"}]}, {"year": "1192", "text": "<PERSON>, Duke of Burgundy (b. 1142)", "html": "1192 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1142)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1142)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1258", "text": "<PERSON>, regent of the Empire of Nicaea", "html": "1258 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, regent of the Empire of Nicaea", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, regent of the Empire of Nicaea", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1270", "text": "<PERSON> of France (b. 1214)", "html": "1270 - <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a> (b. 1214)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a> (b. 1214)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IX_of_France"}]}, {"year": "1270", "text": "<PERSON><PERSON><PERSON> of Brienne (b. c. 1225)", "html": "1270 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Brienne\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Brienne\"><PERSON><PERSON><PERSON> of Brienne</a> (b. c. 1225)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Brienne\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Brienne\"><PERSON><PERSON><PERSON> of Brienne</a> (b. c. 1225)", "links": [{"title": "<PERSON><PERSON><PERSON> of Brienne", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Brienne"}]}, {"year": "1271", "text": "<PERSON>, Countess of Toulouse (b. 1220)", "html": "1271 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Toulouse\" title=\"<PERSON>, Countess of Toulouse\"><PERSON>, Countess of Toulouse</a> (b. 1220)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Toulouse\" title=\"<PERSON>, Countess of Toulouse\"><PERSON>, Countess of Toulouse</a> (b. 1220)", "links": [{"title": "<PERSON>, Countess of Toulouse", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_of_Toulouse"}]}, {"year": "1282", "text": "<PERSON>, English bishop and saint (b. 1218)", "html": "1282 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and saint (b. 1218)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and saint (b. 1218)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1322", "text": "<PERSON> of Silesia, queen consort of Germany (b. c. 1292)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Silesia\" title=\"Beatrice of Silesia\"><PERSON> of Silesia</a>, queen consort of Germany (b. c. 1292)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Silesia\" title=\"Beatrice of Silesia\"><PERSON> of Silesia</a>, queen consort of Germany (b. c. 1292)", "links": [{"title": "Beatrice of Silesia", "link": "https://wikipedia.org/wiki/Beatrice_of_Silesia"}]}, {"year": "1327", "text": "<PERSON><PERSON><PERSON>, Chobanid", "html": "1327 - <a href=\"https://wikipedia.org/wiki/<PERSON>mas<PERSON>_<PERSON>ja\" title=\"Demas<PERSON> Kaja\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Demasq Kaja\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>mas<PERSON>_<PERSON>ja"}]}, {"year": "1330", "text": "Sir <PERSON>, Scottish guerrilla leader (b. 1286)", "html": "1330 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_of_Douglas\" title=\"<PERSON>, Lord of Douglas\">Sir <PERSON></a>, Scottish guerrilla leader (b. 1286)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Lord_of_Douglas\" title=\"<PERSON>, Lord of Douglas\">Sir <PERSON></a>, Scottish guerrilla leader (b. 1286)", "links": [{"title": "<PERSON>, Lord of Douglas", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_of_Douglas"}]}, {"year": "1339", "text": "<PERSON>, 1st Baron <PERSON> (b. 1260)", "html": "1339 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a> (b. 1260)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a> (b. 1260)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1368", "text": "<PERSON>, Italian painter, sculptor, and architect", "html": "1368 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian painter, sculptor, and architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian painter, sculptor, and architect", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1482", "text": "<PERSON> of Anjou wife of <PERSON> and Queen of England  (b. 1429) ", "html": "1482 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> VI of England\"><PERSON> VI</a> and Queen of England (b. 1429) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> VI of England\"><PERSON> VI</a> and Queen of England (b. 1429) ", "links": [{"title": "<PERSON> of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VI_of_England"}]}, {"year": "1485", "text": "<PERSON>, supporter of <PERSON> (b. 1450)", "html": "1485 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, supporter of <PERSON> (b. 1450)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, supporter of <PERSON> (b. 1450)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1554", "text": "<PERSON>, 3rd Duke of Norfolk, English soldier and politician, Lord High Treasurer (b. 1473)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Duke_of_Norfolk\" title=\"<PERSON>, 3rd Duke of Norfolk\"><PERSON>, 3rd Duke of Norfolk</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1473)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Norfolk\" title=\"<PERSON>, 3rd Duke of Norfolk\"><PERSON>, 3rd Duke of Norfolk</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1473)", "links": [{"title": "<PERSON>, 3rd Duke of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Duke_of_Norfolk"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1592", "text": "<PERSON>, Landgrave of Hesse-Kassel (b. 1532)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Kassel\" title=\"<PERSON>, Landgrave of Hesse-Kassel\"><PERSON>, Landgrave of Hesse-Kassel</a> (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Kassel\" title=\"<PERSON>, Landgrave of Hesse-Kassel\"><PERSON>, Landgrave of Hesse-Kassel</a> (b. 1532)", "links": [{"title": "<PERSON>, Landgrave of Hesse-Kassel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse-Kassel"}]}, {"year": "1600", "text": "<PERSON><PERSON><PERSON>, Japanese aristocrat and Catholic convert (b. 1563)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/Hosokawa_Gracia\" title=\"Hosokawa Gracia\"><PERSON><PERSON><PERSON> Gracia</a>, Japanese aristocrat and Catholic convert (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hosokawa_Gracia\" title=\"Hosokawa Gracia\"><PERSON><PERSON><PERSON> Gracia</a>, Japanese aristocrat and Catholic convert (b. 1563)", "links": [{"title": "Hosokawa Gracia", "link": "https://wikipedia.org/wiki/Hosokawa_Gracia"}]}, {"year": "1603", "text": "<PERSON>, Sultan of the Saadi dynasty (b. 1549)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sultan of the Saadi dynasty (b. 1549)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sultan of the Saadi dynasty (b. 1549)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1631", "text": "<PERSON>, Lord Chief Justice of England (b.c. 1572)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales\" title=\"Lord Chief Justice of England and Wales\">Lord Chief Justice</a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> (b.c. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales\" title=\"Lord Chief Justice of England and Wales\">Lord Chief Justice</a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> (b.c. 1572)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Chief Justice of England and Wales", "link": "https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}]}, {"year": "1632", "text": "<PERSON>, English author and playwright (b. 1572)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and playwright (b. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and playwright (b. 1572)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1688", "text": "<PERSON>, Welsh admiral and politician, Lieutenant Governor of Jamaica (b. 1635)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh admiral and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Jamaica\" class=\"mw-redirect\" title=\"Lieutenant Governor of Jamaica\">Lieutenant Governor of Jamaica</a> (b. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh admiral and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Jamaica\" class=\"mw-redirect\" title=\"Lieutenant Governor of Jamaica\">Lieutenant Governor of Jamaica</a> (b. 1635)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Jamaica", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Jamaica"}]}, {"year": "1699", "text": "<PERSON> Denmark (b. 1646)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/Christian_V_of_Denmark\" title=\"Christian V of Denmark\">Christian V of Denmark</a> (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_V_of_Denmark\" title=\"Christian V of Denmark\">Christian V of Denmark</a> (b. 1646)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_V_of_Denmark"}]}, {"year": "1711", "text": "<PERSON>, 1st Earl of Jersey, English politician, Secretary of State for the Southern Department (b. 1656)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Jersey\" title=\"<PERSON>, 1st Earl of Jersey\"><PERSON>, 1st Earl of Jersey</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Jersey\" title=\"<PERSON>, 1st Earl <PERSON> Jersey\"><PERSON>, 1st Earl of Jersey</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1656)", "links": [{"title": "<PERSON>, 1st Earl of Jersey", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Jersey"}, {"title": "Secretary of State for the Southern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department"}]}, {"year": "1742", "text": "<PERSON>, Portuguese organist and composer (b. 1704)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese organist and composer (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese organist and composer (b. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1774", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer and educator (b. 1714)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and educator (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and educator (b. 1714)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>"}]}, {"year": "1776", "text": "<PERSON>, Scottish economist, historian, and philosopher (b. 1711)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist, historian, and philosopher (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist, historian, and philosopher (b. 1711)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Belgian-Austrian diplomat (b. 1727)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>lor<PERSON><PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>lor<PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON></a>, Belgian-Austrian diplomat (b. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lor<PERSON><PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>lor<PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON></a>, Belgian-Austrian diplomat (b. 1727)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lor<PERSON><PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, Governor of the Vermont Republic, and first Governor of the State of Vermont (b. 1730)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Governor of the <a href=\"https://wikipedia.org/wiki/Vermont_Republic\" title=\"Vermont Republic\">Vermont Republic</a>, and first <a href=\"https://wikipedia.org/wiki/Governor_of_Vermont\" title=\"Governor of Vermont\">Governor of the State of Vermont</a> (b. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Governor of the <a href=\"https://wikipedia.org/wiki/Vermont_Republic\" title=\"Vermont Republic\">Vermont Republic</a>, and first <a href=\"https://wikipedia.org/wiki/Governor_of_Vermont\" title=\"Governor of Vermont\">Governor of the State of Vermont</a> (b. 1730)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vermont Republic", "link": "https://wikipedia.org/wiki/Vermont_Republic"}, {"title": "Governor of Vermont", "link": "https://wikipedia.org/wiki/Governor_of_Vermont"}]}, {"year": "1815", "text": "<PERSON>, American artisan and military officer (b. 1815)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artisan and military officer (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artisan and military officer (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, Scottish engineer and instrument maker (b. 1736)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and instrument maker (b. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and instrument maker (b. 1736)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, German-English astronomer and composer (b. 1738)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English astronomer and composer (b. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English astronomer and composer (b. 1738)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, English physicist and chemist (b. 1791)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and chemist (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and chemist (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Estonian physician and author (b. 1803)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physician and author (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physician and author (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek lawyer and politician, 35th Prime Minister of Greece (b. 1791)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1791)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>inovios_<PERSON>vis"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1892", "text": "<PERSON>, English-Australian politician, 1st Premier of Tasmania (b. 1808)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1900", "text": "<PERSON>, German philologist, philosopher, and critic (b. 1844)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist, philosopher, and critic (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist, philosopher, and critic (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, French painter and lithographer (b. 1836)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and lithographer (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and lithographer (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, French physicist and chemist, Nobel Prize laureate (b. 1852)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1916", "text": "<PERSON>, American novelist and short story writer (b. 1851)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Novelist\" title=\"Novelist\">novelist</a> and <a href=\"https://wikipedia.org/wiki/Short_story\" title=\"Short story\">short story</a> writer (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Novelist\" title=\"Novelist\">novelist</a> and <a href=\"https://wikipedia.org/wiki/Short_story\" title=\"Short story\">short story</a> writer (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Novelist", "link": "https://wikipedia.org/wiki/Novelist"}, {"title": "Short story", "link": "https://wikipedia.org/wiki/Short_story"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Russian poet and critic (b. 1886)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and critic (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and critic (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Filipino general and politician (b. 1818)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino general and politician (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino general and politician (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mariano_%C3%81<PERSON><PERSON><PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, American editor, and writer of prose and poetry (b. 1858)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American editor, and writer of prose and poetry (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American editor, and writer of prose and poetry (b. 1858)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Austrian field marshal (b. 1852)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6tzendorf\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6tzendorf\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6tzendorf"}]}, {"year": "1930", "text": "<PERSON>, American boxer (b. 1904)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, South African author and co-founder of Guild of Loyal Women (b. 1862)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author and co-founder of <a href=\"https://wikipedia.org/wiki/Guild_of_Loyal_Women\" title=\"Guild of Loyal Women\">Guild of Loyal Women</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author and co-founder of <a href=\"https://wikipedia.org/wiki/Guild_of_Loyal_Women\" title=\"Guild of Loyal Women\">Guild of Loyal Women</a> (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Guild of Loyal Women", "link": "https://wikipedia.org/wiki/Guild_of_Loyal_Women"}]}, {"year": "1936", "text": "<PERSON>, French author (b. 1836)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Adam\"><PERSON></a>, French author (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Adam\"><PERSON></a>, French author (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Russian pilot, explorer, and author (b. 1870)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian pilot, explorer, and author (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian pilot, explorer, and author (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1904)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON> <PERSON>, Duke of Guise (b. 1874)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Guise\" title=\"Prince <PERSON>, Duke of Guise\">Prince <PERSON>, Duke of Guise</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Guise\" title=\"Prince <PERSON>, Duke of Guise\">Prince <PERSON>, Duke of Guise</a> (b. 1874)", "links": [{"title": "<PERSON> <PERSON>, Duke of Guise", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guise"}]}, {"year": "1942", "text": "<PERSON>, Duke of Kent (b. 1902)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent\" title=\"Prince <PERSON>, Duke of Kent\">Prince <PERSON>, Duke of Kent</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent\" title=\"Prince <PERSON>, Duke of Kent\">Prince <PERSON>, Duke of Kent</a> (b. 1902)", "links": [{"title": "<PERSON>, Duke of Kent", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent"}]}, {"year": "1945", "text": "<PERSON>, American soldier and missionary (b. 1918)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, American soldier and missionary (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, American soldier and missionary (b. 1918)", "links": [{"title": "<PERSON> (missionary)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(missionary)"}]}, {"year": "1956", "text": "<PERSON>, American biologist and academic (b. 1894)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American baseball player and physician (b. 1879)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Graham\"><PERSON><PERSON></a>, American baseball player and physician (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Graham\"><PERSON><PERSON></a>, American baseball player and physician (b. 1879)", "links": [{"title": "Moonlight Graham", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Chinese novelist and dramatist (b. 1899)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_She\" title=\"<PERSON> She\"><PERSON></a>, Chinese novelist and dramatist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_She\" title=\"<PERSON> She\"><PERSON></a>, Chinese novelist and dramatist (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian lawyer and politician, 8th Prime Minister of Australia (b. 1883)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1967", "text": "<PERSON>, Argentine race car driver (b. 1928)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Argentine race car driver (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Argentine race car driver (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>abal%C3%A9n"}]}, {"year": "1967", "text": "<PERSON>, Ukrainian-born American actor (b. 1895)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-born American actor (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-born American actor (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American commander, politician, and activist, founded the American Nazi Party (b. 1918)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, politician, and activist, founded the <a href=\"https://wikipedia.org/wiki/American_Nazi_Party\" title=\"American Nazi Party\">American Nazi Party</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>well\"><PERSON></a>, American commander, politician, and activist, founded the <a href=\"https://wikipedia.org/wiki/American_Nazi_Party\" title=\"American Nazi Party\">American Nazi Party</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>well"}, {"title": "American Nazi Party", "link": "https://wikipedia.org/wiki/American_Nazi_Party"}]}, {"year": "1968", "text": "<PERSON>, Australian cricketer and coach (b. 1910)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian politician, 30th Premier of Tasmania (b. 1884)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Japanese architect and engineer, designed the Tokyo Tower (b. 1886)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Tach%C5%AB_Nait%C5%8D\" title=\"<PERSON>ch<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Tokyo_Tower\" title=\"Tokyo Tower\">Tokyo Tower</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tach%C5%AB_Nait%C5%8D\" title=\"Tachū Naitō\"><PERSON><PERSON><PERSON></a>, Japanese architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Tokyo_Tower\" title=\"Tokyo Tower\">Tokyo Tower</a> (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tach%C5%AB_Nait%C5%8D"}, {"title": "Tokyo Tower", "link": "https://wikipedia.org/wiki/Tokyo_Tower"}]}, {"year": "1971", "text": "<PERSON>, American singer and clarinet player (b. 1890)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and clarinet player (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and clarinet player (b. 1890)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian lawyer and politician, Prime Minister of Hungary (b. 1875)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Dezs%C5%91_Pattanty%C3%BAs-%C3%81brah%C3%A1m\" title=\"<PERSON>z<PERSON><PERSON> Pattantyús-Ábrahám\"><PERSON><PERSON><PERSON><PERSON> Pattantyús-<PERSON><PERSON><PERSON></a>, Hungarian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dezs%C5%91_Pattanty%C3%BAs-%C3%81brah%C3%A1m\" title=\"<PERSON>z<PERSON><PERSON> Pattantyús-Ábrahám\"><PERSON><PERSON><PERSON><PERSON> Pattantyús-<PERSON><PERSON></a>, Hungarian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1875)", "links": [{"title": "Dezső Pattantyús-Ábrahám", "link": "https://wikipedia.org/wiki/Dezs%C5%91_Pattanty%C3%BAs-%C3%81brah%C3%A1m"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish novelist and short story writer, Nobel Prize laureate (b. 1900)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian architect, ethnologist, and politician (b. 1883)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/K%C3%A1roly_K%C3%B3s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian architect, ethnologist, and politician (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A1roly_K%C3%B3s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian architect, ethnologist, and politician (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A1roly_K%C3%B3s"}]}, {"year": "1979", "text": "<PERSON>, American pianist, composer, and bandleader (b. 1911)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American dancer and choreographer (b. 1919)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Gower_Champion\" title=\"Gower Champion\">Gower Champion</a>, American dancer and choreographer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gower_Champion\" title=\"Gower Champion\">Gower Champion</a>, American dancer and choreographer (b. 1919)", "links": [{"title": "Gower Champion", "link": "https://wikipedia.org/wiki/Gower_Champion"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Greek actor and cinematographer (b. 1915)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor and cinematographer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor and cinematographer (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Polish singer (b. 1936)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Anna_German\" title=\"Anna German\"><PERSON></a>, Polish singer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anna_German\" title=\"Anna German\"><PERSON></a>, Polish singer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_German"}]}, {"year": "1984", "text": "<PERSON>, American novelist, playwright, and screenwriter (b. 1924)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and screenwriter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and screenwriter (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Ukrainian gymnast and coach (b. 1921)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian gymnast and coach (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian gymnast and coach (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American baseball player and sportscaster (b. 1899)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American businessman, founded the Pittsburgh Steelers (b. 1901)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Pittsburgh_Steelers\" title=\"Pittsburgh Steelers\">Pittsburgh Steelers</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Pittsburgh_Steelers\" title=\"Pittsburgh Steelers\">Pittsburgh Steelers</a> (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Rooney"}, {"title": "Pittsburgh Steelers", "link": "https://wikipedia.org/wiki/Pittsburgh_Steelers"}]}, {"year": "1990", "text": "<PERSON>, Canadian author and playwright (b. 1903)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and playwright (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and playwright (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American bass player and producer (b. 1951)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Jr., American lawyer and Supreme Court justice (b. 1907)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and Supreme Court justice (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and Supreme Court justice (b. 1907)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1999", "text": "<PERSON>, English keyboard player and songwriter (b. 1956)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, English keyboard player and songwriter (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, English keyboard player and songwriter (b. 1956)", "links": [{"title": "<PERSON> (British musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)"}]}, {"year": "2000", "text": "<PERSON>, American author and illustrator (b. 1901)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American soldier and pilot (b. 1918)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Frederick <PERSON>\"><PERSON></a>, American soldier and pilot (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frederick_<PERSON>_<PERSON>\" title=\"Frederick <PERSON>\"><PERSON></a>, American soldier and pilot (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American pianist, composer, and producer (b. 1937)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and producer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and producer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American bass player and songwriter  (b. 1955)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, American singer and actress (b. 1979)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Aali<PERSON>\" title=\"Aali<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ali<PERSON>\" title=\"Aali<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress (b. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>yah"}]}, {"year": "2001", "text": "<PERSON>, Canadian ice hockey player (b. 1938)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (b. 1938)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish engineer and businessman, co-founded Alarko Holding (b. 1929)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/%C3%9Czeyir_Garih\" title=\"Üzeyir Garih\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Alarko_Holding\" title=\"Alarko Holding\">Alarko Holding</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%9Czeyir_Garih\" title=\"Üzeyir Garih\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Alarko_Holding\" title=\"Alarko Holding\">Alarko Holding</a> (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%9C<PERSON><PERSON><PERSON>_<PERSON>h"}, {"title": "Alarko Holding", "link": "https://wikipedia.org/wiki/Alarko_Holding"}]}, {"year": "2001", "text": "<PERSON>, English race car driver and businessman, founded Tyrrell Racing (b. 1924)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/Tyrrell_Racing\" title=\"Tyrrell Racing\">Tyrrell Racing</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>rrell_Racing\" title=\"Tyrrell Racing\">Tyrrell Racing</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tyrrell Racing", "link": "https://wikipedia.org/wiki/<PERSON>rrell_Racing"}]}, {"year": "2002", "text": "<PERSON>, Australian author and poet (b. 1923)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American author and illustrator (b. 1933)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Tom_Feelings\" title=\"Tom Feelings\"><PERSON></a>, American author and illustrator (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom_Feelings\" title=\"Tom Feelings\"><PERSON></a>, American author and illustrator (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Feelings"}]}, {"year": "2005", "text": "<PERSON>, Czech-German academic and politician (b. 1939)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-German academic and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-German academic and politician (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Trinidadian-Tobagonian lawyer and politician, 2nd President of Trinidad and Tobago (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian-Tobagonian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Trinidad_and_Tobago\" title=\"President of Trinidad and Tobago\">President of Trinidad and Tobago</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian-Tobagonian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Trinidad_and_Tobago\" title=\"President of Trinidad and Tobago\">President of Trinidad and Tobago</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Trinidad and Tobago", "link": "https://wikipedia.org/wiki/President_of_Trinidad_and_Tobago"}]}, {"year": "2007", "text": "<PERSON>, American lawyer and scholar (b. 1915)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and scholar (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and scholar (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English footballer (b. 1988)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1988)\" title=\"<PERSON> (footballer, born 1988)\"><PERSON></a>, English footballer (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1988)\" title=\"<PERSON> (footballer, born 1988)\"><PERSON></a>, English footballer (b. 1988)", "links": [{"title": "<PERSON> (footballer, born 1988)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1988)"}]}, {"year": "2008", "text": "<PERSON>, Pakistani poet (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani poet (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani poet (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American basketball player (b. 1964)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American politician (b. 1932)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Malian economist and politician, Prime Minister of Mali (b. 1940)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Mand%C3%A9_Sidib%C3%A9\" title=\"Man<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malian economist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mali\" class=\"mw-redirect\" title=\"Prime Minister of Mali\">Prime Minister of Mali</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mand%C3%A9_Sidib%C3%A9\" title=\"Mandé <PERSON>\"><PERSON><PERSON><PERSON></a>, Malian economist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mali\" class=\"mw-redirect\" title=\"Prime Minister of Mali\">Prime Minister of Mali</a> (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mand%C3%A9_Sidib%C3%A9"}, {"title": "Prime Minister of Mali", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Mali"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Macedonian politician (b. 1920)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian politician (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Paraguayan footballer, coach, and actor (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Florencio_Amarilla\" title=\"Florencio <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Paraguayan footballer, coach, and actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florenc<PERSON>_Amarilla\" title=\"<PERSON>lorencio <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Paraguayan footballer, coach, and actor (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Florencio_Amarilla"}]}, {"year": "2012", "text": "<PERSON>, American pilot, engineer, and astronaut (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, engineer, and astronaut (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, engineer, and astronaut (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Mexican banker and businessman (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Barrera\" title=\"<PERSON>\"><PERSON></a>, Mexican banker and businessman (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_<PERSON>era\" title=\"<PERSON>\"><PERSON></a>, Mexican banker and businessman (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nz%C3%A1lez_Barrera"}]}, {"year": "2012", "text": "<PERSON>, Scottish politician (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Slovene poet and translator (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>iri<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene poet and translator (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>iri<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene poet and translator (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ciril_<PERSON>les"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese economist and banker (b. 1949)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Borges\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese economist and banker (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Borges\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese economist and banker (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_Borges"}]}, {"year": "2013", "text": "<PERSON>, American screenwriter and producer (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Chinese academic and politician, 3rd Minister of Justice for China (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese academic and politician, 3rd <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Ministry of Justice of the People's Republic of China\">Minister of Justice for China</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese academic and politician, 3rd <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Ministry of Justice of the People's Republic of China\">Minister of Justice for China</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Liu_<PERSON>zhi"}, {"title": "Ministry of Justice of the People's Republic of China", "link": "https://wikipedia.org/wiki/Ministry_of_Justice_of_the_People%27s_Republic_of_China"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian singer-songwriter (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Pan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian singer-songwriter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Pan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian singer-songwriter (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Neves\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> dos Santos Neves\"><PERSON><PERSON><PERSON> <PERSON></a>, Brazilian footballer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>eves\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> dos Santos Neves\"><PERSON><PERSON><PERSON> <PERSON></a>, Brazilian footballer (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON> Santos Neves", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American director and producer (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian educator and politician, 29th Canadian Minister of National Defence (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of National Defence (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)"}]}, {"year": "2014", "text": "<PERSON>, Dutch chemist and academic (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Nico_<PERSON>_<PERSON>_<PERSON>bbe<PERSON>\" title=\"Nico <PERSON> Nibbe<PERSON>\"><PERSON></a>, Dutch chemist and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nico_<PERSON>_<PERSON>_<PERSON>bbe<PERSON>\" title=\"Nico <PERSON>bbe<PERSON>\"><PERSON></a>, Dutch chemist and academic (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nico_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>bbe<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Jamaican-American drummer and producer (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican-American drummer and producer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican-American drummer and producer (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Peruvian journalist and publisher (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian journalist and publisher (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian journalist and publisher (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Spanish lawyer and politician (b. 1948)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Benegas\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Benegas\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and politician (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Benegas"}]}, {"year": "2015", "text": "<PERSON>, Norwegian historian and academic (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian historian and academic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian historian and academic (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1927)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American bodybuilder (b. 1971)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Rich_Pi<PERSON>\" title=\"Rich Piana\"><PERSON></a>, American bodybuilder (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rich Piana\"><PERSON></a>, American bodybuilder (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rich_Pi<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American politician (b. 1936)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Austrian business magnate and engineer (b. 1937) ", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ABch\" title=\"<PERSON>\"><PERSON></a>, Austrian business magnate and engineer (b. 1937) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ABch\" title=\"<PERSON>\"><PERSON></a>, Austrian business magnate and engineer (b. 1937) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_Pi%C3%ABch"}]}, {"year": "2022", "text": "<PERSON><PERSON>, American blues vocalist (b. 1930)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American blues vocalist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American blues vocalist (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Lebanese statesman, 34th Prime Minister of Lebanon (b. 1929)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese statesman, 34th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese statesman, 34th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Lebanon", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lebanon"}]}]}}