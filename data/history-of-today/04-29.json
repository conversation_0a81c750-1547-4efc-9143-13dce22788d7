{"date": "April 29", "url": "https://wikipedia.org/wiki/April_29", "data": {"Events": [{"year": "801", "text": "An earthquake in the Central Apennines hits Rome and Spoleto, damaging the basilica of San Paolo Fuori le Mura.", "html": "801 - An <a href=\"https://wikipedia.org/wiki/801_Apennine_earthquake\" title=\"801 Apennine earthquake\">earthquake</a> in the <a href=\"https://wikipedia.org/wiki/Central_Apennines\" class=\"mw-redirect\" title=\"Central Apennines\">Central Apennines</a> hits <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> and <a href=\"https://wikipedia.org/wiki/Spoleto\" title=\"Spoleto\">Spoleto</a>, damaging the basilica of <a href=\"https://wikipedia.org/wiki/San_Paolo_Fuori_le_Mura\" class=\"mw-redirect\" title=\"San Paolo Fuori le Mura\">San Paolo Fuori le Mura</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/801_Apennine_earthquake\" title=\"801 Apennine earthquake\">earthquake</a> in the <a href=\"https://wikipedia.org/wiki/Central_Apennines\" class=\"mw-redirect\" title=\"Central Apennines\">Central Apennines</a> hits <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> and <a href=\"https://wikipedia.org/wiki/Spoleto\" title=\"Spoleto\">Spoleto</a>, damaging the basilica of <a href=\"https://wikipedia.org/wiki/San_Paolo_Fuori_le_Mura\" class=\"mw-redirect\" title=\"San Paolo Fuori le Mura\">San Paolo Fuori le Mura</a>.", "links": [{"title": "801 Apennine earthquake", "link": "https://wikipedia.org/wiki/801_Apennine_earthquake"}, {"title": "Central Apennines", "link": "https://wikipedia.org/wiki/Central_Apennines"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}, {"title": "Spoleto", "link": "https://wikipedia.org/wiki/Spoleto"}, {"title": "San Paolo Fu<PERSON> le Mura", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1091", "text": "Battle of Levounion: The Pechenegs are defeated by Byzantine Emperor <PERSON><PERSON>.", "html": "1091 - <a href=\"https://wikipedia.org/wiki/Battle_of_Levounion\" title=\"Battle of Levounion\">Battle of Levounion</a>: The <a href=\"https://wikipedia.org/wiki/Pechenegs\" title=\"Pechenegs\">Pechenegs</a> are defeated by <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/Alexios_I_Komnenos\" title=\"<PERSON>ios I Komnenos\"><PERSON>ios I Komnenos</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Levounion\" title=\"Battle of Levounion\">Battle of Levounion</a>: The <a href=\"https://wikipedia.org/wiki/Pechenegs\" title=\"Pechenegs\">Pechenegs</a> are defeated by <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/Alexios_I_Komnenos\" title=\"<PERSON>ios I Komnenos\"><PERSON>ios I Komnenos</a>.", "links": [{"title": "Battle of Levounion", "link": "https://wikipedia.org/wiki/Battle_of_Levounion"}, {"title": "Pechenegs", "link": "https://wikipedia.org/wiki/Pechenegs"}, {"title": "List of Byzantine emperors", "link": "https://wikipedia.org/wiki/List_of_Byzantine_emperors"}, {"title": "Alexios I Komnenos", "link": "https://wikipedia.org/wiki/Alexios_I_Komnenos"}]}, {"year": "1429", "text": "<PERSON> of Arc arrives to relieve the Siege of Orléans.", "html": "1429 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Arc\" title=\"Joan of Arc\"><PERSON> of Arc</a> arrives to relieve the <a href=\"https://wikipedia.org/wiki/Siege_of_Orl%C3%A9ans\" title=\"Siege of Orléans\">Siege of Orléans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Arc\" title=\"Joan of Arc\"><PERSON> of Arc</a> arrives to relieve the <a href=\"https://wikipedia.org/wiki/Siege_of_Orl%C3%A9ans\" title=\"Siege of Orléans\">Siege of Orléans</a>.", "links": [{"title": "<PERSON> of Arc", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Siege of Orléans", "link": "https://wikipedia.org/wiki/Siege_of_Orl%C3%A9ans"}]}, {"year": "1483", "text": "Gran Canaria, the main island of the Canary Islands, is conquered by the Kingdom of Castile.", "html": "1483 - <a href=\"https://wikipedia.org/wiki/Gran_Canaria\" title=\"Gran Canaria\">Gran Canaria</a>, the main island of the <a href=\"https://wikipedia.org/wiki/Canary_Islands\" title=\"Canary Islands\">Canary Islands</a>, is conquered by the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gran_Canaria\" title=\"Gran Canaria\">Gran Canaria</a>, the main island of the <a href=\"https://wikipedia.org/wiki/Canary_Islands\" title=\"Canary Islands\">Canary Islands</a>, is conquered by the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a>.", "links": [{"title": "Gran Canaria", "link": "https://wikipedia.org/wiki/Gran_Canaria"}, {"title": "Canary Islands", "link": "https://wikipedia.org/wiki/Canary_Islands"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}]}, {"year": "1521", "text": "Swedish War of Liberation: Swedish troops defeat a Danish force in the Battle of Västerås.", "html": "1521 - <a href=\"https://wikipedia.org/wiki/Swedish_War_of_Liberation\" title=\"Swedish War of Liberation\">Swedish War of Liberation</a>: Swedish troops defeat a Danish force in the <a href=\"https://wikipedia.org/wiki/Battle_of_V%C3%A4ster%C3%A5s\" title=\"Battle of Västerås\">Battle of Västerås</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swedish_War_of_Liberation\" title=\"Swedish War of Liberation\">Swedish War of Liberation</a>: Swedish troops defeat a Danish force in the <a href=\"https://wikipedia.org/wiki/Battle_of_V%C3%A4ster%C3%A5s\" title=\"Battle of Västerås\">Battle of Västerås</a>.", "links": [{"title": "Swedish War of Liberation", "link": "https://wikipedia.org/wiki/Swedish_War_of_Liberation"}, {"title": "Battle of Västerås", "link": "https://wikipedia.org/wiki/Battle_of_V%C3%A4ster%C3%A5s"}]}, {"year": "1760", "text": "French forces commence the siege of Quebec which is held by the British.", "html": "1760 - French forces commence the <a href=\"https://wikipedia.org/wiki/Siege_of_Quebec_(1760)\" title=\"Siege of Quebec (1760)\">siege of Quebec</a> which is held by the British.", "no_year_html": "French forces commence the <a href=\"https://wikipedia.org/wiki/Siege_of_Quebec_(1760)\" title=\"Siege of Quebec (1760)\">siege of Quebec</a> which is held by the British.", "links": [{"title": "Siege of Quebec (1760)", "link": "https://wikipedia.org/wiki/Siege_of_Quebec_(1760)"}]}, {"year": "1770", "text": "<PERSON> arrives in Australia at Botany Bay, which he names.", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Australia</a> at <a href=\"https://wikipedia.org/wiki/Botany_Bay\" title=\"Botany Bay\">Botany Bay</a>, which he names.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Australia</a> at <a href=\"https://wikipedia.org/wiki/Botany_Bay\" title=\"Botany Bay\">Botany Bay</a>, which he names.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Australia", "link": "https://wikipedia.org/wiki/Australia"}, {"title": "Botany Bay", "link": "https://wikipedia.org/wiki/Botany_Bay"}]}, {"year": "1781", "text": "American Revolutionary War: British and French ships clash in the Battle of Fort Royal off the coast of Martinique.", "html": "1781 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British and French ships clash in the <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Royal\" title=\"Battle of Fort Royal\">Battle of Fort Royal</a> off the coast of <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"Martinique\">Martinique</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British and French ships clash in the <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Royal\" title=\"Battle of Fort Royal\">Battle of Fort Royal</a> off the coast of <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"Martinique\">Martinique</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Fort Royal", "link": "https://wikipedia.org/wiki/Battle_of_Fort_Royal"}, {"title": "Martinique", "link": "https://wikipedia.org/wiki/<PERSON>ique"}]}, {"year": "1826", "text": "The galaxy Centaurus A or NGC 5128 is discovered by <PERSON>.", "html": "1826 - The galaxy <a href=\"https://wikipedia.org/wiki/Centaurus_A\" title=\"Centaurus A\">Centaurus A</a> or NGC 5128 is discovered by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The galaxy <a href=\"https://wikipedia.org/wiki/Centaurus_A\" title=\"Centaurus A\">Centaurus A</a> or NGC 5128 is discovered by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Centaurus A", "link": "https://wikipedia.org/wiki/Centaurus_A"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1861", "text": "Maryland in the American Civil War: Maryland's House of Delegates votes not to secede from the Union.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Maryland_in_the_American_Civil_War\" title=\"Maryland in the American Civil War\">Maryland in the American Civil War</a>: Maryland's House of Delegates votes not to secede from the Union.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maryland_in_the_American_Civil_War\" title=\"Maryland in the American Civil War\">Maryland in the American Civil War</a>: Maryland's House of Delegates votes not to secede from the Union.", "links": [{"title": "Maryland in the American Civil War", "link": "https://wikipedia.org/wiki/Maryland_in_the_American_Civil_War"}]}, {"year": "1862", "text": "American Civil War: The Capture of New Orleans by Union forces under <PERSON>.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Capture_of_New_Orleans\" title=\"Capture of New Orleans\">Capture of New Orleans</a> by Union forces under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Capture_of_New_Orleans\" title=\"Capture of New Orleans\">Capture of New Orleans</a> by Union forces under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Capture of New Orleans", "link": "https://wikipedia.org/wiki/Capture_of_New_Orleans"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1864", "text": "Theta Xi fraternity is founded at Rensselaer Polytechnic Institute, the only fraternity to be founded during the American Civil War.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Theta_Xi\" title=\"Theta Xi\">Theta Xi</a> fraternity is founded at <a href=\"https://wikipedia.org/wiki/Rensselaer_Polytechnic_Institute\" title=\"Rensselaer Polytechnic Institute\">Rensselaer Polytechnic Institute</a>, the only fraternity to be founded during the <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Theta_Xi\" title=\"Theta Xi\">Theta Xi</a> fraternity is founded at <a href=\"https://wikipedia.org/wiki/Rensselaer_Polytechnic_Institute\" title=\"Rensselaer Polytechnic Institute\">Rensselaer Polytechnic Institute</a>, the only fraternity to be founded during the <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>.", "links": [{"title": "Theta Xi", "link": "https://wikipedia.org/wiki/Theta_Xi"}, {"title": "Rensselaer Polytechnic Institute", "link": "https://wikipedia.org/wiki/Rensselaer_Polytechnic_Institute"}, {"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}]}, {"year": "1903", "text": "A landslide kills 70 people in Frank, in the District of Alberta, Canada.", "html": "1903 - A <a href=\"https://wikipedia.org/wiki/Frank_Slide\" title=\"Frank Slide\">landslide</a> kills 70 people in <a href=\"https://wikipedia.org/wiki/<PERSON>,_Alberta\" title=\"Frank, Alberta\"><PERSON></a>, in the <a href=\"https://wikipedia.org/wiki/District_of_Alberta\" title=\"District of Alberta\">District of Alberta</a>, Canada.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lide\" title=\"Frank Slide\">landslide</a> kills 70 people in <a href=\"https://wikipedia.org/wiki/<PERSON>,_Alberta\" title=\"Frank, Alberta\"><PERSON></a>, in the <a href=\"https://wikipedia.org/wiki/District_of_Alberta\" title=\"District of Alberta\">District of Alberta</a>, Canada.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lide"}, {"title": "Frank, Alberta", "link": "https://wikipedia.org/wiki/Frank,_Alberta"}, {"title": "District of Alberta", "link": "https://wikipedia.org/wiki/District_of_Alberta"}]}, {"year": "1910", "text": "The Parliament of the United Kingdom passes the People's Budget, the first budget in British history with the expressed intent of redistributing wealth among the British public.", "html": "1910 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom\" title=\"Parliament of the United Kingdom\">Parliament of the United Kingdom</a> passes the <a href=\"https://wikipedia.org/wiki/People%27s_Budget\" title=\"People's Budget\">People's Budget</a>, the first budget in British history with the expressed intent of <a href=\"https://wikipedia.org/wiki/Redistribution_of_wealth\" class=\"mw-redirect\" title=\"Redistribution of wealth\">redistributing wealth</a> among the British public.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom\" title=\"Parliament of the United Kingdom\">Parliament of the United Kingdom</a> passes the <a href=\"https://wikipedia.org/wiki/People%27s_Budget\" title=\"People's Budget\">People's Budget</a>, the first budget in British history with the expressed intent of <a href=\"https://wikipedia.org/wiki/Redistribution_of_wealth\" class=\"mw-redirect\" title=\"Redistribution of wealth\">redistributing wealth</a> among the British public.", "links": [{"title": "Parliament of the United Kingdom", "link": "https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom"}, {"title": "People's Budget", "link": "https://wikipedia.org/wiki/People%27s_Budget"}, {"title": "Redistribution of wealth", "link": "https://wikipedia.org/wiki/Redistribution_of_wealth"}]}, {"year": "1911", "text": "Tsinghua University, one of mainland China's leading universities, is founded.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Tsinghua_University\" title=\"Tsinghua University\">Tsinghua University</a>, one of <a href=\"https://wikipedia.org/wiki/Mainland_China\" title=\"Mainland China\">mainland China</a>'s leading universities, is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsinghua_University\" title=\"Tsinghua University\">Tsinghua University</a>, one of <a href=\"https://wikipedia.org/wiki/Mainland_China\" title=\"Mainland China\">mainland China</a>'s leading universities, is founded.", "links": [{"title": "Tsinghua University", "link": "https://wikipedia.org/wiki/Tsinghua_University"}, {"title": "Mainland China", "link": "https://wikipedia.org/wiki/Mainland_China"}]}, {"year": "1916", "text": "World War I: The UK's 6th Indian Division surrenders to Ottoman Forces at the Siege of Kut in one of the largest surrenders of British forces up to that point.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The UK's <a href=\"https://wikipedia.org/wiki/6th_Indian_Division\" class=\"mw-redirect\" title=\"6th Indian Division\">6th Indian Division</a> surrenders to <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Forces</a> at the <a href=\"https://wikipedia.org/wiki/Siege_of_Kut\" title=\"Siege of Kut\">Siege of Kut</a> in one of the largest surrenders of British forces up to that point.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The UK's <a href=\"https://wikipedia.org/wiki/6th_Indian_Division\" class=\"mw-redirect\" title=\"6th Indian Division\">6th Indian Division</a> surrenders to <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Forces</a> at the <a href=\"https://wikipedia.org/wiki/Siege_of_Kut\" title=\"Siege of Kut\">Siege of Kut</a> in one of the largest surrenders of British forces up to that point.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "6th Indian Division", "link": "https://wikipedia.org/wiki/6th_Indian_Division"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Siege of Kut", "link": "https://wikipedia.org/wiki/Siege_of_Ku<PERSON>"}]}, {"year": "1916", "text": "Easter Rising: After six days of fighting, Irish rebel leaders surrender to British forces in Dublin, bringing the Easter Rising to an end.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>: After six days of fighting, Irish rebel leaders surrender to British forces in <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a>, bringing the Easter Rising to an end.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>: After six days of fighting, Irish rebel leaders surrender to British forces in <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a>, bringing the Easter Rising to an end.", "links": [{"title": "Easter Rising", "link": "https://wikipedia.org/wiki/Easter_Rising"}, {"title": "Dublin", "link": "https://wikipedia.org/wiki/Dublin"}]}, {"year": "1945", "text": "World War II: The Surrender of Caserta is signed by the commander of German forces in Italy.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Surrender_of_Caserta\" class=\"mw-redirect\" title=\"Surrender of Caserta\">Surrender of Caserta</a> is signed by the commander of German forces in Italy.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Surrender_of_Caserta\" class=\"mw-redirect\" title=\"Surrender of Caserta\">Surrender of Caserta</a> is signed by the commander of German forces in Italy.", "links": [{"title": "Surrender of Caserta", "link": "https://wikipedia.org/wiki/Surrender_of_Caserta"}]}, {"year": "1945", "text": "World War II: Airdrops of food begin over German-occupied regions of the Netherlands.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Operations_Manna_and_Chowhound\" title=\"Operations Manna and Chowhound\">Airdrops of food</a> begin over German-occupied regions of the Netherlands.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Operations_Manna_and_Chowhound\" title=\"Operations Manna and Chowhound\">Airdrops of food</a> begin over German-occupied regions of the Netherlands.", "links": [{"title": "Operations Manna and Chowhound", "link": "https://wikipedia.org/wiki/Operations_Manna_and_Chowhound"}]}, {"year": "1945", "text": "World War II: <PERSON> marries his longtime partner <PERSON> in a Berlin bunker and designates Admiral <PERSON> as his successor.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> marries his longtime partner <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in a <a href=\"https://wikipedia.org/wiki/F%C3%BChrerbunker\" title=\"Führerbunker\">Berlin bunker</a> and designates <a href=\"https://wikipedia.org/wiki/Admiral\" title=\"Admiral\">Admiral</a> <a href=\"https://wikipedia.org/wiki/Karl_D%C3%B6<PERSON>\" title=\"<PERSON>\"><PERSON></a> as his successor.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> marries his longtime partner <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> in a <a href=\"https://wikipedia.org/wiki/F%C3%BChrerbunker\" title=\"Führerbunker\">Berlin bunker</a> and designates <a href=\"https://wikipedia.org/wiki/Admiral\" title=\"Admiral\">Admiral</a> <a href=\"https://wikipedia.org/wiki/Karl_D%C3%B6nitz\" title=\"<PERSON>\"><PERSON></a> as his successor.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Führerbunker", "link": "https://wikipedia.org/wiki/F%C3%BChrerbunker"}, {"title": "Admiral", "link": "https://wikipedia.org/wiki/Admiral"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_D%C3%B6nitz"}]}, {"year": "1945", "text": "Dachau concentration camp is liberated by United States troops.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Dachau_concentration_camp\" title=\"Dachau concentration camp\">Dachau concentration camp</a> is liberated by United States troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dachau_concentration_camp\" title=\"Dachau concentration camp\">Dachau concentration camp</a> is liberated by United States troops.", "links": [{"title": "Dachau concentration camp", "link": "https://wikipedia.org/wiki/Dachau_concentration_camp"}]}, {"year": "1946", "text": "The International Military Tribunal for the Far East convenes and indicts former Prime Minister of Japan <PERSON><PERSON><PERSON> and 28 former Japanese leaders for war crimes.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East\" title=\"International Military Tribunal for the Far East\">International Military Tribunal for the Far East</a> convenes and indicts former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> <a href=\"https://wikipedia.org/wiki/Hideki_Tojo\" title=\"Hideki <PERSON>jo\"><PERSON><PERSON><PERSON></a> and 28 former Japanese leaders for <a href=\"https://wikipedia.org/wiki/War_crimes\" class=\"mw-redirect\" title=\"War crimes\">war crimes</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East\" title=\"International Military Tribunal for the Far East\">International Military Tribunal for the Far East</a> convenes and indicts former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> <a href=\"https://wikipedia.org/wiki/Hideki_Tojo\" title=\"Hideki <PERSON>jo\"><PERSON><PERSON><PERSON></a> and 28 former Japanese leaders for <a href=\"https://wikipedia.org/wiki/War_crimes\" class=\"mw-redirect\" title=\"War crimes\">war crimes</a>.", "links": [{"title": "International Military Tribunal for the Far East", "link": "https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "War crimes", "link": "https://wikipedia.org/wiki/War_crimes"}]}, {"year": "1952", "text": "Pan Am Flight 202 crashes into the Amazon basin near Carolina, Maranhão, Brazil, killing 50 people.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_202\" title=\"Pan Am Flight 202\">Pan Am Flight 202</a> crashes into the <a href=\"https://wikipedia.org/wiki/Amazon_basin\" title=\"Amazon basin\">Amazon basin</a> near <a href=\"https://wikipedia.org/wiki/Carolina,_Maranh%C3%A3o\" title=\"Carolina, Maranhão\">Carolina, Maranhão</a>, Brazil, killing 50 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_202\" title=\"Pan Am Flight 202\">Pan Am Flight 202</a> crashes into the <a href=\"https://wikipedia.org/wiki/Amazon_basin\" title=\"Amazon basin\">Amazon basin</a> near <a href=\"https://wikipedia.org/wiki/Carolina,_Maranh%C3%A3o\" title=\"Carolina, Maranhão\">Carolina, Maranhão</a>, Brazil, killing 50 people.", "links": [{"title": "Pan Am Flight 202", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_202"}, {"title": "Amazon basin", "link": "https://wikipedia.org/wiki/Amazon_basin"}, {"title": "Carolina, Maranhão", "link": "https://wikipedia.org/wiki/Carolina,_Maranh%C3%A3o"}]}, {"year": "1953", "text": "The first U.S. experimental 3D television broadcast shows an episode of Space Patrol on Los Angeles ABC affiliate KECA-TV.", "html": "1953 - The first U.S. experimental <a href=\"https://wikipedia.org/wiki/3D_television\" title=\"3D television\">3D television</a> broadcast shows an episode of <i><a href=\"https://wikipedia.org/wiki/Space_Patrol_(1950_TV_series)\" title=\"Space Patrol (1950 TV series)\">Space Patrol</a></i> on Los Angeles <a href=\"https://wikipedia.org/wiki/American_Broadcasting_Company\" title=\"American Broadcasting Company\">ABC</a> affiliate <a href=\"https://wikipedia.org/wiki/KECA-TV\" class=\"mw-redirect\" title=\"KECA-TV\">KECA-TV</a>.", "no_year_html": "The first U.S. experimental <a href=\"https://wikipedia.org/wiki/3D_television\" title=\"3D television\">3D television</a> broadcast shows an episode of <i><a href=\"https://wikipedia.org/wiki/Space_Patrol_(1950_TV_series)\" title=\"Space Patrol (1950 TV series)\">Space Patrol</a></i> on Los Angeles <a href=\"https://wikipedia.org/wiki/American_Broadcasting_Company\" title=\"American Broadcasting Company\">ABC</a> affiliate <a href=\"https://wikipedia.org/wiki/KECA-TV\" class=\"mw-redirect\" title=\"KECA-TV\">KECA-TV</a>.", "links": [{"title": "3D television", "link": "https://wikipedia.org/wiki/3D_television"}, {"title": "Space Patrol (1950 TV series)", "link": "https://wikipedia.org/wiki/Space_Patrol_(1950_TV_series)"}, {"title": "American Broadcasting Company", "link": "https://wikipedia.org/wiki/American_Broadcasting_Company"}, {"title": "KECA-TV", "link": "https://wikipedia.org/wiki/KECA-TV"}]}, {"year": "1967", "text": "After refusing induction into the United States Army the previous day, <PERSON> is stripped of his boxing title.", "html": "1967 - After refusing induction into the United States Army the previous day, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ali\"><PERSON></a> is stripped of his <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a> title.", "no_year_html": "After refusing induction into the United States Army the previous day, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ali\"><PERSON></a> is stripped of his <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a> title.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Boxing", "link": "https://wikipedia.org/wiki/Boxing"}]}, {"year": "1970", "text": "Vietnam War: United States and South Vietnamese forces invade Cambodia to hunt Viet Cong.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: United States and <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> forces <a href=\"https://wikipedia.org/wiki/Cambodian_Campaign\" class=\"mw-redirect\" title=\"Cambodian Campaign\">invade Cambodia</a> to hunt <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: United States and <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> forces <a href=\"https://wikipedia.org/wiki/Cambodian_Campaign\" class=\"mw-redirect\" title=\"Cambodian Campaign\">invade Cambodia</a> to hunt <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "Cambodian Campaign", "link": "https://wikipedia.org/wiki/Cambodian_Campaign"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}]}, {"year": "1974", "text": "Watergate scandal: United States President <PERSON> announces the release of edited transcripts of White House tape recordings relating to the scandal.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the release of edited transcripts of <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> tape recordings relating to the scandal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the release of edited transcripts of <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> tape recordings relating to the scandal.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}]}, {"year": "1975", "text": "Vietnam War: Operation Frequent Wind: The U.S. begins to evacuate U.S. citizens from Saigon before an expected North Vietnamese takeover. U.S. involvement in the war comes to an end.", "html": "1975 - Vietnam War: <a href=\"https://wikipedia.org/wiki/Operation_Frequent_Wind\" title=\"Operation Frequent Wind\">Operation Frequent Wind</a>: The U.S. begins to evacuate U.S. citizens from <a href=\"https://wikipedia.org/wiki/Saigon\" class=\"mw-redirect\" title=\"Saigon\">Saigon</a> before an expected <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> takeover. U.S. involvement in the war comes to an end.", "no_year_html": "Vietnam War: <a href=\"https://wikipedia.org/wiki/Operation_Frequent_Wind\" title=\"Operation Frequent Wind\">Operation Frequent Wind</a>: The U.S. begins to evacuate U.S. citizens from <a href=\"https://wikipedia.org/wiki/Saigon\" class=\"mw-redirect\" title=\"Saigon\">Saigon</a> before an expected <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> takeover. U.S. involvement in the war comes to an end.", "links": [{"title": "Operation Frequent Wind", "link": "https://wikipedia.org/wiki/Operation_Frequent_Wind"}, {"title": "Saigon", "link": "https://wikipedia.org/wiki/Saigon"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}]}, {"year": "1975", "text": "Vietnam War: The North Vietnamese army completes its capture of all parts of South Vietnam-held Trường Sa Islands.", "html": "1975 - Vietnam War: The <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">North Vietnamese army</a> completes <a href=\"https://wikipedia.org/wiki/East_Sea_Campaign\" title=\"East Sea Campaign\">its capture</a> of all parts of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>-held <a href=\"https://wikipedia.org/wiki/Spratly_Islands\" title=\"Spratly Islands\">Trường Sa Islands</a>.", "no_year_html": "Vietnam War: The <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">North Vietnamese army</a> completes <a href=\"https://wikipedia.org/wiki/East_Sea_Campaign\" title=\"East Sea Campaign\">its capture</a> of all parts of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>-held <a href=\"https://wikipedia.org/wiki/Spratly_Islands\" title=\"Spratly Islands\">Trường Sa Islands</a>.", "links": [{"title": "People's Army of Vietnam", "link": "https://wikipedia.org/wiki/People%27s_Army_of_Vietnam"}, {"title": "East Sea Campaign", "link": "https://wikipedia.org/wiki/East_Sea_Campaign"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "Spratly Islands", "link": "https://wikipedia.org/wiki/Spratly_Islands"}]}, {"year": "1986", "text": "A fire at the Central library of the Los Angeles Public Library damages or destroys 400,000 books and other items.", "html": "1986 - A fire at the Central library of the <a href=\"https://wikipedia.org/wiki/Los_Angeles_Public_Library\" title=\"Los Angeles Public Library\">Los Angeles Public Library</a> damages or destroys 400,000 books and other items.", "no_year_html": "A fire at the Central library of the <a href=\"https://wikipedia.org/wiki/Los_Angeles_Public_Library\" title=\"Los Angeles Public Library\">Los Angeles Public Library</a> damages or destroys 400,000 books and other items.", "links": [{"title": "Los Angeles Public Library", "link": "https://wikipedia.org/wiki/Los_Angeles_Public_Library"}]}, {"year": "1986", "text": "The United States Navy aircraft carrier USS Enterprise becomes the first nuclear-powered aircraft carrier to transit the Suez Canal, navigating from the Red Sea to the Mediterranean Sea to relieve the USS Coral Sea.", "html": "1986 - The United States Navy <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/USS_Enterprise_(CVN-65)\" title=\"USS Enterprise (CVN-65)\">USS <i>Enterprise</i></a> becomes the first nuclear-powered <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> to transit the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>, navigating from the <a href=\"https://wikipedia.org/wiki/Red_Sea\" title=\"Red Sea\">Red Sea</a> to the Mediterranean Sea to relieve the <a href=\"https://wikipedia.org/wiki/USS_Coral_Sea_(CV-43)\" title=\"USS Coral Sea (CV-43)\">USS <i>Coral Sea</i></a>.", "no_year_html": "The United States Navy <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/USS_Enterprise_(CVN-65)\" title=\"USS Enterprise (CVN-65)\">USS <i>Enterprise</i></a> becomes the first nuclear-powered <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> to transit the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>, navigating from the <a href=\"https://wikipedia.org/wiki/Red_Sea\" title=\"Red Sea\">Red Sea</a> to the Mediterranean Sea to relieve the <a href=\"https://wikipedia.org/wiki/USS_Coral_Sea_(CV-43)\" title=\"USS Coral Sea (CV-43)\">USS <i>Coral Sea</i></a>.", "links": [{"title": "Aircraft carrier", "link": "https://wikipedia.org/wiki/Aircraft_carrier"}, {"title": "USS Enterprise (CVN-65)", "link": "https://wikipedia.org/wiki/USS_Enterprise_(CVN-65)"}, {"title": "Aircraft carrier", "link": "https://wikipedia.org/wiki/Aircraft_carrier"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}, {"title": "Red Sea", "link": "https://wikipedia.org/wiki/Red_Sea"}, {"title": "USS Coral Sea (CV-43)", "link": "https://wikipedia.org/wiki/USS_Coral_Sea_(CV-43)"}]}, {"year": "1986", "text": "An assembly of Sikhs, known as a Sarbat Khalsa, officially declared independence for a state of Khalistan.", "html": "1986 - An assembly of <a href=\"https://wikipedia.org/wiki/Sikhs\" title=\"Sikhs\">Sikhs</a>, known as a <a href=\"https://wikipedia.org/wiki/Sarbat_Khalsa\" title=\"Sarbat Khalsa\">Sarbat Khalsa</a>, officially declared independence for a state of <a href=\"https://wikipedia.org/wiki/Khalistan_movement\" title=\"Khalistan movement\">Khalistan</a>.", "no_year_html": "An assembly of <a href=\"https://wikipedia.org/wiki/Sikhs\" title=\"Sikhs\">Sikhs</a>, known as a <a href=\"https://wikipedia.org/wiki/Sarbat_Khalsa\" title=\"Sarbat Khalsa\">Sarbat Khalsa</a>, officially declared independence for a state of <a href=\"https://wikipedia.org/wiki/Khalistan_movement\" title=\"Khalistan movement\">Khalistan</a>.", "links": [{"title": "Sikhs", "link": "https://wikipedia.org/wiki/Sikhs"}, {"title": "Sarbat Khalsa", "link": "https://wikipedia.org/wiki/Sarbat_Khalsa"}, {"title": "Khalistan movement", "link": "https://wikipedia.org/wiki/Khalistan_movement"}]}, {"year": "1991", "text": "A cyclone strikes the Chittagong district of southeastern Bangladesh with winds of around 155 miles per hour (249 km/h), killing at least 138,000 people and leaving as many as ten million homeless.", "html": "1991 - A <a href=\"https://wikipedia.org/wiki/1991_Bangladesh_cyclone\" title=\"1991 Bangladesh cyclone\">cyclone</a> strikes the <a href=\"https://wikipedia.org/wiki/Chittagong\" title=\"Chittagong\">Chittagong</a> district of southeastern <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> with winds of around 155 miles per hour (249 km/h), killing at least 138,000 people and leaving as many as ten million homeless.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1991_Bangladesh_cyclone\" title=\"1991 Bangladesh cyclone\">cyclone</a> strikes the <a href=\"https://wikipedia.org/wiki/Chittagong\" title=\"Chittagong\">Chittagong</a> district of southeastern <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> with winds of around 155 miles per hour (249 km/h), killing at least 138,000 people and leaving as many as ten million homeless.", "links": [{"title": "1991 Bangladesh cyclone", "link": "https://wikipedia.org/wiki/1991_Bangladesh_cyclone"}, {"title": "Chittagong", "link": "https://wikipedia.org/wiki/Chittagong"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}]}, {"year": "1991", "text": "The 7.0 Mw  Racha earthquake affects Georgia with a maximum MSK intensity of IX (Destructive), killing 270 people.", "html": "1991 - The 7.0 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1991_Racha_earthquake\" title=\"1991 Racha earthquake\">Racha earthquake</a> affects <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a> with a maximum <a href=\"https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale\" title=\"Medvedev-Sponheuer-Karnik scale\">MSK</a> intensity of IX (<i>Destructive</i>), killing 270 people.", "no_year_html": "The 7.0 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1991_Racha_earthquake\" title=\"1991 Racha earthquake\">Racha earthquake</a> affects <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a> with a maximum <a href=\"https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale\" title=\"Medvedev-Sponheuer-Karnik scale\">MSK</a> intensity of IX (<i>Destructive</i>), killing 270 people.", "links": [{"title": "1991 Racha earthquake", "link": "https://wikipedia.org/wiki/1991_Racha_earthquake"}, {"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}, {"title": "<PERSON>d<PERSON><PERSON>-Sponheuer-Karnik scale", "link": "https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale"}]}, {"year": "1992", "text": "Riots in Los Angeles begin, following the acquittal of police officers charged with excessive force in the beating of <PERSON>. Over the next three days 63 people are killed and hundreds of buildings are destroyed.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/1992_Los_Angeles_riots\" title=\"1992 Los Angeles riots\">Riots in Los Angeles</a> begin, following the acquittal of police officers charged with excessive force in the beating of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. Over the next three days 63 people are killed and hundreds of buildings are destroyed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1992_Los_Angeles_riots\" title=\"1992 Los Angeles riots\">Riots in Los Angeles</a> begin, following the acquittal of police officers charged with excessive force in the beating of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. Over the next three days 63 people are killed and hundreds of buildings are destroyed.", "links": [{"title": "1992 Los Angeles riots", "link": "https://wikipedia.org/wiki/1992_Los_Angeles_riots"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "The Chemical Weapons Convention of 1993 enters into force, outlawing the production, stockpiling and use of chemical weapons by its signatories.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Chemical_Weapons_Convention\" title=\"Chemical Weapons Convention\">Chemical Weapons Convention</a> of 1993 enters into force, outlawing the production, stockpiling and use of <a href=\"https://wikipedia.org/wiki/Chemical_warfare\" title=\"Chemical warfare\">chemical weapons</a> by its signatories.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chemical_Weapons_Convention\" title=\"Chemical Weapons Convention\">Chemical Weapons Convention</a> of 1993 enters into force, outlawing the production, stockpiling and use of <a href=\"https://wikipedia.org/wiki/Chemical_warfare\" title=\"Chemical warfare\">chemical weapons</a> by its signatories.", "links": [{"title": "Chemical Weapons Convention", "link": "https://wikipedia.org/wiki/Chemical_Weapons_Convention"}, {"title": "Chemical warfare", "link": "https://wikipedia.org/wiki/Chemical_warfare"}]}, {"year": "2004", "text": "The final Oldsmobile is built in Lansing, Michigan, ending 107 years of vehicle production.", "html": "2004 - The final <a href=\"https://wikipedia.org/wiki/Oldsmobile\" title=\"Oldsmobile\">Oldsmobile</a> is built in <a href=\"https://wikipedia.org/wiki/Lansing,_Michigan\" title=\"Lansing, Michigan\">Lansing, Michigan</a>, ending 107 years of vehicle production.", "no_year_html": "The final <a href=\"https://wikipedia.org/wiki/Oldsmobile\" title=\"Oldsmobile\">Oldsmobile</a> is built in <a href=\"https://wikipedia.org/wiki/Lansing,_Michigan\" title=\"Lansing, Michigan\">Lansing, Michigan</a>, ending 107 years of vehicle production.", "links": [{"title": "Oldsmobile", "link": "https://wikipedia.org/wiki/Oldsmobile"}, {"title": "Lansing, Michigan", "link": "https://wikipedia.org/wiki/Lansing,_Michigan"}]}, {"year": "2011", "text": "The Wedding of <PERSON> and <PERSON> takes place at Westminster Abbey in London.", "html": "2011 - The <a href=\"https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>_and_<PERSON>_<PERSON>\" title=\"Wedding of Prince <PERSON> and <PERSON>\">Wedding of Prince <PERSON> and <PERSON></a> takes place at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a> in London.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>_and_<PERSON>_<PERSON>\" title=\"Wedding of Prince <PERSON> and <PERSON>\">Wedding of Prince <PERSON> and <PERSON></a> takes place at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a> in London.", "links": [{"title": "Wedding of <PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>_and_<PERSON>_<PERSON>"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "2013", "text": "A powerful explosion occurs in an office building in Prague, believed to have been caused by natural gas, and injures 43 people.", "html": "2013 - A powerful <a href=\"https://wikipedia.org/wiki/2013_Prague_explosion\" title=\"2013 Prague explosion\">explosion</a> occurs in an office building in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>, believed to have been caused by natural gas, and injures 43 people.", "no_year_html": "A powerful <a href=\"https://wikipedia.org/wiki/2013_Prague_explosion\" title=\"2013 Prague explosion\">explosion</a> occurs in an office building in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>, believed to have been caused by natural gas, and injures 43 people.", "links": [{"title": "2013 Prague explosion", "link": "https://wikipedia.org/wiki/2013_Prague_explosion"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}]}, {"year": "2013", "text": "National Airlines Flight 102, a Boeing 747-400 freighter aircraft, crashes during takeoff from Bagram Airfield in Parwan Province, Afghanistan, killing all seven people on board.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/National_Airlines_Flight_102\" title=\"National Airlines Flight 102\">National Airlines Flight 102</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_747-400\" title=\"Boeing 747-400\">Boeing 747-400</a> freighter aircraft, crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Bagram_Airfield\" title=\"Bagram Airfield\">Bagram Airfield</a> in <a href=\"https://wikipedia.org/wiki/Parwan_Province\" title=\"Parwan Province\">Parwan Province</a>, Afghanistan, killing all seven people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/National_Airlines_Flight_102\" title=\"National Airlines Flight 102\">National Airlines Flight 102</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_747-400\" title=\"Boeing 747-400\">Boeing 747-400</a> freighter aircraft, crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Bagram_Airfield\" title=\"Bagram Airfield\">Bagram Airfield</a> in <a href=\"https://wikipedia.org/wiki/Parwan_Province\" title=\"Parwan Province\">Parwan Province</a>, Afghanistan, killing all seven people on board.", "links": [{"title": "National Airlines Flight 102", "link": "https://wikipedia.org/wiki/National_Airlines_Flight_102"}, {"title": "Boeing 747-400", "link": "https://wikipedia.org/wiki/Boeing_747-400"}, {"title": "Bagram Airfield", "link": "https://wikipedia.org/wiki/Bagram_Airfield"}, {"title": "Parwan Province", "link": "https://wikipedia.org/wiki/Parwan_Province"}]}, {"year": "2015", "text": "A baseball game between the Baltimore Orioles and the Chicago White Sox sets the all-time low attendance mark for Major League Baseball. Zero fans were in attendance for the game, as the stadium was officially closed to the public due to the 2015 Baltimore protests.", "html": "2015 - A baseball game between the <a href=\"https://wikipedia.org/wiki/Baltimore_Orioles\" title=\"Baltimore Orioles\">Baltimore Orioles</a> and the <a href=\"https://wikipedia.org/wiki/Chicago_White_Sox\" title=\"Chicago White Sox\">Chicago White Sox</a> sets the all-time low attendance mark for <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a>. Zero fans were in attendance for the game, as the stadium was officially closed to the public due to the <a href=\"https://wikipedia.org/wiki/2015_Baltimore_protests\" title=\"2015 Baltimore protests\">2015 Baltimore protests</a>.", "no_year_html": "A baseball game between the <a href=\"https://wikipedia.org/wiki/Baltimore_Orioles\" title=\"Baltimore Orioles\">Baltimore Orioles</a> and the <a href=\"https://wikipedia.org/wiki/Chicago_White_Sox\" title=\"Chicago White Sox\">Chicago White Sox</a> sets the all-time low attendance mark for <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a>. Zero fans were in attendance for the game, as the stadium was officially closed to the public due to the <a href=\"https://wikipedia.org/wiki/2015_Baltimore_protests\" title=\"2015 Baltimore protests\">2015 Baltimore protests</a>.", "links": [{"title": "Baltimore Orioles", "link": "https://wikipedia.org/wiki/Baltimore_Orioles"}, {"title": "Chicago White Sox", "link": "https://wikipedia.org/wiki/Chicago_White_Sox"}, {"title": "Major League Baseball", "link": "https://wikipedia.org/wiki/Major_League_Baseball"}, {"title": "2015 Baltimore protests", "link": "https://wikipedia.org/wiki/2015_Baltimore_protests"}]}], "Births": [{"year": "1469", "text": "<PERSON>, Landgrave of Hesse (d. 1509)", "html": "1469 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse\" title=\"<PERSON>, Landgrave of Hesse\"><PERSON>, Landgrave of Hesse</a> (d. 1509)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse\" title=\"<PERSON>, Landgrave of Hesse\"><PERSON>, Landgrave of Hesse</a> (d. 1509)", "links": [{"title": "<PERSON>, Landgrave of Hesse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse"}]}, {"year": "1587", "text": "<PERSON> of Saxony, Duchess of Pomerania (d. 1635)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxony,_Duchess_of_Pomerania\" title=\"<PERSON> of Saxony, Duchess of Pomerania\"><PERSON> of Saxony, Duchess of Pomerania</a> (d. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxony,_Duchess_of_Pomerania\" title=\"<PERSON> of Saxony, Duchess of Pomerania\"><PERSON> of Saxony, Duchess of Pomerania</a> (d. 1635)", "links": [{"title": "<PERSON> of Saxony, Duchess of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxony,_Duchess_of_Pomerania"}]}, {"year": "1636", "text": "<PERSON><PERSON><PERSON>, German lute player and composer (d. 1679)", "html": "1636 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Lute\" title=\"Lute\">lute</a> player and composer (d. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Lute\" title=\"<PERSON><PERSON>\">lute</a> player and composer (d. 1679)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Lute", "link": "https://wikipedia.org/wiki/Lute"}]}, {"year": "1665", "text": "<PERSON>, 2nd Duke of Ormonde, Irish general and politician, Lord Lieutenant of Ireland (d. 1745)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Ormonde\" title=\"<PERSON>, 2nd Duke of Ormonde\"><PERSON>, 2nd Duke of Ormonde</a>, Irish general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Ormonde\" title=\"<PERSON>, 2nd Duke of Ormonde\"><PERSON>, 2nd Duke of Ormonde</a>, Irish general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1745)", "links": [{"title": "<PERSON>, 2nd Duke of Ormonde", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Ormonde"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1667", "text": "<PERSON>, Scottish-English physician and polymath (d. 1735)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English physician and polymath (d. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English physician and polymath (d. 1735)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1727", "text": "<PERSON><PERSON><PERSON>, French actor and dancer (d. 1810)", "html": "1727 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and dancer (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and dancer (d. 1810)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1745", "text": "<PERSON>, American lawyer and politician, 3rd Chief Justice of the United States (d. 1807)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1758", "text": "<PERSON>, Swedish general (d. 1820)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6beln\" title=\"<PERSON>\"><PERSON></a>, Swedish general (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6beln\" title=\"<PERSON>\"><PERSON></a>, Swedish general (d. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6beln"}]}, {"year": "1762", "text": "<PERSON><PERSON><PERSON>, French general and politician, French Minister of Foreign Affairs (d. 1833)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Development_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs and International Development (France)\">French Minister of Foreign Affairs</a> (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Development_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs and International Development (France)\">French Minister of Foreign Affairs</a> (d. 1833)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Ministry of Foreign Affairs and International Development (France)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Development_(France)"}]}, {"year": "1780", "text": "<PERSON>, French librarian and author (d. 1844)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French librarian and author (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French librarian and author (d. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1783", "text": "<PERSON>, English landscape painter (d. 1859)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English landscape painter (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English landscape painter (d. 1859)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1784", "text": "<PERSON>, American publisher and politician, 14th Lieutenant Governor of Massachusetts (d. 1850)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, 14th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts\" title=\"Lieutenant Governor of Massachusetts\">Lieutenant Governor of Massachusetts</a> (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, 14th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts\" title=\"Lieutenant Governor of Massachusetts\">Lieutenant Governor of Massachusetts</a> (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts"}]}, {"year": "1810", "text": "<PERSON>, English journalist and author (d. 1892)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ope\"><PERSON></a>, English journalist and author (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ope\"><PERSON></a>, English journalist and author (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON> of Russia (d. 1881)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a> (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a> (d. 1881)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}]}, {"year": "1837", "text": "<PERSON>, French general and politician, French Minister of War (d. 1891)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1842", "text": "<PERSON>, Austrian composer and conductor (d. 1899)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6cker\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carl_<PERSON>%C3%B6cker\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carl_Mill%C3%B6cker"}]}, {"year": "1847", "text": "<PERSON>, Danish flautist, composer and conductor (d. 1907)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Danish flautist, composer and conductor (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Danish flautist, composer and conductor (d. 1907)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>(composer)"}]}, {"year": "1848", "text": "<PERSON>, Indian painter and academic (d. 1906)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Raja <PERSON></a>, Indian painter and academic (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Raja_<PERSON>_<PERSON>\" title=\"<PERSON>\">Raja <PERSON></a>, Indian painter and academic (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, French mathematician, physicist and engineer (d. 1912)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist and engineer (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist and engineer (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_Poincar%C3%A9"}]}, {"year": "1863", "text": "<PERSON>, Egyptian-Greek journalist and poet (d. 1933)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Constantine_P._<PERSON>avafy\" title=\"<PERSON>\"><PERSON></a>, Egyptian-Greek journalist and poet (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_P._<PERSON>fy\" title=\"<PERSON> P<PERSON> C<PERSON>fy\"><PERSON></a>, Egyptian-Greek journalist and poet (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Constantine_P._Cavafy"}]}, {"year": "1863", "text": "<PERSON>, American publisher and politician, founded the Hearst Corporation (d. 1951)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, founded the <a href=\"https://wikipedia.org/wiki/Hearst_Corporation\" class=\"mw-redirect\" title=\"Hearst Corporation\">Hearst Corporation</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, founded the <a href=\"https://wikipedia.org/wiki/Hearst_Corporation\" class=\"mw-redirect\" title=\"Hearst Corporation\">Hearst Corporation</a> (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Hearst Corporation", "link": "https://wikipedia.org/wiki/Hearst_Corporation"}]}, {"year": "1863", "text": "<PERSON>, Austrian nun and missionary (d. 1922)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Maria_Teres<PERSON>_Led%C3%B3chowska\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian nun and missionary (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_Teres<PERSON>_Led%C3%B3chowska\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian nun and missionary (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Teresia_Led%C3%B3chowska"}]}, {"year": "1872", "text": "<PERSON>, American businessman and lawyer (d. 1930)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and lawyer (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and lawyer (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, American astronomer and academic (d. 1952)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Forest Ray <PERSON>\"><PERSON></a>, American astronomer and academic (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ray_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Italian-English novelist and short story writer (d. 1950)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English novelist and short story writer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English novelist and short story writer (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, English conductor (d. 1961)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Polish historian, musicologist and academic (d. 1952)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish historian, musicologist and academic (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish historian, musicologist and academic (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adolf_Chybi%C5%84ski"}]}, {"year": "1882", "text": "<PERSON>, French painter (d. 1960)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Dutch printer, typographer, and Nazi resister (d. 1945)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch printer, typographer, and <a href=\"https://wikipedia.org/wiki/Dutch_Resistance\" class=\"mw-redirect\" title=\"Dutch Resistance\">Nazi resister</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch printer, typographer, and <a href=\"https://wikipedia.org/wiki/Dutch_Resistance\" class=\"mw-redirect\" title=\"Dutch Resistance\">Nazi resister</a> (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Dutch Resistance", "link": "https://wikipedia.org/wiki/Dutch_Resistance"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Czech journalist and author (d. 1948)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech journalist and author (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech journalist and author (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American ornithologist (d. 1973)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American immunologist (d. 1991)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American immunologist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American immunologist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, British businessman (d. 1980)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British businessman (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British businessman (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American chemist and astronomer, Nobel Prize laureate (d. 1981)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and astronomer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and astronomer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1894", "text": "<PERSON><PERSON>, Austrian physicist and academic (d. 1970)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Marietta_Blau\" title=\"Marietta Blau\"><PERSON><PERSON></a>, Austrian physicist and academic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marietta_Blau\" title=\"Marietta Blau\"><PERSON><PERSON></a>, Austrian physicist and academic (d. 1970)", "links": [{"title": "Marietta Blau", "link": "https://wikipedia.org/wiki/Marietta_Blau"}]}, {"year": "1895", "text": "<PERSON>, Russian scholar and critic (d. 1970)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian scholar and critic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian scholar and critic (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>pp"}]}, {"year": "1895", "text": "<PERSON>, English organist, composer and conductor (d. 1967)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist, composer and conductor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist, composer and conductor (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON> <PERSON><PERSON>, British physical chemist (d. 1980)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British physical chemist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British physical chemist (d. 1980)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American pianist, composer and bandleader (d. 1974)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer and bandleader (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer and bandleader (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American illustrator (d. 1976)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Australian politician (d. 1979)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Best\" title=\"Amelia Best\"><PERSON></a>, Australian politician (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Best\" title=\"Amelia Best\"><PERSON></a>, Australian politician (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese emperor (d. 1989)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Hiro<PERSON>o\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hi<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hi<PERSON><PERSON>o"}]}, {"year": "1907", "text": "<PERSON>, Austrian-American director and producer (d. 1997)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American director and producer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American director and producer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American author and academic (d. 2006)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American actor (d. 1994)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American actor, director, and screenwriter (d. 1977)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and screenwriter (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and screenwriter (d. 1977)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1915", "text": "<PERSON>, German-American physicist and academic (d. 1997)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Ukrainian-American director, poet, and photographer (d. 1961)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Maya Der<PERSON>\"><PERSON></a>, Ukrainian-American director, poet, and photographer (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Maya Der<PERSON>\"><PERSON></a>, Ukrainian-American director, poet, and photographer (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>en"}]}, {"year": "1917", "text": "<PERSON>, American actress and singer (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American football player and coach (d. 1990)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (d. 1990)", "links": [{"title": "<PERSON> (American football coach)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)"}]}, {"year": "1919", "text": "<PERSON><PERSON>, French actor, director and screenwriter (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>y\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>y\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director and screenwriter (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_Oury"}]}, {"year": "1920", "text": "<PERSON>, English author and radio host (d. 1996)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and radio host (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and radio host (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American composer (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, American politician (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Belgian guitarist and harmonica player (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Thi<PERSON>mans\" title=\"<PERSON><PERSON> Thielemans\"><PERSON><PERSON></a>, Belgian guitarist and harmonica player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Thi<PERSON>\" title=\"<PERSON><PERSON> Thielemans\"><PERSON><PERSON></a>, Belgian guitarist and harmonica player (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American actor, director and producer (d. 2010)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director and producer (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, French ballerina and actress (d. 2020)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French ballerina and actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French ballerina and actress (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Saint Lucian lawyer and politician, 1st Prime Minister of Saint Lucia (d. 2007)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saint Lucian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Saint_Lucia\" class=\"mw-redirect\" title=\"Prime Minister of Saint Lucia\">Prime Minister of Saint Lucia</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saint Lucian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Saint_Lucia\" class=\"mw-redirect\" title=\"Prime Minister of Saint Lucia\">Prime Minister of Saint Lucia</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Saint Lucia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Saint_Lucia"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, American animator, director, and producer (d. 2007)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American animator, director, and producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American animator, director, and producer (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American journalist and author (d. 2009)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English sprinter (d. 2021)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English footballer (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 2018)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1928", "text": "<PERSON>, American singer (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, German-English physiologist, engineer, and academic (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English physiologist, engineer, and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English physiologist, engineer, and academic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German author and academic (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and academic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian composer and conductor (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and conductor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and conductor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/April_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/April_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian businessman and diplomat (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and diplomat (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and diplomat (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English lawyer and politician (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, French actor and director (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, German-British painter (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-British painter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-British painter (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Scottish-English singer-songwriter and guitarist (d. 2002)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish-English singer-songwriter and guitarist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish-English singer-songwriter and guitarist (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nnie_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian politician, 1st Premier of Yukon (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Yukon\" title=\"Premier of Yukon\">Premier of Yukon</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Yukon\" title=\"Premier of Yukon\">Premier of Yukon</a> (d. 2014)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Premier of Yukon", "link": "https://wikipedia.org/wiki/Premier_of_Yukon"}]}, {"year": "1932", "text": "<PERSON>, English painter and educator", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Soviet pilot and cosmonaut instructor (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet pilot and cosmonaut instructor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet pilot and cosmonaut instructor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American baseball player and coach (d. 2018)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter and poet (d. 2015)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter, guitarist, producer and actor", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Venezuelan-American baseball player", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Cape Verdean politician, 3rd President of Cape Verde", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cape Verdean politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Cape_Verde\" class=\"mw-redirect\" title=\"President of Cape Verde\">President of Cape Verde</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cape Verdean politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Cape_Verde\" class=\"mw-redirect\" title=\"President of Cape Verde\">President of Cape Verde</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Cape Verde", "link": "https://wikipedia.org/wiki/President_of_Cape_Verde"}]}, {"year": "1935", "text": "<PERSON>, American blues singer-songwriter and guitarist (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Rush\" title=\"<PERSON> Rush\"><PERSON></a>, American blues singer-songwriter and guitarist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Rush\" title=\"Otis Rush\"><PERSON></a>, American blues singer-songwriter and guitarist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Indian conductor", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian conductor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Spanish priest, 13th Superior General of the Society of Jesus (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1s\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish priest, 13th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1s\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish priest, 13th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adolfo_Nicol%C3%A1s"}, {"title": "Superior General of the Society of Jesus", "link": "https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Argentine poet (d. 1972)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine poet (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine poet (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, 4th Baron <PERSON>, English banker and philanthropist (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English banker and philanthropist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English banker and philanthropist (d. 2024)", "links": [{"title": "<PERSON>, 4th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English author (d. 2020)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>,  American writer, businessman and educator (d. 2009)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, businessman and educator (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, businessman and educator (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American businessman, financier and convicted felon (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>off\"><PERSON></a>, American businessman, financier and convicted felon (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Madoff\"><PERSON></a>, American businessman, financier and convicted felon (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, German contemporary artist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German contemporary artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German contemporary artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American musician (d. 1992)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician (d. 1992)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1940", "text": "<PERSON>, American economist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Peter Diamond\"><PERSON></a>, American economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Peter Diamond\"><PERSON></a>, American economist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, German painter (d. 2009)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dick Chrysler\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dick Chrysler\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Baroness <PERSON>, English civil servant and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON><PERSON>\" title=\"<PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON>, Baroness <PERSON></a>, English civil servant and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON>, Baroness <PERSON></a>, English civil servant and academic", "links": [{"title": "<PERSON><PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>,_Baroness_<PERSON><PERSON><PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American country singer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Baroness <PERSON> of Thornton-le-Fylde, English union leader and politician (d. 2018)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Thornton-le-Fylde\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of Thornton-le-Fylde\"><PERSON>, Baroness <PERSON> of Thornton-le-Fylde</a>, English union leader and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Thornton-le-Fylde\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of Thornton-le-Fylde\"><PERSON>, Baroness <PERSON> of Thornton-le-Fylde</a>, English union leader and politician (d. 2018)", "links": [{"title": "<PERSON>, Baroness Dean of Thornton-le-Fylde", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_Dean_of_Thornton-<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, <PERSON>, English lawyer and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baroness <PERSON></a>, English lawyer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English lawyer and academic", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English footballer and businessman (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and businessman (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and businessman (d. 2023)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1945", "text": "<PERSON>, English bass guitarist (d. 2009)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass guitarist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass guitarist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, French singer-songwriter and violinist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American soul singer-songwriter (d. 1970)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soul singer-songwriter (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soul singer-songwriter (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American politician and lobbyist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and lobbyist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and lobbyist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter, guitarist and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American golfer and sportscaster", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American runner and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American judge", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, New Zealand journalist (d. 2013)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, New Zealand journalist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, New Zealand journalist (d. 2013)", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_(broadcaster)"}]}, {"year": "1950", "text": "<PERSON>, Australian director and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American social worker and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social worker and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social worker and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American race car driver (d. 2001)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Australian journalist and television host", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian journalist and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian journalist and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actress and comedian", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American baseball player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American flautist (d. 2017)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American flautist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American flautist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, British musician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American attorney and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American comedian, actor and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor, comedian, writer and singer (d. 2022)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, writer and singer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, writer and singer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rew"}]}, {"year": "1957", "text": "<PERSON>, British actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Samoan politician, 7th Prime Minister of Samoa", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Fiam%C4%93_<PERSON>_<PERSON>%CA%BBafa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Samoan politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Samoa\" title=\"Prime Minister of Samoa\">Prime Minister of Samoa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fiam%C4%93_<PERSON>_<PERSON>%CA%BBafa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Samoan politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Samoa\" title=\"Prime Minister of Samoa\">Prime Minister of Samoa</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fiam%C4%93_<PERSON>_<PERSON>%CA%BBafa"}, {"title": "Prime Minister of Samoa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Samoa"}]}, {"year": "1957", "text": "<PERSON>, American politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English footballer (d. 2013)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1958)\" title=\"<PERSON> (footballer, born 1958)\"><PERSON></a>, English footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1958)\" title=\"<PERSON> (footballer, born 1958)\"><PERSON></a>, English footballer (d. 2013)", "links": [{"title": "<PERSON> (footballer, born 1958)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1958)"}]}, {"year": "1958", "text": "<PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>b"}]}, {"year": "1960", "text": "<PERSON>, Canadian author and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English novelist, lyricist and journalist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, lyricist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Samson\"><PERSON></a>, English novelist, lyricist and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Italian-American actor, director, producer and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor, director, producer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor, director, producer and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Icelandic politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/L%C3%BA%C3%B0v%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%BA%C3%B0v%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ú<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%BA%C3%B0v%C3%AD<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, French geographer, author, and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French geographer, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French geographer, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American author (d. 2017)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, German violinist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Croatian politician and diplomat, 4th President of Croatia", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON><PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician and diplomat, 4th President of Croatia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician and diplomat, 4th President of Croatia", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-Kit<PERSON><PERSON>%C4%87"}]}, {"year": "1969", "text": "<PERSON>, American actor and writer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American tennis player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/April_<PERSON><PERSON>\" title=\"April Telek\">April <PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/April_<PERSON><PERSON>\" title=\"April Telek\">April <PERSON></a>, Canadian actress", "links": [{"title": "April Telek", "link": "https://wikipedia.org/wiki/April_Telek"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American author, editor and educator", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Mi<PERSON><PERSON>_<PERSON>stow\" title=\"<PERSON><PERSON><PERSON> Ostow\"><PERSON><PERSON><PERSON></a>, American author, editor and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>stow\" title=\"<PERSON><PERSON><PERSON> Ostow\"><PERSON><PERSON><PERSON></a>, American author, editor and educator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Micol_Ostow"}]}, {"year": "1976", "text": "<PERSON>, American basketball player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Shammgod\" title=\"God Shammgod\"><PERSON> Shammgod</a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Shammgod\" title=\"God Shammgod\"><PERSON> Shammgod</a>, American basketball player and coach", "links": [{"title": "God Shammgod", "link": "https://wikipedia.org/wiki/God_Shammgod"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Czech tennis player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hejdov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hejdov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uzana_Hejdov%C3%A1"}]}, {"year": "1977", "text": "<PERSON>, Danish international footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish international footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish international footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American film and television actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American film and television actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American film and television actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1978", "text": "<PERSON>, American tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter and musician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian actor and comedian", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, South Korean footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-gook\" title=\"<PERSON>-gook\"><PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-gook\" title=\"<PERSON>-gook\"><PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON>gook", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-gook"}]}, {"year": "1979", "text": "<PERSON>, English pop singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Meara\" title=\"<PERSON>\"><PERSON></a>, English pop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Meara\" title=\"<PERSON>\"><PERSON></a>, English pop singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Meara"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON> Biron\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON> Biron\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Canadian actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Northern Irish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Northern Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Northern Irish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1983", "text": "<PERSON>, American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer", "links": [{"title": "Kirby Cote", "link": "https://wikipedia.org/wiki/Kirby_Cote"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Russian tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, South Korean actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-han\" title=\"<PERSON><PERSON> Yo-han\"><PERSON><PERSON>han</a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-han\" title=\"By<PERSON> Yo-han\"><PERSON><PERSON>han</a>, South Korean actor", "links": [{"title": "<PERSON><PERSON>han", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-han"}]}, {"year": "1986", "text": "<PERSON>-young, South Korean actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>-young", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Italian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Jamaican cricketer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Hong Kong singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Algerian athlete", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>loufi\"><PERSON><PERSON><PERSON></a>, Algerian athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>loufi\"><PERSON><PERSON><PERSON></a>, Algerian athlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, South Korean singer-songwriter and record producer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer-songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer-songwriter and record producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}]}, {"year": "1989", "text": "<PERSON>, American political commentator and activist", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political commentator and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political commentator and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian cricketer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1990)\" title=\"<PERSON> (basketball, born 1990)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball,_born_1990)\" title=\"<PERSON> (basketball, born 1990)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1990)", "link": "https://wikipedia.org/wiki/<PERSON>_(basketball,_born_1990)"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)\" title=\"<PERSON> (footballer, born 1991)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)\" title=\"<PERSON> (footballer, born 1991)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1991)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)"}]}, {"year": "1991", "text": "<PERSON>, South Korean actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Japanese tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Do<PERSON>\"><PERSON><PERSON></a>, Japanese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, German paralympic equestrian", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German paralympic equestrian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German paralympic equestrian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, German tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Australian tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rell"}]}, {"year": "1998", "text": "<PERSON>, American soccer player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Austrian tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON> of Spain, Spanish princess", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Infanta_Sof%C3%ADa_of_Spain\" title=\"Infanta Sofía of Spain\">Infanta Sofía of Spain</a>, Spanish princess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Infanta_Sof%C3%ADa_of_Spain\" title=\"Infanta Sofía of Spain\">Infant<PERSON> Sofía of Spain</a>, Spanish princess", "links": [{"title": "Infanta Sofía of Spain", "link": "https://wikipedia.org/wiki/Infanta_Sof%C3%ADa_of_Spain"}]}], "Deaths": [{"year": "1109", "text": "<PERSON> of Cluny, French abbot (b. 1024)", "html": "1109 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cluny\" title=\"<PERSON> of Cluny\"><PERSON> of Cluny</a>, French abbot (b. 1024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Clu<PERSON>\" title=\"<PERSON> of Cluny\"><PERSON> of Cluny</a>, French abbot (b. 1024)", "links": [{"title": "<PERSON> of Cluny", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1380", "text": "<PERSON> of Siena, Italian mystic, philosopher and saint (b. 1347)", "html": "1380 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Siena\" title=\"<PERSON> of Siena\"><PERSON> of Siena</a>, Italian mystic, philosopher and saint (b. 1347)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Siena\" title=\"<PERSON> of Siena\"><PERSON> of Siena</a>, Italian mystic, philosopher and saint (b. 1347)", "links": [{"title": "<PERSON> of Siena", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Siena"}]}, {"year": "1594", "text": "<PERSON>, English bishop, lexicographer, and theologian (b. 1517)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop, lexicographer, and theologian (b. 1517)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop, lexicographer, and theologian (b. 1517)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1630", "text": "<PERSON><PERSON><PERSON><PERSON>, French soldier and poet (b. 1552)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/Agrippa_d%27Aubign%C3%A9\" title=\"Agrippa d'Aubigné\"><PERSON><PERSON><PERSON><PERSON> d'Aubigné</a>, French soldier and poet (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agrippa_d%27Aubign%C3%A9\" title=\"Agrippa d'Aubigné\"><PERSON><PERSON><PERSON><PERSON> d'Aubigné</a>, French soldier and poet (b. 1552)", "links": [{"title": "A<PERSON>rip<PERSON>", "link": "https://wikipedia.org/wiki/Agrippa_d%27Aubign%C3%A9"}]}, {"year": "1658", "text": "<PERSON>, English poet and author (b. 1613)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1613)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1676", "text": "<PERSON><PERSON><PERSON>, Dutch admiral (b. 1607)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch admiral (b. 1607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch admiral (b. 1607)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1707", "text": "<PERSON>, Irish-English actor and playwright (b. 1678)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English actor and playwright (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English actor and playwright (b. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, Swedish chemist and mineralogist (b. 1694)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish chemist and mineralogist (b. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish chemist and mineralogist (b. 1694)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "<PERSON>, English explorer and author (b. 1713)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(traveller)\" title=\"<PERSON> (traveller)\"><PERSON></a>, English explorer and author (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(traveller)\" title=\"<PERSON> (traveller)\"><PERSON></a>, English explorer and author (b. 1713)", "links": [{"title": "<PERSON> (traveller)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(traveller)"}]}, {"year": "1833", "text": "<PERSON>, Anglo-Irish physician and mineralogist (b. 1756)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Anglo-Irish <a href=\"https://wikipedia.org/wiki/Physician\" title=\"Physician\">physician</a> and <a href=\"https://wikipedia.org/wiki/Mineralogy\" title=\"Mineralogy\">mineralogist</a> (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Anglo-Irish <a href=\"https://wikipedia.org/wiki/Physician\" title=\"Physician\">physician</a> and <a href=\"https://wikipedia.org/wiki/Mineralogy\" title=\"Mineralogy\">mineralogist</a> (b. 1756)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}, {"title": "Physician", "link": "https://wikipedia.org/wiki/Physician"}, {"title": "Mineralogy", "link": "https://wikipedia.org/wiki/Mineralogy"}]}, {"year": "1848", "text": "<PERSON>, American politician (b. 1790)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chester_Ashley"}]}, {"year": "1854", "text": "<PERSON>, 1st Marquess of Anglesey, English field marshal and politician, Lord Lieutenant of Ireland (b. 1768)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Anglesey\" title=\"<PERSON>, 1st Marquess of Anglesey\"><PERSON>, 1st Marquess of Anglesey</a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Anglesey\" title=\"<PERSON>, 1st Marquess of Anglesey\"><PERSON>, 1st Marquess of Anglesey</a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1768)", "links": [{"title": "<PERSON>, 1st Marquess of Anglesey", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Anglesey"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1903", "text": "<PERSON>, Australian businessman and politician, 39th Mayor of Melbourne (b. 1830)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician, 39th <a href=\"https://wikipedia.org/wiki/Mayor_of_Melbourne\" class=\"mw-redirect\" title=\"Mayor of Melbourne\">Mayor of Melbourne</a> (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician, 39th <a href=\"https://wikipedia.org/wiki/Mayor_of_Melbourne\" class=\"mw-redirect\" title=\"Mayor of Melbourne\">Mayor of Melbourne</a> (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Melbourne", "link": "https://wikipedia.org/wiki/Mayor_of_Melbourne"}]}, {"year": "1903", "text": "<PERSON>, French-American anthropologist and zoologist (b. 1835)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American anthropologist and zoologist (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American anthropologist and zoologist (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Cuban pianist and composer (b. 1847)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban pianist and composer (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban pianist and composer (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Danish mathematician and academic (b. 1850)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish mathematician and academic (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish mathematician and academic (b. 1850)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, British actress, composer and director (b. 1860)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Florence_Farr\" title=\"Florence Farr\"><PERSON></a>, British actress, composer and director (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_Farr\" title=\"Florence Farr\"><PERSON></a>, British actress, composer and director (b. 1860)", "links": [{"title": "Florence Farr", "link": "https://wikipedia.org/wiki/Florence_Farr"}]}, {"year": "1922", "text": "<PERSON>, Irish American political boss (b. 1843)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish American political boss (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish American political boss (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American educator and physicist (b. 1869)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and physicist (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and physicist (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American journalist and author (b. 1871)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American politician (b. 1876)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Greek poet and journalist (b. 1863)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Constantine_P._<PERSON>avafy\" title=\"<PERSON>\"><PERSON></a>, Greek poet and journalist (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_P._Cavafy\" title=\"Constantine P. C<PERSON>fy\"><PERSON></a>, Greek poet and journalist (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Constantine_P._Cavafy"}]}, {"year": "1935", "text": "<PERSON>, American singer, songwriter and pianist (b. 1905)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and pianist (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and pianist (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actor and playwright (b. 1853)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Russian composer and violinist (b. 1886)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and violinist (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and violinist (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Spanish pianist (b. 1875)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Ricardo_Vi%C3%B1es\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ricardo_Vi%C3%B1es\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_Vi%C3%B1es"}]}, {"year": "1944", "text": "<PERSON>, American cinematographer (b. 1872)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Soviet violinist (b. 1871)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet violinist (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet violinist (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American economist and statistician (b. 1867)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and statistician (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and statistician (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Austrian-English philosopher and academic (b. 1889)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English philosopher and academic (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English philosopher and academic (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ludwig_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German field marshal (b. 1876)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/1956\" title=\"1956\">1956</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1956\" title=\"1956\">1956</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1876)", "links": [{"title": "1956", "link": "https://wikipedia.org/wiki/1956"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English soldier and Governor of Gibraltar (b. 1891)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English soldier and <a href=\"https://wikipedia.org/wiki/Governor_of_Gibraltar\" title=\"Governor of Gibraltar\">Governor of Gibraltar</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English soldier and <a href=\"https://wikipedia.org/wiki/Governor_of_Gibraltar\" title=\"Governor of Gibraltar\">Governor of Gibraltar</a> (b. 1891)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)"}, {"title": "Governor of Gibraltar", "link": "https://wikipedia.org/wiki/Governor_of_Gibraltar"}]}, {"year": "1966", "text": "<PERSON>, English physicist and engineer (b. 1875)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, English physicist and engineer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, English physicist and engineer (b. 1875)", "links": [{"title": "<PERSON> (physicist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)"}]}, {"year": "1966", "text": "<PERSON>, American actress and acting coach (b. 1909)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and acting coach (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and acting coach (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON> <PERSON><PERSON>, American singer-songwriter and guitarist (b. 1929)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. B. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1929)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Norwegian midwife (b. 1877)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian midwife (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian midwife (b. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Chinese dissident (b. 1932)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese dissident (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese dissident (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, German race car driver (b. 1913)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Turkish actor and director (b. 1892)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_E<PERSON>u%C4%9Frul\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor and director (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u%C4%9Frul\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor and director (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Muhsin_Ertu%C4%9Frul"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American author and illustrator (b. 1907)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ie_<PERSON>ky"}]}, {"year": "1980", "text": "<PERSON>, English-American director and producer (b. 1899)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hitchcock\"><PERSON></a>, English-American director and producer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hitchcock\"><PERSON></a>, English-American director and producer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, French actor, producer and screenwriter (b. 1907)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8res\" title=\"<PERSON>\"><PERSON></a>, French actor, producer and screenwriter (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8res\" title=\"<PERSON>\"><PERSON></a>, French actor, producer and screenwriter (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Raymond_Bussi%C3%A8res"}]}, {"year": "1992", "text": "<PERSON>, American actress (b. 1910)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American actor and director (b. 1909)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, American actor and director (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, American actor and director (b. 1909)", "links": [{"title": "<PERSON> (film director)", "link": "https://wikipedia.org/wiki/<PERSON>_(film_director)"}]}, {"year": "1993", "text": "<PERSON>, English guitarist, songwriter and producer (b. 1946)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter and producer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter and producer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American journalist and author (b. 1932)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Vietnamese lieutenant and politician, 2nd Prime Minister of Vietnam (b. 1906)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Ph%E1%BA%A1m_V%C4%83n_%C4%90%E1%BB%93ng\" title=\"Phạm Văn Đ<PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese lieutenant and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Vietnam\" title=\"Prime Minister of Vietnam\">Prime Minister of Vietnam</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ph%E1%BA%A1m_V%C4%83n_%C4%90%E1%BB%93ng\" title=\"Phạm Văn <PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese lieutenant and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Vietnam\" title=\"Prime Minister of Vietnam\">Prime Minister of Vietnam</a> (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ph%E1%BA%A1m_V%C4%83n_%C4%90%E1%BB%93ng"}, {"title": "Prime Minister of Vietnam", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Vietnam"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American physicist and academic (b. 1936)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON> Jr.</a>, American physicist and academic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American physicist and academic (b. 1936)", "links": [{"title": "<PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2002", "text": "<PERSON>, American race car driver and journalist (b. 1936)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and journalist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and journalist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Croatian Army general and Chief of the General Staff (b. 1919)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian Army general and Chief of the General Staff (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian Army general and Chief of the General Staff (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>-Major, British diplomat and civil servant (b. 1916)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-Major,_8th_Baron_<PERSON>\" title=\"<PERSON>-Major, 8th Baron <PERSON>\"><PERSON>-Major</a>, British diplomat and civil servant (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-Major,_8th_Baron_<PERSON>\" title=\"<PERSON>-Major, 8th Baron <PERSON>\"><PERSON>-Major</a>, British diplomat and civil servant (b. 1916)", "links": [{"title": "<PERSON>-Major, 8th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Major,_8th_Baron_<PERSON>r"}]}, {"year": "2005", "text": "<PERSON>, American screenwriter and producer (b. 1927)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American mathematician and academic (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Canadian-American economist and diplomat, United States Ambassador to India (b. 1908)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American economist and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_India\" class=\"mw-redirect\" title=\"United States Ambassador to India\">United States Ambassador to India</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American economist and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_India\" class=\"mw-redirect\" title=\"United States Ambassador to India\">United States Ambassador to India</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to India", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_India"}]}, {"year": "2007", "text": "<PERSON>, American baseball player (b. 1978)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, New Zealand cricketer and rugby player (b. 1940)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and rugby player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and rugby player (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Croatian politician, 7th Prime Minister of Croatia (b. 1944)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Croatia\" title=\"Prime Minister of Croatia\">Prime Minister of Croatia</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Croatia\" title=\"Prime Minister of Croatia\">Prime Minister of Croatia</a> (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ivica_Ra%C4%8Dan"}, {"title": "Prime Minister of Croatia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Croatia"}]}, {"year": "2008", "text": "<PERSON>, English-American footballer (b. 1933)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American footballer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American footballer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Swiss chemist and academic (b. 1906)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON><PERSON>, French-Israeli artist, printmaker and art historian (b. 1929)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-Israeli artist, printmaker and art historian (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-Israeli artist, printmaker and art historian (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Iranian journalist and critic (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Siamak_Pourzand\" title=\"Siamak Pourzand\"><PERSON><PERSON><PERSON></a>, Iranian journalist and critic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siamak_<PERSON>urzand\" title=\"Siamak Pourzand\"><PERSON><PERSON><PERSON></a>, Iranian journalist and critic (b. 1931)", "links": [{"title": "Siamak Po<PERSON>zand", "link": "https://wikipedia.org/wiki/Siamak_Pourzand"}]}, {"year": "2011", "text": "<PERSON>, American writer, academic and radical feminist (b. 1937)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, academic and radical feminist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, academic and radical feminist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Libyan politician, 22nd Prime Minister of Libya (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Libyan politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Libya\" class=\"mw-redirect\" title=\"Prime Minister of Libya\">Prime Minister of Libya</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Libyan politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Libya\" class=\"mw-redirect\" title=\"Prime Minister of Libya\">Prime Minister of Libya</a> (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Libya", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Libya"}]}, {"year": "2012", "text": "<PERSON>, American composer and conductor (b. 1957)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>. French engineer, invented the smart card (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. French engineer, invented the <a href=\"https://wikipedia.org/wiki/Smart_card\" title=\"Smart card\">smart card</a> (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. French engineer, invented the <a href=\"https://wikipedia.org/wiki/Smart_card\" title=\"Smart card\">smart card</a> (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Smart card", "link": "https://wikipedia.org/wiki/Smart_card"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter (b. 1926)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2013", "text": "<PERSON>, New Zealand-Australian rugby player (b. 1992)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian rugby player (b. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian rugby player (b. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Israeli politician, 13th Israel Minister of Agriculture (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Grup<PERSON>\" title=\"<PERSON><PERSON><PERSON> Grupper\"><PERSON><PERSON><PERSON></a>, Israeli politician, 13th <a href=\"https://wikipedia.org/wiki/Ministry_of_Agriculture_and_Rural_Development_(Israel)\" class=\"mw-redirect\" title=\"Ministry of Agriculture and Rural Development (Israel)\">Israel Minister of Agriculture</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ah Grupper\"><PERSON><PERSON><PERSON></a>, Israeli politician, 13th <a href=\"https://wikipedia.org/wiki/Ministry_of_Agriculture_and_Rural_Development_(Israel)\" class=\"mw-redirect\" title=\"Ministry of Agriculture and Rural Development (Israel)\">Israel Minister of Agriculture</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pesah_Grupper"}, {"title": "Ministry of Agriculture and Rural Development (Israel)", "link": "https://wikipedia.org/wiki/Ministry_of_Agriculture_and_Rural_Development_(Israel)"}]}, {"year": "2013", "text": "<PERSON>, American pianist and composer (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English footballer (b. 1958)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1958)\" title=\"<PERSON> (footballer, born 1958)\"><PERSON></a>, English footballer (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1958)\" title=\"<PERSON> (footballer, born 1958)\"><PERSON></a>, English footballer (b. 1958)", "links": [{"title": "<PERSON> (footballer, born 1958)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1958)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Greek pole vaulter (b. 1990)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek pole vaulter (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek pole vaulter (b. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Czech singer and actress (b. 1966)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ivet<PERSON>_<PERSON><PERSON>%C5%A1ov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech singer and actress (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ivet<PERSON>_<PERSON>%C5%A1ov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech singer and actress (b. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iveta_Barto%C5%A1ov%C3%A1"}]}, {"year": "2014", "text": "<PERSON>, American author and illustrator (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ldstein"}]}, {"year": "2014", "text": "<PERSON>, English actor (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, French businessman (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American businesswoman, co-founded Weight Watchers (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/WW_International\" title=\"WW International\">Weight Watchers</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/WW_International\" title=\"WW International\">Weight Watchers</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "WW International", "link": "https://wikipedia.org/wiki/WW_International"}]}, {"year": "2015", "text": "<PERSON>, American golfer (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American lawyer and politician, 36th Governor of Illinois (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Governor of Illinois</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Illinois\" title=\"Governor of Illinois\">Governor of Illinois</a> (b. 1922)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Governor of Illinois", "link": "https://wikipedia.org/wiki/Governor_of_Illinois"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Filipino lawyer and jurist, 23rd Chief Justice of the Supreme Court of the Philippines (b. 1948)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Renato Corona\"><PERSON><PERSON></a>, Filipino lawyer and jurist, 23rd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines\" class=\"mw-redirect\" title=\"Chief Justice of the Supreme Court of the Philippines\">Chief Justice of the Supreme Court of the Philippines</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>rona\"><PERSON><PERSON></a>, Filipino lawyer and jurist, 23rd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines\" class=\"mw-redirect\" title=\"Chief Justice of the Supreme Court of the Philippines\">Chief Justice of the Supreme Court of the Philippines</a> (b. 1948)", "links": [{"title": "Renato Corona", "link": "https://wikipedia.org/wiki/Renato_Corona"}, {"title": "Chief Justice of the Supreme Court of the Philippines", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Indian bureaucrat and activist (b. 1939)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian bureaucrat and activist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian bureaucrat and activist (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Bolivian general, 57th President of Bolivia (b. 1929)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Meza\" title=\"<PERSON>\"><PERSON></a>, Bolivian general, 57th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Me<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian general, 57th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Garc%C3%ADa_Meza"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "2018", "text": "<PERSON>, British politician (b. 1945)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Springburn\" title=\"<PERSON>, Baron <PERSON> of Springburn\"><PERSON></a>, British politician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Springburn\" title=\"<PERSON>, Baron <PERSON> of Springburn\"><PERSON></a>, British politician (b. 1945)", "links": [{"title": "<PERSON>, Baron <PERSON> of Springburn", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Springburn"}]}, {"year": "2019", "text": "<PERSON>, Czech footballer (b. 1990)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0ural\" title=\"<PERSON>\"><PERSON></a>, Czech footballer (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0ural\" title=\"<PERSON>\"><PERSON></a>, Czech footballer (b. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Josef_%C5%A0ural"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, Indian actor (b. 1967)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (b. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irr<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Mexican astronomer and astrophysicist (b. 1921)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCnch\" title=\"<PERSON>\"><PERSON></a>, Mexican astronomer and astrophysicist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCnch\" title=\"<PERSON>\"><PERSON></a>, Mexican astronomer and astrophysicist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guido_M%C3%BCnch"}]}, {"year": "2021", "text": "<PERSON><PERSON>, English author (b. 1945)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American actress and writer (b. 1934)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and writer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and writer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Indian-American development economist (b. 1931)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American development economist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American development economist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}