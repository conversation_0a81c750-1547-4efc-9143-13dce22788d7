{"date": "September 6", "url": "https://wikipedia.org/wiki/September_6", "data": {"Events": [{"year": "394", "text": "Battle of the Frigidus: Roman emperor <PERSON><PERSON><PERSON> I defeats and kills <PERSON><PERSON><PERSON> the usurper. His Frankish magister milit<PERSON> <PERSON><PERSON><PERSON><PERSON> escapes but commits suicide two days later.", "html": "394 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Frigidus\" title=\"Battle of the Frigidus\">Battle of the Frigidus</a>: <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ius_I\" title=\"Theodosius I\"><PERSON><PERSON><PERSON> I</a> defeats and kills <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> the usurper. His <a href=\"https://wikipedia.org/wiki/Franks\" title=\"<PERSON>\">Frankish</a> <i><a href=\"https://wikipedia.org/wiki/Magister_militum\" title=\"Magister militum\">magister militum</a></i> <a href=\"https://wikipedia.org/wiki/Arbogast_(magister_militum)\" title=\"Arbogast (magister militum)\"><PERSON>rb<PERSON><PERSON></a> escapes but commits suicide two days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Frigidus\" title=\"Battle of the Frigidus\">Battle of the Frigidus</a>: <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/Theo<PERSON>ius_I\" title=\"Theodosius I\"><PERSON><PERSON><PERSON> I</a> defeats and kills <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> the usurper. His <a href=\"https://wikipedia.org/wiki/Franks\" title=\"<PERSON>\">Frankish</a> <i><a href=\"https://wikipedia.org/wiki/Magister_militum\" title=\"Magister militum\">magister militum</a></i> <a href=\"https://wikipedia.org/wiki/Arbogast_(magister_militum)\" title=\"Arbogast (magister militum)\">A<PERSON><PERSON><PERSON></a> escapes but commits suicide two days later.", "links": [{"title": "Battle of the Frigidus", "link": "https://wikipedia.org/wiki/Battle_of_the_Frigidus"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franks"}, {"title": "Magister militum", "link": "https://wikipedia.org/wiki/Magister_militum"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (magister militum)", "link": "https://wikipedia.org/wiki/Arbogast_(magister_militum)"}]}, {"year": "1492", "text": "<PERSON> sails from La Gomera in the Canary Islands, his final port of call before crossing the Atlantic Ocean for the first time.", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sails from <a href=\"https://wikipedia.org/wiki/La_Gomera\" title=\"La Gomera\">La Gomera</a> in the <a href=\"https://wikipedia.org/wiki/Canary_Islands\" title=\"Canary Islands\">Canary Islands</a>, his final port of call before crossing the Atlantic Ocean for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christopher<PERSON>\" title=\"<PERSON>\"><PERSON></a> sails from <a href=\"https://wikipedia.org/wiki/La_Gomera\" title=\"La Gomera\">La Gomera</a> in the <a href=\"https://wikipedia.org/wiki/Canary_Islands\" title=\"Canary Islands\">Canary Islands</a>, his final port of call before crossing the Atlantic Ocean for the first time.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "La Gomera", "link": "https://wikipedia.org/wiki/La_Gomera"}, {"title": "Canary Islands", "link": "https://wikipedia.org/wiki/Canary_Islands"}]}, {"year": "1522", "text": "The Victoria returns to Sanlúcar de Barrameda in Spain, the only surviving ship of <PERSON>'s expedition and the first known ship to circumnavigate the world.", "html": "1522 - The <i><a href=\"https://wikipedia.org/wiki/Victoria_(ship)\" title=\"Victoria (ship)\">Victoria</a></i> returns to <a href=\"https://wikipedia.org/wiki/Sanl%C3%<PERSON>car_de_Barrameda\" title=\"<PERSON><PERSON><PERSON><PERSON> de Barrameda\"><PERSON><PERSON><PERSON><PERSON> Barrameda</a> in Spain, the only surviving ship of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s expedition and the first known ship to <a href=\"https://wikipedia.org/wiki/Circumnavigate\" class=\"mw-redirect\" title=\"Circumnavigate\">circumnavigate</a> the world.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Victoria_(ship)\" title=\"Victoria (ship)\">Victoria</a></i> returns to <a href=\"https://wikipedia.org/wiki/Sanl%C3%BAcar_de_Barrameda\" title=\"<PERSON><PERSON><PERSON><PERSON> de Barrameda\"><PERSON><PERSON><PERSON><PERSON> Barrameda</a> in Spain, the only surviving ship of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s expedition and the first known ship to <a href=\"https://wikipedia.org/wiki/Circumnavigate\" class=\"mw-redirect\" title=\"Circumnavigate\">circumnavigate</a> the world.", "links": [{"title": "Victoria (ship)", "link": "https://wikipedia.org/wiki/Victoria_(ship)"}, {"title": "<PERSON><PERSON><PERSON><PERSON> Barrameda", "link": "https://wikipedia.org/wiki/Sanl%C3%BAcar_de_Barrameda"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Circumnavigate", "link": "https://wikipedia.org/wiki/Circumnavigate"}]}, {"year": "1620", "text": "The Pilgrims sail from Plymouth, England on the Mayflower to settle in North America. (Old Style date; September 16 per New Style date.)", "html": "1620 - The <a href=\"https://wikipedia.org/wiki/Pilgrim_Fathers\" class=\"mw-redirect\" title=\"Pilgrim Fathers\">Pilgrims</a> sail from <a href=\"https://wikipedia.org/wiki/Plymouth\" title=\"Plymouth\">Plymouth</a>, England on the <i><a href=\"https://wikipedia.org/wiki/Mayflower\" title=\"Mayflower\">Mayflower</a></i> to settle in North America. (<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">Old Style</a> date; <a href=\"https://wikipedia.org/wiki/September_16\" title=\"September 16\">September 16</a> per <a href=\"https://wikipedia.org/wiki/New_Style\" class=\"mw-redirect\" title=\"New Style\">New Style</a> date.)", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pilgrim_Fathers\" class=\"mw-redirect\" title=\"Pilgrim Fathers\">Pilgrims</a> sail from <a href=\"https://wikipedia.org/wiki/Plymouth\" title=\"Plymouth\">Plymouth</a>, England on the <i><a href=\"https://wikipedia.org/wiki/Mayflower\" title=\"Mayflower\">Mayflower</a></i> to settle in North America. (<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">Old Style</a> date; <a href=\"https://wikipedia.org/wiki/September_16\" title=\"September 16\">September 16</a> per <a href=\"https://wikipedia.org/wiki/New_Style\" class=\"mw-redirect\" title=\"New Style\">New Style</a> date.)", "links": [{"title": "Pilgrim Fathers", "link": "https://wikipedia.org/wiki/Pilgrim_Fathers"}, {"title": "Plymouth", "link": "https://wikipedia.org/wiki/Plymouth"}, {"title": "May<PERSON>", "link": "https://wikipedia.org/wiki/Mayflower"}, {"title": "Old Style", "link": "https://wikipedia.org/wiki/Old_Style"}, {"title": "September 16", "link": "https://wikipedia.org/wiki/September_16"}, {"title": "New Style", "link": "https://wikipedia.org/wiki/New_Style"}]}, {"year": "1628", "text": "Puritans settle Salem, which became part of Massachusetts Bay Colony.", "html": "1628 - <a href=\"https://wikipedia.org/wiki/Puritans\" title=\"Puritans\">Puritans</a> settle <a href=\"https://wikipedia.org/wiki/Salem,_Massachusetts\" title=\"Salem, Massachusetts\">Salem</a>, which became part of <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Puritans\" title=\"Puritans\">Puritans</a> settle <a href=\"https://wikipedia.org/wiki/Salem,_Massachusetts\" title=\"Salem, Massachusetts\">Salem</a>, which became part of <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a>.", "links": [{"title": "P<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Puritans"}, {"title": "Salem, Massachusetts", "link": "https://wikipedia.org/wiki/Salem,_Massachusetts"}, {"title": "Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Massachusetts_Bay_Colony"}]}, {"year": "1634", "text": "Thirty Years' War: In the Battle of Nördlingen, the Catholic Imperial army defeats Swedish and German Protestant forces.", "html": "1634 - <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_N%C3%B6rdlingen_(1634)\" title=\"Battle of Nördlingen (1634)\">Battle of Nördlingen</a>, the Catholic Imperial army defeats Swedish and German Protestant forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_N%C3%B6rdlingen_(1634)\" title=\"Battle of Nördlingen (1634)\">Battle of Nördlingen</a>, the Catholic Imperial army defeats Swedish and German Protestant forces.", "links": [{"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}, {"title": "Battle of Nördlingen (1634)", "link": "https://wikipedia.org/wiki/Battle_of_N%C3%B6rdlingen_(1634)"}]}, {"year": "1781", "text": "American Revolutionary War: The Battle of Groton Heights takes place, resulting in a British victory.", "html": "1781 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Groton_Heights\" title=\"Battle of Groton Heights\">Battle of Groton Heights</a> takes place, resulting in a British victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Groton_Heights\" title=\"Battle of Groton Heights\">Battle of Groton Heights</a> takes place, resulting in a British victory.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Groton Heights", "link": "https://wikipedia.org/wiki/Battle_of_Groton_Heights"}]}, {"year": "1803", "text": "British scientist <PERSON> begins using symbols to represent the atoms of different elements.", "html": "1803 - British scientist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins using symbols to represent the atoms of different elements.", "no_year_html": "British scientist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins using symbols to represent the atoms of different elements.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "American Civil War: Forces under Union General <PERSON> bloodlessly capture Paducah, Kentucky, giving the Union control of the Tennessee River's mouth.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Forces under <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> General <a href=\"https://wikipedia.org/wiki/Ulysses_<PERSON>_Grant\" title=\"Ulysses <PERSON> Grant\">Ulysses <PERSON></a> bloodlessly capture <a href=\"https://wikipedia.org/wiki/Paducah,_Kentucky\" title=\"Paducah, Kentucky\">Paducah, Kentucky</a>, giving the Union control of the <a href=\"https://wikipedia.org/wiki/Tennessee_River\" title=\"Tennessee River\">Tennessee River</a>'s mouth.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Forces under <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> General <a href=\"https://wikipedia.org/wiki/Ulysses_<PERSON>_<PERSON>\" title=\"Ulysses <PERSON> Grant\">Ulysses <PERSON></a> bloodlessly capture <a href=\"https://wikipedia.org/wiki/Paducah,_Kentucky\" title=\"Paducah, Kentucky\">Paducah, Kentucky</a>, giving the Union control of the <a href=\"https://wikipedia.org/wiki/Tennessee_River\" title=\"Tennessee River\">Tennessee River</a>'s mouth.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Paducah, Kentucky", "link": "https://wikipedia.org/wiki/Paducah,_Kentucky"}, {"title": "Tennessee River", "link": "https://wikipedia.org/wiki/Tennessee_River"}]}, {"year": "1863", "text": "American Civil War: Confederate forces evacuate Battery Wagner and Morris Island in South Carolina.", "html": "1863 - American Civil War: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate forces</a> evacuate <a href=\"https://wikipedia.org/wiki/Fort_Wagner\" title=\"Fort Wagner\">Battery Wagner</a> and <a href=\"https://wikipedia.org/wiki/Morris_Island\" title=\"Morris Island\">Morris Island</a> in <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate forces</a> evacuate <a href=\"https://wikipedia.org/wiki/Fort_Wagner\" title=\"Fort Wagner\">Battery Wagner</a> and <a href=\"https://wikipedia.org/wiki/Morris_Island\" title=\"Morris Island\">Morris Island</a> in <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>.", "links": [{"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Fort Wagner", "link": "https://wikipedia.org/wiki/Fort_Wagner"}, {"title": "Morris Island", "link": "https://wikipedia.org/wiki/Morris_Island"}, {"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}]}, {"year": "1870", "text": "<PERSON> of Laramie, Wyoming becomes the first woman in the United States to cast a vote legally after 1807.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Laramie,_Wyoming\" title=\"Laramie, Wyoming\">Laramie, Wyoming</a> becomes the first woman in the United States to cast a vote legally after <a href=\"https://wikipedia.org/wiki/1807\" title=\"1807\">1807</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Laramie,_Wyoming\" title=\"Laramie, Wyoming\">Laramie, Wyoming</a> becomes the first woman in the United States to cast a vote legally after <a href=\"https://wikipedia.org/wiki/1807\" title=\"1807\">1807</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Laramie, Wyoming", "link": "https://wikipedia.org/wiki/Laramie,_Wyoming"}, {"title": "1807", "link": "https://wikipedia.org/wiki/1807"}]}, {"year": "1885", "text": "Eastern Rumelia declares its union with Bulgaria, thus accomplishing Bulgarian unification.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Eastern_Rumelia\" title=\"Eastern Rumelia\">Eastern Rumelia</a> declares its union with <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, thus accomplishing <a href=\"https://wikipedia.org/wiki/Bulgarian_unification\" title=\"Bulgarian unification\">Bulgarian unification</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eastern_Rumelia\" title=\"Eastern Rumelia\">Eastern Rumelia</a> declares its union with <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, thus accomplishing <a href=\"https://wikipedia.org/wiki/Bulgarian_unification\" title=\"Bulgarian unification\">Bulgarian unification</a>.", "links": [{"title": "Eastern Rumelia", "link": "https://wikipedia.org/wiki/Eastern_Rumelia"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "Bulgarian unification", "link": "https://wikipedia.org/wiki/Bulgarian_unification"}]}, {"year": "1901", "text": "<PERSON>, an unemployed anarchist, shoots and fatally wounds US President <PERSON> at the Pan-American Exposition in Buffalo, New York.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, an unemployed <a href=\"https://wikipedia.org/wiki/Anarchism\" title=\"Anarchism\">anarchist</a>, <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>\" title=\"Assassination of <PERSON>\">shoots and fatally wounds</a> <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">US President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Pan-American_Exposition\" title=\"Pan-American Exposition\">Pan-American Exposition</a> in <a href=\"https://wikipedia.org/wiki/Buffalo,_New_York\" title=\"Buffalo, New York\">Buffalo, New York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, an unemployed <a href=\"https://wikipedia.org/wiki/Anarchism\" title=\"Anarchism\">anarchist</a>, <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>\" title=\"Assassination of William <PERSON>\">shoots and fatally wounds</a> <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">US President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Pan-American_Exposition\" title=\"Pan-American Exposition\">Pan-American Exposition</a> in <a href=\"https://wikipedia.org/wiki/Buffalo,_New_York\" title=\"Buffalo, New York\">Buffalo, New York</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}, {"title": "Anarchism", "link": "https://wikipedia.org/wiki/Anarchism"}, {"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pan-American Exposition", "link": "https://wikipedia.org/wiki/Pan-American_Exposition"}, {"title": "Buffalo, New York", "link": "https://wikipedia.org/wiki/Buffalo,_New_York"}]}, {"year": "1914", "text": "World War I: The First Battle of the Marne, which would halt the Imperial German Army's advance into France, begins.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/First_Battle_of_the_Marne\" title=\"First Battle of the Marne\">First Battle of the Marne</a>, which would halt the <a href=\"https://wikipedia.org/wiki/Imperial_German_Army\" title=\"Imperial German Army\">Imperial German Army</a>'s advance into France, begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/First_Battle_of_the_Marne\" title=\"First Battle of the Marne\">First Battle of the Marne</a>, which would halt the <a href=\"https://wikipedia.org/wiki/Imperial_German_Army\" title=\"Imperial German Army\">Imperial German Army</a>'s advance into France, begins.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "First Battle of the Marne", "link": "https://wikipedia.org/wiki/First_Battle_of_the_Marne"}, {"title": "Imperial German Army", "link": "https://wikipedia.org/wiki/Imperial_German_Army"}]}, {"year": "1915", "text": "World War I: The first tank prototype, developed by William Foster & Co. for the British army, was completed and given its first test drive.", "html": "1915 - World War I: The <a href=\"https://wikipedia.org/wiki/British_heavy_tanks_of_the_First_World_War\" title=\"British heavy tanks of the First World War\">first tank</a> prototype, developed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Co.\" title=\"William Foster &amp; Co.\">William <PERSON> &amp; Co.</a> for the British army, was completed and given its first test drive.", "no_year_html": "World War I: The <a href=\"https://wikipedia.org/wiki/British_heavy_tanks_of_the_First_World_War\" title=\"British heavy tanks of the First World War\">first tank</a> prototype, developed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Co.\" title=\"William Foster &amp; Co.\">William <PERSON> &amp; Co.</a> for the British army, was completed and given its first test drive.", "links": [{"title": "British heavy tanks of the First World War", "link": "https://wikipedia.org/wiki/British_heavy_tanks_of_the_First_World_War"}, {"title": "William Foster & Co.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Co."}]}, {"year": "1930", "text": "Democratically elected Argentine president <PERSON><PERSON><PERSON><PERSON> is deposed in a military coup.", "html": "1930 - Democratically elected <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentine</a> president <a href=\"https://wikipedia.org/wiki/Hip%C3%B3lito_Y<PERSON>oyen\" title=\"Hipólito Yrigoyen\"><PERSON><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1930_Argentine_coup_d%27%C3%A9tat\" title=\"1930 Argentine coup d'état\">deposed in a military coup</a>.", "no_year_html": "Democratically elected <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentine</a> president <a href=\"https://wikipedia.org/wiki/Hip%C3%B3lito_Yrigoyen\" title=\"Hipólito Yrigoyen\"><PERSON><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1930_Argentine_coup_d%27%C3%A9tat\" title=\"1930 Argentine coup d'état\">deposed in a military coup</a>.", "links": [{"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hip%C3%B3lito_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "1930 Argentine coup d'état", "link": "https://wikipedia.org/wiki/1930_Argentine_coup_d%27%C3%A9tat"}]}, {"year": "1936", "text": "Spanish Civil War: The Interprovincial Council of Asturias and León is established.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Interprovincial_Council_of_Asturias_and_Le%C3%B3n\" class=\"mw-redirect\" title=\"Interprovincial Council of Asturias and León\">Interprovincial Council of Asturias and León</a> is established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Interprovincial_Council_of_Asturias_and_Le%C3%B3n\" class=\"mw-redirect\" title=\"Interprovincial Council of Asturias and León\">Interprovincial Council of Asturias and León</a> is established.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Interprovincial Council of Asturias and León", "link": "https://wikipedia.org/wiki/Interprovincial_Council_of_Asturias_and_Le%C3%B3n"}]}, {"year": "1939", "text": "World War II: The British Royal Air Force suffers its first fighter pilot casualty of the Second World War at the Battle of Barking Creek as a result of friendly fire.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The British <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> suffers its first fighter pilot casualty of the Second World War at the <a href=\"https://wikipedia.org/wiki/Battle_of_Barking_Creek\" title=\"Battle of Barking Creek\">Battle of Barking Creek</a> as a result of friendly fire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The British <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> suffers its first fighter pilot casualty of the Second World War at the <a href=\"https://wikipedia.org/wiki/Battle_of_Barking_Creek\" title=\"Battle of Barking Creek\">Battle of Barking Creek</a> as a result of friendly fire.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}, {"title": "Battle of Barking Creek", "link": "https://wikipedia.org/wiki/Battle_of_Barking_Creek"}]}, {"year": "1939", "text": "World War II: Union of South Africa declares war on Germany.", "html": "1939 - World War II: <a href=\"https://wikipedia.org/wiki/Union_of_South_Africa\" title=\"Union of South Africa\">Union of South Africa</a> <a href=\"https://wikipedia.org/wiki/Military_history_of_South_Africa_during_World_War_II\" title=\"Military history of South Africa during World War II\">declares war</a> on Germany.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Union_of_South_Africa\" title=\"Union of South Africa\">Union of South Africa</a> <a href=\"https://wikipedia.org/wiki/Military_history_of_South_Africa_during_World_War_II\" title=\"Military history of South Africa during World War II\">declares war</a> on Germany.", "links": [{"title": "Union of South Africa", "link": "https://wikipedia.org/wiki/Union_of_South_Africa"}, {"title": "Military history of South Africa during World War II", "link": "https://wikipedia.org/wiki/Military_history_of_South_Africa_during_World_War_II"}]}, {"year": "1940", "text": "King <PERSON> of Romania abdicates and is succeeded by his son <PERSON>. General <PERSON> becomes the Conducător of Romania.", "html": "1940 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania\" title=\"<PERSON> of Romania\"><PERSON> of Romania</a> abdicates and is succeeded by his son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania\" title=\"<PERSON> of Romania\">Michael</a>. General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the <i><a href=\"https://wikipedia.org/wiki/Conduc%C4%83tor\" title=\"Conducător\">Condu<PERSON><PERSON>tor</a></i> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania\" title=\"<PERSON> of Romania\"><PERSON> of Romania</a> abdicates and is succeeded by his son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania\" title=\"<PERSON> of Romania\">Michael</a>. General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the <i><a href=\"https://wikipedia.org/wiki/Conduc%C4%83tor\" title=\"Conducător\">Conduc<PERSON>tor</a></i> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>.", "links": [{"title": "<PERSON> of Romania", "link": "https://wikipedia.org/wiki/Carol_II_of_Romania"}, {"title": "<PERSON> of Romania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Romania"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Conduc%C4%83tor"}, {"title": "Kingdom of Romania", "link": "https://wikipedia.org/wiki/Kingdom_of_Romania"}]}, {"year": "1943", "text": "The Monterrey Institute of Technology is founded in Monterrey, Mexico as one of the largest and most influential private universities in Latin America.", "html": "1943 - The <a href=\"https://wikipedia.org/wiki/Monterrey_Institute_of_Technology\" class=\"mw-redirect\" title=\"Monterrey Institute of Technology\">Monterrey Institute of Technology</a> is founded in <a href=\"https://wikipedia.org/wiki/Monterrey\" title=\"Monterrey\">Monterrey</a>, Mexico as one of the largest and most influential private universities in Latin America.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Monterrey_Institute_of_Technology\" class=\"mw-redirect\" title=\"Monterrey Institute of Technology\">Monterrey Institute of Technology</a> is founded in <a href=\"https://wikipedia.org/wiki/Monterrey\" title=\"Monterrey\">Monterrey</a>, Mexico as one of the largest and most influential private universities in Latin America.", "links": [{"title": "Monterrey Institute of Technology", "link": "https://wikipedia.org/wiki/Monterrey_Institute_of_Technology"}, {"title": "Monterrey", "link": "https://wikipedia.org/wiki/Monterrey"}]}, {"year": "1943", "text": "Pennsylvania Railroad's premier train derails at Frankford Junction in Philadelphia, killing 79 people and injuring 117 others.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Pennsylvania_Railroad\" title=\"Pennsylvania Railroad\">Pennsylvania Railroad</a>'s premier train <a href=\"https://wikipedia.org/wiki/1943_Frankford_Junction_train_wreck\" title=\"1943 Frankford Junction train wreck\">derails</a> at <a href=\"https://wikipedia.org/wiki/Frankford_Junction\" class=\"mw-redirect\" title=\"Frankford Junction\">Frankford Junction</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, killing 79 people and injuring 117 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pennsylvania_Railroad\" title=\"Pennsylvania Railroad\">Pennsylvania Railroad</a>'s premier train <a href=\"https://wikipedia.org/wiki/1943_Frankford_Junction_train_wreck\" title=\"1943 Frankford Junction train wreck\">derails</a> at <a href=\"https://wikipedia.org/wiki/Frankford_Junction\" class=\"mw-redirect\" title=\"Frankford Junction\">Frankford Junction</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, killing 79 people and injuring 117 others.", "links": [{"title": "Pennsylvania Railroad", "link": "https://wikipedia.org/wiki/Pennsylvania_Railroad"}, {"title": "1943 Frankford Junction train wreck", "link": "https://wikipedia.org/wiki/1943_Frankford_Junction_train_wreck"}, {"title": "Frankford Junction", "link": "https://wikipedia.org/wiki/Frankford_Junction"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "1944", "text": "World War II: The city of Ypres, Belgium is liberated by Allied forces.", "html": "1944 - World War II: The city of <a href=\"https://wikipedia.org/wiki/Ypres\" title=\"Ypres\">Ypres</a>, Belgium is liberated by Allied forces.", "no_year_html": "World War II: The city of <a href=\"https://wikipedia.org/wiki/Ypres\" title=\"Ypres\">Ypres</a>, Belgium is liberated by Allied forces.", "links": [{"title": "Ypres", "link": "https://wikipedia.org/wiki/Ypres"}]}, {"year": "1944", "text": "World War II: Soviet forces capture the city of Tartu, Estonia.", "html": "1944 - World War II: Soviet forces <a href=\"https://wikipedia.org/wiki/Tartu_Offensive\" class=\"mw-redirect\" title=\"Tartu Offensive\">capture</a> the city of <a href=\"https://wikipedia.org/wiki/Tartu\" title=\"Tartu\">Tartu</a>, <a href=\"https://wikipedia.org/wiki/Estonian_Soviet_Socialist_Republic\" title=\"Estonian Soviet Socialist Republic\">Estonia</a>.", "no_year_html": "World War II: Soviet forces <a href=\"https://wikipedia.org/wiki/Tartu_Offensive\" class=\"mw-redirect\" title=\"Tartu Offensive\">capture</a> the city of <a href=\"https://wikipedia.org/wiki/Tartu\" title=\"Tartu\">Tartu</a>, <a href=\"https://wikipedia.org/wiki/Estonian_Soviet_Socialist_Republic\" title=\"Estonian Soviet Socialist Republic\">Estonia</a>.", "links": [{"title": "Tartu Offensive", "link": "https://wikipedia.org/wiki/Tartu_Offensive"}, {"title": "Tartu", "link": "https://wikipedia.org/wiki/Tartu"}, {"title": "Estonian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Estonian_Soviet_Socialist_Republic"}]}, {"year": "1946", "text": "United States Secretary of State <PERSON> announces that the U.S. will follow a policy of economic reconstruction in postwar Germany.", "html": "1946 - United States Secretary of State <PERSON> <a href=\"https://wikipedia.org/wiki/Restatement_of_Policy_on_Germany\" title=\"Restatement of Policy on Germany\">announces</a> that the U.S. will follow a policy of economic reconstruction in postwar Germany.", "no_year_html": "United States Secretary of State <PERSON> <a href=\"https://wikipedia.org/wiki/Restatement_of_Policy_on_Germany\" title=\"Restatement of Policy on Germany\">announces</a> that the U.S. will follow a policy of economic reconstruction in postwar Germany.", "links": [{"title": "Restatement of Policy on Germany", "link": "https://wikipedia.org/wiki/Restatement_of_Policy_on_Germany"}]}, {"year": "1952", "text": "A prototype aircraft crashes at the Farnborough Airshow in Hampshire, England, killing 29 spectators and the two on board.", "html": "1952 - A prototype aircraft <a href=\"https://wikipedia.org/wiki/1952_Farnborough_Airshow_DH.110_crash\" class=\"mw-redirect\" title=\"1952 Farnborough Airshow DH.110 crash\">crashes</a> at the <a href=\"https://wikipedia.org/wiki/Farnborough_Airshow\" class=\"mw-redirect\" title=\"Farnborough Airshow\">Farnborough Airshow</a> in <a href=\"https://wikipedia.org/wiki/Hampshire\" title=\"Hampshire\">Hampshire</a>, England, killing 29 spectators and the two on board.", "no_year_html": "A prototype aircraft <a href=\"https://wikipedia.org/wiki/1952_Farnborough_Airshow_DH.110_crash\" class=\"mw-redirect\" title=\"1952 Farnborough Airshow DH.110 crash\">crashes</a> at the <a href=\"https://wikipedia.org/wiki/Farnborough_Airshow\" class=\"mw-redirect\" title=\"Farnborough Airshow\">Farnborough Airshow</a> in <a href=\"https://wikipedia.org/wiki/Hampshire\" title=\"Hampshire\">Hampshire</a>, England, killing 29 spectators and the two on board.", "links": [{"title": "1952 Farnborough Airshow DH.110 crash", "link": "https://wikipedia.org/wiki/1952_Farnborough_Airshow_DH.110_crash"}, {"title": "Farnborough Airshow", "link": "https://wikipedia.org/wiki/Farnborough_Airshow"}, {"title": "Hampshire", "link": "https://wikipedia.org/wiki/Hampshire"}]}, {"year": "1955", "text": "Istanbul's Greek, Jewish, and Armenian minorities are the target of a government-sponsored pogrom; dozens are killed in ensuing riots.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>'s Greek, Jewish, and <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenian</a> minorities are the target of <a href=\"https://wikipedia.org/wiki/Istanbul_pogrom\" title=\"Istanbul pogrom\">a government-sponsored pogrom</a>; dozens are killed in ensuing riots.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>'s Greek, Jewish, and <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenian</a> minorities are the target of <a href=\"https://wikipedia.org/wiki/Istanbul_pogrom\" title=\"Istanbul pogrom\">a government-sponsored pogrom</a>; dozens are killed in ensuing riots.", "links": [{"title": "Istanbul", "link": "https://wikipedia.org/wiki/Istanbul"}, {"title": "Armenians", "link": "https://wikipedia.org/wiki/Armenians"}, {"title": "Istanbul pogrom", "link": "https://wikipedia.org/wiki/Istanbul_pogrom"}]}, {"year": "1962", "text": "The United States government begins the Exercise Spade Fork nuclear readiness drill.", "html": "1962 - The United States government begins the <a href=\"https://wikipedia.org/wiki/Federal_Emergency_Plan_D-Minus\" title=\"Federal Emergency Plan D-Minus\">Exercise Spade Fork</a> nuclear readiness drill.", "no_year_html": "The United States government begins the <a href=\"https://wikipedia.org/wiki/Federal_Emergency_Plan_D-Minus\" title=\"Federal Emergency Plan D-Minus\">Exercise Spade Fork</a> nuclear readiness drill.", "links": [{"title": "Federal Emergency Plan D-Minus", "link": "https://wikipedia.org/wiki/Federal_Emergency_Plan_D-Minus"}]}, {"year": "1962", "text": "Archaeologist <PERSON> discovers the first of the Blackfriars Ships dating back to the second century AD in the Blackfriars area of the banks of the River Thames in London.", "html": "1962 - Archaeologist <PERSON> discovers the first of the <a href=\"https://wikipedia.org/wiki/Blackfriars_Ships\" class=\"mw-redirect\" title=\"Blackfriars Ships\">Blackfriars Ships</a> dating back to the second century AD in the <a href=\"https://wikipedia.org/wiki/Blackfriars,_London\" title=\"Blackfriars, London\">Blackfriars</a> area of the banks of the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a> in London.", "no_year_html": "Archaeologist <PERSON> discovers the first of the <a href=\"https://wikipedia.org/wiki/Blackfriars_Ships\" class=\"mw-redirect\" title=\"Blackfriars Ships\">Blackfriars Ships</a> dating back to the second century AD in the <a href=\"https://wikipedia.org/wiki/Blackfriars,_London\" title=\"Blackfriars, London\">Blackfriars</a> area of the banks of the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a> in London.", "links": [{"title": "Blackfriars Ships", "link": "https://wikipedia.org/wiki/Blackfriars_Ships"}, {"title": "Blackfriars, London", "link": "https://wikipedia.org/wiki/Blackfriars,_London"}, {"title": "River Thames", "link": "https://wikipedia.org/wiki/River_Thames"}]}, {"year": "1965", "text": "India retaliates following Pakistan's Operation Grand Slam which results in the Indo-Pakistani War of 1965 that ends in a stalemate followed by the signing of the Tashkent Declaration.", "html": "1965 - India retaliates following Pakistan's <a href=\"https://wikipedia.org/wiki/Operation_Grand_Slam\" title=\"Operation Grand Slam\">Operation Grand Slam</a> which results in the <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1965\">Indo-Pakistani War of 1965</a> that ends in a stalemate followed by the signing of the <a href=\"https://wikipedia.org/wiki/Tashkent_Declaration\" title=\"Tashkent Declaration\">Tashkent Declaration</a>.", "no_year_html": "India retaliates following Pakistan's <a href=\"https://wikipedia.org/wiki/Operation_Grand_Slam\" title=\"Operation Grand Slam\">Operation Grand Slam</a> which results in the <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1965\">Indo-Pakistani War of 1965</a> that ends in a stalemate followed by the signing of the <a href=\"https://wikipedia.org/wiki/Tashkent_Declaration\" title=\"Tashkent Declaration\">Tashkent Declaration</a>.", "links": [{"title": "Operation Grand Slam", "link": "https://wikipedia.org/wiki/Operation_Grand_Slam"}, {"title": "Indo-Pakistani War of 1965", "link": "https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965"}, {"title": "Tashkent Declaration", "link": "https://wikipedia.org/wiki/Tashkent_Declaration"}]}, {"year": "1966", "text": "Prime Minister <PERSON><PERSON><PERSON>, the architect of apartheid, is stabbed to death in Cape Town, South Africa during a parliamentary meeting.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the architect of <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a>, is stabbed to death in <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>, South Africa during a parliamentary meeting.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the architect of <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a>, is stabbed to death in <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>, South Africa during a parliamentary meeting.", "links": [{"title": "Prime Minister of South Africa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Africa"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}, {"title": "Cape Town", "link": "https://wikipedia.org/wiki/Cape_Town"}]}, {"year": "1968", "text": "Swaziland becomes independent.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Eswatini\" title=\"Eswatini\">Swaziland</a> becomes independent.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eswatini\" title=\"<PERSON>s<PERSON><PERSON>\">Swaziland</a> becomes independent.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ni"}]}, {"year": "1970", "text": "Two passenger jets bound from Europe to New York are simultaneously hijacked by Palestinian terrorist members of the PFLP and taken to Dawson's Field, Jordan.", "html": "1970 - Two passenger jets bound from Europe to New York are simultaneously hijacked by <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorist</a> members of the <a href=\"https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine\" title=\"Popular Front for the Liberation of Palestine\">PFLP</a> and taken to <a href=\"https://wikipedia.org/wiki/Dawson%27s_Field_hijackings\" title=\"Dawson's Field hijackings\">Dawson's Field, Jordan</a>.", "no_year_html": "Two passenger jets bound from Europe to New York are simultaneously hijacked by <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorist</a> members of the <a href=\"https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine\" title=\"Popular Front for the Liberation of Palestine\">PFLP</a> and taken to <a href=\"https://wikipedia.org/wiki/Dawson%27s_Field_hijackings\" title=\"Dawson's Field hijackings\">Dawson's Field, Jordan</a>.", "links": [{"title": "Palestinians", "link": "https://wikipedia.org/wiki/Palestinians"}, {"title": "Terrorism", "link": "https://wikipedia.org/wiki/Terrorism"}, {"title": "Popular Front for the Liberation of Palestine", "link": "https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine"}, {"title": "Dawson's Field hijackings", "link": "https://wikipedia.org/wiki/Dawson%27s_Field_hijackings"}]}, {"year": "1971", "text": "Paninternational Flight 112 crashes on the Bundesautobahn 7 highway near Hamburg Airport, in Hamburg, Germany, killing 22.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Paninternational_Flight_112\" title=\"Paninternational Flight 112\">Paninternational Flight 112</a> crashes on the <a href=\"https://wikipedia.org/wiki/Bundesautobahn_7\" title=\"Bundesautobahn 7\">Bundesautobahn 7</a> highway near <a href=\"https://wikipedia.org/wiki/Hamburg_Airport\" title=\"Hamburg Airport\">Hamburg Airport</a>, in <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a>, Germany, killing 22.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paninternational_Flight_112\" title=\"Paninternational Flight 112\">Paninternational Flight 112</a> crashes on the <a href=\"https://wikipedia.org/wiki/Bundesautobahn_7\" title=\"Bundesautobahn 7\">Bundesautobahn 7</a> highway near <a href=\"https://wikipedia.org/wiki/Hamburg_Airport\" title=\"Hamburg Airport\">Hamburg Airport</a>, in <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a>, Germany, killing 22.", "links": [{"title": "Paninternational Flight 112", "link": "https://wikipedia.org/wiki/Paninternational_Flight_112"}, {"title": "Bundesautobahn 7", "link": "https://wikipedia.org/wiki/Bundesautobahn_7"}, {"title": "Hamburg Airport", "link": "https://wikipedia.org/wiki/Hamburg_Airport"}, {"title": "Hamburg", "link": "https://wikipedia.org/wiki/Hamburg"}]}, {"year": "1972", "text": "Munich massacre: Nine Israeli athletes die (along with a German policeman) at the hands of the Palestinian \"Black September\" terrorist group after being taken hostage at the Munich Olympic Games. Two other Israeli athletes were slain in the initial attack the previous day.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich massacre</a>: Nine <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> athletes die (along with a German policeman) at the hands of the <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> \"<a href=\"https://wikipedia.org/wiki/Black_September_Organization\" title=\"Black September Organization\">Black September</a>\" terrorist group after being taken hostage at the <a href=\"https://wikipedia.org/wiki/Munich_Olympic_Games\" class=\"mw-redirect\" title=\"Munich Olympic Games\">Munich Olympic Games</a>. Two other Israeli athletes were slain in the initial attack the previous day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich massacre</a>: Nine <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> athletes die (along with a German policeman) at the hands of the <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> \"<a href=\"https://wikipedia.org/wiki/Black_September_Organization\" title=\"Black September Organization\">Black September</a>\" terrorist group after being taken hostage at the <a href=\"https://wikipedia.org/wiki/Munich_Olympic_Games\" class=\"mw-redirect\" title=\"Munich Olympic Games\">Munich Olympic Games</a>. Two other Israeli athletes were slain in the initial attack the previous day.", "links": [{"title": "Munich massacre", "link": "https://wikipedia.org/wiki/Munich_massacre"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Palestinians", "link": "https://wikipedia.org/wiki/Palestinians"}, {"title": "Black September Organization", "link": "https://wikipedia.org/wiki/Black_September_Organization"}, {"title": "Munich Olympic Games", "link": "https://wikipedia.org/wiki/Munich_Olympic_Games"}]}, {"year": "1976", "text": "Cold War: Soviet Air Defence Forces pilot <PERSON> lands a Mikoyan-G<PERSON>vich MiG-25 jet fighter at Hakodate in Japan and requests political asylum in the United States; his request is granted.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Soviet_Air_Defence_Forces\" title=\"Soviet Air Defence Forces\">Soviet Air Defence Forces</a> pilot <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_MiG-25\" title=\"Mi<PERSON>yan-Gurevich MiG-25\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MiG-25</a> jet fighter at <a href=\"https://wikipedia.org/wiki/Hakodate\" title=\"Hakodate\">Hakodate</a> in Japan and requests <a href=\"https://wikipedia.org/wiki/Refugee\" title=\"Refugee\">political asylum</a> in the United States; his request is granted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Soviet_Air_Defence_Forces\" title=\"Soviet Air Defence Forces\">Soviet Air Defence Forces</a> pilot <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON>_MiG-25\" title=\"Mi<PERSON>yan-Gurevich MiG-25\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>vich MiG-25</a> jet fighter at <a href=\"https://wikipedia.org/wiki/Hakodate\" title=\"Hakodate\">Hakodate</a> in Japan and requests <a href=\"https://wikipedia.org/wiki/Refugee\" title=\"Refugee\">political asylum</a> in the United States; his request is granted.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Soviet Air Defence Forces", "link": "https://wikipedia.org/wiki/Soviet_Air_Defence_Forces"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miko<PERSON><PERSON><PERSON><PERSON><PERSON> MiG-25", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-25"}, {"title": "Hakodate", "link": "https://wikipedia.org/wiki/Hakodate"}, {"title": "Refugee", "link": "https://wikipedia.org/wiki/Refugee"}]}, {"year": "1983", "text": "The Soviet Union admits to shooting down Korean Air Lines Flight 007, stating that its operatives did not know that it was a civilian aircraft when it reportedly violated Soviet airspace.", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> admits to shooting down <a href=\"https://wikipedia.org/wiki/Korean_Air_Lines_Flight_007\" title=\"Korean Air Lines Flight 007\">Korean Air Lines Flight 007</a>, stating that its operatives did not know that it was a civilian aircraft when it reportedly violated Soviet airspace.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> admits to shooting down <a href=\"https://wikipedia.org/wiki/Korean_Air_Lines_Flight_007\" title=\"Korean Air Lines Flight 007\">Korean Air Lines Flight 007</a>, stating that its operatives did not know that it was a civilian aircraft when it reportedly violated Soviet airspace.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Korean Air Lines Flight 007", "link": "https://wikipedia.org/wiki/Korean_Air_Lines_Flight_007"}]}, {"year": "1985", "text": "Midwest Express Airlines Flight 105 crashes near Milwaukee Mitchell International Airport in Milwaukee, Wisconsin, killing all 31 people on board.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Midwest_Express_Airlines_Flight_105\" title=\"Midwest Express Airlines Flight 105\">Midwest Express Airlines Flight 105</a> crashes near <a href=\"https://wikipedia.org/wiki/Milwaukee_Mitchell_International_Airport\" title=\"Milwaukee Mitchell International Airport\">Milwaukee Mitchell International Airport</a> in <a href=\"https://wikipedia.org/wiki/Milwaukee\" title=\"Milwaukee\">Milwaukee</a>, <a href=\"https://wikipedia.org/wiki/Wisconsin\" title=\"Wisconsin\">Wisconsin</a>, killing all 31 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Midwest_Express_Airlines_Flight_105\" title=\"Midwest Express Airlines Flight 105\">Midwest Express Airlines Flight 105</a> crashes near <a href=\"https://wikipedia.org/wiki/Milwaukee_Mitchell_International_Airport\" title=\"Milwaukee Mitchell International Airport\">Milwaukee Mitchell International Airport</a> in <a href=\"https://wikipedia.org/wiki/Milwaukee\" title=\"Milwaukee\">Milwaukee</a>, <a href=\"https://wikipedia.org/wiki/Wisconsin\" title=\"Wisconsin\">Wisconsin</a>, killing all 31 people on board.", "links": [{"title": "Midwest Express Airlines Flight 105", "link": "https://wikipedia.org/wiki/Midwest_Express_Airlines_Flight_105"}, {"title": "Milwaukee Mitchell International Airport", "link": "https://wikipedia.org/wiki/Milwaukee_Mitchell_International_Airport"}, {"title": "Milwaukee", "link": "https://wikipedia.org/wiki/Milwaukee"}, {"title": "Wisconsin", "link": "https://wikipedia.org/wiki/Wisconsin"}]}, {"year": "1986", "text": "In Istanbul, two terrorists from <PERSON>'s organization kill 22 and wound six congregants inside the Neve Shalom Synagogue during Shabbat services.", "html": "1986 - In <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>, two <a href=\"https://wikipedia.org/wiki/Terrorist\" class=\"mw-redirect\" title=\"Terrorist\">terrorists</a> from <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Abu Nidal\"><PERSON></a>'s organization kill 22 and wound six congregants inside the <a href=\"https://wikipedia.org/wiki/Neve_Shalom_Synagogue\" title=\"Neve Shalom Synagogue\">Neve Shalom Synagogue</a> during <a href=\"https://wikipedia.org/wiki/Shabbat\" title=\"Shabbat\">Shabbat</a> services.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>, two <a href=\"https://wikipedia.org/wiki/Terrorist\" class=\"mw-redirect\" title=\"Terrorist\">terrorists</a> from <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Abu Nidal\"><PERSON></a>'s organization kill 22 and wound six congregants inside the <a href=\"https://wikipedia.org/wiki/Neve_Shalom_Synagogue\" title=\"Neve Shalom Synagogue\">Neve Shalom Synagogue</a> during <a href=\"https://wikipedia.org/wiki/Shabbat\" title=\"Shabbat\">Shabbat</a> services.", "links": [{"title": "Istanbul", "link": "https://wikipedia.org/wiki/Istanbul"}, {"title": "Terrorist", "link": "https://wikipedia.org/wiki/Terrorist"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Neve Shalom Synagogue", "link": "https://wikipedia.org/wiki/Neve_Shalom_Synagogue"}, {"title": "Shabbat", "link": "https://wikipedia.org/wiki/Shabbat"}]}, {"year": "1991", "text": "The Soviet Union recognizes the independence of the Baltic states Estonia, Latvia, and Lithuania.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> recognizes the independence of the <a href=\"https://wikipedia.org/wiki/Baltic_state\" class=\"mw-redirect\" title=\"Baltic state\">Baltic states</a> <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, and <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> recognizes the independence of the <a href=\"https://wikipedia.org/wiki/Baltic_state\" class=\"mw-redirect\" title=\"Baltic state\">Baltic states</a> <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, and <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Baltic state", "link": "https://wikipedia.org/wiki/Baltic_state"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}]}, {"year": "1991", "text": "The Russian parliament approves the name change of Leningrad back to Saint Petersburg. The change is effective October 1.", "html": "1991 - The Russian parliament approves the name change of Leningrad back to <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>. The change is effective October 1.", "no_year_html": "The Russian parliament approves the name change of Leningrad back to <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>. The change is effective October 1.", "links": [{"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}]}, {"year": "1992", "text": "A group of hunters at the Stampede trail near Healy, Alaska came across a male corpse in abandoned bus, later identified as <PERSON>.", "html": "1992 - A group of hunters at the Stampede trail near <a href=\"https://wikipedia.org/wiki/Healy,_Alaska\" title=\"Healy, Alaska\">Healy, Alaska</a> came across a male corpse in abandoned bus, later identified as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A group of hunters at the Stampede trail near <a href=\"https://wikipedia.org/wiki/Healy,_Alaska\" title=\"Healy, Alaska\">Healy, Alaska</a> came across a male corpse in abandoned bus, later identified as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Healy, Alaska", "link": "https://wikipedia.org/wiki/Healy,_Alaska"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON> of the Baltimore Orioles plays in his 2,131st consecutive game, breaking a record that had stood for 56 years.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Cal_Ripken_Jr.\" title=\"<PERSON> Ripken Jr.\"><PERSON> Ripken Jr.</a> of the <a href=\"https://wikipedia.org/wiki/Baltimore_Orioles\" title=\"Baltimore Orioles\">Baltimore Orioles</a> plays in his 2,131st consecutive game, breaking a record that had stood for 56 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cal_Ripken_Jr.\" title=\"Cal Ripken Jr.\"><PERSON> Ripken Jr.</a> of the <a href=\"https://wikipedia.org/wiki/Baltimore_Orioles\" title=\"Baltimore Orioles\">Baltimore Orioles</a> plays in his 2,131st consecutive game, breaking a record that had stood for 56 years.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_Jr."}, {"title": "Baltimore Orioles", "link": "https://wikipedia.org/wiki/Baltimore_Orioles"}]}, {"year": "1997", "text": "The funeral of <PERSON>, Princess of Wales takes place in London. Well over a million people line the streets and 2.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}1⁄2 billion watch around the world on television.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Funeral_of_<PERSON>,_Princess_of_Wales\" title=\"<PERSON><PERSON> of <PERSON>, Princess of Wales\">funeral of <PERSON>, Princess of Wales</a> takes place in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>. Well over a million people line the streets and 2<style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\"><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> billion watch around the world on television.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Funeral_of_<PERSON>,_Princess_of_Wales\" title=\"<PERSON>eral of <PERSON>, Princess of Wales\">funeral of <PERSON>, Princess of Wales</a> takes place in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>. Well over a million people line the streets and 2<style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\"><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> billion watch around the world on television.", "links": [{"title": "Funeral of <PERSON>, Princess of Wales", "link": "https://wikipedia.org/wiki/Funeral_of_<PERSON>,_Princess_of_Wales"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1997", "text": "Royal Brunei Airlines Flight 238 crashes in the Lambir Hills National Park while on approach to Miri Airport in Malaysia, killing 10.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Royal_Brunei_Airlines_Flight_238\" class=\"mw-redirect\" title=\"Royal Brunei Airlines Flight 238\">Royal Brunei Airlines Flight 238</a> crashes in the <a href=\"https://wikipedia.org/wiki/Lambir_Hills_National_Park\" title=\"Lambir Hills National Park\">Lambir Hills National Park</a> while on approach to <a href=\"https://wikipedia.org/wiki/Miri_Airport\" title=\"Miri Airport\">Miri Airport</a> in Malaysia, killing 10.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Royal_Brunei_Airlines_Flight_238\" class=\"mw-redirect\" title=\"Royal Brunei Airlines Flight 238\">Royal Brunei Airlines Flight 238</a> crashes in the <a href=\"https://wikipedia.org/wiki/Lambir_Hills_National_Park\" title=\"Lambir Hills National Park\">Lambir Hills National Park</a> while on approach to <a href=\"https://wikipedia.org/wiki/Miri_Airport\" title=\"Miri Airport\">Miri Airport</a> in Malaysia, killing 10.", "links": [{"title": "Royal Brunei Airlines Flight 238", "link": "https://wikipedia.org/wiki/Royal_Brunei_Airlines_Flight_238"}, {"title": "Lambir Hills National Park", "link": "https://wikipedia.org/wiki/Lambir_Hills_National_Park"}, {"title": "Miri Airport", "link": "https://wikipedia.org/wiki/Miri_Airport"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON> resigns from his position of Palestinian Prime Minister.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> resigns from his position of <a href=\"https://wikipedia.org/wiki/Palestinian_Prime_Minister\" class=\"mw-redirect\" title=\"Palestinian Prime Minister\">Palestinian Prime Minister</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> resigns from his position of <a href=\"https://wikipedia.org/wiki/Palestinian_Prime_Minister\" class=\"mw-redirect\" title=\"Palestinian Prime Minister\">Palestinian Prime Minister</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Palestinian Prime Minister", "link": "https://wikipedia.org/wiki/Palestinian_Prime_Minister"}]}, {"year": "2007", "text": "Israel executes the air strike Operation Orchard to destroy a nuclear reactor in Syria.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> executes the air strike <a href=\"https://wikipedia.org/wiki/Operation_Orchard\" class=\"mw-redirect\" title=\"Operation Orchard\">Operation Orchard</a> to destroy a nuclear reactor in <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> executes the air strike <a href=\"https://wikipedia.org/wiki/Operation_Orchard\" class=\"mw-redirect\" title=\"Operation Orchard\">Operation Orchard</a> to destroy a nuclear reactor in <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>.", "links": [{"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Operation Orchard", "link": "https://wikipedia.org/wiki/Operation_Orchard"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}, {"year": "2009", "text": "The ro-ro ferry SuperFerry 9 sinks off the Zamboanga Peninsula in the Philippines with 971 persons aboard; all but ten are rescued.", "html": "2009 - The <a href=\"https://wikipedia.org/wiki/Roll-on/roll-off\" title=\"Roll-on/roll-off\">ro-ro ferry</a> <i><a href=\"https://wikipedia.org/wiki/SuperFerry_9\" title=\"SuperFerry 9\">SuperFerry 9</a></i> sinks off the <a href=\"https://wikipedia.org/wiki/Zamboanga_Peninsula\" title=\"Zamboanga Peninsula\">Zamboanga Peninsula</a> in the Philippines with 971 persons aboard; all but ten are rescued.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Roll-on/roll-off\" title=\"Roll-on/roll-off\">ro-ro ferry</a> <i><a href=\"https://wikipedia.org/wiki/SuperFerry_9\" title=\"SuperFerry 9\">SuperFerry 9</a></i> sinks off the <a href=\"https://wikipedia.org/wiki/Zamboanga_Peninsula\" title=\"Zamboanga Peninsula\">Zamboanga Peninsula</a> in the Philippines with 971 persons aboard; all but ten are rescued.", "links": [{"title": "Roll-on/roll-off", "link": "https://wikipedia.org/wiki/Roll-on/roll-off"}, {"title": "SuperFerry 9", "link": "https://wikipedia.org/wiki/SuperFerry_9"}, {"title": "Zamboanga Peninsula", "link": "https://wikipedia.org/wiki/Zamboanga_Peninsula"}]}, {"year": "2012", "text": "Sixty-one people die after a fishing boat capsizes off the İzmir Province coast of Turkey, near the Greek Aegean islands.", "html": "2012 - Sixty-one people die after a fishing boat <a href=\"https://wikipedia.org/wiki/September_2012_Baradan_Bay,_Turkey_migrant_boat_disaster\" class=\"mw-redirect\" title=\"September 2012 Baradan Bay, Turkey migrant boat disaster\">capsizes</a> off the <a href=\"https://wikipedia.org/wiki/%C4%B0zmir_Province\" title=\"İzmir Province\">İzmir Province</a> coast of <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, near the <a href=\"https://wikipedia.org/wiki/North_Aegean_islands\" title=\"North Aegean islands\">Greek Aegean islands</a>.", "no_year_html": "Sixty-one people die after a fishing boat <a href=\"https://wikipedia.org/wiki/September_2012_Baradan_Bay,_Turkey_migrant_boat_disaster\" class=\"mw-redirect\" title=\"September 2012 Baradan Bay, Turkey migrant boat disaster\">capsizes</a> off the <a href=\"https://wikipedia.org/wiki/%C4%B0zmir_Province\" title=\"İzmir Province\">İzmir Province</a> coast of <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, near the <a href=\"https://wikipedia.org/wiki/North_Aegean_islands\" title=\"North Aegean islands\">Greek Aegean islands</a>.", "links": [{"title": "September 2012 Baradan Bay, Turkey migrant boat disaster", "link": "https://wikipedia.org/wiki/September_2012_Baradan_Bay,_Turkey_migrant_boat_disaster"}, {"title": "İzmir Province", "link": "https://wikipedia.org/wiki/%C4%B0zmir_Province"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "North Aegean islands", "link": "https://wikipedia.org/wiki/North_Aegean_islands"}]}, {"year": "2013", "text": "Forty-one elephants are poisoned with cyanide in salt pans, by poachers in Hwange National Park.", "html": "2013 - Forty-one elephants are poisoned with <a href=\"https://wikipedia.org/wiki/Cyanide\" title=\"Cyanide\">cyanide</a> in salt pans, by poachers in <a href=\"https://wikipedia.org/wiki/Hwange_National_Park\" title=\"Hwange National Park\">Hwange National Park</a>.", "no_year_html": "Forty-one elephants are poisoned with <a href=\"https://wikipedia.org/wiki/Cyanide\" title=\"Cyanide\">cyanide</a> in salt pans, by poachers in <a href=\"https://wikipedia.org/wiki/Hwange_National_Park\" title=\"Hwange National Park\">Hwange National Park</a>.", "links": [{"title": "Cyanide", "link": "https://wikipedia.org/wiki/Cyanide"}, {"title": "Hwange National Park", "link": "https://wikipedia.org/wiki/Hwange_National_Park"}]}, {"year": "2018", "text": "Supreme Court of India decriminalised all consensual sex among adults in private, making homosexuality legal on the Indian lands.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_India\" title=\"Supreme Court of India\">Supreme Court of India</a> decriminalised all consensual sex among adults in private, making <a href=\"https://wikipedia.org/wiki/Homosexuality_in_India#Legal_status\" title=\"Homosexuality in India\">homosexuality legal</a> on the Indian lands.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Supreme_Court_of_India\" title=\"Supreme Court of India\">Supreme Court of India</a> decriminalised all consensual sex among adults in private, making <a href=\"https://wikipedia.org/wiki/Homosexuality_in_India#Legal_status\" title=\"Homosexuality in India\">homosexuality legal</a> on the Indian lands.", "links": [{"title": "Supreme Court of India", "link": "https://wikipedia.org/wiki/Supreme_Court_of_India"}, {"title": "Homosexuality in India", "link": "https://wikipedia.org/wiki/Homosexuality_in_India#Legal_status"}]}, {"year": "2022", "text": "<PERSON> resigns as Prime Minister of the United Kingdom, and is replaced by <PERSON>. Their meetings with Queen <PERSON> at Balmoral Castle were the Queen's final official duties before her death two days later.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/July_2022_United_Kingdom_government_crisis\" title=\"July 2022 United Kingdom government crisis\">resigns</a> as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>, and is <a href=\"https://wikipedia.org/wiki/July%E2%80%93September_2022_Conservative_Party_leadership_election\" title=\"July-September 2022 Conservative Party leadership election\">replaced</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. Their meetings with <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\">Queen <PERSON> II</a> at <a href=\"https://wikipedia.org/wiki/Balmoral_Castle\" title=\"Balmoral Castle\">Balmoral Castle</a> were the Queen's final official duties before her death two days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/July_2022_United_Kingdom_government_crisis\" title=\"July 2022 United Kingdom government crisis\">resigns</a> as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>, and is <a href=\"https://wikipedia.org/wiki/July%E2%80%93September_2022_Conservative_Party_leadership_election\" title=\"July-September 2022 Conservative Party leadership election\">replaced</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. Their meetings with <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\">Queen Elizabeth II</a> at <a href=\"https://wikipedia.org/wiki/Balmoral_Castle\" title=\"Balmoral Castle\">Balmoral Castle</a> were the Queen's final official duties before her death two days later.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "July 2022 United Kingdom government crisis", "link": "https://wikipedia.org/wiki/July_2022_United_Kingdom_government_crisis"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "July-September 2022 Conservative Party leadership election", "link": "https://wikipedia.org/wiki/July%E2%80%93September_2022_Conservative_Party_leadership_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "Balmoral Castle", "link": "https://wikipedia.org/wiki/Balmoral_Castle"}]}, {"year": "2022", "text": "Russo-Ukrainian War: Ukraine begins its Kharkiv counteroffensive, surprising Russian forces and retaking over 3,000 square kilometers of land, recapturing the entire Kharkiv Oblast west of the Oskil River, within the next week.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: Ukraine begins its <a href=\"https://wikipedia.org/wiki/2022_Ukrainian_Kharkiv_counteroffensive\" class=\"mw-redirect\" title=\"2022 Ukrainian Kharkiv counteroffensive\">Kharkiv counteroffensive</a>, surprising Russian forces and retaking over 3,000 square kilometers of land, recapturing the entire <a href=\"https://wikipedia.org/wiki/Kharkiv_Oblast\" title=\"Kharkiv Oblast\">Kharkiv Oblast</a> west of the <a href=\"https://wikipedia.org/wiki/Oskil_River\" class=\"mw-redirect\" title=\"Oskil River\">Oskil River</a>, within the next week.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: Ukraine begins its <a href=\"https://wikipedia.org/wiki/2022_Ukrainian_Kharkiv_counteroffensive\" class=\"mw-redirect\" title=\"2022 Ukrainian Kharkiv counteroffensive\">Kharkiv counteroffensive</a>, surprising Russian forces and retaking over 3,000 square kilometers of land, recapturing the entire <a href=\"https://wikipedia.org/wiki/Kharkiv_Oblast\" title=\"Kharkiv Oblast\">Kharkiv Oblast</a> west of the <a href=\"https://wikipedia.org/wiki/Oskil_River\" class=\"mw-redirect\" title=\"Oskil River\">Oskil River</a>, within the next week.", "links": [{"title": "Russo-Ukrainian War", "link": "https://wikipedia.org/wiki/Russo-Ukrainian_War"}, {"title": "2022 Ukrainian Kharkiv counteroffensive", "link": "https://wikipedia.org/wiki/2022_Ukrainian_Kharkiv_counteroffensive"}, {"title": "Kharkiv Oblast", "link": "https://wikipedia.org/wiki/Kharkiv_Oblast"}, {"title": "Oskil River", "link": "https://wikipedia.org/wiki/Oskil_River"}]}], "Births": [{"year": "1475", "text": "<PERSON><PERSON>, Lord of Boissy, French nobleman and politician (d. 1519)", "html": "1475 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Lord_of_Boissy\" title=\"<PERSON><PERSON>, Lord of Boissy\"><PERSON><PERSON>, Lord of Boissy</a>, French nobleman and politician (d. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Lord_of_Boissy\" title=\"<PERSON><PERSON>, Lord of Boissy\"><PERSON><PERSON>, Lord of Boissy</a>, French nobleman and politician (d. 1519)", "links": [{"title": "<PERSON><PERSON>, Lord of Boissy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Lord_of_Bois<PERSON>"}]}, {"year": "1475", "text": "<PERSON><PERSON>, Italian Mannerist architect (d. 1554)", "html": "1475 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian Mannerist architect (d. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian Mannerist architect (d. 1554)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1610", "text": "<PERSON>, Duke of Modena, Italian noble (d. 1658)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27<PERSON><PERSON>,_Duke_of_Modena\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Modena\"><PERSON>, Duke of Modena</a>, Italian noble (d. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27<PERSON><PERSON>,_Duke_of_Modena\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Modena\"><PERSON>, Duke of Modena</a>, Italian noble (d. 1658)", "links": [{"title": "<PERSON>, Duke of Modena", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Este,_<PERSON>_of_Modena"}]}, {"year": "1620", "text": "<PERSON>, Italian composer and educator (d. 1704)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1631", "text": "<PERSON>, English-born judge (d. 1696)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Chancellor_of_Ireland)\" title=\"<PERSON> (Lord Chancellor of Ireland)\"><PERSON></a>, English-born judge (d. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Chancellor_of_Ireland)\" title=\"<PERSON> (Lord Chancellor of Ireland)\"><PERSON></a>, English-born judge (d. 1696)", "links": [{"title": "<PERSON> (Lord Chancellor of Ireland)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Chancellor_of_Ireland)"}]}, {"year": "1633", "text": "<PERSON>, German cantor and composer (d. 1676)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cantor and composer (d. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cantor and composer (d. 1676)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sebastian_Kn%C3%BCpfer"}]}, {"year": "1656", "text": "<PERSON>, French cardinal and politician (d. 1723)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and politician (d. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and politician (d. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1666", "text": "<PERSON> of Russia, Russian tsar (d. 1696)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/Ivan_V_of_Russia\" title=\"Ivan V of Russia\"><PERSON> of Russia</a>, Russian tsar (d. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ivan_V_of_Russia\" title=\"Ivan V of Russia\"><PERSON> of Russia</a>, Russian tsar (d. 1696)", "links": [{"title": "Ivan V of Russia", "link": "https://wikipedia.org/wiki/Ivan_V_of_Russia"}]}, {"year": "1711", "text": "<PERSON>, German-American pastor and missionary (d. 1787)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pastor and missionary (d. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pastor and missionary (d. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, German philosopher and theologian (d. 1786)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and theologian (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and theologian (d. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1732", "text": "<PERSON>, Swedish physicist and academic (d. 1796)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and academic (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and academic (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, <PERSON>, French general (d. 1834)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquis_<PERSON>_<PERSON>\" title=\"<PERSON>, Marquis <PERSON>\"><PERSON>, Marquis <PERSON></a>, French general (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquis_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Marquis <PERSON></a>, French general (d. 1834)", "links": [{"title": "<PERSON>, Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, English chemist, meteorologist, and physicist (d. 1844)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist, meteorologist, and physicist (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist, meteorologist, and physicist (d. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "<PERSON>, English composer and publisher (d. 1861)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and publisher (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and publisher (d. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1795", "text": "<PERSON>, Scottish-American author and activist (d. 1852)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American author and activist (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American author and activist (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON><PERSON>, American educator and activist (d. 1878)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and activist (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and activist (d. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1802", "text": "<PERSON><PERSON><PERSON>, French zoologist, palaeontologist, and geologist (d. 1857)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/Alcide_d%27Orbigny\" title=\"<PERSON><PERSON><PERSON>Or<PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, French zoologist, palaeontologist, and geologist (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alcide_d%27Orbigny\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, French zoologist, palaeontologist, and geologist (d. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alcide_d%27Orbigny"}]}, {"year": "1808", "text": "<PERSON><PERSON>, Algerian religious and military leader (d. 1883)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian religious and military leader (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian religious and military leader (d. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 9th Premier of East Canada (d. 1873)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada\" class=\"mw-redirect\" title=\"List of Joint Premiers of the Province of Canada\">Premier of East Canada</a> (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada\" class=\"mw-redirect\" title=\"List of Joint Premiers of the Province of Canada\">Premier of East Canada</a> (d. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-%C3%89tien<PERSON>_<PERSON>"}, {"title": "List of Joint Premiers of the Province of Canada", "link": "https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada"}]}, {"year": "1815", "text": "<PERSON><PERSON> <PERSON>, American general (d. 1870)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American general (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American general (d. 1870)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, English-Canadian businessman and politician, 1st Canadian Minister of Finance (d. 1893)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Canadian_Minister_of_Finance\" class=\"mw-redirect\" title=\"Canadian Minister of Finance\">Canadian Minister of Finance</a> (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Canadian_Minister_of_Finance\" class=\"mw-redirect\" title=\"Canadian Minister of Finance\">Canadian Minister of Finance</a> (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Canadian Minister of Finance", "link": "https://wikipedia.org/wiki/Canadian_Minister_of_Finance"}]}, {"year": "1819", "text": "<PERSON>, American general, politician, and diplomat, United States Ambassador to Mexico (d. 1898)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Mexico\" class=\"mw-redirect\" title=\"United States Ambassador to Mexico\">United States Ambassador to Mexico</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Mexico\" class=\"mw-redirect\" title=\"United States Ambassador to Mexico\">United States Ambassador to Mexico</a> (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Mexico", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Mexico"}]}, {"year": "1838", "text": "<PERSON>, American conspirator (d. 1906)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conspirator)\" title=\"<PERSON> (conspirator)\"><PERSON></a>, American conspirator (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conspirator)\" title=\"<PERSON> (conspirator)\"><PERSON></a>, American conspirator (d. 1906)", "links": [{"title": "<PERSON> (conspirator)", "link": "https://wikipedia.org/wiki/<PERSON>(conspirator)"}]}, {"year": "1852", "text": "<PERSON><PERSON><PERSON>, South African commander, lawyer, and politician, 6th President of the South African Republic (d. 1918)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African commander, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_South_African_Republic\" class=\"mw-redirect\" title=\"President of the South African Republic\">President of the South African Republic</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African commander, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_South_African_Republic\" class=\"mw-redirect\" title=\"President of the South African Republic\">President of the South African Republic</a> (d. 1918)", "links": [{"title": "Schalk Willem Burger", "link": "https://wikipedia.org/wiki/<PERSON>halk_Willem_Burger"}, {"title": "President of the South African Republic", "link": "https://wikipedia.org/wiki/President_of_the_South_African_Republic"}]}, {"year": "1855", "text": "<PERSON>, German pianist, composer, and conductor (d. 1928)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON><PERSON>, American archeologist and historian (d. 1933)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American archeologist and historian (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American archeologist and historian (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON>, Australian businessman and philanthropist, founded MacRobertson's (d. 1945)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>%27s\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>'s\"><PERSON><PERSON><PERSON><PERSON><PERSON>'s</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>%27s\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>'s\"><PERSON><PERSON><PERSON><PERSON><PERSON>'s</a> (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>'s", "link": "https://wikipedia.org/wiki/MacRobertson%27s"}]}, {"year": "1860", "text": "<PERSON>, American sociologist and author, Nobel Prize laureate (d. 1935)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1860", "text": "<PERSON>, Australian trade unionist and suffragist (d. 1929)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/May_Jordan_McConnel\" title=\"May Jordan McConnel\">May <PERSON></a>, Australian trade unionist and suffragist (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_Jordan_McConnel\" title=\"May Jordan McConnel\">May <PERSON> Mc<PERSON>on<PERSON></a>, Australian trade unionist and suffragist (d. 1929)", "links": [{"title": "May Jordan McConnel", "link": "https://wikipedia.org/wiki/May_Jordan_McConnel"}]}, {"year": "1861", "text": "<PERSON>, English-Australian journalist, founded New Australia (d. 1917)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Lane\"><PERSON></a>, English-Australian journalist, founded <a href=\"https://wikipedia.org/wiki/New_Australia\" title=\"New Australia\">New Australia</a> (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William<PERSON>\" title=\"William Lane\"><PERSON></a>, English-Australian journalist, founded <a href=\"https://wikipedia.org/wiki/New_Australia\" title=\"New Australia\">New Australia</a> (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New Australia", "link": "https://wikipedia.org/wiki/New_Australia"}]}, {"year": "1863", "text": "<PERSON>, American illustrator (d. 1935)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Illustrator\" title=\"Illustrator\">illustrator</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Illustrator\" title=\"Illustrator\">illustrator</a> (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Illustrator", "link": "https://wikipedia.org/wiki/Illustrator"}]}, {"year": "1868", "text": "<PERSON>, Swiss judge and politician, President of the Swiss National Council (d. 1947)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss judge and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland\" class=\"mw-redirect\" title=\"List of Presidents of the National Council of Switzerland\">President of the Swiss National Council</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss judge and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland\" class=\"mw-redirect\" title=\"List of Presidents of the National Council of Switzerland\">President of the Swiss National Council</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Heinrich_H%C3%A4berlin"}, {"title": "List of Presidents of the National Council of Switzerland", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, English organist and composer (d. 1941)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English organist and composer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English organist and composer (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON> Davies", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, Austrian-Swiss author and critic (d. 1945)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss author and critic (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss author and critic (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Scottish physician and physiologist, Nobel Prize laureate (d. 1935)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(physiologist)\" title=\"<PERSON> (physiologist)\"><PERSON></a>, Scottish physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(physiologist)\" title=\"<PERSON> (physiologist)\"><PERSON></a>, Scottish physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1935)", "links": [{"title": "<PERSON> (physiologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(physiologist)"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1879", "text": "<PERSON>, German actor (d. 1936)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, German educator and politician, Chancellor of Germany (d. 1956)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>h"}, {"title": "Chancellor of Germany (German Reich)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)"}]}, {"year": "1885", "text": "<PERSON>, American actor (d. 1974)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American businessman and diplomat, 44th United States Ambassador to the United Kingdom (d. 1969)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>.\"><PERSON>.</a>, American businessman and diplomat, 44th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American businessman and diplomat, 44th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (d. 1969)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "United States Ambassador to the United Kingdom", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom"}]}, {"year": "1889", "text": "<PERSON>, American composer (d. 1954)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Silvers\"><PERSON></a>, American composer (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Silvers"}]}, {"year": "1890", "text": "<PERSON>, American actress and producer (d. 1960)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, English-Scottish physicist and academic, Nobel Prize laureate (d. 1965)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English-Scottish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English-Scottish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1893", "text": "<PERSON>, American general and pilot (d. 1958)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American composer and manager (d. 1966)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and manager (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and manager (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Canadian businessman and politician, 25th Premier of British Columbia (d. 1979)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian businessman and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian businessman and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (d. 1979)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Premier of British Columbia", "link": "https://wikipedia.org/wiki/Premier_of_British_Columbia"}]}, {"year": "1900", "text": "<PERSON>, French-American author (d. 1998)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese political journalist (d. 1943)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_<PERSON>_<PERSON>nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese political journalist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_<PERSON>_<PERSON>nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese political journalist (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_<PERSON>_<PERSON>nh"}]}, {"year": "1906", "text": "<PERSON>, French-Argentine physician and biochemist, Nobel Prize laureate (d. 1987)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Argentine physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Argentine physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1908", "text": "<PERSON>, English genealogist and academic (d. 1995)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English genealogist and academic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English genealogist and academic (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, American sculptor, designed the Crazy Horse Memorial (d. 1982)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sculptor, designed the <a href=\"https://wikipedia.org/wiki/Crazy_Horse_Memorial\" title=\"Crazy Horse Memorial\">Crazy Horse Memorial</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sculptor, designed the <a href=\"https://wikipedia.org/wiki/Crazy_Horse_Memorial\" title=\"Crazy Horse Memorial\">Crazy Horse Memorial</a> (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Crazy Horse Memorial", "link": "https://wikipedia.org/wiki/Crazy_Horse_Memorial"}]}, {"year": "1909", "text": "<PERSON>, American actor and director (d. 1993)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, American actor and director (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, American actor and director (d. 1993)", "links": [{"title": "<PERSON> (film director)", "link": "https://wikipedia.org/wiki/<PERSON>_(film_director)"}]}, {"year": "1910", "text": "<PERSON>, American soccer player, referee, and coach (d. 1976)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player, referee, and coach (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player, referee, and coach (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American baseball player and coach (d. 2004)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, French aerodynamics engineer and automobile maker, co-founder of the brand \"DB (d. 1980)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Aerodynamics\" title=\"Aerodynamics\">aerodynamics</a> <a href=\"https://wikipedia.org/wiki/Engineer\" title=\"Engineer\">engineer</a> and automobile maker, co-founder of the brand \"<a href=\"https://wikipedia.org/wiki/DB_(car)\" title=\"DB (car)\">DB</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Aerodynamics\" title=\"Aerodynamics\">aerodynamics</a> <a href=\"https://wikipedia.org/wiki/Engineer\" title=\"Engineer\">engineer</a> and automobile maker, co-founder of the brand \"<a href=\"https://wikipedia.org/wiki/DB_(car)\" title=\"DB (car)\">DB</a> (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Aerodynamics", "link": "https://wikipedia.org/wiki/Aerodynamics"}, {"title": "Engineer", "link": "https://wikipedia.org/wiki/Engineer"}, {"title": "DB (car)", "link": "https://wikipedia.org/wiki/DB_(car)"}]}, {"year": "1912", "text": "<PERSON>, American organist, composer, and director (d. 1996)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist, composer, and director (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist, composer, and director (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actress and singer (d. 2019)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer (d. 2004)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Le%C3%B4ni<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le%C3%B4nidas\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le%C3%B4nidas"}]}, {"year": "1915", "text": "<PERSON>, American golfer (d. 1961)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1961)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1915", "text": "<PERSON>, German lieutenant and politician, Minister President of Bavaria (d. 1988)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Minister_President_of_Bavaria\" class=\"mw-redirect\" title=\"Minister President of Bavaria\">Minister President of Bavaria</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Minister_President_of_Bavaria\" class=\"mw-redirect\" title=\"Minister President of Bavaria\">Minister President of Bavaria</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister President of Bavaria", "link": "https://wikipedia.org/wiki/Minister_President_of_Bavaria"}]}, {"year": "1917", "text": "<PERSON>, American-French actor, director, producer, and screenwriter (d. 1999)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, American-French actor, director, producer, and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, American-French actor, director, producer, and screenwriter (d. 1999)", "links": [{"title": "<PERSON> (film director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)"}]}, {"year": "1917", "text": "<PERSON>, English cricketer (d. 2001)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 2001)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1917", "text": "<PERSON>, German soldier and economist (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and economist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and economist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American engineer and philanthropist (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and philanthropist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Great<PERSON>ch\"><PERSON></a>, American engineer and philanthropist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>batch"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Brazilian actress, singer, and author (d. 2003)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>g%C3%A3\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian actress, singer, and author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>g%C3%A3\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian actress, singer, and author (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elvira_Pag%C3%A3"}]}, {"year": "1921", "text": "<PERSON>, Spanish author (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American inventor, co-created the bar code (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman <PERSON>\"><PERSON></a>, American inventor, co-created the <a href=\"https://wikipedia.org/wiki/Bar_code\" class=\"mw-redirect\" title=\"Bar code\">bar code</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman <PERSON>\"><PERSON></a>, American inventor, co-created the <a href=\"https://wikipedia.org/wiki/Bar_code\" class=\"mw-redirect\" title=\"Bar code\">bar code</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bar code", "link": "https://wikipedia.org/wiki/Bar_code"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Portuguese politician, Minister of the Overseas Provinces, President of the CDS - People's Party (d. 2022)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese politician, Minister of the Overseas Provinces, President of the <a href=\"https://wikipedia.org/wiki/CDS_%E2%80%93_People%27s_Party\" title=\"CDS - People's Party\">CDS - People's Party</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese politician, Minister of the Overseas Provinces, President of the <a href=\"https://wikipedia.org/wiki/CDS_%E2%80%93_People%27s_Party\" title=\"CDS - People's Party\">CDS - People's Party</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "CDS - People's Party", "link": "https://wikipedia.org/wiki/CDS_%E2%80%93_People%27s_Party"}]}, {"year": "1923", "text": "<PERSON> of Yugoslavia (d. 1970)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\"><PERSON> of Yugoslavia</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\"><PERSON> of Yugoslavia</a> (d. 1970)", "links": [{"title": "<PERSON> of Yugoslavia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia"}]}, {"year": "1924", "text": "<PERSON>, American veterinarian and politician (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American veterinarian and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American veterinarian and politician (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Italian author, screenwriter, and director (d. 2019)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author, screenwriter, and director (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author, screenwriter, and director (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1976)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON> of the Netherlands (d. 2002)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_the_Netherlands\" title=\"Prince <PERSON> of the Netherlands\">Prince <PERSON> of the Netherlands</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_the_Netherlands\" title=\"Prince <PERSON> of the Netherlands\">Prince <PERSON> of the Netherlands</a> (d. 2002)", "links": [{"title": "<PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_of_the_Netherlands"}]}, {"year": "1926", "text": "<PERSON>, American lawyer and politician (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Jack_<PERSON>_Hightower\" class=\"mw-redirect\" title=\"Jack <PERSON> Hightower\"><PERSON>tower</a>, American lawyer and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jack_English_Hightower\" class=\"mw-redirect\" title=\"Jack English Hightower\"><PERSON></a>, American lawyer and politician (d. 2013)", "links": [{"title": "<PERSON>tower", "link": "https://wikipedia.org/wiki/Jack_English_Hightower"}]}, {"year": "1926", "text": "<PERSON>, English composer and conductor (d. 2003)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American photographer and director (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and director (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and director (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Japanese architect and academic, designed the Tokyo Metropolitan Gymnasium and Makuhari Messe (d. 2024)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Tokyo_Metropolitan_Gymnasium\" title=\"Tokyo Metropolitan Gymnasium\">Tokyo Metropolitan Gymnasium</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Messe\" title=\"<PERSON><PERSON><PERSON> Messe\"><PERSON><PERSON><PERSON></a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Tokyo_Metropolitan_Gymnasium\" title=\"Tokyo Metropolitan Gymnasium\">Tokyo Metropolitan Gymnasium</a> and <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>_Messe\" title=\"<PERSON><PERSON><PERSON> Messe\"><PERSON><PERSON><PERSON></a> (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Tokyo Metropolitan Gymnasium", "link": "https://wikipedia.org/wiki/Tokyo_Metropolitan_Gymnasium"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>sse"}]}, {"year": "1928", "text": "<PERSON>, American novelist and philosopher (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and philosopher (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and philosopher (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Russian conductor and composer (d. 2002)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian conductor and composer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian conductor and composer (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English neurosurgeon and academic (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English neurosurgeon and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English neurosurgeon and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Indian film producer, founded Dharma Productions (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian film producer, founded <a href=\"https://wikipedia.org/wiki/Dharma_Productions\" title=\"Dharma Productions\">Dharma Productions</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian film producer, founded <a href=\"https://wikipedia.org/wiki/Dharma_Productions\" title=\"Dharma Productions\">Dharma Productions</a> (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>"}, {"title": "Dharma Productions", "link": "https://wikipedia.org/wiki/Dharma_Productions"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian physicist and mathematician (d. 1991)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Ljubov_Rebane\" title=\"Ljubov Rebane\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian physicist and mathematician (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ljubov_Rebane\" title=\"Ljubov Rebane\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian physicist and mathematician (d. 1991)", "links": [{"title": "Ljubov Rebane", "link": "https://wikipedia.org/wiki/Ljubov_Rebane"}]}, {"year": "1930", "text": "<PERSON>, American game designer, co-created <PERSON><PERSON><PERSON> (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American game designer, co-created <a href=\"https://wikipedia.org/wiki/Twister_(game)\" title=\"Twister (game)\">Twister</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American game designer, co-created <a href=\"https://wikipedia.org/wiki/Twister_(game)\" title=\"Twister (game)\">Twister</a> (d. 2013)", "links": [{"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>_(inventor)"}, {"title": "<PERSON><PERSON><PERSON> (game)", "link": "https://wikipedia.org/wiki/Twister_(game)"}]}, {"year": "1930", "text": "<PERSON>, Estonian historian and academic (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4e\" title=\"<PERSON>\"><PERSON></a>, Estonian historian and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A4e\" title=\"<PERSON>\"><PERSON></a>, Estonian historian and academic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4e"}]}, {"year": "1931", "text": "<PERSON>, American journalist, author, and screenwriter (d. 2009)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ke\" title=\"<PERSON>ke\"><PERSON></a>, American journalist, author, and screenwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ke\" title=\"<PERSON>ke\"><PERSON></a>, American journalist, author, and screenwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bud_Shrake"}]}, {"year": "1932", "text": "<PERSON>, English intelligence officer", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English intelligence officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English intelligence officer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian composer and educator (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Canadian composer and educator (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Canadian composer and educator (d. 2017)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1935", "text": "<PERSON>, French actress and author (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Scottish footballer and coach (d. 1996)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, Scottish footballer and coach (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, Scottish footballer and coach (d. 1996)", "links": [{"title": "<PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1937", "text": "<PERSON>, Spanish-Mexican author and illustrator", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Sergio_<PERSON>%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Spanish-Mexican author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sergio_Aragon%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Spanish-Mexican author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sergio_Aragon%C3%A9s"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Polish fencer and mountaineer (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish fencer and mountaineer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish fencer and mountaineer (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actress, comedian, and singer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a>, American actress, comedian, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a>, American actress, comedian, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American pianist, composer, and conductor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joan Tower\"><PERSON></a>, American pianist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joan_Tower\" title=\"Joan Tower\"><PERSON></a>, American pianist, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, American actress, painter, and photographer (d. 2020)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Brigid_Berlin\" title=\"Brigid Berlin\"><PERSON><PERSON><PERSON></a>, American actress, painter, and photographer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brigid_Berlin\" title=\"Brigid Berlin\"><PERSON><PERSON><PERSON></a>, American actress, painter, and photographer (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B<PERSON>id_Berlin"}]}, {"year": "1939", "text": "<PERSON>, American outlaw country music singer-songwriter and guitarist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American outlaw country music singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American outlaw country music singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Japanese biologist and immunologist, Nobel Prize laureate", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tonegawa\" title=\"<PERSON><PERSON><PERSON> To<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese biologist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tonegawa\" title=\"<PERSON><PERSON><PERSON> To<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese biologist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tonegawa"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1940", "text": "<PERSON>, American scientist (d. 2017)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, American scientist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, American scientist (d. 2017)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(scientist)"}]}, {"year": "1940", "text": "<PERSON>, American painter and illustrator (d. 2007)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and illustrator (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and illustrator (d. 2007)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1940", "text": "<PERSON>, English-Spanish singer-songwriter and actress (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Spanish singer-songwriter and actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Spanish singer-songwriter and actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English illustrator", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Roger Law\"><PERSON></a>, English illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Roger Law\"><PERSON></a>, English illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, South African ballerina and director", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African ballerina and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African ballerina and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American trombonist and tuba player (d. 2025)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist and tuba player (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist and tuba player (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English cricketer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2011)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English engineer and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English biochemist and biologist, Nobel Prize laureate", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1943", "text": "<PERSON>, English singer-songwriter and bass player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American author, academic, and activist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, academic, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, academic, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>wo<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>wo<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>wo<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>woos<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Swo<PERSON>ie_<PERSON>z"}]}, {"year": "1946", "text": "<PERSON>, American basketball player and commentator", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and commentator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English cricketer and educator", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American scientist, academic and educator", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist, academic and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist, academic and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actress and comedian", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English footballer and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Polish-Israeli engineer and academic (d. 2018)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli engineer and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli engineer and academic (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter (d. 1988)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 1988)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American guitarist (d. 2006)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Northern Irish politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Robinson"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Turkish composer (d. 2005)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish composer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish composer (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>bar"}]}, {"year": "1952", "text": "<PERSON>, English politician, Minister of State for Transport", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Transport\" title=\"Minister of State for Transport\">Minister of State for Transport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Transport\" title=\"Minister of State for Transport\">Minister of State for Transport</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of State for Transport", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Transport"}]}, {"year": "1952", "text": "<PERSON>, Russian footballer, coach, and manager (d. 2017)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer, coach, and manager (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer, coach, and manager (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Miller\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American businesswoman and activist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Cypriot footballer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_<PERSON>\" title=\"De<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demetris_<PERSON>zas"}]}, {"year": "1954", "text": "<PERSON>, American bassist and composer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hearn\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hearn\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patrick_O%27Hearn"}]}, {"year": "1954", "text": "<PERSON>, English economist and environmentalist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and environmentalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and environmentalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American author and playwright", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American lawyer and politician, 41st Governor of Colorado", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor of Colorado</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor of Colorado</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Colorado", "link": "https://wikipedia.org/wiki/Governor_of_Colorado"}]}, {"year": "1956", "text": "<PERSON>, English sociologist and academic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Iranian painter, sculptor, and journalist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian painter, sculptor, and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian painter, sculptor, and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, Haitian-Canadian journalist and politician, 27th Governor General of Canada", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Micha%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Haitian-Canadian journalist and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Haitian-Canadian journalist and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Micha%C3%<PERSON><PERSON>_<PERSON>"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1957", "text": "<PERSON>, Portuguese engineer and politician, 119th Prime Minister of Portugal", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_S%C3%B3crates\" title=\"<PERSON>\"><PERSON></a>, Portuguese engineer and politician, 119th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Portugal\" class=\"mw-redirect\" title=\"List of Prime Ministers of Portugal\">Prime Minister of Portugal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_S%C3%B3crates\" title=\"<PERSON>\"><PERSON></a>, Portuguese engineer and politician, 119th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Portugal\" class=\"mw-redirect\" title=\"List of Prime Ministers of Portugal\">Prime Minister of Portugal</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_S%C3%B3crates"}, {"title": "List of Prime Ministers of Portugal", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Portugal"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bloodvessel"}]}, {"year": "1958", "text": "<PERSON>, American comedian, actor, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Australian composer and conductor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "The Barbarian, Tongan wrestler", "html": "1958 - <a href=\"https://wikipedia.org/wiki/The_Barbarian_(wrestler)\" title=\"The Barbarian (wrestler)\">The Barbarian</a>, Tongan wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Barbarian_(wrestler)\" title=\"The Barbarian (wrestler)\">The Barbarian</a>, Tongan wrestler", "links": [{"title": "The Barbarian (wrestler)", "link": "https://wikipedia.org/wiki/The_Barbarian_(wrestler)"}]}, {"year": "1959", "text": "<PERSON>, Canadian ice hockey player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1961", "text": "<PERSON>, Australian journalist and game show host", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_TV_presenter)\" title=\"<PERSON> (Australian TV presenter)\"><PERSON></a>, Australian journalist and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_TV_presenter)\" title=\"<PERSON> (Australian TV presenter)\"><PERSON></a>, Australian journalist and game show host", "links": [{"title": "<PERSON> (Australian TV presenter)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_TV_presenter)"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American wrestler", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American rock drummer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>-Savoy,  Norwegian musician and songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian musician and songwriter", "links": [{"title": "<PERSON>-Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American lawyer and politician, 55th Governor of New Jersey", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1962", "text": "<PERSON>, Estonian badminton player and diplomat, Estonia Ambassador to Russia", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian badminton player and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_Estonia_to_Russia\" class=\"mw-redirect\" title=\"List of Ambassadors of Estonia to Russia\">Estonia Ambassador to Russia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian badminton player and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_Estonia_to_Russia\" class=\"mw-redirect\" title=\"List of Ambassadors of Estonia to Russia\">Estonia Ambassador to Russia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_<PERSON>"}, {"title": "List of Ambassadors of Estonia to Russia", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_Estonia_to_Russia"}]}, {"year": "1962", "text": "<PERSON>, American journalist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and fashion designer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Scottish footballer and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Se<PERSON>d"}]}, {"year": "1963", "text": "<PERSON>, American engineer and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Dutch lawyer and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch lawyer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actress, dancer, and director", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ickers"}]}, {"year": "1965", "text": "<PERSON>, Australian sprinter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian darts player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian darts player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian darts player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Irish author and poet (d. 2009)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Irish author and poet (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Irish author and poet (d. 2009)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1965", "text": "<PERSON>, American football player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter, producer, and actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gray\"><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Estonian painter and illustrator", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian painter and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Bosnian Serb convicted of war crimes by the ICTY", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Milan_Luki%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian Serb convicted of war crimes by the <a href=\"https://wikipedia.org/wiki/ICTY\" class=\"mw-redirect\" title=\"ICTY\">ICTY</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Luki%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian Serb convicted of war crimes by the <a href=\"https://wikipedia.org/wiki/ICTY\" class=\"mw-redirect\" title=\"ICTY\">ICTY</a>", "links": [{"title": "Milan Lukić", "link": "https://wikipedia.org/wiki/Milan_Luki%C4%87"}, {"title": "ICTY", "link": "https://wikipedia.org/wiki/ICTY"}]}, {"year": "1967", "text": "<PERSON>, Croatian footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0timac\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0timac\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Igor_%C5%A0timac"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Scottish author", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American journalist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American author and illustrator", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American chess player and educator", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Australian-American triathlete", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian-American triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian-American triathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, actress, and former beauty pageant winner", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>iston\" title=\"<PERSON><PERSON>e Peniston\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, actress, and former beauty pageant winner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>e <PERSON>iston\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, actress, and former beauty pageant winner", "links": [{"title": "Ce<PERSON>e <PERSON>", "link": "https://wikipedia.org/wiki/CeCe_Peniston"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Australian singer-songwriter and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ates\" title=\"<PERSON><PERSON><PERSON> Coates\"><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Coates\"><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and producer", "links": [{"title": "Cheyne Coates", "link": "https://wikipedia.org/wiki/Cheyne_Coates"}]}, {"year": "1970", "text": "<PERSON>, Russian-Canadian ice hockey player and coach (d. 2011)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Canadian ice hockey player and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Canadian ice hockey player and coach (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian-English journalist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American alternative country singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American alternative country singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American alternative country singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Estonian poet and illustrator", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Asko_K%C3%BCnnap\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asko_K%C3%BCnnap\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asko_K%C3%BCnnap"}]}, {"year": "1971", "text": "<PERSON>, Irish singer-songwriter (d. 2018)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Riordan\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Riordan\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dolores_<PERSON>%27R<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor and model", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, English actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>dr<PERSON>_<PERSON>\" title=\"<PERSON>dr<PERSON>\"><PERSON><PERSON><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>dr<PERSON>_<PERSON>\" title=\"<PERSON>dr<PERSON>\"><PERSON><PERSON><PERSON></a>, English actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Idris_Elba"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Lithuanian footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%ABnas\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%ABnas\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saulius_Mikalaj%C5%ABnas"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Italian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian-English tennis player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Italian rugby player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, Italian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, Italian rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alessandro_Troncon"}]}, {"year": "1974", "text": "<PERSON>, English tennis player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Swedish singer-songwriter and musician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American baseball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Japanese judoka and politician", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese judoka and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese judoka and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1976", "text": "<PERSON>, Brazilian singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, English actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Spanish footballer (d. 2013)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3pez\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3pez\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>%C3%B3pez"}]}, {"year": "1976", "text": "<PERSON>, American decathlete and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Adler\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Adler\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON> Adler", "link": "https://wikipedia.org/wiki/Cisco_Adler"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American rapper", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(rapper)"}]}, {"year": "1978", "text": "<PERSON>, Venezuelan baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alex_Escobar"}]}, {"year": "1978", "text": "<PERSON><PERSON>, English actor and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Japanese footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Greek boxer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Italian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Mexican footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ri%C3%A1<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American wrestler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Low_Ki\" title=\"Low Ki\">Low Ki</a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Low_Ki\" title=\"Low Ki\">Low Ki</a>, American wrestler", "links": [{"title": "Low Ki", "link": "https://wikipedia.org/wiki/Low_Ki"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American wrestler and singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English singer and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Kerry_Kat<PERSON>\" title=\"Kerry <PERSON>\"><PERSON></a>, English singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Kat<PERSON>\" title=\"Kerry <PERSON>\"><PERSON></a>, English singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kerry_Katona"}]}, {"year": "1980", "text": "<PERSON>, Nigerian boxer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Nigerian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Japanese footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Hong Kong singer and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hong Kong singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hong Kong singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Jamaican cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(West_Indian_cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (West Indian cricketer)\"><PERSON></a>, Jamaican cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(West_Indian_cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (West Indian cricketer)\"><PERSON></a>, Jamaican cricketer", "links": [{"title": "<PERSON> (West Indian cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(West_Indian_cricketer)"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Johnson"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, English socialite and author", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Middleton\" title=\"<PERSON><PERSON> Middleton\"><PERSON><PERSON></a>, English socialite and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Middleton\" title=\"<PERSON><PERSON> Middleton\"><PERSON><PERSON></a>, English socialite and author", "links": [{"title": "Pippa Middleton", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Middleton"}]}, {"year": "1983", "text": "<PERSON>, American wrestler and strongman", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and strongman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and strongman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Swedish skier", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Northern Irish cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress and comedian", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish novelist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ma%C5%82gorzata_Rejmer\" title=\"Małgorzata Rejmer\"><PERSON><PERSON>gor<PERSON><PERSON>j<PERSON></a>, Polish novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma%C5%82gorzata_Rejmer\" title=\"Małgorzata Rejmer\"><PERSON><PERSON>gor<PERSON><PERSON>j<PERSON></a>, Polish novelist", "links": [{"title": "Małgorzata Rejmer", "link": "https://wikipedia.org/wiki/Ma%C5%82gorza<PERSON>_<PERSON><PERSON>mer"}]}, {"year": "1986", "text": "<PERSON>, Australian rugby league player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Filipino-American singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino-American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino-American singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Turkish basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Emir_Preld%C5%BEi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emir_Preld%C5%BEi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emir_Preld%C5%BEi%C4%87"}]}, {"year": "1988", "text": "<PERSON>, French-Japanese actor and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Japanese actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Japanese actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_Fuji<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English singer-songwriter and actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and actor", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1988", "text": "<PERSON>, Italian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Greek footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Nikos_Boutzikos\" title=\"Nikos Boutzikos\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nikos_Boutzikos\" title=\"Nikos Boutzikos\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "Nikos <PERSON>", "link": "https://wikipedia.org/wiki/Nikos_Boutzikos"}]}, {"year": "1989", "text": "<PERSON>, South Korean actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>un\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>un"}]}, {"year": "1990", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Samoan rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Young_Tonumaipea\" title=\"Young Tonumaipea\"><PERSON></a>, Samoan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Young_Tonumaipea\" title=\"Young Tonumaipea\"><PERSON></a>, Samoan rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Young_Tonumaipea"}]}, {"year": "1993", "text": "<PERSON>, American rapper", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Famous_Dex\" title=\"Famous Dex\"><PERSON> <PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Famous_Dex\" title=\"Famous Dex\"><PERSON></a>, American rapper", "links": [{"title": "Famous Dex", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Italian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mattia_Valoti"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi cricketer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Colombian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Tello"}]}, {"year": "1996", "text": "<PERSON>, American rapper", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, American rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American swimmer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Australian rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Jai_Field\" title=\"Jai Field\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jai_Field\" title=\"Jai Field\"><PERSON></a>, Australian rugby league player", "links": [{"title": "Jai Field", "link": "https://wikipedia.org/wiki/Jai_Field"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese wrestler", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wrestler)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (wrestler)\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wrestler)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (wrestler)\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wrestler)"}]}, {"year": "1998", "text": "<PERSON>, Italian singer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian-Taiwanese singer-songwriter", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Taiwanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Taiwanese singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American singer-songwriter", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer-songwriter)\" title=\"<PERSON> (singer-songwriter)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer-songwriter)\" title=\"<PERSON> (singer-songwriter)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (singer-songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer-songwriter)"}]}, {"year": "2001", "text": "<PERSON><PERSON>, English actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American basketball player (d. 2021)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actor", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Asher Angel\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Asher Angel\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Canadian tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": " <PERSON><PERSON><PERSON>, Kazakh rhythmic gymnast", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kazakh rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kazakh rhythmic gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}], "Deaths": [{"year": "394", "text": "<PERSON><PERSON><PERSON>, Roman usurper", "html": "394 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman usurper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman usurper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>"}]}, {"year": "926", "text": "<PERSON><PERSON> of Liao, Khitan ruler (b. 872)", "html": "926 - <a href=\"https://wikipedia.org/wiki/Abaoji\" title=\"Abaoji\"><PERSON><PERSON> of Liao</a>, Khitan ruler (b. 872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abaoji\" title=\"Abaoji\"><PERSON><PERSON> of Liao</a>, Khitan ruler (b. 872)", "links": [{"title": "Abaoji", "link": "https://wikipedia.org/wiki/Abaoji"}]}, {"year": "952", "text": "<PERSON><PERSON><PERSON>, emperor of Japan (b. 923)", "html": "952 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>u\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a> (b. 923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>u\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a> (b. 923)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}]}, {"year": "957", "text": "<PERSON><PERSON><PERSON>, duke of Swabia (b. 930)", "html": "957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Swabia\" title=\"<PERSON><PERSON><PERSON>, Duke of Swabia\"><PERSON><PERSON><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Swabia\" title=\"Duchy of Swabia\">Swabia</a> (b. 930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Swabia\" title=\"<PERSON><PERSON><PERSON>, Duke of Swabia\"><PERSON><PERSON><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Swabia\" title=\"Duchy of Swabia\">Swabia</a> (b. 930)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Swabia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Swabia"}, {"title": "Duchy of Swabia", "link": "https://wikipedia.org/wiki/Duchy_of_Swabia"}]}, {"year": "972", "text": "<PERSON>, pope of the Catholic Church (b. 930)", "html": "972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a>, pope of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> (b. 930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a>, pope of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> (b. 930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "1276", "text": "<PERSON><PERSON><PERSON> <PERSON>, Italian cardinal (b. 1210)", "html": "1276 - <a href=\"https://wikipedia.org/wiki/Vicedomino_de_Vicedominis\" title=\"Vicedomino de Vicedominis\">Vicedomino de Vicedominis</a>, Italian cardinal (b. 1210)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vicedomino_de_Vicedominis\" title=\"Vicedomino de Vicedominis\">Vicedomino de Vicedominis</a>, Italian cardinal (b. 1210)", "links": [{"title": "Vicedomino de Vicedominis", "link": "https://wikipedia.org/wiki/Vicedomino_de_Vicedominis"}]}, {"year": "1431", "text": "<PERSON><PERSON><PERSON><PERSON>, Byzantine admiral and diplomat", "html": "1431 - <a href=\"https://wikipedia.org/wiki/Demet<PERSON><PERSON>_<PERSON>s_<PERSON>es\" title=\"Demet<PERSON>s Laskaris Leontares\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine admiral and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"De<PERSON><PERSON><PERSON> Leon<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine admiral and diplomat", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demet<PERSON><PERSON>_<PERSON>_<PERSON>es"}]}, {"year": "1511", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (b. 1481)", "html": "1511 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Yoshizumi\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1481)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Yoshizumi\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1481)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1553", "text": "<PERSON>, 47th Grandmaster of the Knights Hospitaller (b. c.1477)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>des_y_Coscon\" class=\"mw-redirect\" title=\"<PERSON>des y Coscon\"><PERSON> y Coscon</a>, 47th Grandmaster of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a> (b. c.1477)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_y_Coscon\" class=\"mw-redirect\" title=\"<PERSON> y Coscon\"><PERSON> y Coscon</a>, 47th Grandmaster of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a> (b. c.1477)", "links": [{"title": "Juan de Homedes y Coscon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>n"}, {"title": "Knights Hospitaller", "link": "https://wikipedia.org/wiki/Knights_Hospitaller"}]}, {"year": "1566", "text": "<PERSON><PERSON><PERSON> the Magnificent, Ottoman sultan (b. 1494)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/Suleim<PERSON>_the_Magnificent\" title=\"<PERSON><PERSON><PERSON> the Magnificent\"><PERSON><PERSON><PERSON> the Magnificent</a>, Ottoman sultan (b. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suleim<PERSON>_the_Magnificent\" title=\"<PERSON><PERSON><PERSON> the Magnificent\"><PERSON><PERSON><PERSON> the Magnificent</a>, Ottoman sultan (b. 1494)", "links": [{"title": "Sul<PERSON><PERSON> the Magnificent", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Magnificent"}]}, {"year": "1625", "text": "<PERSON>, Scottish historian and scholar (b. 1579)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and scholar (b. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and scholar (b. 1579)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1635", "text": "<PERSON><PERSON>, Dutch mathematician and astronomer (b. 1571)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/Metius\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and astronomer (b. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Met<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and astronomer (b. 1571)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Metius"}]}, {"year": "1649", "text": "<PERSON>, English geographer and explorer (b. 1574)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English geographer and explorer (b. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English geographer and explorer (b. 1574)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(explorer)"}]}, {"year": "1683", "text": "<PERSON><PERSON><PERSON>, French economist and politician, French Controller-General of Finances (b. 1619)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Finance_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Finance Ministers of France\">French Controller-General of Finances</a> (b. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Finance_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Finance Ministers of France\">French Controller-General of Finances</a> (b. 1619)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "List of Finance Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Finance_Ministers_of_France"}]}, {"year": "1708", "text": "Sir <PERSON>, 1st Baronet, English merchant and philanthropist, founded Morden College (b. 1623)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English merchant and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Morden_College\" title=\"Morden College\">Morden College</a> (b. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English merchant and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Morden_College\" title=\"Morden College\">Morden College</a> (b. 1623)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet"}, {"title": "Morden College", "link": "https://wikipedia.org/wiki/Morden_College"}]}, {"year": "1748", "text": "<PERSON>, English bishop and scholar (b. 1669)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and scholar (b. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and scholar (b. 1669)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1783", "text": "<PERSON>, Italian actor and author (b. 1710)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and author (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and author (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1808", "text": "<PERSON><PERSON><PERSON>, French historian and author (b. 1723)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and author (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and author (b. 1723)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1836", "text": "<PERSON><PERSON>, three terms mayor of San Antonio, in Spanish Texas (b. 1781)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_de_Abrego\" title=\"<PERSON><PERSON> de Abrego\"><PERSON><PERSON> Abrego</a>, three terms mayor of San Antonio, in Spanish Texas (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_de_Abrego\" title=\"<PERSON><PERSON> de Abrego\"><PERSON><PERSON> Abrego</a>, three terms mayor of San Antonio, in Spanish Texas (b. 1781)", "links": [{"title": "Gaspar <PERSON> de Abrego", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_de_Abrego"}]}, {"year": "1868", "text": "<PERSON>, American lawyer, judge, and politician (b. 1797)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Spanish engineer, designed the Ictineo I and Ictineo II (b. 1819)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Narc%C3%ADs_Monturiol\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish engineer, designed the <i><a href=\"https://wikipedia.org/wiki/Ictineo_I\" class=\"mw-redirect\" title=\"Ictineo I\">Ictineo I</a></i> and <i><a href=\"https://wikipedia.org/wiki/Ictineo_II\" class=\"mw-redirect\" title=\"Ictineo II\">Ictineo II</a></i> (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Narc%C3%ADs_Monturiol\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish engineer, designed the <i><a href=\"https://wikipedia.org/wiki/Ictineo_I\" class=\"mw-redirect\" title=\"Ictineo I\">Ictineo I</a></i> and <i><a href=\"https://wikipedia.org/wiki/Ictineo_II\" class=\"mw-redirect\" title=\"Ictineo II\">Ictineo II</a></i> (b. 1819)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narc%C3%ADs_Monturiol"}, {"title": "Ictineo I", "link": "https://wikipedia.org/wiki/Ictineo_I"}, {"title": "Ictineo II", "link": "https://wikipedia.org/wiki/Ictineo_II"}]}, {"year": "1891", "text": "<PERSON>, German-English businessman (b. 1815)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English businessman (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English businessman (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English chemist and engineer (b. 1827)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and engineer (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and engineer (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, French poet and critic, Nobel Prize laureate (b. 1839)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1839)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1919", "text": "Lord <PERSON>, English admiral and politician (b. 1846)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English admiral and politician (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English admiral and politician (b. 1846)", "links": [{"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American target shooter and geographer (b. 1855)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American target shooter and geographer (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American target shooter and geographer (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English race car driver and pilot (b. 1907)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and pilot (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and pilot (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English illustrator (b. 1867)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON> (Irish republican), executed by firing squad (b. 1915)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a>, executed by firing squad (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a>, executed by firing squad (b. 1915)", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)"}]}, {"year": "1940", "text": "<PERSON> (Irish Republican), executed by firing squad (b. 1894)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_Republican)\" class=\"mw-redirect\" title=\"<PERSON> (Irish Republican)\"><PERSON> (Irish Republican)</a>, executed by firing squad (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_Republican)\" class=\"mw-redirect\" title=\"<PERSON> (Irish Republican)\"><PERSON> (Irish Republican)</a>, executed by firing squad (b. 1894)", "links": [{"title": "<PERSON> (Irish Republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_Republican)"}]}, {"year": "1944", "text": "<PERSON>, American Bishop of the Methodist Episcopal Church, South", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American Bishop of the <a href=\"https://wikipedia.org/wiki/Methodist_Episcopal_Church,_South\" title=\"Methodist Episcopal Church, South\">Methodist Episcopal Church, South</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American Bishop of the <a href=\"https://wikipedia.org/wiki/Methodist_Episcopal_Church,_South\" title=\"Methodist Episcopal Church, South\">Methodist Episcopal Church, South</a>", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "Methodist Episcopal Church, South", "link": "https://wikipedia.org/wiki/Methodist_Episcopal_Church,_South"}]}, {"year": "1945", "text": "<PERSON>, American admiral (b. 1884)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Sr.\"><PERSON>.</a>, American admiral (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Sr<PERSON>\"><PERSON>.</a>, American admiral (b. 1884)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1949", "text": "<PERSON>, communist activist, executed (b. 1904)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, communist activist, executed (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, communist activist, executed (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, child internee, executed (b. 1941)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, child internee, executed (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Song Z<PERSON>hong\"><PERSON></a>, child internee, executed (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Song_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English tenor and actor (b. 1892)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tenor and actor (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tenor and actor (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>p"}]}, {"year": "1949", "text": "<PERSON>, communist activist, executed (b. 1904/1905)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, communist activist, executed (b. 1904/1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Xu Lin<PERSON>\"><PERSON></a>, communist activist, executed (b. 1904/1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Xu_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, general, executed (b. 1893)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general, executed (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general, executed (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English philosopher and author (b. 1886)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American lawyer and diplomat, United States Ambassador to Germany (b. 1867)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Germany\" class=\"mw-redirect\" title=\"United States Ambassador to Germany\">United States Ambassador to Germany</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Germany\" class=\"mw-redirect\" title=\"United States Ambassador to Germany\">United States Ambassador to Germany</a> (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to Germany", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Germany"}]}, {"year": "1952", "text": "<PERSON>, English actress, singer, and dancer (b. 1898)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and dancer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and dancer (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Polish mathematician (b. 1904)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>\" title=\"Wito<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>\" title=\"Wito<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, North Korean painter (b. 1916)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, North Korean painter (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, North Korean painter (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English actor (b. 1877)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English actress and comedian (b. 1927)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and comedian (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and comedian (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, German-Austrian composer (b. 1898)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian composer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian composer (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese tennis player (b. 1892)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese tennis player (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese tennis player (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1966", "text": "<PERSON>, American nurse, educator, and activist (b. 1879)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse, educator, and activist (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse, educator, and activist (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Dutch-South African journalist and politician, 7th Prime Minister of South Africa (b. 1901)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-South African journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-South African journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of South Africa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Africa"}]}, {"year": "1969", "text": "<PERSON>, Brazilian footballer (b. 1892)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "Perpetrator and victims of the Munich massacre\n<PERSON><PERSON><PERSON>, Palestinian terrorist (b. 1945)\n<PERSON>, American-Israeli weightlifter (b. 1944)\n<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish-Israeli weightlifter (b. 1944)\n<PERSON><PERSON><PERSON>, Israeli wrestling judge (b. 1931)\n<PERSON><PERSON>, Russian-Israeli wrestler (b. 1948)\n<PERSON><PERSON><PERSON>, Russian-Israeli runner and coach (b. 1932)\n<PERSON><PERSON>, Romanian shooting coach (b. 1919)\n<PERSON>, Israeli wrestler (b. 1954)\n<PERSON>, Romanian-Israeli fencer and coach (b. 1945)\n<PERSON><PERSON>, Polish-Israeli wrestler and coach (b. 1921)", "html": "1972 - Perpetrator and victims of the <a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich massacre</a>\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/Luttif_Afif\" title=\"<PERSON>tti<PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian terrorist (b. 1945)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American-Israeli weightlifter (b. 1944)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Ze%27ev_<PERSON>\" title=\"<PERSON><PERSON>'ev <PERSON>\"><PERSON><PERSON>'<PERSON><PERSON></a>, Polish-Israeli weightlifter (b. 1944)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Yossef_Gutfreund\" title=\"<PERSON><PERSON><PERSON>f<PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli wrestling judge (b. 1931)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Israeli wrestler (b. 1948)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Amitzur_Shapira\" title=\"Amitzur Shapira\">Amitzur Shapira</a>, Russian-Israeli runner and coach (b. 1932)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Kehat_Shorr\" title=\"Kehat Shorr\">Kehat Shorr</a>, Romanian shooting coach (b. 1919)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Mark_Slavin\" title=\"Mark Slavin\">Mark Slavin</a>, Israeli wrestler (b. 1954)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Andre_Spitzer\" title=\"Andre Spitzer\">Andre Spitzer</a>, Romanian-Israeli fencer and coach (b. 1945)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Yakov_Springer\" title=\"Yakov Springer\">Yakov Springer</a>, Polish-Israeli wrestler and coach (b. 1921)</li>\n</ul>", "no_year_html": "Perpetrator and victims of the <a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich massacre</a>\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/Luttif_Afif\" title=\"<PERSON>tti<PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian terrorist (b. 1945)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American-Israeli weightlifter (b. 1944)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Ze%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>'ev <PERSON>\"><PERSON><PERSON>'<PERSON><PERSON></a>, Polish-Israeli weightlifter (b. 1944)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Yossef_Gutfreund\" title=\"<PERSON><PERSON><PERSON>f<PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli wrestling judge (b. 1931)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Israeli wrestler (b. 1948)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Amitzur_Shapira\" title=\"Amitzur Shapira\">Amitzur Shapira</a>, Russian-Israeli runner and coach (b. 1932)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Kehat_Shorr\" title=\"Kehat Shorr\">Kehat Shorr</a>, Romanian shooting coach (b. 1919)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Mark_Slavin\" title=\"Mark Slavin\">Mark Slavin</a>, Israeli wrestler (b. 1954)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Andre_Spitzer\" title=\"Andre Spitzer\">Andre Spitzer</a>, Romanian-Israeli fencer and coach (b. 1945)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Yakov_Springer\" title=\"Yakov Springer\">Yakov Springer</a>, Polish-Israeli wrestler and coach (b. 1921)</li>\n</ul>", "links": [{"title": "Munich massacre", "link": "https://wikipedia.org/wiki/Munich_massacre"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luttif_Afif"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ze%27ev_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yo<PERSON><PERSON>_<PERSON>freund"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ra"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Shorr"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, Palestinian terrorist (b. 1945)", "text": null, "html": "<PERSON><PERSON><PERSON>, Palestinian terrorist (b. 1945) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Lu<PERSON><PERSON>_Afif\" title=\"<PERSON><PERSON><PERSON> A<PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian terrorist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Luttif_Afif\" title=\"<PERSON>tti<PERSON> Afif\"><PERSON><PERSON><PERSON></a>, Palestinian terrorist (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Luttif_Afif"}]}, {"year": "<PERSON>, American-Israeli weightlifter (b. 1944)", "text": null, "html": "<PERSON>, American-Israeli weightlifter (b. 1944) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American-Israeli weightlifter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American-Israeli weightlifter (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish-Israeli weightlifter (b. 1944)", "text": null, "html": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, Polish-Israeli weightlifter (b. 1944) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>'ev <PERSON>\"><PERSON><PERSON>'e<PERSON> <PERSON></a>, Polish-Israeli weightlifter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>'ev <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Polish-Israeli weightlifter (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Ze%27ev_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, Israeli wrestling judge (b. 1931)", "text": null, "html": "<PERSON><PERSON><PERSON>, Israeli wrestling judge (b. 1931) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Yo<PERSON><PERSON>_<PERSON>freund\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli wrestling judge (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Yo<PERSON><PERSON>_<PERSON>fre<PERSON>\" title=\"<PERSON><PERSON><PERSON>f<PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli wrestling judge (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>freund"}]}, {"year": "<PERSON><PERSON>, Russian-Israeli wrestler (b. 1948)", "text": null, "html": "<PERSON><PERSON>, Russian-Israeli wrestler (b. 1948) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Israeli wrestler (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Israeli wrestler (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, Russian-Israeli runner and coach (b. 1932)", "text": null, "html": "<PERSON><PERSON><PERSON>, Russian-Israeli runner and coach (b. 1932) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Am<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Israeli runner and coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Am<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Israeli runner and coach (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>ra"}]}, {"year": "<PERSON><PERSON>, Romanian shooting coach (b. 1919)", "text": null, "html": "<PERSON><PERSON>, Romanian shooting coach (b. 1919) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian shooting coach (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian shooting coach (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Ke<PERSON>_Shorr"}]}, {"year": "<PERSON>, Israeli wrestler (b. 1954)", "text": null, "html": "<PERSON>, Israeli wrestler (b. 1954) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli wrestler (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli wrestler (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, Romanian-Israeli fencer and coach (b. 1945)", "text": null, "html": "<PERSON>, Romanian-Israeli fencer and coach (b. 1945) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Israeli fencer and coach (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Israeli fencer and coach (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, Polish-Israeli wrestler and coach (b. 1921)", "text": null, "html": "<PERSON><PERSON>, Polish-Israeli wrestler and coach (b. 1921) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli wrestler and coach (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli wrestler and coach (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Russian-Swiss actress and ballerina (b. 1896)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss actress and ballerina (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss actress and ballerina (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor (b. 1885)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, French tennis player (b. 1882)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>is"}]}, {"year": "1978", "text": "<PERSON>, American record producer (b. 1931)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(record_producer)\" title=\"<PERSON> (record producer)\"><PERSON></a>, American record producer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(record_producer)\" title=\"<PERSON> (record producer)\"><PERSON></a>, American record producer (b. 1931)", "links": [{"title": "<PERSON> (record producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(record_producer)"}]}, {"year": "1978", "text": "<PERSON>, German cobbler and entrepreneur, founded <PERSON><PERSON> (b. 1900)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cobbler and entrepreneur, founded <a href=\"https://wikipedia.org/wiki/Adidas\" title=\"Adidas\">Adidas</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cobbler and entrepreneur, founded <a href=\"https://wikipedia.org/wiki/Adidas\" title=\"Adidas\">Adidas</a> (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Adidas", "link": "https://wikipedia.org/wiki/Adidas"}]}, {"year": "1979", "text": "<PERSON>, English organist and composer (b. 1910)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Turkish archaeologist, author, and academic (b. 1915)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish archaeologist, author, and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish archaeologist, author, and academic (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1914)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Italian conductor and composer (b. 1911)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Franco_Fe<PERSON>ra\" title=\"<PERSON>\"><PERSON></a>, Italian conductor and composer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco_Fe<PERSON>ra\" title=\"<PERSON>\"><PERSON></a>, Italian conductor and composer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_Ferrara"}]}, {"year": "1986", "text": "<PERSON>, American actress (b. 1896)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sweet\"><PERSON></a>, American actress (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American wrestler (b. 1950)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (b. 1950)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1990", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1941)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English cricketer and soldier (b. 1916)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian ice hockey player and sportscaster (b. 1922)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American playwright, screenwriter, and producer (b. 1912)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, screenwriter, and producer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, screenwriter, and producer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English pianist (b. 1944)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American trumpet player and bandleader (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trumpet player and bandleader (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trumpet player and bandleader (b. 1908)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1997", "text": "<PERSON><PERSON> <PERSON><PERSON>, English author and broadcaster (b. 1918)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author and broadcaster (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author and broadcaster (b. 1918)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Japanese director, producer, and screenwriter (b. 1910)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese director, producer, and screenwriter (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese director, producer, and screenwriter (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American-Filipino singer-songwriter, actor, and journalist (b. 1952)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Filipino singer-songwriter, actor, and journalist (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Filipino singer-songwriter, actor, and journalist (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON>o"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Swedish actor (b. 1928)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A4reg%C3%A5rd\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4reg%C3%A5rd\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actor (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A4reg%C3%A5rd"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Nauruan politician, 3rd President of Nauru (b. 1938)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nauruan politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nauruan politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Nauru", "link": "https://wikipedia.org/wiki/President_of_Nauru"}]}, {"year": "1999", "text": "<PERSON>, Canadian sportscaster (b. 1918)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sportscaster (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sportscaster (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>ier"}]}, {"year": "2005", "text": "<PERSON>, Pakistani journalist and poet (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani journalist and poet (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani journalist and poet (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Indonesian Military (b. 1918)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian Military (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian Military (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Dominican lawyer and politician, 2nd Prime Minister of Dominica (b. 1919)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Dominica\" title=\"Prime Minister of Dominica\">Prime Minister of Dominica</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Dominica\" title=\"Prime Minister of Dominica\">Prime Minister of Dominica</a> (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Dominica", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Dominica"}]}, {"year": "2007", "text": "<PERSON>, American author and poet (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Engle\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Engle\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Madeleine_L%27Engle"}]}, {"year": "2007", "text": "<PERSON>, Italian tenor (b. 1935)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actress (b. 1910)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Irish-Australian author (b. 1929)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian author (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Russian painter (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English director and editor (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and editor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and editor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American author, founded Project Gutenberg (b. 1947)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Gutenberg\" title=\"Project Gutenberg\"><PERSON> Gutenberg</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Gutenberg\" title=\"Project Gutenberg\"><PERSON> Gutenberg</a> (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Project Gutenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, German architect (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hm\" title=\"<PERSON>\"><PERSON></a>, German architect (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hm\" title=\"<PERSON>\"><PERSON></a>, German architect (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elisabeth_B%C3%B6hm"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Scottish scout leader, founded World Federation of Independent Scouts (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dring\"><PERSON><PERSON></a>, Scottish scout leader, founded <a href=\"https://wikipedia.org/wiki/World_Federation_of_Independent_Scouts\" title=\"World Federation of Independent Scouts\">World Federation of Independent Scouts</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dr<PERSON>\"><PERSON><PERSON></a>, Scottish scout leader, founded <a href=\"https://wikipedia.org/wiki/World_Federation_of_Independent_Scouts\" title=\"World Federation of Independent Scouts\">World Federation of Independent Scouts</a> (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "World Federation of Independent Scouts", "link": "https://wikipedia.org/wiki/World_Federation_of_Independent_Scouts"}]}, {"year": "2012", "text": "<PERSON>, American actor and playwright (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American businessman (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Art_Modell\" title=\"Art Modell\"><PERSON></a>, American businessman (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Modell\" title=\"Art Modell\"><PERSON></a>, American businessman (b. 1925)", "links": [{"title": "Art Modell", "link": "https://wikipedia.org/wiki/Art_Modell"}]}, {"year": "2012", "text": "<PERSON>, Argentine footballer and manager (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English naturalist, television presenter and author (b. 1946)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English naturalist, television presenter and author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English naturalist, television presenter and author (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American author (b. 1950)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Ann_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ann_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Burmese economist and scholar (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Burmese economist and scholar (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Burmese economist and scholar (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Puerto Rican-American baseball player and coach (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Santiago_Rosario\" title=\"Santiago Rosario\"><PERSON> Rosario</a>, Puerto Rican-American baseball player and coach (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Rosario\" title=\"Santiago Rosario\"><PERSON> Rosario</a>, Puerto Rican-American baseball player and coach (b. 1939)", "links": [{"title": "Santiago Rosario", "link": "https://wikipedia.org/wiki/Santiago_Rosario"}]}, {"year": "2014", "text": "<PERSON>, Norwegian bishop and theologian (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ik\" title=\"<PERSON> Bondevik\"><PERSON></a>, Norwegian bishop and theologian (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Odd Bondevik\"><PERSON></a>, Norwegian bishop and theologian (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bondevik"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, American bishop (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/C<PERSON><PERSON>_B._Flores\" title=\"C<PERSON>lo B. Flores\"><PERSON><PERSON><PERSON></a>, American bishop (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>._<PERSON>\" title=\"C<PERSON>lo B. Flores\"><PERSON><PERSON><PERSON></a>, American bishop (b. 1948)", "links": [{"title": "Cirilo B. Flores", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_B._Flores"}]}, {"year": "2014", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Belarusian chess player and educator (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian chess player and educator (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian chess player and educator (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Scottish footballer (b. 1961)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actor (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American baseball player and coach (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American general and pilot (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Australian journalist and television host (b. 1944)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and television host (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and television host (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American feminist author and activist (b. 1934)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American feminist author and activist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American feminist author and activist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American billionaire businessman (b. 1926)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American billionaire businessman (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American billionaire businessman (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, English actress (b. 1930)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American comedian and actor (b. 1927)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Will_Jordan"}]}, {"year": "2018", "text": "<PERSON>, American actor, director and producer (b. 1936)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director and producer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director and producer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Zimbabwean politician, 2nd President of Zimbabwe (b. 1924)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Zimbabwe\" title=\"President of Zimbabwe\">President of Zimbabwe</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Zimbabwe\" title=\"President of Zimbabwe\">President of Zimbabwe</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Zimbabwe", "link": "https://wikipedia.org/wiki/President_of_Zimbabwe"}]}, {"year": "2020", "text": "<PERSON>, American baseball player (b. 1939)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, French actor (b. 1933)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actor (b. 1966)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish-American activist (b. 1998)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Ay%C5%9Fenur_Ezgi_Eygi\" class=\"mw-redirect\" title=\"Ayşenur Ezgi Eygi\"><PERSON><PERSON>ş<PERSON><PERSON> Ez<PERSON></a>, Turkish-American activist (b. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ay%C5%9Fenur_Ezgi_Eygi\" class=\"mw-redirect\" title=\"Ayşenur Ezgi Eygi\"><PERSON><PERSON>ş<PERSON>ur <PERSON></a>, Turkish-American activist (b. 1998)", "links": [{"title": "Ayşenur Ezgi Eygi", "link": "https://wikipedia.org/wiki/Ay%C5%9Fenur_Ezgi_Eygi"}]}, {"year": "2024", "text": "<PERSON>, German visual artist (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German visual artist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Horn\"><PERSON></a>, German visual artist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American songwriter (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Canadian First Nations leader (b. 1961/1962)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian First Nations leader (b. 1961/1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian First Nations leader (b. 1961/1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Scottish footballer (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}