{"date": "June 4", "url": "https://wikipedia.org/wiki/June_4", "data": {"Events": [{"year": "1411", "text": "King <PERSON> grants a monopoly for the ripening of Roquefort cheese to the people of Roquefort-sur-Soulzon as they had been doing for centuries.", "html": "1411 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VI of France\"><PERSON> VI</a> grants a monopoly for the ripening of <a href=\"https://wikipedia.org/wiki/Roquefort\" title=\"Roquefort\">Roquefort cheese</a> to the people of <a href=\"https://wikipedia.org/wiki/Roquefort-sur-Soulzon\" title=\"Roquefort-sur-Soulzon\">Roquefort-sur-Soulzon</a> as they had been doing for centuries.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VI of France\"><PERSON> VI</a> grants a monopoly for the ripening of <a href=\"https://wikipedia.org/wiki/Roquefort\" title=\"Roquefort\">Roquefort cheese</a> to the people of <a href=\"https://wikipedia.org/wiki/Roquefort-sur-Soulzon\" title=\"Roquefort-sur-Soulzon\">Roquefort-sur-Soulzon</a> as they had been doing for centuries.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_VI_of_France"}, {"title": "R<PERSON>quefort", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Roquefort-sur-Soulzon", "link": "https://wikipedia.org/wiki/Roquefort-sur-Soulzon"}]}, {"year": "1561", "text": "The steeple of St Paul's, the medieval cathedral of London, is destroyed in a fire caused by lightning and is never rebuilt.", "html": "1561 - The <a href=\"https://wikipedia.org/wiki/Steeple\" title=\"Steeple\">steeple</a> of <a href=\"https://wikipedia.org/wiki/Old_St_Paul%27s_Cathedral\" title=\"Old St Paul's Cathedral\">St Paul's</a>, the medieval cathedral of <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, is destroyed in a fire caused by lightning and is never rebuilt.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Steeple\" title=\"Steeple\">steeple</a> of <a href=\"https://wikipedia.org/wiki/Old_St_Paul%27s_Cathedral\" title=\"Old St Paul's Cathedral\">St Paul's</a>, the medieval cathedral of <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, is destroyed in a fire caused by lightning and is never rebuilt.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Steeple"}, {"title": "Old St Paul's Cathedral", "link": "https://wikipedia.org/wiki/Old_St_Paul%27s_Cathedral"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1615", "text": "Siege of Osaka: Forces under <PERSON> take Osaka Castle in Japan.", "html": "1615 - <a href=\"https://wikipedia.org/wiki/Siege_of_Osaka\" title=\"Siege of Osaka\">Siege of Osaka</a>: Forces under <a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa <PERSON>u</a> take <a href=\"https://wikipedia.org/wiki/Osaka_Castle\" title=\"Osaka Castle\">Osaka Castle</a> in Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Osaka\" title=\"Siege of Osaka\">Siege of Osaka</a>: Forces under <a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa <PERSON>u</a> take <a href=\"https://wikipedia.org/wiki/Osaka_Castle\" title=\"Osaka Castle\">Osaka Castle</a> in Japan.", "links": [{"title": "Siege of Osaka", "link": "https://wikipedia.org/wiki/Siege_of_Osaka"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>u"}, {"title": "Osaka Castle", "link": "https://wikipedia.org/wiki/Osaka_Castle"}]}, {"year": "1745", "text": "Battle of Hohenfriedberg: <PERSON> the Great's Prussian army decisively defeated an Austrian army under Prince <PERSON> of Lorraine during the War of the Austrian Succession.", "html": "1745 - <a href=\"https://wikipedia.org/wiki/Battle_of_Hohenfriedberg\" title=\"Battle of Hohenfriedberg\">Battle of Hohenfriedberg</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great's</a> <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussian</a> army decisively defeated an Austrian army under <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Lorraine\" title=\"Prince <PERSON> of Lorraine\">Prince <PERSON> of Lorraine</a> during the <a href=\"https://wikipedia.org/wiki/War_of_the_Austrian_Succession\" title=\"War of the Austrian Succession\">War of the Austrian Succession</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Hohenfriedberg\" title=\"Battle of Hohenfriedberg\">Battle of Hohenfriedberg</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great's</a> <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussian</a> army decisively defeated an Austrian army under <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Lorraine\" title=\"Prince <PERSON> of Lorraine\">Prince <PERSON> of Lorraine</a> during the <a href=\"https://wikipedia.org/wiki/War_of_the_Austrian_Succession\" title=\"War of the Austrian Succession\">War of the Austrian Succession</a>.", "links": [{"title": "Battle of Hohenfriedberg", "link": "https://wikipedia.org/wiki/Battle_of_Hohenfriedberg"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "Prince <PERSON> of Lorraine", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_Lorraine"}, {"title": "War of the Austrian Succession", "link": "https://wikipedia.org/wiki/War_of_the_Austrian_Succession"}]}, {"year": "1760", "text": "Great Upheaval: New England planters arrive to claim land in Nova Scotia, Canada, taken from the Acadians.", "html": "1760 - <a href=\"https://wikipedia.org/wiki/Great_Upheaval\" class=\"mw-redirect\" title=\"Great Upheaval\">Great Upheaval</a>: <a href=\"https://wikipedia.org/wiki/New_England\" title=\"New England\">New England</a> planters arrive to claim land in <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a>, Canada, taken from the <a href=\"https://wikipedia.org/wiki/Acadians\" title=\"Acadians\">Acadians</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Upheaval\" class=\"mw-redirect\" title=\"Great Upheaval\">Great Upheaval</a>: <a href=\"https://wikipedia.org/wiki/New_England\" title=\"New England\">New England</a> planters arrive to claim land in <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a>, Canada, taken from the <a href=\"https://wikipedia.org/wiki/Acadians\" title=\"Acadians\">Acadians</a>.", "links": [{"title": "Great Upheaval", "link": "https://wikipedia.org/wiki/Great_Upheaval"}, {"title": "New England", "link": "https://wikipedia.org/wiki/New_England"}, {"title": "Nova Scotia", "link": "https://wikipedia.org/wiki/Nova_Scotia"}, {"title": "Acadians", "link": "https://wikipedia.org/wiki/Acadians"}]}, {"year": "1783", "text": "The Montgolfier brothers publicly demonstrate their montgolfière (hot air balloon).", "html": "1783 - The <a href=\"https://wikipedia.org/wiki/Montgolfier_brothers\" title=\"Montgolfier brothers\">Montgolfier brothers</a> publicly demonstrate their <i>montgolfière</i> (<a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Montgolfier_brothers\" title=\"Montgolfier brothers\">Montgolfier brothers</a> publicly demonstrate their <i>montgolfière</i> (<a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a>).", "links": [{"title": "Montgolfier brothers", "link": "https://wikipedia.org/wiki/Montgol<PERSON>r_brothers"}, {"title": "Hot air balloon", "link": "https://wikipedia.org/wiki/Hot_air_balloon"}]}, {"year": "1784", "text": "<PERSON><PERSON><PERSON><PERSON> becomes the first woman to fly in an untethered hot air balloon. Her flight covers four kilometres (2.5 mi) in 45 minutes, and reached 1,500 metres (4,900 ft) altitude (estimated).", "html": "1784 - <a href=\"https://wikipedia.org/wiki/%C3%89lisabeth_Thible\" title=\"É<PERSON>abeth Thible\"><PERSON><PERSON><PERSON><PERSON> Thible</a> becomes the first woman to fly in an untethered hot air balloon. Her flight covers four kilometres (2.5 mi) in 45 minutes, and reached 1,500 metres (4,900 ft) altitude (estimated).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89lisabeth_Thible\" title=\"É<PERSON>abeth Thible\"><PERSON><PERSON><PERSON><PERSON> Thible</a> becomes the first woman to fly in an untethered hot air balloon. Her flight covers four kilometres (2.5 mi) in 45 minutes, and reached 1,500 metres (4,900 ft) altitude (estimated).", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lisabeth_Thible"}]}, {"year": "1792", "text": "Captain <PERSON> claims Puget Sound for the Kingdom of Great Britain.", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Captain_(Royal_Navy)\" title=\"Captain (Royal Navy)\">Captain</a> <a href=\"https://wikipedia.org/wiki/George_Vancouver\" title=\"George Vancouver\"><PERSON></a> claims <a href=\"https://wikipedia.org/wiki/Puget_Sound\" title=\"Puget Sound\">Puget Sound</a> for the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Captain_(Royal_Navy)\" title=\"Captain (Royal Navy)\">Captain</a> <a href=\"https://wikipedia.org/wiki/George_Vancouver\" title=\"George Vancouver\"><PERSON></a> claims <a href=\"https://wikipedia.org/wiki/Puget_Sound\" title=\"Puget Sound\">Puget Sound</a> for the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a>.", "links": [{"title": "Captain (Royal Navy)", "link": "https://wikipedia.org/wiki/Captain_(Royal_Navy)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}, {"title": "Puget Sound", "link": "https://wikipedia.org/wiki/Puget_Sound"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}]}, {"year": "1802", "text": "King <PERSON> of Sardinia abdicates his throne in favor of his brother, <PERSON>.", "html": "1802 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON> of Sardinia</a> abdicates his throne in favor of his brother, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON></a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON> of Sardinia</a> abdicates his throne in favor of his brother, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON></a>.", "links": [{"title": "<PERSON> of Sardinia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia"}, {"title": "<PERSON> of Sardinia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Sardinia"}]}, {"year": "1812", "text": "Following Louisiana's admittance as a U.S. state, the Louisiana Territory is renamed the Missouri Territory.", "html": "1812 - Following <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>'s admittance as a <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>, the <a href=\"https://wikipedia.org/wiki/Louisiana_Territory\" title=\"Louisiana Territory\">Louisiana Territory</a> is renamed the <a href=\"https://wikipedia.org/wiki/Missouri_Territory\" title=\"Missouri Territory\">Missouri Territory</a>.", "no_year_html": "Following <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>'s admittance as a <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>, the <a href=\"https://wikipedia.org/wiki/Louisiana_Territory\" title=\"Louisiana Territory\">Louisiana Territory</a> is renamed the <a href=\"https://wikipedia.org/wiki/Missouri_Territory\" title=\"Missouri Territory\">Missouri Territory</a>.", "links": [{"title": "Louisiana", "link": "https://wikipedia.org/wiki/Louisiana"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}, {"title": "Louisiana Territory", "link": "https://wikipedia.org/wiki/Louisiana_Territory"}, {"title": "Missouri Territory", "link": "https://wikipedia.org/wiki/Missouri_Territory"}]}, {"year": "1825", "text": "General <PERSON>, a French officer in the American Revolutionary War, speaks at what would become Lafayette Square in Buffalo, New York, during his visit to the United States.", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\">General <PERSON></a>, a French officer in the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>, speaks at what would become <a href=\"https://wikipedia.org/wiki/Lafayette_Square_(Buffalo,_New_York)\" title=\"Lafayette Square (Buffalo, New York)\">Lafayette Square</a> in <a href=\"https://wikipedia.org/wiki/Buffalo,_New_York\" title=\"Buffalo, New York\">Buffalo, New York</a>, during <a href=\"https://wikipedia.org/wiki/Visit_of_the_<PERSON>_<PERSON>_<PERSON>_to_the_United_States\" title=\"Visit of the <PERSON> to the United States\">his visit to the United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\">General <PERSON></a>, a French officer in the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>, speaks at what would become <a href=\"https://wikipedia.org/wiki/Lafayette_Square_(Buffalo,_New_York)\" title=\"Lafayette Square (Buffalo, New York)\">Lafayette Square</a> in <a href=\"https://wikipedia.org/wiki/Buffalo,_New_York\" title=\"Buffalo, New York\">Buffalo, New York</a>, during <a href=\"https://wikipedia.org/wiki/Visit_of_the_<PERSON>_<PERSON>_<PERSON>_to_the_United_States\" title=\"Visit of the <PERSON> to the United States\">his visit to the United States</a>.", "links": [{"title": "<PERSON>, Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Lafayette Square (Buffalo, New York)", "link": "https://wikipedia.org/wiki/Lafayette_Square_(Buffalo,_New_York)"}, {"title": "Buffalo, New York", "link": "https://wikipedia.org/wiki/Buffalo,_New_York"}, {"title": "Visit of the <PERSON> to the United States", "link": "https://wikipedia.org/wiki/Visit_of_the_Marquis_de_<PERSON>_to_the_United_States"}]}, {"year": "1855", "text": "Major <PERSON> departs New York aboard the USS Supply to procure camels to establish the U.S. Camel Corps.", "html": "1855 - Major <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> departs New York aboard the <a href=\"https://wikipedia.org/wiki/USS_Supply_(1846)\" title=\"USS Supply (1846)\">USS <i>Supply</i></a> to procure <a href=\"https://wikipedia.org/wiki/Camel\" title=\"Camel\">camels</a> to establish the <a href=\"https://wikipedia.org/wiki/U.S._Camel_Corps\" class=\"mw-redirect\" title=\"U.S. Camel Corps\">U.S. Camel Corps</a>.", "no_year_html": "Major <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> departs New York aboard the <a href=\"https://wikipedia.org/wiki/USS_Supply_(1846)\" title=\"USS Supply (1846)\">USS <i>Supply</i></a> to procure <a href=\"https://wikipedia.org/wiki/Camel\" title=\"Camel\">camels</a> to establish the <a href=\"https://wikipedia.org/wiki/U.S._Camel_Corps\" class=\"mw-redirect\" title=\"U.S. Camel Corps\">U.S. Camel Corps</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "USS Supply (1846)", "link": "https://wikipedia.org/wiki/USS_Supply_(1846)"}, {"title": "Camel", "link": "https://wikipedia.org/wiki/Camel"}, {"title": "U.S. Camel Corps", "link": "https://wikipedia.org/wiki/U.S._Camel_Corps"}]}, {"year": "1859", "text": "Italian Independence wars: In the Battle of Magenta, the French army, under <PERSON><PERSON><PERSON>, defeat the Austrian army.", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Second_Italian_War_of_Independence\" title=\"Second Italian War of Independence\">Italian Independence wars</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Magenta\" title=\"Battle of Magenta\">Battle of Magenta</a>, the French army, under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a>, defeat the Austrian army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Italian_War_of_Independence\" title=\"Second Italian War of Independence\">Italian Independence wars</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Magenta\" title=\"Battle of Magenta\">Battle of Magenta</a>, the French army, under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a>, defeat the Austrian army.", "links": [{"title": "Second Italian War of Independence", "link": "https://wikipedia.org/wiki/Second_Italian_War_of_Independence"}, {"title": "Battle of Magenta", "link": "https://wikipedia.org/wiki/Battle_of_Magenta"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1862", "text": "American Civil War: Confederate troops evacuate Fort Pillow on the Mississippi River, leaving the way clear for Union troops to take Memphis, Tennessee.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops evacuate <a href=\"https://wikipedia.org/wiki/Fort_Pillow,_Tennessee\" class=\"mw-redirect\" title=\"Fort Pillow, Tennessee\">Fort Pillow</a> on the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>, leaving the way clear for <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> troops to take <a href=\"https://wikipedia.org/wiki/Memphis,_Tennessee\" title=\"Memphis, Tennessee\">Memphis, Tennessee</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops evacuate <a href=\"https://wikipedia.org/wiki/Fort_Pillow,_Tennessee\" class=\"mw-redirect\" title=\"Fort Pillow, Tennessee\">Fort Pillow</a> on the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>, leaving the way clear for <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> troops to take <a href=\"https://wikipedia.org/wiki/Memphis,_Tennessee\" title=\"Memphis, Tennessee\">Memphis, Tennessee</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Fort Pillow, Tennessee", "link": "https://wikipedia.org/wiki/Fort_Pillow,_Tennessee"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Memphis, Tennessee", "link": "https://wikipedia.org/wiki/Memphis,_Tennessee"}]}, {"year": "1876", "text": "An express train called the Transcontinental Express arrives in San Francisco via the first transcontinental railroad only 83 hours and 39 minutes after leaving New York City.", "html": "1876 - An <a href=\"https://wikipedia.org/wiki/Express_train\" title=\"Express train\">express train</a> called the <i><a href=\"https://wikipedia.org/wiki/Transcontinental_Express\" title=\"Transcontinental Express\">Transcontinental Express</a></i> arrives in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> via the <a href=\"https://wikipedia.org/wiki/First_transcontinental_railroad\" title=\"First transcontinental railroad\">first transcontinental railroad</a> only 83 hours and 39 minutes after leaving <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Express_train\" title=\"Express train\">express train</a> called the <i><a href=\"https://wikipedia.org/wiki/Transcontinental_Express\" title=\"Transcontinental Express\">Transcontinental Express</a></i> arrives in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> via the <a href=\"https://wikipedia.org/wiki/First_transcontinental_railroad\" title=\"First transcontinental railroad\">first transcontinental railroad</a> only 83 hours and 39 minutes after leaving <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "links": [{"title": "Express train", "link": "https://wikipedia.org/wiki/Express_train"}, {"title": "Transcontinental Express", "link": "https://wikipedia.org/wiki/Transcontinental_Express"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}, {"title": "First transcontinental railroad", "link": "https://wikipedia.org/wiki/First_transcontinental_railroad"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1878", "text": "Cyprus Convention: The Ottoman Empire cedes Cyprus to the United Kingdom but retains nominal title.", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Cyprus_Convention\" title=\"Cyprus Convention\">Cyprus Convention</a>: The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> cedes <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> to the United Kingdom but retains nominal title.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cyprus_Convention\" title=\"Cyprus Convention\">Cyprus Convention</a>: The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> cedes <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> to the United Kingdom but retains nominal title.", "links": [{"title": "Cyprus Convention", "link": "https://wikipedia.org/wiki/Cyprus_Convention"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}]}, {"year": "1896", "text": "<PERSON> completes the Ford Quadricycle, his first gasoline-powered automobile, and gives it a successful test run.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ford\" title=\"Henry Ford\"><PERSON></a> completes the <a href=\"https://wikipedia.org/wiki/Ford_Quadricycle\" title=\"Ford Quadricycle\">Ford Quadricycle</a>, his first gasoline-powered automobile, and gives it a successful test run.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ford\" title=\"Henry Ford\"><PERSON> Ford</a> completes the <a href=\"https://wikipedia.org/wiki/Ford_Quadricycle\" title=\"Ford Quadricycle\">Ford Quadricycle</a>, his first gasoline-powered automobile, and gives it a successful test run.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ford Quadricycle", "link": "https://wikipedia.org/wiki/Ford_Quadricycle"}]}, {"year": "1912", "text": "Massachusetts becomes the first state of the United States to set a minimum wage.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> becomes the first state of the United States to set a <a href=\"https://wikipedia.org/wiki/Minimum_wage_in_the_United_States\" title=\"Minimum wage in the United States\">minimum wage</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> becomes the first state of the United States to set a <a href=\"https://wikipedia.org/wiki/Minimum_wage_in_the_United_States\" title=\"Minimum wage in the United States\">minimum wage</a>.", "links": [{"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}, {"title": "Minimum wage in the United States", "link": "https://wikipedia.org/wiki/Minimum_wage_in_the_United_States"}]}, {"year": "1913", "text": "<PERSON>, a suffragette, runs out in front of <PERSON>'s horse at The Derby. She is trampled, never regains consciousness, and dies four days later.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Suffragette\" title=\"Suffragette\">suffragette</a>, runs out in front of <PERSON> <a href=\"https://wikipedia.org/wiki/George_V\" title=\"George V\"><PERSON></a>'s horse at <a href=\"https://wikipedia.org/wiki/Epsom_Derby\" title=\"Epsom Derby\">The Derby</a>. She is trampled, never regains consciousness, and dies four days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Suffragette\" title=\"Suffragette\">suffragette</a>, runs out in front of <PERSON> <a href=\"https://wikipedia.org/wiki/George_V\" title=\"George V\"><PERSON></a>'s horse at <a href=\"https://wikipedia.org/wiki/Epsom_Derby\" title=\"Epsom Derby\">The Derby</a>. She is trampled, never regains consciousness, and dies four days later.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Suffragette", "link": "https://wikipedia.org/wiki/Suffragette"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Epsom Derby", "link": "https://wikipedia.org/wiki/Epsom_Derby"}]}, {"year": "1916", "text": "World War I: Russia opens the Brusilov Offensive with an artillery barrage of Austro-Hungarian lines in Galicia.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a> opens the <a href=\"https://wikipedia.org/wiki/Brusilov_Offensive\" class=\"mw-redirect\" title=\"Brusilov Offensive\">Brusilov Offensive</a> with an <a href=\"https://wikipedia.org/wiki/Artillery\" title=\"Artillery\">artillery</a> barrage of <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austro-Hungarian</a> lines in <a href=\"https://wikipedia.org/wiki/Galicia_(Eastern_Europe)\" title=\"Galicia (Eastern Europe)\">Galicia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a> opens the <a href=\"https://wikipedia.org/wiki/Brusilov_Offensive\" class=\"mw-redirect\" title=\"Brusilov Offensive\">Brusilov Offensive</a> with an <a href=\"https://wikipedia.org/wiki/Artillery\" title=\"Artillery\">artillery</a> barrage of <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austro-Hungarian</a> lines in <a href=\"https://wikipedia.org/wiki/Galicia_(Eastern_Europe)\" title=\"Galicia (Eastern Europe)\">Galicia</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Brusilov Offensive", "link": "https://wikipedia.org/wiki/Brusilov_Offensive"}, {"title": "Artillery", "link": "https://wikipedia.org/wiki/Artillery"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}, {"title": "Galicia (Eastern Europe)", "link": "https://wikipedia.org/wiki/Galicia_(Eastern_Europe)"}]}, {"year": "1917", "text": "The first Pulitzer Prizes are awarded: <PERSON>, <PERSON><PERSON>, and <PERSON> Hall receive the first Pulitzer for biography (for <PERSON>). <PERSON> receives the first Pulitzer for history for his work With Americans of Past and Present Days. <PERSON> receives the first Pulitzer for journalism for his work for the New York World.", "html": "1917 - The first <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize\" title=\"Pulitzer Prize\">Pulitzer Prizes</a> are awarded: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/Florence_<PERSON>_Hall\" title=\"Florence Howe Hall\">Florence Hall</a> receive the first Pulitzer for biography (for <i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a></i>). <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives the first Pulitzer for history for his work <i><a href=\"https://wikipedia.org/wiki/With_Americans_of_Past_and_Present_Days\" class=\"mw-redirect\" title=\"With Americans of Past and Present Days\">With Americans of Past and Present Days</a></i>. <a href=\"https://wikipedia.org/wiki/Herbert_B._Swope\" class=\"mw-redirect\" title=\"Herbert B. Swope\">Herbert B. Swope</a> receives the first Pulitzer for <a href=\"https://wikipedia.org/wiki/Journalism\" title=\"Journalism\">journalism</a> for his work for the <i><a href=\"https://wikipedia.org/wiki/New_York_World\" title=\"New York World\">New York World</a></i>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize\" title=\"Pulitzer Prize\">Pulitzer Prizes</a> are awarded: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/Florence_Howe_Hall\" title=\"Florence Howe Hall\">Florence Hall</a> receive the first Pulitzer for biography (for <i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a></i>). <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives the first Pulitzer for history for his work <i><a href=\"https://wikipedia.org/wiki/With_Americans_of_Past_and_Present_Days\" class=\"mw-redirect\" title=\"With Americans of Past and Present Days\">With Americans of Past and Present Days</a></i>. <a href=\"https://wikipedia.org/wiki/Herbert_B._Swope\" class=\"mw-redirect\" title=\"Herbert B. Swope\">Herbert B. Swope</a> receives the first Pulitzer for <a href=\"https://wikipedia.org/wiki/Journalism\" title=\"Journalism\">journalism</a> for his work for the <i><a href=\"https://wikipedia.org/wiki/New_York_World\" title=\"New York World\">New York World</a></i>.", "links": [{"title": "Pulitzer Prize", "link": "https://wikipedia.org/wiki/Pulitzer_Prize"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Howe_Hall"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "With Americans of Past and Present Days", "link": "https://wikipedia.org/wiki/With_Americans_of_Past_and_Present_Days"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Journalism", "link": "https://wikipedia.org/wiki/Journalism"}, {"title": "New York World", "link": "https://wikipedia.org/wiki/New_York_World"}]}, {"year": "1919", "text": "Women's rights: The U.S. Congress approves the Nineteenth Amendment to the United States Constitution, which guarantees suffrage to women, and sends it to the U.S. states for ratification.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Women%27s_rights\" title=\"Women's rights\">Women's rights</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> approves the <a href=\"https://wikipedia.org/wiki/Nineteenth_Amendment_to_the_United_States_Constitution\" title=\"Nineteenth Amendment to the United States Constitution\">Nineteenth Amendment to the United States Constitution</a>, which guarantees <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States\" title=\"Women's suffrage in the United States\">suffrage to women</a>, and sends it to the <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. states</a> for ratification.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Women%27s_rights\" title=\"Women's rights\">Women's rights</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> approves the <a href=\"https://wikipedia.org/wiki/Nineteenth_Amendment_to_the_United_States_Constitution\" title=\"Nineteenth Amendment to the United States Constitution\">Nineteenth Amendment to the United States Constitution</a>, which guarantees <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States\" title=\"Women's suffrage in the United States\">suffrage to women</a>, and sends it to the <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. states</a> for ratification.", "links": [{"title": "Women's rights", "link": "https://wikipedia.org/wiki/Women%27s_rights"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Nineteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Nineteenth_Amendment_to_the_United_States_Constitution"}, {"title": "Women's suffrage in the United States", "link": "https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1919", "text": "<PERSON> bans the Planned Fourth Regional Congress of Peasants, Workers and Insurgents.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> bans the <a href=\"https://wikipedia.org/wiki/Regional_Congress_of_Peasants,_Workers_and_Insurgents#Planned_Fourth_Congress_(June_1919)\" title=\"Regional Congress of Peasants, Workers and Insurgents\">Planned Fourth Regional Congress of Peasants, Workers and Insurgents</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> bans the <a href=\"https://wikipedia.org/wiki/Regional_Congress_of_Peasants,_Workers_and_Insurgents#Planned_Fourth_Congress_(June_1919)\" title=\"Regional Congress of Peasants, Workers and Insurgents\">Planned Fourth Regional Congress of Peasants, Workers and Insurgents</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Regional Congress of Peasants, Workers and Insurgents", "link": "https://wikipedia.org/wiki/Regional_Congress_of_Peasants,_Workers_and_Insurgents#Planned_Fourth_Congress_(June_1919)"}]}, {"year": "1920", "text": "Hungary loses 71% of its territory and 63% of its population when the Treaty of Trianon is signed in Paris.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a> loses 71% of its territory and 63% of its population when the <a href=\"https://wikipedia.org/wiki/Treaty_of_Trianon\" title=\"Treaty of Trianon\">Treaty of Trianon</a> is signed in Paris.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a> loses 71% of its territory and 63% of its population when the <a href=\"https://wikipedia.org/wiki/Treaty_of_Trianon\" title=\"Treaty of Trianon\">Treaty of Trianon</a> is signed in Paris.", "links": [{"title": "Hungary", "link": "https://wikipedia.org/wiki/Hungary"}, {"title": "Treaty of Trianon", "link": "https://wikipedia.org/wiki/Treaty_of_Trianon"}]}, {"year": "1928", "text": "The President of the Republic of China, <PERSON>, is assassinated by Japanese agents.", "html": "1928 - The <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is assassinated by Japanese agents.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is assassinated by Japanese agents.", "links": [{"title": "President of the Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_China"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON> and other Chilean military officers lead a coup d'état establishing the short-lived Socialist Republic of Chile.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Marmaduke_Grove\" title=\"Marmaduke Grove\">Marmaduke Grove</a> and other Chilean military officers lead a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> establishing the short-lived <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Chile\" title=\"Socialist Republic of Chile\">Socialist Republic of Chile</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marmaduke_Grove\" title=\"Marmaduke Grove\">Marmaduke Grove</a> and other Chilean military officers lead a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> establishing the short-lived <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Chile\" title=\"Socialist Republic of Chile\">Socialist Republic of Chile</a>.", "links": [{"title": "Marmaduke Grove", "link": "https://wikipedia.org/wiki/Marmaduke_Grove"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "Socialist Republic of Chile", "link": "https://wikipedia.org/wiki/Socialist_Republic_of_Chile"}]}, {"year": "1939", "text": "The Holocaust: The MS St. Louis, a ship carrying 963 German Jewish refugees, is denied permission to land in Florida, in the United States, after already being turned away from Cuba. Forced to return to Europe, more than 200 of its passengers later die in Nazi concentration camps.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: The <a href=\"https://wikipedia.org/wiki/MS_St._Louis\" title=\"MS St. Louis\">MS <i>St. Louis</i></a>, a ship carrying 963 <a href=\"https://wikipedia.org/wiki/Emigration_of_Jews_from_Nazi_Germany_and_German-occupied_Europe\" title=\"Emigration of Jews from Nazi Germany and German-occupied Europe\">German Jewish refugees</a>, is denied permission to land in <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, in the United States, after already being turned away from <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>. Forced to return to Europe, more than 200 of its passengers later die in <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> <a href=\"https://wikipedia.org/wiki/Concentration_camp\" title=\"Concentration camp\">concentration camps</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: The <a href=\"https://wikipedia.org/wiki/MS_St._Louis\" title=\"MS St. Louis\">MS <i>St. Louis</i></a>, a ship carrying 963 <a href=\"https://wikipedia.org/wiki/Emigration_of_Jews_from_Nazi_Germany_and_German-occupied_Europe\" title=\"Emigration of Jews from Nazi Germany and German-occupied Europe\">German Jewish refugees</a>, is denied permission to land in <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, in the United States, after already being turned away from <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>. Forced to return to Europe, more than 200 of its passengers later die in <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> <a href=\"https://wikipedia.org/wiki/Concentration_camp\" title=\"Concentration camp\">concentration camps</a>.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "MS St. Louis", "link": "https://wikipedia.org/wiki/MS_St._Louis"}, {"title": "Emigration of Jews from Nazi Germany and German-occupied Europe", "link": "https://wikipedia.org/wiki/Emigration_of_Jews_from_Nazi_Germany_and_German-occupied_Europe"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}, {"title": "Concentration camp", "link": "https://wikipedia.org/wiki/Concentration_camp"}]}, {"year": "1940", "text": "World War II: The Dunkirk evacuation ends: the British Armed Forces completes evacuation of 338,000 troops from Dunkirk in France. To rally the morale of the country, <PERSON> delivers, only to the House of Commons, his famous \"We shall fight on the beaches\" speech.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Dunkirk_evacuation\" title=\"Dunkirk evacuation\">Dunkirk evacuation</a> ends: the <a href=\"https://wikipedia.org/wiki/British_Armed_Forces\" title=\"British Armed Forces\">British Armed Forces</a> completes evacuation of 338,000 troops from <a href=\"https://wikipedia.org/wiki/Dunkirk\" title=\"Dunkirk\">Dunkirk</a> in France. To rally the morale of the country, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers, only to the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">House of Commons</a>, his famous \"<a href=\"https://wikipedia.org/wiki/We_shall_fight_on_the_beaches\" title=\"We shall fight on the beaches\">We shall fight on the beaches</a>\" speech.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Dunkirk_evacuation\" title=\"Dunkirk evacuation\">Dunkirk evacuation</a> ends: the <a href=\"https://wikipedia.org/wiki/British_Armed_Forces\" title=\"British Armed Forces\">British Armed Forces</a> completes evacuation of 338,000 troops from <a href=\"https://wikipedia.org/wiki/Dunkirk\" title=\"Dunkirk\">Dunkirk</a> in France. To rally the morale of the country, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers, only to the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">House of Commons</a>, his famous \"<a href=\"https://wikipedia.org/wiki/We_shall_fight_on_the_beaches\" title=\"We shall fight on the beaches\">We shall fight on the beaches</a>\" speech.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Dunkirk evacuation", "link": "https://wikipedia.org/wiki/Dunkirk_evacuation"}, {"title": "British Armed Forces", "link": "https://wikipedia.org/wiki/British_Armed_Forces"}, {"title": "Dunkirk", "link": "https://wikipedia.org/wiki/Dunkirk"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "House of Commons of the United Kingdom", "link": "https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom"}, {"title": "We shall fight on the beaches", "link": "https://wikipedia.org/wiki/We_shall_fight_on_the_beaches"}]}, {"year": "1942", "text": "World War II: The Battle of Midway begins. The Japanese Admiral <PERSON><PERSON><PERSON> orders a strike on Midway Island by much of the Imperial Japanese Navy.", "html": "1942 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Midway\" title=\"Battle of Midway\">Battle of Midway</a> begins. The Japanese <a href=\"https://wikipedia.org/wiki/Admiral\" title=\"Admiral\">Admiral</a> <a href=\"https://wikipedia.org/wiki/Ch%C5%ABichi_Nagumo\" title=\"Chūichi Nagumo\"><PERSON><PERSON><PERSON>gum<PERSON></a> orders a strike on <a href=\"https://wikipedia.org/wiki/Midway_Island\" class=\"mw-redirect\" title=\"Midway Island\">Midway Island</a> by much of the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Midway\" title=\"Battle of Midway\">Battle of Midway</a> begins. The Japanese <a href=\"https://wikipedia.org/wiki/Admiral\" title=\"Admiral\">Admiral</a> <a href=\"https://wikipedia.org/wiki/Ch%C5%ABichi_Nagumo\" title=\"Chūichi Nagumo\"><PERSON><PERSON><PERSON></a> orders a strike on <a href=\"https://wikipedia.org/wiki/Midway_Island\" class=\"mw-redirect\" title=\"Midway Island\">Midway Island</a> by much of the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a>.", "links": [{"title": "Battle of Midway", "link": "https://wikipedia.org/wiki/Battle_of_Midway"}, {"title": "Admiral", "link": "https://wikipedia.org/wiki/Admiral"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ch%C5%ABichi_Nagumo"}, {"title": "Midway Island", "link": "https://wikipedia.org/wiki/Midway_Island"}, {"title": "Imperial Japanese Navy", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Navy"}]}, {"year": "1942", "text": "World War II: <PERSON><PERSON><PERSON>, the Commander-in-Chief of the Finnish Army, is granted the title of Marshal of Finland by the government on his 75th birthday. On the same day, <PERSON> arrives in Finland for a surprise visit to meet <PERSON><PERSON><PERSON>.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, the Commander-in-Chief of the <a href=\"https://wikipedia.org/wiki/Finnish_Army\" title=\"Finnish Army\">Finnish Army</a>, is granted the title of <a href=\"https://wikipedia.org/wiki/Field_marshal_(Finland)\" title=\"Field marshal (Finland)\">Marshal of Finland</a> by the government on his 75th birthday. On the same day, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in Finland <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_recording\" title=\"<PERSON> and <PERSON> recording\">for a surprise visit to meet <PERSON><PERSON><PERSON></a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, the Commander-in-Chief of the <a href=\"https://wikipedia.org/wiki/Finnish_Army\" title=\"Finnish Army\">Finnish Army</a>, is granted the title of <a href=\"https://wikipedia.org/wiki/Field_marshal_(Finland)\" title=\"Field marshal (Finland)\">Marshal of Finland</a> by the government on his 75th birthday. On the same day, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in Finland <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_recording\" title=\"<PERSON> and <PERSON> recording\">for a surprise visit to meet <PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Finnish Army", "link": "https://wikipedia.org/wiki/Finnish_Army"}, {"title": "Field marshal (Finland)", "link": "https://wikipedia.org/wiki/Field_marshal_(Finland)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hitler and <PERSON><PERSON><PERSON> recording", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_recording"}]}, {"year": "1943", "text": "A military coup in Argentina ousts <PERSON>.", "html": "1943 - A <a href=\"https://wikipedia.org/wiki/1943_Argentine_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1943 Argentine coup d'état\">military coup</a> in <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a> ousts <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1943_Argentine_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1943 Argentine coup d'état\">military coup</a> in <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a> ousts <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "1943 Argentine coup d'état", "link": "https://wikipedia.org/wiki/1943_Argentine_coup_d%27%C3%A9tat"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Castillo"}]}, {"year": "1944", "text": "World War II: A hunter-killer group of the United States Navy captures the German Kriegsmarine submarine U-505: The first time a U.S. Navy vessel had captured an enemy vessel at sea since the 19th century.", "html": "1944 - World War II: A hunter-killer group of the <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> captures the German <a href=\"https://wikipedia.org/wiki/Kriegsmarine\" title=\"Kriegsmarine\">Kriegsmarine</a> submarine <a href=\"https://wikipedia.org/wiki/German_submarine_U-505\" title=\"German submarine U-505\"><i>U-505</i></a>: The first time a U.S. Navy vessel had captured an enemy vessel at sea since the 19th century.", "no_year_html": "World War II: A hunter-killer group of the <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> captures the German <a href=\"https://wikipedia.org/wiki/Kriegsmarine\" title=\"Kriegsmarine\">Kriegsmarine</a> submarine <a href=\"https://wikipedia.org/wiki/German_submarine_U-505\" title=\"German submarine U-505\"><i>U-505</i></a>: The first time a U.S. Navy vessel had captured an enemy vessel at sea since the 19th century.", "links": [{"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "Kriegsmarine", "link": "https://wikipedia.org/wiki/Kriegsmarine"}, {"title": "German submarine U-505", "link": "https://wikipedia.org/wiki/German_submarine_U-505"}]}, {"year": "1944", "text": "World War II: The United States Fifth Army captures Rome, although much of the German Fourteenth Army is able to withdraw to the north.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/United_States_Army_North\" title=\"United States Army North\">United States Fifth Army</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Anzio#Breakout\" title=\"Battle of Anzio\">captures Rome</a>, although much of the <a href=\"https://wikipedia.org/wiki/14th_Army_(Wehrmacht)\" title=\"14th Army (Wehrmacht)\">German Fourteenth Army</a> is able to withdraw to the north.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/United_States_Army_North\" title=\"United States Army North\">United States Fifth Army</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Anzio#Breakout\" title=\"Battle of Anzio\">captures Rome</a>, although much of the <a href=\"https://wikipedia.org/wiki/14th_Army_(Wehrmacht)\" title=\"14th Army (Wehrmacht)\">German Fourteenth Army</a> is able to withdraw to the north.", "links": [{"title": "United States Army North", "link": "https://wikipedia.org/wiki/United_States_Army_North"}, {"title": "Battle of Anzio", "link": "https://wikipedia.org/wiki/Battle_of_Anzio#Breakout"}, {"title": "14th Army (Wehrmacht)", "link": "https://wikipedia.org/wiki/14th_Army_(Wehrmacht)"}]}, {"year": "1961", "text": "Cold War: In the Vienna summit, the Soviet premier <PERSON><PERSON> sparks the Berlin Crisis by threatening to sign a separate peace treaty with East Germany and ending American, British and French access to East Berlin.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: In the <a href=\"https://wikipedia.org/wiki/Vienna_summit\" title=\"Vienna summit\">Vienna summit</a>, the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> premier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> sparks the <a href=\"https://wikipedia.org/wiki/Berlin_Crisis_of_1961\" title=\"Berlin Crisis of 1961\">Berlin Crisis</a> by threatening to sign a separate peace treaty with <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> and ending American, British and French access to <a href=\"https://wikipedia.org/wiki/East_Berlin\" title=\"East Berlin\">East Berlin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: In the <a href=\"https://wikipedia.org/wiki/Vienna_summit\" title=\"Vienna summit\">Vienna summit</a>, the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> premier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> sparks the <a href=\"https://wikipedia.org/wiki/Berlin_Crisis_of_1961\" title=\"Berlin Crisis of 1961\">Berlin Crisis</a> by threatening to sign a separate peace treaty with <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> and ending American, British and French access to <a href=\"https://wikipedia.org/wiki/East_Berlin\" title=\"East Berlin\">East Berlin</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Vienna summit", "link": "https://wikipedia.org/wiki/Vienna_summit"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Berlin Crisis of 1961", "link": "https://wikipedia.org/wiki/Berlin_Crisis_of_1961"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "East Berlin", "link": "https://wikipedia.org/wiki/East_Berlin"}]}, {"year": "1967", "text": "Seventy-two people are killed when a Canadair C-4 Argonaut crashes at Stockport in England.", "html": "1967 - Seventy-two people are killed when a <a href=\"https://wikipedia.org/wiki/Canadair_C-4_Argonaut\" class=\"mw-redirect\" title=\"Canadair C-4 Argonaut\">Canadair C-4 Argonaut</a> <a href=\"https://wikipedia.org/wiki/Stockport_air_disaster\" title=\"Stockport air disaster\">crashes at Stockport</a> in England.", "no_year_html": "Seventy-two people are killed when a <a href=\"https://wikipedia.org/wiki/Canadair_C-4_Argonaut\" class=\"mw-redirect\" title=\"Canadair C-4 Argonaut\">Canadair C-4 Argonaut</a> <a href=\"https://wikipedia.org/wiki/Stockport_air_disaster\" title=\"Stockport air disaster\">crashes at Stockport</a> in England.", "links": [{"title": "Canadair C-4 Argonaut", "link": "https://wikipedia.org/wiki/Canadair_C-4_Argonaut"}, {"title": "Stockport air disaster", "link": "https://wikipedia.org/wiki/Stockport_air_disaster"}]}, {"year": "1970", "text": "Tonga gains independence from the British Empire.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Tonga\" title=\"Tonga\">Tonga</a> gains independence from the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tonga\" title=\"Tonga\">Tonga</a> gains independence from the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "links": [{"title": "Tonga", "link": "https://wikipedia.org/wiki/Tonga"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}]}, {"year": "1975", "text": "The Governor of California <PERSON> signs the California Agricultural Labor Relations Act into law, the first law in the United States giving farmworkers collective bargaining rights.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/California_Agricultural_Labor_Relations_Act\" class=\"mw-redirect\" title=\"California Agricultural Labor Relations Act\">California Agricultural Labor Relations Act</a> into law, the first law in the United States giving farmworkers <a href=\"https://wikipedia.org/wiki/Collective_bargaining\" title=\"Collective bargaining\">collective bargaining</a> rights.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/California_Agricultural_Labor_Relations_Act\" class=\"mw-redirect\" title=\"California Agricultural Labor Relations Act\">California Agricultural Labor Relations Act</a> into law, the first law in the United States giving farmworkers <a href=\"https://wikipedia.org/wiki/Collective_bargaining\" title=\"Collective bargaining\">collective bargaining</a> rights.", "links": [{"title": "Governor of California", "link": "https://wikipedia.org/wiki/Governor_of_California"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "California Agricultural Labor Relations Act", "link": "https://wikipedia.org/wiki/California_Agricultural_Labor_Relations_Act"}, {"title": "Collective bargaining", "link": "https://wikipedia.org/wiki/Collective_bargaining"}]}, {"year": "1977", "text": "JVC introduces its VHS videotape at the Consumer Electronics Show in Chicago. It will eventually prevail against Sony's rival Betamax system in a format war to become the predominant home video medium.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/JVC\" title=\"JVC\">JVC</a> introduces its <a href=\"https://wikipedia.org/wiki/VHS\" title=\"VHS\">VHS</a> videotape at the <a href=\"https://wikipedia.org/wiki/Consumer_Electronics_Show\" class=\"mw-redirect\" title=\"Consumer Electronics Show\">Consumer Electronics Show</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>. It will eventually prevail against <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a>'s rival <a href=\"https://wikipedia.org/wiki/Betamax\" title=\"Betamax\">Betamax</a> system in a <a href=\"https://wikipedia.org/wiki/Videotape_format_war\" title=\"Videotape format war\">format war</a> to become the predominant <a href=\"https://wikipedia.org/wiki/Home_video\" title=\"Home video\">home video</a> medium.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/JVC\" title=\"JVC\">JVC</a> introduces its <a href=\"https://wikipedia.org/wiki/VHS\" title=\"VHS\">VHS</a> videotape at the <a href=\"https://wikipedia.org/wiki/Consumer_Electronics_Show\" class=\"mw-redirect\" title=\"Consumer Electronics Show\">Consumer Electronics Show</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>. It will eventually prevail against <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a>'s rival <a href=\"https://wikipedia.org/wiki/Betamax\" title=\"Betamax\">Betamax</a> system in a <a href=\"https://wikipedia.org/wiki/Videotape_format_war\" title=\"Videotape format war\">format war</a> to become the predominant <a href=\"https://wikipedia.org/wiki/Home_video\" title=\"Home video\">home video</a> medium.", "links": [{"title": "JVC", "link": "https://wikipedia.org/wiki/JVC"}, {"title": "VHS", "link": "https://wikipedia.org/wiki/VHS"}, {"title": "Consumer Electronics Show", "link": "https://wikipedia.org/wiki/Consumer_Electronics_Show"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}, {"title": "Sony", "link": "https://wikipedia.org/wiki/Sony"}, {"title": "Betamax", "link": "https://wikipedia.org/wiki/Betamax"}, {"title": "Videotape format war", "link": "https://wikipedia.org/wiki/Videotape_format_war"}, {"title": "Home video", "link": "https://wikipedia.org/wiki/Home_video"}]}, {"year": "1979", "text": "Flight Lieutenant <PERSON> takes power in Ghana after a military coup in which General <PERSON> is overthrown.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Flight_lieutenant\" title=\"Flight lieutenant\">Flight Lieutenant</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes power in <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a> after a <a href=\"https://wikipedia.org/wiki/1979_Ghanaian_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1979 Ghanaian coup d'état\">military coup</a> in which General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is overthrown.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flight_lieutenant\" title=\"Flight lieutenant\">Flight Lieutenant</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes power in <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a> after a <a href=\"https://wikipedia.org/wiki/1979_Ghanaian_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1979 Ghanaian coup d'état\">military coup</a> in which General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is overthrown.", "links": [{"title": "Flight lieutenant", "link": "https://wikipedia.org/wiki/Flight_lieutenant"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ghana", "link": "https://wikipedia.org/wiki/Ghana"}, {"title": "1979 Ghanaian coup d'état", "link": "https://wikipedia.org/wiki/1979_Ghanaian_coup_d%27%C3%A9tat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, who killed two US Marshals in Medina, North Dakota on February 13, is killed in a shootout in Smithville, Arkansas, along with a local sheriff, after a four-month manhunt.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who killed two <a href=\"https://wikipedia.org/wiki/United_States_Marshals_Service\" title=\"United States Marshals Service\">US Marshals</a> in <a href=\"https://wikipedia.org/wiki/Medina,_North_Dakota\" title=\"Medina, North Dakota\">Medina, North Dakota</a> on February 13, is killed in a shootout in <a href=\"https://wikipedia.org/wiki/Smithville,_Arkansas\" title=\"Smithville, Arkansas\">Smithville, Arkansas</a>, along with a local sheriff, after a four-month manhunt.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who killed two <a href=\"https://wikipedia.org/wiki/United_States_Marshals_Service\" title=\"United States Marshals Service\">US Marshals</a> in <a href=\"https://wikipedia.org/wiki/Medina,_North_Dakota\" title=\"Medina, North Dakota\">Medina, North Dakota</a> on February 13, is killed in a shootout in <a href=\"https://wikipedia.org/wiki/Smithville,_Arkansas\" title=\"Smithville, Arkansas\">Smithville, Arkansas</a>, along with a local sheriff, after a four-month manhunt.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Marshals Service", "link": "https://wikipedia.org/wiki/United_States_Marshals_Service"}, {"title": "Medina, North Dakota", "link": "https://wikipedia.org/wiki/Medina,_North_Dakota"}, {"title": "Smithville, Arkansas", "link": "https://wikipedia.org/wiki/Smithville,_Arkansas"}]}, {"year": "1986", "text": "<PERSON> pleads guilty to espionage for selling top secret United States military intelligence to Israel.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> pleads guilty to <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a> for selling top secret United States <a href=\"https://wikipedia.org/wiki/Military_intelligence\" title=\"Military intelligence\">military intelligence</a> to <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> pleads guilty to <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a> for selling top secret United States <a href=\"https://wikipedia.org/wiki/Military_intelligence\" title=\"Military intelligence\">military intelligence</a> to <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Espionage", "link": "https://wikipedia.org/wiki/Espionage"}, {"title": "Military intelligence", "link": "https://wikipedia.org/wiki/Military_intelligence"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}]}, {"year": "1988", "text": "Three cars on a train carrying hexogen to Kazakhstan explode in Arzamas, Gorky Oblast, USSR, killing 91 and injuring about 1,500.", "html": "1988 - Three cars on a train carrying <a href=\"https://wikipedia.org/wiki/RDX\" title=\"RDX\">hexogen</a> to <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a> <a href=\"https://wikipedia.org/wiki/Arzamas_train_disaster\" title=\"Arzamas train disaster\">explode</a> in <a href=\"https://wikipedia.org/wiki/Arzamas\" title=\"Arzamas\">Arzamas</a>, <a href=\"https://wikipedia.org/wiki/Gorky_Oblast\" class=\"mw-redirect\" title=\"Gorky Oblast\">Gorky Oblast</a>, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">USSR</a>, killing 91 and injuring about 1,500.", "no_year_html": "Three cars on a train carrying <a href=\"https://wikipedia.org/wiki/RDX\" title=\"RDX\">hexogen</a> to <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a> <a href=\"https://wikipedia.org/wiki/Arzamas_train_disaster\" title=\"Arzamas train disaster\">explode</a> in <a href=\"https://wikipedia.org/wiki/Arzamas\" title=\"Arzamas\">Arzamas</a>, <a href=\"https://wikipedia.org/wiki/Gorky_Oblast\" class=\"mw-redirect\" title=\"Gorky Oblast\">Gorky Oblast</a>, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">USSR</a>, killing 91 and injuring about 1,500.", "links": [{"title": "RDX", "link": "https://wikipedia.org/wiki/RDX"}, {"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}, {"title": "Arzamas train disaster", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_train_disaster"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rz<PERSON>s"}, {"title": "Gorky Oblast", "link": "https://wikipedia.org/wiki/Gorky_Oblast"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1989", "text": "In the 1989 Iranian Supreme Leader election, <PERSON> is elected as the new Supreme Leader of Iran after the death and funeral of <PERSON><PERSON><PERSON><PERSON>.", "html": "1989 - In the <a href=\"https://wikipedia.org/wiki/1989_Iranian_Supreme_Leader_election\" title=\"1989 Iranian Supreme Leader election\">1989 Iranian Supreme Leader election</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as the new <a href=\"https://wikipedia.org/wiki/Supreme_Leader_of_Iran\" title=\"Supreme Leader of Iran\">Supreme Leader of Iran</a> after the <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_Ruhollah_Khomeini\" title=\"Death and state funeral of <PERSON><PERSON><PERSON><PERSON>homein<PERSON>\">death and funeral of <PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/1989_Iranian_Supreme_Leader_election\" title=\"1989 Iranian Supreme Leader election\">1989 Iranian Supreme Leader election</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as the new <a href=\"https://wikipedia.org/wiki/Supreme_Leader_of_Iran\" title=\"Supreme Leader of Iran\">Supreme Leader of Iran</a> after the <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_Ruhollah_Khomeini\" title=\"Death and state funeral of <PERSON><PERSON><PERSON><PERSON>homein<PERSON>\">death and funeral of <PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "1989 Iranian Supreme Leader election", "link": "https://wikipedia.org/wiki/1989_Iranian_Supreme_Leader_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Supreme Leader of Iran", "link": "https://wikipedia.org/wiki/Supreme_Leader_of_Iran"}, {"title": "Death and state funeral of <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON><PERSON>ollah_Khomeini"}]}, {"year": "1989", "text": "The Tiananmen Square protests and massacre are suppressed in Beijing by the People's Liberation Army, with between 241 and 10,000 dead (an unofficial estimate).", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/1989_Tiananmen_Square_protests_and_massacre\" title=\"1989 Tiananmen Square protests and massacre\">Tiananmen Square protests and massacre</a> are <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army_at_the_1989_Tiananmen_Square_protests_and_massacre\" title=\"People's Liberation Army at the 1989 Tiananmen Square protests and massacre\">suppressed in Beijing by the People's Liberation Army</a>, with between 241 and 10,000 dead (an unofficial estimate).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1989_Tiananmen_Square_protests_and_massacre\" title=\"1989 Tiananmen Square protests and massacre\">Tiananmen Square protests and massacre</a> are <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army_at_the_1989_Tiananmen_Square_protests_and_massacre\" title=\"People's Liberation Army at the 1989 Tiananmen Square protests and massacre\">suppressed in Beijing by the People's Liberation Army</a>, with between 241 and 10,000 dead (an unofficial estimate).", "links": [{"title": "1989 Tiananmen Square protests and massacre", "link": "https://wikipedia.org/wiki/1989_Tiananmen_Square_protests_and_massacre"}, {"title": "People's Liberation Army at the 1989 Tiananmen Square protests and massacre", "link": "https://wikipedia.org/wiki/People%27s_Liberation_Army_at_the_1989_Tiananmen_Square_protests_and_massacre"}]}, {"year": "1989", "text": "Solidarity's victory in the 1989 Polish legislative election occurs, the first election since the Communist Polish United Workers' Party abandoned its monopoly of power. It sparks off the Revolutions of 1989 in Eastern Europe.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a>'s victory in the <a href=\"https://wikipedia.org/wiki/1989_Polish_parliamentary_election\" title=\"1989 Polish parliamentary election\">1989 Polish legislative election</a> occurs, the first election since the Communist <a href=\"https://wikipedia.org/wiki/Polish_United_Workers%27_Party\" title=\"Polish United Workers' Party\">Polish United Workers' Party</a> abandoned its monopoly of power. It sparks off the <a href=\"https://wikipedia.org/wiki/Revolutions_of_1989\" title=\"Revolutions of 1989\">Revolutions of 1989</a> in Eastern Europe.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a>'s victory in the <a href=\"https://wikipedia.org/wiki/1989_Polish_parliamentary_election\" title=\"1989 Polish parliamentary election\">1989 Polish legislative election</a> occurs, the first election since the Communist <a href=\"https://wikipedia.org/wiki/Polish_United_Workers%27_Party\" title=\"Polish United Workers' Party\">Polish United Workers' Party</a> abandoned its monopoly of power. It sparks off the <a href=\"https://wikipedia.org/wiki/Revolutions_of_1989\" title=\"Revolutions of 1989\">Revolutions of 1989</a> in Eastern Europe.", "links": [{"title": "Solidarity (Polish trade union)", "link": "https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)"}, {"title": "1989 Polish parliamentary election", "link": "https://wikipedia.org/wiki/1989_Polish_parliamentary_election"}, {"title": "Polish United Workers' Party", "link": "https://wikipedia.org/wiki/Polish_United_Workers%27_Party"}, {"title": "Revolutions of 1989", "link": "https://wikipedia.org/wiki/Revolutions_of_1989"}]}, {"year": "1989", "text": "Ufa train disaster: A natural gas explosion near Ufa, Russia, kills 575 as two trains passing each other throw sparks near a leaky pipeline.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Ufa_train_disaster\" title=\"Ufa train disaster\">Ufa train disaster</a>: A <a href=\"https://wikipedia.org/wiki/Natural_gas\" title=\"Natural gas\">natural gas</a> explosion near <a href=\"https://wikipedia.org/wiki/Ufa\" title=\"Ufa\">Ufa</a>, Russia, kills 575 as two trains passing each other throw sparks near a leaky pipeline.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ufa_train_disaster\" title=\"Ufa train disaster\">Ufa train disaster</a>: A <a href=\"https://wikipedia.org/wiki/Natural_gas\" title=\"Natural gas\">natural gas</a> explosion near <a href=\"https://wikipedia.org/wiki/Ufa\" title=\"Ufa\">Ufa</a>, Russia, kills 575 as two trains passing each other throw sparks near a leaky pipeline.", "links": [{"title": "Ufa train disaster", "link": "https://wikipedia.org/wiki/Ufa_train_disaster"}, {"title": "Natural gas", "link": "https://wikipedia.org/wiki/Natural_gas"}, {"title": "Ufa", "link": "https://wikipedia.org/wiki/Ufa"}]}, {"year": "1996", "text": "The first flight of Ariane 5 explodes after roughly 37 seconds. It was a Cluster mission.", "html": "1996 - The first flight of <a href=\"https://wikipedia.org/wiki/Ariane_5\" title=\"Ariane 5\">Ariane 5</a> <a href=\"https://wikipedia.org/wiki/Ariane_flight_V88\" title=\"Ariane flight V88\">explodes</a> after roughly 37 seconds. It was a <a href=\"https://wikipedia.org/wiki/Cluster_II_(spacecraft)\" title=\"Cluster II (spacecraft)\">Cluster mission</a>.", "no_year_html": "The first flight of <a href=\"https://wikipedia.org/wiki/Ariane_5\" title=\"Ariane 5\">Ariane 5</a> <a href=\"https://wikipedia.org/wiki/Ariane_flight_V88\" title=\"Ariane flight V88\">explodes</a> after roughly 37 seconds. It was a <a href=\"https://wikipedia.org/wiki/Cluster_II_(spacecraft)\" title=\"Cluster II (spacecraft)\">Cluster mission</a>.", "links": [{"title": "Ariane 5", "link": "https://wikipedia.org/wiki/Ariane_5"}, {"title": "Ariane flight V88", "link": "https://wikipedia.org/wiki/Ariane_flight_V88"}, {"title": "Cluster II (spacecraft)", "link": "https://wikipedia.org/wiki/Cluster_II_(spacecraft)"}]}, {"year": "1998", "text": "<PERSON> is sentenced to life in prison for his role in the Oklahoma City bombing.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to life in prison for his role in the <a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to life in prison for his role in the <a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Oklahoma City bombing", "link": "https://wikipedia.org/wiki/Oklahoma_City_bombing"}]}, {"year": "2005", "text": "The Civic Forum of the Romanians of Covasna, Harghita and Mureș is founded.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Civic_Forum_of_the_Romanians_of_Covasna,_Harghita_and_Mure%C8%99\" title=\"Civic Forum of the Romanians of Covasna, Harghita and Mureș\">Civic Forum of the Romanians of Covasna, Harghita and Mureș</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Civic_Forum_of_the_Romanians_of_Covasna,_Harghita_and_Mure%C8%99\" title=\"Civic Forum of the Romanians of Covasna, Harghita and Mureș\">Civic Forum of the Romanians of Covasna, Harghita and Mureș</a> is founded.", "links": [{"title": "Civic Forum of the Romanians of Covasna, Harghita and Mureș", "link": "https://wikipedia.org/wiki/Civic_Forum_of_the_Romanians_of_Covasna,_<PERSON><PERSON><PERSON>_and_<PERSON><PERSON>%C8%99"}]}, {"year": "2010", "text": "Falcon 9 Flight 1 is the maiden flight of the SpaceX Falcon 9 rocket, which launches from Cape Canaveral Air Force Station Space Launch Complex 40.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Falcon_9_Flight_1\" class=\"mw-redirect\" title=\"Falcon 9 Flight 1\">Falcon 9 Flight 1</a> is the <a href=\"https://wikipedia.org/wiki/Maiden_flight\" title=\"Maiden flight\">maiden flight</a> of the <a href=\"https://wikipedia.org/wiki/SpaceX\" title=\"SpaceX\">SpaceX</a> <a href=\"https://wikipedia.org/wiki/Falcon_9\" title=\"Falcon 9\">Falcon 9</a> rocket, which launches from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Space_Launch_Complex_40\" title=\"Cape Canaveral Space Launch Complex 40\">Cape Canaveral Air Force Station Space Launch Complex 40</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Falcon_9_Flight_1\" class=\"mw-redirect\" title=\"Falcon 9 Flight 1\">Falcon 9 Flight 1</a> is the <a href=\"https://wikipedia.org/wiki/Maiden_flight\" title=\"Maiden flight\">maiden flight</a> of the <a href=\"https://wikipedia.org/wiki/SpaceX\" title=\"SpaceX\">SpaceX</a> <a href=\"https://wikipedia.org/wiki/Falcon_9\" title=\"Falcon 9\">Falcon 9</a> rocket, which launches from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Space_Launch_Complex_40\" title=\"Cape Canaveral Space Launch Complex 40\">Cape Canaveral Air Force Station Space Launch Complex 40</a>.", "links": [{"title": "Falcon 9 Flight 1", "link": "https://wikipedia.org/wiki/Falcon_9_Flight_1"}, {"title": "Maiden flight", "link": "https://wikipedia.org/wiki/Maiden_flight"}, {"title": "SpaceX", "link": "https://wikipedia.org/wiki/SpaceX"}, {"title": "Falcon 9", "link": "https://wikipedia.org/wiki/Falcon_9"}, {"title": "Cape Canaveral Space Launch Complex 40", "link": "https://wikipedia.org/wiki/Cape_Canaveral_Space_Launch_Complex_40"}]}, {"year": "2023", "text": "Protests begin in Poland against the <PERSON><PERSON> government.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/2023_Polish_protests\" title=\"2023 Polish protests\">Protests</a> begin in Poland against the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> government</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2023_Polish_protests\" title=\"2023 Polish protests\">Protests</a> begin in Poland against the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> government</a>.", "links": [{"title": "2023 Polish protests", "link": "https://wikipedia.org/wiki/2023_Polish_protests"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "Four people are killed when a Cessna Citation V crashes into Mine Bank Mountain in Augusta County, Virginia.", "html": "2023 - Four people are killed when a <a href=\"https://wikipedia.org/wiki/Cessna_Citation_V\" title=\"Cessna Citation V\">Cessna Citation V</a> <a href=\"https://wikipedia.org/wiki/2023_Virginia_plane_crash\" title=\"2023 Virginia plane crash\">crashes</a> into <a href=\"https://wikipedia.org/wiki/Mine_Bank_Mountain\" title=\"Mine Bank Mountain\">Mine Bank Mountain</a> in <a href=\"https://wikipedia.org/wiki/Augusta_County,_Virginia\" title=\"Augusta County, Virginia\">Augusta County, Virginia</a>.", "no_year_html": "Four people are killed when a <a href=\"https://wikipedia.org/wiki/Cessna_Citation_V\" title=\"Cessna Citation V\">Cessna Citation V</a> <a href=\"https://wikipedia.org/wiki/2023_Virginia_plane_crash\" title=\"2023 Virginia plane crash\">crashes</a> into <a href=\"https://wikipedia.org/wiki/Mine_Bank_Mountain\" title=\"Mine Bank Mountain\">Mine Bank Mountain</a> in <a href=\"https://wikipedia.org/wiki/Augusta_County,_Virginia\" title=\"Augusta County, Virginia\">Augusta County, Virginia</a>.", "links": [{"title": "Cessna Citation V", "link": "https://wikipedia.org/wiki/Cessna_Citation_V"}, {"title": "2023 Virginia plane crash", "link": "https://wikipedia.org/wiki/2023_Virginia_plane_crash"}, {"title": "Mine Bank Mountain", "link": "https://wikipedia.org/wiki/Mine_Bank_Mountain"}, {"title": "Augusta County, Virginia", "link": "https://wikipedia.org/wiki/Augusta_County,_Virginia"}]}], "Births": [{"year": "590", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> of Kannauj (d. 647)", "html": "590 - <a href=\"https://wikipedia.org/wiki/Harsha\" title=\"Harsha\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON><PERSON> of Kannauj (d. 647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harsha\" title=\"<PERSON>rs<PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON><PERSON> of Kannauj (d. 647)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Harsha"}]}, {"year": "1394", "text": "<PERSON><PERSON> of England, Queen of Denmark, Norway and Sweden (d. 1430)", "html": "1394 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_England\" title=\"<PERSON><PERSON> of England\"><PERSON><PERSON> of England</a>, Queen of Denmark, Norway and Sweden (d. 1430)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_England\" title=\"<PERSON><PERSON> of England\"><PERSON><PERSON> of England</a>, Queen of Denmark, Norway and Sweden (d. 1430)", "links": [{"title": "<PERSON><PERSON> of England", "link": "https://wikipedia.org/wiki/Philippa_of_England"}]}, {"year": "1489", "text": "<PERSON>, Duke of Lorraine (d. 1544)", "html": "1489 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (d. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (d. 1544)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine"}]}, {"year": "1563", "text": "<PERSON>, Scottish goldsmith (d. 1624)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish goldsmith (d. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish goldsmith (d. 1624)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1604", "text": "<PERSON>, Italian daughter of <PERSON> of Lorraine (d. 1648)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lorraine\"><PERSON> of <PERSON></a> (d. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of <PERSON>\"><PERSON> of Lorraine</a> (d. 1648)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>"}, {"title": "<PERSON> of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lorraine"}]}, {"year": "1665", "text": "<PERSON><PERSON><PERSON>, Canadian captain (d. 1733)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_La_Noue\" title=\"<PERSON><PERSON><PERSON> La Noue\"><PERSON><PERSON><PERSON> Noue</a>, Canadian captain (d. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_La_Noue\" title=\"<PERSON><PERSON><PERSON> La Noue\"><PERSON><PERSON><PERSON> Noue</a>, Canadian captain (d. 1733)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON>, French economist and physician (d. 1774)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Quesnay\" title=\"<PERSON>\"><PERSON></a>, French economist and physician (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Quesnay\" title=\"<PERSON>\"><PERSON></a>, French economist and physician (d. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Quesnay"}]}, {"year": "1704", "text": "<PERSON>, English inventor and businessman (d. 1776)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English inventor and businessman (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English inventor and businessman (d. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1738", "text": "<PERSON> of the United Kingdom (d. 1820)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> of the United Kingdom\"><PERSON> of the United Kingdom</a> (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> of the United Kingdom\"><PERSON> of the United Kingdom</a> (d. 1820)", "links": [{"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom"}]}, {"year": "1744", "text": "<PERSON>, Scottish soldier, designed the <PERSON> rifle (d. 1780)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier, designed the <a href=\"https://wikipedia.org/wiki/Ferguson_rifle\" title=\"Ferguson rifle\">Ferguson rifle</a> (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier, designed the <a href=\"https://wikipedia.org/wiki/Ferguson_rifle\" title=\"Ferguson rifle\">Ferguson rifle</a> (d. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ferguson rifle", "link": "https://wikipedia.org/wiki/Ferguson_rifle"}]}, {"year": "1754", "text": "<PERSON>, Argentinian soldier (d. 1833)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9naga\" title=\"<PERSON>\"><PERSON></a>, Argentinian soldier (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9naga\" title=\"<PERSON>\"><PERSON></a>, Argentinian soldier (d. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9naga"}]}, {"year": "1754", "text": "<PERSON>, Slovak astronomer and academic (d. 1832)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak astronomer and academic (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak astronomer and academic (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON><PERSON>, French geologist and academic (d. 1856)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/Constant_Pr%C3%A9vost\" title=\"<PERSON><PERSON>révost\"><PERSON><PERSON></a>, French geologist and academic (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constant_Pr%C3%A9vost\" title=\"<PERSON><PERSON> Prévost\"><PERSON><PERSON></a>, French geologist and academic (d. 1856)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constant_Pr%C3%A9vost"}]}, {"year": "1801", "text": "<PERSON>, English architect, designed Victoria Park (d. 1871)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/Victoria_Park,_Tower_Hamlets\" title=\"Victoria Park, Tower Hamlets\">Victoria Park</a> (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/Victoria_Park,_Tower_Hamlets\" title=\"Victoria Park, Tower Hamlets\">Victoria Park</a> (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Park, Tower Hamlets", "link": "https://wikipedia.org/wiki/Victoria_Park,_Tower_Hamlets"}]}, {"year": "1821", "text": "<PERSON><PERSON>, Russian poet and playwright (d. 1897)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and playwright (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and playwright (d. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 12th <PERSON><PERSON><PERSON><PERSON> (d. 1903)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/Jinmaku_Ky%C5%ABgor%C5%8D\" title=\"<PERSON><PERSON><PERSON>ūgor<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 12th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ma<PERSON>_Ky%C5%ABgor%C5%8D\" title=\"<PERSON><PERSON><PERSON> K<PERSON>ūgor<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 12th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1903)", "links": [{"title": "Jinmaku <PERSON>", "link": "https://wikipedia.org/wiki/Jinmaku_Ky%C5%ABgor%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1854", "text": "<PERSON><PERSON>, Dutch target shooter (d. 1916)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch target shooter (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch target shooter (d. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Canadian runner (d. 1924)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian runner (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian runner (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, Australian politician, 20th Premier of Tasmania (d. 1937)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1866", "text": "<PERSON><PERSON>, Finnish journalist and politician (d. 1952)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>llanp%C3%A4%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist and politician (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist and politician (d. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ina_Sillanp%C3%A4%C3%A4"}]}, {"year": "1867", "text": "<PERSON>, Finnish general and politician, 6th President of Finland (d. 1951)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish general and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish general and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, American author (d. 1942)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Nict<PERSON>_Dyalhis\" title=\"Nictzin Dyalhis\"><PERSON><PERSON><PERSON>yal<PERSON></a>, American author (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nict<PERSON>_Dyalhis\" title=\"Nictzin Dyalhis\"><PERSON><PERSON><PERSON></a>, American author (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nict<PERSON>_<PERSON>yal<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 1957)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1879", "text": "<PERSON>, English author and illustrator (d. 1964)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American actress (d. 1962)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Argentinian general and politician, 26th President of Argentina (d. 1952)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 26th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 26th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1889", "text": "<PERSON><PERSON>, German-American seismologist (d. 1960)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American seismologist (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American seismologist (d. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Russian conductor (d. 1988)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian conductor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian conductor (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Indian publisher, environmentalist, and philanthropist (d. 1992)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian publisher, environmentalist, and philanthropist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian publisher, environmentalist, and philanthropist (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Haitian journalist and politician (d. 1944)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian journalist and politician (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian journalist and politician (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American actress (d. 1976)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, English poet and journalist (d. 1990)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Patience_Strong\" title=\"Patience Strong\"><PERSON><PERSON></a>, English poet and journalist (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patience_Strong\" title=\"Patience Strong\"><PERSON><PERSON> <PERSON></a>, English poet and journalist (d. 1990)", "links": [{"title": "Patience <PERSON>", "link": "https://wikipedia.org/wiki/Patience_Strong"}]}, {"year": "1910", "text": "<PERSON>, English engineer, invented the hovercraft (d. 1999)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, invented the <a href=\"https://wikipedia.org/wiki/Hovercraft\" title=\"Hovercraft\">hovercraft</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, invented the <a href=\"https://wikipedia.org/wiki/Hovercraft\" title=\"Hovercraft\">hovercraft</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Hovercraft", "link": "https://wikipedia.org/wiki/Hovercraft"}]}, {"year": "1912", "text": "<PERSON>, Danish sculptor and painter (d. 1993)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish sculptor and painter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish sculptor and painter (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, New Zealand cricketer (d. 2006)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Malian educator and politician, 1st President of Mali (d. 1977)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Modibo_Ke%C3%AFta\" title=\"Modibo <PERSON>\"><PERSON><PERSON><PERSON></a>, Malian educator and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Mali\" class=\"mw-redirect\" title=\"President of Mali\">President of Mali</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Modibo_Ke%C3%AFta\" title=\"Modi<PERSON>\"><PERSON><PERSON><PERSON></a>, Malian educator and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Mali\" class=\"mw-redirect\" title=\"President of Mali\">President of Mali</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Modibo_Ke%C3%AFta"}, {"title": "President of Mali", "link": "https://wikipedia.org/wiki/President_of_Mali"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Swedish actor, singer, and director (d. 1965)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor, singer, and director (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor, singer, and director (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American biochemist and pharmacologist, Nobel Prize laureate (d. 2009)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Canadian painter (d. 2014)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian painter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian painter (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actor and singer (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Slovenian-Argentinian philosopher and academic (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Milan_Komar\" title=\"Milan Komar\"><PERSON></a>, Slovenian-Argentinian philosopher and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Komar\" title=\"Milan Komar\"><PERSON></a>, Slovenian-Argentinian philosopher and academic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milan_Komar"}]}, {"year": "1921", "text": "<PERSON>, American basketball player and coach (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English-Australian author and academic (d. 2007)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian author and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian author and academic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese karateka (d. 1994)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>yama\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese karateka (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Oyama\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese karateka (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Princess <PERSON><PERSON><PERSON>, Japanese princess (d. 2024)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Princess_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>, Princess <PERSON><PERSON><PERSON>\"><PERSON><PERSON>, Princess <PERSON><PERSON><PERSON></a>, Japanese princess (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Princess_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>, Princess <PERSON><PERSON><PERSON>\"><PERSON><PERSON>, Princess <PERSON><PERSON><PERSON></a>, Japanese princess (d. 2024)", "links": [{"title": "<PERSON><PERSON>, Princess <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Princess_<PERSON><PERSON><PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Samoan politician, 5th Prime Minister of Samoa (d. 1999)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Tofilau <PERSON>\"><PERSON><PERSON><PERSON></a>, Samoan politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Samoa\" title=\"Prime Minister of Samoa\">Prime Minister of Samoa</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Tofilau <PERSON>\"><PERSON><PERSON><PERSON></a>, Samoan politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Samoa\" title=\"Prime Minister of Samoa\">Prime Minister of Samoa</a> (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>a"}, {"title": "Prime Minister of Samoa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Samoa"}]}, {"year": "1924", "text": "<PERSON>, American actor and director (d. 2006)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Spanish footballer (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American who was the heaviest human being recorded in the history of the world during his lifetime (d. 1958)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/List_of_the_heaviest_people\" class=\"mw-redirect\" title=\"List of the heaviest people\">who was the heaviest human being recorded in the history of the world during his lifetime</a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/List_of_the_heaviest_people\" class=\"mw-redirect\" title=\"List of the heaviest people\">who was the heaviest human being recorded in the history of the world during his lifetime</a> (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of the heaviest people", "link": "https://wikipedia.org/wiki/List_of_the_heaviest_people"}]}, {"year": "1926", "text": "<PERSON>, Estonian poet, playwright, and critic (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Ain_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet, playwright, and critic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet, playwright, and critic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ain_<PERSON>p"}]}, {"year": "1926", "text": "<PERSON>, German-American actress and director, co-founded The Living Theatre (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American actress and director, co-founded <a href=\"https://wikipedia.org/wiki/The_Living_Theatre\" title=\"The Living Theatre\">The Living Theatre</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American actress and director, co-founded <a href=\"https://wikipedia.org/wiki/The_Living_Theatre\" title=\"The Living Theatre\">The Living Theatre</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Living Theatre", "link": "https://wikipedia.org/wiki/The_Living_Theatre"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Danish director, producer, and screenwriter (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish director, producer, and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish director, producer, and screenwriter (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actor (d. 2020)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2020)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1928", "text": "<PERSON>, German-American sex therapist, talk show host, professor, author, and Holocaust survivor (d. 2024)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sex therapist, talk show host, professor, author, and Holocaust survivor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sex therapist, talk show host, professor, author, and Holocaust survivor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Greek lawyer and politician, 5th President of Greece (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Greece", "link": "https://wikipedia.org/wiki/President_of_Greece"}]}, {"year": "1930", "text": "<PERSON>, English air marshal and politician, Lord Lieutenant of Moray (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English air marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Moray\" title=\"Lord Lieutenant of Moray\">Lord Lieutenant of Moray</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English air marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Moray\" title=\"Lord Lieutenant of Moray\">Lord Lieutenant of Moray</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of Moray", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Moray"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American singer and actress (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>a King\"><PERSON><PERSON></a>, American singer and actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Morgana King\"><PERSON><PERSON></a>, American singer and actress (d. 2018)", "links": [{"title": "<PERSON><PERSON> King", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_King"}]}, {"year": "1930", "text": "<PERSON>, Russian ice hockey player and coach (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1930)\" class=\"mw-redirect\" title=\"<PERSON> (ice hockey, born 1930)\"><PERSON></a>, Russian ice hockey player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1930)\" class=\"mw-redirect\" title=\"<PERSON> (ice hockey, born 1930)\"><PERSON></a>, Russian ice hockey player and coach (d. 2014)", "links": [{"title": "<PERSON> (ice hockey, born 1930)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1930)"}]}, {"year": "1931", "text": "<PERSON>, Austrian-Australian biologist and academic", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Australian biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Australian biologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actor (d. 2004)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American saxophonist and composer (d. 1975)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, New Zealand author and playwright (d. 2004)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand author and playwright (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand author and playwright (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Vincentian educator and politician, 6th Governor-General of Saint Vincent and the Grenadines", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vincentian educator and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Saint_Vincent_and_the_Grenadines\" title=\"Governor-General of Saint Vincent and the Grenadines\">Governor-General of Saint Vincent and the Grenadines</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vincentian educator and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Saint_Vincent_and_the_Grenadines\" title=\"Governor-General of Saint Vincent and the Grenadines\">Governor-General of Saint Vincent and the Grenadines</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor-General of Saint Vincent and the Grenadines", "link": "https://wikipedia.org/wiki/Governor-General_of_Saint_<PERSON>_and_the_Grenadines"}]}, {"year": "1934", "text": "<PERSON>, Kenyan-British conservationist and author (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-British conservationist and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-British conservationist and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Canadian soprano and actress", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soprano and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON>, Ethiopian economist and diplomat (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian economist and diplomat (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian economist and diplomat (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ka"}]}, {"year": "1936", "text": "<PERSON>, American fashion designer and businessman, co-founded Nine West (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Nine_West\" title=\"Nine West\">Nine West</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Nine_West\" title=\"Nine West\">Nine West</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nine West", "link": "https://wikipedia.org/wiki/Nine_West"}]}, {"year": "1936", "text": "<PERSON>, American actor", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer and guitarist (d. 2006)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fender\"><PERSON></a>, American singer and guitarist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Freddy Fender\"><PERSON></a>, American singer and guitarist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American wrestler (d. 1999)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Gorilla_Monsoon\" title=\"Gorilla Monsoon\">Go<PERSON></a>, American wrestler (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gorilla_Monsoon\" title=\"Gorilla Monsoon\">Go<PERSON></a>, American wrestler (d. 1999)", "links": [{"title": "Gorilla Monsoon", "link": "https://wikipedia.org/wiki/Gorilla_Monsoon"}]}, {"year": "1937", "text": "<PERSON>, Canadian-American businessman and publisher, founded Boston Properties", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman and publisher, founded <a href=\"https://wikipedia.org/wiki/Boston_Properties\" class=\"mw-redirect\" title=\"Boston Properties\">Boston Properties</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman and publisher, founded <a href=\"https://wikipedia.org/wiki/Boston_Properties\" class=\"mw-redirect\" title=\"Boston Properties\">Boston Properties</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Boston Properties", "link": "https://wikipedia.org/wiki/Boston_Properties"}]}, {"year": "1938", "text": "<PERSON>, Canadian journalist and politician, 23rd Lieutenant Governor of Manitoba (d. 2016)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian journalist and politician, 23rd <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Manitoba\" title=\"Lieutenant Governor of Manitoba\">Lieutenant Governor of Manitoba</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian journalist and politician, 23rd <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Manitoba\" title=\"Lieutenant Governor of Manitoba\">Lieutenant Governor of Manitoba</a> (d. 2016)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Lieutenant Governor of Manitoba", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Manitoba"}]}, {"year": "1938", "text": "<PERSON>, American baseball player", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ma<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>y"}]}, {"year": "1939", "text": "<PERSON>, 11th Marquess of Sligo, Anglo-Irish peer (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Marquess_of_Sligo\" title=\"<PERSON>, 11th Marquess of Sligo\"><PERSON>, 11th Marquess of Sligo</a>, Anglo-Irish peer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_11th_Marquess_of_Sligo\" title=\"<PERSON>, 11th Marquess of Sligo\"><PERSON>, 11th Marquess of Sligo</a>, Anglo-Irish peer (d. 2014)", "links": [{"title": "<PERSON>, 11th Marquess of Sligo", "link": "https://wikipedia.org/wiki/<PERSON>,_11th_Marquess_of_Sligo"}]}, {"year": "1939", "text": "<PERSON>, Canadian civil servant and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian civil servant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian civil servant and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American director and producer (d. 2008)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Scottish journalist and politician, 2nd Presiding Officer of the Scottish Parliament", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_politician)\" title=\"<PERSON> (Scottish politician)\"><PERSON></a>, Scottish journalist and politician, 2nd <a href=\"https://wikipedia.org/wiki/Presiding_Officer_of_the_Scottish_Parliament\" title=\"Presiding Officer of the Scottish Parliament\">Presiding Officer of the Scottish Parliament</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Scottish_politician)\" title=\"<PERSON> (Scottish politician)\"><PERSON></a>, Scottish journalist and politician, 2nd <a href=\"https://wikipedia.org/wiki/Presiding_Officer_of_the_Scottish_Parliament\" title=\"Presiding Officer of the Scottish Parliament\">Presiding Officer of the Scottish Parliament</a>", "links": [{"title": "<PERSON> (Scottish politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_politician)"}, {"title": "Presiding Officer of the Scottish Parliament", "link": "https://wikipedia.org/wiki/Presiding_Officer_of_the_Scottish_Parliament"}]}, {"year": "1940", "text": "<PERSON>, Slovak-Austrian bishop", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-Austrian bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-Austrian bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Australian playwright and screenwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian playwright and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian playwright and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American mountaineer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian lawyer and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Australian radio and television host", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(host)\" title=\"<PERSON> (host)\"><PERSON></a>, Australian radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(host)\" title=\"<PERSON> (host)\"><PERSON></a>, Australian radio and television host", "links": [{"title": "<PERSON> (host)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(host)"}]}, {"year": "1943", "text": "<PERSON>, American golfer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Scottish saxophonist and songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish saxophonist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish saxophonist and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter and actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American saxophonist, clarinet player, and composer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, clarinet player, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, clarinet player, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English rower and coach (d. 2015)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Scottish singer-songwriter and guitarist (d. 2009)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Austrian businessman and politician, 25th Chancellor of Austria", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian businessman and politician, 25th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian businessman and politician, 25th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Austria", "link": "https://wikipedia.org/wiki/Chancellor_of_Austria"}]}, {"year": "1948", "text": "<PERSON>, English jockey", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Bob_Champion\" title=\"Bob Champion\">Bob <PERSON></a>, English jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bob_Champion\" title=\"Bob Champion\">Bob <PERSON></a>, English jockey", "links": [{"title": "Bob Champion", "link": "https://wikipedia.org/wiki/<PERSON>_Champion"}]}, {"year": "1948", "text": "<PERSON>, Canadian golfer and sportscaster", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>rgen_Sparwasser\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>rgen_<PERSON>rwasser\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCrgen_Sparwasser"}]}, {"year": "1949", "text": "<PERSON>, Canadian actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American lawyer and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian bishop (d. 2012)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bishop (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bishop (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American author", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Polish runner (d. 1981)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Bronis%C5%82<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON></a>, Polish runner (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bronis%C5%82<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON></a>, Polish runner (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON> (athlete)", "link": "https://wikipedia.org/wiki/Bronis%C5%82aw_<PERSON><PERSON>_(athlete)"}]}, {"year": "1951", "text": "<PERSON>, English journalist and author", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American author and illustrator", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English actor and playwright", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Canadian politician, Saskatchewan MLA (1999-2023) (d. 2024)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, <a href=\"https://wikipedia.org/wiki/Legislative_Assembly_of_Saskatchewan\" title=\"Legislative Assembly of Saskatchewan\">Saskatchewan MLA</a> (1999-2023) (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, <a href=\"https://wikipedia.org/wiki/Legislative_Assembly_of_Saskatchewan\" title=\"Legislative Assembly of Saskatchewan\">Saskatchewan MLA</a> (1999-2023) (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Legislative Assembly of Saskatchewan", "link": "https://wikipedia.org/wiki/Legislative_Assembly_of_Saskatchewan"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Polish historian and politician, 5th President of Poland", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Bronis%C5%82<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish historian and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bronis%C5%82<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish historian and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bronis%C5%82aw_<PERSON><PERSON>"}, {"title": "President of Poland", "link": "https://wikipedia.org/wiki/President_of_Poland"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, Zimbabwean author and poet (d. 1987)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Zimbabwean author and poet (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Zimbabwean author and poet (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1953", "text": "<PERSON>, American journalist and politician, 6th Governor of Hawaii", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Hawaii\" title=\"Governor of Hawaii\">Governor of Hawaii</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Hawaii\" title=\"Governor of Hawaii\">Governor of Hawaii</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Hawaii", "link": "https://wikipedia.org/wiki/Governor_of_Hawaii"}]}, {"year": "1953", "text": "<PERSON>, Scottish musician and songwriter (d. 1979)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish musician and songwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish musician and songwriter (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Japanese businessman, founded Huser", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ser"}]}, {"year": "1953", "text": "<PERSON>, English guitarist and producer (d. 2002)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and producer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English saxophonist and composer (d. 2014)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English saxophonist and composer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English saxophonist and composer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Japanese actor and voice actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and voice actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Scottish author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>c<PERSON>ermid\" title=\"<PERSON>D<PERSON>mid\"><PERSON></a>, Scottish author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>c<PERSON>ermid\" title=\"<PERSON>Dermid\"><PERSON></a>, Scottish author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Val_M<PERSON>ermid"}]}, {"year": "1955", "text": "<PERSON>, American singer and actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American journalist and author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American baseball player and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1956", "text": "<PERSON>, American author and poet", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Scottish footballer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Bolivian runner", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bolivian_athlete)\" title=\"<PERSON> (Bolivian athlete)\"><PERSON></a>, Bolivian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bolivian_athlete)\" title=\"<PERSON> (Bolivian athlete)\"><PERSON></a>, Bolivian runner", "links": [{"title": "<PERSON> (Bolivian athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bolivian_athlete)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Greek politician, 21st Greek Minister for Culture", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, 21st <a href=\"https://wikipedia.org/wiki/Minister_for_Culture_(Greece)\" class=\"mw-redirect\" title=\"Minister for Culture (Greece)\">Greek Minister for Culture</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, 21st <a href=\"https://wikipedia.org/wiki/Minister_for_Culture_(Greece)\" class=\"mw-redirect\" title=\"Minister for Culture (Greece)\">Greek Minister for Culture</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georg<PERSON>_<PERSON>"}, {"title": "Minister for Culture (Greece)", "link": "https://wikipedia.org/wiki/Minister_for_Culture_(Greece)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Indian businessman and Chairman of Reliance Infrastructure", "html": "1959 - <a href=\"https://wikipedia.org/wiki/An<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businessman and Chairman of Reliance Infrastructure", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businessman and Chairman of Reliance Infrastructure", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anil_<PERSON>i"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Serbian footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Milo%C5%A1_%C4%90elma%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milo%C5%A1_%C4%90elma%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milo%C5%A1_%C4%90elma%C5%A1"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American guitarist and keyboard player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>(keyboardist)\" title=\"<PERSON> (keyboardist)\"><PERSON></a>, American guitarist and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(keyboardist)\" title=\"<PERSON> (keyboardist)\"><PERSON></a>, American guitarist and keyboard player", "links": [{"title": "<PERSON> (keyboardist)", "link": "https://wikipedia.org/wiki/<PERSON>_(keyboardist)"}]}, {"year": "1960", "text": "<PERSON>, English television presenter, comedian, singer and former footballer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television presenter, comedian, singer and former footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television presenter, comedian, singer and former footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_DeBarge\" title=\"<PERSON> DeBarge\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_DeBarge\" title=\"El DeBarge\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "El DeBarge", "link": "https://wikipedia.org/wiki/El_DeBarge"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Hungarian businessman and politician, 6th Prime Minister of Hungary", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Ferenc_Gyurcs%C3%A1ny\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>renc_Gyurcs%C3%A1ny\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a>", "links": [{"title": "<PERSON>ren<PERSON> Gyu<PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_Gyurcs%C3%A1ny"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish race car driver", "html": "1962 - <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>ztof_Ho%C5%82owczyc\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON><PERSON>tof_Ho%C5%82owczyc\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Krzysztof_Ho%C5%82owczyc"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Polish cyclist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Zenon_Jasku%C5%82a\" title=\"Zenon Jaskuła\">Zenon Jaskuła</a>, Polish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zenon_Jasku%C5%82a\" title=\"Zenon Jaskuła\">Zen<PERSON> Jaskuła</a>, Polish cyclist", "links": [{"title": "Zenon Jaskuła", "link": "https://wikipedia.org/wiki/Zenon_Jasku%C5%82a"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter and pastor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pastor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pastor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Hong Kong solicitor and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ho\"><PERSON><PERSON></a>, Hong Kong solicitor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ho\"><PERSON><PERSON></a>, Hong Kong solicitor and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, New Zealand rugby union player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby union player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American basketball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Japanese animator, producer, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese animator, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese animator, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian motorcycle racer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American tennis player and preacher", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and preacher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and preacher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Italian soprano and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American mathematician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mathematician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Russian mathematician and academic (d. 2017)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>oevodsky"}]}, {"year": "1966", "text": "<PERSON>, English politician, Shadow Secretary of State for Wales", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales\" title=\"Shadow Secretary of State for Wales\">Shadow Secretary of State for Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales\" title=\"Shadow Secretary of State for Wales\">Shadow Secretary of State for Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>gin"}, {"title": "Shadow Secretary of State for Wales", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales"}]}, {"year": "1967", "text": "<PERSON>, Canadian actor, dancer, choreographer, director, and educator", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, dancer, choreographer, director, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, dancer, choreographer, director, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American colonel and astronaut", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American colonel and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American colonel and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Cuban-Spanish long jumper", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Montalvo\" title=\"<PERSON><PERSON><PERSON> Montalvo\"><PERSON><PERSON><PERSON></a>, Cuban-Spanish long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Montalvo\" title=\"<PERSON><PERSON><PERSON> Montalvo\"><PERSON><PERSON><PERSON></a>, Cuban-Spanish long jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niur<PERSON>_Montalvo"}]}, {"year": "1968", "text": "<PERSON>, American R&B singer-songwriter, keyboard player, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Al_<PERSON>._Sure!\" title=\"Al B. Sure!\">Al <PERSON>. Sure!</a>, American R&amp;B singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_<PERSON>._Sure!\" title=\"Al B. Sure!\"><PERSON>. Sure!</a>, American R&amp;B singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON>!", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._<PERSON>!"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Chilean-American actor and comedian", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean-American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean-American actor and comedian", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Horatio_<PERSON>z"}]}, {"year": "1970", "text": "<PERSON>, Italian skier", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1970", "text": "<PERSON>, English-Canadian DJ and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian DJ and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian DJ and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English bass player and songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Polish-Swedish actress and model", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Swedish actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Swedish actress and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Congolese soldier and politician, President of the Democratic Republic of the Congo", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Democratic_Republic_of_the_Congo\" title=\"President of the Democratic Republic of the Congo\">President of the Democratic Republic of the Congo</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Democratic_Republic_of_the_Congo\" title=\"President of the Democratic Republic of the Congo\">President of the Democratic Republic of the Congo</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/President_of_the_Democratic_Republic_of_the_Congo"}]}, {"year": "1971", "text": "<PERSON>, American lawyer and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Japanese director and composer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wyle\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_W<PERSON>\" title=\"<PERSON> Wyle\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wyle"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American ice hockey defenseman", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey defenseman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey defenseman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American comedian, actor, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American wrestler and trainer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hipwreck\" title=\"<PERSON><PERSON>hipwreck\"><PERSON><PERSON></a>, American wrestler and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hipwreck\" title=\"<PERSON><PERSON>hipwreck\"><PERSON><PERSON></a>, American wrestler and trainer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Whipwreck"}]}, {"year": "1974", "text": "<PERSON>, Indian chef (d. 2012)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian chef (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian chef (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American baseball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English lawyer and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Slovak tennis player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1rov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1rov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Janette_Hus%C3%A1rov%C3%A1"}]}, {"year": "1974", "text": "<PERSON>, American poet and author", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English comedian and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Russell Brand\"><PERSON></a>, English comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Russell Brand\"><PERSON></a>, English comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress, filmmaker, humanitarian, and activist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, filmmaker, humanitarian, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, filmmaker, humanitarian, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Trinidadian cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Trinidadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Trinidadian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Australian singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Russian lawyer and politician (d. 2024)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lawyer and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lawyer and politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Serbian tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Nenad_Zimonji%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nenad_Zimonji%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nenad_Zimonji%C4%87"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Austrian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Ukrainian guitarist and composer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Roman_Mir<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Mir<PERSON>\" title=\"Roman Mir<PERSON>ko\"><PERSON></a>, Ukrainian guitarist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American economist and professor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American economist and professor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American economist and professor", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, South African-Australian rugby player (d. 2017)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African-Australian rugby player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African-Australian rugby player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Beau<PERSON>min\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Beau<PERSON>min\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Beauchemin"}]}, {"year": "1981", "text": "<PERSON>, Canadian swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Canadian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Canadian swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>(swimmer)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, American actor and comedian", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/G<PERSON>ur<PERSON>_Seitaridis\" title=\"<PERSON><PERSON>ur<PERSON>itarid<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>itaridis\" title=\"<PERSON><PERSON>ur<PERSON> Seitarid<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giourkas_Seitaridis"}]}, {"year": "1981", "text": "<PERSON>-<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Russian basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Scottish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Kenyan runner", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American-Canadian football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Ivorian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)"}]}, {"year": "1983", "text": "<PERSON>, Ivorian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emmanuel_E<PERSON>u%C3%A9"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Ukrainian triple jumper", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian triple jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1984", "text": "<PERSON>, Cameroonian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Japanese actor and singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1984", "text": "<PERSON>, Scottish football manager and former player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish football manager and former player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish football manager and former player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Taiwanese actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, South African painter and DJ (d. 2011)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African painter and DJ (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African painter and DJ (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, German tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_Gr%C3%B6<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_Gr%C3%B6<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anna-<PERSON>_Gr%C3%B6<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American figure skater", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, German footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>do<PERSON>\" title=\"<PERSON><PERSON>do<PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>do<PERSON>\" title=\"<PERSON><PERSON>do<PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>do<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Israeli model and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Re<PERSON>eli\" title=\"<PERSON> Re<PERSON>eli\"><PERSON></a>, Israeli model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Re<PERSON>\" title=\"<PERSON> Re<PERSON>eli\"><PERSON></a>, Israeli model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Re<PERSON>eli"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Norwegian politician", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>kvam\" title=\"<PERSON><PERSON>vam\"><PERSON><PERSON></a>, Norwegian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>kvam\" title=\"<PERSON><PERSON>kvam\"><PERSON><PERSON></a>, Norwegian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Reiakvam"}]}, {"year": "1987", "text": "<PERSON><PERSON>, English singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Mollie King\"><PERSON><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Mollie King\"><PERSON><PERSON></a>, English singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American ice hockey defenseman", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey defenseman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey defenseman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Australian model", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Kimberley_Busteed\" title=\"Kimberley Busteed\"><PERSON><PERSON> Busteed</a>, Australian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kimberley_Busteed\" title=\"Kimberley Busteed\"><PERSON><PERSON> Busteed</a>, Australian model", "links": [{"title": "Kimberley Busteed", "link": "https://wikipedia.org/wiki/Kimberley_Busteed"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch-born Surinamese footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Tjaronn_Chery\" title=\"Tjaronn Chery\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch-born Surinamese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tjaronn_Chery\" title=\"Tjaronn Chery\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch-born Surinamese footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tjaronn_Chery"}]}, {"year": "1989", "text": "<PERSON>, Italian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Federico_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Polish hammer thrower", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Pawe%C5%82_F<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish hammer thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pawe%C5%82_F<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish hammer thrower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pawe%C5%82_<PERSON><PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American Internet entrepreneur", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Internet entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Internet entrepreneur", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Italian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, New Zealand rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, New Zealand-English cricketer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Hugill"}]}, {"year": "1993", "text": "<PERSON>, Canadian ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Paraguayan footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, British-American cross-country skier", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British-American cross-country skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British-American cross-country skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Scottish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ie"}]}, {"year": "1998", "text": "<PERSON>, British rapper and songwriter ", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Central_Cee\" title=\"Central Cee\">Central Cee</a>, British rapper and songwriter ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Central_Cee\" title=\"Central Cee\">Central Cee</a>, British rapper and songwriter ", "links": [{"title": "Central Cee", "link": "https://wikipedia.org/wiki/Central_Cee"}]}, {"year": "1999", "text": "<PERSON>, South Korean actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yun"}]}, {"year": "1999", "text": "<PERSON>, Australian activist", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bo\" title=\"<PERSON><PERSON><PERSON>bo\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bo\" title=\"<PERSON><PERSON><PERSON>bo\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bo"}]}, {"year": "2004", "text": "<PERSON>, American child actress, dancer, and recording artist", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Child_actor\" title=\"Child actor\">child actress</a>, dancer, and <a href=\"https://wikipedia.org/wiki/Recording_Artist\" class=\"mw-redirect\" title=\"Recording Artist\">recording artist</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Child_actor\" title=\"Child actor\">child actress</a>, dancer, and <a href=\"https://wikipedia.org/wiki/Recording_Artist\" class=\"mw-redirect\" title=\"Recording Artist\">recording artist</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Child actor", "link": "https://wikipedia.org/wiki/Child_actor"}, {"title": "Recording Artist", "link": "https://wikipedia.org/wiki/Recording_Artist"}]}, {"year": "2021", "text": "Princess <PERSON><PERSON><PERSON> of Sussex", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Princess_Lilibe<PERSON>_of_Sussex\" title=\"Princess Lil<PERSON><PERSON> of Sussex\">Princess <PERSON><PERSON><PERSON> of Sussex</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Lilibe<PERSON>_of_Sussex\" title=\"Princess Lilibe<PERSON> of Sussex\">Princess <PERSON><PERSON><PERSON> of Sussex</a>", "links": [{"title": "Princess <PERSON><PERSON><PERSON> of Sussex", "link": "https://wikipedia.org/wiki/Princess_Lilibet_of_Sussex"}]}], "Deaths": [{"year": "756", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese emperor (b. 701)", "html": "756 - <a href=\"https://wikipedia.org/wiki/Emperor_Sh%C5%8Dmu\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Sh%C5%8Dmu\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 701)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Sh%C5%8Dmu"}]}, {"year": "863", "text": "<PERSON>, archbishop of Mainz", "html": "863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Mainz)\" title=\"<PERSON> (archbishop of Mainz)\"><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Electorate_of_Mainz\" title=\"Electorate of Mainz\">Mainz</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Mainz)\" title=\"<PERSON> (archbishop of Mainz)\"><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Electorate_of_Mainz\" title=\"Electorate of Mainz\">Mainz</a>", "links": [{"title": "<PERSON> (archbishop of Mainz)", "link": "https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Mainz)"}, {"title": "Electorate of Mainz", "link": "https://wikipedia.org/wiki/Electorate_of_Mainz"}]}, {"year": "895", "text": "<PERSON>, chancellor of the Tang Dynasty", "html": "895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Xi_(Tang_dynasty)\" title=\"Li Xi (Tang dynasty)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor</a> of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang Dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Tang_dynasty)\" title=\"<PERSON> Xi (Tang dynasty)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor</a> of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang Dynasty</a>", "links": [{"title": "<PERSON> (Tang dynasty)", "link": "https://wikipedia.org/wiki/<PERSON>_Xi_(Tang_dynasty)"}, {"title": "Chancellor of the Tang dynasty", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "946", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON> (Gybbos<PERSON>), Lombard prince", "html": "946 - <a href=\"https://wikipedia.org/wiki/Guaimar_II_of_Salerno\" title=\"Guaimar II of Salerno\">Guaimar II</a> (<PERSON><PERSON><PERSON><PERSON><PERSON>), Lombard prince", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guaimar_II_of_Salerno\" title=\"Guaimar II of Salerno\">Guaimar II</a> (<PERSON><PERSON><PERSON><PERSON><PERSON>), Lombard prince", "links": [{"title": "Guaimar II of Salerno", "link": "https://wikipedia.org/wiki/Guaimar_II_of_Salerno"}]}, {"year": "956", "text": "<PERSON> of Shirvan, Muslim ruler", "html": "956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Shirvan\" title=\"<PERSON> III of Shirvan\"><PERSON> of Shirvan</a>, Muslim ruler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Shirvan\" title=\"<PERSON> III of Shirvan\"><PERSON> of Shirvan</a>, Muslim ruler", "links": [{"title": "<PERSON> of Shirvan", "link": "https://wikipedia.org/wiki/<PERSON>_III_<PERSON>_Shirvan"}]}, {"year": "1039", "text": "<PERSON>, Holy Roman Emperor (b. 990)", "html": "1039 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 990)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1102", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish nobleman (b. c. 1044)", "html": "1102 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Polish nobleman (b. c. 1044)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish nobleman (b. c. 1044)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_I_<PERSON>"}]}, {"year": "1134", "text": "<PERSON> of Sweden (b. 1106)", "html": "1134 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> I of Sweden\"><PERSON> of Sweden</a> (b. 1106)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> I of Sweden\"><PERSON> of Sweden</a> (b. 1106)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1135", "text": "Emperor <PERSON><PERSON> of Song (b. 1082)", "html": "1135 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (b. 1082)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (b. 1082)", "links": [{"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}]}, {"year": "1206", "text": "<PERSON><PERSON> of Champagne (b. 1140)", "html": "1206 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Champagne\" title=\"<PERSON><PERSON> of Champagne\"><PERSON><PERSON> of Champagne</a> (b. 1140)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Champagne\" title=\"<PERSON><PERSON> of Champagne\"><PERSON><PERSON> of Champagne</a> (b. 1140)", "links": [{"title": "<PERSON><PERSON> of Champagne", "link": "https://wikipedia.org/wiki/<PERSON>ela_of_Champagne"}]}, {"year": "1246", "text": "<PERSON> of Angoulême (b. 1188)", "html": "1246 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Angoul%C3%AAme\" title=\"<PERSON> of Angoulême\"><PERSON> of Angoulême</a> (b. 1188)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Angoul%C3%AAme\" title=\"<PERSON> of Angoulême\"><PERSON> of Angoulême</a> (b. 1188)", "links": [{"title": "<PERSON> of Angoulême", "link": "https://wikipedia.org/wiki/<PERSON>_of_Angoul%C3%AAme"}]}, {"year": "1257", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> I of Greater Poland (b. 1221)", "html": "1257 - <a href=\"https://wikipedia.org/wiki/Przemys%C5%82_I_of_Greater_Poland\" title=\"Przemysł I of Greater Poland\">Przemysł I of Greater Poland</a> (b. 1221)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Przemys%C5%82_I_of_Greater_Poland\" title=\"Przemysł I of Greater Poland\">Przemysł I of Greater Poland</a> (b. 1221)", "links": [{"title": "Przemysł I of Greater Poland", "link": "https://wikipedia.org/wiki/Przemys%C5%82_I_of_Greater_Poland"}]}, {"year": "1394", "text": "<PERSON>, wife of <PERSON> of England (b.c. 1368)", "html": "1394 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> IV of England\"><PERSON> of England</a> (b.c. 1368)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> IV of England\"><PERSON> of England</a> (b.c. 1368)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_England"}]}, {"year": "1453", "text": "Andronikos Palaiolo<PERSON>, Byzantine commander", "html": "1453 - <a href=\"https://wikipedia.org/wiki/Andronikos_Palaiologos_Kanta<PERSON>nos\" title=\"Andronikos Palaiologos Kanta<PERSON>uzenos\">Andronikos Palaiologos <PERSON></a>, Byzantine commander", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andronikos_Palaiologos_Kanta<PERSON>uzenos\" title=\"Andronikos Palaiologos Kantakouzenos\">Andronikos Palaiologos <PERSON></a>, Byzantine commander", "links": [{"title": "Andronikos Palaiologos <PERSON>", "link": "https://wikipedia.org/wiki/Andronikos_Palaiologos_Ka<PERSON>nos"}]}, {"year": "1463", "text": "<PERSON><PERSON><PERSON>, Italian historian and author (b. 1392)", "html": "1463 - <a href=\"https://wikipedia.org/wiki/Flavio_Biondo\" title=\"Flavio Biondo\"><PERSON><PERSON><PERSON></a>, Italian historian and author (b. 1392)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flavio_Biondo\" title=\"Flavio Biondo\"><PERSON><PERSON><PERSON></a>, Italian historian and author (b. 1392)", "links": [{"title": "Flavio Biondo", "link": "https://wikipedia.org/wiki/Flavio_Biondo"}]}, {"year": "1472", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Aztec poet (b. 1402)", "html": "1472 - <a href=\"https://wikipedia.org/wiki/Nezahualcoyotl_(tlatoani)\" title=\"Nezahualcoyotl (tlatoani)\">Nezahualcoyotl</a>, Aztec poet (b. 1402)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nezahualcoyotl_(tlatoani)\" title=\"Nezahualcoyotl (tlatoani)\">Nezahualcoyotl</a>, Aztec poet (b. 1402)", "links": [{"title": "Nezahualcoyotl (tlatoani)", "link": "https://wikipedia.org/wiki/Nezahualcoyotl_(tlatoani)"}]}, {"year": "1585", "text": "<PERSON><PERSON><PERSON>, French philosopher and author (b. 1526)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and author (b. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and author (b. 1526)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Muretus"}]}, {"year": "1608", "text": "<PERSON>, Italian Catholic priest (b. 1563)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saint)\" class=\"mw-redirect\" title=\"<PERSON> (saint)\"><PERSON></a>, Italian Catholic priest (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saint)\" class=\"mw-redirect\" title=\"<PERSON> (saint)\"><PERSON></a>, Italian Catholic priest (b. 1563)", "links": [{"title": "<PERSON> (saint)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saint)"}]}, {"year": "1622", "text": "<PERSON><PERSON><PERSON>, Hungarian soldier and historian (b. 1568)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/P%C3%A9ter_R%C3%A9vay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian soldier and historian (b. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A9ter_R%C3%A9vay\" title=\"<PERSON><PERSON><PERSON> Ré<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian soldier and historian (b. 1568)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A9ter_R%C3%A9vay"}]}, {"year": "1647", "text": "<PERSON><PERSON>, Grand Chief <PERSON><PERSON><PERSON> of the Narragansett (b. 1565)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/Canonicus\" title=\"Canonicus\"><PERSON><PERSON></a>, Grand Chief <PERSON><PERSON><PERSON> of the Narragansett (b. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canonicus\" title=\"Canonicus\">Canon<PERSON></a>, Grand Chief <PERSON><PERSON><PERSON> of the Narragansett (b. 1565)", "links": [{"title": "Canonicus", "link": "https://wikipedia.org/wiki/Canonicus"}]}, {"year": "1663", "text": "<PERSON>, English archbishop and academic (b. 1582)", "html": "1663 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and academic (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and academic (b. 1582)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, Italian adventurer and author (b. 1725)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian adventurer and author (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian adventurer and author (b. 1725)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, American minister and politician, 1st Speaker of the United States House of Representatives (b. 1750)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and politician, 1st <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and politician, 1st <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1809", "text": "<PERSON><PERSON>, Danish neoclassical and history painter, sculptor and architect (b. 1743)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish neoclassical and history painter, sculptor and architect (b. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish neoclassical and history painter, sculptor and architect (b. 1743)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Venezuelan general and politician, 2nd President of Bolivia (b. 1795)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_de_<PERSON>cre"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "1872", "text": "<PERSON>, Dutch historian, jurist, and politician, Prime Minister of the Netherlands (b. 1798)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian, jurist, and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian, jurist, and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1875", "text": "<PERSON>, German pastor and poet (b. 1804)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rike\" title=\"<PERSON>\"><PERSON></a>, German pastor and poet (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rike\" title=\"<PERSON>\"><PERSON></a>, German pastor and poet (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_M%C3%B6rike"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of the Ottoman Empire, 32nd Sultan of the Ottoman Empire (b. 1830)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Abd%C3%BC<PERSON><PERSON>z_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of the Ottoman Empire\"><PERSON><PERSON><PERSON><PERSON><PERSON> of the Ottoman Empire</a>, 32nd Sultan of the Ottoman Empire (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abd%C3%<PERSON><PERSON><PERSON>z_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of the Ottoman Empire\"><PERSON><PERSON><PERSON><PERSON><PERSON> of the Ottoman Empire</a>, 32nd Sultan of the Ottoman Empire (b. 1830)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>z of the Ottoman Empire", "link": "https://wikipedia.org/wiki/Abd%C3%BCla<PERSON>z_of_the_Ottoman_Empire"}]}, {"year": "1906", "text": "<PERSON>, British writer (b. 1857)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, English anthropologist, neurologist, ethnologist, and psychiatrist (b. 1864)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"W. H. R. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English anthropologist, neurologist, ethnologist, and psychiatrist (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"W. H. R. Rivers\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English anthropologist, neurologist, ethnologist, and psychiatrist (b. 1864)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American Academic (b. 1865)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Academic (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Academic (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Australian-English cricketer and coach (b. 1853)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English cricketer and coach (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English cricketer and coach (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Chinese warlord (b. 1873)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese warlord (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese warlord (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American director, producer, and agent (b. 1881)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and agent (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and agent (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Sharif of Mecca, <PERSON> and Emir of Mecca, King of the Hejaz (b. 1853-54)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_of_Mecca\" class=\"mw-redirect\" title=\"<PERSON>, Sharif of Mecca\"><PERSON>, Sharif of Mecca</a>, <PERSON> and Emir of Mecca, King of the Hejaz (b. 1853-54)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sharif_of_Mecca\" class=\"mw-redirect\" title=\"<PERSON>, Sharif of Mecca\"><PERSON>, Sharif of Mecca</a>, <PERSON> and Emir of Mecca, King of the Hejaz (b. 1853-54)", "links": [{"title": "<PERSON>, Sharif of Mecca", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sharif_of_Mecca"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Turkish poet and author (b. 1884)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Ahmet_Ha%C5%9Fim\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish poet and author (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahmet_Ha%C5%9Fim\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish poet and author (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ahmet_Ha%C5%9Fim"}]}, {"year": "1936", "text": "<PERSON><PERSON>, English pianist and educator (b. 1869)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English pianist and educator (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English pianist and educator (b. 1869)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American trumpet player (b. 1900)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, German Emperor (b. 1859)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_German_Emperor\" class=\"mw-redirect\" title=\"<PERSON> II, German Emperor\"><PERSON>, German Emperor</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\"><PERSON>, German Emperor</a> (b. 1859)", "links": [{"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, German SS officer and politician (b. 1904)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and politician (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and politician (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1951", "text": "<PERSON>, Russian-American bassist, composer, and conductor (b. 1874)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American bassist, composer, and conductor (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American bassist, composer, and conductor (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress and producer (b. 1881)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American sportscaster (b. 1882)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sportscaster (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sportscaster (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Estonian lawyer and politician (b. 1890)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress (b. 1898)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor (b. 1911)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian historian and philosopher (b. 1885)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_Luk%C3%A1cs\" title=\"György <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian historian and philosopher (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_Luk%C3%A1cs\" title=\"György <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian historian and philosopher (b. 1885)", "links": [{"title": "György <PERSON>", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_Luk%C3%A1cs"}]}, {"year": "1973", "text": "<PERSON>, French mathematician and academic (b. 1878)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Fr%C3%A9chet\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Fr%C3%A9chet\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Fr%C3%A9chet"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American songwriter, producer, and manager (b. 1917)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American songwriter, producer, and manager (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American songwriter, producer, and manager (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, New Zealand doctor and soldier (b. 1897)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand doctor and soldier (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand doctor and soldier (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American cartoonist (b. 1917)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cartoonist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cartoonist (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1949)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bat<PERSON>\" title=\"St<PERSON> Bators\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"St<PERSON> Bators\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1949)", "links": [{"title": "Stiv Bators", "link": "https://wikipedia.org/wiki/Stiv_Bators"}]}, {"year": "1992", "text": "<PERSON>, American businessman, founded Little League Baseball (b. 1910)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Little_League_Baseball\" title=\"Little League Baseball\">Little League Baseball</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Little_League_Baseball\" title=\"Little League Baseball\">Little League Baseball</a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Little League Baseball", "link": "https://wikipedia.org/wiki/Little_League_Baseball"}]}, {"year": "1993", "text": "<PERSON>, American writer (b. 1922)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English musician (b. 1943) ", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English musician (b. 1943) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English musician (b. 1943) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, English singer-songwriter, guitarist, and producer (b. 1946) ", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer (b. 1946) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lane\"><PERSON></a>, English singer-songwriter, guitarist, and producer (b. 1946) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actress (b. 1903) ", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1903) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1903) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Peruvian architect and politician, 42nd President of Peru (b. 1912) ", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA<PERSON>_Terry\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Peruvian architect and politician, 42nd <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (b. 1912) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Peruvian architect and politician, 42nd <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (b. 1912) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_Bela%C3%BAnde_Terry"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "2004", "text": "<PERSON>, American saxophonist and composer (b. 1934) ", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saxophonist)\" title=\"<PERSON> (saxophonist)\"><PERSON></a>, American saxophonist and composer (b. 1934) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saxophonist)\" title=\"<PERSON> (saxophonist)\"><PERSON></a>, American saxophonist and composer (b. 1934) ", "links": [{"title": "<PERSON> (saxophonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saxophonist)"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Italian actor (b. 1921) ", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor (b. 1921) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor (b. 1921) ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, American baseball player and manager (b. 1937) ", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (b. 1937) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (b. 1937) ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American businessman (b. 1933) ", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American businessman (b. 1933) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American businessman (b. 1933) ", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2007", "text": "<PERSON>, American captain and politician (b. 1933) ", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (b. 1933) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (b. 1933) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American basketball player and coach (b. 1910) ", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1910) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1910) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Virgin Islander sergeant and politician, 23rd Governor of the United States Virgin Islands (b. 1940) ", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Juan <PERSON>\"><PERSON></a>, Virgin Islander sergeant and politician, 23rd <a href=\"https://wikipedia.org/wiki/Governor_of_the_United_States_Virgin_Islands\" class=\"mw-redirect\" title=\"Governor of the United States Virgin Islands\">Governor of the United States Virgin Islands</a> (b. 1940) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Juan <PERSON>\"><PERSON></a>, Virgin Islander sergeant and politician, 23rd <a href=\"https://wikipedia.org/wiki/Governor_of_the_United_States_Virgin_Islands\" class=\"mw-redirect\" title=\"Governor of the United States Virgin Islands\">Governor of the United States Virgin Islands</a> (b. 1940) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of the United States Virgin Islands", "link": "https://wikipedia.org/wiki/Governor_of_the_United_States_Virgin_Islands"}]}, {"year": "2011", "text": "<PERSON>, Danish author and composer (b. 1953)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and composer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and composer (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, New Zealand architect, designed the Lyttelton Road Tunnel Administration Building (b. 1925) ", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand architect, designed the <a href=\"https://wikipedia.org/wiki/Lyttelton_Road_Tunnel_Administration_Building\" title=\"Lyttelton Road Tunnel Administration Building\">Lyttelton Road Tunnel Administration Building</a> (b. 1925) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand architect, designed the <a href=\"https://wikipedia.org/wiki/Lyttelton_Road_Tunnel_Administration_Building\" title=\"Lyttelton Road Tunnel Administration Building\">Lyttelton Road Tunnel Administration Building</a> (b. 1925) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lyttelton Road Tunnel Administration Building", "link": "https://wikipedia.org/wiki/Lyttelton_Road_Tunnel_Administration_Building"}]}, {"year": "2012", "text": "<PERSON>, Dominican-American baseball player (b. 1946)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Borb%C3%B3n"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Guatemalan cardinal (b. 1932) ", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Toru%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guatemalan cardinal (b. 1932) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Toru%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guatemalan cardinal (b. 1932) ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zada_Toru%C3%B1o"}]}, {"year": "2012", "text": "<PERSON>, American violinist (b. 1929) ", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist (b. 1929) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist (b. 1929) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American race car driver (b. 1916)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American drummer (b. 1945) ", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (b. 1945) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (b. 1945) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Icelandic footballer, handball player, and sportscaster (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Icelandic footballer, handball player, and sportscaster (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Icelandic footballer, handball player, and sportscaster (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1949)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1949)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2014", "text": "<PERSON>, American-Hong Kong businessman (b. 1919) ", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Hong Kong businessman (b. 1919) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Hong Kong businessman (b. 1919) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Zimbabwean journalist and politician, Zimbabwean Minister of Foreign Affairs (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Zimbabwe)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs (Zimbabwe)\">Zimbabwean Minister of Foreign Affairs</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Zimbabwe)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs (Zimbabwe)\">Zimbabwean Minister of Foreign Affairs</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs (Zimbabwe)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Zimbabwe)"}]}, {"year": "2014", "text": "<PERSON>, <PERSON>, English lawyer and judge (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Sydney_Templeman,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and judge (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Templeman,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and judge (b. 1920)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player, coach, and manager (b. 1931) ", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1931) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1931) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English economist and author (b. 1915) ", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and author (b. 1915) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and author (b. 1915) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Ukrainian mathematician and academic (b. 1938) ", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian mathematician and academic (b. 1938) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian mathematician and academic (b. 1938) ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American race car driver (b. 1930) ", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver (b. 1930) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver (b. 1930) ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, British academic and diplomat, British Ambassador to Denmark (b. 1927) ", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Denmark\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Denmark\">British Ambassador to Denmark</a> (b. 1927) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Denmark\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Denmark\">British Ambassador to Denmark</a> (b. 1927) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Ambassadors of the United Kingdom to Denmark", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Denmark"}]}, {"year": "2016", "text": "<PERSON>, Bissau-Guinean politician (b. 1937)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bissau-Guinean politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bissau-Guinean politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Spanish essayist, poet and novelist (b. 1931) ", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish essayist, poet and novelist (b. 1931) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish essayist, poet and novelist (b. 1931) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actor (b. 1939)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Barbadian novelist (b. 1927)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian novelist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian novelist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Indian actress (b. 1928)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Australian radio and television presenter  (b. 1947)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio and television presenter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio and television presenter (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, American racing driver (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American racing driver (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American racing driver (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}