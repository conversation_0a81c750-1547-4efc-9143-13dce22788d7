{"date": "July 27", "url": "https://wikipedia.org/wiki/July_27", "data": {"Events": [{"year": "1054", "text": "<PERSON><PERSON>, Earl of Northumbria, invades Scotland and defeats <PERSON><PERSON>, King of Scotland, somewhere north of the Firth of Forth. This is known as the Battle of Dunsinane.", "html": "1054 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Earl_of_Northumbria\" title=\"<PERSON><PERSON>, Earl of Northumbria\"><PERSON><PERSON>, Earl of Northumbria</a>, invades Scotland and defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland\" title=\"<PERSON><PERSON>, King of Scotland\"><PERSON><PERSON>, King of Scotland</a>, somewhere north of the <a href=\"https://wikipedia.org/wiki/Firth_of_Forth\" title=\"Firth of Forth\">Firth of Forth</a>. This is known as the <a href=\"https://wikipedia.org/wiki/Battle_of_Dunsinane\" title=\"Battle of Dunsinane\">Battle of Dunsinane</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Earl_of_Northumbria\" title=\"<PERSON><PERSON>, Earl of Northumbria\"><PERSON><PERSON>, Earl of Northumbria</a>, invades Scotland and defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland\" title=\"<PERSON><PERSON>, King of Scotland\"><PERSON><PERSON>, King of Scotland</a>, somewhere north of the <a href=\"https://wikipedia.org/wiki/Firth_of_Forth\" title=\"Firth of Forth\">Firth of Forth</a>. This is known as the <a href=\"https://wikipedia.org/wiki/Battle_of_Dunsinane\" title=\"Battle of Dunsinane\">Battle of Dunsinane</a>.", "links": [{"title": "<PERSON><PERSON>, Earl of Northumbria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Earl_of_Northumbria"}, {"title": "<PERSON><PERSON>, King of Scotland", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland"}, {"title": "Firth of Forth", "link": "https://wikipedia.org/wiki/Firth_of_Forth"}, {"title": "Battle of Dunsinane", "link": "https://wikipedia.org/wiki/Battle_of_Dunsinane"}]}, {"year": "1189", "text": "<PERSON> arrives at Niš, the capital of Serbian King <PERSON>, during the Third Crusade.", "html": "1189 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"Frederick I, Holy Roman Emperor\"><PERSON></a> arrives at <a href=\"https://wikipedia.org/wiki/Ni%C5%A1\" title=\"Ni<PERSON>\">Niš</a>, the capital of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia_(medieval)\" class=\"mw-redirect\" title=\"Kingdom of Serbia (medieval)\">Serbian King</a> <a href=\"https://wikipedia.org/wiki/Stefan_Nemanja\" title=\"<PERSON>\"><PERSON></a>, during the <a href=\"https://wikipedia.org/wiki/Third_Crusade\" title=\"Third Crusade\">Third Crusade</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"Frederick I, Holy Roman Emperor\"><PERSON></a> arrives at <a href=\"https://wikipedia.org/wiki/Ni%C5%A1\" title=\"Niš\">Niš</a>, the capital of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia_(medieval)\" class=\"mw-redirect\" title=\"Kingdom of Serbia (medieval)\">Serbian King</a> <a href=\"https://wikipedia.org/wiki/Stefan_Nemanja\" title=\"<PERSON>\"><PERSON></a>, during the <a href=\"https://wikipedia.org/wiki/Third_Crusade\" title=\"Third Crusade\">Third Crusade</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ni%C5%A1"}, {"title": "Kingdom of Serbia (medieval)", "link": "https://wikipedia.org/wiki/Kingdom_of_Serbia_(medieval)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Third Crusade", "link": "https://wikipedia.org/wiki/Third_Crusade"}]}, {"year": "1202", "text": "Georgian-Seljuk wars: At the Battle of Basian the Kingdom of Georgia defeats the Sultanate of Rum.", "html": "1202 - <a href=\"https://wikipedia.org/wiki/Georgian%E2%80%93Seljuk_wars\" title=\"Georgian-Seljuk wars\">Georgian-Seljuk wars</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Basian\" class=\"mw-redirect\" title=\"Battle of Basian\">Battle of Basian</a> the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Georgia\" title=\"Kingdom of Georgia\">Kingdom of Georgia</a> defeats the <a href=\"https://wikipedia.org/wiki/Sultanate_of_Rum\" title=\"Sultanate of Rum\">Sultanate of Rum</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgian%E2%80%93Seljuk_wars\" title=\"Georgian-Seljuk wars\">Georgian-Seljuk wars</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Basian\" class=\"mw-redirect\" title=\"Battle of Basian\">Battle of Basian</a> the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Georgia\" title=\"Kingdom of Georgia\">Kingdom of Georgia</a> defeats the <a href=\"https://wikipedia.org/wiki/Sultanate_of_Rum\" title=\"Sultanate of Rum\">Sultanate of Rum</a>.", "links": [{"title": "Georgian-Seljuk wars", "link": "https://wikipedia.org/wiki/Georgian%E2%80%93Seljuk_wars"}, {"title": "Battle of Basian", "link": "https://wikipedia.org/wiki/Battle_of_Basian"}, {"title": "Kingdom of Georgia", "link": "https://wikipedia.org/wiki/Kingdom_of_Georgia"}, {"title": "Sultanate of Rum", "link": "https://wikipedia.org/wiki/Sultanate_of_Rum"}]}, {"year": "1214", "text": "Battle of Bouvines: <PERSON> of France decisively defeats Imperial, English and Flemish armies, effectively ending John of England's Angevin Empire.", "html": "1214 - <a href=\"https://wikipedia.org/wiki/Battle_of_Bouvines\" title=\"Battle of Bouvines\">Battle of Bouvines</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> decisively defeats <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Imperial</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">English</a> and <a href=\"https://wikipedia.org/wiki/County_of_Flanders\" title=\"County of Flanders\">Flemish</a> armies, effectively ending <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\">John of England</a>'s <a href=\"https://wikipedia.org/wiki/Angevin_Empire\" title=\"Angevin Empire\">Angevin Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Bouvines\" title=\"Battle of Bouvines\">Battle of Bouvines</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> decisively defeats <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Imperial</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">English</a> and <a href=\"https://wikipedia.org/wiki/County_of_Flanders\" title=\"County of Flanders\">Flemish</a> armies, effectively ending <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of England</a>'s <a href=\"https://wikipedia.org/wiki/Angevin_Empire\" title=\"Angevin Empire\">Angevin Empire</a>.", "links": [{"title": "Battle of Bouvines", "link": "https://wikipedia.org/wiki/Battle_of_Bouvines"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "County of Flanders", "link": "https://wikipedia.org/wiki/County_of_Flanders"}, {"title": "<PERSON>, King of England", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_England"}, {"title": "Angevin Empire", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>in_<PERSON>"}]}, {"year": "1299", "text": "According to <PERSON>, <PERSON><PERSON> I invades the territory of Nicomedia for the first time, usually considered to be the founding day of the Ottoman state.", "html": "1299 - According to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I\" title=\"<PERSON>sman I\"><PERSON><PERSON> I</a> invades the territory of <a href=\"https://wikipedia.org/wiki/Nicomedia\" title=\"Nicomedia\">Nicomedia</a> for the first time, usually considered to be the founding day of the <a href=\"https://wikipedia.org/wiki/Ottoman_state\" class=\"mw-redirect\" title=\"Ottoman state\">Ottoman state</a>.", "no_year_html": "According to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I\" title=\"<PERSON>sman I\"><PERSON><PERSON> I</a> invades the territory of <a href=\"https://wikipedia.org/wiki/Nicomedia\" title=\"Nicomedia\">Nicomedia</a> for the first time, usually considered to be the founding day of the <a href=\"https://wikipedia.org/wiki/Ottoman_state\" class=\"mw-redirect\" title=\"Ottoman state\">Ottoman state</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nicomedia", "link": "https://wikipedia.org/wiki/Nicomedia"}, {"title": "Ottoman state", "link": "https://wikipedia.org/wiki/Ottoman_state"}]}, {"year": "1302", "text": "Battle of Bapheus: Decisive Ottoman victory over the Byzantines opening up Bithynia for Turkish conquest.", "html": "1302 - <a href=\"https://wikipedia.org/wiki/Battle_of_Bapheus\" title=\"Battle of Bapheus\">Battle of Bapheus</a>: Decisive <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> victory over the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantines</a> opening up <a href=\"https://wikipedia.org/wiki/Bithynia\" title=\"Bithynia\">Bithynia</a> for Turkish conquest.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Bapheus\" title=\"Battle of Bapheus\">Battle of Bapheus</a>: Decisive <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> victory over the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantines</a> opening up <a href=\"https://wikipedia.org/wiki/Bithynia\" title=\"Bithynia\">Bithynia</a> for Turkish conquest.", "links": [{"title": "Battle of Bapheus", "link": "https://wikipedia.org/wiki/Battle_of_Bapheus"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "Bithynia", "link": "https://wikipedia.org/wiki/Bithynia"}]}, {"year": "1549", "text": "The Jesuit priest <PERSON>'s ship reaches Japan.", "html": "1549 - The <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Jesuit</a> priest <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s ship reaches Japan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Jesuit</a> priest <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s ship reaches Japan.", "links": [{"title": "Society of Jesus", "link": "https://wikipedia.org/wiki/Society_of_Jesus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1663", "text": "The English Parliament passes the second Navigation Act requiring that all goods bound for the American colonies have to be sent in English ships from English ports. After the Acts of Union 1707, Scotland would be included in the Act.", "html": "1663 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">English Parliament</a> passes the <a href=\"https://wikipedia.org/wiki/Navigation_Acts#The_Navigation_Acts\" title=\"Navigation Acts\">second Navigation Act</a> requiring that all goods bound for the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">American colonies</a> have to be sent in English ships from English ports. After the <a href=\"https://wikipedia.org/wiki/Acts_of_Union_1707\" title=\"Acts of Union 1707\">Acts of Union 1707</a>, Scotland would be included in the Act.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">English Parliament</a> passes the <a href=\"https://wikipedia.org/wiki/Navigation_Acts#The_Navigation_Acts\" title=\"Navigation Acts\">second Navigation Act</a> requiring that all goods bound for the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">American colonies</a> have to be sent in English ships from English ports. After the <a href=\"https://wikipedia.org/wiki/Acts_of_Union_1707\" title=\"Acts of Union 1707\">Acts of Union 1707</a>, Scotland would be included in the Act.", "links": [{"title": "Parliament of England", "link": "https://wikipedia.org/wiki/Parliament_of_England"}, {"title": "Navigation Acts", "link": "https://wikipedia.org/wiki/Navigation_Acts#The_Navigation_Acts"}, {"title": "Thirteen Colonies", "link": "https://wikipedia.org/wiki/Thirteen_Colonies"}, {"title": "Acts of Union 1707", "link": "https://wikipedia.org/wiki/Acts_of_Union_1707"}]}, {"year": "1689", "text": "Glorious Revolution: The Battle of Killiecrankie is a victory for the Jacobites.", "html": "1689 - <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Killiecrankie\" title=\"Battle of Killiecrankie\">Battle of Killiecrankie</a> is a victory for the Jacobites.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Killiecrankie\" title=\"Battle of Killiecrankie\">Battle of Killiecrankie</a> is a victory for the Jacobites.", "links": [{"title": "Glorious Revolution", "link": "https://wikipedia.org/wiki/Glorious_Revolution"}, {"title": "Battle of Killiecrankie", "link": "https://wikipedia.org/wiki/Battle_of_Killiecrankie"}]}, {"year": "1694", "text": "A Royal charter is granted to the Bank of England.", "html": "1694 - A <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">Royal charter</a> is granted to the <a href=\"https://wikipedia.org/wiki/Bank_of_England\" title=\"Bank of England\">Bank of England</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">Royal charter</a> is granted to the <a href=\"https://wikipedia.org/wiki/Bank_of_England\" title=\"Bank of England\">Bank of England</a>.", "links": [{"title": "Royal charter", "link": "https://wikipedia.org/wiki/Royal_charter"}, {"title": "Bank of England", "link": "https://wikipedia.org/wiki/Bank_of_England"}]}, {"year": "1714", "text": "The Great Northern War: The first significant victory of the Russian Navy in the naval battle of Gangut against the Swedish Navy near the Hanko Peninsula.", "html": "1714 - The <a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>: The first significant victory of the <a href=\"https://wikipedia.org/wiki/Russian_Navy\" title=\"Russian Navy\">Russian Navy</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Gangut\" title=\"Battle of Gangut\">naval battle of Gangut</a> against the <a href=\"https://wikipedia.org/wiki/Swedish_Navy\" title=\"Swedish Navy\">Swedish Navy</a> near the <a href=\"https://wikipedia.org/wiki/Hanko_Peninsula\" title=\"Hanko Peninsula\">Hanko Peninsula</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>: The first significant victory of the <a href=\"https://wikipedia.org/wiki/Russian_Navy\" title=\"Russian Navy\">Russian Navy</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Gangut\" title=\"Battle of Gangut\">naval battle of Gangut</a> against the <a href=\"https://wikipedia.org/wiki/Swedish_Navy\" title=\"Swedish Navy\">Swedish Navy</a> near the <a href=\"https://wikipedia.org/wiki/Hanko_Peninsula\" title=\"Hanko Peninsula\">Hanko Peninsula</a>.", "links": [{"title": "Great Northern War", "link": "https://wikipedia.org/wiki/Great_Northern_War"}, {"title": "Russian Navy", "link": "https://wikipedia.org/wiki/Russian_Navy"}, {"title": "Battle of Gangut", "link": "https://wikipedia.org/wiki/Battle_of_Gangut"}, {"title": "Swedish Navy", "link": "https://wikipedia.org/wiki/Swedish_Navy"}, {"title": "Hanko Peninsula", "link": "https://wikipedia.org/wiki/Hanko_Peninsula"}]}, {"year": "1775", "text": "Founding of the U.S. Army Medical Department:  The Second Continental Congress passes legislation establishing \"an hospital for an army consisting of 20,000 men.\"", "html": "1775 - Founding of the <a href=\"https://wikipedia.org/wiki/Army_Medical_Department_(United_States)\" title=\"Army Medical Department (United States)\">U.S. Army Medical Department</a>: The <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a> passes legislation establishing \"an hospital for <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">an army</a> consisting of 20,000 men.\"", "no_year_html": "Founding of the <a href=\"https://wikipedia.org/wiki/Army_Medical_Department_(United_States)\" title=\"Army Medical Department (United States)\">U.S. Army Medical Department</a>: The <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a> passes legislation establishing \"an hospital for <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">an army</a> consisting of 20,000 men.\"", "links": [{"title": "Army Medical Department (United States)", "link": "https://wikipedia.org/wiki/Army_Medical_Department_(United_States)"}, {"title": "Second Continental Congress", "link": "https://wikipedia.org/wiki/Second_Continental_Congress"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}]}, {"year": "1778", "text": "American Revolution: First Battle of Ushant: British and French fleets fight to a standoff.", "html": "1778 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Ushant_(1778)\" title=\"Battle of Ushant (1778)\">First Battle of Ushant</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">British</a> and French fleets fight to a standoff.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Ushant_(1778)\" title=\"Battle of Ushant (1778)\">First Battle of Ushant</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">British</a> and French fleets fight to a standoff.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "Battle of Ushant (1778)", "link": "https://wikipedia.org/wiki/Battle_of_Ushant_(1778)"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}]}, {"year": "1789", "text": "The first U.S. federal government agency, the Department of Foreign Affairs, is established (it will be later renamed Department of State).", "html": "1789 - The first U.S. <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">federal government</a> agency, the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_State#History\" title=\"United States Department of State\">Department of Foreign Affairs</a>, is established (it will be later renamed <a href=\"https://wikipedia.org/wiki/United_States_Department_of_State\" title=\"United States Department of State\">Department of State</a>).", "no_year_html": "The first U.S. <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">federal government</a> agency, the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_State#History\" title=\"United States Department of State\">Department of Foreign Affairs</a>, is established (it will be later renamed <a href=\"https://wikipedia.org/wiki/United_States_Department_of_State\" title=\"United States Department of State\">Department of State</a>).", "links": [{"title": "Federal government of the United States", "link": "https://wikipedia.org/wiki/Federal_government_of_the_United_States"}, {"title": "United States Department of State", "link": "https://wikipedia.org/wiki/United_States_Department_of_State#History"}, {"title": "United States Department of State", "link": "https://wikipedia.org/wiki/United_States_Department_of_State"}]}, {"year": "1794", "text": "French Revolution: <PERSON><PERSON><PERSON> is arrested after encouraging the execution of more than 17,000 \"enemies of the Revolution\".", "html": "1794 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Maximilien_Robespierre\" title=\"Maximilien Robespierre\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Thermidorian_Reaction\" title=\"Thermidorian Reaction\">arrested</a> after encouraging the execution of more than 17,000 \"enemies of the Revolution\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Maximilien_Robespierre\" title=\"Maximilien Robespierre\">Maximilien <PERSON></a> is <a href=\"https://wikipedia.org/wiki/Thermidorian_Reaction\" title=\"Thermidorian Reaction\">arrested</a> after encouraging the execution of more than 17,000 \"enemies of the Revolution\".", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maximilien_Robespierre"}, {"title": "Thermidorian Reaction", "link": "https://wikipedia.org/wiki/Thermidorian_Reaction"}]}, {"year": "1816", "text": "Seminole Wars: The Battle of Negro Fort ends when a hot shot cannonball fired by US Navy Gunboat No. 154 explodes the fort's Powder Magazine, killing approximately 275. It is considered the deadliest single cannon shot in US history.", "html": "1816 - Seminole Wars: The <a href=\"https://wikipedia.org/wiki/Battle_of_Negro_Fort\" class=\"mw-redirect\" title=\"Battle of Negro Fort\">Battle of Negro Fort</a> ends when a hot shot cannonball fired by US Navy Gunboat No. 154 explodes the fort's Powder Magazine, killing approximately 275. It is considered the deadliest single cannon shot in US history.", "no_year_html": "Seminole Wars: The <a href=\"https://wikipedia.org/wiki/Battle_of_Negro_Fort\" class=\"mw-redirect\" title=\"Battle of Negro Fort\">Battle of Negro Fort</a> ends when a hot shot cannonball fired by US Navy Gunboat No. 154 explodes the fort's Powder Magazine, killing approximately 275. It is considered the deadliest single cannon shot in US history.", "links": [{"title": "Battle of Negro Fort", "link": "https://wikipedia.org/wiki/Battle_of_Negro_Fort"}]}, {"year": "1857", "text": "Indian Rebellion: Sixty-eight men hold out for eight days against a force of 2,500 to 3,000 mutinying sepoys and 8,000 irregular forces.", "html": "1857 - Indian Rebellion: Sixty-eight men <a href=\"https://wikipedia.org/wiki/Siege_of_Arrah\" title=\"Siege of Arrah\">hold out for eight days</a> against a force of 2,500 to 3,000 mutinying sepoys and 8,000 irregular forces.", "no_year_html": "Indian Rebellion: Sixty-eight men <a href=\"https://wikipedia.org/wiki/Siege_of_Arrah\" title=\"Siege of Arrah\">hold out for eight days</a> against a force of 2,500 to 3,000 mutinying sepoys and 8,000 irregular forces.", "links": [{"title": "Siege of Arrah", "link": "https://wikipedia.org/wiki/Siege_of_Arrah"}]}, {"year": "1865", "text": "Welsh settlers arrive at Chubut in Argentina.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Y_Wladfa\" title=\"Y Wladfa\">Welsh settlers</a> arrive at <a href=\"https://wikipedia.org/wiki/Chubut_River\" title=\"Chubut River\">Chubut</a> in <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y_Wladfa\" title=\"Y Wladfa\">Welsh settlers</a> arrive at <a href=\"https://wikipedia.org/wiki/Chubut_River\" title=\"Chubut River\">Chubut</a> in <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "links": [{"title": "Y Wladfa", "link": "https://wikipedia.org/wiki/Y_Wladfa"}, {"title": "Chubut River", "link": "https://wikipedia.org/wiki/Chubut_River"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}]}, {"year": "1866", "text": "The first permanent transatlantic telegraph cable is successfully completed, stretching from Valentia Island, Ireland, to Heart's Content, Newfoundland.", "html": "1866 - The first permanent <a href=\"https://wikipedia.org/wiki/Transatlantic_telegraph_cable\" title=\"Transatlantic telegraph cable\">transatlantic telegraph cable</a> is successfully completed, stretching from <a href=\"https://wikipedia.org/wiki/Valentia_Island\" title=\"Valentia Island\">Valentia Island</a>, Ireland, to <a href=\"https://wikipedia.org/wiki/Heart%27s_Content,_Newfoundland_and_Labrador\" title=\"Heart's Content, Newfoundland and Labrador\">Heart's Content</a>, <a href=\"https://wikipedia.org/wiki/Newfoundland_(island)\" title=\"Newfoundland (island)\">Newfoundland</a>.", "no_year_html": "The first permanent <a href=\"https://wikipedia.org/wiki/Transatlantic_telegraph_cable\" title=\"Transatlantic telegraph cable\">transatlantic telegraph cable</a> is successfully completed, stretching from <a href=\"https://wikipedia.org/wiki/Valentia_Island\" title=\"Valentia Island\">Valentia Island</a>, Ireland, to <a href=\"https://wikipedia.org/wiki/Heart%27s_Content,_Newfoundland_and_Labrador\" title=\"Heart's Content, Newfoundland and Labrador\">Heart's Content</a>, <a href=\"https://wikipedia.org/wiki/Newfoundland_(island)\" title=\"Newfoundland (island)\">Newfoundland</a>.", "links": [{"title": "Transatlantic telegraph cable", "link": "https://wikipedia.org/wiki/Transatlantic_telegraph_cable"}, {"title": "Valentia Island", "link": "https://wikipedia.org/wiki/Valentia_Island"}, {"title": "Heart's Content, Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/Heart%27s_Content,_Newfoundland_and_Labrador"}, {"title": "Newfoundland (island)", "link": "https://wikipedia.org/wiki/Newfoundland_(island)"}]}, {"year": "1880", "text": "Second Anglo-Afghan War: Battle of Maiwand: Afghan forces led by <PERSON> defeat the British Army in battle near Maiwand, Afghanistan.", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Second_Anglo-Afghan_War\" title=\"Second Anglo-Afghan War\">Second Anglo-Afghan War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Maiwand\" title=\"Battle of Maiwand\">Battle of Maiwand</a>: <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghan</a> forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Emir_of_Afghanistan)\" class=\"mw-redirect\" title=\"<PERSON> (Emir of Afghanistan)\"><PERSON></a> defeat the British Army in battle near <a href=\"https://wikipedia.org/wiki/Maiwand\" title=\"Maiwand\">Maiwand</a>, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Anglo-Afghan_War\" title=\"Second Anglo-Afghan War\">Second Anglo-Afghan War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Maiwand\" title=\"Battle of Maiwand\">Battle of Maiwand</a>: <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghan</a> forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Emir_of_Afghanistan)\" class=\"mw-redirect\" title=\"<PERSON> (Emir of Afghanistan)\"><PERSON></a> defeat the British Army in battle near <a href=\"https://wikipedia.org/wiki/Maiwand\" title=\"Maiwand\">Maiwand</a>, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "links": [{"title": "Second Anglo-Afghan War", "link": "https://wikipedia.org/wiki/Second_Anglo-Afghan_War"}, {"title": "Battle of Maiwand", "link": "https://wikipedia.org/wiki/Battle_of_Maiwand"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}, {"title": "<PERSON> (Emir of Afghanistan)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(Emir_of_Afghanistan)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>d"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "1890", "text": "<PERSON> shoots himself and dies two days later.", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> shoots himself and dies two days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> shoots himself and dies two days later.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON> makes a speech comparing Germans to Huns; for years afterwards, \"Hun\" would be a disparaging name for Germans.", "html": "1900 - Kaiser <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\"><PERSON> II</a> makes a <a href=\"https://wikipedia.org/wiki/Hun_speech\" title=\"Hun speech\">speech</a> comparing Germans to Huns; for years afterwards, \"Hun\" would be a disparaging name for Germans.", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\"><PERSON> II</a> makes a <a href=\"https://wikipedia.org/wiki/Hun_speech\" title=\"Hun speech\">speech</a> comparing Germans to Huns; for years afterwards, \"Hun\" would be a disparaging name for Germans.", "links": [{"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}, {"title": "Hun speech", "link": "https://wikipedia.org/wiki/Hun_speech"}]}, {"year": "1917", "text": "World War I: The Allies reach the Yser Canal at the Battle of Passchendaele.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allies</a> reach the <a href=\"https://wikipedia.org/wiki/Yser\" title=\"Yser\">Yser Canal</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Passchendaele\" title=\"Battle of Passchendaele\">Battle of Passchendaele</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allies</a> reach the <a href=\"https://wikipedia.org/wiki/Yser\" title=\"Yser\">Yser Canal</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Passchendaele\" title=\"Battle of Passchendaele\">Battle of Passchendaele</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yser"}, {"title": "Battle of Passchendaele", "link": "https://wikipedia.org/wiki/Battle_of_Passchendaele"}]}, {"year": "1919", "text": "The Chicago Race Riot erupts after a racial incident occurred on a South Side beach, leading to 38 fatalities and 537 injuries over a five-day period.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Chicago_race_riot_of_1919\" title=\"Chicago race riot of 1919\">Chicago Race Riot</a> erupts after a racial incident occurred on a <a href=\"https://wikipedia.org/wiki/South_Side,_Chicago\" title=\"South Side, Chicago\">South Side</a> beach, leading to 38 fatalities and 537 injuries over a five-day period.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chicago_race_riot_of_1919\" title=\"Chicago race riot of 1919\">Chicago Race Riot</a> erupts after a racial incident occurred on a <a href=\"https://wikipedia.org/wiki/South_Side,_Chicago\" title=\"South Side, Chicago\">South Side</a> beach, leading to 38 fatalities and 537 injuries over a five-day period.", "links": [{"title": "Chicago race riot of 1919", "link": "https://wikipedia.org/wiki/Chicago_race_riot_of_1919"}, {"title": "South Side, Chicago", "link": "https://wikipedia.org/wiki/South_Side,_Chicago"}]}, {"year": "1921", "text": "Researchers at the University of Toronto, led by biochemist <PERSON>, prove that the hormone insulin regulates blood sugar.", "html": "1921 - Researchers at the <a href=\"https://wikipedia.org/wiki/University_of_Toronto\" title=\"University of Toronto\">University of Toronto</a>, led by <a href=\"https://wikipedia.org/wiki/Biochemist\" title=\"Biochemist\">biochemist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, prove that the <a href=\"https://wikipedia.org/wiki/Hormone\" title=\"Hormone\">hormone</a> <a href=\"https://wikipedia.org/wiki/Insulin\" title=\"Insulin\">insulin</a> regulates <a href=\"https://wikipedia.org/wiki/Blood_sugar\" class=\"mw-redirect\" title=\"Blood sugar\">blood sugar</a>.", "no_year_html": "Researchers at the <a href=\"https://wikipedia.org/wiki/University_of_Toronto\" title=\"University of Toronto\">University of Toronto</a>, led by <a href=\"https://wikipedia.org/wiki/Biochemist\" title=\"Biochemist\">biochemist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, prove that the <a href=\"https://wikipedia.org/wiki/Hormone\" title=\"Hormone\">hormone</a> <a href=\"https://wikipedia.org/wiki/Insulin\" title=\"Insulin\">insulin</a> regulates <a href=\"https://wikipedia.org/wiki/Blood_sugar\" class=\"mw-redirect\" title=\"Blood sugar\">blood sugar</a>.", "links": [{"title": "University of Toronto", "link": "https://wikipedia.org/wiki/University_of_Toronto"}, {"title": "Biochemist", "link": "https://wikipedia.org/wiki/Biochemist"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hormone", "link": "https://wikipedia.org/wiki/Hormone"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Insulin"}, {"title": "Blood sugar", "link": "https://wikipedia.org/wiki/Blood_sugar"}]}, {"year": "1929", "text": "The Geneva Convention of 1929, dealing with treatment of prisoners-of-war, is signed by 53 nations.", "html": "1929 - The <a href=\"https://wikipedia.org/wiki/Geneva_Convention_on_Prisoners_of_War_(1929)\" class=\"mw-redirect\" title=\"Geneva Convention on Prisoners of War (1929)\">Geneva Convention of 1929</a>, dealing with treatment of prisoners-of-war, is signed by 53 nations.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Geneva_Convention_on_Prisoners_of_War_(1929)\" class=\"mw-redirect\" title=\"Geneva Convention on Prisoners of War (1929)\">Geneva Convention of 1929</a>, dealing with treatment of prisoners-of-war, is signed by 53 nations.", "links": [{"title": "Geneva Convention on Prisoners of War (1929)", "link": "https://wikipedia.org/wiki/Geneva_Convention_on_Prisoners_of_War_(1929)"}]}, {"year": "1940", "text": "The animated short A Wild Hare is released, introducing the character of <PERSON>.", "html": "1940 - The animated short <i><a href=\"https://wikipedia.org/wiki/A_Wild_Hare\" title=\"A Wild Hare\">A Wild Hare</a></i> is released, introducing the character of <a href=\"https://wikipedia.org/wiki/Bugs_Bunny\" title=\"Bugs Bunny\">Bugs Bunny</a>.", "no_year_html": "The animated short <i><a href=\"https://wikipedia.org/wiki/A_Wild_Hare\" title=\"A Wild Hare\">A Wild Hare</a></i> is released, introducing the character of <a href=\"https://wikipedia.org/wiki/Bugs_Bunny\" title=\"Bugs Bunny\">Bugs Bunny</a>.", "links": [{"title": "A Wild Hare", "link": "https://wikipedia.org/wiki/A_Wild_Hare"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bunny"}]}, {"year": "1942", "text": "World War II: Allied forces successfully halt the final Axis advance into Egypt.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces <a href=\"https://wikipedia.org/wiki/First_Battle_of_El_Alamein\" title=\"First Battle of El Alamein\">successfully halt</a> the final <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis</a> advance into Egypt.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces <a href=\"https://wikipedia.org/wiki/First_Battle_of_El_Alamein\" title=\"First Battle of El Alamein\">successfully halt</a> the final <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis</a> advance into Egypt.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "First Battle of El Alamein", "link": "https://wikipedia.org/wiki/First_Battle_of_El_Alamein"}, {"title": "Axis powers", "link": "https://wikipedia.org/wiki/Axis_powers"}]}, {"year": "1947", "text": "In Vatican City, Rome, canonization of <PERSON>, the saint whose apparitions of the Virgin <PERSON> originated the worldwide diffusion of the Miraculous Medal.", "html": "1947 - In Vatican City, Rome, <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonization</a> of <a href=\"https://wikipedia.org/wiki/Catherine_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, the saint whose apparitions of the Virgin <PERSON> originated the worldwide diffusion of the Miraculous Medal.", "no_year_html": "In Vatican City, Rome, <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonization</a> of <a href=\"https://wikipedia.org/wiki/Catherine_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, the saint whose apparitions of the <PERSON> originated the worldwide diffusion of the Miraculous Medal.", "links": [{"title": "Canonization", "link": "https://wikipedia.org/wiki/Canonization"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Catherine_Labour%C3%A9"}]}, {"year": "1949", "text": "Initial flight of the de Havilland Comet, the first jet-powered airliner.", "html": "1949 - Initial flight of the <a href=\"https://wikipedia.org/wiki/De_Havilland_Comet\" title=\"De Havilland Comet\">de Havilland Comet</a>, the first jet-powered airliner.", "no_year_html": "Initial flight of the <a href=\"https://wikipedia.org/wiki/De_Havilland_Comet\" title=\"De Havilland Comet\">de Havilland Comet</a>, the first jet-powered airliner.", "links": [{"title": "De Havilland Comet", "link": "https://wikipedia.org/wiki/De_Havilland_Comet"}]}, {"year": "1953", "text": "Cessation of hostilities is achieved in the Korean War when the United States, China, and North Korea sign an armistice agreement. <PERSON><PERSON><PERSON>, President of South Korea, refuses to sign but pledges to observe the armistice.", "html": "1953 - Cessation of hostilities is achieved in the <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a> when the United States, China, and North Korea <a href=\"https://wikipedia.org/wiki/Korean_Armistice_Agreement\" title=\"Korean Armistice Agreement\">sign an armistice agreement</a>. <a href=\"https://wikipedia.org/wiki/Syngman_Rhee\" title=\"Syngman Rhee\">Syn<PERSON>hee</a>, <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a>, refuses to sign but pledges to observe the armistice.", "no_year_html": "Cessation of hostilities is achieved in the <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a> when the United States, China, and North Korea <a href=\"https://wikipedia.org/wiki/Korean_Armistice_Agreement\" title=\"Korean Armistice Agreement\">sign an armistice agreement</a>. <a href=\"https://wikipedia.org/wiki/Syngman_Rhee\" title=\"Syngman Rhee\">Syn<PERSON>hee</a>, <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a>, refuses to sign but pledges to observe the armistice.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "Korean Armistice Agreement", "link": "https://wikipedia.org/wiki/Korean_Armistice_Agreement"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "1955", "text": "The Austrian State Treaty restores Austrian sovereignty.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Austrian_State_Treaty\" title=\"Austrian State Treaty\">Austrian State Treaty</a> restores Austrian sovereignty.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Austrian_State_Treaty\" title=\"Austrian State Treaty\">Austrian State Treaty</a> restores Austrian sovereignty.", "links": [{"title": "Austrian State Treaty", "link": "https://wikipedia.org/wiki/Austrian_State_Treaty"}]}, {"year": "1955", "text": "El Al Flight 402 is shot down by two fighter jets after straying into Bulgarian air space. All 58 people on board are killed.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/El_Al_Flight_402\" title=\"El Al Flight 402\">El Al Flight 402</a> is shot down by two fighter jets after straying into Bulgarian air space. All 58 people on board are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El_Al_Flight_402\" title=\"El Al Flight 402\">El Al Flight 402</a> is shot down by two fighter jets after straying into Bulgarian air space. All 58 people on board are killed.", "links": [{"title": "El Al Flight 402", "link": "https://wikipedia.org/wiki/El_Al_Flight_402"}]}, {"year": "1959", "text": "The Continental League is announced as baseball's \"third major league\" in the United States.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/Continental_League\" title=\"Continental League\">Continental League</a> is announced as baseball's \"third major league\" in the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Continental_League\" title=\"Continental League\">Continental League</a> is announced as baseball's \"third major league\" in the United States.", "links": [{"title": "Continental League", "link": "https://wikipedia.org/wiki/Continental_League"}]}, {"year": "1963", "text": "The Puijo observation tower is opened to the general public at Puijo Hill in Kuopio, Finland.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Puijo_tower\" title=\"Puijo tower\">Puijo observation tower</a> is opened to the general public at <a href=\"https://wikipedia.org/wiki/Puijo_Hill\" class=\"mw-redirect\" title=\"Puijo Hill\"><PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Kuopio\" title=\"Kuopio\">Kuopio, Finland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Puijo_tower\" title=\"Puijo tower\">Puijo observation tower</a> is opened to the general public at <a href=\"https://wikipedia.org/wiki/Puijo_Hill\" class=\"mw-redirect\" title=\"Puijo Hill\"><PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Kuopio\" title=\"Kuopio\">Kuopio, Finland</a>.", "links": [{"title": "Puijo tower", "link": "https://wikipedia.org/wiki/P<PERSON>jo_tower"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P<PERSON>jo_Hill"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>opio"}]}, {"year": "1964", "text": "Vietnam War: Five thousand more American military advisers are sent to South Vietnam bringing the total number of United States forces in Vietnam to 21,000.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Five thousand more American military advisers are sent to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> bringing the total number of United States forces in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> to 21,000.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Five thousand more American military advisers are sent to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> bringing the total number of United States forces in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> to 21,000.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1974", "text": "Watergate scandal: The House of Representatives Judiciary Committee votes 27 to 11 to recommend the first article of impeachment (for obstruction of justice) against President <PERSON>.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House of Representatives</a> <a href=\"https://wikipedia.org/wiki/United_States_House_Committee_on_the_Judiciary\" title=\"United States House Committee on the Judiciary\">Judiciary Committee</a> votes 27 to 11 to recommend the first article of <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a> (for obstruction of justice) against <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House of Representatives</a> <a href=\"https://wikipedia.org/wiki/United_States_House_Committee_on_the_Judiciary\" title=\"United States House Committee on the Judiciary\">Judiciary Committee</a> votes 27 to 11 to recommend the first article of <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a> (for obstruction of justice) against <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "United States House Committee on the Judiciary", "link": "https://wikipedia.org/wiki/United_States_House_Committee_on_the_Judiciary"}, {"title": "Impeachment", "link": "https://wikipedia.org/wiki/Impeachment"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "Mayor of Jaffna and former MP <PERSON> is shot dead.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Mayor_of_Jaffna\" class=\"mw-redirect\" title=\"Mayor of Jaffna\">Mayor of Jaffna</a> and former MP <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is shot dead.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mayor_of_Jaffna\" class=\"mw-redirect\" title=\"Mayor of Jaffna\">Mayor of Jaffna</a> and former MP <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is shot dead.", "links": [{"title": "Mayor of Jaffna", "link": "https://wikipedia.org/wiki/Mayor_of_Jaffna"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "While landing at Chihuahua International Airport, Aeromexico Flight 230 overshoots the runway. Thirty-two of the 66 passengers and crew on board the DC-9 are killed.", "html": "1981 - While landing at <a href=\"https://wikipedia.org/wiki/Chihuahua_International_Airport\" title=\"Chihuahua International Airport\">Chihuahua International Airport</a>, <a href=\"https://wikipedia.org/wiki/Aeromexico_Flight_230\" class=\"mw-redirect\" title=\"Aeromexico Flight 230\">Aeromexico Flight 230</a> <a href=\"https://wikipedia.org/wiki/Runway_excursion\" title=\"Runway excursion\">overshoots the runway</a>. Thirty-two of the 66 passengers and crew on board the <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\">DC-9</a> are killed.", "no_year_html": "While landing at <a href=\"https://wikipedia.org/wiki/Chihuahua_International_Airport\" title=\"Chihuahua International Airport\">Chihuahua International Airport</a>, <a href=\"https://wikipedia.org/wiki/Aeromexico_Flight_230\" class=\"mw-redirect\" title=\"Aeromexico Flight 230\">Aeromexico Flight 230</a> <a href=\"https://wikipedia.org/wiki/Runway_excursion\" title=\"Runway excursion\">overshoots the runway</a>. Thirty-two of the 66 passengers and crew on board the <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\">DC-9</a> are killed.", "links": [{"title": "Chihuahua International Airport", "link": "https://wikipedia.org/wiki/Chihuahua_International_Airport"}, {"title": "Aeromexico Flight 230", "link": "https://wikipedia.org/wiki/Aeromexico_Flight_230"}, {"title": "Runway excursion", "link": "https://wikipedia.org/wiki/Runway_excursion"}, {"title": "McDonnell Douglas DC-9", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_DC-9"}]}, {"year": "1983", "text": "Black July: Eighteen Tamil political prisoners at the Welikada high security prison in Colombo are massacred by Sinhalese prisoners, the second such massacre in two days.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Black_July\" title=\"Black July\">Black July</a>: Eighteen <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Tamil_people\" class=\"mw-redirect\" title=\"Sri Lankan Tamil people\">Tamil</a> political prisoners at the <a href=\"https://wikipedia.org/wiki/Welikada\" title=\"Welikada\">Welikada</a> <a href=\"https://wikipedia.org/wiki/Welikada_Prison\" title=\"Welikada Prison\">high security prison</a> in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a> are <a href=\"https://wikipedia.org/wiki/Welikada_prison_massacre\" title=\"Welikada prison massacre\">massacred</a> by <a href=\"https://wikipedia.org/wiki/Sinhalese_people\" title=\"Sinhalese people\">Sinhalese</a> prisoners, the second such massacre in two days.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_July\" title=\"Black July\">Black July</a>: Eighteen <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Tamil_people\" class=\"mw-redirect\" title=\"Sri Lankan Tamil people\">Tamil</a> political prisoners at the <a href=\"https://wikipedia.org/wiki/Welikada\" title=\"Welikada\">Welikada</a> <a href=\"https://wikipedia.org/wiki/Welikada_Prison\" title=\"Welikada Prison\">high security prison</a> in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a> are <a href=\"https://wikipedia.org/wiki/Welikada_prison_massacre\" title=\"Welikada prison massacre\">massacred</a> by <a href=\"https://wikipedia.org/wiki/Sinhalese_people\" title=\"Sinhalese people\">Sinhalese</a> prisoners, the second such massacre in two days.", "links": [{"title": "Black July", "link": "https://wikipedia.org/wiki/Black_July"}, {"title": "Sri Lankan Tamil people", "link": "https://wikipedia.org/wiki/Sri_Lankan_Tamil_people"}, {"title": "Welikada", "link": "https://wikipedia.org/wiki/Welikada"}, {"title": "Welikada Prison", "link": "https://wikipedia.org/wiki/Welikada_Prison"}, {"title": "Colombo", "link": "https://wikipedia.org/wiki/Colombo"}, {"title": "Welikada prison massacre", "link": "https://wikipedia.org/wiki/Welikada_prison_massacre"}, {"title": "Sinhalese people", "link": "https://wikipedia.org/wiki/Sinhalese_people"}]}, {"year": "1989", "text": "While attempting to land at Tripoli International Airport in Libya, Korean Air Flight 803 crashes just short of the runway. Seventy-five of the 199 passengers and crew and four people on the ground are killed, in the second accident involving a DC-10 in less than two weeks, the first being United Airlines Flight 232.", "html": "1989 - While attempting to land at <a href=\"https://wikipedia.org/wiki/Tripoli_International_Airport\" title=\"Tripoli International Airport\">Tripoli International Airport</a> in Libya, <a href=\"https://wikipedia.org/wiki/Korean_Air_Flight_803\" title=\"Korean Air Flight 803\">Korean Air Flight 803</a> crashes just short of the runway. Seventy-five of the 199 passengers and crew and four people on the ground are killed, in the second accident involving a <a href=\"https://wikipedia.org/wiki/DC-10\" class=\"mw-redirect\" title=\"DC-10\">DC-10</a> in less than two weeks, the first being <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_232\" title=\"United Airlines Flight 232\">United Airlines Flight 232</a>.", "no_year_html": "While attempting to land at <a href=\"https://wikipedia.org/wiki/Tripoli_International_Airport\" title=\"Tripoli International Airport\">Tripoli International Airport</a> in Libya, <a href=\"https://wikipedia.org/wiki/Korean_Air_Flight_803\" title=\"Korean Air Flight 803\">Korean Air Flight 803</a> crashes just short of the runway. Seventy-five of the 199 passengers and crew and four people on the ground are killed, in the second accident involving a <a href=\"https://wikipedia.org/wiki/DC-10\" class=\"mw-redirect\" title=\"DC-10\">DC-10</a> in less than two weeks, the first being <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_232\" title=\"United Airlines Flight 232\">United Airlines Flight 232</a>.", "links": [{"title": "Tripoli International Airport", "link": "https://wikipedia.org/wiki/Tripoli_International_Airport"}, {"title": "Korean Air Flight 803", "link": "https://wikipedia.org/wiki/Korean_Air_Flight_803"}, {"title": "DC-10", "link": "https://wikipedia.org/wiki/DC-10"}, {"title": "United Airlines Flight 232", "link": "https://wikipedia.org/wiki/United_Airlines_Flight_232"}]}, {"year": "1990", "text": "The Supreme Soviet of the Belarusian Soviet Republic declares independence of Belarus from the Soviet Union. Until 1996 the day is celebrated as the Independence Day of Belarus; after a referendum held that year the celebration of independence is moved to June 3.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/Supreme_Soviet\" title=\"Supreme Soviet\">Supreme Soviet</a> of the <a href=\"https://wikipedia.org/wiki/Byelorussian_Soviet_Socialist_Republic\" title=\"Byelorussian Soviet Socialist Republic\">Belarusian Soviet Republic</a> declares independence of <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a> from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>. Until <a href=\"https://wikipedia.org/wiki/1996\" title=\"1996\">1996</a> the day is celebrated as the Independence Day of Belarus; after a <a href=\"https://wikipedia.org/wiki/1996_Belarusian_referendum\" title=\"1996 Belarusian referendum\">referendum</a> held that year the celebration of <a href=\"https://wikipedia.org/wiki/Independence\" title=\"Independence\">independence</a> is moved to <a href=\"https://wikipedia.org/wiki/June_3\" title=\"June 3\">June 3</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Soviet\" title=\"Supreme Soviet\">Supreme Soviet</a> of the <a href=\"https://wikipedia.org/wiki/Byelorussian_Soviet_Socialist_Republic\" title=\"Byelorussian Soviet Socialist Republic\">Belarusian Soviet Republic</a> declares independence of <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a> from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>. Until <a href=\"https://wikipedia.org/wiki/1996\" title=\"1996\">1996</a> the day is celebrated as the Independence Day of Belarus; after a <a href=\"https://wikipedia.org/wiki/1996_Belarusian_referendum\" title=\"1996 Belarusian referendum\">referendum</a> held that year the celebration of <a href=\"https://wikipedia.org/wiki/Independence\" title=\"Independence\">independence</a> is moved to <a href=\"https://wikipedia.org/wiki/June_3\" title=\"June 3\">June 3</a>.", "links": [{"title": "Supreme Soviet", "link": "https://wikipedia.org/wiki/Supreme_Soviet"}, {"title": "Byelorussian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Byelorussian_Soviet_Socialist_Republic"}, {"title": "Belarus", "link": "https://wikipedia.org/wiki/Belarus"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "1996", "link": "https://wikipedia.org/wiki/1996"}, {"title": "1996 Belarusian referendum", "link": "https://wikipedia.org/wiki/1996_Belarusian_referendum"}, {"title": "Independence", "link": "https://wikipedia.org/wiki/Independence"}, {"title": "June 3", "link": "https://wikipedia.org/wiki/June_3"}]}, {"year": "1990", "text": "The Jamaat al Muslimeen attempt a coup d'état in Trinidad and Tobago.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_al_Muslimeen\" title=\"<PERSON><PERSON><PERSON> al Muslimeen\"><PERSON><PERSON><PERSON> al Muslimeen</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Muslimeen_coup_attempt\" title=\"<PERSON><PERSON><PERSON> al Muslim<PERSON> coup attempt\">attempt a <i>coup d'état</i></a> in <a href=\"https://wikipedia.org/wiki/Trinidad_and_Tobago\" title=\"Trinidad and Tobago\">Trinidad and Tobago</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_al_Muslimeen\" title=\"<PERSON><PERSON><PERSON> al Muslimeen\"><PERSON><PERSON><PERSON> al Muslimeen</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Muslimeen_coup_attempt\" title=\"<PERSON><PERSON><PERSON> al Muslim<PERSON> coup attempt\">attempt a <i>coup d'état</i></a> in <a href=\"https://wikipedia.org/wiki/Trinidad_and_Tobago\" title=\"Trinidad and Tobago\">Trinidad and Tobago</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> al <PERSON>een coup attempt", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Muslimeen_coup_attempt"}, {"title": "Trinidad and Tobago", "link": "https://wikipedia.org/wiki/Trinidad_and_Tobago"}]}, {"year": "1995", "text": "The Korean War Veterans Memorial is dedicated in Washington, D.C.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/Korean_War_Veterans_Memorial\" title=\"Korean War Veterans Memorial\">Korean War Veterans Memorial</a> is dedicated in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Korean_War_Veterans_Memorial\" title=\"Korean War Veterans Memorial\">Korean War Veterans Memorial</a> is dedicated in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>", "links": [{"title": "Korean War Veterans Memorial", "link": "https://wikipedia.org/wiki/Korean_War_Veterans_Memorial"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}]}, {"year": "1996", "text": "In Atlanta, United States, a pipe bomb explodes at Centennial Olympic Park during the 1996 Summer Olympics.", "html": "1996 - In <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, United States</a>, a <a href=\"https://wikipedia.org/wiki/Centennial_Olympic_Park_bombing\" title=\"Centennial Olympic Park bombing\">pipe bomb explodes at Centennial Olympic Park</a> during the <a href=\"https://wikipedia.org/wiki/1996_Summer_Olympics\" title=\"1996 Summer Olympics\">1996 Summer Olympics</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, United States</a>, a <a href=\"https://wikipedia.org/wiki/Centennial_Olympic_Park_bombing\" title=\"Centennial Olympic Park bombing\">pipe bomb explodes at Centennial Olympic Park</a> during the <a href=\"https://wikipedia.org/wiki/1996_Summer_Olympics\" title=\"1996 Summer Olympics\">1996 Summer Olympics</a>.", "links": [{"title": "Atlanta", "link": "https://wikipedia.org/wiki/Atlanta"}, {"title": "Centennial Olympic Park bombing", "link": "https://wikipedia.org/wiki/Centennial_Olympic_Park_bombing"}, {"title": "1996 Summer Olympics", "link": "https://wikipedia.org/wiki/1996_Summer_Olympics"}]}, {"year": "1997", "text": "About 50 people are killed in the Si Zerrouk massacre in Algeria.", "html": "1997 - About 50 people are killed in the <a href=\"https://wikipedia.org/wiki/Si_Zerrouk_massacre\" title=\"Si Zerrouk massacre\">Si <PERSON> massacre</a> in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "no_year_html": "About 50 people are killed in the <a href=\"https://wikipedia.org/wiki/Si_Zerrouk_massacre\" title=\"Si Zerrouk massacre\"><PERSON> massacre</a> in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "links": [{"title": "Si Zerrouk massacre", "link": "https://wikipedia.org/wiki/Si_Zerrouk_massacre"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}]}, {"year": "2002", "text": "Ukraine airshow disaster: A Sukhoi Su-27 fighter crashes during an air show at Lviv, Ukraine killing 77 and injuring more than 500 others, making it the deadliest air show disaster in history.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Sknyliv_air_show_disaster\" title=\"Sknyliv air show disaster\">Ukraine airshow disaster</a>: A <a href=\"https://wikipedia.org/wiki/Sukhoi_Su-27\" title=\"Sukhoi Su-27\">Sukhoi Su-27</a> fighter crashes during an air show at <a href=\"https://wikipedia.org/wiki/Lviv\" title=\"Lviv\">Lviv</a>, <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> killing 77 and injuring more than 500 others, making it the deadliest air show disaster in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sknyliv_air_show_disaster\" title=\"Sknyliv air show disaster\">Ukraine airshow disaster</a>: A <a href=\"https://wikipedia.org/wiki/Sukhoi_Su-27\" title=\"Sukhoi Su-27\">Sukhoi Su-27</a> fighter crashes during an air show at <a href=\"https://wikipedia.org/wiki/Lviv\" title=\"Lviv\">Lviv</a>, <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> killing 77 and injuring more than 500 others, making it the deadliest air show disaster in history.", "links": [{"title": "Sknyliv air show disaster", "link": "https://wikipedia.org/wiki/Sknyliv_air_show_disaster"}, {"title": "Sukhoi Su-27", "link": "https://wikipedia.org/wiki/Sukhoi_Su-27"}, {"title": "Lviv", "link": "https://wikipedia.org/wiki/Lviv"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}]}, {"year": "2005", "text": "After an incident during STS-114, NASA grounds the Space Shuttle, pending an investigation of the continuing problem with the shedding of foam insulation from the external fuel tank.", "html": "2005 - After an incident during <a href=\"https://wikipedia.org/wiki/STS-114\" title=\"STS-114\">STS-114</a>, <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> grounds the <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a>, pending an investigation of the continuing problem with the shedding of foam insulation from the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_external_tank\" title=\"Space Shuttle external tank\">external fuel tank</a>.", "no_year_html": "After an incident during <a href=\"https://wikipedia.org/wiki/STS-114\" title=\"STS-114\">STS-114</a>, <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> grounds the <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a>, pending an investigation of the continuing problem with the shedding of foam insulation from the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_external_tank\" title=\"Space Shuttle external tank\">external fuel tank</a>.", "links": [{"title": "STS-114", "link": "https://wikipedia.org/wiki/STS-114"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Space Shuttle", "link": "https://wikipedia.org/wiki/Space_Shuttle"}, {"title": "Space Shuttle external tank", "link": "https://wikipedia.org/wiki/Space_Shuttle_external_tank"}]}, {"year": "2015", "text": "At least seven people are killed and many injured after gunmen attack an Indian police station in Punjab.", "html": "2015 - At least seven people are killed and many injured after gunmen <a href=\"https://wikipedia.org/wiki/2015_Gurdaspur_attack\" title=\"2015 Gurdaspur attack\">attack</a> an Indian police station in <a href=\"https://wikipedia.org/wiki/Punjab,_India\" title=\"Punjab, India\">Punjab</a>.", "no_year_html": "At least seven people are killed and many injured after gunmen <a href=\"https://wikipedia.org/wiki/2015_Gurdaspur_attack\" title=\"2015 Gurdaspur attack\">attack</a> an Indian police station in <a href=\"https://wikipedia.org/wiki/Punjab,_India\" title=\"Punjab, India\">Punjab</a>.", "links": [{"title": "2015 Gurdaspur attack", "link": "https://wikipedia.org/wiki/2015_Gurdaspur_attack"}, {"title": "Punjab, India", "link": "https://wikipedia.org/wiki/Punjab,_India"}]}], "Births": [{"year": "774", "text": "<PERSON><PERSON><PERSON>, Japanese Buddhist monk, founder of Esoteric (Shingon) Buddhism (d. 835)", "html": "774 - <a href=\"https://wikipedia.org/wiki/K%C5%ABkai\" title=\"Kūkai\"><PERSON><PERSON><PERSON></a>, Japanese Buddhist monk, founder of <a href=\"https://wikipedia.org/wiki/Shingon\" class=\"mw-redirect\" title=\"Shingon\">Eso<PERSON>ic (Shingon) Buddhism</a> (d. 835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%ABkai\" title=\"Kūkai\"><PERSON><PERSON><PERSON></a>, Japanese Buddhist monk, founder of <a href=\"https://wikipedia.org/wiki/Shingon\" class=\"mw-redirect\" title=\"Shingon\">Esoteric (Shingon) Buddhism</a> (d. 835)", "links": [{"title": "Kūkai", "link": "https://wikipedia.org/wiki/K%C5%ABkai"}, {"title": "Shingon", "link": "https://wikipedia.org/wiki/Shingon"}]}, {"year": "1452", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian son of <PERSON> (d. 1508)", "html": "1452 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rz<PERSON>\" title=\"<PERSON> I <PERSON>forz<PERSON>\"><PERSON></a> (d. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rz<PERSON>\" title=\"<PERSON> I Sforza\"><PERSON></a> (d. 1508)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>a"}, {"title": "Francesco I Sforza", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1452", "text": "<PERSON><PERSON><PERSON>, mistress of <PERSON><PERSON><PERSON><PERSON> (d. 1508)", "html": "1452 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, mistress of <PERSON><PERSON><PERSON><PERSON> (d. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, mistress of <PERSON><PERSON><PERSON><PERSON> (d. 1508)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1502", "text": "<PERSON>, Italian composer (d. 1571)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1571)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1578", "text": "<PERSON>, Duchess of Richmond (d. 1639)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Richmond\" class=\"mw-redirect\" title=\"<PERSON>, Duchess of Richmond\"><PERSON>, Duchess of Richmond</a> (d. 1639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Richmond\" class=\"mw-redirect\" title=\"<PERSON>, Duchess of Richmond\"><PERSON>, Duchess of Richmond</a> (d. 1639)", "links": [{"title": "<PERSON>, Duchess of Richmond", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Richmond"}]}, {"year": "1612", "text": "<PERSON><PERSON>, Ottoman Sultan (d. 1640)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/Murad_IV\" title=\"Murad IV\">Murad IV</a>, <a href=\"https://wikipedia.org/wiki/Ottoman_Sultan\" class=\"mw-redirect\" title=\"Ottoman Sultan\">Ottoman Sultan</a> (d. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_IV\" title=\"Murad IV\">Murad IV</a>, <a href=\"https://wikipedia.org/wiki/Ottoman_Sultan\" class=\"mw-redirect\" title=\"Ottoman Sultan\">Ottoman Sultan</a> (d. 1640)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_IV"}, {"title": "Ottoman Sultan", "link": "https://wikipedia.org/wiki/Ottoman_Sultan"}]}, {"year": "1625", "text": "<PERSON>, 1st Earl of Sandwich (d. 1672)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Sandwich\" title=\"<PERSON>, 1st Earl of Sandwich\"><PERSON>, 1st Earl of Sandwich</a> (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Sandwich\" title=\"<PERSON>, 1st Earl of Sandwich\"><PERSON>, 1st Earl of Sandwich</a> (d. 1672)", "links": [{"title": "<PERSON>, 1st Earl of Sandwich", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Sandwich"}]}, {"year": "1667", "text": "<PERSON>, Swiss mathematician and academic (d. 1748)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and academic (d. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and academic (d. 1748)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1733", "text": "<PERSON>, English surveyor and astronomer (d. 1779)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surveyor and astronomer (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surveyor and astronomer (d. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON>, French explorer (d. 1803)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French explorer (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French explorer (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jeanne_Bar%C3%A9"}]}, {"year": "1741", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French-English violinist and composer (d. 1808)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois-Hippolyte_Barth%C3%A9l%C3%A9mon\" title=\"François-Hippolyte <PERSON>\">François-<PERSON><PERSON><PERSON></a>, French-English violinist and composer (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois-Hippolyte_Barth%C3%A9l%C3%A9mon\" title=\"François-Hippol<PERSON>\">François-<PERSON><PERSON><PERSON></a>, French-English violinist and composer (d. 1808)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois-Hippolyte_Barth%C3%A9l%C3%A9mon"}]}, {"year": "1752", "text": "<PERSON>, American general and politician (d. 1839)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Maryland_politician)\" title=\"<PERSON> (Maryland politician)\"><PERSON></a>, American general and politician (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Maryland_politician)\" title=\"<PERSON> (Maryland politician)\"><PERSON></a>, American general and politician (d. 1839)", "links": [{"title": "<PERSON> (Maryland politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Maryland_politician)"}]}, {"year": "1768", "text": "<PERSON>, French assassin of <PERSON><PERSON><PERSON> (d. 1793)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French assassin of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French assassin of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charlotte_<PERSON>y"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1768", "text": "<PERSON>, Austrian painter (d. 1839)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter (d. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON>, Norwegian economist and politician (d. 1844)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian economist and politician (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian economist and politician (d. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, Scottish-French poet and academic (d. 1844)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish-French poet and academic (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish-French poet and academic (d. 1844)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1777", "text": "<PERSON>, 21st Baron <PERSON>, English general (d. 1853)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_21st_Baron_<PERSON>\" title=\"<PERSON>, 21st Baron <PERSON>\"><PERSON>, 21st Baron <PERSON></a>, English general (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_21st_Baron_<PERSON>\" title=\"<PERSON>, 21st Baron <PERSON>\"><PERSON>, 21st Baron <PERSON></a>, English general (d. 1853)", "links": [{"title": "<PERSON>, 21st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_21st_Baron_<PERSON>"}]}, {"year": "1781", "text": "<PERSON><PERSON>, Italian singer-songwriter and guitarist (d. 1828)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (d. 1828)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1784", "text": "<PERSON>, Russian general and poet (d. 1839)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and poet (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and poet (d. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, American general and politician (d. 1897)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON><PERSON><PERSON>, Italian priest and saint (d. 1902)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian priest and saint (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian priest and saint (d. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1824", "text": "<PERSON>, fils, French novelist and playwright (d. 1895)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_fils\" class=\"mw-redirect\" title=\"<PERSON>, fils\"><PERSON>, fils</a>, French novelist and playwright (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_fils\" class=\"mw-redirect\" title=\"<PERSON>, fils\"><PERSON>, fils</a>, French novelist and playwright (d. 1895)", "links": [{"title": "<PERSON>, fils", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_fils"}]}, {"year": "1833", "text": "<PERSON>, English geologist, mountaineer, and academic (d. 1923)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist, mountaineer, and academic (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist, mountaineer, and academic (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, Peruvian admiral (d. 1879)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>nario\" class=\"mw-redirect\" title=\"<PERSON>nar<PERSON>\"><PERSON></a>, Peruvian admiral (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Seminario\" class=\"mw-redirect\" title=\"<PERSON> Seminario\"><PERSON></a>, Peruvian admiral (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Seminario"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian poet and educator, Nobel Prize laureate (d. 1907)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Giosu%C3%A<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet and educator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giosu%C3%A<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet and educator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giosu%C3%A8_<PERSON><PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian physicist and politician, Minister of Education of Hungary (d. 1919)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Lor%C3%A1nd_E%C3%B6tv%C3%B6s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian physicist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Education_(Hungary)\" title=\"Minister of Education (Hungary)\">Minister of Education of Hungary</a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lor%C3%A1nd_E%C3%B6tv%C3%B6s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian physicist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Education_(Hungary)\" title=\"Minister of Education (Hungary)\">Minister of Education of Hungary</a> (d. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lor%C3%A1nd_E%C3%B6tv%C3%B6s"}, {"title": "Minister of Education (Hungary)", "link": "https://wikipedia.org/wiki/Minister_of_Education_(Hungary)"}]}, {"year": "1848", "text": "<PERSON>, German physicist (d. 1916)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, Ukrainian journalist, author, and activist (d. 1921)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian journalist, author, and activist (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian journalist, author, and activist (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, American philanthropist (d. 1923)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, Japanese accountant and politician, 20th Prime Minister of Japan (d. 1936)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese accountant and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese accountant and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1857", "text": "<PERSON>, Puerto Rican physician, sociologist, and politician (d. 1921)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican physician, sociologist, and politician (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican physician, sociologist, and politician (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1857", "text": "<PERSON>, English Egyptologist, Orientalist, and philologist (d. 1934)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English Egyptologist, Orientalist, and philologist (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English Egyptologist, Orientalist, and philologist (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>ge"}]}, {"year": "1858", "text": "<PERSON>, Canadian golfer and cricketer (d. 1938)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Canadian golfer and cricketer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Canadian golfer and cricketer (d. 1938)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese physician and politician, 6th President of Portugal (d. 1929)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Jos%C3%A9_de_Almeida\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese physician and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Jos%C3%A9_de_Almeida\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese physician and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_Jos%C3%A9_de_Almeida"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1867", "text": "<PERSON>, Spanish pianist and composer (d. 1916)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist and composer (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist and composer (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON>, French-born British writer and historian (d. 1953)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-born British writer and historian (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-born British writer and historian (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON>, Serbian composer, conductor, and pedagogue. (d. 1942)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Dki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian composer, conductor, and pedagogue. (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Dki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian composer, conductor, and pedagogue. (d. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Dki"}]}, {"year": "1879", "text": "<PERSON>, Italian poet (d. 1927)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Italian poet (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Italian poet (d. 1927)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON>, Hungarian pianist, composer, and conductor (d. 1960)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Ern%C5%91_Dohn%C3%A1nyi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian pianist, composer, and conductor (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ern%C5%91_Dohn%C3%A1nyi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian pianist, composer, and conductor (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ern%C5%91_<PERSON><PERSON>%C3%A1nyi"}]}, {"year": "1881", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 1945)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1882", "text": "<PERSON>, English pilot and engineer, founded the de Havilland Aircraft Company (d. 1965)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot and engineer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"De Havilland\">de Havilland Aircraft Company</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot and engineer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"De Havilland\">de Havilland Aircraft Company</a> (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> Havilland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, German architect and urban planner (d. 1970)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect and urban planner (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect and urban planner (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "1889", "text": "<PERSON>, Russian ballerina, choreographer, and actress (d. 1972)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballerina, choreographer, and actress (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballerina, choreographer, and actress (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American radio engineer and inventor (d. 1976)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio engineer and inventor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio engineer and inventor (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, Finnish discus thrower and shot putter (d. 1976)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Armas_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish discus thrower and shot putter (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armas_<PERSON>e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish discus thrower and shot putter (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Armas_Taipale"}]}, {"year": "1891", "text": "<PERSON>, Dutch-Israeli veterinarian and academic (d. 1968)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Israeli veterinarian and academic (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Israeli veterinarian and academic (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Italian cyclist (d. 1941)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Ugo_Agostoni\" title=\"Ugo Agos<PERSON>i\"><PERSON><PERSON></a>, Italian cyclist (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ugo_Agostoni\" title=\"Ugo <PERSON>gos<PERSON>i\"><PERSON><PERSON></a>, Italian cyclist (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ugo_Agostoni"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Dutch actress (d. 1966)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch actress (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch actress (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Scottish air marshal and politician, 24th Governor of South Australia (d. 1967)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, Scottish air marshal and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, Scottish air marshal and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (d. 1967)", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)"}, {"title": "Governor of South Australia", "link": "https://wikipedia.org/wiki/Governor_of_South_Australia"}]}, {"year": "1896", "text": "<PERSON>, French lawyer and politician (d. 1969)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Henri<PERSON>\" title=\"Henri <PERSON>\"><PERSON></a>, French lawyer and politician (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henri<PERSON>\" title=\"Henri <PERSON>\"><PERSON></a>, French lawyer and politician (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Australian cricketer (d. 1976)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Ukrainian playwright and publicist (d. 1949)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian playwright and publicist (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian playwright and publicist (d. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Russian actor (d. 1966)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian actor (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian actor (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Greek jurist and politician, President of Greece (d. 2002)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek jurist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek jurist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Greece", "link": "https://wikipedia.org/wiki/President_of_Greece"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Latvian playwright (d. 1990)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/M%C4%81rti%C5%86%C5%A1_Z%C4%ABverts\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian playwright (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C4%81rti%C5%86%C5%A1_Z%C4%ABverts\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian playwright (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C4%81rti%C5%86%C5%A1_Z%C4%ABverts"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Soviet chess player (d. 1986)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Soviet chess player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Soviet chess player (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American baseball player and manager (d. 1991)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Polish author and activist (d. 2000)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author and activist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author and activist (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Canadian psychologist and neurologist (d. 1999)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian psychologist and neurologist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian psychologist and neurologist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American stage and film actor (d. 1937)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage and film actor (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage and film actor (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American educator and academic administrator (d. 1995)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and academic administrator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and academic administrator (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Austrian-American geodesist and mathematician (d. 2009)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American geodesist and mathematician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American geodesist and mathematician (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American journalist and author (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American journalist and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American journalist and author (d. 1996)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "1910", "text": "<PERSON>, French author and critic (d. 2007)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Mexican-American actress (d. 2016)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican-American actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican-American actress (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lupita_Tovar"}]}, {"year": "1911", "text": "<PERSON><PERSON>, English author and poet (d. 1981)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Heppenstall\"><PERSON><PERSON></a>, English author and poet (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Heppenstall\"><PERSON><PERSON></a>, English author and poet (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English bassoon player, composer, and conductor (d. 1996)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Bassoon\" title=\"Bassoon\">bassoon</a> player, composer, and conductor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Bassoon\" title=\"Bassoon\">bassoon</a> player, composer, and conductor (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bassoon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American captain, Medal of Honor recipient (d. 2000)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Street_III\" title=\"George L. Street III\"><PERSON></a>, American captain, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Street_III\" title=\"George L. Street III\"><PERSON></a>, American captain, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2000)", "links": [{"title": "<PERSON> III", "link": "https://wikipedia.org/wiki/George_<PERSON>._Street_III"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1914", "text": "<PERSON>, Estonian poet and translator (d. 1969)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/August_Sang\" title=\"August Sang\">August <PERSON></a>, Estonian poet and translator (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Sang\" title=\"August Sang\">August Sang</a>, Estonian poet and translator (d. 1969)", "links": [{"title": "August Sang", "link": "https://wikipedia.org/wiki/August_Sang"}]}, {"year": "1915", "text": "<PERSON>, Italian tenor (d. 1982)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, German colonel and pilot (d. 1961)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American literary critic, novelist, and short story  writer (d. 2007)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American literary critic, novelist, and short story writer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American literary critic, novelist, and short story writer (d. 2007)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American saxophonist and arranger (d. 1994)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist and arranger (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist and arranger (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American actor (d. 1986)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ynn\" title=\"<PERSON><PERSON> Wynn\"><PERSON><PERSON></a>, American actor (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ynn\" title=\"<PERSON><PERSON> Wynn\"><PERSON><PERSON></a>, American actor (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wynn"}]}, {"year": "1918", "text": "<PERSON>, American cellist and educator (d. 1984)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cellist and educator (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cellist and educator (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON> \"<PERSON>\" <PERSON>, American comedian and musician (d. 1971)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%22Homer%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American comedian and musician (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%22Homer%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American comedian and musician (d. 1971)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_%22Homer%22_Haynes"}]}, {"year": "1921", "text": "<PERSON>, American pilot and activist, created the World Passport (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and activist, created the <a href=\"https://wikipedia.org/wiki/World_Passport\" title=\"World Passport\">World Passport</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and activist, created the <a href=\"https://wikipedia.org/wiki/World_Passport\" title=\"World Passport\">World Passport</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "World Passport", "link": "https://wikipedia.org/wiki/World_Passport"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Canadian-American actor (d. 2003)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Genest\" title=\"É<PERSON> Genest\"><PERSON><PERSON></a>, Canadian-American actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Genest\" title=\"É<PERSON> Genest\"><PERSON><PERSON></a>, Canadian-American actor (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Genest"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Italian actor, director, and screenwriter (d. 1986)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor, director, and screenwriter (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor, director, and screenwriter (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American screenwriter and producer (d. 2023)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, South Korean-Japanese martial artist (d. 1994)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean-Japanese martial artist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>yama\"><PERSON><PERSON></a>, South Korean-Japanese martial artist (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American historian and critic (d. 2000)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and critic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and critic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Georgian composer and conductor (d. 1989)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian composer and conductor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian composer and conductor (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American singer and musicologist (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musicologist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musicologist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, French director and screenwriter (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American illustrator (d. 2004)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hunter\"><PERSON></a>, American illustrator (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American comedian and actor (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Will_Jordan"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Sri Lankan journalist and politician, 1st Mayor of Batticaloa", "html": "1927 - <a href=\"https://wikipedia.org/wiki/C._Rajadurai\" title=\"C. Rajadurai\"><PERSON><PERSON></a>, Sri Lankan journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Batticaloa\" class=\"mw-redirect\" title=\"Mayor of Batticaloa\">Mayor of Batticaloa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._Rajadurai\" title=\"C. Rajadurai\"><PERSON><PERSON></a>, Sri Lankan journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Batticaloa\" class=\"mw-redirect\" title=\"Mayor of Batticaloa\">Mayor of Batticaloa</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._Rajadurai"}, {"title": "Mayor of Batticaloa", "link": "https://wikipedia.org/wiki/Mayor_of_Batticaloa"}]}, {"year": "1927", "text": "<PERSON>, American journalist and academic (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American colonel and pilot (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, French sociologist and philosopher (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist and philosopher (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist and philosopher (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer-songwriter and producer (d. 2010)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English author and academic (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, French-Australian composer and conductor (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Australian composer and conductor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Australian composer and conductor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Whitby"}]}, {"year": "1930", "text": "<PERSON>, English academic and politician, Secretary of State for Education (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Education", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Education"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Cambodian academic and politician, 28th Prime Minister of Cambodia", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cambodian academic and politician, 28th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Cambodia\" class=\"mw-redirect\" title=\"List of Prime Ministers of Cambodia\">Prime Minister of Cambodia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cambodian academic and politician, 28th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Cambodia\" class=\"mw-redirect\" title=\"List of Prime Ministers of Cambodia\">Prime Minister of Cambodia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}, {"title": "List of Prime Ministers of Cambodia", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Cambodia"}]}, {"year": "1931", "text": "<PERSON>, American actor (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American basketball player", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Forest_Able\" title=\"Forest Able\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Forest_Able\" title=\"Forest Able\"><PERSON></a>, American basketball player", "links": [{"title": "Forest Able", "link": "https://wikipedia.org/wiki/Forest_Able"}]}, {"year": "1932", "text": "<PERSON>, American model, dancer and actress (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, dancer and actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, dancer and actress (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer and bongo player (d. 2008)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bongo player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bongo player (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Australian football player and journalist (d. 1995)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian football player and journalist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian football player and journalist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Estonian chess player (d. 2017)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Hillar_K%C3%A4rner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hillar_K%C3%A4rner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hillar_K%C3%A4rner"}]}, {"year": "1935", "text": "<PERSON>, Northern Irish footballer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American businessman and politician (d. 2008)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman and politician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman and politician (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>,  English actress and singer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actor (d. 2009)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, South African-Australian businessman and lawyer (d. 1990)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%A0_Court\" title=\"<PERSON>\"><PERSON></a>, South African-Australian businessman and lawyer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%A0_Court\" title=\"<PERSON>\"><PERSON></a>, South African-Australian businessman and lawyer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%A0_Court"}]}, {"year": "1938", "text": "<PERSON>, French comics creator and writer (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French comics creator and writer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French comics creator and writer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American game designer, co-created Dungeons & Dragons (d. 2008)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, co-created <a href=\"https://wikipedia.org/wiki/Dungeons_%26_Dragons\" title=\"Dungeons &amp; Dragons\">Dungeons &amp; Dragons</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, co-created <a href=\"https://wikipedia.org/wiki/Dungeons_%26_Dragons\" title=\"Dungeons &amp; Dragons\">Dungeons &amp; Dragons</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dungeons & Dragons", "link": "https://wikipedia.org/wiki/Dungeons_%26_Dragons"}]}, {"year": "1939", "text": "<PERSON>, American photographer and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Northern Irish poet and academic (d. 2025)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish poet and academic (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish poet and academic (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Brazilian comedian, composer and actor (d. 2017)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian comedian, composer and actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian comedian, composer and actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paulo_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, German dancer and choreographer (d. 2009)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German dancer and choreographer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German dancer and choreographer (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Austrian opera singer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian opera singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian opera singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, German viola player and composer (d. 2010)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German viola player and composer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German viola player and composer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Canadian singer-songwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/%C3%89dit<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89dit<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89dith_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gentry\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gentry\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American tennis player (d. 2020)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English diplomat, British Ambassador to the United Nations", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/Permanent_Representative_of_the_United_Kingdom_to_the_United_Nations\" title=\"Permanent Representative of the United Kingdom to the United Nations\">British Ambassador to the United Nations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/Permanent_Representative_of_the_United_Kingdom_to_the_United_Nations\" title=\"Permanent Representative of the United Kingdom to the United Nations\">British Ambassador to the United Nations</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Permanent Representative of the United Kingdom to the United Nations", "link": "https://wikipedia.org/wiki/Permanent_Representative_of_the_United_Kingdom_to_the_United_Nations"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, French cyclist and journalist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English saxophonist and composer (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English saxophonist and composer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English saxophonist and composer (d. 2022)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1946", "text": "<PERSON>, English poet and author (d. 2011)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Reading\"><PERSON></a>, English poet and author (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Peter_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Japanese businessman (d. 2008)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(businessman)\" title=\"<PERSON><PERSON><PERSON> (businessman)\"><PERSON><PERSON><PERSON></a>, Japanese businessman (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(businessman)\" title=\"<PERSON><PERSON><PERSON> (businessman)\"><PERSON><PERSON><PERSON></a>, Japanese businessman (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(businessman)"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Israeli footballer and coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gel"}]}, {"year": "1947", "text": "<PERSON>, American actress, director, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American figure skater and sportscaster", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English lawyer and judge", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Dutch singer-songwriter and bass player (d. 2022)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch singer-songwriter and bass player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch singer-songwriter and bass player (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/He<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American-Canadian actor (d. 2010)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian actor (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Scottish singer-songwriter and bass player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter and bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1949", "text": "<PERSON>, American singer and actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English author and illustrator", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Scottish lawyer and politician, Minister for Community Safety and Legal Affairs", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Community_Safety_and_Legal_Affairs\" class=\"mw-redirect\" title=\"Minister for Community Safety and Legal Affairs\">Minister for Community Safety and Legal Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Community_Safety_and_Legal_Affairs\" class=\"mw-redirect\" title=\"Minister for Community Safety and Legal Affairs\">Minister for Community Safety and Legal Affairs</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister for Community Safety and Legal Affairs", "link": "https://wikipedia.org/wiki/Minister_for_Community_Safety_and_Legal_Affairs"}]}, {"year": "1951", "text": "<PERSON>, American-English banker and businessman", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(banker)\" title=\"<PERSON> (banker)\"><PERSON></a>, American-English banker and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(banker)\" title=\"<PERSON> (banker)\"><PERSON></a>, American-English banker and businessman", "links": [{"title": "<PERSON> (banker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(banker)"}]}, {"year": "1951", "text": "<PERSON>, Dutch tennis player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}]}, {"year": "1952", "text": "<PERSON>, American basketball player (d. 2014)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, South Korean journalist and politician, 31st South Korean Minister of Unification", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean journalist and politician, 31st <a href=\"https://wikipedia.org/wiki/Ministry_of_Unification\" title=\"Ministry of Unification\">South Korean Minister of Unification</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean journalist and politician, 31st <a href=\"https://wikipedia.org/wiki/Ministry_of_Unification\" title=\"Ministry of Unification\">South Korean Minister of Unification</a>", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Ministry of Unification", "link": "https://wikipedia.org/wiki/Ministry_of_Unification"}]}, {"year": "1953", "text": "<PERSON>, Australian actor, director, producer, and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Yahoo_Serious\" title=\"Yahoo Serious\">Yahoo Serious</a>, Australian actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yahoo_Serious\" title=\"Yahoo Serious\">Yahoo Serious</a>, Australian actor, director, producer, and screenwriter", "links": [{"title": "Yahoo Serious", "link": "https://wikipedia.org/wiki/Yahoo_Serious"}]}, {"year": "1954", "text": "<PERSON>, French race car driver and sportscaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician (d. 2021)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/G._S._Bali\" title=\"G. S. Bali\">G. S. Bali</a>, Indian lawyer and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G._S._Bali\" title=\"G. S. Bali\">G. S. Bali</a>, Indian lawyer and politician (d. 2021)", "links": [{"title": "G. S. Bali", "link": "https://wikipedia.org/wiki/G._S._Bali"}]}, {"year": "1954", "text": "<PERSON>, English keyboard player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Peruvian journalist and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American journalist, author, and playwright", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian cricketer and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Allan Border\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Allan Border\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English journalist and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English journalist and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1955", "text": "<PERSON>, American drummer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress, comedian, screenwriter, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, screenwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, screenwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American comedian, actor, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English figure skater and choreographer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English figure skater and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English figure skater and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Finnish composer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American baseball player (d. 1986)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American football player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1959", "text": "<PERSON>, American journalist and author (d. 2024)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American journalist and author (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American journalist and author (d. 2024)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, French-Greek economist and politician, Greek Minister of National Defence", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Greek economist and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_National_Defence_(Greece)\" title=\"Minister for National Defence (Greece)\">Greek Minister of National Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Greek economist and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_National_Defence_(Greece)\" title=\"Minister for National Defence (Greece)\">Greek Minister of National Defence</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister for National Defence (Greece)", "link": "https://wikipedia.org/wiki/Minister_for_National_Defence_(Greece)"}]}, {"year": "1960", "text": "<PERSON>, English tennis player and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian singer-songwriter and keyboard player (d. 2018)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Conway Savage\"><PERSON></a>, Australian singer-songwriter and keyboard player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Conway Savage\"><PERSON></a>, Australian singer-songwriter and keyboard player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English lawyer and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American football coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian swimmer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American bass player (d. 2005)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rock_musician)\" class=\"mw-redirect\" title=\"<PERSON> (rock musician)\"><PERSON></a>, American bass player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rock_musician)\" class=\"mw-redirect\" title=\"<PERSON> (rock musician)\"><PERSON></a>, American bass player (d. 2005)", "links": [{"title": "<PERSON> (rock musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rock_musician)"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Chinese-Hong Kong actor, director, producer, and martial artist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Yen\" title=\"<PERSON><PERSON> Yen\"><PERSON><PERSON></a>, Chinese-Hong Kong actor, director, producer, and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Yen\" title=\"<PERSON>nie Yen\"><PERSON><PERSON></a>, Chinese-Hong Kong actor, director, producer, and martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Yen"}]}, {"year": "1964", "text": "<PERSON>, American bass player and songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Paraguayan footballer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Indian journalist, actor, director, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian journalist, actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian journalist, actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and musician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juliana_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Norwegian guitarist and composer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian guitarist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English cricketer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1967)\" title=\"<PERSON> (cricketer, born 1967)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer,_born_1967)\" title=\"<PERSON> (cricketer, born 1967)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer, born 1967)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer,_born_1967)"}]}, {"year": "1967", "text": "<PERSON>, American ice hockey player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Italian actress and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Maria_Grazia_Cuc<PERSON>\" title=\"<PERSON> Grazia Cuc<PERSON>\"><PERSON></a>, Italian actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_Grazia_Cuc<PERSON>\" title=\"<PERSON> Grazia C<PERSON>\"><PERSON></a>, Italian actress and producer", "links": [{"title": "Maria Grazia <PERSON>", "link": "https://wikipedia.org/wiki/Maria_Grazia_Cucinotta"}]}, {"year": "1968", "text": "<PERSON>, American baseball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Swedish-German engineer and academic", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-German engineer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-German engineer and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian actor and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Brazilian race car driver", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ricardo Ross<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON> <PERSON>, American wrestler and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Triple_H\" title=\"Triple H\"><PERSON> H</a>, American wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Triple_H\" title=\"Triple H\"><PERSON> H</a>, American wrestler and actor", "links": [{"title": "Triple H", "link": "https://wikipedia.org/wiki/Triple_H"}]}, {"year": "1969", "text": "<PERSON><PERSON>, South African cricketer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Danish actor and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English-Welsh politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Welsh politician)\"><PERSON></a>, English-Welsh politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Welsh politician)\"><PERSON></a>, English-Welsh politician", "links": [{"title": "<PERSON> (Welsh politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Welsh_politician)"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player, sportscaster and television host", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, sportscaster and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, sportscaster and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian Paralympic archer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Paralympic archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Paralympic archer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian kayaker", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, Australian kayaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, Australian kayaker", "links": [{"title": "<PERSON> (canoeist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)"}]}, {"year": "1972", "text": "<PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "Sheikh <PERSON><PERSON><PERSON>, Malaysian surgeon and astronaut", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Sheikh <PERSON><PERSON><PERSON><PERSON>\">Sheikh <PERSON><PERSON><PERSON><PERSON></a>, Malaysian surgeon and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Sheikh <PERSON><PERSON><PERSON><PERSON>\">Sheikh <PERSON><PERSON><PERSON><PERSON></a>, Malaysian surgeon and astronaut", "links": [{"title": "Sheikh <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American journalist and author", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Belgian long jumper", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Australian rugby league player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Tallis\"><PERSON><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Tallis\"><PERSON><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Tallis"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Hong Kong singer, actor, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hong Kong singer, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hong Kong singer, actor, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Turkish keyboard player and songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Ser<PERSON>_%C3%87elik%C3%B6z\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ser<PERSON>_%C3%87elik%C3%B6z\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish keyboard player and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Serkan_%C3%87elik%C3%B6z"}]}, {"year": "1975", "text": "<PERSON>, American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Italian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, English computer scientist and academic", "html": "1976 - <a href=\"https://wikipedia.org/wiki/De<PERSON>_<PERSON>\" title=\"De<PERSON>\"><PERSON><PERSON></a>, English computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON>_<PERSON>\" title=\"De<PERSON>\"><PERSON><PERSON></a>, English computer scientist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demis_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian cricketer (d. 2005)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 2005)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Singaporean illustrator", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Foo_<PERSON>wee_Chin\" title=\"Foo Swee Chin\"><PERSON><PERSON> <PERSON>we<PERSON></a>, Singaporean illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Foo_<PERSON>wee_Chin\" title=\"Foo Swee Chin\"><PERSON><PERSON> <PERSON>we<PERSON></a>, Singaporean illustrator", "links": [{"title": "<PERSON>oo <PERSON>wee <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oo_<PERSON>we<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, German footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B<PERSON><PERSON>_<PERSON>_(footballer_born_1977)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer born 1977)\"><PERSON><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B<PERSON><PERSON>_<PERSON>_(footballer_born_1977)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer born 1977)\"><PERSON><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (footballer born 1977)", "link": "https://wikipedia.org/wiki/Bj%C3%B6<PERSON>_<PERSON><PERSON>_(footballer_born_1977)"}]}, {"year": "1977", "text": "<PERSON>, Irish actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Irish hurler and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Diarmuid_O%27Sullivan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish hurler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diarmuid_O%27Sullivan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish hurler and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Diarmuid_O%27Sullivan"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Brazilian politician, feminist, and human rights activist (d. 2018)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian politician, feminist, and human rights activist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian politician, feminist, and human rights activist (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Mexican boxer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, French footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sidney_Go<PERSON>u"}]}, {"year": "1979", "text": "<PERSON>, American wrestler and singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian cyclist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Australian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Australian cyclist", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>(cyclist)"}]}, {"year": "1980", "text": "<PERSON>, Filipino basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, American wrestler and comedian", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ziggler\"><PERSON><PERSON><PERSON></a>, American wrestler and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ziggler\"><PERSON><PERSON><PERSON></a>, American wrestler and comedian", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Kenyan cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Collins_Obuya"}]}, {"year": "1981", "text": "<PERSON>, American painter and photographer (d. 2009)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Snow\" title=\"Dash Snow\"><PERSON></a>, American painter and photographer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dash_Snow\" title=\"Dash Snow\"><PERSON></a>, American painter and photographer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, German rugby player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English-Catalan painter, composer, and activist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Catalan painter, composer, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Catalan painter, composer, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Albanian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Dutch cyclist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Macedonian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Goran_Pandev\" title=\"Goran Pandev\"><PERSON><PERSON></a>, Macedonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Goran_Pandev\" title=\"Goran Pandev\"><PERSON><PERSON></a>, Macedonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Goran_Pandev"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Indian footballer (d. 2013)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/So<PERSON><PERSON>_<PERSON>\" title=\"So<PERSON><PERSON> Velho\"><PERSON><PERSON><PERSON></a>, Indian footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/So<PERSON><PERSON>_<PERSON>\" title=\"Socco<PERSON> Velho\"><PERSON><PERSON><PERSON></a>, Indian footballer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/So<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Norwegian politician", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor, dancer, and choreographer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Italian rugby player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, English cricketer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Slovak footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ham%C5%A1%C3%ADk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1%C3%ADk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marek_Ham%C5%A1%C3%ADk"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Hill_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/Jordan_Hill_(basketball)"}]}, {"year": "1987", "text": "<PERSON>, American ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (soccer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Venezuelan baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Pakistani actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" class=\"mw-redirect\" title=\"<PERSON> (actress)\"><PERSON></a>, Pakistani actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" class=\"mw-redirect\" title=\"<PERSON> (actress)\"><PERSON></a>, Pakistani actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1990", "text": "<PERSON>, American race car driver and actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Peruvian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paolo_<PERSON>ado"}]}, {"year": "1990", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Cheyenne_Kimball\" title=\"Cheyenne Kimball\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cheyenne_Kimball\" title=\"Cheyenne Kimball\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>ball", "link": "https://wikipedia.org/wiki/Cheyenne_Kimball"}]}, {"year": "1990", "text": "<PERSON>, Taiwanese-American figure skater", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Indian actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>_<PERSON>\" title=\"K<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Krit<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kriti_Sanon"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian singer and rapper", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Power_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1993", "text": "<PERSON>, American golfer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_<PERSON>th"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American tennis player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "903", "text": "<PERSON><PERSON><PERSON> II of Ifriqiya, Aghlabid emir", "html": "903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Ifriqiya\" title=\"<PERSON><PERSON><PERSON> II of Ifriqiya\"><PERSON><PERSON><PERSON> II of Ifriqiya</a>, Aghlabid emir", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Ifriqiya\" title=\"<PERSON><PERSON><PERSON> II of Ifriqiya\"><PERSON><PERSON><PERSON> II of Ifriqiya</a>, Aghlabid emir", "links": [{"title": "<PERSON>allah II of Ifriqiya", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Ifriqiya"}]}, {"year": "959", "text": "<PERSON><PERSON>, emperor of Later Zhou", "html": "959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Later Zhou", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Later Zhou", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>g"}]}, {"year": "1144", "text": "<PERSON><PERSON><PERSON> of Berg, High Duchess consort of Poland", "html": "1144 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Berg\" title=\"<PERSON><PERSON><PERSON> of Berg\"><PERSON><PERSON><PERSON> of Berg</a>, High Duchess consort of Poland", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Berg\" title=\"<PERSON><PERSON><PERSON> of Berg\"><PERSON><PERSON><PERSON> of Berg</a>, High Duchess consort of Poland", "links": [{"title": "Salomea of Berg", "link": "https://wikipedia.org/wiki/Salomea_of_Berg"}]}, {"year": "1061", "text": "<PERSON>, pope of the Catholic Church", "html": "1061 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_II\" title=\"Pope Nicholas II\"><PERSON> II</a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Nicholas II\"><PERSON> II</a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1101", "text": "<PERSON>, king of Italy (b. 1074)", "html": "1101 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Italy\" title=\"<PERSON> II of Italy\"><PERSON> II</a>, king of Italy (b. 1074)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Italy\" title=\"Conrad II of Italy\"><PERSON> II</a>, king of Italy (b. 1074)", "links": [{"title": "<PERSON> of Italy", "link": "https://wikipedia.org/wiki/Conrad_II_of_Italy"}]}, {"year": "1101", "text": "<PERSON>, Earl of Chester (b. c. 1047)", "html": "1101 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Avranches,_Earl_of_Chester\" title=\"<PERSON>, Earl of Chester\"><PERSON>, Earl of Chester</a> (b. c. 1047)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Avranches,_Earl_of_Chester\" title=\"<PERSON>, Earl of Chester\"><PERSON>, Earl of Chester</a> (b. c. 1047)", "links": [{"title": "<PERSON>, Earl of Chester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Avranches,_<PERSON>_of_Chester"}]}, {"year": "1158", "text": "<PERSON>, Count of Anjou (b. 1134)", "html": "1158 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Nantes\" title=\"<PERSON>, Count of Nantes\"><PERSON>, Count of Anjou</a> (b. 1134)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Nantes\" title=\"<PERSON>, Count of Nantes\"><PERSON>, Count of Anjou</a> (b. 1134)", "links": [{"title": "<PERSON>, Count of Nantes", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1276", "text": "<PERSON> of Aragon (b. 1208)", "html": "1276 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (b. 1208)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (b. 1208)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}]}, {"year": "1365", "text": "<PERSON>, Duke of Austria (b. 1339)", "html": "1365 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1339)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1339)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}]}, {"year": "1382", "text": "<PERSON> of Naples (b. 1326)", "html": "1382 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (b. 1326)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (b. 1326)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples"}]}, {"year": "1469", "text": "<PERSON>, 1st Earl of Pembroke (b. 1423)", "html": "1469 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Pembroke_(died_1469)\" title=\"<PERSON>, 1st Earl of Pembroke (died 1469)\"><PERSON>, 1st Earl of Pembroke</a> (b. 1423)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Pembroke_(died_1469)\" title=\"<PERSON>, 1st Earl of Pembroke (died 1469)\"><PERSON>, 1st Earl of Pembroke</a> (b. 1423)", "links": [{"title": "<PERSON>, 1st Earl of Pembroke (died 1469)", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Pembroke_(died_1469)"}]}, {"year": "1656", "text": "<PERSON><PERSON>, German theologian and critic (b. 1593)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German theologian and critic (b. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German theologian and critic (b. 1593)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Salomo_Glassius"}]}, {"year": "1675", "text": "<PERSON> d<PERSON>Auvergne, <PERSON><PERSON><PERSON>, French general (b. 1611)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Tour_d%27<PERSON><PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_Turenne\" class=\"mw-redirect\" title=\"<PERSON> Tour d'Auvergne, Vic<PERSON><PERSON> de Turenne\"><PERSON> d'Auvergne, <PERSON><PERSON><PERSON></a>, French general (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Tour_d%27<PERSON><PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_Turenne\" class=\"mw-redirect\" title=\"Henri <PERSON> la Tour d'Auvergne, Vic<PERSON><PERSON> de Turenne\"><PERSON> d'Auvergne, <PERSON><PERSON><PERSON></a>, French general (b. 1611)", "links": [{"title": "<PERSON> d'Auvergne, Vicomte de Turenne", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_d%27A<PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1689", "text": "<PERSON>, 1st Viscount <PERSON>, Scottish general (b. c. 1648)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Scottish general (b. c. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Scottish general (b. c. 1648)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1759", "text": "<PERSON>, French mathematician and philosopher (b. 1698)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (b. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (b. 1698)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, Scottish merchant and politician, Colonial Governor of Virginia (b. 1693)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish merchant and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Colonial Governor of Virginia</a> (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish merchant and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Colonial Governor of Virginia</a> (b. 1693)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Virginia", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia"}]}, {"year": "1841", "text": "<PERSON>, Russian poet and painter (b. 1814)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and painter (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and painter (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, English physicist, meteorologist, and chemist (b. 1776)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, meteorologist, and chemist (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, meteorologist, and chemist (b. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American journalist and politician (b. 1813)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, French painter and lithographer (b. 1791)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and lithographer (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and lithographer (b. 1791)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian composer and educator (b. 1845)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>leid\" title=\"<PERSON>eksan<PERSON> Kunileid\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian composer and educator (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>leid\" title=\"<PERSON>eksan<PERSON> Kunileid\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian composer and educator (b. 1845)", "links": [{"title": "<PERSON>ek<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleksander_Kunileid"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Dutch-born American minister and author (b. 1811)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-born American minister and author (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-born American minister and author (b. 1811)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American lieutenant and politician, 20th United States Postmaster General (b. 1813)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 20th <a href=\"https://wikipedia.org/wiki/United_States_Postmaster_General\" title=\"United States Postmaster General\">United States Postmaster General</a> (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 20th <a href=\"https://wikipedia.org/wiki/United_States_Postmaster_General\" title=\"United States Postmaster General\">United States Postmaster General</a> (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Postmaster General", "link": "https://wikipedia.org/wiki/United_States_Postmaster_General"}]}, {"year": "1916", "text": "<PERSON>, English captain (b. 1872)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English footballer (b. 1890)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Swiss physician and academic, Nobel Prize laureate (b. 1841)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Welsh writer and antiquarian scholar (b. 1836)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh writer and antiquarian scholar (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh writer and antiquarian scholar (b. 1836)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/My<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian pianist, composer, and conductor (b. 1866)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian pianist, composer, and conductor (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian pianist, composer, and conductor (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Swiss neuroanatomist and psychiatrist (b. 1848)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss neuroanatomist and psychiatrist (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss neuroanatomist and psychiatrist (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Irish seaman and explorer (b. 1877)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, Irish seaman and explorer (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, Irish seaman and explorer (b. 1877)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)"}]}, {"year": "1941", "text": "<PERSON>, New Zealand painter and educator (b. 1858)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Kee<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand painter and educator (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Kee<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand painter and educator (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Keeffe"}]}, {"year": "1942", "text": "<PERSON>, Estonian painter (b. 1902)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rsim%C3%A4gi\" title=\"<PERSON>\"><PERSON></a>, Estonian painter (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rsim%C3%A4gi\" title=\"<PERSON>\"><PERSON></a>, Estonian painter (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_P%C3%A4rsim%C3%A4gi"}]}, {"year": "1942", "text": "<PERSON>, German physician and SA general (b. 1894)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and SA general (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and SA general (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American novelist, poet, and playwright (b. 1874)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and playwright (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and playwright (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, English race car driver and businessman (b. 1898)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Woolf_Barnato\" title=\"Woolf Barnato\">Woolf <PERSON></a>, English race car driver and businessman (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Woolf_Barnato\" title=\"Woolf Barnato\">Wool<PERSON></a>, English race car driver and businessman (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Woolf_Barnato"}]}, {"year": "1948", "text": "<PERSON>, American baseball player and manager (b. 1880)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Estonian chemist and politician, 22nd Estonian Minister of Education (b. 1891)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian chemist and politician, 22nd <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_Education\" class=\"mw-redirect\" title=\"Estonian Minister of Education\">Estonian Minister of Education</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian chemist and politician, 22nd <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_Education\" class=\"mw-redirect\" title=\"Estonian Minister of Education\">Estonian Minister of Education</a> (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Estonian Minister of Education", "link": "https://wikipedia.org/wiki/Estonian_Minister_of_Education"}]}, {"year": "1958", "text": "<PERSON>, American general and pilot (b. 1893)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Danish-Swiss astronomer and academic (b. 1890)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Swiss astronomer and academic (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Swiss astronomer and academic (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English poet and author (b. 1892)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American pilot and businessman (b. 1895)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and businessman (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and businessman (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American baseball player (b. 1889)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dauss\"><PERSON><PERSON></a>, American baseball player (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>uss\"><PERSON><PERSON></a>, American baseball player (b. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American inventor (b. 1877)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American actress, writer, and director (b. 1898)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, writer, and director (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, writer, and director (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, French historian and author (b. 1901)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, French historian and author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, French historian and author (b. 1901)", "links": [{"title": "Daniel-R<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American baseball player and manager (b. 1882)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Babe_Adams\" title=\"<PERSON> Adams\"><PERSON></a>, American baseball player and manager (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Babe_Adams\" title=\"Babe Adams\"><PERSON></a>, American baseball player and manager (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese economist and politician, 100th Prime Minister of Portugal (b. 1889)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_<PERSON>_<PERSON>_Salazar\" title=\"<PERSON>t<PERSON><PERSON> <PERSON> Salazar\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Portuguese economist and politician, 100th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_<PERSON>_<PERSON>_Salazar\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON> Salazar\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Portuguese economist and politician, 100th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> (b. 1889)", "links": [{"title": "Ant<PERSON><PERSON> de Oliveira <PERSON>azar", "link": "https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_<PERSON>_<PERSON>_<PERSON>azar"}, {"title": "Prime Minister of Portugal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Portugal"}]}, {"year": "1971", "text": "<PERSON>, Irish footballer and manager (b. 1924)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Sri Lankan Tamil lawyer and politician (b. 1926)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan Tamil lawyer and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan Tamil lawyer and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Norwegian police officer (b. 1883)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian police officer (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian police officer (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, New Zealand-Australian miner and politician, 30th Premier of New South Wales (b. 1890)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian miner and politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian miner and politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1978", "text": "<PERSON>, Dutch cellist, composer, and conductor (b. 1907)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cellist, composer, and conductor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cellist, composer, and conductor (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Iranian Shah (b. 1919)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON> <PERSON> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON> <PERSON> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Egyptian actor (b. 1926)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_A<PERSON>za\" title=\"Rushdy A<PERSON>za\"><PERSON><PERSON></a>, Egyptian actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_A<PERSON>\" title=\"<PERSON><PERSON> A<PERSON>za\"><PERSON><PERSON></a>, Egyptian actor (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rushdy_A<PERSON>za"}]}, {"year": "1981", "text": "<PERSON>, American director, producer, and screenwriter (b. 1902)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Hungarian American nuclear chemist (b. 1890)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian American nuclear chemist (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian American nuclear chemist (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English actor (b. 1909)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON> <PERSON>, American baseball player and coach (b. 1889)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Smoky_<PERSON>_<PERSON>\" title=\"Smoky <PERSON>\"><PERSON><PERSON> <PERSON></a>, American baseball player and coach (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Smoky_<PERSON>_<PERSON>\" title=\"Smoky <PERSON>\"><PERSON><PERSON> <PERSON></a>, American baseball player and coach (b. 1889)", "links": [{"title": "Smoky <PERSON>", "link": "https://wikipedia.org/wiki/S<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player, coach, and manager (b. 1903)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American inventor and businessman, founded the Zamboni Company (b. 1901)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and businessman, founded the <a href=\"https://wikipedia.org/wiki/Zamboni_Company\" title=\"Zamboni Company\">Zamboni Company</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and businessman, founded the <a href=\"https://wikipedia.org/wiki/Zamboni_Company\" title=\"Zamboni Company\">Zamboni Company</a> (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Zamboni Company", "link": "https://wikipedia.org/wiki/Zamboni_Company"}]}, {"year": "1990", "text": "<PERSON>, American singer-songwriter, pianist, and producer (b. 1928)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Day\"><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Day\"><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Guadeloupean politician (b. 1912)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guadeloupean politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guadeloupean politician (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Toribio"}]}, {"year": "1991", "text": "<PERSON>, German-Australian engineer and conman (b. 1950)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fraudster)\" title=\"<PERSON> (fraudster)\"><PERSON></a>, German-Australian engineer and conman (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fraudster)\" title=\"<PERSON> (fraudster)\"><PERSON></a>, German-Australian engineer and conman (b. 1950)", "links": [{"title": "<PERSON> (fraudster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(fraudster)"}]}, {"year": "1992", "text": "<PERSON>, Australian photographer and educator (b. 1911)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian photographer and educator (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian photographer and educator (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Greek actress and screenwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actress and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1993", "text": "<PERSON>, American basketball player (b. 1965)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South African photographer and journalist (b. 1960)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African photographer and journalist (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African photographer and journalist (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Turkish politician and diplomat, 20th Turkish Minister of Foreign Affairs (b. 1915)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish politician and diplomat, 20th <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_(Turkey)\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Affairs (Turkey)\">Turkish Minister of Foreign Affairs</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish politician and diplomat, 20th <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_(Turkey)\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Affairs (Turkey)\">Turkish Minister of Foreign Affairs</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Ministers of Foreign Affairs (Turkey)", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_(Turkey)"}]}, {"year": "1995", "text": "<PERSON>, American baseball player and coach (b. 1905)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian-American composer and conductor (b. 1907)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_R%C3%B3zsa\" title=\"<PERSON><PERSON><PERSON><PERSON>z<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-American composer and conductor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_R%C3%B3zsa\" title=\"<PERSON><PERSON><PERSON><PERSON> R<PERSON>z<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-American composer and conductor (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikl%C3%B3s_R%C3%B3zsa"}]}, {"year": "1998", "text": "<PERSON><PERSON>, English-American actress (b. 1903)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actress (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actress (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Russian mathematician, physicist, and mountaineer (b. 1912)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician, physicist, and mountaineer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician, physicist, and mountaineer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American trumpet player (b. 1915)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American sportscaster (b. 1929)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Canadian wrestler (b. 1961)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian wrestler (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian wrestler (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American bass player and songwriter (b. 1952)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American lieutenant, lawyer, and politician (b. 1919)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English-American actor, comedian, television personality, and businessman (b. 1903)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, comedian, television personality, and businessman (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, comedian, television personality, and businessman (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American painter and academic (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Held\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Held"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Dutch author and illustrator (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author and illustrator (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author and illustrator (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American academic and politician (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and politician (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Nigerian-English boxer (b. 1961)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-English boxer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-English boxer (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/James_O<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Egyptian director, producer, and screenwriter (b. 1926)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian director, producer, and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian director, producer, and screenwriter (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, German-born Swiss conductor (b. 1928)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-born Swiss conductor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-born Swiss conductor (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Mexican businessman (b. 1923)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American-Canadian actor (b. 1949)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian actor (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian actor (b. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American football player (b. 1948)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American actor (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor and playwright (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor and playwright (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor and playwright (b. 1917)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian singer-songwriter, guitarist, and actor (b. 1949)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and actor (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and actor (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English actor (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1944)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "2012", "text": "<PERSON>, American actor and singer (b. 1913)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American actor and singer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American actor and singer (b. 1913)", "links": [{"title": "<PERSON> (American singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)"}]}, {"year": "2012", "text": "<PERSON>, English footballer and referee (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(referee)\" title=\"<PERSON> (referee)\"><PERSON></a>, English footballer and referee (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(referee)\" title=\"<PERSON> (referee)\"><PERSON></a>, English footballer and referee (b. 1930)", "links": [{"title": "<PERSON> (referee)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(referee)"}]}, {"year": "2013", "text": "<PERSON>, Cuban dancer, co-founded the Cuban National Ballet (b. 1914)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, Cuban dancer, co-founded the <a href=\"https://wikipedia.org/wiki/Cuban_National_Ballet\" title=\"Cuban National Ballet\">Cuban National Ballet</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, Cuban dancer, co-founded the <a href=\"https://wikipedia.org/wiki/Cuban_National_Ballet\" title=\"Cuban National Ballet\">Cuban National Ballet</a> (b. 1914)", "links": [{"title": "<PERSON> (dancer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)"}, {"title": "Cuban National Ballet", "link": "https://wikipedia.org/wiki/Cuban_National_Ballet"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American politician and diplomat, 5th United States Ambassador to the Holy See (b. 1916)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician and diplomat, 5th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Holy_See\" class=\"mw-redirect\" title=\"United States Ambassador to the Holy See\">United States Ambassador to the Holy See</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician and diplomat, 5th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Holy_See\" class=\"mw-redirect\" title=\"United States Ambassador to the Holy See\">United States Ambassador to the Holy See</a> (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to the Holy See", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_Holy_See"}]}, {"year": "2013", "text": "<PERSON>, American colonel and pilot, Medal of Honor recipient (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Bud_Day\" title=\"Bud Day\"><PERSON></a>, American colonel and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bud_Day\" title=\"Bud Day\"><PERSON></a>, American colonel and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1925)", "links": [{"title": "Bud <PERSON>", "link": "https://wikipedia.org/wiki/Bud_Day"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American radio host (b. 1959)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American radio host (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American radio host (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Russian businessman, co-founded Yandex (b. 1964)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian businessman, co-founded <a href=\"https://wikipedia.org/wiki/Yandex\" title=\"Yandex\">Yandex</a> (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian businessman, co-founded <a href=\"https://wikipedia.org/wiki/Yandex\" title=\"Yandex\">Yandex</a> (b. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Yandex", "link": "https://wikipedia.org/wiki/Yandex"}]}, {"year": "2014", "text": "<PERSON>, New Zealand air marshal and pilot (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RNZAF_officer)\" title=\"<PERSON> (RNZAF officer)\"><PERSON></a>, New Zealand air marshal and pilot (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RNZAF_officer)\" title=\"<PERSON> (RNZAF officer)\"><PERSON></a>, New Zealand air marshal and pilot (b. 1923)", "links": [{"title": "<PERSON> (RNZAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RNZAF_officer)"}]}, {"year": "2014", "text": "<PERSON>, American baseball player and coach (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American basketball player and coach (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Italian cardinal (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and politician, 50th Mayor of Seattle (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Seattle\" class=\"mw-redirect\" title=\"List of mayors of Seattle\">Mayor of Seattle</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Seattle\" class=\"mw-redirect\" title=\"List of mayors of Seattle\">Mayor of Seattle</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Seattle", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Seattle"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American singer-songwriter (b. 1959)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Indian engineer, academic, and politician, 11th President of India (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian engineer, academic, and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian engineer, academic, and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1931)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "2015", "text": "<PERSON>, Polish-born American lawyer and author (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born American lawyer and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born American lawyer and author (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English general (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (b. 1930)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish composer (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish composer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish composer (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American short story writer and essayist (b. 1943)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and essayist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and essayist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor and talk show host (b. 1956)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and talk show host (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and talk show host (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Dutch politician and naval officer, Minister of Defence), Prime Minister of the Netherlands (b. 1915)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician and naval officer, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Netherlands)\" title=\"Ministry of Defence (Netherlands)\">Minister of Defence)</a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician and naval officer, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Netherlands)\" title=\"Ministry of Defence (Netherlands)\">Minister of Defence)</a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Ministry of Defence (Netherlands)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Netherlands)"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "2017", "text": "<PERSON>, American playwright, actor, author, screenwriter, and director (b. 1943)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, actor, author, screenwriter, and director (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, actor, author, screenwriter, and director (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Peruvian literature critic, television host and sexologist", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian literature critic, television host and sexologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian literature critic, television host and sexologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American actor, film producer, director, and sculptor (b. 1945)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, film producer, director, and sculptor (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, film producer, director, and sculptor (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Irish novelist, playwright, poet and short story writer (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, playwright, poet and short story writer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, Irish novelist, playwright, poet and short story writer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edna_O%27Brien"}]}]}}