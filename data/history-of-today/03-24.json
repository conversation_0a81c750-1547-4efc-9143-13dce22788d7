{"date": "March 24", "url": "https://wikipedia.org/wiki/March_24", "data": {"Events": [{"year": "1199", "text": "King <PERSON> of England is wounded by a crossbow bolt while fighting in France, leading to his death on April 6.", "html": "1199 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> is wounded by a crossbow bolt while fighting in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>, leading to his death on <a href=\"https://wikipedia.org/wiki/April_6\" title=\"April 6\">April 6</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> is wounded by a crossbow bolt while fighting in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>, leading to his death on <a href=\"https://wikipedia.org/wiki/April_6\" title=\"April 6\">April 6</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "April 6", "link": "https://wikipedia.org/wiki/April_6"}]}, {"year": "1387", "text": "English victory over a Franco-Castilian-Flemish fleet in the Battle of Margate off the coast of Margate.", "html": "1387 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">English</a> victory over a <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\"><PERSON></a>-<a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castilian</a>-<a href=\"https://wikipedia.org/wiki/County_of_Flanders\" title=\"County of Flanders\">Flemish</a> <a href=\"https://wikipedia.org/wiki/Naval_fleet\" title=\"Naval fleet\">fleet</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Margate\" title=\"Battle of Margate\">Battle of Margate</a> off the coast of <a href=\"https://wikipedia.org/wiki/Margate\" title=\"Margate\">Margate</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">English</a> victory over a <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">Franco</a>-<a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castilian</a>-<a href=\"https://wikipedia.org/wiki/County_of_Flanders\" title=\"County of Flanders\">Flemish</a> <a href=\"https://wikipedia.org/wiki/Naval_fleet\" title=\"Naval fleet\">fleet</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Margate\" title=\"Battle of Margate\">Battle of Margate</a> off the coast of <a href=\"https://wikipedia.org/wiki/Margate\" title=\"Margate\">Margate</a>.", "links": [{"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}, {"title": "County of Flanders", "link": "https://wikipedia.org/wiki/County_of_Flanders"}, {"title": "Naval fleet", "link": "https://wikipedia.org/wiki/Naval_fleet"}, {"title": "Battle of Margate", "link": "https://wikipedia.org/wiki/Battle_of_Margate"}, {"title": "Margate", "link": "https://wikipedia.org/wiki/Margate"}]}, {"year": "1401", "text": "Turco-Mongol emperor <PERSON><PERSON> sacks Damascus.", "html": "1401 - <a href=\"https://wikipedia.org/wiki/Turco-Mongol\" class=\"mw-redirect\" title=\"Turco-Mongol\">Turco-Mongol</a> emperor <a href=\"https://wikipedia.org/wiki/Timur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> sacks <a href=\"https://wikipedia.org/wiki/Damascus\" title=\"Damascus\">Damascus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turco-Mongol\" class=\"mw-redirect\" title=\"Turco-Mongol\">Turco-Mongol</a> emperor <a href=\"https://wikipedia.org/wiki/Timur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> sacks <a href=\"https://wikipedia.org/wiki/Damascus\" title=\"Damascus\">Damascus</a>.", "links": [{"title": "Turco-Mongol", "link": "https://wikipedia.org/wiki/Turco-Mongol"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ur"}, {"title": "Damascus", "link": "https://wikipedia.org/wiki/Damascus"}]}, {"year": "1603", "text": "<PERSON> of Scotland is proclaimed King <PERSON> of England and Ireland, upon the death of <PERSON>.", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"James I of England\"><PERSON> of Scotland</a> is proclaimed King <PERSON> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ireland\" title=\"Kingdom of Ireland\">Ireland</a>, upon the death of <a href=\"https://wikipedia.org/wiki/Elizabeth_I_of_England\" class=\"mw-redirect\" title=\"<PERSON> of England\"><PERSON> I</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"James I of England\"><PERSON> of Scotland</a> is proclaimed King <PERSON> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ireland\" title=\"Kingdom of Ireland\">Ireland</a>, upon the death of <a href=\"https://wikipedia.org/wiki/Elizabeth_I_of_England\" class=\"mw-redirect\" title=\"<PERSON> of England\"><PERSON> I</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Kingdom of Ireland", "link": "https://wikipedia.org/wiki/Kingdom_of_Ireland"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1603", "text": "<PERSON> <PERSON><PERSON><PERSON><PERSON> is granted the title of shōgun from Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and establishes the Tokugawa shogunate in Edo, Japan.", "html": "1603 - <a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa Ieyasu</a> is granted the title of <i><a href=\"https://wikipedia.org/wiki/Sh%C5%8Dgun\" class=\"mw-redirect\" title=\"Shōgun\">shōgun</a></i> from <a href=\"https://wikipedia.org/wiki/Emperor_Go-Y%C5%8Dzei\" title=\"Emperor Go-Yōzei\">Emperor <PERSON><PERSON>Yōzei</a>, and establishes the <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa shogunate</a> in <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a>, Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa Ieyasu</a> is granted the title of <i><a href=\"https://wikipedia.org/wiki/Sh%C5%8Dgun\" class=\"mw-redirect\" title=\"Shōgun\">shōgun</a></i> from <a href=\"https://wikipedia.org/wiki/Emperor_Go-Y%C5%8Dzei\" title=\"Emperor Go-Yōzei\">Emperor <PERSON><PERSON>Yōzei</a>, and establishes the <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa shogunate</a> in <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a>, Japan.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>u"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sh%C5%8Dgun"}, {"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Go-Y%C5%8Dzei"}, {"title": "Tokugawa shogunate", "link": "https://wikipedia.org/wiki/Tokugawa_shogunate"}, {"title": "Edo (Tokyo)", "link": "https://wikipedia.org/wiki/Edo_(Tokyo)"}]}, {"year": "1663", "text": "The Province of Carolina is granted by charter to eight Lords Proprietor in reward for their assistance in restoring <PERSON> of England to the throne.", "html": "1663 - The <a href=\"https://wikipedia.org/wiki/Province_of_Carolina\" title=\"Province of Carolina\">Province of Carolina</a> is granted by charter to eight <a href=\"https://wikipedia.org/wiki/Lords_Proprietor\" class=\"mw-redirect\" title=\"Lords Proprietor\">Lords Proprietor</a> in reward for their assistance in <a href=\"https://wikipedia.org/wiki/Restoration_(England)\" class=\"mw-redirect\" title=\"Restoration (England)\">restoring</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> to the throne.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Province_of_Carolina\" title=\"Province of Carolina\">Province of Carolina</a> is granted by charter to eight <a href=\"https://wikipedia.org/wiki/Lords_Proprietor\" class=\"mw-redirect\" title=\"Lords Proprietor\">Lords Proprietor</a> in reward for their assistance in <a href=\"https://wikipedia.org/wiki/Restoration_(England)\" class=\"mw-redirect\" title=\"Restoration (England)\">restoring</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> to the throne.", "links": [{"title": "Province of Carolina", "link": "https://wikipedia.org/wiki/Province_of_Carolina"}, {"title": "Lords Proprietor", "link": "https://wikipedia.org/wiki/Lords_Proprietor"}, {"title": "Restoration (England)", "link": "https://wikipedia.org/wiki/Restoration_(England)"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1720", "text": "Count <PERSON> of Hesse-Kassel is elected King of Sweden by the Riksdag of the Estates, after his consort <PERSON><PERSON><PERSON> abdicated the throne on 29 February.", "html": "1720 - Count <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Hesse-Kassel</a> is elected King of <a href=\"https://wikipedia.org/wiki/Swedish_Empire\" title=\"Swedish Empire\">Sweden</a> by the <a href=\"https://wikipedia.org/wiki/Riksdag_of_the_Estates\" title=\"Riksdag of the Estates\">Riksdag of the Estates</a>, after his consort <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Queen_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Queen of Sweden\"><PERSON><PERSON><PERSON></a> abdicated the throne on 29 February.", "no_year_html": "Count <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Hesse-Kassel</a> is elected King of <a href=\"https://wikipedia.org/wiki/Swedish_Empire\" title=\"Swedish Empire\">Sweden</a> by the <a href=\"https://wikipedia.org/wiki/Riksdag_of_the_Estates\" title=\"Riksdag of the Estates\">Riksdag of the Estates</a>, after his consort <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Queen_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Queen of Sweden\"><PERSON><PERSON><PERSON></a> abdicated the throne on 29 February.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}, {"title": "Swedish Empire", "link": "https://wikipedia.org/wiki/Swedish_Empire"}, {"title": "Riksdag of the Estates", "link": "https://wikipedia.org/wiki/Riksdag_of_the_Estates"}, {"title": "<PERSON><PERSON><PERSON>, Queen of Sweden", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>,_Queen_of_Sweden"}]}, {"year": "1721", "text": "<PERSON> dedicated six concertos to <PERSON><PERSON> of Brandenburg-Schwedt, now commonly called the Brandenburg Concertos, BWV 1046-1051.", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dedicated six <a href=\"https://wikipedia.org/wiki/Concerto\" title=\"Concerto\">concertos</a> to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_of_Brandenburg-Schwedt\" title=\"Margrave <PERSON> of Brandenburg-Schwedt\">Mar<PERSON> of Brandenburg-Schwedt</a>, now commonly called the <a href=\"https://wikipedia.org/wiki/Brandenburg_Concertos\" title=\"Brandenburg Concertos\">Brandenburg Concertos</a>, BWV 1046-1051.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dedicated six <a href=\"https://wikipedia.org/wiki/Concerto\" title=\"Concerto\">concertos</a> to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_of_Brandenburg-Schwedt\" title=\"<PERSON><PERSON> of Brandenburg-Schwedt\"><PERSON><PERSON> of Brandenburg-Schwedt</a>, now commonly called the <a href=\"https://wikipedia.org/wiki/Brandenburg_Concertos\" title=\"Brandenburg Concertos\">Brandenburg Concertos</a>, BWV 1046-1051.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Concerto", "link": "https://wikipedia.org/wiki/Concerto"}, {"title": "Margrave <PERSON> of Brandenburg-Schwedt", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_of_Brandenburg-Schwedt"}, {"title": "Brandenburg Concertos", "link": "https://wikipedia.org/wiki/Brandenburg_Concertos"}]}, {"year": "1765", "text": "Great Britain passes the Quartering Act, which requires the Thirteen Colonies to house British troops.", "html": "1765 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a> passes the <a href=\"https://wikipedia.org/wiki/Quartering_Act\" class=\"mw-redirect\" title=\"Quartering Act\">Quartering Act</a>, which requires the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a> to house <a href=\"https://wikipedia.org/wiki/British_Army_during_the_American_Revolutionary_War\" title=\"British Army during the American Revolutionary War\">British troops</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a> passes the <a href=\"https://wikipedia.org/wiki/Quartering_Act\" class=\"mw-redirect\" title=\"Quartering Act\">Quartering Act</a>, which requires the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a> to house <a href=\"https://wikipedia.org/wiki/British_Army_during_the_American_Revolutionary_War\" title=\"British Army during the American Revolutionary War\">British troops</a>.", "links": [{"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Quartering Act", "link": "https://wikipedia.org/wiki/Quartering_Act"}, {"title": "Thirteen Colonies", "link": "https://wikipedia.org/wiki/Thirteen_Colonies"}, {"title": "British Army during the American Revolutionary War", "link": "https://wikipedia.org/wiki/British_Army_during_the_American_Revolutionary_War"}]}, {"year": "1794", "text": "In Kraków, <PERSON><PERSON><PERSON> announces a general uprising against Imperial Russia and the Kingdom of Prussia, and assumes the powers of the Commander in Chief of all of the Polish forces.", "html": "1794 - In <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ko%C5%9B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Ko%C5%9Bciuszko%27s_proclamation\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>'s proclamation\">announces</a> a <a href=\"https://wikipedia.org/wiki/Ko%C5%9Bciuszko_Uprising\" title=\"Kościuszko Uprising\">general uprising</a> against Imperial Russia and the Kingdom of Prussia, and assumes the powers of the <a href=\"https://wikipedia.org/wiki/Commander-in-chief\" title=\"Commander-in-chief\">Commander in Chief</a> of all of the <a href=\"https://wikipedia.org/wiki/Polish_Armed_Forces\" title=\"Polish Armed Forces\">Polish forces</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>, <a href=\"https://wikipedia.org/wiki/Tad<PERSON>z_Ko%C5%9B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>cius<PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Ko%C5%9Bciuszko%27s_proclamation\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>'s proclamation\">announces</a> a <a href=\"https://wikipedia.org/wiki/Ko%C5%9Bciuszko_Uprising\" title=\"Kościuszko Uprising\">general uprising</a> against Imperial Russia and the Kingdom of Prussia, and assumes the powers of the <a href=\"https://wikipedia.org/wiki/Commander-in-chief\" title=\"Commander-in-chief\">Commander in Chief</a> of all of the <a href=\"https://wikipedia.org/wiki/Polish_Armed_Forces\" title=\"Polish Armed Forces\">Polish forces</a>.", "links": [{"title": "Kraków", "link": "https://wikipedia.org/wiki/Krak%C3%B3w"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadeusz_Ko%C5%9B<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>'s proclamation", "link": "https://wikipedia.org/wiki/Ko%C5%9Bciuszko%27s_proclamation"}, {"title": "Kościuszko Uprising", "link": "https://wikipedia.org/wiki/Ko%C5%9Bciuszko_Uprising"}, {"title": "Commander-in-chief", "link": "https://wikipedia.org/wiki/Commander-in-chief"}, {"title": "Polish Armed Forces", "link": "https://wikipedia.org/wiki/Polish_Armed_Forces"}]}, {"year": "1829", "text": "The Parliament of the United Kingdom passes the Roman Catholic Relief Act 1829, allowing Catholics to serve in Parliament.", "html": "1829 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom\" title=\"Parliament of the United Kingdom\">Parliament of the United Kingdom</a> passes the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Relief_Act_1829\" title=\"Roman Catholic Relief Act 1829\">Roman Catholic Relief Act 1829</a>, allowing Catholics to serve in Parliament.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom\" title=\"Parliament of the United Kingdom\">Parliament of the United Kingdom</a> passes the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Relief_Act_1829\" title=\"Roman Catholic Relief Act 1829\">Roman Catholic Relief Act 1829</a>, allowing Catholics to serve in Parliament.", "links": [{"title": "Parliament of the United Kingdom", "link": "https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom"}, {"title": "Roman Catholic Relief Act 1829", "link": "https://wikipedia.org/wiki/Roman_Catholic_Relief_Act_1829"}]}, {"year": "1832", "text": "In Hiram, Ohio, a group of men beat and tar and feather Mormon leader <PERSON>.", "html": "1832 - In <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Ohio\" title=\"Hiram, Ohio\">Hiram, Ohio</a>, a group of men beat and <a href=\"https://wikipedia.org/wiki/Tar_and_feather\" class=\"mw-redirect\" title=\"Tar and feather\">tar and feather</a> <a href=\"https://wikipedia.org/wiki/Mormon\" class=\"mw-redirect\" title=\"Mormon\">Mormon</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Ohio\" title=\"Hiram, Ohio\">Hiram, Ohio</a>, a group of men beat and <a href=\"https://wikipedia.org/wiki/Tar_and_feather\" class=\"mw-redirect\" title=\"Tar and feather\">tar and feather</a> <a href=\"https://wikipedia.org/wiki/Mormon\" class=\"mw-redirect\" title=\"Mormon\">Mormon</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Hiram, Ohio", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Ohio"}, {"title": "Tar and feather", "link": "https://wikipedia.org/wiki/Tar_and_feather"}, {"title": "Mormon", "link": "https://wikipedia.org/wiki/Mormon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1854", "text": "President <PERSON> abolishes slavery in Venezuela.", "html": "1854 - <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President</a> <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> abolishes slavery in Venezuela.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President</a> <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> abolishes slavery in Venezuela.", "links": [{"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>gas"}]}, {"year": "1860", "text": "Sakuradamon Incident: Japanese Chief Minister (<PERSON><PERSON>) <PERSON><PERSON> is assassinated by r<PERSON>nin samurai outside the Sakurada Gate of Edo Castle.", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Sakuradamon_Incident_(1860)\" title=\"Sakuradamon Incident (1860)\">Sakuradamon Incident</a>: Japanese Chief Minister (<a href=\"https://wikipedia.org/wiki/Tair%C5%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>) <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Naosuke\" title=\"Ii Naosuke\"><PERSON><PERSON> Naosuke</a> is assassinated by <i><a href=\"https://wikipedia.org/wiki/R%C5%8Dnin\" title=\"Rōnin\">rōnin</a></i> <i><a href=\"https://wikipedia.org/wiki/Samurai\" title=\"Samurai\">samurai</a></i> outside the <a href=\"https://wikipedia.org/wiki/Sakurada_Gate\" title=\"Sakurada Gate\">Sakurada Gate</a> of <a href=\"https://wikipedia.org/wiki/Edo_Castle\" title=\"Edo Castle\">Edo Castle</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sakuradamon_Incident_(1860)\" title=\"Sakuradamon Incident (1860)\">Sakuradamon Incident</a>: Japanese Chief Minister (<a href=\"https://wikipedia.org/wiki/Tair%C5%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>) <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Naosuke\" title=\"Ii Naosuke\"><PERSON><PERSON> Naosuke</a> is assassinated by <i><a href=\"https://wikipedia.org/wiki/R%C5%8Dnin\" title=\"Rōnin\">rōnin</a></i> <i><a href=\"https://wikipedia.org/wiki/Samurai\" title=\"Samurai\">samurai</a></i> outside the <a href=\"https://wikipedia.org/wiki/Sakurada_Gate\" title=\"Sakurada Gate\">Sakurada Gate</a> of <a href=\"https://wikipedia.org/wiki/Edo_Castle\" title=\"Edo Castle\">Edo Castle</a>.", "links": [{"title": "Sakuradamon Incident (1860)", "link": "https://wikipedia.org/wiki/Sakuradamon_Incident_(1860)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tair%C5%8D"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C5%8Dnin"}, {"title": "Samurai", "link": "https://wikipedia.org/wiki/Samurai"}, {"title": "Sakurada Gate", "link": "https://wikipedia.org/wiki/Sakurada_Gate"}, {"title": "Edo Castle", "link": "https://wikipedia.org/wiki/Edo_Castle"}]}, {"year": "1869", "text": "The last of <PERSON><PERSON><PERSON><PERSON><PERSON>'s forces surrendered to the New Zealand government, ending his uprising.", "html": "1869 - The last of <a href=\"https://wikipedia.org/wiki/T%C4%ABtokowaru\" title=\"Tītokowaru\">Tītokowaru</a>'s forces surrendered to the <a href=\"https://wikipedia.org/wiki/Colony_of_New_Zealand\" title=\"Colony of New Zealand\">New Zealand government</a>, ending <a href=\"https://wikipedia.org/wiki/T%C4%ABtokowaru%27s_War\" title=\"Tī<PERSON><PERSON><PERSON>'s War\">his uprising</a>.", "no_year_html": "The last of <a href=\"https://wikipedia.org/wiki/T%C4%ABtokowaru\" title=\"Tītokowaru\">Tītokowa<PERSON></a>'s forces surrendered to the <a href=\"https://wikipedia.org/wiki/Colony_of_New_Zealand\" title=\"Colony of New Zealand\">New Zealand government</a>, ending <a href=\"https://wikipedia.org/wiki/T%C4%ABtokowaru%27s_War\" title=\"Tītok<PERSON><PERSON>'s War\">his uprising</a>.", "links": [{"title": "Tītokowaru", "link": "https://wikipedia.org/wiki/T%C4%ABtokowaru"}, {"title": "Colony of New Zealand", "link": "https://wikipedia.org/wiki/Colony_of_New_Zealand"}, {"title": "Tītokowaru's War", "link": "https://wikipedia.org/wiki/T%C4%ABtokowaru%27s_War"}]}, {"year": "1870", "text": "A Chilean prospecting party led by <PERSON> discovers the silver ores of Caracoles in the Bolivian portion of Atacama Desert,  leading to the last of the Chilean silver rushes  and a diplomatic dispute over its taxation between Chile and Bolivia.", "html": "1870 - A Chilean prospecting party led by <PERSON> discovers the silver ores of <a href=\"https://wikipedia.org/wiki/Caracoles\" title=\"Caracoles\">Caracoles</a> in the Bolivian portion of <a href=\"https://wikipedia.org/wiki/Atacama_Desert\" title=\"Atacama Desert\">Atacama Desert</a>, leading to the last of the <a href=\"https://wikipedia.org/wiki/Chilean_silver_rush\" title=\"Chilean silver rush\">Chilean silver rushes</a> and a diplomatic dispute over its taxation between Chile and Bolivia.", "no_year_html": "A Chilean prospecting party led by <PERSON> discovers the silver ores of <a href=\"https://wikipedia.org/wiki/Caracoles\" title=\"Caracoles\">Caracoles</a> in the Bolivian portion of <a href=\"https://wikipedia.org/wiki/Atacama_Desert\" title=\"Atacama Desert\">Atacama Desert</a>, leading to the last of the <a href=\"https://wikipedia.org/wiki/Chilean_silver_rush\" title=\"Chilean silver rush\">Chilean silver rushes</a> and a diplomatic dispute over its taxation between Chile and Bolivia.", "links": [{"title": "Caracoles", "link": "https://wikipedia.org/wiki/Caracoles"}, {"title": "Atacama Desert", "link": "https://wikipedia.org/wiki/Atacama_Desert"}, {"title": "Chilean silver rush", "link": "https://wikipedia.org/wiki/Chilean_silver_rush"}]}, {"year": "1878", "text": "The British frigate HMS Eurydice sinks, killing more than 300.", "html": "1878 - The British frigate <a href=\"https://wikipedia.org/wiki/HMS_Eurydice_(1843)\" title=\"HMS Eurydice (1843)\">HMS <i><PERSON><PERSON><PERSON></i></a> sinks, killing more than 300.", "no_year_html": "The British frigate <a href=\"https://wikipedia.org/wiki/HMS_Eurydice_(1843)\" title=\"HMS Eurydice (1843)\">HMS <i><PERSON><PERSON><PERSON></i></a> sinks, killing more than 300.", "links": [{"title": "HMS Eurydice (1843)", "link": "https://wikipedia.org/wiki/HMS_E<PERSON><PERSON>_(1843)"}]}, {"year": "1882", "text": "<PERSON> announces the discovery of Mycobacterium tuberculosis, the bacterium responsible for tuberculosis.", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the discovery of <i><a href=\"https://wikipedia.org/wiki/Mycobacterium_tuberculosis\" title=\"Mycobacterium tuberculosis\">Mycobacterium tuberculosis</a></i>, the <a href=\"https://wikipedia.org/wiki/Bacterium\" class=\"mw-redirect\" title=\"Bacterium\">bacterium</a> responsible for <a href=\"https://wikipedia.org/wiki/Tuberculosis\" title=\"Tuberculosis\">tuberculosis</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the discovery of <i><a href=\"https://wikipedia.org/wiki/Mycobacterium_tuberculosis\" title=\"Mycobacterium tuberculosis\">Mycobacterium tuberculosis</a></i>, the <a href=\"https://wikipedia.org/wiki/Bacterium\" class=\"mw-redirect\" title=\"Bacterium\">bacterium</a> responsible for <a href=\"https://wikipedia.org/wiki/Tuberculosis\" title=\"Tuberculosis\">tuberculosis</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mycobacterium tuberculosis", "link": "https://wikipedia.org/wiki/Mycobacterium_tuberculosis"}, {"title": "Bacterium", "link": "https://wikipedia.org/wiki/Bacterium"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tuberculosis"}]}, {"year": "1900", "text": "Mayor of New York City <PERSON> breaks ground for a new underground \"Rapid Transit Railroad\" that would link Manhattan and Brooklyn.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> breaks ground for a new underground \"<a href=\"https://wikipedia.org/wiki/New_York_City_Subway\" title=\"New York City Subway\">Rapid Transit Railroad</a>\" that would link <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a> and <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> breaks ground for a new underground \"<a href=\"https://wikipedia.org/wiki/New_York_City_Subway\" title=\"New York City Subway\">Rapid Transit Railroad</a>\" that would link <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a> and <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a>.", "links": [{"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "New York City Subway", "link": "https://wikipedia.org/wiki/New_York_City_Subway"}, {"title": "Manhattan", "link": "https://wikipedia.org/wiki/Manhattan"}, {"title": "Brooklyn", "link": "https://wikipedia.org/wiki/Brooklyn"}]}, {"year": "1900", "text": "Carnegie Steel Company is formed in New Jersey; its capitalization of $160 million is the largest to date.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Carnegie_Steel_Company\" title=\"Carnegie Steel Company\">Carnegie Steel Company</a> is formed in <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a>; its capitalization of $160 million is the largest to date.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carnegie_Steel_Company\" title=\"Carnegie Steel Company\">Carnegie Steel Company</a> is formed in <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a>; its capitalization of $160 million is the largest to date.", "links": [{"title": "Carnegie Steel Company", "link": "https://wikipedia.org/wiki/Carnegie_Steel_Company"}, {"title": "New Jersey", "link": "https://wikipedia.org/wiki/New_Jersey"}]}, {"year": "1921", "text": "The 1921 Women's Olympiad began in Monte Carlo, becoming the first international women's sports event.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/1921_Women%27s_Olympiad\" title=\"1921 Women's Olympiad\">1921 Women's Olympiad</a> began in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Monte Carlo\"><PERSON></a>, becoming the first international <a href=\"https://wikipedia.org/wiki/Women%27s_sports\" title=\"Women's sports\">women's sports</a> event.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1921_Women%27s_Olympiad\" title=\"1921 Women's Olympiad\">1921 Women's Olympiad</a> began in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Monte Carlo\"><PERSON></a>, becoming the first international <a href=\"https://wikipedia.org/wiki/Women%27s_sports\" title=\"Women's sports\">women's sports</a> event.", "links": [{"title": "1921 Women's Olympiad", "link": "https://wikipedia.org/wiki/1921_Women%27s_Olympiad"}, {"title": "Monte Carlo", "link": "https://wikipedia.org/wiki/Monte_Carlo"}, {"title": "Women's sports", "link": "https://wikipedia.org/wiki/Women%27s_sports"}]}, {"year": "1922", "text": "The <PERSON> killings take place in Belfast. Six Catholic civilians are shot dead, two others wounded and a female family member assaulted. Police were suspected as being responsible, but no one was prosecuted.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/McMahon_killings\" title=\"McMahon killings\">McMahon killings</a> take place in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>. Six Catholic civilians are shot dead, two others wounded and a female family member assaulted. Police were suspected as being responsible, but no one was prosecuted.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/McMahon_killings\" title=\"McMahon killings\">McMahon killings</a> take place in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>. Six Catholic civilians are shot dead, two others wounded and a female family member assaulted. Police were suspected as being responsible, but no one was prosecuted.", "links": [{"title": "McMahon killings", "link": "https://wikipedia.org/wiki/McMahon_killings"}, {"title": "Belfast", "link": "https://wikipedia.org/wiki/Belfast"}]}, {"year": "1927", "text": "Nanking Incident: Foreign warships bombard Nanjing, China, in defence of the foreign citizens within the city.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Nanking_incident_of_1927\" title=\"Nanking incident of 1927\">Nanking Incident</a>: Foreign warships bombard <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a>, <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">China</a>, in defence of the foreign citizens within the city.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nanking_incident_of_1927\" title=\"Nanking incident of 1927\">Nanking Incident</a>: Foreign warships bombard <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a>, <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">China</a>, in defence of the foreign citizens within the city.", "links": [{"title": "Nanking incident of 1927", "link": "https://wikipedia.org/wiki/Nanking_incident_of_1927"}, {"title": "Nanjing", "link": "https://wikipedia.org/wiki/Nanjing"}, {"title": "Republic of China (1912-1949)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)"}]}, {"year": "1934", "text": "The Tydings-McDuffie Act is passed by the United States Congress, allowing the Philippines to become a self-governing commonwealth.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/Tydings%E2%80%93McDuffie_Act\" title=\"Tydings-McDuffie Act\">Tydings-McDuffie Act</a> is passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>, allowing the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> to become a self-governing <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">commonwealth</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tydings%E2%80%93McDuffie_Act\" title=\"Tydings-McDuffie Act\">Tydings-McDuffie Act</a> is passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>, allowing the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> to become a self-governing <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">commonwealth</a>.", "links": [{"title": "Tydings-<PERSON>c<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tydings%E2%80%93McDuffie_Act"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "Commonwealth of the Philippines", "link": "https://wikipedia.org/wiki/Commonwealth_of_the_Philippines"}]}, {"year": "1939", "text": "The 1939 Liechtenstein putsch takes place; approximately 40 members of the VBDL starting from Nendeln march towards Vaduz with the intention of overthrowing the government and provoking Liechtenstein's annexation into Germany.", "html": "1939 - The <a href=\"https://wikipedia.org/wiki/1939_Liechtenstein_putsch\" title=\"1939 Liechtenstein putsch\">1939 Liechtenstein putsch</a> takes place; approximately 40 members of the <a href=\"https://wikipedia.org/wiki/German_National_Movement_in_Liechtenstein\" title=\"German National Movement in Liechtenstein\">VBDL</a> starting from <a href=\"https://wikipedia.org/wiki/Nendeln\" title=\"Nendeln\">Nendeln</a> march towards <a href=\"https://wikipedia.org/wiki/Vaduz\" title=\"Vaduz\">Vaduz</a> with the intention of overthrowing <a href=\"https://wikipedia.org/wiki/Third_Hoop_cabinet\" class=\"mw-redirect\" title=\"Third Hoop cabinet\">the government</a> and provoking Liechtenstein's annexation into Germany.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1939_Liechtenstein_putsch\" title=\"1939 Liechtenstein putsch\">1939 Liechtenstein putsch</a> takes place; approximately 40 members of the <a href=\"https://wikipedia.org/wiki/German_National_Movement_in_Liechtenstein\" title=\"German National Movement in Liechtenstein\">VBDL</a> starting from <a href=\"https://wikipedia.org/wiki/Nendeln\" title=\"Nendeln\">Nendeln</a> march towards <a href=\"https://wikipedia.org/wiki/Vaduz\" title=\"Vaduz\">Vaduz</a> with the intention of overthrowing <a href=\"https://wikipedia.org/wiki/Third_Hoop_cabinet\" class=\"mw-redirect\" title=\"Third Hoop cabinet\">the government</a> and provoking Liechtenstein's annexation into Germany.", "links": [{"title": "1939 Liechtenstein putsch", "link": "https://wikipedia.org/wiki/1939_Liechtenstein_putsch"}, {"title": "German National Movement in Liechtenstein", "link": "https://wikipedia.org/wiki/German_National_Movement_in_Liechtenstein"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nendeln"}, {"title": "Vaduz", "link": "https://wikipedia.org/wiki/Vaduz"}, {"title": "Third <PERSON><PERSON> cabinet", "link": "https://wikipedia.org/wiki/Third_Hoop_cabinet"}]}, {"year": "1944", "text": "German troops massacre 335 Italian civilians in Rome.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops <a href=\"https://wikipedia.org/wiki/Ardeatine_massacre\" title=\"Ardeatine massacre\">massacre 335 Italian civilians</a> in <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops <a href=\"https://wikipedia.org/wiki/Ardeatine_massacre\" title=\"Ardeatine massacre\">massacre 335 Italian civilians</a> in <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a>.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Ardeatine massacre", "link": "https://wikipedia.org/wiki/Ardeatine_massacre"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}]}, {"year": "1944", "text": "World War II: In an event later dramatized in the movie The Great Escape, 76 Allied prisoners of war begin breaking out of the German camp Stalag Luft III.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In an event later dramatized in the movie <i><a href=\"https://wikipedia.org/wiki/The_Great_Escape_(film)\" title=\"The Great Escape (film)\">The Great Escape</a></i>, 76 <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> prisoners of war begin breaking out of the <a href=\"https://wikipedia.org/wiki/German_prisoner-of-war_camps_in_World_War_II\" title=\"German prisoner-of-war camps in World War II\">German camp</a> <a href=\"https://wikipedia.org/wiki/Stalag_Luft_III\" title=\"Stalag Luft III\">Stalag Luft III</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In an event later dramatized in the movie <i><a href=\"https://wikipedia.org/wiki/The_Great_Escape_(film)\" title=\"The Great Escape (film)\">The Great Escape</a></i>, 76 <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> prisoners of war begin breaking out of the <a href=\"https://wikipedia.org/wiki/German_prisoner-of-war_camps_in_World_War_II\" title=\"German prisoner-of-war camps in World War II\">German camp</a> <a href=\"https://wikipedia.org/wiki/Stalag_Luft_III\" title=\"Stalag Luft III\">Stalag Luft III</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "The Great Escape (film)", "link": "https://wikipedia.org/wiki/The_Great_Escape_(film)"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "German prisoner-of-war camps in World War II", "link": "https://wikipedia.org/wiki/German_prisoner-of-war_camps_in_World_War_II"}, {"title": "Stalag Luft III", "link": "https://wikipedia.org/wiki/Stalag_Luft_III"}]}, {"year": "1946", "text": "A British Cabinet Mission arrives in India to discuss and plan for the transfer of power from the British Raj to Indian leadership.", "html": "1946 - A <a href=\"https://wikipedia.org/wiki/1946_Cabinet_Mission_to_India\" title=\"1946 Cabinet Mission to India\">British Cabinet Mission arrives in India</a> to discuss and plan for the transfer of power from the <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British Raj</a> to Indian leadership.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1946_Cabinet_Mission_to_India\" title=\"1946 Cabinet Mission to India\">British Cabinet Mission arrives in India</a> to discuss and plan for the transfer of power from the <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British Raj</a> to Indian leadership.", "links": [{"title": "1946 Cabinet Mission to India", "link": "https://wikipedia.org/wiki/1946_Cabinet_Mission_to_India"}, {"title": "British Raj", "link": "https://wikipedia.org/wiki/British_Raj"}]}, {"year": "1949", "text": "<PERSON><PERSON>, a chief SS and Police Leader, in the Netherlands, is convicted and executed for crimes against humanity.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Hanns <PERSON>bin Rauter\"><PERSON><PERSON></a>, a chief <a href=\"https://wikipedia.org/wiki/SS_and_Police_Leader\" class=\"mw-redirect\" title=\"SS and Police Leader\">SS and Police Leader</a>, in the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a>, is convicted and executed for crimes against humanity.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Hanns <PERSON>bin Rauter\"><PERSON><PERSON></a>, a chief <a href=\"https://wikipedia.org/wiki/SS_and_Police_Leader\" class=\"mw-redirect\" title=\"SS and Police Leader\">SS and Police Leader</a>, in the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a>, is convicted and executed for crimes against humanity.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "SS and Police Leader", "link": "https://wikipedia.org/wiki/SS_and_Police_Leader"}, {"title": "Netherlands", "link": "https://wikipedia.org/wiki/Netherlands"}]}, {"year": "1961", "text": "The Quebec Board of the French Language is established.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/Office_qu%C3%A9b%C3%A9cois_de_la_langue_fran%C3%A7aise\" title=\"Office québécois de la langue française\">Quebec Board of the French Language</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Office_qu%C3%A9b%C3%A9cois_de_la_langue_fran%C3%A7aise\" title=\"Office québécois de la langue française\">Quebec Board of the French Language</a> is established.", "links": [{"title": "Office québécois de la langue française", "link": "https://wikipedia.org/wiki/Office_qu%C3%A9b%C3%A9cois_de_la_langue_fran%C3%A7aise"}]}, {"year": "1972", "text": "Direct rule is imposed on Northern Ireland by the Government of the United Kingdom under <PERSON>.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Direct_rule_(Northern_Ireland)\" title=\"Direct rule (Northern Ireland)\">Direct rule</a> is imposed on <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> by the <a href=\"https://wikipedia.org/wiki/Government_of_the_United_Kingdom\" title=\"Government of the United Kingdom\">Government of the United Kingdom</a> under <a href=\"https://wikipedia.org/wiki/Edward_<PERSON>\" title=\"Edward <PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Direct_rule_(Northern_Ireland)\" title=\"Direct rule (Northern Ireland)\">Direct rule</a> is imposed on <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> by the <a href=\"https://wikipedia.org/wiki/Government_of_the_United_Kingdom\" title=\"Government of the United Kingdom\">Government of the United Kingdom</a> under <a href=\"https://wikipedia.org/wiki/Edward_Heath\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Direct rule (Northern Ireland)", "link": "https://wikipedia.org/wiki/Direct_rule_(Northern_Ireland)"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "Government of the United Kingdom", "link": "https://wikipedia.org/wiki/Government_of_the_United_Kingdom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "In Argentina, the armed forces overthrow the constitutional government of President <PERSON> and start a seven-year dictatorial period self-styled the National Reorganization Process.", "html": "1976 - In <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, the <a href=\"https://wikipedia.org/wiki/Argentine_Army\" title=\"Argentine Army\">armed forces</a> <a href=\"https://wikipedia.org/wiki/1976_Argentine_coup_d%27%C3%A9tat\" title=\"1976 Argentine coup d'état\">overthrow</a> the constitutional government of <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President</a> <a href=\"https://wikipedia.org/wiki/Isabel_Per%C3%B3n\" title=\"<PERSON>\"><PERSON></a> and start a seven-year dictatorial period self-styled the <i><a href=\"https://wikipedia.org/wiki/National_Reorganization_Process\" title=\"National Reorganization Process\">National Reorganization Process</a></i>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, the <a href=\"https://wikipedia.org/wiki/Argentine_Army\" title=\"Argentine Army\">armed forces</a> <a href=\"https://wikipedia.org/wiki/1976_Argentine_coup_d%27%C3%A9tat\" title=\"1976 Argentine coup d'état\">overthrow</a> the constitutional government of <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President</a> <a href=\"https://wikipedia.org/wiki/Isabel_Per%C3%B3n\" title=\"<PERSON>\"><PERSON></a> and start a seven-year dictatorial period self-styled the <i><a href=\"https://wikipedia.org/wiki/National_Reorganization_Process\" title=\"National Reorganization Process\">National Reorganization Process</a></i>.", "links": [{"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "Argentine Army", "link": "https://wikipedia.org/wiki/Argentine_Army"}, {"title": "1976 Argentine coup d'état", "link": "https://wikipedia.org/wiki/1976_Argentine_coup_d%27%C3%A9tat"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Isabel_Per%C3%B3n"}, {"title": "National Reorganization Process", "link": "https://wikipedia.org/wiki/National_Reorganization_Process"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON> became the Prime Minister of India, the first Prime Minister not to belong to Indian National Congress.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> became the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a>, the first Prime Minister not to belong to <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> became the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a>, the first Prime Minister not to belong to <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}, {"title": "Indian National Congress", "link": "https://wikipedia.org/wiki/Indian_National_Congress"}]}, {"year": "1980", "text": "El Salvadorian Archbishop <PERSON><PERSON><PERSON> is assassinated while celebrating Mass in San Salvador.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvadorian</a> <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">Archbishop</a> <a href=\"https://wikipedia.org/wiki/%C3%93<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is assassinated while celebrating <a href=\"https://wikipedia.org/wiki/Mass_(Catholic_Church)\" class=\"mw-redirect\" title=\"Mass (Catholic Church)\">Mass</a> in <a href=\"https://wikipedia.org/wiki/San_Salvador\" title=\"San Salvador\">San Salvador</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvadorian</a> <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">Archbishop</a> <a href=\"https://wikipedia.org/wiki/%C3%93<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is assassinated while celebrating <a href=\"https://wikipedia.org/wiki/Mass_(Catholic_Church)\" class=\"mw-redirect\" title=\"Mass (Catholic Church)\">Mass</a> in <a href=\"https://wikipedia.org/wiki/San_Salvador\" title=\"San Salvador\">San Salvador</a>.", "links": [{"title": "El Salvador", "link": "https://wikipedia.org/wiki/El_Salvador"}, {"title": "Archbishop", "link": "https://wikipedia.org/wiki/Archbishop"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_<PERSON>"}, {"title": "Mass (Catholic Church)", "link": "https://wikipedia.org/wiki/Mass_(Catholic_Church)"}, {"title": "San Salvador", "link": "https://wikipedia.org/wiki/San_Salvador"}]}, {"year": "1982", "text": "Bangladeshi President <PERSON><PERSON> is deposed in a bloodless coup led by Army Chief Lieutenant general <PERSON>, who suspends the Constitution and imposes martial law.", "html": "1982 - Bangladeshi President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(president)\" title=\"<PERSON><PERSON> (president)\"><PERSON><PERSON></a> is deposed in a <a href=\"https://wikipedia.org/wiki/1982_Bangladeshi_coup_d%27%C3%A9tat\" title=\"1982 Bangladeshi coup d'état\">bloodless coup</a> led by <a href=\"https://wikipedia.org/wiki/Chief_of_Army_Staff_(Bangladesh)\" title=\"Chief of Army Staff (Bangladesh)\">Army Chief</a> Lieutenant general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who suspends the <a href=\"https://wikipedia.org/wiki/Constitution_of_Bangladesh\" title=\"Constitution of Bangladesh\">Constitution</a> and imposes <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a>.", "no_year_html": "Bangladeshi President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(president)\" title=\"<PERSON><PERSON> (president)\"><PERSON><PERSON></a> is deposed in a <a href=\"https://wikipedia.org/wiki/1982_Bangladeshi_coup_d%27%C3%A9tat\" title=\"1982 Bangladeshi coup d'état\">bloodless coup</a> led by <a href=\"https://wikipedia.org/wiki/Chief_of_Army_Staff_(Bangladesh)\" title=\"Chief of Army Staff (Bangladesh)\">Army Chief</a> Lieutenant general <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who suspends the <a href=\"https://wikipedia.org/wiki/Constitution_of_Bangladesh\" title=\"Constitution of Bangladesh\">Constitution</a> and imposes <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a>.", "links": [{"title": "<PERSON><PERSON> (president)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(president)"}, {"title": "1982 Bangladeshi coup d'état", "link": "https://wikipedia.org/wiki/1982_Bangladeshi_coup_d%27%C3%A9tat"}, {"title": "Chief of Army Staff (Bangladesh)", "link": "https://wikipedia.org/wiki/Chief_of_Army_Staff_(Bangladesh)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Constitution of Bangladesh", "link": "https://wikipedia.org/wiki/Constitution_of_Bangladesh"}, {"title": "Martial law", "link": "https://wikipedia.org/wiki/Martial_law"}]}, {"year": "1986", "text": "The Loscoe gas explosion leads to new UK laws on landfill gas migration and gas protection on landfill sites.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/Loscoe_gas_explosion\" class=\"mw-redirect\" title=\"Loscoe gas explosion\">Loscoe gas explosion</a> leads to new UK laws on <a href=\"https://wikipedia.org/wiki/Landfill_gas_migration\" title=\"Landfill gas migration\">landfill gas migration</a> and gas protection on <a href=\"https://wikipedia.org/wiki/Landfill\" title=\"Landfill\">landfill</a> sites.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Loscoe_gas_explosion\" class=\"mw-redirect\" title=\"Loscoe gas explosion\">Loscoe gas explosion</a> leads to new UK laws on <a href=\"https://wikipedia.org/wiki/Landfill_gas_migration\" title=\"Landfill gas migration\">landfill gas migration</a> and gas protection on <a href=\"https://wikipedia.org/wiki/Landfill\" title=\"Landfill\">landfill</a> sites.", "links": [{"title": "Loscoe gas explosion", "link": "https://wikipedia.org/wiki/Loscoe_gas_explosion"}, {"title": "Landfill gas migration", "link": "https://wikipedia.org/wiki/Landfill_gas_migration"}, {"title": "Landfill", "link": "https://wikipedia.org/wiki/Landfill"}]}, {"year": "1989", "text": "In Prince William Sound in Alaska, the Exxon Valdez spills 240,000 barrels (38,000 m3) of crude oil after running aground.", "html": "1989 - In <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_Sound\" title=\"Prince William Sound\">Prince <PERSON></a> in Alaska, the <i><a href=\"https://wikipedia.org/wiki/Exxon_Valdez_oil_spill\" title=\"Exxon Valdez oil spill\">Exxon Valdez</a></i> spills 240,000 barrels (38,000 m) of <a href=\"https://wikipedia.org/wiki/Crude_oil\" class=\"mw-redirect\" title=\"Crude oil\">crude oil</a> after running aground.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_Sound\" title=\"Prince William Sound\">Prince <PERSON> Sound</a> in Alaska, the <i><a href=\"https://wikipedia.org/wiki/Exxon_Valdez_oil_spill\" title=\"Exxon Valdez oil spill\">Exxon Valdez</a></i> spills 240,000 barrels (38,000 m) of <a href=\"https://wikipedia.org/wiki/Crude_oil\" class=\"mw-redirect\" title=\"Crude oil\">crude oil</a> after running aground.", "links": [{"title": "Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Exxon Valdez oil spill", "link": "https://wikipedia.org/wiki/Exxon_Valdez_oil_spill"}, {"title": "Crude oil", "link": "https://wikipedia.org/wiki/Crude_oil"}]}, {"year": "1990", "text": "Indian intervention in the Sri Lankan Civil War ends with last ship of Indian Peace Keeping Force leaving Sri Lanka.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Indian_intervention_in_the_Sri_Lankan_civil_war\" title=\"Indian intervention in the Sri Lankan civil war\">Indian intervention in the Sri Lankan Civil War</a> ends with last ship of <a href=\"https://wikipedia.org/wiki/Indian_Peace_Keeping_Force\" title=\"Indian Peace Keeping Force\">Indian Peace Keeping Force</a> leaving <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indian_intervention_in_the_Sri_Lankan_civil_war\" title=\"Indian intervention in the Sri Lankan civil war\">Indian intervention in the Sri Lankan Civil War</a> ends with last ship of <a href=\"https://wikipedia.org/wiki/Indian_Peace_Keeping_Force\" title=\"Indian Peace Keeping Force\">Indian Peace Keeping Force</a> leaving <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>.", "links": [{"title": "Indian intervention in the Sri Lankan civil war", "link": "https://wikipedia.org/wiki/Indian_intervention_in_the_Sri_Lankan_civil_war"}, {"title": "Indian Peace Keeping Force", "link": "https://wikipedia.org/wiki/Indian_Peace_Keeping_Force"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}]}, {"year": "1992", "text": "Space Shuttle Atlantis launches on STS-45.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-45\" title=\"STS-45\">STS-45</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-45\" title=\"STS-45\">STS-45</a>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-45", "link": "https://wikipedia.org/wiki/STS-45"}]}, {"year": "1993", "text": "Comet Shoemaker-Levy 9 is discovered by <PERSON> and <PERSON>, and <PERSON> at the Palomar Observatory in California.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Comet_Shoemaker%E2%80%93Levy_9\" title=\"Comet Shoemaker-Levy 9\">Comet Shoemaker-Levy 9</a> is discovered by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hoemaker\" title=\"<PERSON>hoemaker\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Palomar_Observatory\" title=\"Palomar Observatory\">Palomar Observatory</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Comet_Shoemaker%E2%80%93Levy_9\" title=\"Comet Shoemaker-Levy 9\">Comet Shoemaker-Levy 9</a> is discovered by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Shoemaker\" title=\"<PERSON> Shoemaker\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Palomar_Observatory\" title=\"Palomar Observatory\">Palomar Observatory</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "links": [{"title": "Comet Shoemaker-Levy 9", "link": "https://wikipedia.org/wiki/Comet_Shoemaker%E2%80%93Levy_9"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Palomar Observatory", "link": "https://wikipedia.org/wiki/Palomar_Observatory"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}]}, {"year": "1998", "text": "<PERSON> and <PERSON>, aged 11 and 13 respectively, open fire upon teachers and students at Westside Middle School in Jonesboro, Arkansas; five people are killed and ten are wounded.", "html": "1998 - <PERSON> and <PERSON>, aged 11 and 13 respectively, <a href=\"https://wikipedia.org/wiki/1998_Westside_Middle_School_shooting\" title=\"1998 Westside Middle School shooting\">open fire</a> upon teachers and students at Westside Middle School in <a href=\"https://wikipedia.org/wiki/Jonesboro,_Arkansas\" title=\"Jonesboro, Arkansas\">Jonesboro, Arkansas</a>; five people are killed and ten are wounded.", "no_year_html": "<PERSON> and <PERSON>, aged 11 and 13 respectively, <a href=\"https://wikipedia.org/wiki/1998_Westside_Middle_School_shooting\" title=\"1998 Westside Middle School shooting\">open fire</a> upon teachers and students at Westside Middle School in <a href=\"https://wikipedia.org/wiki/Jonesboro,_Arkansas\" title=\"Jonesboro, Arkansas\">Jonesboro, Arkansas</a>; five people are killed and ten are wounded.", "links": [{"title": "1998 Westside Middle School shooting", "link": "https://wikipedia.org/wiki/1998_Westside_Middle_School_shooting"}, {"title": "Jonesboro, Arkansas", "link": "https://wikipedia.org/wiki/Jonesboro,_Arkansas"}]}, {"year": "1998", "text": "A tornado sweeps through Dantan in India, killing 250 people and injuring 3,000 others.", "html": "1998 - A <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> sweeps through <a href=\"https://wikipedia.org/wiki/Dantan\" title=\"Dantan\">Dantan</a> in India, killing 250 people and injuring 3,000 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> sweeps through <a href=\"https://wikipedia.org/wiki/Dantan\" title=\"Dantan\">Dantan</a> in India, killing 250 people and injuring 3,000 others.", "links": [{"title": "Tornado", "link": "https://wikipedia.org/wiki/Tornado"}, {"title": "Dan<PERSON>", "link": "https://wikipedia.org/wiki/Dantan"}]}, {"year": "1998", "text": "Dr. <PERSON><PERSON><PERSON><PERSON> performed the first computer-assisted Bone Segment Navigation at the University of Regensburg, Germany.", "html": "1998 - Dr. <a href=\"https://wikipedia.org/wiki/R%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> performed the first <a href=\"https://wikipedia.org/wiki/Automation\" title=\"Automation\">computer-assisted</a> <a href=\"https://wikipedia.org/wiki/Bone_segment_navigation\" title=\"Bone segment navigation\">Bone Segment Navigation</a> at the <a href=\"https://wikipedia.org/wiki/University_of_Regensburg\" title=\"University of Regensburg\">University of Regensburg</a>, <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Germany</a>.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/R%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> performed the first <a href=\"https://wikipedia.org/wiki/Automation\" title=\"Automation\">computer-assisted</a> <a href=\"https://wikipedia.org/wiki/Bone_segment_navigation\" title=\"Bone segment navigation\">Bone Segment Navigation</a> at the <a href=\"https://wikipedia.org/wiki/University_of_Regensburg\" title=\"University of Regensburg\">University of Regensburg</a>, <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Germany</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Automation", "link": "https://wikipedia.org/wiki/Automation"}, {"title": "Bone segment navigation", "link": "https://wikipedia.org/wiki/Bone_segment_navigation"}, {"title": "University of Regensburg", "link": "https://wikipedia.org/wiki/University_of_Regensburg"}, {"title": "Germany", "link": "https://wikipedia.org/wiki/Germany"}]}, {"year": "1999", "text": "Kosovo War: NATO began attacks on Yugoslavia without United Nations Security Council (UNSC) approval, marking the first time NATO has attacked a sovereign country.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Kosovo_War\" title=\"Kosovo War\">Kosovo War</a>: <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> began attacks on <a href=\"https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Federal Republic of Yugoslavia\">Yugoslavia</a> without <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council (UNSC)</a> approval, marking the first time NATO has attacked a sovereign country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kosovo_War\" title=\"Kosovo War\">Kosovo War</a>: <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> began attacks on <a href=\"https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Federal Republic of Yugoslavia\">Yugoslavia</a> without <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council (UNSC)</a> approval, marking the first time NATO has attacked a sovereign country.", "links": [{"title": "Kosovo War", "link": "https://wikipedia.org/wiki/Kosovo_War"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}, {"title": "Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia"}, {"title": "United Nations Security Council", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council"}]}, {"year": "1999", "text": "A lorry carrying margarine and flour catches fire inside the Mont Blanc Tunnel, creating an inferno that kills 38 people.", "html": "1999 - A lorry carrying margarine and flour <a href=\"https://wikipedia.org/wiki/Mont_Blanc_Tunnel_fire\" title=\"Mont Blanc Tunnel fire\">catches fire</a> inside the <a href=\"https://wikipedia.org/wiki/Mont_Blanc_Tunnel\" title=\"Mont Blanc Tunnel\">Mont Blanc Tunnel</a>, creating an inferno that kills 38 people.", "no_year_html": "A lorry carrying margarine and flour <a href=\"https://wikipedia.org/wiki/Mont_Blanc_Tunnel_fire\" title=\"Mont Blanc Tunnel fire\">catches fire</a> inside the <a href=\"https://wikipedia.org/wiki/Mont_Blanc_Tunnel\" title=\"Mont Blanc Tunnel\">Mont Blanc Tunnel</a>, creating an inferno that kills 38 people.", "links": [{"title": "Mont Blanc Tunnel fire", "link": "https://wikipedia.org/wiki/Mont_Blanc_Tunnel_fire"}, {"title": "Mont Blanc Tunnel", "link": "https://wikipedia.org/wiki/Mont_Blanc_Tunnel"}]}, {"year": "2003", "text": "The Arab League votes 21-1 in favor of a resolution demanding an end to the 2003 invasion of Iraq.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Arab_League\" title=\"Arab League\">Arab League</a> votes 21-1 in favor of a resolution demanding an end to the <a href=\"https://wikipedia.org/wiki/2003_invasion_of_Iraq\" title=\"2003 invasion of Iraq\">2003 invasion of Iraq</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arab_League\" title=\"Arab League\">Arab League</a> votes 21-1 in favor of a resolution demanding an end to the <a href=\"https://wikipedia.org/wiki/2003_invasion_of_Iraq\" title=\"2003 invasion of Iraq\">2003 invasion of Iraq</a>.", "links": [{"title": "Arab League", "link": "https://wikipedia.org/wiki/Arab_League"}, {"title": "2003 invasion of Iraq", "link": "https://wikipedia.org/wiki/2003_invasion_of_Iraq"}]}, {"year": "2008", "text": "Bhutan officially becomes a democracy, with its first ever general election.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Bhutan\" title=\"Bhutan\">Bhutan</a> officially becomes a <a href=\"https://wikipedia.org/wiki/Democracy\" title=\"Democracy\">democracy</a>, with its <a href=\"https://wikipedia.org/wiki/2008_Bhutanese_National_Assembly_election\" title=\"2008 Bhutanese National Assembly election\">first ever general election</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bhutan\" title=\"Bhutan\">Bhutan</a> officially becomes a <a href=\"https://wikipedia.org/wiki/Democracy\" title=\"Democracy\">democracy</a>, with its <a href=\"https://wikipedia.org/wiki/2008_Bhutanese_National_Assembly_election\" title=\"2008 Bhutanese National Assembly election\">first ever general election</a>.", "links": [{"title": "Bhutan", "link": "https://wikipedia.org/wiki/Bhutan"}, {"title": "Democracy", "link": "https://wikipedia.org/wiki/Democracy"}, {"title": "2008 Bhutanese National Assembly election", "link": "https://wikipedia.org/wiki/2008_Bhutanese_National_Assembly_election"}]}, {"year": "2015", "text": "Germanwings Flight 9525 crashes in the French Alps in an apparent pilot mass murder-suicide, killing all 150 people on board.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Germanwings_Flight_9525\" title=\"Germanwings Flight 9525\">Germanwings Flight 9525</a> crashes in the <a href=\"https://wikipedia.org/wiki/French_Alps\" title=\"French Alps\">French Alps</a> in an apparent <a href=\"https://wikipedia.org/wiki/Suicide_by_pilot\" title=\"Suicide by pilot\">pilot mass murder-suicide</a>, killing all 150 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Germanwings_Flight_9525\" title=\"Germanwings Flight 9525\">Germanwings Flight 9525</a> crashes in the <a href=\"https://wikipedia.org/wiki/French_Alps\" title=\"French Alps\">French Alps</a> in an apparent <a href=\"https://wikipedia.org/wiki/Suicide_by_pilot\" title=\"Suicide by pilot\">pilot mass murder-suicide</a>, killing all 150 people on board.", "links": [{"title": "Germanwings Flight 9525", "link": "https://wikipedia.org/wiki/Germanwings_Flight_9525"}, {"title": "French Alps", "link": "https://wikipedia.org/wiki/French_Alps"}, {"title": "Suicide by pilot", "link": "https://wikipedia.org/wiki/Suicide_by_pilot"}]}, {"year": "2018", "text": "Syrian civil war: The Turkish Armed Forces (TAF) and Syrian National Army (SNA) take full control of Afrin District, marking the end of the Afrin offensive.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Turkish_Armed_Forces\" title=\"Turkish Armed Forces\">Turkish Armed Forces</a> (TAF) and <a href=\"https://wikipedia.org/wiki/Syrian_National_Army\" title=\"Syrian National Army\">Syrian National Army</a> (SNA) take full control of <a href=\"https://wikipedia.org/wiki/Afrin_District\" title=\"Afrin District\">Afrin District</a>, marking the end of the <a href=\"https://wikipedia.org/wiki/Afrin_offensive_(January%E2%80%93March_2018)\" class=\"mw-redirect\" title=\"Afrin offensive (January-March 2018)\">Afrin offensive</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Turkish_Armed_Forces\" title=\"Turkish Armed Forces\">Turkish Armed Forces</a> (TAF) and <a href=\"https://wikipedia.org/wiki/Syrian_National_Army\" title=\"Syrian National Army\">Syrian National Army</a> (SNA) take full control of <a href=\"https://wikipedia.org/wiki/Afrin_District\" title=\"Afrin District\">Afrin District</a>, marking the end of the <a href=\"https://wikipedia.org/wiki/Afrin_offensive_(January%E2%80%93March_2018)\" class=\"mw-redirect\" title=\"Afrin offensive (January-March 2018)\">Afrin offensive</a>.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "Turkish Armed Forces", "link": "https://wikipedia.org/wiki/Turkish_Armed_Forces"}, {"title": "Syrian National Army", "link": "https://wikipedia.org/wiki/Syrian_National_Army"}, {"title": "Afrin District", "link": "https://wikipedia.org/wiki/Afrin_District"}, {"title": "Afrin offensive (January-March 2018)", "link": "https://wikipedia.org/wiki/Afrin_offensive_(January%E2%80%93March_2018)"}]}, {"year": "2018", "text": "Students across the United States stage the March for Our Lives demanding gun control in response to the Stoneman Douglas High School shooting.", "html": "2018 - Students across the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> stage the <a href=\"https://wikipedia.org/wiki/March_for_Our_Lives\" title=\"March for Our Lives\">March for Our Lives</a> demanding gun control in response to the <a href=\"https://wikipedia.org/wiki/Parkland_high_school_shooting\" title=\"Parkland high school shooting\">Stoneman Douglas High School shooting</a>.", "no_year_html": "Students across the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> stage the <a href=\"https://wikipedia.org/wiki/March_for_Our_Lives\" title=\"March for Our Lives\">March for Our Lives</a> demanding gun control in response to the <a href=\"https://wikipedia.org/wiki/Parkland_high_school_shooting\" title=\"Parkland high school shooting\">Stoneman Douglas High School shooting</a>.", "links": [{"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "March for Our Lives", "link": "https://wikipedia.org/wiki/March_for_Our_Lives"}, {"title": "Parkland high school shooting", "link": "https://wikipedia.org/wiki/Parkland_high_school_shooting"}]}, {"year": "2019", "text": "Jakarta MRT, a rapid transit system in Jakarta, began operation.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Jakarta_MRT\" title=\"Jakarta MRT\">Jakarta MRT</a>, a <a href=\"https://wikipedia.org/wiki/Rapid_transit\" title=\"Rapid transit\">rapid transit system</a> in <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a>, began operation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jakarta_MRT\" title=\"Jakarta MRT\">Jakarta MRT</a>, a <a href=\"https://wikipedia.org/wiki/Rapid_transit\" title=\"Rapid transit\">rapid transit system</a> in <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a>, began operation.", "links": [{"title": "Jakarta MRT", "link": "https://wikipedia.org/wiki/Jakarta_MRT"}, {"title": "Rapid transit", "link": "https://wikipedia.org/wiki/Rapid_transit"}, {"title": "Jakarta", "link": "https://wikipedia.org/wiki/Jakarta"}]}, {"year": "2024", "text": "The 2024 Senegalese presidential election is held following anti-government protests.", "html": "2024 - The <a href=\"https://wikipedia.org/wiki/2024_Senegalese_presidential_election\" title=\"2024 Senegalese presidential election\">2024 Senegalese presidential election</a> is held following <a href=\"https://wikipedia.org/wiki/2023%E2%80%932024_Senegalese_protests\" title=\"2023-2024 Senegalese protests\">anti-government protests</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2024_Senegalese_presidential_election\" title=\"2024 Senegalese presidential election\">2024 Senegalese presidential election</a> is held following <a href=\"https://wikipedia.org/wiki/2023%E2%80%932024_Senegalese_protests\" title=\"2023-2024 Senegalese protests\">anti-government protests</a>.", "links": [{"title": "2024 Senegalese presidential election", "link": "https://wikipedia.org/wiki/2024_Senegalese_presidential_election"}, {"title": "2023-2024 Senegalese protests", "link": "https://wikipedia.org/wiki/2023%E2%80%932024_Senegalese_protests"}]}], "Births": [{"year": "1103", "text": "<PERSON><PERSON>, Chinese military general (d. 1142)", "html": "1103 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese military general (d. 1142)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese military general (d. 1142)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1441", "text": "<PERSON>, Elector of Saxony, German ruler of Saxony (d. 1486)", "html": "1441 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a>, German ruler of Saxony (d. 1486)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a>, German ruler of Saxony (d. 1486)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony"}]}, {"year": "1494", "text": "<PERSON><PERSON>, German mineralogist and scholar (d. 1555)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gricola\" title=\"<PERSON>ius Agricola\"><PERSON><PERSON></a>, German mineralogist and scholar (d. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gricola\" title=\"<PERSON>ius Agricola\"><PERSON><PERSON></a>, German mineralogist and scholar (d. 1555)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgius_Agricola"}]}, {"year": "1577", "text": "<PERSON>, Duke of Pomerania-Stettin, Bishop of Cammin (d. 1620)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a>-<PERSON><PERSON><PERSON>, Bishop of Cammin (d. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a>-<PERSON><PERSON><PERSON>, Bishop of Cammin (d. 1620)", "links": [{"title": "<PERSON>, Duke of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Pomerania"}]}, {"year": "1607", "text": "<PERSON><PERSON><PERSON>, Dutch admiral (d. 1667)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch admiral (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch admiral (d. 1667)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1628", "text": "<PERSON> of Brunswick-Lüneburg (d. 1685)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brunswick-L%C3%BCneburg\" class=\"mw-redirect\" title=\"<PERSON> of Brunswick-Lüneburg\"><PERSON> of Brunswick-Lüneburg</a> (d. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brunswick-L%C3%BCneburg\" class=\"mw-redirect\" title=\"<PERSON> of Brunswick-Lüneburg\"><PERSON> of Brunswick-Lüneburg</a> (d. 1685)", "links": [{"title": "<PERSON> of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1657", "text": "<PERSON><PERSON>, Japanese academic and politician (d. 1725)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese academic and politician (d. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese academic and politician (d. 1725)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1693", "text": "<PERSON>, English carpenter and clock-maker, invented the Marine chronometer (d. 1776)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English carpenter and clock-maker, invented the <a href=\"https://wikipedia.org/wiki/Marine_chronometer\" title=\"Marine chronometer\">Marine chronometer</a> (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English carpenter and clock-maker, invented the <a href=\"https://wikipedia.org/wiki/Marine_chronometer\" title=\"Marine chronometer\">Marine chronometer</a> (d. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Marine chronometer", "link": "https://wikipedia.org/wiki/Marine_chronometer"}]}, {"year": "1725", "text": "<PERSON>, American lawyer and politician, 9th Governor of North Carolina (d. 1813)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(North_Carolina_governor)\" title=\"<PERSON> (North Carolina governor)\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(North_Carolina_governor)\" title=\"<PERSON> (North Carolina governor)\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (d. 1813)", "links": [{"title": "<PERSON> (North Carolina governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(North_Carolina_governor)"}, {"title": "Governor of North Carolina", "link": "https://wikipedia.org/wiki/Governor_of_North_Carolina"}]}, {"year": "1725", "text": "<PERSON>, American lawyer and politician, 1st Lieutenant Governor of Massachusetts (d. 1788)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts\" title=\"Lieutenant Governor of Massachusetts\">Lieutenant Governor of Massachusetts</a> (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts\" title=\"Lieutenant Governor of Massachusetts\">Lieutenant Governor of Massachusetts</a> (d. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts"}]}, {"year": "1755", "text": "<PERSON>, American lawyer and politician, United States Ambassador to the United Kingdom (d. 1827)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/Rufus_King\" title=\"Rufus King\">Rufus King</a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rufus_King\" title=\"Rufus King\">Rufus <PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (d. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Kingdom", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom"}]}, {"year": "1762", "text": "<PERSON>, Portuguese organist and composer (d. 1830)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/Marcos_Portugal\" title=\"Marcos Portugal\"><PERSON></a>, Portuguese organist and composer (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marcos_Portugal\" title=\"Marcos Portugal\"><PERSON></a>, Portuguese organist and composer (d. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcos_Portugal"}]}, {"year": "1775", "text": "<PERSON><PERSON><PERSON>, Indian poet and composer (d. 1835)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and composer (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and composer (d. 1835)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON><PERSON>, Russian-Italian painter (d. 1836)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Italian painter (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Italian painter (d. 1836)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON><PERSON><PERSON>, French author (d. 1889)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author (d. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, Canadian businessman and politician (d. 1878)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON><PERSON><PERSON>, Canadian minister, educator, and politician (d. 1882)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Egerton_Ryerson\" title=\"Egerton Ryerson\"><PERSON><PERSON><PERSON></a>, Canadian minister, educator, and politician (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egerton_Ryerson\" title=\"Egerton Ryerson\"><PERSON><PERSON><PERSON></a>, Canadian minister, educator, and politician (d. 1882)", "links": [{"title": "Egerton Ryerson", "link": "https://wikipedia.org/wiki/Egerton_Ryerson"}]}, {"year": "1808", "text": "<PERSON>, Spanish-French soprano (d. 1836)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Maria_Mali<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, Spanish-French soprano (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_Mali<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-French soprano (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Malibran"}]}, {"year": "1809", "text": "<PERSON>, Spanish journalist and author (d. 1837)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist and author (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist and author (d. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Jo<PERSON>%C3%A9_de_<PERSON>rra"}]}, {"year": "1809", "text": "<PERSON>, French mathematician and academic (d. 1882)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1816", "text": "<PERSON><PERSON><PERSON>, Mexican politician and Roman Catholic archbishop, regent during the Second Mexican Empire (d. 1891)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/Pela<PERSON>_Antonio_de_Labastida_y_D%C3%A1valos\" title=\"Pelagio Antonio de Labastida y Dávalos\"><PERSON><PERSON><PERSON> Labastida y Dávalos</a>, Mexican politician and Roman Catholic <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a>, <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> during the <a href=\"https://wikipedia.org/wiki/Second_Mexican_Empire\" title=\"Second Mexican Empire\">Second Mexican Empire</a> (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pelagio_Antonio_de_Labastida_y_D%C3%A1valos\" title=\"Pelagio Antonio de Labastida y Dávalos\"><PERSON><PERSON><PERSON> Labastida y Dávalos</a>, Mexican politician and Roman Catholic <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a>, <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> during the <a href=\"https://wikipedia.org/wiki/Second_Mexican_Empire\" title=\"Second Mexican Empire\">Second Mexican Empire</a> (d. 1891)", "links": [{"title": "Pelagio Antonio de Labastida y Dávalos", "link": "https://wikipedia.org/wiki/Pelagio_Antonio_de_Labastida_y_D%C3%A1valos"}, {"title": "Archbishop", "link": "https://wikipedia.org/wiki/Archbishop"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regent"}, {"title": "Second Mexican Empire", "link": "https://wikipedia.org/wiki/Second_Mexican_Empire"}]}, {"year": "1820", "text": "<PERSON>, French physicist and academic (d. 1891)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, American poet and composer (d. 1915)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and composer (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and composer (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, English philosopher and critic (d. 1887)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and critic (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and critic (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, American activist and author (d. 1898)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, American activist and author (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, American activist and author (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, American lawyer and jurist (d. 1902)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, American businessman (d. 1904)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, Mexican general (d. 1862)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ignacio_<PERSON>go<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Austrian poet and playwright (d. 1889)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian poet and playwright (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian poet and playwright (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, English textile designer, poet, and author (d. 1896)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English textile designer, poet, and author (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Morris\"><PERSON></a>, English textile designer, poet, and author (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, American soldier, geologist, and explorer (d. 1902)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, geologist, and explorer (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, geologist, and explorer (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, Austrian physicist, mathematician, and poet (d. 1893)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian physicist, mathematician, and poet (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian physicist, mathematician, and poet (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON>, Canadian journalist and politician, 18th Mayor of Montreal (d. 1906)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician, 18th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician, 18th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (d. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>nd"}, {"title": "Mayor of Montreal", "link": "https://wikipedia.org/wiki/Mayor_of_Montreal"}]}, {"year": "1850", "text": "<PERSON>, English minister and author (d. 1935)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English minister and author (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English minister and author (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Australian politician, 11th Premier of Western Australia (d. 1930)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1855", "text": "<PERSON>, American banker, financier, and diplomat, 49th United States Secretary of the Treasury (d. 1937)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American banker, financier, and diplomat, 49th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American banker, financier, and diplomat, 49th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1855", "text": "<PERSON>, South African author and activist (d. 1920)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author and activist (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author and activist (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American painter and educator (d. 1951)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON>, French author and playwright (d. 1955)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Fabre\" title=\"Émile <PERSON>\"><PERSON><PERSON></a>, French author and playwright (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Fabre\" title=\"Émile Fabre\"><PERSON><PERSON></a>, French author and playwright (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Fabre"}]}, {"year": "1871", "text": "<PERSON>, English music hall singer (d. 1913)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English music hall singer (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English music hall singer (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Italian economist and politician, 2nd President of the Italian Republic (d. 1961)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian economist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Italian_Republic\" class=\"mw-redirect\" title=\"President of the Italian Republic\">President of the Italian Republic</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian economist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Italian_Republic\" class=\"mw-redirect\" title=\"President of the Italian Republic\">President of the Italian Republic</a> (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Italian Republic", "link": "https://wikipedia.org/wiki/President_of_the_Italian_Republic"}]}, {"year": "1874", "text": "<PERSON>, Hungarian-American magician and actor (d. 1926)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American magician and actor (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American magician and actor (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Canadian lacrosse player (d. 1953)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lacrosse)\" title=\"<PERSON> (lacrosse)\"><PERSON></a>, Canadian lacrosse player (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lacrosse)\" title=\"<PERSON> (lacrosse)\"><PERSON></a>, Canadian lacrosse player (d. 1953)", "links": [{"title": "<PERSON> (lacrosse)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lacrosse)"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, Turkish philosopher, poet, and composer (d. 1953)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>zen_Tevfik\" title=\"Neyzen Tevfik\"><PERSON><PERSON><PERSON>v<PERSON></a>, Turkish philosopher, poet, and composer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>zen_Tevfik\" title=\"Neyzen Tevfik\"><PERSON><PERSON><PERSON>v<PERSON></a>, Turkish philosopher, poet, and composer (d. 1953)", "links": [{"title": "Neyzen Tevfik", "link": "https://wikipedia.org/wiki/Neyzen_Tevfik"}]}, {"year": "1882", "text": "<PERSON>, French gymnast (d. 1951)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French gymnast (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French gymnast (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON><PERSON>, 8th Viscount <PERSON>, English politician, 5th Governor-General of New Zealand (d. 1943)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Viscount_Galway\" title=\"<PERSON>, 8th Viscount <PERSON>\"><PERSON>, 8th Viscount <PERSON></a>, English politician, 5th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Viscount_<PERSON>\" title=\"<PERSON>, 8th Viscount <PERSON>\"><PERSON>, 8th Viscount <PERSON></a>, English politician, 5th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, 8th Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_8th_Viscount_<PERSON>"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}]}, {"year": "1883", "text": "<PERSON>, Scottish-American golfer (d. 1945)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American golfer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American golfer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Dutch-American physicist and chemist, Nobel Prize laureate (d. 1966)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Japanese chemist (d. 1968)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese chemist (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese chemist (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1884", "text": "<PERSON>, French cardinal (d. 1972)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Tisserant\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, French cardinal (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Tisserant\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, French cardinal (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_Tisserant"}]}, {"year": "1885", "text": "<PERSON>, American swimmer (d. 1973)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer (d. 1973)", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>(swimmer)"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Romanian violinist and composer (d. 1978)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian violinist and composer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian violinist and composer (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American photographer (d. 1958)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>-<PERSON>, French architect and designer (d. 1945)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect and designer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect and designer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, American actor, director, and screenwriter (d. 1933)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rbuckle\" title=\"<PERSON><PERSON><PERSON> Arbuckle\"><PERSON><PERSON><PERSON></a>, American actor, director, and screenwriter (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rbuckle\" title=\"<PERSON><PERSON><PERSON> Arbuckle\"><PERSON><PERSON><PERSON></a>, American actor, director, and screenwriter (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Estonian politician (d. 1922)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, English-Canadian runner (d. 1969)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English-Canadian runner (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English-Canadian runner (d. 1969)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1890", "text": "<PERSON>, Canadian educator and politician (d. 1954)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Russian physicist and academic (d. 1951)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, American mathematician and academic (d. 1977)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Morse\" title=\"<PERSON><PERSON> Morse\"><PERSON><PERSON></a>, American mathematician and academic (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Morse\" title=\"<PERSON><PERSON> Morse\"><PERSON><PERSON></a>, American mathematician and academic (d. 1977)", "links": [{"title": "<PERSON><PERSON> Morse", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Morse"}]}, {"year": "1893", "text": "<PERSON>, German astronomer and author (d. 1960)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and author (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and author (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American baseball player and scout (d. 1973)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Austrian-American psychotherapist and academic (d. 1957)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Wilhelm_Reich\" title=\"Wilhelm Reich\"><PERSON></a>, Austrian-American psychotherapist and academic (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilhelm_Reich\" title=\"Wilhelm Reich\"><PERSON></a>, Austrian-American psychotherapist and academic (d. 1957)", "links": [{"title": "Wilhelm <PERSON>", "link": "https://wikipedia.org/wiki/Wilhelm_Reich"}]}, {"year": "1901", "text": "<PERSON><PERSON>, American animator, director, and producer, co-created <PERSON> (d. 1971)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Ub_Iwerks\" title=\"Ub Iwerks\"><PERSON><PERSON> Iwerks</a>, American animator, director, and producer, co-created <a href=\"https://wikipedia.org/wiki/<PERSON>_Mouse\" title=\"Mickey Mouse\"><PERSON> Mouse</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ub_Iwerks\" title=\"Ub Iwerks\"><PERSON><PERSON> Iwerks</a>, American animator, director, and producer, co-created <a href=\"https://wikipedia.org/wiki/<PERSON>_Mouse\" title=\"Mickey Mouse\"><PERSON> Mouse</a> (d. 1971)", "links": [{"title": "Ub Iwerks", "link": "https://wikipedia.org/wiki/Ub_Iwerks"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American lawyer and politician, 47th Governor of New York (d. 1971)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "1903", "text": "<PERSON>, German biochemist and academic, Nobel Prize laureate (d. 1995)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1903", "text": "<PERSON>, English journalist, author, and scholar (d. 1990)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and scholar (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and scholar (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>, Filipino author and diplomat (d. 2007)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Santillan-Castrence\" title=\"<PERSON>ura Santillan-Castrence\"><PERSON><PERSON>-<PERSON><PERSON><PERSON></a>, Filipino author and diplomat (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Santillan-Castrence\" title=\"<PERSON>ura Santillan-Castrence\"><PERSON><PERSON>-<PERSON><PERSON><PERSON></a>, Filipino author and diplomat (d. 2007)", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pura_Santillan-Castrence"}]}, {"year": "1907", "text": "<PERSON>, Canadian lawyer and politician, 17th Premier of Quebec (d. 1960)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_Sauv%C3%A9"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1909", "text": "<PERSON>, American criminal (d. 1934)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Barrow\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American criminal (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clyde_Barrow\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American criminal (d. 1934)", "links": [{"title": "Clyde Barrow", "link": "https://wikipedia.org/wiki/Clyde_Barrow"}]}, {"year": "1909", "text": "<PERSON>, Romanian pastor and evangelist (d. 2001)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian pastor and evangelist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian pastor and evangelist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actor, singer, and director (d. 1975)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American animator, director, and producer, co-founded Hanna<PERSON>Barber<PERSON> (d. 2006)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>-Barber<PERSON>\" title=\"<PERSON><PERSON>Barbera\"><PERSON><PERSON></a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>Barber<PERSON>\"><PERSON><PERSON></a> (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American educator and activist (d. 2010)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Height\"><PERSON></a>, American educator and activist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Height\"><PERSON></a>, American educator and activist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, French racing driver (d. 2006)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Swedish-American soldier and author (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American soldier and author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American soldier and author (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English palaeontologist and academic (d. 2010)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English palaeontologist and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English palaeontologist and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Greek painter and sculptor (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek painter and sculptor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek painter and sculptor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English biochemist and crystallographer, Nobel Prize laureate (d. 1997)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and crystallographer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and crystallographer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1919", "text": "<PERSON>, American poet and publisher, co-founded City Lights Bookstore (d. 2021)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and publisher, co-founded <a href=\"https://wikipedia.org/wiki/City_Lights_Bookstore\" title=\"City Lights Bookstore\">City Lights Bookstore</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and publisher, co-founded <a href=\"https://wikipedia.org/wiki/City_Lights_Bookstore\" title=\"City Lights Bookstore\">City Lights Bookstore</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "City Lights Bookstore", "link": "https://wikipedia.org/wiki/City_Lights_Bookstore"}]}, {"year": "1919", "text": "<PERSON>, American economist and historian (d. 2005)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and historian (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and historian (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor, director, and screenwriter (d. 1996)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American author (d. 2006)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Polish priest (d. 1987)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>nicki\"><PERSON><PERSON><PERSON></a>, Polish priest (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>i\"><PERSON><PERSON><PERSON></a>, Polish priest (d. 1987)", "links": [{"title": "Franciszek Blachnicki", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Russian chess player (d. 2010)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian chess player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian chess player (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Canadian dancer and choreographer (d. 2005)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian dancer and choreographer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian dancer and choreographer (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actor (d. 1986)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hamilton\"><PERSON></a>, American actor (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hamilton\"><PERSON></a>, American actor (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English author and publisher (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and publisher (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and publisher (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor (d. 1998)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fell\" title=\"Norman Fell\"><PERSON></a>, American actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norman_Fell\" title=\"Norman Fell\"><PERSON></a>, American actor (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Irish cardinal (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cardinal (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cardinal (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Italian playwright, actor, director, and composer, Nobel Prize laureate (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian playwright, actor, director, and composer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian playwright, actor, director, and composer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2016)", "links": [{"title": "Dario <PERSON>", "link": "https://wikipedia.org/wiki/Dario_Fo"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1926", "text": "<PERSON>, American hurdler (d. 2000)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler (d. 2000)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1927", "text": "<PERSON>, American biochemist and academic (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"John <PERSON> Hastings\"><PERSON></a>, American biochemist and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_<PERSON>_Hastings\" title=\"John <PERSON> Hastings\"><PERSON></a>, American biochemist and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hastings"}]}, {"year": "1927", "text": "<PERSON>, German author and playwright (d. 2023)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American pianist and composer (d. 2024)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Italian-American actor (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Central African politician, 1st President of the Central African Republic (d. 2003)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Central African politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"President of the Central African Republic\">President of the Central African Republic</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Central African politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"President of the Central African Republic\">President of the Central African Republic</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the Central African Republic", "link": "https://wikipedia.org/wiki/President_of_the_Central_African_Republic"}]}, {"year": "1930", "text": "<PERSON>, American actor and producer (d. 1980)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, German educator and politician, Mayor of Marburg (d. 2003)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German educator and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German educator and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of mayors of Marburg", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Marburg"}]}, {"year": "1933", "text": "<PERSON>, American sculptor and educator (d. 2011)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American television producer (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television producer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television producer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English writer, chef, author, and television presenter", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer, chef, author, and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer, chef, author, and television presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American bass guitarist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2015)", "links": [{"title": "Don <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Peruvian-American tennis player (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-American tennis player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-American tennis player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer and pianist (d. 1970)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, German musician (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German musician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German musician (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English historian and author", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American football player (d. 2020)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2020)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1940", "text": "<PERSON>, American fashion designer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American songwriter, composer and producer (d. 2015)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, composer and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, composer and producer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Alou\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Alou\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Alou"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American sergeant and actor (d. 2018)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American sergeant and actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American sergeant and actor (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian academic and politician, 8th Prime Minister of Serbia", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C5%A1tunica\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian academic and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Serbia\" title=\"Prime Minister of Serbia\">Prime Minister of Serbia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C5%A1tunica\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian academic and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Serbia\" title=\"Prime Minister of Serbia\">Prime Minister of Serbia</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C5%A1tunica"}, {"title": "Prime Minister of Serbia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Serbia"}]}, {"year": "1945", "text": "<PERSON>, American paleontologist and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American director, producer, and screenwriter (d. 2016)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English actor and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, German guitarist and songwriter (d. 2008)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German guitarist and songwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German guitarist and songwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American stuntwoman (d. 2018)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Kitty_O%27Neil\" title=\"<PERSON>\"><PERSON></a>, American stuntwoman (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kitty_O%27Neil\" title=\"<PERSON>\"><PERSON></a>, American stuntwoman (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kitty_O%27Neil"}]}, {"year": "1947", "text": "<PERSON>, American football player and coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American lawyer and politician, 22nd Governor of Washington", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 22nd <a href=\"https://wikipedia.org/wiki/Governor_of_Washington\" class=\"mw-redirect\" title=\"Governor of Washington\">Governor of Washington</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 22nd <a href=\"https://wikipedia.org/wiki/Governor_of_Washington\" class=\"mw-redirect\" title=\"Governor of Washington\">Governor of Washington</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Washington", "link": "https://wikipedia.org/wiki/Governor_of_Washington"}]}, {"year": "1947", "text": "<PERSON>, English footballer and coach (d. 2022)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1947)\" title=\"<PERSON> (footballer, born 1947)\"><PERSON></a>, English footballer and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1947)\" title=\"<PERSON> (footballer, born 1947)\"><PERSON></a>, English footballer and coach (d. 2022)", "links": [{"title": "<PERSON> (footballer, born 1947)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1947)"}]}, {"year": "1947", "text": "<PERSON>, English businessman", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Peruvian sociologist and politician (d. 2013)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian sociologist and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian sociologist and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Polish mountaineer (d. 1989)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Danish musician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American author and poet", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tabitha King\"><PERSON></a>, American author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tabitha King\"><PERSON></a>, American author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Dutch footballer and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian bass player (d. 2017)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter, bass player, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Iranian academic and politician, 36th Foreign Affairs Minister of Iran", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian academic and politician, 36th <a href=\"https://wikipedia.org/wiki/Foreign_Affairs_Minister_of_Iran\" class=\"mw-redirect\" title=\"Foreign Affairs Minister of Iran\">Foreign Affairs Minister of Iran</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian academic and politician, 36th <a href=\"https://wikipedia.org/wiki/Foreign_Affairs_Minister_of_Iran\" class=\"mw-redirect\" title=\"Foreign Affairs Minister of Iran\">Foreign Affairs Minister of Iran</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Foreign Affairs Minister of Iran", "link": "https://wikipedia.org/wiki/Foreign_Affairs_Minister_of_Iran"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Sri Lankan lawyer and politician, 13th Prime Minister of Sri Lanka", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ick<PERSON>esing<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka\" title=\"Prime Minister of Sri Lanka\">Prime Minister of Sri Lanka</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka\" title=\"Prime Minister of Sri Lanka\">Prime Minister of Sri Lanka</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wickremesinghe"}, {"title": "Prime Minister of Sri Lanka", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka"}]}, {"year": "1950", "text": "<PERSON>, American football player and agent (d. 2011)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and agent (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and agent (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Scottish-Australian footballer and manager (d. 2013)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1951)\" title=\"<PERSON> (footballer, born 1951)\"><PERSON></a>, Scottish-Australian footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1951)\" title=\"<PERSON> (footballer, born 1951)\"><PERSON></a>, Scottish-Australian footballer and manager (d. 2013)", "links": [{"title": "<PERSON> (footballer, born 1951)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1951)"}]}, {"year": "1951", "text": "<PERSON>, American golfer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1951", "text": "<PERSON>, American fashion designer, founded the Tommy Hilfiger Corporation", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Corporation\" class=\"mw-redirect\" title=\"Tommy <PERSON>iger Corporation\">Tommy <PERSON> Corporation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Corporation\" class=\"mw-redirect\" title=\"Tommy Hilfiger Corporation\">Tommy <PERSON> Corporation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tommy <PERSON> Corporation", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Corporation"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Scottish bass player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish bass player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Polish long jumper and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_W%C5%82odarczy<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish long jumper and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anna_W%C5%82odarczyk\" title=\"<PERSON>\"><PERSON></a>, Polish long jumper and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_W%C5%82odarczyk"}]}, {"year": "1952", "text": "<PERSON>, American football player (d. 2013)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American lawyer, philosopher, and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, philosopher, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, philosopher, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor and comedian (d. 2022)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Colombian singer (d. 1992)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mae<PERSON>re\" title=\"<PERSON>\"><PERSON></a>, Colombian singer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mae<PERSON>re\" title=\"<PERSON>\"><PERSON></a>, Colombian singer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_Orozco_Maestre"}]}, {"year": "1954", "text": "<PERSON>, American actress and director", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Donna_P<PERSON>w"}]}, {"year": "1955", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1956", "text": "<PERSON>, American businessman", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American cartoonist and painter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American cartoonist and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American cartoonist and painter", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1957", "text": "<PERSON>, Canadian cyclist and skier", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist and skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist and skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American basketball player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American sprinter (d. 2021)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Em<PERSON>_King\" title=\"Emmit King\"><PERSON><PERSON></a>, American sprinter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Em<PERSON>_King\" title=\"Emmit King\">Em<PERSON></a>, American sprinter (d. 2021)", "links": [{"title": "Emmit King", "link": "https://wikipedia.org/wiki/Emmit_King"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American hurdler and football player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler and football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English footballer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Swedish cartoonist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish cartoonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American wrestler", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English-American actress and model", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English-American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English-American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, German singer-songwriter and actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nena"}]}, {"year": "1960", "text": "<PERSON>, American race car driver", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Australian cricketer and coach (d. 2020)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and coach (d. 2020)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Greek economist and politician, Greek Minister of Finance", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Finance (Greece)\">Greek Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Finance (Greece)\">Greek Minister of Finance</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Finance (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Finance_(Greece)"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Canadian violinist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Ang%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ang%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian violinist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ang%C3%A8<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American lawyer, journalist, and talk show host", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Jones\" title=\"<PERSON> Jones\"><PERSON></a>, American lawyer, journalist, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Jones\" title=\"<PERSON> Jones\"><PERSON></a>, American lawyer, journalist, and talk show host", "links": [{"title": "<PERSON> Jones", "link": "https://wikipedia.org/wiki/<PERSON>_Jones"}]}, {"year": "1962", "text": "<PERSON><PERSON>, German discus thrower", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German discus thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer and manager (d. 2015)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tyshchenko\" title=\"<PERSON><PERSON><PERSON> Tyshchenko\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tyshchenko\" title=\"<PERSON><PERSON><PERSON> Tyshchenko\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Dutch footballer and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, German decathlete and bobsledder", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Voss\"><PERSON><PERSON></a>, German decathlete and bobsledder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Voss\"><PERSON><PERSON></a>, German decathlete and bobsledder", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hood\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hood\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON> Undertaker, American wrestler and actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/The_Undertaker\" title=\"The Undertaker\">The Undertaker</a>, American wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Undertaker\" title=\"The Undertaker\">The Undertaker</a>, American wrestler and actor", "links": [{"title": "The Undertaker", "link": "https://wikipedia.org/wiki/The_Undertaker"}]}, {"year": "1966", "text": "<PERSON>, American sprinter and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Heard\"><PERSON></a>, American sprinter and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Heard\"><PERSON></a>, American sprinter and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>,  Filipino broadcast journalist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Hizon\" title=\"<PERSON>zon\"><PERSON></a>, Filipino broadcast journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Hizon\" title=\"<PERSON> Hizon\"><PERSON></a>, Filipino broadcast journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rico_Hizon"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American skier", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Indonesian badminton player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian badminton player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ur"}]}, {"year": "1969", "text": "<PERSON>, Austrian skier", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Albanian politician, incumbent President of Albania", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian politician, incumbent <a href=\"https://wikipedia.org/wiki/Presidents_of_Albania\" class=\"mw-redirect\" title=\"Presidents of Albania\">President of Albania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian politician, incumbent <a href=\"https://wikipedia.org/wiki/Presidents_of_Albania\" class=\"mw-redirect\" title=\"Presidents of Albania\">President of Albania</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ilir_Meta"}, {"title": "Presidents of Albania", "link": "https://wikipedia.org/wiki/Presidents_of_Albania"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bowles\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bowles\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lauren_Bowles"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Irish singer-songwriter and violinist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Austrian swimmer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American journalist and author (d. 2012)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian-American football player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American comedian and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Tig_<PERSON><PERSON>\" title=\"Tig Notaro\"><PERSON><PERSON> <PERSON><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tig_<PERSON><PERSON>\" title=\"Tig Notaro\"><PERSON><PERSON> <PERSON><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tig_Notaro"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, French footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Polish footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_B%C4%85k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_B%C4%85k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "Jacek Bąk", "link": "https://wikipedia.org/wiki/Jacek_B%C4%85k"}]}, {"year": "1973", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Slovenian actor, concert pianist and chansonnier", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Jure_Ivanu%C5%A1i%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian actor, concert pianist and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">chansonnier</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u%C5%A1i%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian actor, concert pianist and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">chansonnier</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jure_Ivanu%C5%A1i%C4%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Danish swimmer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Russian high jumper", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Filipino comedian and activist (d. 2014)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(comedian)\" title=\"<PERSON><PERSON> (comedian)\"><PERSON><PERSON></a>, Filipino comedian and activist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(comedian)\" title=\"<PERSON><PERSON> (comedian)\"><PERSON><PERSON></a>, Filipino comedian and activist (d. 2014)", "links": [{"title": "<PERSON><PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(comedian)"}]}, {"year": "1975", "text": "<PERSON>, Swedish-Monégasque tennis player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Monégasque tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Monégasque tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Senegalese footballer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aliou_Ciss%C3%A9"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1976", "text": "<PERSON>, American football player and entrepreneur", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and entrepreneur", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Russian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian footballer and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Czech footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Ujfalu%C5%A1i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Ujfalu%C5%A1i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Ujfalu%C5%A1i"}]}, {"year": "1978", "text": "<PERSON>, Dominican baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>de\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Valverde"}]}, {"year": "1979", "text": "<PERSON>, American actress, director, and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Lake_Bell\" title=\"Lake Bell\"><PERSON> Bell</a>, American actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lake_Bell\" title=\"Lake Bell\"><PERSON> Bell</a>, American actress, director, and screenwriter", "links": [{"title": "Lake Bell", "link": "https://wikipedia.org/wiki/Lake_Bell"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Greek hurdler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek hurdler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>bid\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>bid\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>bid"}]}, {"year": "1980", "text": "<PERSON>, American ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Ta<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Venetis\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Vene<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tis"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(safety)\" title=\"<PERSON> (safety)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(safety)\" title=\"<PERSON> (safety)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (safety)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(safety)"}]}, {"year": "1981", "text": "<PERSON>, American ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Dutch footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English racing driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Puerto Rican professional wrestler", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Epico_Colon\" class=\"mw-redirect\" title=\"Epico Colon\"><PERSON><PERSON></a>, Puerto Rican professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Epico_Colon\" class=\"mw-redirect\" title=\"Epico Colon\"><PERSON><PERSON></a>, Puerto Rican professional wrestler", "links": [{"title": "Epico Colon", "link": "https://wikipedia.org/wiki/Epico_Colon"}]}, {"year": "1982", "text": "<PERSON>, American mixed martial artist and professional wrestler", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and professional wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1982", "text": "<PERSON>, Belgian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_24_March_1983)\" title=\"<PERSON> (footballer, born 24 March 1983)\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_24_March_1983)\" title=\"<PERSON> (footballer, born 24 March 1983)\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON> (footballer, born 24 March 1983)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_24_March_1983)"}]}, {"year": "1983", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T. J<PERSON> Ford\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, French-Cameroonian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_<PERSON>sou-Ekot<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>u-E<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_Assou-Ekot<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>u-E<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-Cameroonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beno%C3%AEt_<PERSON>so<PERSON>-<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, South Korean singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Park_Bom\" title=\"Park Bom\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Park_Bom\" title=\"Park Bom\"><PERSON></a>, South Korean singer", "links": [{"title": "Park Bom", "link": "https://wikipedia.org/wiki/Park_Bom"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Indian field hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_D%27Souza\" title=\"<PERSON>\"><PERSON></a>, Indian field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Souza\" title=\"<PERSON>\"><PERSON></a>, Indian field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adrian_D%27Souza"}]}, {"year": "1984", "text": "<PERSON>, Kenyan runner", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, German tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Japanese actress and singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American wrestler, manager, and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler, manager, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler, manager, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Bangladeshi cricketer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Japanese actress and singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ami"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1987)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "Ramires", "link": "https://wikipedia.org/wiki/Ramires"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Latvian heptathlete", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Aiga_Grabuste\" title=\"<PERSON>ga Grabuste\"><PERSON><PERSON></a>, Latvian heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aiga_Grabuste\" title=\"<PERSON>ga Grabuste\"><PERSON><PERSON></a>, Latvian heptathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aiga_Grabuste"}]}, {"year": "1988", "text": "<PERSON>, Zimbabwean cricketer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1988)\" title=\"<PERSON> (cricketer, born 1988)\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1988)\" title=\"<PERSON> (cricketer, born 1988)\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON> (cricketer, born 1988)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1988)"}]}, {"year": "1988", "text": "<PERSON>, English actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Argentinian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Mat%C3%ADas_Mart%C3%ADnez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mat%C3%ADas_Mart%C3%ADnez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mat%C3%ADas_Mart%C3%ADnez"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Estonian swimmer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ip<PERSON>\" title=\"<PERSON><PERSON>ip<PERSON>\"><PERSON><PERSON></a>, Estonian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1988", "text": "<PERSON>, New Zealand rugby union player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby union player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Russian-Australian bodybuilder and internet personality (d. 2011)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Zyzz\" title=\"<PERSON>yzz\"><PERSON><PERSON><PERSON></a>, Russian-Australian bodybuilder and internet personality (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zyzz\" title=\"<PERSON>yzz\"><PERSON><PERSON><PERSON></a>, Russian-Australian bodybuilder and internet personality (d. 2011)", "links": [{"title": "Zyzz", "link": "https://wikipedia.org/wiki/Zyzz"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Filipino actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Aljur_A<PERSON>nica\" title=\"Aljur <PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al<PERSON><PERSON>_<PERSON>\" title=\"Aljur <PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor", "links": [{"title": "Aljur <PERSON>", "link": "https://wikipedia.org/wiki/Aljur_Abrenica"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>-<PERSON>, Australian-New Zealand actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-New Zealand actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-New Zealand actress", "links": [{"title": "Keisha Castle-Hughes", "link": "https://wikipedia.org/wiki/Keisha_Castle-Hughes"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Castro\" title=\"<PERSON><PERSON> Castro\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Starlin_Castro\" title=\"<PERSON><PERSON> Castro\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Castro"}]}, {"year": "1990", "text": "<PERSON>, American wrestler", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Australian cricketer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American YouTuber", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American YouTuber", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/JonTron"}]}, {"year": "1991", "text": "<PERSON>, English cricketer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Slovenian tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, French-Spanish footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Enzo <PERSON>\"><PERSON><PERSON></a>, French-Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"En<PERSON>\"><PERSON><PERSON></a>, French-Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enzo_Zidane"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Japanese singer and dancer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Japanese_singer)\" title=\"<PERSON> (Japanese singer)\"><PERSON></a>, Japanese singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Japanese_singer)\" title=\"<PERSON> (Japanese singer)\"><PERSON></a>, Japanese singer and dancer", "links": [{"title": "<PERSON> (Japanese singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Japanese_singer)"}]}, {"year": "1998", "text": "<PERSON>, American actor", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American football player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English tennis player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, French tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}], "Deaths": [{"year": "809", "text": "<PERSON><PERSON>, Arab caliph (b. 763)", "html": "809 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Arab caliph (b. 763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Arab caliph (b. 763)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "832", "text": "<PERSON><PERSON><PERSON>, archbishop of Canterbury", "html": "832 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_Canterbury\" title=\"Diocese of Canterbury\">Canterbury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_Canterbury\" title=\"Diocese of Canterbury\">Canterbury</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Diocese of Canterbury", "link": "https://wikipedia.org/wiki/Diocese_of_Canterbury"}]}, {"year": "1284", "text": "<PERSON> of Cyprus (b. 1235)", "html": "1284 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a> (b. 1235)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a> (b. 1235)", "links": [{"title": "<PERSON> of Cyprus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus"}]}, {"year": "1296", "text": "<PERSON><PERSON>, Grand Master of the Knights Hospitaller", "html": "1296 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> de <PERSON>\"><PERSON><PERSON></a>, Grand Master of the Knights Hospitaller", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Grand Master of the Knights Hospitaller", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>ns"}]}, {"year": "1381", "text": "<PERSON> Vadstena, Swedish saint (b. 1332)", "html": "1381 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Vadstena\" title=\"<PERSON> of Vadstena\"><PERSON> of Vadstena</a>, Swedish saint (b. 1332)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Vadstena\" title=\"<PERSON> of Vadstena\"><PERSON> of Vadstena</a>, Swedish saint (b. 1332)", "links": [{"title": "Catherine of Vadstena", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1396", "text": "<PERSON>, English mystic and saint (b. 1340)", "html": "1396 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mystic and saint (b. 1340)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mystic and saint (b. 1340)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1399", "text": "<PERSON>, Duchess of Norfolk (b.c. 1320)", "html": "1399 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Norfolk\" title=\"<PERSON>, Duchess of Norfolk\"><PERSON>, Duchess of Norfolk</a> (b.c. 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Norfolk\" title=\"<PERSON>, Duchess of Norfolk\"><PERSON>, Duchess of Norfolk</a> (b.c. 1320)", "links": [{"title": "<PERSON>, Duchess of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Norfolk"}]}, {"year": "1443", "text": "<PERSON>, 7th Earl of Douglas (b. 1371)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Earl_<PERSON>_Douglas\" title=\"<PERSON>, 7th Earl of Douglas\"><PERSON>, 7th Earl of Douglas</a> (b. 1371)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 7th Earl <PERSON> Douglas\"><PERSON>, 7th Earl of Douglas</a> (b. 1371)", "links": [{"title": "<PERSON>, 7th Earl of Douglas", "link": "https://wikipedia.org/wiki/<PERSON>,_7th_Earl_<PERSON>_<PERSON>"}]}, {"year": "1455", "text": "<PERSON> (b. 1397)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_V\" title=\"Pope Nicholas V\">Pope <PERSON> V</a> (b. 1397)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Nicholas V\">Pope <PERSON> V</a> (b. 1397)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1499", "text": "<PERSON>, 2nd Earl of Wiltshire, English nobleman (b. 1470)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Wiltshire\" title=\"<PERSON>, 2nd Earl of Wiltshire\"><PERSON>, 2nd Earl of Wiltshire</a>, English nobleman (b. 1470)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Wiltshire\" title=\"<PERSON>, 2nd Earl of Wiltshire\"><PERSON>, 2nd Earl of Wiltshire</a>, English nobleman (b. 1470)", "links": [{"title": "<PERSON>, 2nd Earl of Wiltshire", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Wiltshire"}]}, {"year": "1563", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (b. 1514)[citation needed]", "html": "1563 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ha<PERSON>oto\" title=\"<PERSON><PERSON><PERSON> Harumoto\"><PERSON><PERSON><PERSON></a>, Japanese daim<PERSON> (b. 1514)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Harumoto\"><PERSON><PERSON><PERSON></a>, Japanese daim<PERSON> (b. 1514)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ha<PERSON>oto"}]}, {"year": "1575", "text": "<PERSON><PERSON>, Spanish-Portuguese rabbi and author (b. 1488)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> ben <PERSON>\"><PERSON> ben <PERSON></a>, Spanish-Portuguese rabbi and author (b. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish-Portuguese rabbi and author (b. 1488)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1603", "text": "<PERSON> of England (b. 1533)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/Elizabeth_I_of_England\" class=\"mw-redirect\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> I of England\"><PERSON> of England</a> (b. 1533)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1653", "text": "<PERSON>, German organist and composer (b. 1587)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1587)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1587)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1684", "text": "<PERSON>, Dutch painter (b. 1629)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1629)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1684", "text": "<PERSON>, English woman convicted of poisoning her husband", "html": "1684 - <a href=\"https://wikipedia.org/wiki/Elizabeth_<PERSON>way\" title=\"Elizabeth <PERSON>way\"><PERSON></a>, English woman convicted of poisoning her husband", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_Ridgeway\" title=\"Elizabeth Ridgeway\"><PERSON></a>, English woman convicted of poisoning her husband", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elizabeth_Ridgeway"}]}, {"year": "1773", "text": "<PERSON>, 4th Earl of Chesterfield, English politician, Captain of the Yeomen of the Guard (b. 1694)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Chesterfield\" title=\"<PERSON>, 4th Earl of Chesterfield\"><PERSON>, 4th Earl of Chesterfield</a>, English politician, <a href=\"https://wikipedia.org/wiki/Captain_of_the_Yeomen_of_the_Guard\" title=\"Captain of the Yeomen of the Guard\">Captain of the Yeomen of the Guard</a> (b. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Chesterfield\" title=\"<PERSON>, 4th Earl of Chesterfield\"><PERSON>, 4th Earl of Chesterfield</a>, English politician, <a href=\"https://wikipedia.org/wiki/Captain_of_the_Yeomen_of_the_Guard\" title=\"Captain of the Yeomen of the Guard\">Captain of the Yeomen of the Guard</a> (b. 1694)", "links": [{"title": "<PERSON>, 4th Earl of Chesterfield", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Chesterfield"}, {"title": "Captain of the Yeomen of the Guard", "link": "https://wikipedia.org/wiki/Captain_of_the_Yeomen_of_the_Guard"}]}, {"year": "1776", "text": "<PERSON>, English carpenter and clockmaker, invented the Marine chronometer (b. 1693)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English carpenter and clockmaker, invented the <a href=\"https://wikipedia.org/wiki/Marine_chronometer\" title=\"Marine chronometer\">Marine chronometer</a> (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English carpenter and clockmaker, invented the <a href=\"https://wikipedia.org/wiki/Marine_chronometer\" title=\"Marine chronometer\">Marine chronometer</a> (b. 1693)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Marine chronometer", "link": "https://wikipedia.org/wiki/Marine_chronometer"}]}, {"year": "1824", "text": "<PERSON>, French lawyer (b. 1753)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9velli%C3%A8re-L%C3%A9peaux\" title=\"<PERSON> Révellière-Lépeaux\"><PERSON>-<PERSON></a>, French lawyer (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9velli%C3%A8re-L%C3%A9peaux\" title=\"<PERSON> Révellière-Lépeaux\"><PERSON>aux</a>, French lawyer (b. 1753)", "links": [{"title": "<PERSON> Révellière-Lépeaux", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9velli%C3%A8re-L%C3%A9peaux"}]}, {"year": "1838", "text": "<PERSON>, English floriculturist and Tory politician (b. 1748/49)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Floriculturist\" class=\"mw-redirect\" title=\"Floriculturist\">floriculturist</a> and <a href=\"https://wikipedia.org/wiki/Tory_(British_political_party)\" class=\"mw-redirect\" title=\"Tory (British political party)\">Tory</a> politician (b. 1748/49)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Floriculturist\" class=\"mw-redirect\" title=\"Floriculturist\">floriculturist</a> and <a href=\"https://wikipedia.org/wiki/Tory_(British_political_party)\" class=\"mw-redirect\" title=\"Tory (British political party)\">Tory</a> politician (b. 1748/49)", "links": [{"title": "Sir <PERSON>, 2nd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet"}, {"title": "Floriculturist", "link": "https://wikipedia.org/wiki/Floriculturist"}, {"title": "Tory (British political party)", "link": "https://wikipedia.org/wiki/Tory_(British_political_party)"}]}, {"year": "1866", "text": "<PERSON> of Naples and Sicily, Queen of France (b. 1782)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples_and_Sicily\" title=\"<PERSON> of Naples and Sicily\"><PERSON> of Naples and Sicily</a>, Queen of France (b. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples_and_Sicily\" title=\"<PERSON> of Naples and Sicily\"><PERSON> of Naples and Sicily</a>, Queen of France (b. 1782)", "links": [{"title": "<PERSON> of Naples and Sicily", "link": "https://wikipedia.org/wiki/Maria_<PERSON><PERSON>_of_Naples_and_Sicily"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, French-Russian general (b. 1779)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Russian general (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Russian general (b. 1779)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, French geologist and mineralogist (b. 1817)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"A<PERSON>lle <PERSON>\"><PERSON><PERSON><PERSON></a>, French geologist and mineralogist (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Achille <PERSON>\"><PERSON><PERSON><PERSON></a>, French geologist and mineralogist (b. 1817)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American poet and educator (b. 1807)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Longfellow\" title=\"<PERSON> Longfellow\"><PERSON></a>, American poet and educator (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Longfellow\" title=\"<PERSON> Longfellow\"><PERSON></a>, American poet and educator (b. 1807)", "links": [{"title": "<PERSON> Longfellow", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>worth_Longfellow"}]}, {"year": "1887", "text": "<PERSON>, Russian painter and critic (b. 1837)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and critic (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and critic (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Russian author (b. 1855)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Vsevolod_Garshin\" title=\"Vsevolod Garshin\">V<PERSON><PERSON><PERSON><PERSON></a>, Russian author (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vsevolod_Garshin\" title=\"Vsevolod Garshin\">V<PERSON><PERSON><PERSON><PERSON></a>, Russian author (b. 1855)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vsevolod_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, French novelist, poet, and playwright (b. 1828)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, poet, and playwright (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, poet, and playwright (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Irish playwright and poet (b. 1871)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Synge\" title=\"<PERSON>ington Synge\"><PERSON></a>, Irish playwright and poet (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Synge\" title=\"<PERSON>ington Synge\"><PERSON></a>, Irish playwright and poet (b. 1871)", "links": [{"title": "<PERSON> Synge", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Synge"}]}, {"year": "1915", "text": "<PERSON>, Anglo-Irish astronomer (b. 1848)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish astronomer (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish astronomer (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Polish chemist, mathematician, and physicist (b. 1846)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish chemist, mathematician, and physicist (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish chemist, mathematician, and physicist (b. 1846)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Spanish pianist and composer (b. 1867)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist and composer (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish pianist and composer (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Vietnamese activist (b. 1872)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese activist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese activist (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, French rugby player and hurdler (b. 1871)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French rugby player and hurdler (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French rugby player and hurdler (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Reichel"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Mongolian politician (b. 1870)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Yondonwangchug\" title=\"Yondonwangchug\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian politician (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yondonwangchug\" title=\"Yondonwangchug\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian politician (b. 1870)", "links": [{"title": "Yondonwangchug", "link": "https://wikipedia.org/wiki/Yondonwangchug"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, French physicist and academic (b. 1844)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and academic (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and academic (b. 1844)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Indian-English general (b. 1903)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Orde_Wingate\" title=\"Orde Wingate\"><PERSON><PERSON></a>, Indian-English general (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orde_Wingate\" title=\"Orde Wingate\"><PERSON><PERSON></a>, Indian-English general (b. 1903)", "links": [{"title": "Orde Wingate", "link": "https://wikipedia.org/wiki/Orde_Wingate"}]}, {"year": "1946", "text": "<PERSON>, Russian chess player (b. 1892)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, German gymnast, shot putter, and jumper (b. 1869)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast, shot putter, and jumper (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast, shot putter, and jumper (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Swedish painter and illustrator (b. 1885)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Sig<PERSON>_<PERSON>t%C3%A9n\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish painter and illustrator (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sig<PERSON>_<PERSON>t%C3%A9n\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish painter and illustrator (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sigrid_Hjert%C3%A9n"}]}, {"year": "1950", "text": "<PERSON>, American lawyer and politician, 23rd United States Secretary of the Interior (b. 1865)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Australian educator and educational psychologist (b. 1887)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian educator and <a href=\"https://wikipedia.org/wiki/Educational_psychologist\" title=\"Educational psychologist\">educational psychologist</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian educator and <a href=\"https://wikipedia.org/wiki/Educational_psychologist\" title=\"Educational psychologist\">educational psychologist</a> (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Educational psychologist", "link": "https://wikipedia.org/wiki/Educational_psychologist"}]}, {"year": "1953", "text": "<PERSON> Teck (b. 1867)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Teck\"><PERSON> of Teck</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Teck\"><PERSON> of <PERSON>ck</a> (b. 1867)", "links": [{"title": "<PERSON> of Teck", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON> <PERSON><PERSON>, British mathematician and physicist (b. 1873)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Whittaker\"><PERSON><PERSON> <PERSON><PERSON></a>, British mathematician and physicist (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Whittaker\"><PERSON><PERSON> <PERSON><PERSON></a>, British mathematician and physicist (b. 1873)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON>, French-American pianist and bandleader (b. 1899)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American pianist and bandleader (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American pianist and bandleader (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Swiss physicist and explorer (b. 1884)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist and explorer (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist and explorer (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American director, producer, and screenwriter (b. 1873)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>lach%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ch%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Blach%C3%A9"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Danish architect, designed the Radisson Blu Royal Hotel and Aarhus City Hall (b. 1902)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish architect, designed the <a href=\"https://wikipedia.org/wiki/Ra<PERSON>son_Blu_Royal_Hotel,_Copenhagen\" class=\"mw-redirect\" title=\"Radisson Blu Royal Hotel, Copenhagen\">Radisson Blu Royal Hotel</a> and <a href=\"https://wikipedia.org/wiki/Aarhus_City_Hall\" title=\"Aarhus City Hall\">Aarhus City Hall</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish architect, designed the <a href=\"https://wikipedia.org/wiki/Ra<PERSON>son_Blu_Royal_Hotel,_Copenhagen\" class=\"mw-redirect\" title=\"Radisson Blu Royal Hotel, Copenhagen\">Radisson Blu Royal Hotel</a> and <a href=\"https://wikipedia.org/wiki/Aarhus_City_Hall\" title=\"Aarhus City Hall\">Aarhus City Hall</a> (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Radisson Blu Royal Hotel, Copenhagen", "link": "https://wikipedia.org/wiki/Ra<PERSON>son_Blu_Royal_Hotel,_Copenhagen"}, {"title": "Aarhus City Hall", "link": "https://wikipedia.org/wiki/Aarhus_City_Hall"}]}, {"year": "1971", "text": "<PERSON>, Australian public servant (b. 1895)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (b. 1895)", "links": [{"title": "<PERSON> (public servant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Australian accountant and politician, 25th Premier of New South Wales (b. 1889)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Australian accountant and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Australian accountant and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1889)", "links": [{"title": "<PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1976", "text": "<PERSON>, 1st Viscount <PERSON> of Alamein, English field marshal (b. 1887)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>_of_Alamein\" class=\"mw-redirect\" title=\"<PERSON>, 1st Viscount <PERSON> of Alamein\"><PERSON>, 1st Viscount <PERSON> of Alamein</a>, English field marshal (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>_of_Alamein\" class=\"mw-redirect\" title=\"<PERSON>, 1st Viscount <PERSON> of Alamein\"><PERSON>, 1st Viscount <PERSON> of Alamein</a>, English field marshal (b. 1887)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON> of Alamein", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>_of_Alamein"}]}, {"year": "1978", "text": "<PERSON>, influential Korean poet and academic (b. 1916)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-wol\" class=\"mw-redirect\" title=\"<PERSON>wol\"><PERSON></a>, influential Korean poet and academic (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-wol\" class=\"mw-redirect\" title=\"<PERSON>wol\"><PERSON>-<PERSON></a>, influential Korean poet and academic (b. 1916)", "links": [{"title": "Park Mo<PERSON>-wol", "link": "https://wikipedia.org/wiki/Park_Mok-wol"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Salvadoran archbishop (b. 1917)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/%C3%93<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran archbishop (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran archbishop (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor (b. 1891)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Turkish academic and politician, 27th Deputy Prime Minister of Turkey (b. 1922)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Turhan_Feyzio%C4%9Flu\" title=\"<PERSON><PERSON><PERSON>y<PERSON>ğlu\"><PERSON><PERSON><PERSON></a>, Turkish academic and politician, 27th <a href=\"https://wikipedia.org/wiki/List_of_Deputy_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Deputy Prime Ministers of Turkey\">Deputy Prime Minister of Turkey</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turhan_Feyzio%C4%9Flu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish academic and politician, 27th <a href=\"https://wikipedia.org/wiki/List_of_Deputy_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Deputy Prime Ministers of Turkey\">Deputy Prime Minister of Turkey</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Turhan_Feyzio%C4%9Flu"}, {"title": "List of Deputy Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Deputy_Prime_Ministers_of_Turkey"}]}, {"year": "1990", "text": "<PERSON>, American comedian and radio host (b. 1922)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and radio host (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and radio host (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian lawyer and politician, 18th Governor-General of Australia (b. 1914)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(governor-general)\" title=\"<PERSON> (governor-general)\"><PERSON></a>, Australian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor-general)\" title=\"<PERSON> (governor-general)\"><PERSON></a>, Australian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (b. 1914)", "links": [{"title": "<PERSON> (governor-general)", "link": "https://wikipedia.org/wiki/<PERSON>(governor-general)"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}]}, {"year": "1993", "text": "<PERSON>, Australian pianist, composer, actor, and playwright (b. 1905)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist, composer, actor, and playwright (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist, composer, actor, and playwright (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American journalist and author (b. 1914)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English historian and academic (b. 1900)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, German politician (b. 1902)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American baseball player and manager (b. 1912)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, English television host and producer (b. 1928)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English television host and producer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English television host and producer (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Argentinian-English biochemist and academic, Nobel Prize laureate (b. 1927)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON><PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2002", "text": "<PERSON>, American race car driver and bobsledder (b. 1932)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Said\"><PERSON></a>, American race car driver and bobsledder (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Said\"><PERSON></a>, American race car driver and bobsledder (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Austrian cardinal (b. 1919)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABr\" title=\"<PERSON>\"><PERSON></a>, Austrian cardinal (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABr\" title=\"<PERSON>\"><PERSON></a>, Austrian cardinal (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABr"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Sri Lankan police officer and diplomat (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan police officer and diplomat (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan police officer and diplomat (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Indian Marathi novelist (b. 1913)[citation needed]", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Pendse\" title=\"<PERSON><PERSON> Narayan Pendse\"><PERSON><PERSON> Pendse</a>, Indian <a href=\"https://wikipedia.org/wiki/Marathi_language\" title=\"Marathi language\">Marathi</a> novelist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Pendse\" title=\"<PERSON><PERSON> Narayan Pendse\"><PERSON><PERSON> Pendse</a>, Indian <a href=\"https://wikipedia.org/wiki/Marathi_language\" title=\"Marathi language\">Marathi</a> novelist (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Pendse"}, {"title": "Marathi language", "link": "https://wikipedia.org/wiki/Marathi_language"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, American guitarist (b. 1955)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American guitarist (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American guitarist (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Welsh-English record producer and manager (b. 1941)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English record producer and manager (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English record producer and manager (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Spanish author and screenwriter (b. 1926)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actor (b. 1914)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>mark"}]}, {"year": "2008", "text": "<PERSON>, Croatian actor (b. 1939)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian actor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian actor (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American baseball player and sportscaster (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, German racing driver (b. 1919)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Hungarian ice hockey player (b. 1975)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian ice hockey player (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian ice hockey player (b. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A1bor_<PERSON>y"}]}, {"year": "2010", "text": "<PERSON>, American actor (b. 1930)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American photographer (b. 1936)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer (b. 1936)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)"}]}, {"year": "2012", "text": "<PERSON>, New Zealand physicist and academic (b. 1947)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand physicist and academic (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand physicist and academic (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1926)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "2013", "text": "<PERSON>, New Zealand author (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, New Zealand author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, New Zealand author (b. 1926)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Norwegian theologian, academic, and politician (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Inge_L%C3%B8nning\" title=\"Inge Lønning\"><PERSON><PERSON></a>, Norwegian theologian, academic, and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inge_L%C3%B8nning\" title=\"Inge Lønning\"><PERSON><PERSON></a>, Norwegian theologian, academic, and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Inge_L%C3%B8nning"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Russian physicist, mathematician, and academic (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist, mathematician, and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist, mathematician, and academic (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>uk"}]}, {"year": "2013", "text": "<PERSON>, Italian footballer (b. 1972)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Egyptian dentist and politician (b. 1974)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian dentist and politician (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian dentist and politician (b. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 8th Baron <PERSON>, English diplomat (b. 1912)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-<PERSON>,_8th_Baron_<PERSON><PERSON>\" title=\"<PERSON><PERSON>, 8th Baron <PERSON>\"><PERSON>-<PERSON>, 8th Baron <PERSON></a>, English diplomat (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-<PERSON>,_8th_Baron_<PERSON>\" title=\"<PERSON>-<PERSON><PERSON>, 8th Baron <PERSON>\"><PERSON>-<PERSON>, 8th Baron <PERSON></a>, English diplomat (b. 1912)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 8th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON>,_8th_Baron_<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian activist (b. 1962)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian activist (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian activist (b. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English author and scholar (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and scholar (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and scholar (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American illustrator (b. 1954)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rampier\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Trampier\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, English-Israeli diplomat (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Avner\"><PERSON><PERSON><PERSON></a>, English-Israeli diplomat (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>vner\"><PERSON><PERSON><PERSON></a>, English-Israeli diplomat (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ner"}]}, {"year": "2015", "text": "notable deaths of the Germanwings Flight 9525 crash:\n<PERSON><PERSON>, Kazakhstani-German opera singer (b. 1960)\n<PERSON>, German opera singer (b. 1981)", "html": "2015 - notable deaths of the <a href=\"https://wikipedia.org/wiki/Germanwings_Flight_9525\" title=\"Germanwings Flight 9525\">Germanwings Flight 9525</a> crash:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani-German opera singer (b. 1960)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German opera singer (b. 1981)</li>\n</ul>", "no_year_html": "notable deaths of the <a href=\"https://wikipedia.org/wiki/Germanwings_Flight_9525\" title=\"Germanwings Flight 9525\">Germanwings Flight 9525</a> crash:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani-German opera singer (b. 1960)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German opera singer (b. 1981)</li>\n</ul>", "links": [{"title": "Germanwings Flight 9525", "link": "https://wikipedia.org/wiki/Germanwings_Flight_9525"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, Kazakhstani-German opera singer (b. 1960)", "text": null, "html": "<PERSON><PERSON>, Kazakhstani-German opera singer (b. 1960) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Ole<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani-German opera singer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani-German opera singer (b. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON>, German opera singer (b. 1981)", "text": null, "html": "<PERSON>, German opera singer (b. 1981) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German opera singer (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German opera singer (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Dutch footballer (b. 1947)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American comedian, actor, and screenwriter (b. 1949)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and screenwriter (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and screenwriter (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Swiss singer and First Winner of the Eurovision Song Contest (b. 1924)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Lys_Assia\" title=\"Lys Assia\"><PERSON><PERSON> Assia</a>, Swiss singer and First Winner of the <a href=\"https://wikipedia.org/wiki/Eurovision_Song_Contest\" title=\"Eurovision Song Contest\">Eurovision Song Contest</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lys_Assia\" title=\"Lys Assia\"><PERSON><PERSON> Assia</a>, Swiss singer and First Winner of the <a href=\"https://wikipedia.org/wiki/Eurovision_Song_Contest\" title=\"Eurovision Song Contest\">Eurovision Song Contest</a> (b. 1924)", "links": [{"title": "Lys Assia", "link": "https://wikipedia.org/wiki/Lys_Assia"}, {"title": "Eurovision Song Contest", "link": "https://wikipedia.org/wiki/Eurovision_Song_Contest"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Palestinian singer, composer, arranger and activist (b. 1966)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian singer, <a href=\"https://wikipedia.org/wiki/Composer\" title=\"Composer\">composer</a>, <a href=\"https://wikipedia.org/wiki/Arranger\" class=\"mw-redirect\" title=\"Arranger\">arranger</a> and activist (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian singer, <a href=\"https://wikipedia.org/wiki/Composer\" title=\"Composer\">composer</a>, <a href=\"https://wikipedia.org/wiki/Arranger\" class=\"mw-redirect\" title=\"Arranger\">arranger</a> and activist (b. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON>na"}, {"title": "Composer", "link": "https://wikipedia.org/wiki/Composer"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>nger"}]}, {"year": "2019", "text": "<PERSON>, American film and voice actor (b. 1949)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film and voice actor (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film and voice actor (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, French comic book artist (b. 1927)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French comic book artist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French comic book artist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Cameroonian musician and songwriter (b. 1933)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cameroon\" title=\"Cameroon\">Cameroonian</a> musician and songwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cameroon\" title=\"Cameroon\">Cameroonian</a> musician and songwriter (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Cameroon", "link": "https://wikipedia.org/wiki/Cameroon"}]}, {"year": "2021", "text": "<PERSON>, American actress and voice artist (b. 1941)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Swedish blogger and influencer (b. 1912)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish blogger and influencer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish blogger and influencer (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American businessman, engineer and co-founder of Intel Corporation (b. 1929)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, engineer and co-founder of <a href=\"https://wikipedia.org/wiki/Intel_Corporation\" class=\"mw-redirect\" title=\"Intel Corporation\">Intel Corporation</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, engineer and co-founder of <a href=\"https://wikipedia.org/wiki/Intel_Corporation\" class=\"mw-redirect\" title=\"Intel Corporation\">Intel Corporation</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Intel Corporation", "link": "https://wikipedia.org/wiki/Intel_Corporation"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Indian writer and director (b. 1955)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian writer and director (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian writer and director (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>r"}]}]}}