{"date": "January 31", "url": "https://wikipedia.org/wiki/January_31", "data": {"Events": [{"year": "314", "text": "<PERSON> <PERSON> is consecrated, as successor to the late <PERSON> <PERSON><PERSON><PERSON><PERSON>.", "html": "314 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Sylvester I\">Pope <PERSON> I</a> is consecrated, as successor to the late <a href=\"https://wikipedia.org/wiki/Pope_Miltiades\" title=\"Pope Miltiades\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Sylvester <PERSON>\">Pope <PERSON> I</a> is consecrated, as successor to the late <a href=\"https://wikipedia.org/wiki/<PERSON>_Miltia<PERSON>\" title=\"Pope Miltia<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Miltiades"}]}, {"year": "1208", "text": "The Battle of Lena takes place between King <PERSON><PERSON><PERSON> of Sweden and his rival, Prince <PERSON>, whose victory puts him on the throne as King <PERSON> of Sweden.", "html": "1208 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Lena\" title=\"Battle of Lena\">Battle of Lena</a> takes place between King <a href=\"https://wikipedia.org/wiki/Sverker_II_of_Sweden\" class=\"mw-redirect\" title=\"Sverker II of Sweden\"><PERSON><PERSON><PERSON> II of Sweden</a> and his rival, Prince <PERSON>, whose victory puts him on the throne as King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Lena\" title=\"Battle of Lena\">Battle of Lena</a> takes place between King <a href=\"https://wikipedia.org/wiki/Sverker_II_of_Sweden\" class=\"mw-redirect\" title=\"Sverker II of Sweden\"><PERSON><PERSON><PERSON> of Sweden</a> and his rival, Prince <PERSON>, whose victory puts him on the throne as King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a>.", "links": [{"title": "Battle of Lena", "link": "https://wikipedia.org/wiki/Battle_of_Lena"}, {"title": "Sverker II of Sweden", "link": "https://wikipedia.org/wiki/Sverker_II_of_Sweden"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1266", "text": "The Mudéjar of Murcia, who had rebelled against the Crown of Castile during the Mudéjar revolt of 1264-1266, surrender the city to <PERSON> of Aragon after a siege lasting a month.", "html": "1266 - The <a href=\"https://wikipedia.org/wiki/Mud%C3%A9jar\" title=\"Mudéjar\">Mudéjar</a> of Murcia, who had rebelled against the <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Crown of Castile</a> during the <a href=\"https://wikipedia.org/wiki/Mud%C3%A9jar_revolt_of_1264%E2%80%931266\" title=\"Mudéjar revolt of 1264-1266\">Mudéjar revolt of 1264-1266</a>, surrender the city to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> after a <a href=\"https://wikipedia.org/wiki/Conquest_of_Murcia_(1265%E2%80%931266)\" title=\"Conquest of Murcia (1265-1266)\">siege lasting a month</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mud%C3%A9jar\" title=\"Mudéjar\">Mudéjar</a> of Murcia, who had rebelled against the <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Crown of Castile</a> during the <a href=\"https://wikipedia.org/wiki/Mud%C3%A9jar_revolt_of_1264%E2%80%931266\" title=\"Mudéjar revolt of 1264-1266\">Mudéjar revolt of 1264-1266</a>, surrender the city to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> after a <a href=\"https://wikipedia.org/wiki/Conquest_of_Murcia_(1265%E2%80%931266)\" title=\"Conquest of Murcia (1265-1266)\">siege lasting a month</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mud%C3%A9jar"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}, {"title": "Mudéjar revolt of 1264-1266", "link": "https://wikipedia.org/wiki/Mud%C3%A9jar_revolt_of_1264%E2%80%931266"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}, {"title": "Conquest of Murcia (1265-1266)", "link": "https://wikipedia.org/wiki/Conquest_of_Murcia_(1265%E2%80%931266)"}]}, {"year": "1504", "text": "The Treaty of Lyon ends the Italian War, confirming French domination of northern Italy, while Spain receives the Kingdom of Naples.", "html": "1504 - The Treaty of Lyon ends the <a href=\"https://wikipedia.org/wiki/Italian_War_of_1499%E2%80%931504\" class=\"mw-redirect\" title=\"Italian War of 1499-1504\">Italian War</a>, confirming French domination of northern Italy, while Spain receives the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Naples\" title=\"Kingdom of Naples\">Kingdom of Naples</a>.", "no_year_html": "The Treaty of Lyon ends the <a href=\"https://wikipedia.org/wiki/Italian_War_of_1499%E2%80%931504\" class=\"mw-redirect\" title=\"Italian War of 1499-1504\">Italian War</a>, confirming French domination of northern Italy, while Spain receives the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Naples\" title=\"Kingdom of Naples\">Kingdom of Naples</a>.", "links": [{"title": "Italian War of 1499-1504", "link": "https://wikipedia.org/wiki/Italian_War_of_1499%E2%80%931504"}, {"title": "Kingdom of Naples", "link": "https://wikipedia.org/wiki/Kingdom_of_Naples"}]}, {"year": "1578", "text": "Eighty Years' War and Anglo-Spanish War: The Battle of Gembloux is a victory for Spanish forces led by <PERSON> of Austria over a rebel army of Dutch, Flemish, English, Scottish, German, French and Walloons.", "html": "1578 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a> and <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)\" title=\"Anglo-Spanish War (1585-1604)\">Anglo-Spanish War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Gembloux_(1578)\" title=\"Battle of Gembloux (1578)\">Battle of Gembloux</a> is a victory for Spanish forces led by <a href=\"https://wikipedia.org/wiki/Don_<PERSON>_of_Austria\" class=\"mw-redirect\" title=\"Don <PERSON> of Austria\">Don <PERSON> of Austria</a> over a rebel army of <a href=\"https://wikipedia.org/wiki/Dutch_people\" title=\"Dutch people\">Dutch</a>, <a href=\"https://wikipedia.org/wiki/Flemish_people\" title=\"Flemish people\">Flemish</a>, <a href=\"https://wikipedia.org/wiki/English_people\" title=\"English people\">English</a>, <a href=\"https://wikipedia.org/wiki/Scottish_people\" title=\"Scottish people\">Scottish</a>, <a href=\"https://wikipedia.org/wiki/German_language\" title=\"German language\">German</a>, <a href=\"https://wikipedia.org/wiki/French_people\" title=\"French people\">French</a> and <a href=\"https://wikipedia.org/wiki/Walloons\" title=\"Walloons\">Walloons</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a> and <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)\" title=\"Anglo-Spanish War (1585-1604)\">Anglo-Spanish War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Gembloux_(1578)\" title=\"Battle of Gembloux (1578)\">Battle of Gembloux</a> is a victory for Spanish forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" class=\"mw-redirect\" title=\"Don <PERSON> of Austria\"><PERSON> of Austria</a> over a rebel army of <a href=\"https://wikipedia.org/wiki/Dutch_people\" title=\"Dutch people\">Dutch</a>, <a href=\"https://wikipedia.org/wiki/Flemish_people\" title=\"Flemish people\">Flemish</a>, <a href=\"https://wikipedia.org/wiki/English_people\" title=\"English people\">English</a>, <a href=\"https://wikipedia.org/wiki/Scottish_people\" title=\"Scottish people\">Scottish</a>, <a href=\"https://wikipedia.org/wiki/German_language\" title=\"German language\">German</a>, <a href=\"https://wikipedia.org/wiki/French_people\" title=\"French people\">French</a> and <a href=\"https://wikipedia.org/wiki/Walloons\" title=\"Walloons\">Walloons</a>.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Anglo-Spanish War (1585-1604)", "link": "https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)"}, {"title": "Battle of Gembloux (1578)", "link": "https://wikipedia.org/wiki/Battle_of_Gembloux_(1578)"}, {"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Austria"}, {"title": "Dutch people", "link": "https://wikipedia.org/wiki/Dutch_people"}, {"title": "Flemish people", "link": "https://wikipedia.org/wiki/Flemish_people"}, {"title": "English people", "link": "https://wikipedia.org/wiki/English_people"}, {"title": "Scottish people", "link": "https://wikipedia.org/wiki/Scottish_people"}, {"title": "German language", "link": "https://wikipedia.org/wiki/German_language"}, {"title": "French people", "link": "https://wikipedia.org/wiki/French_people"}, {"title": "Walloons", "link": "https://wikipedia.org/wiki/Walloons"}]}, {"year": "1606", "text": "Gunpowder Plot: Four of the conspirators, including <PERSON>, are executed for treason by hanging, drawing and quartering, for plotting against <PERSON> and <PERSON>.", "html": "1606 - <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a>: Four of the conspirators, including <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, are executed for treason by <a href=\"https://wikipedia.org/wiki/Hanged,_drawn_and_quartered\" title=\"Hanged, drawn and quartered\">hanging, drawing and quartering</a>, for plotting against <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">Parliament</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_<PERSON>\" title=\"<PERSON> VI and I\">King <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a>: Four of the conspirators, including <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, are executed for treason by <a href=\"https://wikipedia.org/wiki/Hanged,_drawn_and_quartered\" title=\"Hanged, drawn and quartered\">hanging, drawing and quartering</a>, for plotting against <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">Parliament</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_<PERSON>\" title=\"<PERSON> VI and I\">King <PERSON></a>.", "links": [{"title": "Gunpowder Plot", "link": "https://wikipedia.org/wiki/Gunpowder_Plot"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hanged, drawn and quartered", "link": "https://wikipedia.org/wiki/Hanged,_drawn_and_quartered"}, {"title": "Parliament of England", "link": "https://wikipedia.org/wiki/Parliament_of_England"}, {"title": "James <PERSON> and I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_I"}]}, {"year": "1609", "text": " Wisselbank of Amsterdam established", "html": "1609 - <a href=\"https://wikipedia.org/wiki/Bank_of_Amsterdam\" title=\"Bank of Amsterdam\">Wisselbank of Amsterdam</a> established", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bank_of_Amsterdam\" title=\"Bank of Amsterdam\">Wisselbank of Amsterdam</a> established", "links": [{"title": "Bank of Amsterdam", "link": "https://wikipedia.org/wiki/Bank_of_Amsterdam"}]}, {"year": "1703", "text": " Forty-seven r<PERSON>nin, under the command of <PERSON><PERSON>, avenged the death of their master, by killing <PERSON>.", "html": "1703 - <a href=\"https://wikipedia.org/wiki/Forty-seven_r%C5%8Dnin\" title=\"Forty-seven rōnin\">Forty-seven rōnin</a>, under the command of <a href=\"https://wikipedia.org/wiki/%C5%8<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, avenged the death of their master, by killing <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Forty-seven_r%C5%8Dnin\" title=\"Forty-seven rōnin\">Forty-seven rōnin</a>, under the command of <a href=\"https://wikipedia.org/wiki/%C5%8<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, avenged the death of their master, by killing <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Forty-seven rōnin", "link": "https://wikipedia.org/wiki/Forty-seven_r%C5%8Dnin"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%8C<PERSON>_<PERSON><PERSON>o"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1747", "text": "The first venereal diseases clinic opens at London Lock Hospital.", "html": "1747 - The first <a href=\"https://wikipedia.org/wiki/Venereal_disease\" class=\"mw-redirect\" title=\"Venereal disease\">venereal diseases</a> clinic opens at <a href=\"https://wikipedia.org/wiki/London_Lock_Hospital\" title=\"London Lock Hospital\">London Lock Hospital</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Venereal_disease\" class=\"mw-redirect\" title=\"Venereal disease\">venereal diseases</a> clinic opens at <a href=\"https://wikipedia.org/wiki/London_Lock_Hospital\" title=\"London Lock Hospital\">London Lock Hospital</a>.", "links": [{"title": "Venereal disease", "link": "https://wikipedia.org/wiki/Venereal_disease"}, {"title": "London Lock Hospital", "link": "https://wikipedia.org/wiki/London_Lock_Hospital"}]}, {"year": "1814", "text": "<PERSON><PERSON><PERSON><PERSON> becomes Supreme Director of the United Provinces of the Río de la Plata (present-day Argentina).", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Ger<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">Ger<PERSON><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Supreme_Director_of_the_United_Provinces_of_the_R%C3%ADo_de_la_Plata\" title=\"Supreme Director of the United Provinces of the Río de la Plata\">Supreme Director of the United Provinces of the Río de la Plata</a> (present-day Argentina).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ger<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">Ger<PERSON><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Supreme_Director_of_the_United_Provinces_of_the_R%C3%ADo_de_la_Plata\" title=\"Supreme Director of the United Provinces of the Río de la Plata\">Supreme Director of the United Provinces of the Río de la Plata</a> (present-day Argentina).", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ger<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Supreme Director of the United Provinces of the Río de la Plata", "link": "https://wikipedia.org/wiki/Supreme_Director_of_the_United_Provinces_of_the_R%C3%ADo_de_la_Plata"}]}, {"year": "1846", "text": "After the Milwaukee Bridge War, the United States towns of Juneautown and Kilbourntown unify to create the City of Milwaukee.", "html": "1846 - After the <a href=\"https://wikipedia.org/wiki/Milwaukee_Bridge_War\" title=\"Milwaukee Bridge War\">Milwaukee Bridge War</a>, the United States towns of Juneautown and Kilbourntown unify to create the City of <a href=\"https://wikipedia.org/wiki/Milwaukee\" title=\"Milwaukee\">Milwaukee</a>.", "no_year_html": "After the <a href=\"https://wikipedia.org/wiki/Milwaukee_Bridge_War\" title=\"Milwaukee Bridge War\">Milwaukee Bridge War</a>, the United States towns of Juneautown and Kilbourntown unify to create the City of <a href=\"https://wikipedia.org/wiki/Milwaukee\" title=\"Milwaukee\">Milwaukee</a>.", "links": [{"title": "Milwaukee Bridge War", "link": "https://wikipedia.org/wiki/Milwaukee_Bridge_War"}, {"title": "Milwaukee", "link": "https://wikipedia.org/wiki/Milwaukee"}]}, {"year": "1848", "text": "<PERSON> is court-martialed for mutiny and disobeying orders.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fr%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Court-martial\" title=\"Court-martial\">court-martialed</a> for mutiny and disobeying orders.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fr%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Court-martial\" title=\"Court-martial\">court-martialed</a> for mutiny and disobeying orders.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_<PERSON>._Fr%C3%A9mont"}, {"title": "Court-martial", "link": "https://wikipedia.org/wiki/Court-martial"}]}, {"year": "1862", "text": "<PERSON><PERSON> discovers the white dwarf star <PERSON> B, a companion of Sirius, through an 18.5-inch (47 cm) telescope now located at Northwestern University.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/White_dwarf\" title=\"White dwarf\">white dwarf</a> star <PERSON> B, a companion of <a href=\"https://wikipedia.org/wiki/Sirius\" title=\"Sirius\">Sirius</a>, through an 18.5-inch (47 cm) telescope now located at <a href=\"https://wikipedia.org/wiki/Northwestern_University\" title=\"Northwestern University\">Northwestern University</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/White_dwarf\" title=\"White dwarf\">white dwarf</a> star <PERSON> B, a companion of <a href=\"https://wikipedia.org/wiki/Sirius\" title=\"Sirius\">Sirius</a>, through an 18.5-inch (47 cm) telescope now located at <a href=\"https://wikipedia.org/wiki/Northwestern_University\" title=\"Northwestern University\">Northwestern University</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "White dwarf", "link": "https://wikipedia.org/wiki/White_dwarf"}, {"title": "Sirius", "link": "https://wikipedia.org/wiki/Sirius"}, {"title": "Northwestern University", "link": "https://wikipedia.org/wiki/Northwestern_University"}]}, {"year": "1865", "text": "American Civil War: The United States Congress passes the Thirteenth Amendment to the United States Constitution, abolishing slavery, and submits it to the states for ratification.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution\" title=\"Thirteenth Amendment to the United States Constitution\">Thirteenth Amendment to the United States Constitution</a>, abolishing slavery, and submits it to the states for ratification.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution\" title=\"Thirteenth Amendment to the United States Constitution\">Thirteenth Amendment to the United States Constitution</a>, abolishing slavery, and submits it to the states for ratification.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Thirteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution"}]}, {"year": "1865", "text": "American Civil War: Confederate General <PERSON> becomes general-in-chief of all Confederate armies.", "html": "1865 - American Civil War: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes general-in-chief of all Confederate armies.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes general-in-chief of all Confederate armies.", "links": [{"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "History of Portugal: The first attempt at a Portuguese republican revolution breaks out in the northern city of Porto.", "html": "1891 - <a href=\"https://wikipedia.org/wiki/History_of_Portugal_(1834%E2%80%931910)\" title=\"History of Portugal (1834-1910)\">History of Portugal</a>: The first attempt at a Portuguese <a href=\"https://wikipedia.org/wiki/Republicanism\" title=\"Republicanism\">republican</a> revolution breaks out in the northern city of <a href=\"https://wikipedia.org/wiki/Porto\" title=\"Porto\">Porto</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/History_of_Portugal_(1834%E2%80%931910)\" title=\"History of Portugal (1834-1910)\">History of Portugal</a>: The first attempt at a Portuguese <a href=\"https://wikipedia.org/wiki/Republicanism\" title=\"Republicanism\">republican</a> revolution breaks out in the northern city of <a href=\"https://wikipedia.org/wiki/Porto\" title=\"Porto\">Porto</a>.", "links": [{"title": "History of Portugal (1834-1910)", "link": "https://wikipedia.org/wiki/History_of_Portugal_(1834%E2%80%931910)"}, {"title": "Republicanism", "link": "https://wikipedia.org/wiki/Republicanism"}, {"title": "Porto", "link": "https://wikipedia.org/wiki/Porto"}]}, {"year": "1900", "text": "<PERSON><PERSON> is killed in Kampung Teboh, Tambunan, ending the Mat Salleh Rebellion.", "html": "1900 - <PERSON><PERSON> is killed in Kampung Teboh, Tambunan, ending the <a href=\"https://wikipedia.org/wiki/Mat_<PERSON>_Rebellion\" title=\"Mat Salleh Rebellion\">Mat Salleh Rebellion</a>.", "no_year_html": "<PERSON><PERSON> is killed in Kampung Teboh, Tambunan, ending the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rebellion\" title=\"Mat Salleh Rebellion\">Mat Salleh Rebellion</a>.", "links": [{"title": "Mat <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>'s Three Sisters premieres at Moscow Art Theatre in Russia.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Three_Sisters_(play)\" title=\"Three Sisters (play)\">Three Sisters</a></i> premieres at <a href=\"https://wikipedia.org/wiki/Moscow_Art_Theatre\" title=\"Moscow Art Theatre\">Moscow Art Theatre</a> in Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Three_Sisters_(play)\" title=\"Three Sisters (play)\">Three Sisters</a></i> premieres at <a href=\"https://wikipedia.org/wiki/Moscow_Art_Theatre\" title=\"Moscow Art Theatre\">Moscow Art Theatre</a> in Russia.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Three Sisters (play)", "link": "https://wikipedia.org/wiki/Three_Sisters_(play)"}, {"title": "Moscow Art Theatre", "link": "https://wikipedia.org/wiki/Moscow_Art_Theatre"}]}, {"year": "1915", "text": "World War I: Germany is the first to make large-scale use of poison gas in warfare in the Battle of Bolimów against Russia.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">Germany</a> is the first to make large-scale use of <a href=\"https://wikipedia.org/wiki/Chemical_weapons_in_World_War_I\" title=\"Chemical weapons in World War I\">poison gas</a> in warfare in the <a href=\"https://wikipedia.org/wiki/Battle_of_Bolim%C3%B3w\" class=\"mw-redirect\" title=\"Battle of Bolimów\">Battle of Bolimów</a> against <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">Germany</a> is the first to make large-scale use of <a href=\"https://wikipedia.org/wiki/Chemical_weapons_in_World_War_I\" title=\"Chemical weapons in World War I\">poison gas</a> in warfare in the <a href=\"https://wikipedia.org/wiki/Battle_of_Bolim%C3%B3w\" class=\"mw-redirect\" title=\"Battle of Bolimów\">Battle of Bolimów</a> against <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}, {"title": "Chemical weapons in World War I", "link": "https://wikipedia.org/wiki/Chemical_weapons_in_World_War_I"}, {"title": "Battle of Bolimów", "link": "https://wikipedia.org/wiki/Battle_of_Bolim%C3%B3w"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}]}, {"year": "1917", "text": "World War I: Kaiser Wilhelm II orders the resumption of unrestricted submarine warfare.", "html": "1917 - World War I: Kaiser <PERSON> orders the <a href=\"https://wikipedia.org/wiki/U-boat_Campaign#1917:_Resumption_of_unrestricted_submarine_warfare\" class=\"mw-redirect\" title=\"U-boat Campaign\">resumption of unrestricted submarine warfare</a>.", "no_year_html": "World War I: Kaiser <PERSON> orders the <a href=\"https://wikipedia.org/wiki/U-boat_Campaign#1917:_Resumption_of_unrestricted_submarine_warfare\" class=\"mw-redirect\" title=\"U-boat Campaign\">resumption of unrestricted submarine warfare</a>.", "links": [{"title": "U-boat Campaign", "link": "https://wikipedia.org/wiki/U-boat_Campaign#1917:_Resumption_of_unrestricted_submarine_warfare"}]}, {"year": "1918", "text": "A series of accidental collisions on a misty Scottish night leads to the loss of two Royal Navy submarines with over a hundred lives, and damage to another five British warships.", "html": "1918 - A <a href=\"https://wikipedia.org/wiki/Battle_of_May_Island\" title=\"Battle of May Island\">series of accidental collisions</a> on a misty Scottish night leads to the loss of two <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarines</a> with over a hundred lives, and damage to another five British warships.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Battle_of_May_Island\" title=\"Battle of May Island\">series of accidental collisions</a> on a misty Scottish night leads to the loss of two <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarines</a> with over a hundred lives, and damage to another five British warships.", "links": [{"title": "Battle of May Island", "link": "https://wikipedia.org/wiki/Battle_of_May_Island"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Submarine", "link": "https://wikipedia.org/wiki/Submarine"}]}, {"year": "1918", "text": "Finnish Civil War: The Suinula massacre, which changes the nature of the war in a more hostile direction, takes place in Kangasala.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/List_of_massacres_in_the_Finnish_Civil_War\" title=\"List of massacres in the Finnish Civil War\">Suinula massacre</a>, which changes the nature of the war in a more hostile direction, takes place in <a href=\"https://wikipedia.org/wiki/Kangasala\" title=\"Kangasala\">Kangasala</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/List_of_massacres_in_the_Finnish_Civil_War\" title=\"List of massacres in the Finnish Civil War\">Suinula massacre</a>, which changes the nature of the war in a more hostile direction, takes place in <a href=\"https://wikipedia.org/wiki/Kangasala\" title=\"Kangasala\">Kangasala</a>.", "links": [{"title": "Finnish Civil War", "link": "https://wikipedia.org/wiki/Finnish_Civil_War"}, {"title": "List of massacres in the Finnish Civil War", "link": "https://wikipedia.org/wiki/List_of_massacres_in_the_Finnish_Civil_War"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kangasala"}]}, {"year": "1919", "text": "The Battle of George Square takes place in Glasgow, Scotland, during a campaign for shorter working hours.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Battle_of_George_Square\" title=\"Battle of George Square\">Battle of George Square</a> takes place in <a href=\"https://wikipedia.org/wiki/Glasgow\" title=\"Glasgow\">Glasgow</a>, Scotland, during a campaign for shorter working hours.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_George_Square\" title=\"Battle of George Square\">Battle of George Square</a> takes place in <a href=\"https://wikipedia.org/wiki/Glasgow\" title=\"Glasgow\">Glasgow</a>, Scotland, during a campaign for shorter working hours.", "links": [{"title": "Battle of George Square", "link": "https://wikipedia.org/wiki/Battle_of_George_Square"}, {"title": "Glasgow", "link": "https://wikipedia.org/wiki/Glasgow"}]}, {"year": "1928", "text": "<PERSON> is exiled to Alma-Ata.", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is exiled to <a href=\"https://wikipedia.org/wiki/Alma-Ata\" class=\"mw-redirect\" title=\"Alma-Ata\"><PERSON>-<PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is exiled to <a href=\"https://wikipedia.org/wiki/Alma-Ata\" class=\"mw-redirect\" title=\"Alma-Ata\"><PERSON>-<PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Alma-At<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-Ata"}]}, {"year": "1942", "text": "World War II: Allied forces are defeated by the Japanese at the Battle of Malaya and retreat to Singapore.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Allied forces are defeated by the Japanese at the <a href=\"https://wikipedia.org/wiki/Battle_of_Malaya\" class=\"mw-redirect\" title=\"Battle of Malaya\">Battle of Malaya</a> and retreat to Singapore.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Allied forces are defeated by the Japanese at the <a href=\"https://wikipedia.org/wiki/Battle_of_Malaya\" class=\"mw-redirect\" title=\"Battle of Malaya\">Battle of Malaya</a> and retreat to Singapore.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Malaya", "link": "https://wikipedia.org/wiki/Battle_of_Malaya"}]}, {"year": "1943", "text": "World War II: German field marshal <PERSON> surrenders to the Soviets at Stalingrad, followed two days later by the remainder of his Sixth Army, ending one of the war's fiercest battles.", "html": "1943 - World War II: German field marshal <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> surrenders to the Soviets at <a href=\"https://wikipedia.org/wiki/Battle_of_Stalingrad\" title=\"Battle of Stalingrad\">Stalingrad</a>, followed two days later by the remainder of his Sixth Army, ending one of the war's fiercest battles.", "no_year_html": "World War II: German field marshal <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> surrenders to the Soviets at <a href=\"https://wikipedia.org/wiki/Battle_of_Stalingrad\" title=\"Battle of Stalingrad\">Stalingrad</a>, followed two days later by the remainder of his Sixth Army, ending one of the war's fiercest battles.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Stalingrad", "link": "https://wikipedia.org/wiki/Battle_of_Stalingrad"}]}, {"year": "1944", "text": "World War II: American forces land on Kwajalein Atoll and other islands in the Japanese-held Marshall Islands.", "html": "1944 - World War II: American forces land on <a href=\"https://wikipedia.org/wiki/Kwajalein_Atoll\" title=\"Kwajalein Atoll\">Kwajalein Atoll</a> and other islands in the Japanese-held <a href=\"https://wikipedia.org/wiki/Marshall_Islands\" title=\"Marshall Islands\">Marshall Islands</a>.", "no_year_html": "World War II: American forces land on <a href=\"https://wikipedia.org/wiki/Kwajalein_Atoll\" title=\"Kwajalein Atoll\">Kwajalein Atoll</a> and other islands in the Japanese-held <a href=\"https://wikipedia.org/wiki/Marshall_Islands\" title=\"Marshall Islands\">Marshall Islands</a>.", "links": [{"title": "Kwajalein Atoll", "link": "https://wikipedia.org/wiki/Kwajalein_Atoll"}, {"title": "Marshall Islands", "link": "https://wikipedia.org/wiki/Marshall_Islands"}]}, {"year": "1944", "text": "World War II: During the Anzio campaign, the 1st Ranger Battalion (Darby's Rangers) is destroyed behind enemy lines in a heavily outnumbered encounter at Battle of Cisterna, Italy.", "html": "1944 - World War II: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Anzio\" title=\"Battle of Anzio\">Anzio campaign</a>, the <a href=\"https://wikipedia.org/wiki/1st_Ranger_Battalion\" title=\"1st Ranger Battalion\">1st Ranger Battalion</a> (<PERSON><PERSON>'s Rangers) is destroyed behind enemy lines in a heavily outnumbered encounter at <a href=\"https://wikipedia.org/wiki/Battle_of_Cisterna\" title=\"Battle of Cisterna\">Battle of Cisterna</a>, Italy.", "no_year_html": "World War II: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Anzio\" title=\"Battle of Anzio\">Anzio campaign</a>, the <a href=\"https://wikipedia.org/wiki/1st_Ranger_Battalion\" title=\"1st Ranger Battalion\">1st Ranger Battalion</a> (<PERSON><PERSON>'s Rangers) is destroyed behind enemy lines in a heavily outnumbered encounter at <a href=\"https://wikipedia.org/wiki/Battle_of_Cisterna\" title=\"Battle of Cisterna\">Battle of Cisterna</a>, Italy.", "links": [{"title": "Battle of Anzio", "link": "https://wikipedia.org/wiki/Battle_of_Anzio"}, {"title": "1st Ranger Battalion", "link": "https://wikipedia.org/wiki/1st_Ranger_Battalion"}, {"title": "Battle of Cisterna", "link": "https://wikipedia.org/wiki/Battle_of_Cisterna"}]}, {"year": "1945", "text": "US Army private <PERSON> is executed for desertion, the first such execution of an American soldier since the Civil War.", "html": "1945 - US Army private <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">executed</a> for <a href=\"https://wikipedia.org/wiki/Desertion\" title=\"Desertion\">desertion</a>, the first such execution of an American soldier since the <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">Civil War</a>.", "no_year_html": "US Army private <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">executed</a> for <a href=\"https://wikipedia.org/wiki/Desertion\" title=\"Desertion\">desertion</a>, the first such execution of an American soldier since the <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">Civil War</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Capital punishment", "link": "https://wikipedia.org/wiki/Capital_punishment"}, {"title": "Desertion", "link": "https://wikipedia.org/wiki/Desertion"}, {"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}]}, {"year": "1945", "text": "World War II: About 3,000 inmates from the Stutthof concentration camp are forcibly marched into the Baltic Sea at Palmnicken (now Yantarny, Russia) and executed.", "html": "1945 - World War II: About 3,000 inmates from the <a href=\"https://wikipedia.org/wiki/Stutthof_concentration_camp\" title=\"Stutthof concentration camp\">Stutthof concentration camp</a> are forcibly marched into the <a href=\"https://wikipedia.org/wiki/Baltic_Sea\" title=\"Baltic Sea\">Baltic Sea</a> at Palmnicken (now <a href=\"https://wikipedia.org/wiki/Yantarny,_Kaliningrad_Oblast\" title=\"Yantarny, Kaliningrad Oblast\">Yantarny</a>, Russia) and executed.", "no_year_html": "World War II: About 3,000 inmates from the <a href=\"https://wikipedia.org/wiki/Stutthof_concentration_camp\" title=\"Stutthof concentration camp\">Stutthof concentration camp</a> are forcibly marched into the <a href=\"https://wikipedia.org/wiki/Baltic_Sea\" title=\"Baltic Sea\">Baltic Sea</a> at Palmnicken (now <a href=\"https://wikipedia.org/wiki/Yantarny,_Kaliningrad_Oblast\" title=\"Yantarny, Kaliningrad Oblast\">Yantarny</a>, Russia) and executed.", "links": [{"title": "Stutthof concentration camp", "link": "https://wikipedia.org/wiki/Stutthof_concentration_camp"}, {"title": "Baltic Sea", "link": "https://wikipedia.org/wiki/Baltic_Sea"}, {"title": "Yantarny, Kaliningrad Oblast", "link": "https://wikipedia.org/wiki/Yantarny,_Kaliningrad_Oblast"}]}, {"year": "1945", "text": "World War II: The end of fighting in the Battle of Hill 170 during the Burma Campaign, in which the British 3 Commando Brigade repulsed a Japanese counterattack on their positions and precipitated a general retirement from the Arakan Peninsula.", "html": "1945 - World War II: The end of fighting in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hill_170\" title=\"Battle of Hill 170\">Battle of Hill 170</a> during the <a href=\"https://wikipedia.org/wiki/Burma_Campaign\" class=\"mw-redirect\" title=\"Burma Campaign\">Burma Campaign</a>, in which the British <a href=\"https://wikipedia.org/wiki/3_Commando_Brigade\" class=\"mw-redirect\" title=\"3 Commando Brigade\">3 Commando Brigade</a> repulsed a Japanese counterattack on their positions and precipitated a general retirement from the <a href=\"https://wikipedia.org/wiki/Rakhine_State\" title=\"Rakhine State\">Arakan</a> Peninsula.", "no_year_html": "World War II: The end of fighting in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hill_170\" title=\"Battle of Hill 170\">Battle of Hill 170</a> during the <a href=\"https://wikipedia.org/wiki/Burma_Campaign\" class=\"mw-redirect\" title=\"Burma Campaign\">Burma Campaign</a>, in which the British <a href=\"https://wikipedia.org/wiki/3_Commando_Brigade\" class=\"mw-redirect\" title=\"3 Commando Brigade\">3 Commando Brigade</a> repulsed a Japanese counterattack on their positions and precipitated a general retirement from the <a href=\"https://wikipedia.org/wiki/Rakhine_State\" title=\"Rakhine State\">Arakan</a> Peninsula.", "links": [{"title": "Battle of Hill 170", "link": "https://wikipedia.org/wiki/Battle_of_Hill_170"}, {"title": "Burma Campaign", "link": "https://wikipedia.org/wiki/Burma_Campaign"}, {"title": "3 Commando Brigade", "link": "https://wikipedia.org/wiki/3_Commando_Brigade"}, {"title": "Rakhine State", "link": "https://wikipedia.org/wiki/Rakhine_State"}]}, {"year": "1946", "text": "Cold War: Yugoslavia's new constitution, modeling that of the Soviet Union, establishes six constituent republics (Bosnia and Herzegovina, Croatia, Macedonia, Montenegro, Serbia and Slovenia).", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a>'s new <a href=\"https://wikipedia.org/wiki/1946_Yugoslav_Constitution\" title=\"1946 Yugoslav Constitution\">constitution</a>, modeling that of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, establishes six constituent republics (<a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Bosnia_and_Herzegovina\" title=\"Socialist Republic of Bosnia and Herzegovina\">Bosnia and Herzegovina</a>, <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Croatia\" title=\"Socialist Republic of Croatia\">Croatia</a>, <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Macedonia\" title=\"Socialist Republic of Macedonia\">Macedonia</a>, <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Montenegro\" title=\"Socialist Republic of Montenegro\">Montenegro</a>, <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Serbia\" title=\"Socialist Republic of Serbia\">Serbia</a> and <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Slovenia\" title=\"Socialist Republic of Slovenia\">Slovenia</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a>'s new <a href=\"https://wikipedia.org/wiki/1946_Yugoslav_Constitution\" title=\"1946 Yugoslav Constitution\">constitution</a>, modeling that of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, establishes six constituent republics (<a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Bosnia_and_Herzegovina\" title=\"Socialist Republic of Bosnia and Herzegovina\">Bosnia and Herzegovina</a>, <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Croatia\" title=\"Socialist Republic of Croatia\">Croatia</a>, <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Macedonia\" title=\"Socialist Republic of Macedonia\">Macedonia</a>, <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Montenegro\" title=\"Socialist Republic of Montenegro\">Montenegro</a>, <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Serbia\" title=\"Socialist Republic of Serbia\">Serbia</a> and <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Slovenia\" title=\"Socialist Republic of Slovenia\">Slovenia</a>).", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}, {"title": "1946 Yugoslav Constitution", "link": "https://wikipedia.org/wiki/1946_Yugoslav_Constitution"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Socialist Republic of Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Socialist_Republic_of_Bosnia_and_Herzegovina"}, {"title": "Socialist Republic of Croatia", "link": "https://wikipedia.org/wiki/Socialist_Republic_of_Croatia"}, {"title": "Socialist Republic of Macedonia", "link": "https://wikipedia.org/wiki/Socialist_Republic_of_Macedonia"}, {"title": "Socialist Republic of Montenegro", "link": "https://wikipedia.org/wiki/Socialist_Republic_of_Montenegro"}, {"title": "Socialist Republic of Serbia", "link": "https://wikipedia.org/wiki/Socialist_Republic_of_Serbia"}, {"title": "Socialist Republic of Slovenia", "link": "https://wikipedia.org/wiki/Socialist_Republic_of_Slovenia"}]}, {"year": "1946", "text": "The Democratic Republic of Vietnam introduces the đồng to replace the French Indochinese piastre at par.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">Democratic Republic of Vietnam</a> introduces the <a href=\"https://wikipedia.org/wiki/North_Vietnamese_%C4%91%E1%BB%93ng\" title=\"North Vietnamese đồng\">đồng</a> to replace the <a href=\"https://wikipedia.org/wiki/French_Indochinese_piastre\" title=\"French Indochinese piastre\">French Indochinese piastre</a> at par.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">Democratic Republic of Vietnam</a> introduces the <a href=\"https://wikipedia.org/wiki/North_Vietnamese_%C4%91%E1%BB%93ng\" title=\"North Vietnamese đồng\">đồng</a> to replace the <a href=\"https://wikipedia.org/wiki/French_Indochinese_piastre\" title=\"French Indochinese piastre\">French Indochinese piastre</a> at par.", "links": [{"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}, {"title": "North Vietnamese đồng", "link": "https://wikipedia.org/wiki/North_Vietnamese_%C4%91%E1%BB%93ng"}, {"title": "French Indochinese piastre", "link": "https://wikipedia.org/wiki/French_Indochinese_piastre"}]}, {"year": "1949", "text": "These Are My Children, the first television daytime soap opera, is broadcast by the NBC station in Chicago, United States.", "html": "1949 - <i><a href=\"https://wikipedia.org/wiki/These_Are_My_Children\" title=\"These Are My Children\">These Are My Children</a></i>, the first television daytime <a href=\"https://wikipedia.org/wiki/Soap_opera\" title=\"Soap opera\">soap opera</a>, is broadcast by the NBC station in Chicago, United States.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/These_Are_My_Children\" title=\"These Are My Children\">These Are My Children</a></i>, the first television daytime <a href=\"https://wikipedia.org/wiki/Soap_opera\" title=\"Soap opera\">soap opera</a>, is broadcast by the NBC station in Chicago, United States.", "links": [{"title": "These Are My Children", "link": "https://wikipedia.org/wiki/These_Are_My_Children"}, {"title": "Soap opera", "link": "https://wikipedia.org/wiki/Soap_opera"}]}, {"year": "1951", "text": "United Nations Security Council Resolution 90 relating to the Korean War is adopted.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_90\" title=\"United Nations Security Council Resolution 90\">United Nations Security Council Resolution 90</a> relating to the <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a> is adopted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_90\" title=\"United Nations Security Council Resolution 90\">United Nations Security Council Resolution 90</a> relating to the <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a> is adopted.", "links": [{"title": "United Nations Security Council Resolution 90", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_90"}, {"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}]}, {"year": "1953", "text": "A North Sea flood causes over 1,800 deaths in the Netherlands and over 300 in the United Kingdom.", "html": "1953 - A <a href=\"https://wikipedia.org/wiki/North_Sea_flood_of_1953\" title=\"North Sea flood of 1953\">North Sea flood</a> causes over 1,800 deaths in the Netherlands and over 300 in the United Kingdom.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/North_Sea_flood_of_1953\" title=\"North Sea flood of 1953\">North Sea flood</a> causes over 1,800 deaths in the Netherlands and over 300 in the United Kingdom.", "links": [{"title": "North Sea flood of 1953", "link": "https://wikipedia.org/wiki/North_Sea_flood_of_1953"}]}, {"year": "1957", "text": "Eight people (five total crew from two aircraft and three on the ground) in Pacoima, California are killed following the mid-air collision between a Douglas DC-7 airliner and a Northrop F-89 Scorpion fighter jet.", "html": "1957 - Eight people (five total crew from two aircraft and three on the ground) in <a href=\"https://wikipedia.org/wiki/Pacoima,_Los_Angeles\" title=\"Pacoima, Los Angeles\">Pacoima, California</a> are killed following the <a href=\"https://wikipedia.org/wiki/Pacoima_aircraft_accident\" class=\"mw-redirect\" title=\"Pacoima aircraft accident\">mid-air collision</a> between a <a href=\"https://wikipedia.org/wiki/Douglas_DC-7\" title=\"Douglas DC-7\">Douglas DC-7</a> airliner and a <a href=\"https://wikipedia.org/wiki/Northrop_F-89_Scorpion\" title=\"Northrop F-89 Scorpion\">Northrop F-89 Scorpion</a> fighter jet.", "no_year_html": "Eight people (five total crew from two aircraft and three on the ground) in <a href=\"https://wikipedia.org/wiki/Pacoima,_Los_Angeles\" title=\"Pacoima, Los Angeles\">Pacoima, California</a> are killed following the <a href=\"https://wikipedia.org/wiki/Pacoima_aircraft_accident\" class=\"mw-redirect\" title=\"Pacoima aircraft accident\">mid-air collision</a> between a <a href=\"https://wikipedia.org/wiki/Douglas_DC-7\" title=\"Douglas DC-7\">Douglas DC-7</a> airliner and a <a href=\"https://wikipedia.org/wiki/Northrop_F-89_Scorpion\" title=\"Northrop F-89 Scorpion\">Northrop F-89 Scorpion</a> fighter jet.", "links": [{"title": "Pacoima, Los Angeles", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Los_Angeles"}, {"title": "Pacoima aircraft accident", "link": "https://wikipedia.org/wiki/Pacoima_aircraft_accident"}, {"title": "Douglas DC-7", "link": "https://wikipedia.org/wiki/Douglas_DC-7"}, {"title": "Northrop F-89 Scorpion", "link": "https://wikipedia.org/wiki/Northrop_F-89_Scorpion"}]}, {"year": "1958", "text": "Cold War: Space Race: The Explorer 1, the first successful American satellite, detects the Van Allen radiation belt.", "html": "1958 - Cold War: <a href=\"https://wikipedia.org/wiki/Space_Race\" title=\"Space Race\">Space Race</a>: The <a href=\"https://wikipedia.org/wiki/Explorer_1\" title=\"Explorer 1\">Explorer 1</a>, the first successful American satellite, detects the <a href=\"https://wikipedia.org/wiki/Van_<PERSON>_radiation_belt\" title=\"Van Allen radiation belt\">Van Allen radiation belt</a>.", "no_year_html": "Cold War: <a href=\"https://wikipedia.org/wiki/Space_Race\" title=\"Space Race\">Space Race</a>: The <a href=\"https://wikipedia.org/wiki/Explorer_1\" title=\"Explorer 1\">Explorer 1</a>, the first successful American satellite, detects the <a href=\"https://wikipedia.org/wiki/Van_<PERSON>_radiation_belt\" title=\"Van Allen radiation belt\">Van Allen radiation belt</a>.", "links": [{"title": "Space Race", "link": "https://wikipedia.org/wiki/Space_Race"}, {"title": "Explorer 1", "link": "https://wikipedia.org/wiki/Explorer_1"}, {"title": "<PERSON> radiation belt", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_radiation_belt"}]}, {"year": "1961", "text": "Project Mercury: Mercury-Redstone 2: The chimpanzee <PERSON> travels into outer space.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>: <a href=\"https://wikipedia.org/wiki/Mercury-Redstone_2\" title=\"Mercury-Redstone 2\">Mercury-Redstone 2</a>: The chimpanzee <a href=\"https://wikipedia.org/wiki/Ham_(chimpanzee)\" title=\"Ham (chimpanzee)\">Ham</a> travels into <a href=\"https://wikipedia.org/wiki/Space_science\" class=\"mw-redirect\" title=\"Space science\">outer space</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>: <a href=\"https://wikipedia.org/wiki/Mercury-Redstone_2\" title=\"Mercury-Redstone 2\">Mercury-Redstone 2</a>: The chimpanzee <a href=\"https://wikipedia.org/wiki/Ham_(chimpanzee)\" title=\"Ham (chimpanzee)\">Ham</a> travels into <a href=\"https://wikipedia.org/wiki/Space_science\" class=\"mw-redirect\" title=\"Space science\">outer space</a>.", "links": [{"title": "Project Mercury", "link": "https://wikipedia.org/wiki/Project_Mercury"}, {"title": "Mercury-Redstone 2", "link": "https://wikipedia.org/wiki/Mercury-Redstone_2"}, {"title": "<PERSON> (chimpanzee)", "link": "https://wikipedia.org/wiki/<PERSON>_(chimpanzee)"}, {"title": "Space science", "link": "https://wikipedia.org/wiki/Space_science"}]}, {"year": "1966", "text": "The Soviet Union launches the unmanned Luna 9 spacecraft as part of the Luna program.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches the unmanned <a href=\"https://wikipedia.org/wiki/Luna_9\" title=\"Luna 9\">Luna 9</a> spacecraft as part of the <a href=\"https://wikipedia.org/wiki/Luna_programme\" title=\"Luna programme\">Luna program</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches the unmanned <a href=\"https://wikipedia.org/wiki/Luna_9\" title=\"Luna 9\">Luna 9</a> spacecraft as part of the <a href=\"https://wikipedia.org/wiki/Luna_programme\" title=\"Luna programme\">Luna program</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Luna 9", "link": "https://wikipedia.org/wiki/Luna_9"}, {"title": "Luna programme", "link": "https://wikipedia.org/wiki/Luna_programme"}]}, {"year": "1968", "text": "Vietnam War: Viet Cong guerrillas attack the United States embassy in Saigon, and other attacks, in the early morning hours, later grouped together as the Tet Offensive.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> guerrillas attack the United States embassy in <a href=\"https://wikipedia.org/wiki/Saigon\" class=\"mw-redirect\" title=\"Saigon\">Saigon</a>, and other attacks, in the early morning hours, later grouped together as the <a href=\"https://wikipedia.org/wiki/Tet_Offensive\" title=\"Tet Offensive\">Tet Offensive</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> guerrillas attack the United States embassy in <a href=\"https://wikipedia.org/wiki/Saigon\" class=\"mw-redirect\" title=\"Saigon\">Saigon</a>, and other attacks, in the early morning hours, later grouped together as the <a href=\"https://wikipedia.org/wiki/Tet_Offensive\" title=\"Tet Offensive\">Tet Offensive</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}, {"title": "Saigon", "link": "https://wikipedia.org/wiki/Saigon"}, {"title": "Tet Offensive", "link": "https://wikipedia.org/wiki/Tet_Offensive"}]}, {"year": "1971", "text": "Apollo program: Apollo 14: Astronauts <PERSON>, <PERSON>, and <PERSON>, aboard a Saturn V, lift off for a mission to the Fra Mauro Highlands on the Moon.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_14\" title=\"Apollo 14\">Apollo 14</a>: Astronauts <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, aboard a <a href=\"https://wikipedia.org/wiki/Saturn_V\" title=\"Saturn V\">Saturn V</a>, lift off for a mission to the <a href=\"https://wikipedia.org/wiki/Fra_Mauro_formation\" title=\"Fra Mauro formation\">Fra Mauro Highlands</a> on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_14\" title=\"Apollo 14\">Apollo 14</a>: Astronauts <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, aboard a <a href=\"https://wikipedia.org/wiki/Saturn_V\" title=\"Saturn V\">Saturn V</a>, lift off for a mission to the <a href=\"https://wikipedia.org/wiki/Fra_Mauro_formation\" title=\"Fra Mauro formation\">Fra Mauro Highlands</a> on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 14", "link": "https://wikipedia.org/wiki/Apollo_14"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Saturn V", "link": "https://wikipedia.org/wiki/Saturn_V"}, {"title": "Fra Mauro formation", "link": "https://wikipedia.org/wiki/Fra_<PERSON><PERSON>_formation"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}]}, {"year": "1971", "text": "The Winter Soldier Investigation, organized by the Vietnam Veterans Against the War to publicize alleged war crimes and atrocities by Americans and allies in Vietnam, begins in Detroit.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Winter_Soldier_Investigation\" title=\"Winter Soldier Investigation\">Winter Soldier Investigation</a>, organized by the <a href=\"https://wikipedia.org/wiki/Vietnam_Veterans_Against_the_War\" title=\"Vietnam Veterans Against the War\">Vietnam Veterans Against the War</a> to publicize alleged <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a> and atrocities by Americans and allies in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>, begins in <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Winter_Soldier_Investigation\" title=\"Winter Soldier Investigation\">Winter Soldier Investigation</a>, organized by the <a href=\"https://wikipedia.org/wiki/Vietnam_Veterans_Against_the_War\" title=\"Vietnam Veterans Against the War\">Vietnam Veterans Against the War</a> to publicize alleged <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a> and atrocities by Americans and allies in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>, begins in <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>.", "links": [{"title": "Winter Soldier Investigation", "link": "https://wikipedia.org/wiki/Winter_Soldier_Investigation"}, {"title": "Vietnam Veterans Against the War", "link": "https://wikipedia.org/wiki/Vietnam_Veterans_Against_the_War"}, {"title": "War crime", "link": "https://wikipedia.org/wiki/War_crime"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}, {"title": "Detroit", "link": "https://wikipedia.org/wiki/Detroit"}]}, {"year": "1978", "text": "The Crown of St. Stephen (also known as the Holy Crown of Hungary) goes on public display after being returned to Hungary from the United States, where it was held after World War II.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/Holy_Crown_of_Hungary\" title=\"Holy Crown of Hungary\">Crown of St. Stephen</a> (also known as the Holy Crown of Hungary) goes on public display after being returned to Hungary from the United States, where it was held after <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Holy_Crown_of_Hungary\" title=\"Holy Crown of Hungary\">Crown of <PERSON>. Stephen</a> (also known as the Holy Crown of Hungary) goes on public display after being returned to Hungary from the United States, where it was held after <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "links": [{"title": "Holy Crown of Hungary", "link": "https://wikipedia.org/wiki/Holy_Crown_of_Hungary"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1988", "text": "<PERSON> becomes the first African American quarterback to play in a Super Bowl and leads the Washington Redskins to victory in Super Bowl XXII.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>(quarterback)\" title=\"<PERSON> (quarterback)\"><PERSON></a> becomes the first African American quarterback to play in a <a href=\"https://wikipedia.org/wiki/Super_Bowl\" title=\"Super Bowl\">Super Bowl</a> and leads the <a href=\"https://wikipedia.org/wiki/Washington_Redskins\" class=\"mw-redirect\" title=\"Washington Redskins\">Washington Redskins</a> to victory in <a href=\"https://wikipedia.org/wiki/Super_Bowl_XXII\" title=\"Super Bowl XXII\">Super Bowl XXII</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(quarterback)\" title=\"<PERSON> (quarterback)\"><PERSON></a> becomes the first African American quarterback to play in a <a href=\"https://wikipedia.org/wiki/Super_Bowl\" title=\"Super Bowl\">Super Bowl</a> and leads the <a href=\"https://wikipedia.org/wiki/Washington_Redskins\" class=\"mw-redirect\" title=\"Washington Redskins\">Washington Redskins</a> to victory in <a href=\"https://wikipedia.org/wiki/Super_Bowl_XXII\" title=\"Super Bowl XXII\">Super Bowl XXII</a>.", "links": [{"title": "<PERSON> (quarterback)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(quarterback)"}, {"title": "Super Bowl", "link": "https://wikipedia.org/wiki/Super_Bowl"}, {"title": "Washington Redskins", "link": "https://wikipedia.org/wiki/Washington_Redskins"}, {"title": "Super Bowl XXII", "link": "https://wikipedia.org/wiki/Super_Bowl_XXII"}]}, {"year": "1996", "text": "An explosives-filled truck rams into the gates of the Central Bank of Sri Lanka in Colombo, killing at least 86 people and injuring 1,400.", "html": "1996 - An explosives-filled truck rams into the gates of the <a href=\"https://wikipedia.org/wiki/Central_Bank_of_Sri_Lanka\" title=\"Central Bank of Sri Lanka\">Central Bank of Sri Lanka</a> in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a>, killing at least 86 people and injuring 1,400.", "no_year_html": "An explosives-filled truck rams into the gates of the <a href=\"https://wikipedia.org/wiki/Central_Bank_of_Sri_Lanka\" title=\"Central Bank of Sri Lanka\">Central Bank of Sri Lanka</a> in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a>, killing at least 86 people and injuring 1,400.", "links": [{"title": "Central Bank of Sri Lanka", "link": "https://wikipedia.org/wiki/Central_Bank_of_Sri_Lanka"}, {"title": "Colombo", "link": "https://wikipedia.org/wiki/Colombo"}]}, {"year": "2000", "text": "Alaska Airlines Flight 261 crash: An MD-83, experiencing horizontal stabilizer problems, crashes in the Pacific Ocean off the coast of Point Mugu, California, killing all 88 aboard.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Alaska_Airlines_Flight_261\" title=\"Alaska Airlines Flight 261\">Alaska Airlines Flight 261</a> crash: An <a href=\"https://wikipedia.org/wiki/MD-83\" class=\"mw-redirect\" title=\"MD-83\">MD-83</a>, experiencing horizontal stabilizer problems, crashes in the Pacific Ocean off the coast of <a href=\"https://wikipedia.org/wiki/Point_Mugu,_California\" class=\"mw-redirect\" title=\"Point Mugu, California\">Point Mugu, California</a>, killing all 88 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alaska_Airlines_Flight_261\" title=\"Alaska Airlines Flight 261\">Alaska Airlines Flight 261</a> crash: An <a href=\"https://wikipedia.org/wiki/MD-83\" class=\"mw-redirect\" title=\"MD-83\">MD-83</a>, experiencing horizontal stabilizer problems, crashes in the Pacific Ocean off the coast of <a href=\"https://wikipedia.org/wiki/Point_Mugu,_California\" class=\"mw-redirect\" title=\"Point Mugu, California\">Point Mugu, California</a>, killing all 88 aboard.", "links": [{"title": "Alaska Airlines Flight 261", "link": "https://wikipedia.org/wiki/Alaska_Airlines_Flight_261"}, {"title": "MD-83", "link": "https://wikipedia.org/wiki/MD-83"}, {"title": "Point Mugu, California", "link": "https://wikipedia.org/wiki/Point_Mugu,_California"}]}, {"year": "2001", "text": "In the Netherlands, a Scottish court convicts Libyan <PERSON><PERSON><PERSON><PERSON> and acquits another Libyan citizen for their part in the bombing of Pan Am Flight 103 over Lockerbie, Scotland in 1988.", "html": "2001 - In the Netherlands, a Scottish court convicts <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libyan</a> <a href=\"https://wikipedia.org/wiki/Abd<PERSON><PERSON><PERSON>_al-Megrahi\" title=\"<PERSON><PERSON><PERSON><PERSON> al-Megrahi\"><PERSON><PERSON><PERSON><PERSON> al-Megrahi</a> and acquits another Libyan citizen for their part in the bombing of <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_103\" title=\"Pan Am Flight 103\">Pan Am Flight 103</a> over <a href=\"https://wikipedia.org/wiki/Lockerbie\" title=\"Lockerbie\">Locke<PERSON>ie</a>, Scotland in 1988.", "no_year_html": "In the Netherlands, a Scottish court convicts <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libyan</a> <a href=\"https://wikipedia.org/wiki/Abdelbaset_al-Megrahi\" title=\"<PERSON><PERSON><PERSON>t al-Megrahi\"><PERSON><PERSON><PERSON><PERSON>-Megrahi</a> and acquits another Libyan citizen for their part in the bombing of <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_103\" title=\"Pan Am Flight 103\">Pan Am Flight 103</a> over <a href=\"https://wikipedia.org/wiki/Lockerbie\" title=\"Lockerbie\">Lockerbie</a>, Scotland in 1988.", "links": [{"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}, {"title": "Abdelbaset al-Megrahi", "link": "https://wikipedia.org/wiki/Abdelbaset_al-Megrahi"}, {"title": "Pan Am Flight 103", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_103"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ie"}]}, {"year": "2001", "text": "Two Japan Airlines planes nearly collide over Suruga Bay in Japan.", "html": "2001 - Two <a href=\"https://wikipedia.org/wiki/Japan_Airlines\" title=\"Japan Airlines\">Japan Airlines</a> planes <a href=\"https://wikipedia.org/wiki/2001_Japan_Airlines_mid-air_incident\" title=\"2001 Japan Airlines mid-air incident\">nearly collide</a> over <a href=\"https://wikipedia.org/wiki/Suruga_Bay\" title=\"Suruga Bay\">Suruga Bay</a> in Japan.", "no_year_html": "Two <a href=\"https://wikipedia.org/wiki/Japan_Airlines\" title=\"Japan Airlines\">Japan Airlines</a> planes <a href=\"https://wikipedia.org/wiki/2001_Japan_Airlines_mid-air_incident\" title=\"2001 Japan Airlines mid-air incident\">nearly collide</a> over <a href=\"https://wikipedia.org/wiki/Suruga_Bay\" title=\"Suruga Bay\">Suruga Bay</a> in Japan.", "links": [{"title": "Japan Airlines", "link": "https://wikipedia.org/wiki/Japan_Airlines"}, {"title": "2001 Japan Airlines mid-air incident", "link": "https://wikipedia.org/wiki/2001_Japan_Airlines_mid-air_incident"}, {"title": "Suruga Bay", "link": "https://wikipedia.org/wiki/Suruga_Bay"}]}, {"year": "2003", "text": "The Waterfall rail accident occurs near Waterfall, New South Wales, Australia.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Waterfall_rail_accident\" title=\"Waterfall rail accident\">Waterfall rail accident</a> occurs near <a href=\"https://wikipedia.org/wiki/Waterfall,_New_South_Wales\" title=\"Waterfall, New South Wales\">Waterfall, New South Wales</a>, Australia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Waterfall_rail_accident\" title=\"Waterfall rail accident\">Waterfall rail accident</a> occurs near <a href=\"https://wikipedia.org/wiki/Waterfall,_New_South_Wales\" title=\"Waterfall, New South Wales\">Waterfall, New South Wales</a>, Australia.", "links": [{"title": "Waterfall rail accident", "link": "https://wikipedia.org/wiki/Waterfall_rail_accident"}, {"title": "Waterfall, New South Wales", "link": "https://wikipedia.org/wiki/Waterfall,_New_South_Wales"}]}, {"year": "2007", "text": "Emergency officials in Boston mistakenly identified battery-powered LED placards depicting characters from Aqua Teen Hunger Force as Improvised explosive devices (IEDs), causing a panic.", "html": "2007 - Emergency officials in Boston mistakenly identified battery-powered LED placards depicting characters from <i><a href=\"https://wikipedia.org/wiki/Aqua_Teen_Hunger_Force\" title=\"Aqua Teen Hunger Force\">Aqua Teen Hunger Force</a></i> as <a href=\"https://wikipedia.org/wiki/Improvised_explosive_device\" title=\"Improvised explosive device\">Improvised explosive devices</a> (IEDs), causing <a href=\"https://wikipedia.org/wiki/2007_Boston_Mooninite_panic\" title=\"2007 Boston Mooninite panic\">a panic</a>.", "no_year_html": "Emergency officials in Boston mistakenly identified battery-powered LED placards depicting characters from <i><a href=\"https://wikipedia.org/wiki/Aqua_Teen_Hunger_Force\" title=\"Aqua Teen Hunger Force\">Aqua Teen Hunger Force</a></i> as <a href=\"https://wikipedia.org/wiki/Improvised_explosive_device\" title=\"Improvised explosive device\">Improvised explosive devices</a> (IEDs), causing <a href=\"https://wikipedia.org/wiki/2007_Boston_Mooninite_panic\" title=\"2007 Boston Mooninite panic\">a panic</a>.", "links": [{"title": "Aqua Teen Hunger Force", "link": "https://wikipedia.org/wiki/Aqua_Teen_Hunger_Force"}, {"title": "Improvised explosive device", "link": "https://wikipedia.org/wiki/Improvised_explosive_device"}, {"title": "2007 Boston Mooninite panic", "link": "https://wikipedia.org/wiki/2007_Boston_Mooninite_panic"}]}, {"year": "2009", "text": "At least 113 people are killed in Kenya and over 200 injured following an oil spillage ignition in Molo, days after a massive fire at a Nakumatt supermarket in Nairobi killed at least 25 people.", "html": "2009 - At least 113 people are killed in <a href=\"https://wikipedia.org/wiki/Kenya\" title=\"Kenya\">Kenya</a> and over 200 injured following an <a href=\"https://wikipedia.org/wiki/Molo_fire\" class=\"mw-redirect\" title=\"Molo fire\">oil spillage ignition</a> in <a href=\"https://wikipedia.org/wiki/Molo,_Kenya\" title=\"Molo, Kenya\">Molo</a>, days after <a href=\"https://wikipedia.org/wiki/2009_Nakumatt_supermarket_fire\" title=\"2009 Nakumatt supermarket fire\">a massive fire</a> at a <a href=\"https://wikipedia.org/wiki/Nakumatt\" title=\"Nakumatt\">Nakumatt</a> supermarket in <a href=\"https://wikipedia.org/wiki/Nairobi\" title=\"Nairobi\">Nairobi</a> killed at least 25 people.", "no_year_html": "At least 113 people are killed in <a href=\"https://wikipedia.org/wiki/Kenya\" title=\"Kenya\">Kenya</a> and over 200 injured following an <a href=\"https://wikipedia.org/wiki/Molo_fire\" class=\"mw-redirect\" title=\"Molo fire\">oil spillage ignition</a> in <a href=\"https://wikipedia.org/wiki/Molo,_Kenya\" title=\"Molo, Kenya\">Molo</a>, days after <a href=\"https://wikipedia.org/wiki/2009_Nakumatt_supermarket_fire\" title=\"2009 Nakumatt supermarket fire\">a massive fire</a> at a <a href=\"https://wikipedia.org/wiki/Nakumatt\" title=\"Nakumatt\">Nakumatt</a> supermarket in <a href=\"https://wikipedia.org/wiki/Nairobi\" title=\"Nairobi\">Nairobi</a> killed at least 25 people.", "links": [{"title": "Kenya", "link": "https://wikipedia.org/wiki/Kenya"}, {"title": "Molo fire", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_fire"}, {"title": "Molo, Kenya", "link": "https://wikipedia.org/wiki/Molo,_Kenya"}, {"title": "2009 Nakumatt supermarket fire", "link": "https://wikipedia.org/wiki/2009_Nakumatt_supermarket_fire"}, {"title": "Na<PERSON>mat<PERSON>", "link": "https://wikipedia.org/wiki/Nakumatt"}, {"title": "Nairobi", "link": "https://wikipedia.org/wiki/Nairobi"}]}, {"year": "2019", "text": "<PERSON> is sworn in as the 16th Yang <PERSON><PERSON> of Malaysia.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pa<PERSON>\" title=\"<PERSON> of Pahang\"><PERSON> of Pahang</a> is sworn in as the 16th Yang <PERSON>-<PERSON><PERSON> of Malaysia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pa<PERSON>\" title=\"<PERSON> of Pahang\"><PERSON> of Pahang</a> is sworn in as the 16th Yang <PERSON>-<PERSON> of Malaysia.", "links": [{"title": "<PERSON> of Pahang", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pahang"}]}, {"year": "2020", "text": "The United Kingdom's membership within the European Union ceases in accordance with Article 50, after 47 years of being a member state.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>'s membership within the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a> ceases in accordance with <a href=\"https://wikipedia.org/wiki/Withdrawal_from_the_European_Union#Procedure\" title=\"Withdrawal from the European Union\">Article 50</a>, after 47 years of being a member state.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>'s membership within the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a> ceases in accordance with <a href=\"https://wikipedia.org/wiki/Withdrawal_from_the_European_Union#Procedure\" title=\"Withdrawal from the European Union\">Article 50</a>, after 47 years of being a member state.", "links": [{"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}, {"title": "<PERSON><PERSON><PERSON> from the European Union", "link": "https://wikipedia.org/wiki/Withdrawal_from_the_European_Union#Procedure"}]}, {"year": "2022", "text": "<PERSON>, a senior civil servant in the United Kingdom, publishes an initial version of her report on the Downing Street Partygate controversy.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(civil_servant)\" class=\"mw-redirect\" title=\"<PERSON> (civil servant)\"><PERSON></a>, a senior <a href=\"https://wikipedia.org/wiki/Civil_Service_(United_Kingdom)\" title=\"Civil Service (United Kingdom)\">civil servant</a> in the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>, publishes an initial version of her report on the <a href=\"https://wikipedia.org/wiki/Downing_Street\" title=\"Downing Street\">Downing Street</a> <a href=\"https://wikipedia.org/wiki/Partygate\" title=\"Partygate\">Partygate controversy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(civil_servant)\" class=\"mw-redirect\" title=\"<PERSON> (civil servant)\"><PERSON></a>, a senior <a href=\"https://wikipedia.org/wiki/Civil_Service_(United_Kingdom)\" title=\"Civil Service (United Kingdom)\">civil servant</a> in the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>, publishes an initial version of her report on the <a href=\"https://wikipedia.org/wiki/Downing_Street\" title=\"Downing Street\">Downing Street</a> <a href=\"https://wikipedia.org/wiki/Partygate\" title=\"Partygate\">Partygate controversy</a>.", "links": [{"title": "<PERSON> (civil servant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(civil_servant)"}, {"title": "Civil Service (United Kingdom)", "link": "https://wikipedia.org/wiki/Civil_Service_(United_Kingdom)"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Downing Street", "link": "https://wikipedia.org/wiki/Downing_Street"}, {"title": "Partygate", "link": "https://wikipedia.org/wiki/Partygate"}]}, {"year": "2023", "text": "The last Boeing 747, the first wide-body airliner, is delivered.", "html": "2023 - The last <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a>, the first <a href=\"https://wikipedia.org/wiki/Wide-body_airliner\" class=\"mw-redirect\" title=\"Wide-body airliner\">wide-body airliner</a>, is delivered.", "no_year_html": "The last <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a>, the first <a href=\"https://wikipedia.org/wiki/Wide-body_airliner\" class=\"mw-redirect\" title=\"Wide-body airliner\">wide-body airliner</a>, is delivered.", "links": [{"title": "Boeing 747", "link": "https://wikipedia.org/wiki/Boeing_747"}, {"title": "Wide-body airliner", "link": "https://wikipedia.org/wiki/Wide-body_airliner"}]}, {"year": "2025", "text": "Med Jets Flight 056 crashes near Roosevelt Mall in Philadelphia, Pennsylvania, killing 7 people and injuring 19.", "html": "2025 - <a href=\"https://wikipedia.org/wiki/Med_Jets_Flight_056\" title=\"Med Jets Flight 056\">Med Jets Flight 056</a> crashes near <a href=\"https://wikipedia.org/wiki/Roosevelt_Mall\" title=\"Roosevelt Mall\">Roosevelt Mall</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, Pennsylvania, killing 7 people and injuring 19.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Med_Jets_Flight_056\" title=\"Med Jets Flight 056\">Med Jets Flight 056</a> crashes near <a href=\"https://wikipedia.org/wiki/Roosevelt_Mall\" title=\"Roosevelt Mall\">Roosevelt Mall</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, Pennsylvania, killing 7 people and injuring 19.", "links": [{"title": "Med Jets Flight 056", "link": "https://wikipedia.org/wiki/Med_Jets_Flight_056"}, {"title": "Roosevelt Mall", "link": "https://wikipedia.org/wiki/Roosevelt_Mall"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}], "Births": [{"year": "1512", "text": "<PERSON>, King of Portugal (d. 1580)", "html": "1512 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Portugal\" title=\"<PERSON>, King of Portugal\"><PERSON>, King of Portugal</a> (d. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Portugal\" title=\"<PERSON>, King of Portugal\"><PERSON>, King of Portugal</a> (d. 1580)", "links": [{"title": "<PERSON>, King of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_Portugal"}]}, {"year": "1543", "text": "<PERSON>, Japanese shōgun (d. 1616)", "html": "1543 - <a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa <PERSON>u</a>, Japanese shōgun (d. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa I<PERSON>u</a>, Japanese shōgun (d. 1616)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>u"}]}, {"year": "1583", "text": "<PERSON>, English and later American Puritan (d. 1659)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English and later American Puritan (d. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English and later American Puritan (d. 1659)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1597", "text": "<PERSON>, French priest and saint (d. 1640)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and saint (d. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and saint (d. 1640)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, 7th Earl of Derby (d. 1651)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Earl_of_Derby\" title=\"<PERSON>, 7th Earl of Derby\"><PERSON>, 7th Earl of Derby</a> (d. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Earl_of_Derby\" title=\"<PERSON>, 7th Earl of Derby\"><PERSON>, 7th Earl of Derby</a> (d. 1651)", "links": [{"title": "<PERSON>, 7th Earl of Derby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Earl_of_Derby"}]}, {"year": "1624", "text": "<PERSON>, Flemish philosopher and academic (d. 1669)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish philosopher and academic (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish philosopher and academic (d. 1669)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1673", "text": "<PERSON>, French priest and saint (d. 1716)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and saint (d. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and saint (d. 1716)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, Norwegian missionary and explorer (d. 1758)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian missionary and explorer (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian missionary and explorer (d. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON><PERSON><PERSON><PERSON>, American lawyer, politician, and diplomat, United States Ambassador to France (d. 1816)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>uve<PERSON><PERSON>_<PERSON>\" title=\"Gouve<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uve<PERSON><PERSON>_<PERSON>\" title=\"Gouverne<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (d. 1816)", "links": [{"title": "Gouve<PERSON>ur Morris", "link": "https://wikipedia.org/wiki/Gouverneur_Morris"}, {"title": "United States Ambassador to France", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_France"}]}, {"year": "1759", "text": "<PERSON>, French flute player and composer (d. 1803)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French flute player and composer (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French flute player and composer (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>enne"}]}, {"year": "1769", "text": "<PERSON><PERSON><PERSON>, French balloonist and the inventor of the frameless parachute (d. 1823)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French balloonist and the inventor of the frameless parachute (d. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French balloonist and the inventor of the frameless parachute (d. 1823)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, Czech cookbook author (d. 1845)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/Magdalena_Dobromila_Rettigov%C3%A1\" title=\"Magdalena Dobromila <PERSON>\"><PERSON></a>, Czech cookbook author (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magdalena_Dobromila_Rettigov%C3%A1\" title=\"Magdalena Dobromila <PERSON>\"><PERSON></a>, Czech cookbook author (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Magdalena_Dobromila_Rettigov%C3%A1"}]}, {"year": "1797", "text": "<PERSON>, Austrian pianist and composer (d. 1828)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Schubert\"><PERSON></a>, Austrian pianist and composer (d. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, Swiss teacher, author, painter, cartoonist, and caricaturist (d. 1846)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_T%C3%B6pffer\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss teacher, author, painter, cartoonist, and caricaturist (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_T%C3%B6pffer\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss teacher, author, painter, cartoonist, and caricaturist (d. 1846)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_T%C3%B6pffer"}]}, {"year": "1820", "text": "<PERSON>, American politician, 28th Governor of Massachusetts (d. 1887)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 28th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 28th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON> of Hawaii (d. 1874)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Lunalilo\" title=\"Lunali<PERSON>\"><PERSON><PERSON><PERSON></a> of Hawaii (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lunalilo\" title=\"Lunali<PERSON>\"><PERSON><PERSON><PERSON></a> of Hawaii (d. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Romanian mathematician and academic (d. 1941)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Romanian mathematician and academic (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Romanian mathematician and academic (d. 1941)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(mathematician)"}]}, {"year": "1865", "text": "<PERSON>, French cyclist and journalist (d. 1940)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist and journalist (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist and journalist (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian spiritual leader, founded BAPS (d. 1951)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Maharaj\" title=\"<PERSON><PERSON><PERSON><PERSON> Maharaj\"><PERSON><PERSON><PERSON><PERSON></a>, Indian spiritual leader, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>chasan<PERSON><PERSON>urus<PERSON> Swaminarayan Sanstha\">BAPS</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Maharaj\" title=\"<PERSON><PERSON><PERSON><PERSON> Maharaj\"><PERSON><PERSON><PERSON><PERSON></a>, Indian spiritual leader, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Bochasan<PERSON><PERSON> Shri Aks<PERSON>urushottam Swaminarayan Sanstha\">BAPS</a> (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>haraj"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Shri <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 1928)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1872", "text": "<PERSON>, American author (d. 1939)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Grey\"><PERSON></a>, American author (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American chemist and physicist, Nobel Prize laureate (d. 1957)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1884", "text": "<PERSON>, German journalist and politician, 1st President of the Federal Republic of Germany (d. 1963)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Federal_Republic_of_Germany\" class=\"mw-redirect\" title=\"President of the Federal Republic of Germany\">President of the Federal Republic of Germany</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Federal_Republic_of_Germany\" class=\"mw-redirect\" title=\"President of the Federal Republic of Germany\">President of the Federal Republic of Germany</a> (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Federal Republic of Germany", "link": "https://wikipedia.org/wiki/President_of_the_Federal_Republic_of_Germany"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Azerbaijani scholar and politician, 1st President of The Democratic Republic of Azerbaijan (d. 1955)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Mammad_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Mammad <PERSON>\">Mammad <PERSON></a>, Azerbaijani scholar and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Azerbaijan\" class=\"mw-redirect\" title=\"List of heads of government of Azerbaijan\">President of The Democratic Republic of Azerbaijan</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mamma<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Mammad <PERSON>\">Mammad <PERSON></a>, Azerbaijani scholar and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Azerbaijan\" class=\"mw-redirect\" title=\"List of heads of government of Azerbaijan\">President of The Democratic Republic of Azerbaijan</a> (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of government of Azerbaijan", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Azerbaijan"}]}, {"year": "1889", "text": "<PERSON>, English cricketer (d. 1958)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1958)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1892", "text": "<PERSON>, American singer-songwriter, actor, and dancer (d. 1964)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, actor, and dancer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, actor, and dancer (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON>, American saxophonist, composer, and bandleader (d. 1956)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist, composer, and bandleader (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist, composer, and bandleader (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Russian mathematician and historian (d. 1966)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and historian (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and historian (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American artist, art dealer and collector (d. 1982)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist, art dealer and collector (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist, art dealer and collector (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Canadian businessman, founded White Spot (d. 1978)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/White_Spot\" title=\"White Spot\">White Spot</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/White_Spot\" title=\"White Spot\">White Spot</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White Spot", "link": "https://wikipedia.org/wiki/White_Spot"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, American actress (d. 1968)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Tallulah_Bankhead\" title=\"Tallulah Bankhead\"><PERSON><PERSON><PERSON></a>, American actress (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tallulah_Bankhead\" title=\"Tallulah Bankhead\"><PERSON><PERSON><PERSON></a>, American actress (d. 1968)", "links": [{"title": "Tallulah Bankhead", "link": "https://wikipedia.org/wiki/Tallulah_Bankhead"}]}, {"year": "1902", "text": "<PERSON><PERSON>, Swedish sociologist and politician, Nobel Prize laureate (d. 1986)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish sociologist and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish sociologist and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>va_<PERSON>rdal"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1902", "text": "<PERSON>, American anthropologist (d. 1972)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American author, playwright, and screenwriter (d. 1970)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and screenwriter (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and screenwriter (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_<PERSON>%27Hara"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Romanian-English journalist (d. 1995)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Miron_Grindea\" title=\"Miron Grindea\"><PERSON><PERSON></a>, Romanian-English journalist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miron_Grindea\" title=\"Mir<PERSON> Grindea\"><PERSON><PERSON></a>, Romanian-English journalist (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mir<PERSON>_<PERSON>dea"}]}, {"year": "1913", "text": "<PERSON>, American football player and coach (d. 1997)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON> <PERSON>, American boxer and police officer (d. 1994)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Jersey_<PERSON>_<PERSON>\" title=\"Jersey Joe <PERSON>\">Jersey <PERSON></a>, American boxer and police officer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jersey_<PERSON>_<PERSON>\" title=\"Jersey Joe <PERSON>\">Jersey <PERSON></a>, American boxer and police officer (d. 1994)", "links": [{"title": "Jersey <PERSON>", "link": "https://wikipedia.org/wiki/Jersey_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American trumpet player and cornet player (d. 1976)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and cornet player (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and cornet player (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American historian, author, and scholar (d. 2002)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and scholar (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and scholar (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American monk and author (d. 1968)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American monk and author (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American monk and author (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American comedian and game show host (d. 1993)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and game show host (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and game show host (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American tennis player (d. 1997)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player (d. 1997)", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1917", "text": "<PERSON>, American architect and academic, founded Bassetti Architects (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and academic, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Architects\" title=\"Bassetti Architects\">Bassetti Architects</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and academic, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Architects\" title=\"Bassetti Architects\">Bassetti Architects</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Bassetti Architects", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Architects"}]}, {"year": "1919", "text": "<PERSON>, American baseball player and sportscaster (d. 1972)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American lawyer and politician, 37th United States Secretary of the Interior (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1920", "text": "<PERSON>, English footballer (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1920)\" title=\"<PERSON> (footballer, born 1920)\"><PERSON></a>, English footballer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1920)\" title=\"<PERSON> (footballer, born 1920)\"><PERSON></a>, English footballer (d. 2014)", "links": [{"title": "<PERSON> (footballer, born 1920)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1920)"}]}, {"year": "1921", "text": "<PERSON>, American actor (d. 2002)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actress, singer, and dancer (d. 2019)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American architect, designed the Thorncrown Chapel (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Thorncrown_Chapel\" title=\"Thorncrown Chapel\">Thorncrown Chapel</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Thorncrown_Chapel\" title=\"Thorncrown Chapel\">Thorncrown Chapel</a> (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Thorncrown Chapel", "link": "https://wikipedia.org/wiki/Thorncrown_Chapel"}]}, {"year": "1921", "text": "<PERSON>, American tenor and actor (d. 1959)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and actor (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actress (d. 1996)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>u"}]}, {"year": "1923", "text": "<PERSON>, American journalist and author (d. 2007)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Mailer\"><PERSON></a>, American journalist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Mailer\"><PERSON></a>, American journalist and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1925", "text": "<PERSON>, American minister, lawyer, and activist (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, lawyer, and activist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, lawyer, and activist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American baseball player (d. 1993)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American singer-songwriter (d. 1958)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American animator, producer, and composer, co-founded Filmation Studios (d. 2005)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American animator, producer, and composer, co-founded <a href=\"https://wikipedia.org/wiki/Filmation\" title=\"Filmation\">Filmation Studios</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American animator, producer, and composer, co-founded <a href=\"https://wikipedia.org/wiki/Filmation\" title=\"Filmation\">Filmation Studios</a> (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Filmation", "link": "https://wikipedia.org/wiki/Filmation"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American computer scientist and engineer (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist and engineer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist and engineer (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_<PERSON>yman"}]}, {"year": "1929", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (d. 2011)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ssbauer\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ssbauer\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rudolf_M%C3%B6<PERSON><PERSON><PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1929", "text": "<PERSON>, English-American actress (d. 2010)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Swedish race car driver (d. 1972)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish race car driver (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish race car driver (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American composer, conductor, and producer (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, conductor, and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, conductor, and producer (d. 2012)", "links": [{"title": "Al De Lory", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American baseball player and coach (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English runner, journalist, and politician (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner, journalist, and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner, journalist, and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Polish sea captain (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Miron_Babiak\" title=\"Miron Babiak\"><PERSON><PERSON></a>, Polish sea captain (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miron_Babiak\" title=\"Miron Babiak\"><PERSON><PERSON></a>, Polish sea captain (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miron_Babiak"}]}, {"year": "1933", "text": "<PERSON>, Canadian ice hockey player and coach (d. 1997)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American cardiologist and inventor (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardiologist and inventor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardiologist and inventor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Italian motorcycle racer and race car driver (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer and race car driver (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer and race car driver (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American author (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actor and producer (d. 1991)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2005)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2005)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese author and academic, Nobel Prize laureate (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Kenzabur%C5%8D_%C5%8Ce\" title=\"Kenzaburō Ōe\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese author and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kenzabur%C5%8D_%C5%8Ce\" title=\"Kenzaburō Ōe\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese author and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kenzabur%C5%8D_%C5%8Ce"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1936", "text": "<PERSON>, Turkish footballer and basketball player (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Can_Bartu\" title=\"Can Bartu\"><PERSON></a>, Turkish footballer and basketball player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Can_Bartu\" title=\"Can Bartu\"><PERSON></a>, Turkish footballer and basketball player (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Can_Bartu"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Lithuanian actor (d. 2022)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Reg<PERSON><PERSON>_Adomaitis\" title=\"Regimantas Adomaitis\"><PERSON><PERSON><PERSON> Adoma<PERSON></a>, Lithuanian actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Adomaitis\" title=\"Regimantas Adomaitis\"><PERSON><PERSON><PERSON> Adoma<PERSON></a>, Lithuanian actor (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Regiman<PERSON>_Adomaitis"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Canadian educator and politician, 39th Mayor of Quebec City (d. 2007)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9e_<PERSON>ucher\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian educator and politician, 39th <a href=\"https://wikipedia.org/wiki/Mayor_of_Quebec_City\" class=\"mw-redirect\" title=\"Mayor of Quebec City\">Mayor of Quebec City</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9e_<PERSON>ucher\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian educator and politician, 39th <a href=\"https://wikipedia.org/wiki/Mayor_of_Quebec_City\" class=\"mw-redirect\" title=\"Mayor of Quebec City\">Mayor of Quebec City</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9e_<PERSON>ucher"}, {"title": "Mayor of Quebec City", "link": "https://wikipedia.org/wiki/Mayor_of_Quebec_City"}]}, {"year": "1937", "text": "<PERSON>, American composer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Glass\"><PERSON></a>, American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actress (d. 2008)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON> of the Netherlands", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Beatrix_of_the_Netherlands\" title=\"Beatrix of the Netherlands\">Beatrix of the Netherlands</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beat<PERSON>_of_the_Netherlands\" title=\"Beatrix of the Netherlands\">Beat<PERSON> of the Netherlands</a>", "links": [{"title": "<PERSON><PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/Beat<PERSON>_of_the_Netherlands"}]}, {"year": "1938", "text": "<PERSON>, American actress", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and politician, 43rd United States Secretary of the Interior (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 43rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 43rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1939", "text": "<PERSON>, American serial killer (d. 2006)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, South African rugby player and coach (d. 1998)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African rugby player and coach (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African rugby player and coach (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American actor and director (d. 2022)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American basketball player (d. 2018)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American lawyer and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American author and illustrator (d. 2012)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American actress (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Italian actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> B<PERSON>chi\"><PERSON><PERSON></a>, Italian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English director, stage designer, and author (d. 1994)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, stage designer, and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, stage designer, and author (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Australian cricketer and coach", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John In<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John In<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Inverarity"}]}, {"year": "1944", "text": "<PERSON>, American musician and singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American historian and author (d. 2014)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and author (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, <PERSON> of Richmond, English lawyer, judge, and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Richmond\" title=\"<PERSON>, Baroness <PERSON> of Richmond\"><PERSON>, Baroness <PERSON> of Richmond</a>, English lawyer, judge, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Richmond\" title=\"<PERSON>, Baroness <PERSON> of Richmond\"><PERSON>, Baroness <PERSON> of Richmond</a>, English lawyer, judge, and academic", "links": [{"title": "<PERSON>, <PERSON> of Richmond", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Richmond"}]}, {"year": "1945", "text": "<PERSON>, American sculptor and theorist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and theorist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian journalist and radio host", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American guitarist and singer-songwriter (d. 1978)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and singer-songwriter (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and singer-songwriter (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Albanian footballer and manager (d. 2012)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Me<PERSON>_<PERSON>\" title=\"Medin <PERSON>\"><PERSON><PERSON></a>, Albanian footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Me<PERSON>_<PERSON>\" title=\"Me<PERSON>\"><PERSON><PERSON></a>, Albanian footballer and manager (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Medin_<PERSON>ga"}]}, {"year": "1947", "text": "<PERSON>, American actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American baseball player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, German footballer (d. 2014)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Volkmar_Gro%C3%9F\" title=\"Volkmar G<PERSON>ß\"><PERSON><PERSON><PERSON></a>, German footballer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Volkmar_Gro%C3%9F\" title=\"Volkmar Groß\"><PERSON><PERSON><PERSON></a>, German footballer (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Volkmar_Gro%C3%9F"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician", "links": [{"title": "Muneo Suzuki", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Dutch footballer and journalist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American model and educator (d. 2010)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Church_Mailer\" title=\"<PERSON> Church Mailer\"><PERSON></a>, American model and educator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Church_Mailer\" title=\"<PERSON> Church Mailer\"><PERSON></a>, American model and educator (d. 2010)", "links": [{"title": "<PERSON> Church Mailer", "link": "https://wikipedia.org/wiki/Norris_Church_Mailer"}]}, {"year": "1949", "text": "<PERSON>, American sociologist, philosopher, and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, philosopher, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, philosopher, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ber"}]}, {"year": "1950", "text": "<PERSON>, American author and illustrator", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Russian general and bodyguard", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and bodyguard", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and bodyguard", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American-Israeli author and poet (d. 2015)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Israeli author and poet (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Israeli author and poet (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter, pianist, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Guyanese cricketer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>ao<PERSON>_<PERSON>\" title=\"Faoud Bacchus\"><PERSON><PERSON><PERSON></a>, Guyanese cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ao<PERSON>_<PERSON>\" title=\"<PERSON>ao<PERSON> Ba<PERSON>hus\"><PERSON><PERSON><PERSON></a>, Guyanese cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Dutch guitarist and songwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Romanian tennis player and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Virginia_Ruzici\" title=\"Virginia Ruzici\"><PERSON></a>, Romanian tennis player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Ruzici\" title=\"Virginia Ruzici\"><PERSON></a>, Romanian tennis player and manager", "links": [{"title": "Virginia Ruzici", "link": "https://wikipedia.org/wiki/Virginia_Ruzici"}]}, {"year": "1956", "text": "<PERSON>, English singer-songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Dutch programmer, creator of the Python programming language", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch programmer, creator of the <a href=\"https://wikipedia.org/wiki/Python_(programming_language)\" title=\"Python (programming language)\">Python</a> programming language", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch programmer, creator of the <a href=\"https://wikipedia.org/wiki/Python_(programming_language)\" title=\"Python (programming language)\">Python</a> programming language", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Python (programming language)", "link": "https://wikipedia.org/wiki/Python_(programming_language)"}]}, {"year": "1957", "text": "<PERSON>, American swimmer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian actor and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American model and actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Iranian journalist and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Scottish author and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Montenegrin politician, 31st Prime Minister of Montenegro (d. 2014)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/%C5%BDeljko_%C5%A0turanovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Montenegrin politician, 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Montenegro\" title=\"Prime Minister of Montenegro\">Prime Minister of Montenegro</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%B<PERSON><PERSON>jko_%C5%A0turanovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Montenegrin politician, 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Montenegro\" title=\"Prime Minister of Montenegro\">Prime Minister of Montenegro</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%BDeljko_%C5%A0turanovi%C4%87"}, {"title": "Prime Minister of Montenegro", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Montenegro"}]}, {"year": "1961", "text": "<PERSON>, <PERSON>, English politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Gambian lawyer and judge", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Gambian lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Gambian lawyer and judge", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cole\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian rugby league player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American lawyer and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American journalist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American scientist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Greek basketball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, Greek basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, Greek basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Israeli-Canadian cellist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>ra_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-Canadian cellist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ra <PERSON>\"><PERSON><PERSON></a>, Israeli-Canadian cellist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ofra_<PERSON>y"}]}, {"year": "1965", "text": "<PERSON>, American author and radio host", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English actor and director", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Myanmar historian, diplomat, conservationist, and former presidential advisor.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Thant_Myint-U\" title=\"Thant Myint-U\"><PERSON><PERSON>int-<PERSON></a>, Myanmar historian, diplomat, conservationist, and former presidential advisor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thant_Myint-U\" title=\"Thant Myint-U\"><PERSON><PERSON>-<PERSON></a>, Myanmar historian, diplomat, conservationist, and former presidential advisor.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thant_Myint-U"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter, bass player, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mike\" title=\"Fat Mike\"><PERSON></a>, American singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Mike\" title=\"Fat Mike\"><PERSON></a>, American singer-songwriter, bass player, and producer", "links": [{"title": "Fat <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Scottish footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1968)\" title=\"<PERSON> (footballer, born 1968)\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1968)\" title=\"<PERSON> (footballer, born 1968)\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1968)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1968)"}]}, {"year": "1968", "text": "<PERSON>, English actor, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, English actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, English actor, producer, and screenwriter", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Swedish politician, 2nd Swedish Minister for Infrastructure", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Ulrica_Messing\" title=\"Ulrica Messing\"><PERSON><PERSON><PERSON></a>, Swedish politician, 2nd <a href=\"https://wikipedia.org/wiki/Minister_for_Infrastructure_(Sweden)\" title=\"Minister for Infrastructure (Sweden)\">Swedish Minister for Infrastructure</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ulrica_Messing\" title=\"Ulrica <PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish politician, 2nd <a href=\"https://wikipedia.org/wiki/Minister_for_Infrastructure_(Sweden)\" title=\"Minister for Infrastructure (Sweden)\">Swedish Minister for Infrastructure</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ulrica_Messing"}, {"title": "Minister for Infrastructure (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_Infrastructure_(Sweden)"}]}, {"year": "1968", "text": "<PERSON>, Belgian sprinter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Canadian-American fashion designer and businessman, founded American Apparel", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Dov_Charney\" title=\"Dov Charney\"><PERSON><PERSON></a>, Canadian-American fashion designer and businessman, founded <a href=\"https://wikipedia.org/wiki/American_Apparel\" title=\"American Apparel\">American Apparel</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dov_Charney\" title=\"<PERSON>v Charney\"><PERSON><PERSON></a>, Canadian-American fashion designer and businessman, founded <a href=\"https://wikipedia.org/wiki/American_Apparel\" title=\"American Apparel\">American Apparel</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dov_Charney"}, {"title": "American Apparel", "link": "https://wikipedia.org/wiki/American_Apparel"}]}, {"year": "1969", "text": "<PERSON>, American cinematographer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, English singer-songwriter and actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Driver\"><PERSON><PERSON></a>, English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Driver\"><PERSON><PERSON></a>, English singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian singer-songwriter and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Venezuelan model and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1squez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1squez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patricia_Vel%C3%A1squez"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Australian-American actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian-American actress", "links": [{"title": "Portia de Rossi", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Cuban baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Indian actress, producer, and television host", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Preity_Zinta\" title=\"Preity Zinta\"><PERSON><PERSON></a>, Indian actress, producer, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pre<PERSON>_Zinta\" title=\"Preity Zinta\"><PERSON><PERSON></a>, Indian actress, producer, and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pre<PERSON>_<PERSON>inta"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Trai<PERSON><PERSON>_Dellas\" title=\"Trai<PERSON><PERSON> Dellas\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trai<PERSON><PERSON>_<PERSON>s\" title=\"Trai<PERSON><PERSON> Dellas\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rai<PERSON><PERSON>_Dellas"}]}, {"year": "1976", "text": "<PERSON>, American race car driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rice\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor and comedian", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Kerry_Washington\" title=\"Kerry Washington\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kerry_Washington\" title=\"Kerry Washington\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kerry_Washington"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Argentine footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_Caballero\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_Caballero\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fabi%C3%A1n_Caballero"}]}, {"year": "1979", "text": "<PERSON>, English author and educator", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American comedian, actor, and screenwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Irish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>young, South Korean actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-young\" title=\"<PERSON><PERSON>-young\"><PERSON><PERSON>-<PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-young\" title=\"<PERSON><PERSON>-young\"><PERSON><PERSON>-<PERSON></a>, South Korean actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-young"}]}, {"year": "1981", "text": "<PERSON>, Argentine footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Julio <PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1981", "text": "<PERSON>, English media personality and businesswoman", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English media personality and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English media personality and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter, dancer, and actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Estonian tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mare<PERSON>_Ani"}]}, {"year": "1982", "text": "<PERSON>, Scottish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Latvian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/J%C4%81nis_Sprukts\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C4%81nis_Sprukts\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C4%81<PERSON>_Sprukts"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, German-Belarusian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Belarusian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Belarusian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian-American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, Canadian-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, Canadian-American baseball player", "links": [{"title": "<PERSON> (pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)"}]}, {"year": "1984", "text": "<PERSON>, American runner", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adam_Federici"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American sprinter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American film producer, founded Annapurna Pictures", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer, founded <a href=\"https://wikipedia.org/wiki/Annapurna_Pictures\" title=\"Annapurna Pictures\">Annapurna Pictures</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer, founded <a href=\"https://wikipedia.org/wiki/Annapurna_Pictures\" title=\"Annapurna Pictures\">Annapurna Pictures</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Annapurna Pictures", "link": "https://wikipedia.org/wiki/Annapurna_Pictures"}]}, {"year": "1986", "text": "<PERSON>, Cameroonian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Belgian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, French tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American-English singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Teniste\" title=\"Taijo Teniste\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Teniste\" title=\"Tai<PERSON> Teniste\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, German rapper", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, German rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, German rapper", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>unato"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Argentine basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Laprov%C3%ADttola\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Laprov%C3%ADttola\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%A1s_Laprov%C3%ADttola"}]}, {"year": "1990", "text": "<PERSON>, Swedish ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m"}]}, {"year": "1990", "text": "<PERSON>, Japanese singer-songwriter, model, and actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Kota_Yabu\" class=\"mw-redirect\" title=\"Kota Yabu\"><PERSON></a>, Japanese singer-songwriter, model, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kota_Yabu\" class=\"mw-redirect\" title=\"Kota Yabu\"><PERSON></a>, Japanese singer-songwriter, model, and actor", "links": [{"title": "Kota Yabu", "link": "https://wikipedia.org/wiki/Kota_Yabu"}]}, {"year": "1992", "text": "<PERSON>, Canadian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Chinese diver", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Chinese diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Chinese diver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Danish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actor", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American Youtuber", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American Youtuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American Youtuber", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Arna<PERSON>_<PERSON>\" title=\"<PERSON>rna<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"Arna<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arna<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>o"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, South Korean singer and actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>on\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON>on"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Juli%C3%A1n_%C3%81l<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juli%C3%A1n_%C3%81l<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juli%C3%A1n_%C3%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Czech tennis player", "html": "2006 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ra_Bejlek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ra_Bejlek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ra_Bejlek"}]}], "Deaths": [{"year": "632", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Ferns, Irish bishop and saint (b. 550)", "html": "632 - <a href=\"https://wikipedia.org/wiki/M%C3%A1ed%C3%B3c_of_Ferns\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Ferns\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Ferns</a>, Irish bishop and saint (b. 550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1ed%C3%B3c_of_Ferns\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Ferns\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Ferns</a>, Irish bishop and saint (b. 550)", "links": [{"title": "M<PERSON>ed<PERSON><PERSON> of Ferns", "link": "https://wikipedia.org/wiki/M%C3%A1ed%C3%B3c_of_Ferns"}]}, {"year": "876", "text": "<PERSON><PERSON> of Altdorf, Frankish queen", "html": "876 - <a href=\"https://wikipedia.org/wiki/He<PERSON>\" title=\"<PERSON>mma\"><PERSON><PERSON> of Altdorf</a>, Frankish queen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>mma\"><PERSON><PERSON> of Altdorf</a>, Frankish queen", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "985", "text": "<PERSON><PERSON><PERSON>, Japanese monk and abbot (b. 912)", "html": "985 - <a href=\"https://wikipedia.org/wiki/Ry%C5%8Dgen\" title=\"Ryōgen\"><PERSON><PERSON><PERSON></a>, Japanese monk and abbot (b. 912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ry%C5%8Dgen\" title=\"Ryōgen\"><PERSON><PERSON><PERSON></a>, Japanese monk and abbot (b. 912)", "links": [{"title": "Ryōgen", "link": "https://wikipedia.org/wiki/Ry%C5%8Dgen"}]}, {"year": "1030", "text": "<PERSON>, duke of Aquitaine (b. 969)", "html": "1030 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON></a>, duke of Aquitaine (b. 969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON></a>, duke of Aquitaine (b. 969)", "links": [{"title": "<PERSON>, Duke of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine"}]}, {"year": "1216", "text": "<PERSON>, patriarch of Constantinople", "html": "1216 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople\" title=\"<PERSON> of Constantinople\"><PERSON> II</a>, patriarch of Constantinople", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople\" title=\"<PERSON> of Constantinople\"><PERSON> II</a>, patriarch of Constantinople", "links": [{"title": "<PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople"}]}, {"year": "1398", "text": "<PERSON><PERSON><PERSON>, emperor of Japan (b. 1334)", "html": "1398 - <a href=\"https://wikipedia.org/wiki/Emperor_Suk%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 1334)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Suk%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 1334)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Suk%C5%8D"}]}, {"year": "1418", "text": "<PERSON><PERSON>, prince of Wallachia (b. 1355)", "html": "1418 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I_of_Wallachia\" class=\"mw-redirect\" title=\"<PERSON><PERSON> I of Wallachia\"><PERSON><PERSON> I</a>, prince of Wallachia (b. 1355)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I_of_Wallachia\" class=\"mw-redirect\" title=\"<PERSON><PERSON> I of Wallachia\"><PERSON><PERSON> I</a>, prince of Wallachia (b. 1355)", "links": [{"title": "<PERSON><PERSON> I of Wallachia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_I_of_Wallachia"}]}, {"year": "1435", "text": "<PERSON><PERSON><PERSON>, emperor of China (b. 1398)", "html": "1435 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Xuande Emperor\"><PERSON><PERSON><PERSON></a>, emperor of China (b. 1398)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Xuande Emperor\"><PERSON><PERSON><PERSON></a>, emperor of China (b. 1398)", "links": [{"title": "<PERSON><PERSON><PERSON> Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}]}, {"year": "1561", "text": "<PERSON><PERSON>, Mughalan general (b. 1501)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mughalan general (b. 1501)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mughalan general (b. 1501)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1561", "text": "<PERSON><PERSON>, Dutch minister and theologian (b. 1496)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Simon<PERSON>\" title=\"<PERSON><PERSON> Simons\"><PERSON><PERSON></a>, Dutch minister and theologian (b. 1496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Simon<PERSON>\" title=\"Menno Simons\"><PERSON><PERSON></a>, Dutch minister and theologian (b. 1496)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>no_Simon<PERSON>"}]}, {"year": "1580", "text": "<PERSON>, king of Portugal (b. 1512)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Portugal\" title=\"<PERSON>, King of Portugal\"><PERSON></a>, king of Portugal (b. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Portugal\" title=\"<PERSON>, King of Portugal\"><PERSON></a>, king of Portugal (b. 1512)", "links": [{"title": "<PERSON>, King of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_Portugal"}]}, {"year": "1606", "text": "<PERSON>, English conspirator, leader of the Gunpowder Plot (b. 1570)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conspirator, leader of the <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a> (b. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conspirator, leader of the <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a> (b. 1570)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gunpowder Plot", "link": "https://wikipedia.org/wiki/Gunpowder_Plot"}]}, {"year": "1606", "text": "<PERSON>, English Gunpowder Plot conspirator (b. 1578)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a> conspirator (b. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a> conspirator (b. 1578)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gunpowder Plot", "link": "https://wikipedia.org/wiki/Gunpowder_Plot"}]}, {"year": "1606", "text": "<PERSON>, English Gunpowder Plot conspirator (b. 1571)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a> conspirator (b. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a> conspirator (b. 1571)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gunpowder Plot", "link": "https://wikipedia.org/wiki/Gunpowder_Plot"}]}, {"year": "1615", "text": "<PERSON>, Italian priest, 5th Superior General of the Society of Jesus (b. 1543)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, 5th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (b. 1543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, 5th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (b. 1543)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Superior General of the Society of Jesus", "link": "https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus"}]}, {"year": "1632", "text": "<PERSON><PERSON>, Swiss clockmaker and mathematician (b. 1552)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/Jost_B%C3%BCrgi\" title=\"<PERSON><PERSON> B<PERSON>rg<PERSON>\"><PERSON><PERSON></a>, Swiss clockmaker and mathematician (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON>_B%C3%BCrgi\" title=\"<PERSON><PERSON> B<PERSON>rg<PERSON>\"><PERSON><PERSON></a>, Swiss clockmaker and mathematician (b. 1552)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jost_B%C3%BCrgi"}]}, {"year": "1665", "text": "<PERSON>, German philosopher and theologian (b. 1622)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and theologian (b. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and theologian (b. 1622)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, French playwright (b. 1604)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright (b. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright (b. 1604)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1720", "text": "<PERSON>, 2nd Earl of Stamford, English politician, Chancellor of the Duchy of Lancaster (b. 1654)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Stamford\" title=\"<PERSON>, 2nd Earl of Stamford\"><PERSON>, 2nd Earl of Stamford</a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Stamford\" title=\"<PERSON>, 2nd Earl of Stamford\"><PERSON>, 2nd Earl of Stamford</a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1654)", "links": [{"title": "<PERSON>, 2nd Earl of Stamford", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Stamford"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1729", "text": "<PERSON>, Dutch explorer (b. 1659)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch explorer (b. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch explorer (b. 1659)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1736", "text": "<PERSON><PERSON><PERSON>, Italian architect and set designer, designed the Basilica of Superga (b. 1678)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian architect and set designer, designed the <a href=\"https://wikipedia.org/wiki/Basilica_of_Superga\" title=\"Basilica of Superga\">Basilica of Superga</a> (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian architect and set designer, designed the <a href=\"https://wikipedia.org/wiki/Basilica_of_Superga\" title=\"Basilica of Superga\">Basilica of Superga</a> (b. 1678)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Basilica of Superga", "link": "https://wikipedia.org/wiki/Basilica_of_Superga"}]}, {"year": "1790", "text": "<PERSON>, Irish-born American lawyer and surveyor (b. 1718)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Virginia_politician)\" title=\"<PERSON> (Virginia politician)\"><PERSON></a>, Irish-born American lawyer and surveyor (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Virginia_politician)\" title=\"<PERSON> (Virginia politician)\"><PERSON></a>, Irish-born American lawyer and surveyor (b. 1718)", "links": [{"title": "<PERSON> (Virginia politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Virginia_politician)"}]}, {"year": "1794", "text": "<PERSON><PERSON>, English admiral and politician, 12th Lieutenant Governor of Nova Scotia (b. 1711)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English admiral and politician, 12th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Nova_Scotia\" title=\"List of lieutenant governors of Nova Scotia\">Lieutenant Governor of Nova Scotia</a> (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English admiral and politician, 12th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Nova_Scotia\" title=\"List of lieutenant governors of Nova Scotia\">Lieutenant Governor of Nova Scotia</a> (b. 1711)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>not"}, {"title": "List of lieutenant governors of Nova Scotia", "link": "https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Nova_Scotia"}]}, {"year": "1811", "text": "<PERSON>, Argentinian priest and journalist (b. 1763)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian priest and journalist (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian priest and journalist (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, Venezuelan soldier (b. 1775)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_F%C3%A9lix_Ribas\" title=\"<PERSON>\"><PERSON></a>, Venezuelan soldier (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_F%C3%A9lix_Ribas\" title=\"<PERSON>\"><PERSON></a>, Venezuelan soldier (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_F%C3%A9lix_Ribas"}]}, {"year": "1828", "text": "<PERSON>, Greek general (b. 1792)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek general (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek general (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, English physician and author (b. 1777)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician and author (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician and author (b. 1777)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}]}, {"year": "1844", "text": "<PERSON>, French general (b. 1773)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "11th <PERSON><PERSON> (b. 1838)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/11th_<PERSON><PERSON>_Lama\" title=\"11th Dalai Lama\">11th Dalai Lama</a> (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/11th_Dalai_Lama\" title=\"11th Dalai Lama\">11th Dalai Lama</a> (b. 1838)", "links": [{"title": "11th Dal<PERSON> Lama", "link": "https://wikipedia.org/wiki/11th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON>, Moldavian Romanian journalist and author (b. 1812)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moldavian Romanian journalist and author (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moldavian Romanian journalist and author (b. 1812)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>bi_<PERSON>ise"}]}, {"year": "1888", "text": "<PERSON>, Italian priest and educator, founded the Salesian Society (b. 1815)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and educator, founded the <a href=\"https://wikipedia.org/wiki/Salesian_Society\" class=\"mw-redirect\" title=\"Salesian Society\">Salesian Society</a> (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and educator, founded the <a href=\"https://wikipedia.org/wiki/Salesian_Society\" class=\"mw-redirect\" title=\"Salesian Society\">Salesian Society</a> (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Salesian Society", "link": "https://wikipedia.org/wiki/Salesian_Society"}]}, {"year": "1892", "text": "<PERSON>, English pastor and author (b. 1834)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pastor and author (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pastor and author (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, 9th Marquess of Queensberry, Scottish nobleman (b. 1844)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_9th_Marquess_of_Queensberry\" title=\"<PERSON>, 9th Marquess of Queensberry\"><PERSON>, 9th Marquess of Queensberry</a>, Scottish nobleman (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_9th_Marquess_of_Queensberry\" title=\"<PERSON>, 9th Marquess of Queensberry\"><PERSON>, 9th Marquess of Queensberry</a>, Scottish nobleman (b. 1844)", "links": [{"title": "<PERSON>, 9th Marquess of Queensberry", "link": "https://wikipedia.org/wiki/<PERSON>,_9th_Marquess_of_Queensberry"}]}, {"year": "1911", "text": "<PERSON>, German politician (b. 1844)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German politician (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German politician (b. 1844)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish painter and critic (b. 1869)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Eligi<PERSON><PERSON>_<PERSON>domski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish painter and critic (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish painter and critic (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eligi<PERSON><PERSON>_<PERSON>ewiadomski"}]}, {"year": "1933", "text": "<PERSON>, English novelist and playwright, Nobel Prize laureate (b. 1867)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1944", "text": "<PERSON>, French author and playwright (b. 1882)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American engineer, invented FM radio (b. 1890)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented <a href=\"https://wikipedia.org/wiki/Frequency_modulation\" title=\"Frequency modulation\">FM radio</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented <a href=\"https://wikipedia.org/wiki/Frequency_modulation\" title=\"Frequency modulation\">FM radio</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Frequency modulation", "link": "https://wikipedia.org/wiki/Frequency_modulation"}]}, {"year": "1955", "text": "<PERSON>, American activist, Nobel Prize laureate (b. 1865)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1956", "text": "<PERSON><PERSON> <PERSON><PERSON>, English author, poet, and playwright, created <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (b. 1882)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author, poet, and playwright, created <i><a href=\"https://wikipedia.org/wiki/Winnie-the-Pooh\" title=\"<PERSON>nie-the-Pooh\"><PERSON><PERSON>-<PERSON>-<PERSON><PERSON></a></i> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author, poet, and playwright, created <i><a href=\"https://wikipedia.org/wiki/Winnie-the-Pooh\" title=\"<PERSON>nie-the-Pooh\"><PERSON><PERSON>-the-Pooh</a></i> (b. 1882)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-the-Po<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Estonian politician, 14th Estonian Minister of Foreign Affairs (b. 1898)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, 14th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, 14th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a> (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)"}]}, {"year": "1960", "text": "<PERSON>, French painter (b. 1882)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Indian politician, 1st Chief Minister of Bihar (b. 1887)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, Indian politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Bihar\" class=\"mw-redirect\" title=\"Chief Minister of Bihar\">Chief Minister of Bihar</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, Indian politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Bihar\" class=\"mw-redirect\" title=\"Chief Minister of Bihar\">Chief Minister of Bihar</a> (b. 1887)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Chief Minister of Bihar", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Bihar"}]}, {"year": "1966", "text": "<PERSON>, English general (b. 1887)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American sprinter and educator (b. 1908)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and educator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and educator (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Indian spiritual master (b. 1894)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>her <PERSON>\"><PERSON><PERSON></a>, Indian spiritual master (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>her Baba\"><PERSON><PERSON></a>, Indian spiritual master (b. 1894)", "links": [{"title": "<PERSON>her <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Russian historian and linguist (b. 1891)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian historian and linguist (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian historian and linguist (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Norwegian economist and academic, Nobel Prize laureate (b. 1895)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1974", "text": "<PERSON>, Polish American film producer, co-founded Goldwyn Pictures (b. 1882)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Goldwyn_Pictures\" title=\"Goldwyn Pictures\">Goldwyn Pictures</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Goldwyn_Pictures\" title=\"Goldwyn Pictures\">Goldwyn Pictures</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Goldwyn Pictures", "link": "https://wikipedia.org/wiki/Goldwyn_Pictures"}]}, {"year": "1976", "text": "<PERSON>, American criminal (b. 1941)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Swedish author and composer (b. 1890)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and composer (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and composer (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English Australian film producer (b. 1896)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)\" title=\"<PERSON> (film producer)\"><PERSON></a>, English Australian film producer (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)\" title=\"<PERSON> (film producer)\"><PERSON></a>, English Australian film producer (b. 1896)", "links": [{"title": "<PERSON> (film producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese author (b. 1905)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Tatsuz%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese author (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tatsuz%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese author (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tatsuz%C5%8D_<PERSON><PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, French director and screenwriter (b. 1907)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gret\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gret\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Yves_All%C3%A9gret"}]}, {"year": "1989", "text": "<PERSON>, Canadian captain and spy (b. 1896)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain and spy (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain and spy (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>-<PERSON><PERSON>, German zoologist and academic (b. 1901)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German zoologist and academic (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German zoologist and academic (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Egyptian American biochemist and academic (b. 1935)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian American biochemist and academic (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian American biochemist and academic (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Irish American bishop (b. 1930)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish American bishop (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish American bishop (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Japanese wrestler and trainer, co-founded All Japan Pro Wrestling (b. 1938)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Baba\" title=\"Giant Baba\"><PERSON> Baba</a>, Japanese wrestler and trainer, co-founded <a href=\"https://wikipedia.org/wiki/All_Japan_Pro_Wrestling\" title=\"All Japan Pro Wrestling\">All Japan Pro Wrestling</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giant_Baba\" title=\"Giant Baba\"><PERSON> Baba</a>, Japanese wrestler and trainer, co-founded <a href=\"https://wikipedia.org/wiki/All_Japan_Pro_Wrestling\" title=\"All Japan Pro Wrestling\">All Japan Pro Wrestling</a> (b. 1938)", "links": [{"title": "Giant Baba", "link": "https://wikipedia.org/wiki/<PERSON>_Baba"}, {"title": "All Japan Pro Wrestling", "link": "https://wikipedia.org/wiki/All_Japan_Pro_Wrestling"}]}, {"year": "2000", "text": "<PERSON>, Latvian American author and illustrator (b. 1926)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian American author and illustrator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian American author and illustrator (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Canadian American author (b. 1923)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian American author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian American author (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American colonel and pilot (b. 1919)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American colonel and pilot (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American colonel and pilot (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American swimmer and actress (b. 1913)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and actress (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and actress (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Indian actress and playback singer (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Sur<PERSON>ya\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress and playback singer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suraiya\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress and playback singer (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Suraiya"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Scottish actress and ballerina (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish actress and ballerina (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish actress and ballerina (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American journalist and author (b. 1944)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, South African activist and politician (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Adelaide_Tambo\" title=\"Adelaide Tambo\"><PERSON></a>, South African activist and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide_Tambo\" title=\"Adelaide Tambo\"><PERSON></a>, South African activist and politician (b. 1929)", "links": [{"title": "Adelaide Tambo", "link": "https://wikipedia.org/wiki/Adelaide_Tambo"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON><PERSON>, Czechoslovakian canoeist (b. 1914)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_%C4%8Capek\" title=\"František Čapek\"><PERSON><PERSON><PERSON><PERSON></a>, Czechoslovakian canoeist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_%C4%8Capek\" title=\"František Čapek\"><PERSON><PERSON><PERSON><PERSON></a>, Czechoslovakian canoeist (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_%C4%8Capek"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON><PERSON>, Romanian bishop and poet (b. 1921)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Bartolomeu_Anania\" title=\"Bartolomeu Anania\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian bishop and poet (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bart<PERSON>meu_Anania\" title=\"Bartolomeu Anania\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian bishop and poet (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bart<PERSON>meu_Anania"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian lawyer and politician (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American painter and sculptor (b. 1910)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Mexican poet and scholar (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Bonifaz_Nu%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican poet and scholar (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Bonifaz_Nu%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican poet and scholar (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Bonifaz_Nu%C3%B1o"}]}, {"year": "2013", "text": "<PERSON>, Iranian lawyer and politician, 1st Vice President of Iran (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_President_of_Iran\" class=\"mw-redirect\" title=\"Vice President of Iran\">Vice President of Iran</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_President_of_Iran\" class=\"mw-redirect\" title=\"Vice President of Iran\">Vice President of Iran</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of Iran", "link": "https://wikipedia.org/wiki/Vice_President_of_Iran"}]}, {"year": "2014", "text": "<PERSON>, American songwriter and producer, co-founded Anna Records (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer, co-founded <a href=\"https://wikipedia.org/wiki/Anna_Records\" title=\"Anna Records\">Anna Records</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer, co-founded <a href=\"https://wikipedia.org/wiki/Anna_Records\" title=\"Anna Records\">Anna Records</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Anna Records", "link": "https://wikipedia.org/wiki/Anna_Records"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Somalian politician, 4th Prime Minister of Somalia (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Somalian politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Somalia\" class=\"mw-redirect\" title=\"Prime Minister of Somalia\">Prime Minister of Somalia</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Somalian politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Somalia\" class=\"mw-redirect\" title=\"Prime Minister of Somalia\">Prime Minister of Somalia</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Somalia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Somalia"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian director and screenwriter (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Jancs%C3%B3\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian director and screenwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Jancs%C3%B3\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian director and screenwriter (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikl%C3%B3s_Jancs%C3%B3"}]}, {"year": "2015", "text": "<PERSON>, Canadian ice hockey player (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Howe"}]}, {"year": "2015", "text": "<PERSON><PERSON>, German footballer, coach, and journalist (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Udo_<PERSON>ttek\" title=\"Udo <PERSON>ttek\"><PERSON><PERSON></a>, German footballer, coach, and journalist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Udo_<PERSON>ttek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer, coach, and journalist (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Udo_<PERSON>k"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American actress (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German captain and politician, 6th President of Germany (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4cker\" title=\"<PERSON>\"><PERSON></a>, German captain and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">President of Germany</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4cker\" title=\"<PERSON>\"><PERSON></a>, German captain and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">President of Germany</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4cker"}, {"title": "List of German presidents", "link": "https://wikipedia.org/wiki/List_of_German_presidents"}]}, {"year": "2016", "text": "<PERSON>, Irish radio and television host (b. 1938)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish radio and television host (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish radio and television host (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Canadian filmmaker (b. 1979)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, Canadian filmmaker (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, Canadian filmmaker (b. 1979)", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, American professional basketball player (b. 1979)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American professional basketball player (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American professional basketball player (b. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American singer (b. 1986)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}