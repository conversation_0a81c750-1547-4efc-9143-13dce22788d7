{"date": "February 22", "url": "https://wikipedia.org/wiki/February_22", "data": {"Events": [{"year": "1076", "text": "Having received a letter during the Lenten synod of 14-20 February demanding that he abdicate, Pope <PERSON> excommunicates <PERSON>, Holy Roman Emperor.", "html": "1076 - Having received a letter during the Lenten synod of 14-20 February demanding that he abdicate, <a href=\"https://wikipedia.org/wiki/<PERSON>_Gregory_VII\" title=\"Pope Gregory VII\"><PERSON> <PERSON> VII</a> excommunicates <a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV, Holy Roman Emperor</a>.", "no_year_html": "Having received a letter during the Lenten synod of 14-20 February demanding that he abdicate, <a href=\"https://wikipedia.org/wiki/<PERSON>_Gregory_VII\" title=\"Pope Gregory VII\"><PERSON> <PERSON> VII</a> excommunicates <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV, Holy Roman Emperor</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1316", "text": "The Battle of Picotin, between <PERSON> of Majorca and the forces of <PERSON> of Hainaut, ends in victory for <PERSON>.", "html": "1316 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Picotin\" title=\"Battle of Picotin\">Battle of Picotin</a>, between <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Majorca\" title=\"<PERSON> of Majorca\"><PERSON> of Majorca</a> and the forces of <a href=\"https://wikipedia.org/wiki/Matilda_of_Hainaut\" title=\"Matilda of Hainaut\"><PERSON> of Hainaut</a>, ends in victory for <PERSON>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Picotin\" title=\"Battle of Picotin\">Battle of Picotin</a>, between <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Majorca\" title=\"<PERSON> of Majorca\"><PERSON> of Majorca</a> and the forces of <a href=\"https://wikipedia.org/wiki/Matilda_of_Hainaut\" title=\"Matilda of Hainaut\"><PERSON> of Hainaut</a>, ends in victory for <PERSON>.", "links": [{"title": "Battle of Picotin", "link": "https://wikipedia.org/wiki/Battle_of_Picotin"}, {"title": "<PERSON> of Majorca", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Majorca"}, {"title": "<PERSON> of Hainaut", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hainaut"}]}, {"year": "1371", "text": "<PERSON> becomes King of Scotland, beginning the Stuart dynasty.", "html": "1371 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> II</a> becomes King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a>, beginning the <a href=\"https://wikipedia.org/wiki/House_of_Stuart\" title=\"House of Stuart\">Stuart</a> dynasty.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> II</a> becomes King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a>, beginning the <a href=\"https://wikipedia.org/wiki/House_of_Stuart\" title=\"House of Stuart\">Stuart</a> dynasty.", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}, {"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}, {"title": "House of Stuart", "link": "https://wikipedia.org/wiki/<PERSON>_of_<PERSON>"}]}, {"year": "1495", "text": "King <PERSON> of France enters Naples to claim the city's throne.", "html": "1495 - King <a href=\"https://wikipedia.org/wiki/Charles_<PERSON>_of_France\" title=\"Charles VIII of France\"><PERSON> VIII of France</a> enters <a href=\"https://wikipedia.org/wiki/Naples\" title=\"Naples\">Naples</a> to claim the city's throne.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VIII of France\"><PERSON> of France</a> enters <a href=\"https://wikipedia.org/wiki/Naples\" title=\"Naples\">Naples</a> to claim the city's throne.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_France"}, {"title": "Naples", "link": "https://wikipedia.org/wiki/Naples"}]}, {"year": "1632", "text": "<PERSON><PERSON>, Grand Duke of Tuscany, the dedicatee, receives the first printed copy of <PERSON>'s Dialogue Concerning the Two Chief World Systems.", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_de%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON>, Grand Duke of Tuscany</a>, the dedicatee, receives the first printed copy of <a href=\"https://wikipedia.org/wiki/Galileo_Galilei\" title=\"Galileo Galilei\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Dialogue_Concerning_the_Two_Chief_World_Systems\" title=\"Dialogue Concerning the Two Chief World Systems\">Dialogue Concerning the Two Chief World Systems</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON>, Grand Duke of Tuscany</a>, the dedicatee, receives the first printed copy of <a href=\"https://wikipedia.org/wiki/Galileo_Galilei\" title=\"Galileo Galilei\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Dialogue_Concerning_the_Two_Chief_World_Systems\" title=\"Dialogue Concerning the Two Chief World Systems\">Dialogue Concerning the Two Chief World Systems</a></i>.", "links": [{"title": "<PERSON><PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gali<PERSON>i"}, {"title": "Dialogue Concerning the Two Chief World Systems", "link": "https://wikipedia.org/wiki/Dialogue_Concerning_the_Two_Chief_World_Systems"}]}, {"year": "1651", "text": "St. Peter's Flood: A storm surge floods the Frisian coast, drowning 15,000 people.", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Peter%27s_Flood\" class=\"mw-redirect\" title=\"St. Peter's Flood\">St. Peter's Flood</a>: A storm surge floods the <a href=\"https://wikipedia.org/wiki/German_Bight\" title=\"German Bight\">Frisian coast</a>, drowning 15,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Peter%27s_Flood\" class=\"mw-redirect\" title=\"St. Peter's Flood\">St. Peter's Flood</a>: A storm surge floods the <a href=\"https://wikipedia.org/wiki/German_Bight\" title=\"German Bight\">Frisian coast</a>, drowning 15,000 people.", "links": [{"title": "St. Peter's Flood", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27s_Flood"}, {"title": "German Bight", "link": "https://wikipedia.org/wiki/German_Bight"}]}, {"year": "1744", "text": "War of the Austrian Succession: The Battle of Toulon causes several Royal Navy captains to be court-martialed, and the Articles of War to be amended.", "html": "1744 - <a href=\"https://wikipedia.org/wiki/War_of_the_Austrian_Succession\" title=\"War of the Austrian Succession\">War of the Austrian Succession</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Toulon_(1744)\" title=\"Battle of Toulon (1744)\">Battle of Toulon</a> causes several <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> captains to be <a href=\"https://wikipedia.org/wiki/Court-martial\" title=\"Court-martial\">court-martialed</a>, and the <a href=\"https://wikipedia.org/wiki/Articles_of_War\" title=\"Articles of War\">Articles of War</a> to be amended.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Austrian_Succession\" title=\"War of the Austrian Succession\">War of the Austrian Succession</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Toulon_(1744)\" title=\"Battle of Toulon (1744)\">Battle of Toulon</a> causes several <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> captains to be <a href=\"https://wikipedia.org/wiki/Court-martial\" title=\"Court-martial\">court-martialed</a>, and the <a href=\"https://wikipedia.org/wiki/Articles_of_War\" title=\"Articles of War\">Articles of War</a> to be amended.", "links": [{"title": "War of the Austrian Succession", "link": "https://wikipedia.org/wiki/War_of_the_Austrian_Succession"}, {"title": "Battle of Toulon (1744)", "link": "https://wikipedia.org/wiki/Battle_of_Toulon_(1744)"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Court-martial", "link": "https://wikipedia.org/wiki/Court-martial"}, {"title": "Articles of War", "link": "https://wikipedia.org/wiki/Articles_of_War"}]}, {"year": "1770", "text": "British customs officer <PERSON><PERSON><PERSON> fires blindly into a crowd during a protest in North End, Boston, fatally wounding 11-year-old <PERSON>; the first American fatality of the American Revolution.", "html": "1770 - British customs officer <PERSON><PERSON><PERSON> fires blindly into a crowd during a protest in <a href=\"https://wikipedia.org/wiki/North_End,_Boston\" title=\"North End, Boston\">North End, Boston</a>, fatally wounding 11-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>; the first American fatality of the <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>.", "no_year_html": "British customs officer <PERSON><PERSON><PERSON> fires blindly into a crowd during a protest in <a href=\"https://wikipedia.org/wiki/North_End,_Boston\" title=\"North End, Boston\">North End, Boston</a>, fatally wounding 11-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>; the first American fatality of the <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>.", "links": [{"title": "North End, Boston", "link": "https://wikipedia.org/wiki/North_End,_Boston"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}]}, {"year": "1797", "text": "The last Invasion of Britain begins near Fishguard, Wales.", "html": "1797 - The <a href=\"https://wikipedia.org/wiki/Last_Invasion_of_Britain\" class=\"mw-redirect\" title=\"Last Invasion of Britain\">last Invasion of Britain</a> begins near <a href=\"https://wikipedia.org/wiki/Fishguard\" title=\"Fishguard\">Fishguard</a>, Wales.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Last_Invasion_of_Britain\" class=\"mw-redirect\" title=\"Last Invasion of Britain\">last Invasion of Britain</a> begins near <a href=\"https://wikipedia.org/wiki/Fishguard\" title=\"Fishguard\">Fishguard</a>, Wales.", "links": [{"title": "Last Invasion of Britain", "link": "https://wikipedia.org/wiki/Last_Invasion_of_Britain"}, {"title": "Fishguard", "link": "https://wikipedia.org/wiki/Fishguard"}]}, {"year": "1819", "text": "By the Adams-Onís Treaty, Spain sells Florida to the United States for five million U.S. dollars.", "html": "1819 - By the <a href=\"https://wikipedia.org/wiki/Adams%E2%80%93On%C3%ADs_Treaty\" title=\"Adams-Onís Treaty\">Adams-Onís Treaty</a>, Spain <a href=\"https://wikipedia.org/wiki/History_of_Florida\" title=\"History of Florida\">sells Florida</a> to the United States for five million U.S. dollars.", "no_year_html": "By the <a href=\"https://wikipedia.org/wiki/Adams%E2%80%93On%C3%ADs_Treaty\" title=\"Adams-Onís Treaty\">Adams-Onís Treaty</a>, Spain <a href=\"https://wikipedia.org/wiki/History_of_Florida\" title=\"History of Florida\">sells Florida</a> to the United States for five million U.S. dollars.", "links": [{"title": "Adams-Onís Treaty", "link": "https://wikipedia.org/wiki/Adams%E2%80%93On%C3%ADs_Treaty"}, {"title": "History of Florida", "link": "https://wikipedia.org/wiki/History_of_Florida"}]}, {"year": "1847", "text": "Mexican-American War: The Battle of Buena Vista: Five thousand American troops defeat 15,000 Mexican troops.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Buena_Vista\" title=\"Battle of Buena Vista\">Battle of Buena Vista</a>: Five thousand American troops defeat 15,000 <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexican</a> troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Buena_Vista\" title=\"Battle of Buena Vista\">Battle of Buena Vista</a>: Five thousand American troops defeat 15,000 <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexican</a> troops.", "links": [{"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}, {"title": "Battle of Buena Vista", "link": "https://wikipedia.org/wiki/Battle_of_Buena_Vista"}, {"title": "Mexico", "link": "https://wikipedia.org/wiki/Mexico"}]}, {"year": "1848", "text": "The French Revolution of 1848, which would lead to the establishment of the French Second Republic, begins.", "html": "1848 - The <a href=\"https://wikipedia.org/wiki/French_Revolution_of_1848\" title=\"French Revolution of 1848\">French Revolution of 1848</a>, which would lead to the establishment of the <a href=\"https://wikipedia.org/wiki/French_Second_Republic\" title=\"French Second Republic\">French Second Republic</a>, begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/French_Revolution_of_1848\" title=\"French Revolution of 1848\">French Revolution of 1848</a>, which would lead to the establishment of the <a href=\"https://wikipedia.org/wiki/French_Second_Republic\" title=\"French Second Republic\">French Second Republic</a>, begins.", "links": [{"title": "French Revolution of 1848", "link": "https://wikipedia.org/wiki/French_Revolution_of_1848"}, {"title": "French Second Republic", "link": "https://wikipedia.org/wiki/French_Second_Republic"}]}, {"year": "1856", "text": "The United States Republican Party opens its first national convention in Pittsburgh.", "html": "1856 - The United States <a href=\"https://wikipedia.org/wiki/Republican_Party_(United_States)\" title=\"Republican Party (United States)\">Republican Party</a> opens its first national convention in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Republican_Party_(United_States)\" title=\"Republican Party (United States)\">Republican Party</a> opens its first national convention in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>.", "links": [{"title": "Republican Party (United States)", "link": "https://wikipedia.org/wiki/Republican_Party_(United_States)"}, {"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}]}, {"year": "1862", "text": "American Civil War: <PERSON> is officially inaugurated for a six-year term as the President of the Confederate States of America in Richmond, Virginia. He was previously inaugurated as a provisional president on February 18, 1861.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is officially inaugurated for a six-year term as the <a href=\"https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America\" title=\"President of the Confederate States of America\">President of the Confederate States of America</a> in <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>. He was previously inaugurated as a provisional president on February 18, 1861.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is officially inaugurated for a six-year term as the <a href=\"https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America\" title=\"President of the Confederate States of America\">President of the Confederate States of America</a> in <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>. He was previously inaugurated as a provisional president on February 18, 1861.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Confederate States of America", "link": "https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America"}, {"title": "Richmond, Virginia", "link": "https://wikipedia.org/wiki/Richmond,_Virginia"}]}, {"year": "1872", "text": "The Prohibition Party holds its first national convention in Columbus, Ohio, nominating <PERSON> as its presidential nominee.", "html": "1872 - The <a href=\"https://wikipedia.org/wiki/Prohibition_Party\" title=\"Prohibition Party\">Prohibition Party</a> holds its first national convention in <a href=\"https://wikipedia.org/wiki/Columbus,_Ohio\" title=\"Columbus, Ohio\">Columbus, Ohio</a>, nominating <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(prohibitionist)\" title=\"<PERSON> (prohibitionist)\"><PERSON></a> as its presidential nominee.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Prohibition_Party\" title=\"Prohibition Party\">Prohibition Party</a> holds its first national convention in <a href=\"https://wikipedia.org/wiki/Columbus,_Ohio\" title=\"Columbus, Ohio\">Columbus, Ohio</a>, nominating <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(prohibitionist)\" title=\"<PERSON> (prohibitionist)\"><PERSON></a> as its presidential nominee.", "links": [{"title": "Prohibition Party", "link": "https://wikipedia.org/wiki/Prohibition_Party"}, {"title": "Columbus, Ohio", "link": "https://wikipedia.org/wiki/Columbus,_Ohio"}, {"title": "<PERSON> (prohibitionist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(prohibitionist)"}]}, {"year": "1879", "text": "In Utica, New York, <PERSON> opens the first of many of five-and-dime Woolworth stores.", "html": "1879 - In <a href=\"https://wikipedia.org/wiki/Utica,_New_York\" title=\"Utica, New York\">Utica, New York</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> opens the first of many of five-and-dime <a href=\"https://wikipedia.org/wiki/F._W._Woolworth_Company\" title=\"F. W. Woolworth Company\">Woolworth</a> stores.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Utica,_New_York\" title=\"Utica, New York\">Utica, New York</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> opens the first of many of five-and-dime <a href=\"https://wikipedia.org/wiki/F._W._Woolworth_Company\" title=\"F. W. Woolworth Company\">Woolworth</a> stores.", "links": [{"title": "Utica, New York", "link": "https://wikipedia.org/wiki/Utica,_New_York"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "F. W. Woolworth Company", "link": "https://wikipedia.org/wiki/F._W._Woolworth_Company"}]}, {"year": "1881", "text": "<PERSON>'s Needle, a 3,500-year-old Ancient Egyptian obelisk is erected in Central Park, New York.", "html": "1881 - <i><a href=\"https://wikipedia.org/wiki/Cleopatra%27s_Needle_(New_York)\" class=\"mw-redirect\" title=\"<PERSON>'s Needle (New York)\"><PERSON>'s Needle</a></i>, a 3,500-year-old Ancient Egyptian obelisk is erected in <a href=\"https://wikipedia.org/wiki/Central_Park\" title=\"Central Park\">Central Park</a>, New York.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Cleopatra%27s_Needle_(New_York)\" class=\"mw-redirect\" title=\"<PERSON>'s Needle (New York)\"><PERSON>'s Needle</a></i>, a 3,500-year-old Ancient Egyptian obelisk is erected in <a href=\"https://wikipedia.org/wiki/Central_Park\" title=\"Central Park\">Central Park</a>, New York.", "links": [{"title": "<PERSON>'s Needle (New York)", "link": "https://wikipedia.org/wiki/Cleopatra%27s_Needle_(New_York)"}, {"title": "Central Park", "link": "https://wikipedia.org/wiki/Central_Park"}]}, {"year": "1889", "text": "President <PERSON><PERSON> signs a bill admitting North Dakota, South Dakota, Montana and Washington as U.S. states.", "html": "1889 - <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/Grover_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Enabling_Act_of_1889\" title=\"Enabling Act of 1889\">signs a bill</a> admitting <a href=\"https://wikipedia.org/wiki/North_Dakota\" title=\"North Dakota\">North Dakota</a>, <a href=\"https://wikipedia.org/wiki/South_Dakota\" title=\"South Dakota\">South Dakota</a>, <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a> and <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a> as <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. states</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/Grover_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Enabling_Act_of_1889\" title=\"Enabling Act of 1889\">signs a bill</a> admitting <a href=\"https://wikipedia.org/wiki/North_Dakota\" title=\"North Dakota\">North Dakota</a>, <a href=\"https://wikipedia.org/wiki/South_Dakota\" title=\"South Dakota\">South Dakota</a>, <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a> and <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a> as <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. states</a>.", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "Grover <PERSON>", "link": "https://wikipedia.org/wiki/Grover_Cleveland"}, {"title": "Enabling Act of 1889", "link": "https://wikipedia.org/wiki/Enabling_Act_of_1889"}, {"title": "North Dakota", "link": "https://wikipedia.org/wiki/North_Dakota"}, {"title": "South Dakota", "link": "https://wikipedia.org/wiki/South_Dakota"}, {"title": "Montana", "link": "https://wikipedia.org/wiki/Montana"}, {"title": "Washington (state)", "link": "https://wikipedia.org/wiki/Washington_(state)"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1899", "text": "Filipino forces led by General <PERSON> launch counterattacks for the first time against the American forces during the Philippine-American War. The Filipinos fail to regain Manila from the Americans.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Philippine_Revolutionary_Army\" title=\"Philippine Revolutionary Army\">Filipino forces</a> led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Antonio Luna\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Caloocan\" title=\"Second Battle of Caloocan\">launch counterattacks for the first time</a> against the American forces during the <a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a>. The Filipinos fail to regain Manila from the Americans.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philippine_Revolutionary_Army\" title=\"Philippine Revolutionary Army\">Filipino forces</a> led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Antonio Luna\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Caloocan\" title=\"Second Battle of Caloocan\">launch counterattacks for the first time</a> against the American forces during the <a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a>. The Filipinos fail to regain Manila from the Americans.", "links": [{"title": "Philippine Revolutionary Army", "link": "https://wikipedia.org/wiki/Philippine_Revolutionary_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Second Battle of Caloocan", "link": "https://wikipedia.org/wiki/Second_Battle_of_Caloocan"}, {"title": "Philippine-American War", "link": "https://wikipedia.org/wiki/Philippine%E2%80%93American_War"}]}, {"year": "1904", "text": "The United Kingdom sells a meteorological station on the South Orkney Islands to Argentina; the islands are subsequently claimed by the United Kingdom in 1908.", "html": "1904 - The United Kingdom sells a meteorological station on the <a href=\"https://wikipedia.org/wiki/South_Orkney_Islands\" title=\"South Orkney Islands\">South Orkney Islands</a> to <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>; the islands are subsequently claimed by the United Kingdom in 1908.", "no_year_html": "The United Kingdom sells a meteorological station on the <a href=\"https://wikipedia.org/wiki/South_Orkney_Islands\" title=\"South Orkney Islands\">South Orkney Islands</a> to <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>; the islands are subsequently claimed by the United Kingdom in 1908.", "links": [{"title": "South Orkney Islands", "link": "https://wikipedia.org/wiki/South_Orkney_Islands"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}]}, {"year": "1909", "text": "The sixteen battleships of the Great White Fleet, led by USS Connecticut, return to the United States after a voyage around the world.", "html": "1909 - The sixteen <a href=\"https://wikipedia.org/wiki/Battleship\" title=\"Battleship\">battleships</a> of the <a href=\"https://wikipedia.org/wiki/Great_White_Fleet\" title=\"Great White Fleet\">Great White Fleet</a>, led by <a href=\"https://wikipedia.org/wiki/USS_Connecticut_(BB-18)\" title=\"USS Connecticut (BB-18)\">USS <i>Connecticut</i></a>, return to the United States after a voyage around the world.", "no_year_html": "The sixteen <a href=\"https://wikipedia.org/wiki/Battleship\" title=\"Battleship\">battleships</a> of the <a href=\"https://wikipedia.org/wiki/Great_White_Fleet\" title=\"Great White Fleet\">Great White Fleet</a>, led by <a href=\"https://wikipedia.org/wiki/USS_Connecticut_(BB-18)\" title=\"USS Connecticut (BB-18)\">USS <i>Connecticut</i></a>, return to the United States after a voyage around the world.", "links": [{"title": "Battleship", "link": "https://wikipedia.org/wiki/Battleship"}, {"title": "Great White Fleet", "link": "https://wikipedia.org/wiki/Great_White_Fleet"}, {"title": "USS Connecticut (BB-18)", "link": "https://wikipedia.org/wiki/USS_Connecticut_(BB-18)"}]}, {"year": "1921", "text": "After Russian forces under Baron <PERSON> drive the Chinese out, the Bogd Khan is reinstalled as the emperor of Mongolia.[citation needed]", "html": "1921 - After Russian forces under <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> drive the Chinese out, the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is reinstalled as the <a href=\"https://wikipedia.org/wiki/Khagan\" title=\"Khagan\">emperor</a> of <a href=\"https://wikipedia.org/wiki/Mongolia_(1911%E2%80%9324)\" class=\"mw-redirect\" title=\"Mongolia (1911-24)\">Mongolia</a>.", "no_year_html": "After Russian forces under <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> drive the Chinese out, the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is reinstalled as the <a href=\"https://wikipedia.org/wiki/Khagan\" title=\"Khagan\">emperor</a> of <a href=\"https://wikipedia.org/wiki/Mongolia_(1911%E2%80%9324)\" class=\"mw-redirect\" title=\"Mongolia (1911-24)\">Mongolia</a>.", "links": [{"title": "<PERSON>Stern<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bog<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>gan"}, {"title": "Mongolia (1911-24)", "link": "https://wikipedia.org/wiki/Mongolia_(1911%E2%80%9324)"}]}, {"year": "1942", "text": "World War II: President <PERSON> orders General <PERSON> out of the Philippines as the Japanese victory becomes inevitable.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_escape_from_the_Philippines\" title=\"<PERSON>'s escape from the Philippines\"><PERSON> out of the Philippines</a> as the Japanese victory becomes inevitable.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_escape_from_the_Philippines\" title=\"<PERSON>'s escape from the Philippines\"><PERSON> out of the Philippines</a> as the Japanese victory becomes inevitable.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>'s escape from the Philippines", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_escape_from_the_Philippines"}]}, {"year": "1943", "text": "World War II: Members of the White Rose resistance, <PERSON>, <PERSON>, and <PERSON> are executed in Nazi Germany.", "html": "1943 - World War II: Members of the <a href=\"https://wikipedia.org/wiki/White_Rose\" title=\"White Rose\"><PERSON></a> resistance, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are executed in <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>.", "no_year_html": "World War II: Members of the <a href=\"https://wikipedia.org/wiki/White_Rose\" title=\"White Rose\"><PERSON> Rose</a> resistance, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> are executed in <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>.", "links": [{"title": "White Rose", "link": "https://wikipedia.org/wiki/<PERSON>_Rose"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1943", "text": "Yankee Clipper crashes while landing on the Tagus in Lisbon, killing 24.", "html": "1943 - <i><a href=\"https://wikipedia.org/wiki/Yankee_Clipper_(flying_boat)\" title=\"Yankee Clipper (flying boat)\">Yankee Clipper</a></i> crashes while landing on the <a href=\"https://wikipedia.org/wiki/Tagus\" title=\"Tagus\">Tagus</a> in Lisbon, killing 24.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Yankee_Clipper_(flying_boat)\" title=\"Yankee Clipper (flying boat)\">Yankee Clipper</a></i> crashes while landing on the <a href=\"https://wikipedia.org/wiki/Tagus\" title=\"Tagus\">Tagus</a> in Lisbon, killing 24.", "links": [{"title": "<PERSON> Clipper (flying boat)", "link": "https://wikipedia.org/wiki/Yankee_Clipper_(flying_boat)"}, {"title": "Tagus", "link": "https://wikipedia.org/wiki/Tagus"}]}, {"year": "1944", "text": "World War II: American aircraft mistakenly bomb the Dutch towns of Nijmegen, Arnhem, Enschede and Deventer, resulting in 800 dead in Nijmegen alone.", "html": "1944 - World War II: American aircraft <a href=\"https://wikipedia.org/wiki/Bombing_of_Nijmegen\" title=\"Bombing of Nijmegen\">mistakenly bomb</a> the Dutch towns of <a href=\"https://wikipedia.org/wiki/Nijmegen\" title=\"Nijmegen\">Nijmegen</a>, <a href=\"https://wikipedia.org/wiki/Arnhem\" title=\"Arnhem\">Arnhem</a>, <a href=\"https://wikipedia.org/wiki/Enschede\" title=\"Enschede\">Enschede</a> and <a href=\"https://wikipedia.org/wiki/Deventer\" title=\"Deventer\">Deventer</a>, resulting in 800 dead in Nijmegen alone.", "no_year_html": "World War II: American aircraft <a href=\"https://wikipedia.org/wiki/Bombing_of_Nijmegen\" title=\"Bombing of Nijmegen\">mistakenly bomb</a> the Dutch towns of <a href=\"https://wikipedia.org/wiki/Nijmegen\" title=\"Nijmegen\">Nijmegen</a>, <a href=\"https://wikipedia.org/wiki/Arnhem\" title=\"Arnhem\">Arnhem</a>, <a href=\"https://wikipedia.org/wiki/Enschede\" title=\"Enschede\">Enschede</a> and <a href=\"https://wikipedia.org/wiki/Deventer\" title=\"Deventer\">Deventer</a>, resulting in 800 dead in Nijmegen alone.", "links": [{"title": "Bombing of Nijmegen", "link": "https://wikipedia.org/wiki/Bombing_of_Nijmegen"}, {"title": "Nijmegen", "link": "https://wikipedia.org/wiki/Nijmegen"}, {"title": "Arnhem", "link": "https://wikipedia.org/wiki/Arnhem"}, {"title": "Enschede", "link": "https://wikipedia.org/wiki/Enschede"}, {"title": "Deventer", "link": "https://wikipedia.org/wiki/Deventer"}]}, {"year": "1944", "text": "World War II: The Soviet Red Army recaptures Krivoi Rog.", "html": "1944 - World War II: The Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> recaptures <a href=\"https://wikipedia.org/wiki/Krivoi_Rog\" class=\"mw-redirect\" title=\"Krivoi Rog\">Krivoi Rog</a>.", "no_year_html": "World War II: The Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> recaptures <a href=\"https://wikipedia.org/wiki/Krivoi_Rog\" class=\"mw-redirect\" title=\"Krivoi Rog\">Krivoi Rog</a>.", "links": [{"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Krivoi Rog", "link": "https://wikipedia.org/wiki/<PERSON>rivoi_Rog"}]}, {"year": "1946", "text": "The \"Long Telegram\", proposing how the United States should deal with the Soviet Union, arrives from the US embassy in Moscow.", "html": "1946 - The \"<a href=\"https://wikipedia.org/wiki/X_Article\" title=\"X Article\">Long Telegram</a>\", proposing how the United States should deal with the Soviet Union, arrives from the US embassy in Moscow.", "no_year_html": "The \"<a href=\"https://wikipedia.org/wiki/X_Article\" title=\"X Article\">Long Telegram</a>\", proposing how the United States should deal with the Soviet Union, arrives from the US embassy in Moscow.", "links": [{"title": "X Article", "link": "https://wikipedia.org/wiki/X_Article"}]}, {"year": "1957", "text": "<PERSON><PERSON> of South Vietnam survives a communist shooting assassination attempt in Buôn Ma Thuột.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> survives a <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">communist</a> shooting assassination attempt in <a href=\"https://wikipedia.org/wiki/Bu%C3%B4n_Ma_Thu%E1%BB%99t\" title=\"<PERSON><PERSON>ô<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m\" class=\"mw-redirect\" title=\"Ng<PERSON> Đ<PERSON>\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> survives a <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">communist</a> shooting assassination attempt in <a href=\"https://wikipedia.org/wiki/Bu%C3%B4n_Ma_Thu%E1%BB%99t\" title=\"Buôn <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bu%C3%B4n_Ma_Thu%E1%BB%99t"}]}, {"year": "1958", "text": "Following a plebiscite in both countries the previous day, Egypt and Syria join to form the United Arab Republic.", "html": "1958 - Following a plebiscite in both countries the previous day, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> and <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> join to form the <a href=\"https://wikipedia.org/wiki/United_Arab_Republic\" title=\"United Arab Republic\">United Arab Republic</a>.", "no_year_html": "Following a plebiscite in both countries the previous day, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> and <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> join to form the <a href=\"https://wikipedia.org/wiki/United_Arab_Republic\" title=\"United Arab Republic\">United Arab Republic</a>.", "links": [{"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}, {"title": "United Arab Republic", "link": "https://wikipedia.org/wiki/United_Arab_Republic"}]}, {"year": "1959", "text": "<PERSON> wins the first Daytona 500.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Petty\"><PERSON></a> wins <a href=\"https://wikipedia.org/wiki/1959_Daytona_500\" title=\"1959 Daytona 500\">the first</a> <a href=\"https://wikipedia.org/wiki/Daytona_500\" title=\"Daytona 500\">Daytona 500</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lee Petty\"><PERSON></a> wins <a href=\"https://wikipedia.org/wiki/1959_Daytona_500\" title=\"1959 Daytona 500\">the first</a> <a href=\"https://wikipedia.org/wiki/Daytona_500\" title=\"Daytona 500\">Daytona 500</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1959 Daytona 500", "link": "https://wikipedia.org/wiki/1959_Daytona_500"}, {"title": "Daytona 500", "link": "https://wikipedia.org/wiki/Daytona_500"}]}, {"year": "1972", "text": "The Official Irish Republican Army detonates a car bomb at Aldershot barracks, killing seven and injuring nineteen others.", "html": "1972 - The <a href=\"https://wikipedia.org/wiki/Official_Irish_Republican_Army\" title=\"Official Irish Republican Army\">Official Irish Republican Army</a> <a href=\"https://wikipedia.org/wiki/1972_Aldershot_bombing\" title=\"1972 Aldershot bombing\">detonates</a> a car bomb at <a href=\"https://wikipedia.org/wiki/Aldershot\" title=\"Aldershot\">Aldershot</a> barracks, killing seven and injuring nineteen others.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Official_Irish_Republican_Army\" title=\"Official Irish Republican Army\">Official Irish Republican Army</a> <a href=\"https://wikipedia.org/wiki/1972_Aldershot_bombing\" title=\"1972 Aldershot bombing\">detonates</a> a car bomb at <a href=\"https://wikipedia.org/wiki/Aldershot\" title=\"Aldershot\">Aldershot</a> barracks, killing seven and injuring nineteen others.", "links": [{"title": "Official Irish Republican Army", "link": "https://wikipedia.org/wiki/Official_Irish_Republican_Army"}, {"title": "1972 Aldershot bombing", "link": "https://wikipedia.org/wiki/1972_Aldershot_bombing"}, {"title": "Aldershot", "link": "https://wikipedia.org/wiki/Aldershot"}]}, {"year": "1973", "text": "Cold War: Following President <PERSON>'s visit to the People's Republic of China, the two countries agree to establish liaison offices.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Following President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/<PERSON>_visit_to_China_1972\" class=\"mw-redirect\" title=\"<PERSON> visit to China 1972\">visit</a> to the People's Republic of China, the two countries agree to establish <a href=\"https://wikipedia.orghttps://en.wiktionary.org/wiki/liaison\" class=\"extiw\" title=\"wikt:liaison\">liaison</a> offices.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Following President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/<PERSON>_visit_to_China_1972\" class=\"mw-redirect\" title=\"Nixon visit to China 1972\">visit</a> to the People's Republic of China, the two countries agree to establish <a href=\"https://wikipedia.orghttps://en.wiktionary.org/wiki/liaison\" class=\"extiw\" title=\"wikt:liaison\">liaison</a> offices.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> visit to China 1972", "link": "https://wikipedia.org/wiki/<PERSON>_visit_to_China_1972"}, {"title": "wikt:liaison", "link": "https://wikipedia.orghttps://en.wiktionary.org/wiki/liaison"}]}, {"year": "1974", "text": "The Organisation of the Islamic Conference summit begins in Lahore, Pakistan. Thirty-seven countries attend and twenty-two heads of state and government participate. It also recognizes Bangladesh.", "html": "1974 - The <a href=\"https://wikipedia.org/wiki/Organisation_of_Islamic_Cooperation\" title=\"Organisation of Islamic Cooperation\">Organisation of the Islamic Conference</a> summit begins in <a href=\"https://wikipedia.org/wiki/Lahore\" title=\"Lahore\">Lahore</a>, Pakistan. Thirty-seven countries attend and twenty-two heads of state and government participate. It also recognizes <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Organisation_of_Islamic_Cooperation\" title=\"Organisation of Islamic Cooperation\">Organisation of the Islamic Conference</a> summit begins in <a href=\"https://wikipedia.org/wiki/Lahore\" title=\"Lahore\">Lahore</a>, Pakistan. Thirty-seven countries attend and twenty-two heads of state and government participate. It also recognizes <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "links": [{"title": "Organisation of Islamic Cooperation", "link": "https://wikipedia.org/wiki/Organisation_of_Islamic_Cooperation"}, {"title": "Lahore", "link": "https://wikipedia.org/wiki/Lahore"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}]}, {"year": "1974", "text": "<PERSON> attempts to hijack an aircraft at Baltimore/Washington International Airport with the intention of crashing it into the White House to assassinate <PERSON>, but commits suicide after being wounded by police.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> attempts to hijack an aircraft at <a href=\"https://wikipedia.org/wiki/Baltimore/Washington_International_Airport\" title=\"Baltimore/Washington International Airport\">Baltimore/Washington International Airport</a> with the intention of crashing it into the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> to assassinate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, but commits suicide after being wounded by police.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> attempts to hijack an aircraft at <a href=\"https://wikipedia.org/wiki/Baltimore/Washington_International_Airport\" title=\"Baltimore/Washington International Airport\">Baltimore/Washington International Airport</a> with the intention of crashing it into the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> to assassinate <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, but commits suicide after being wounded by police.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Baltimore/Washington International Airport", "link": "https://wikipedia.org/wiki/Baltimore/Washington_International_Airport"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "Saint Lucia gains independence from the United Kingdom.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Saint_Lucia\" title=\"Saint Lucia\">Saint Lucia</a> gains independence from the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Lucia\" title=\"Saint Lucia\">Saint Lucia</a> gains independence from the United Kingdom.", "links": [{"title": "Saint Lucia", "link": "https://wikipedia.org/wiki/Saint_Lucia"}]}, {"year": "1980", "text": "Miracle on Ice: In Lake Placid, New York, the United States hockey team defeats the Soviet Union hockey team 4-3.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Miracle_on_Ice\" title=\"Miracle on Ice\">Miracle on Ice</a>: In <a href=\"https://wikipedia.org/wiki/Lake_Placid,_New_York\" title=\"Lake Placid, New York\">Lake Placid, New York</a>, the United States hockey team defeats the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> hockey team 4-3.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miracle_on_Ice\" title=\"Miracle on Ice\">Miracle on Ice</a>: In <a href=\"https://wikipedia.org/wiki/Lake_Placid,_New_York\" title=\"Lake Placid, New York\">Lake Placid, New York</a>, the United States hockey team defeats the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> hockey team 4-3.", "links": [{"title": "Miracle on Ice", "link": "https://wikipedia.org/wiki/<PERSON>_on_Ice"}, {"title": "Lake Placid, New York", "link": "https://wikipedia.org/wiki/Lake_Placid,_New_York"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1983", "text": "The notorious Broadway flop Moose Murders opens and closes on the same night at the Eugene O'Neill Theatre.", "html": "1983 - The notorious <a href=\"https://wikipedia.org/wiki/Broadway_theatre\" title=\"Broadway theatre\">Broadway</a> flop <i><a href=\"https://wikipedia.org/wiki/Moose_Murders\" title=\"Moose Murders\">Moose Murders</a></i> opens and closes on the same night at the <a href=\"https://wikipedia.org/wiki/Eugene_O%27Neill_Theatre\" title=\"Eugene O'Neill Theatre\">Eugene <PERSON> Theatre</a>.", "no_year_html": "The notorious <a href=\"https://wikipedia.org/wiki/Broadway_theatre\" title=\"Broadway theatre\">Broadway</a> flop <i><a href=\"https://wikipedia.org/wiki/Moose_Murders\" title=\"Moose Murders\">Moose Murders</a></i> opens and closes on the same night at the <a href=\"https://wikipedia.org/wiki/Eugene_O%27Neill_Theatre\" title=\"Eugene O'Neill Theatre\">Eugene <PERSON> Theatre</a>.", "links": [{"title": "Broadway theatre", "link": "https://wikipedia.org/wiki/Broadway_theatre"}, {"title": "Moose Murders", "link": "https://wikipedia.org/wiki/Moose_Murders"}, {"title": "Eugene <PERSON> Theatre", "link": "https://wikipedia.org/wiki/Eugene_O%27Neill_Theatre"}]}, {"year": "1986", "text": "Start of the People Power Revolution in the Philippines.", "html": "1986 - Start of the <a href=\"https://wikipedia.org/wiki/People_Power_Revolution\" title=\"People Power Revolution\">People Power Revolution</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "no_year_html": "Start of the <a href=\"https://wikipedia.org/wiki/People_Power_Revolution\" title=\"People Power Revolution\">People Power Revolution</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "links": [{"title": "People Power Revolution", "link": "https://wikipedia.org/wiki/People_Power_Revolution"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON> and his wife are charged by the United States Department of Justice with spying for the Soviet Union.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and his wife are charged by the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a> with spying for the Soviet Union.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and his wife are charged by the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a> with spying for the Soviet Union.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "United States Department of Justice", "link": "https://wikipedia.org/wiki/United_States_Department_of_Justice"}]}, {"year": "1995", "text": "The Corona reconnaissance satellite program, in existence from 1959 to 1972, is declassified.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/Corona_(satellite)\" class=\"mw-redirect\" title=\"Corona (satellite)\">Corona</a> <a href=\"https://wikipedia.org/wiki/Reconnaissance_satellite\" title=\"Reconnaissance satellite\">reconnaissance satellite</a> program, in existence from 1959 to 1972, is declassified.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Corona_(satellite)\" class=\"mw-redirect\" title=\"Corona (satellite)\">Corona</a> <a href=\"https://wikipedia.org/wiki/Reconnaissance_satellite\" title=\"Reconnaissance satellite\">reconnaissance satellite</a> program, in existence from 1959 to 1972, is declassified.", "links": [{"title": "Corona (satellite)", "link": "https://wikipedia.org/wiki/Corona_(satellite)"}, {"title": "Reconnaissance satellite", "link": "https://wikipedia.org/wiki/Reconnaissance_satellite"}]}, {"year": "1997", "text": "In Roslin, Midlothian, British scientists announce that an adult sheep named <PERSON> has been successfully cloned.", "html": "1997 - In <a href=\"https://wikipedia.org/wiki/Roslin,_Midlothian\" title=\"Roslin, Midlothian\">Roslin, Midlothian</a>, British scientists announce that an adult sheep named <a href=\"https://wikipedia.org/wiki/<PERSON>_the_sheep\" class=\"mw-redirect\" title=\"<PERSON> the sheep\"><PERSON></a> has been successfully <a href=\"https://wikipedia.org/wiki/Cloning\" title=\"Cloning\">cloned</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Roslin,_Midlothian\" title=\"Roslin, Midlothian\">Roslin, Midlothian</a>, British scientists announce that an adult sheep named <a href=\"https://wikipedia.org/wiki/<PERSON>_the_sheep\" class=\"mw-redirect\" title=\"<PERSON> the sheep\"><PERSON></a> has been successfully <a href=\"https://wikipedia.org/wiki/Cloning\" title=\"Cloning\">cloned</a>.", "links": [{"title": "Roslin, Midlothian", "link": "https://wikipedia.org/wiki/Roslin,_Midlothian"}, {"title": "<PERSON> the sheep", "link": "https://wikipedia.org/wiki/<PERSON>_the_sheep"}, {"title": "Cloning", "link": "https://wikipedia.org/wiki/Cloning"}]}, {"year": "2002", "text": "Angolan political and rebel leader <PERSON> is killed in a military ambush.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angolan</a> political and rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed in a military ambush.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angolan</a> political and rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed in a military ambush.", "links": [{"title": "Angola", "link": "https://wikipedia.org/wiki/Angola"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "The 6.4 Mw  Zarand earthquake shakes the Kerman province of Iran with a maximum Mercalli intensity of VIII (Severe), leaving 612 people dead and 1,411 injured.", "html": "2005 - The 6.4 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2005_Zarand_earthquake\" title=\"2005 Zarand earthquake\">Zarand earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Kerman_province\" title=\"Kerman province\">Kerman province</a> of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>), leaving 612 people dead and 1,411 injured.", "no_year_html": "The 6.4 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2005_Zarand_earthquake\" title=\"2005 Zarand earthquake\">Zarand earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Kerman_province\" title=\"Kerman province\">Kerman province</a> of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>), leaving 612 people dead and 1,411 injured.", "links": [{"title": "2005 Zarand earthquake", "link": "https://wikipedia.org/wiki/2005_Zarand_earthquake"}, {"title": "Kerman province", "link": "https://wikipedia.org/wiki/Kerman_province"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "2006", "text": "At approximately 6:44 a.m.  local Iraqi time, explosions occurred at the al-Askari Shrine in Samarra, Iraq. The attack on the shrine, one of the holiest sites in Shia Islam, caused the escalation of sectarian tensions in Iraq into a full-scale civil war.", "html": "2006 - At approximately 6:44 a.m. <a href=\"https://wikipedia.org/wiki/UTC%2B03:00\" title=\"UTC+03:00\">local Iraqi time</a>, <a href=\"https://wikipedia.org/wiki/2006_al-Askari_mosque_bombing\" title=\"2006 al-Askari mosque bombing\">explosions occurred</a> at the <a href=\"https://wikipedia.org/wiki/Al-Askari_Shrine\" title=\"Al-Askari Shrine\">al-Askari Shrine</a> in <a href=\"https://wikipedia.org/wiki/Samarra\" title=\"Samarra\">Samarra</a>, Iraq. The attack on the shrine, one of the holiest sites in <a href=\"https://wikipedia.org/wiki/Shia_Islam\" title=\"Shia Islam\">Shia Islam</a>, caused the escalation of <a href=\"https://wikipedia.org/wiki/Sectarian_violence_in_Iraq\" title=\"Sectarian violence in Iraq\">sectarian tensions in Iraq</a> into a <a href=\"https://wikipedia.org/wiki/Iraqi_Civil_War_(2006-2008)\" class=\"mw-redirect\" title=\"Iraqi Civil War (2006-2008)\">full-scale civil war</a>.", "no_year_html": "At approximately 6:44 a.m. <a href=\"https://wikipedia.org/wiki/UTC%2B03:00\" title=\"UTC+03:00\">local Iraqi time</a>, <a href=\"https://wikipedia.org/wiki/2006_al-Askari_mosque_bombing\" title=\"2006 al-Askari mosque bombing\">explosions occurred</a> at the <a href=\"https://wikipedia.org/wiki/Al-Askari_Shrine\" title=\"Al-Askari Shrine\">al-Askari Shrine</a> in <a href=\"https://wikipedia.org/wiki/Samarra\" title=\"Samarra\">Samarra</a>, Iraq. The attack on the shrine, one of the holiest sites in <a href=\"https://wikipedia.org/wiki/Shia_Islam\" title=\"Shia Islam\">Shia Islam</a>, caused the escalation of <a href=\"https://wikipedia.org/wiki/Sectarian_violence_in_Iraq\" title=\"Sectarian violence in Iraq\">sectarian tensions in Iraq</a> into a <a href=\"https://wikipedia.org/wiki/Iraqi_Civil_War_(2006-2008)\" class=\"mw-redirect\" title=\"Iraqi Civil War (2006-2008)\">full-scale civil war</a>.", "links": [{"title": "UTC+03:00", "link": "https://wikipedia.org/wiki/UTC%2B03:00"}, {"title": "2006 al-Askari mosque bombing", "link": "https://wikipedia.org/wiki/2006_al-Askari_mosque_bombing"}, {"title": "Al-Askari Shrine", "link": "https://wikipedia.org/wiki/Al-Askari_Shrine"}, {"title": "Samarra", "link": "https://wikipedia.org/wiki/Samarra"}, {"title": "Shia Islam", "link": "https://wikipedia.org/wiki/Shia_Islam"}, {"title": "Sectarian violence in Iraq", "link": "https://wikipedia.org/wiki/Sectarian_violence_in_Iraq"}, {"title": "Iraqi Civil War (2006-2008)", "link": "https://wikipedia.org/wiki/Iraqi_Civil_War_(2006-2008)"}]}, {"year": "2006", "text": "The Securitas depot robbery was the UK's largest heist. Almost £53m (about $92.5 million or €78 million) was stolen from a Securitas depot in Tonbridge, Kent.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/Securitas_depot_robbery\" title=\"Securitas depot robbery\">Securitas depot robbery</a> was the UK's largest heist. Almost £53m (about $92.5 million or €78 million) was stolen from a Securitas depot in <a href=\"https://wikipedia.org/wiki/Tonbridge\" title=\"Tonbridge\">Tonbridge</a>, <a href=\"https://wikipedia.org/wiki/Kent\" title=\"Kent\">Kent</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Securitas_depot_robbery\" title=\"Securitas depot robbery\">Securitas depot robbery</a> was the UK's largest heist. Almost £53m (about $92.5 million or €78 million) was stolen from a Securitas depot in <a href=\"https://wikipedia.org/wiki/Tonbridge\" title=\"Tonbridge\">Tonbridge</a>, <a href=\"https://wikipedia.org/wiki/Kent\" title=\"Kent\">Kent</a>.", "links": [{"title": "Securitas depot robbery", "link": "https://wikipedia.org/wiki/Securitas_depot_robbery"}, {"title": "Tonbridge", "link": "https://wikipedia.org/wiki/Tonbridge"}, {"title": "Kent", "link": "https://wikipedia.org/wiki/Kent"}]}, {"year": "2011", "text": "New Zealand's second deadliest earthquake, the 2011 Christchurch earthquake, kills 185 people.", "html": "2011 - New Zealand's second deadliest earthquake, the <a href=\"https://wikipedia.org/wiki/2011_Christchurch_earthquake\" title=\"2011 Christchurch earthquake\">2011 Christchurch earthquake</a>, kills 185 people.", "no_year_html": "New Zealand's second deadliest earthquake, the <a href=\"https://wikipedia.org/wiki/2011_Christchurch_earthquake\" title=\"2011 Christchurch earthquake\">2011 Christchurch earthquake</a>, kills 185 people.", "links": [{"title": "2011 Christchurch earthquake", "link": "https://wikipedia.org/wiki/2011_Christchurch_earthquake"}]}, {"year": "2011", "text": "Bahraini uprising: Tens of thousands of people march in protest against the deaths of seven victims killed by police and army forces during previous protests.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/2011_Bahraini_uprising\" title=\"2011 Bahraini uprising\">Bahraini uprising</a>: Tens of thousands of people <a href=\"https://wikipedia.org/wiki/March_of_loyalty_to_martyrs\" title=\"March of loyalty to martyrs\">march in protest</a> against the deaths of seven victims killed by police and army forces during previous protests.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2011_Bahraini_uprising\" title=\"2011 Bahraini uprising\">Bahraini uprising</a>: Tens of thousands of people <a href=\"https://wikipedia.org/wiki/March_of_loyalty_to_martyrs\" title=\"March of loyalty to martyrs\">march in protest</a> against the deaths of seven victims killed by police and army forces during previous protests.", "links": [{"title": "2011 Bahraini uprising", "link": "https://wikipedia.org/wiki/2011_Bahraini_uprising"}, {"title": "March of loyalty to martyrs", "link": "https://wikipedia.org/wiki/March_of_loyalty_to_martyrs"}]}, {"year": "2012", "text": "A train crash in Buenos Aires, Argentina, kills 51 people and injures 700 others.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/2012_Buenos_Aires_rail_disaster\" title=\"2012 Buenos Aires rail disaster\">train crash</a> in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, Argentina, kills 51 people and injures 700 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2012_Buenos_Aires_rail_disaster\" title=\"2012 Buenos Aires rail disaster\">train crash</a> in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, Argentina, kills 51 people and injures 700 others.", "links": [{"title": "2012 Buenos Aires rail disaster", "link": "https://wikipedia.org/wiki/2012_Buenos_Aires_rail_disaster"}, {"title": "Buenos Aires", "link": "https://wikipedia.org/wiki/Buenos_Aires"}]}, {"year": "2014", "text": "President <PERSON> of Ukraine is impeached by the Verkhovna Rada of Ukraine by a vote of 328-0, fulfilling a major goal of the Euromaidan rebellion.", "html": "2014 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> is impeached by the <a href=\"https://wikipedia.org/wiki/Verkhovna_Rada\" title=\"Verkhovna Rada\">Verkhovna Rada</a> of Ukraine by a vote of 328-0, fulfilling a major goal of the <a href=\"https://wikipedia.org/wiki/Euromaidan\" title=\"Euromaidan\">Euromaidan</a> rebellion.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> is impeached by the <a href=\"https://wikipedia.org/wiki/Verkhovna_Rada\" title=\"Verkhovna Rada\">Verkhovna Rada</a> of Ukraine by a vote of 328-0, fulfilling a major goal of the <a href=\"https://wikipedia.org/wiki/Euromaidan\" title=\"Euromaidan\">Euromaidan</a> rebellion.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "Verkhovna Rada", "link": "https://wikipedia.org/wiki/Verkhovna_Rada"}, {"title": "Euromaidan", "link": "https://wikipedia.org/wiki/Euromaidan"}]}, {"year": "2015", "text": "A ferry carrying 100 passengers capsizes in the Padma River, killing 70 people.", "html": "2015 - A ferry carrying 100 passengers <a href=\"https://wikipedia.org/wiki/Sinking_of_the_ML_Mostofa-3\" class=\"mw-redirect\" title=\"Sinking of the ML Mostofa-3\">capsizes</a> in the <a href=\"https://wikipedia.org/wiki/Padma_River\" title=\"Padma River\">Padma River</a>, killing 70 people.", "no_year_html": "A ferry carrying 100 passengers <a href=\"https://wikipedia.org/wiki/Sinking_of_the_ML_Mostofa-3\" class=\"mw-redirect\" title=\"Sinking of the ML Mostofa-3\">capsizes</a> in the <a href=\"https://wikipedia.org/wiki/Padma_River\" title=\"Padma River\">Padma River</a>, killing 70 people.", "links": [{"title": "Sinking of the ML Mostofa-3", "link": "https://wikipedia.org/wiki/Sinking_of_the_ML_Mostofa-3"}, {"title": "Padma River", "link": "https://wikipedia.org/wiki/Padma_River"}]}, {"year": "2018", "text": "A man throws a grenade at the U.S. embassy in Podgorica, Montenegro. He dies at the scene from a second explosion, with no one else hurt.", "html": "2018 - A man <a href=\"https://wikipedia.org/wiki/2018_United_States_embassy_attack_in_Podgorica\" title=\"2018 United States embassy attack in Podgorica\">throws a grenade</a> at the U.S. embassy in <a href=\"https://wikipedia.org/wiki/Podgorica\" title=\"Podgorica\">Podgorica</a>, Montenegro. He dies at the scene from a second explosion, with no one else hurt.", "no_year_html": "A man <a href=\"https://wikipedia.org/wiki/2018_United_States_embassy_attack_in_Podgorica\" title=\"2018 United States embassy attack in Podgorica\">throws a grenade</a> at the U.S. embassy in <a href=\"https://wikipedia.org/wiki/Podgorica\" title=\"Podgorica\">Podgorica</a>, Montenegro. He dies at the scene from a second explosion, with no one else hurt.", "links": [{"title": "2018 United States embassy attack in Podgorica", "link": "https://wikipedia.org/wiki/2018_United_States_embassy_attack_in_Podgorica"}, {"title": "Podgorica", "link": "https://wikipedia.org/wiki/Podgorica"}]}, {"year": "2022", "text": "Twosday, the name given to Tuesday, February 22, 2022, at 2:22:22, occurs.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Twosday\" title=\"Twosday\">Twosday</a>, the name given to Tuesday, February 22, 2022, at 2:22:22, occurs.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Twosday\" title=\"Twosday\">Twosday</a>, the name given to Tuesday, February 22, 2022, at 2:22:22, occurs.", "links": [{"title": "Twosday", "link": "https://wikipedia.org/wiki/Twosday"}]}], "Births": [{"year": "1040", "text": "<PERSON><PERSON>, French rabbi and author (d. 1105)", "html": "1040 - <a href=\"https://wikipedia.org/wiki/Ra<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French rabbi and author (d. 1105)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French rabbi and author (d. 1105)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rashi"}]}, {"year": "1403", "text": "<PERSON> of France (d. 1461)", "html": "1403 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VII of France\"><PERSON> of France</a> (d. 1461)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VII of France\"><PERSON> of France</a> (d. 1461)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_France"}]}, {"year": "1440", "text": "<PERSON><PERSON><PERSON> the Posthumous, Hungarian King (d. 1457)", "html": "1440 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Posthumous\" title=\"<PERSON><PERSON><PERSON> the Posthumous\"><PERSON><PERSON><PERSON> the Posthumous</a>, Hungarian King (d. 1457)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Posthumous\" title=\"<PERSON><PERSON><PERSON> the Posthumous\"><PERSON><PERSON><PERSON> the Posthumous</a>, Hungarian King (d. 1457)", "links": [{"title": "<PERSON><PERSON><PERSON> the Posthumous", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Posthumous"}]}, {"year": "1500", "text": "<PERSON><PERSON><PERSON>, Italian cardinal (d. 1564)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1564)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1514", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian shah (d. 1576)", "html": "1514 - <a href=\"https://wikipedia.org/wiki/Tahmasp_I\" title=\"Tahmasp I\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Iranian shah (d. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hmasp_I\" title=\"Tahmasp I\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Iranian shah (d. 1576)", "links": [{"title": "Tahmasp I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I"}]}, {"year": "1520", "text": "<PERSON>, Polish rabbi (d. 1572)", "html": "1520 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish rabbi (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish rabbi (d. 1572)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1550", "text": "<PERSON>, 2nd Prince of Arenberg (d. 1616)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Prince_of_Arenberg\" title=\"<PERSON>, 2nd Prince of Arenberg\"><PERSON>, 2nd Prince of Arenberg</a> (d. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Prince_of_Arenberg\" title=\"<PERSON>, 2nd Prince of Arenberg\"><PERSON>, 2nd Prince of Arenberg</a> (d. 1616)", "links": [{"title": "<PERSON>, 2nd Prince of Arenberg", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Prince_of_Arenberg"}]}, {"year": "1592", "text": "<PERSON>, English scholar (d. 1637)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar (d. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar (d. 1637)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1631", "text": "<PERSON><PERSON><PERSON>, Danish historian (d. 1702)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/Peder_Syv\" title=\"Peder Syv\"><PERSON><PERSON><PERSON></a>, Danish historian (d. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peder_Syv\" title=\"Peder Syv\"><PERSON><PERSON><PERSON></a>, Danish historian (d. 1702)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Peder_Syv"}]}, {"year": "1649", "text": "<PERSON>, French painter (d. 1717)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Boullogne\"><PERSON></a>, French painter (d. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Boullogne\"><PERSON></a>, French painter (d. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON><PERSON><PERSON>, French artist (d. 1790)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French artist (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French artist (d. 1790)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1732", "text": "<PERSON>, American general and politician, 1st President of the United States (d. 1799)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\"><PERSON></a>, American general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\"><PERSON></a>, American general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1749", "text": "<PERSON>, German musicologist and theorist (d. 1818)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German musicologist and theorist (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German musicologist and theorist (d. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON><PERSON><PERSON><PERSON>, American painter and curator (d. 1860)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American painter and curator (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American painter and curator (d. 1860)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, German philosopher and author (d. 1860)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, French priest and missionary (d. 1837)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and missionary (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and missionary (d. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON><PERSON><PERSON>, Belgian mathematician, astronomer, and sociologist (d. 1874)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian mathematician, astronomer, and sociologist (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian mathematician, astronomer, and sociologist (d. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, English poet and hymnwriter (d. 1848)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and hymnwriter (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and hymnwriter (d. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON><PERSON><PERSON>, Polish historian and philosopher (d. 1875)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish historian and philosopher (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish historian and philosopher (d. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, German mathematician and academic (d. 1880)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, American poet and critic (d. 1891)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, French astronomer and mathematician (d. 1907)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and mathematician (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and mathematician (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1825", "text": "<PERSON><PERSON><PERSON>, French-American archbishop (d. 1898)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American archbishop (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American archbishop (d. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1836", "text": "<PERSON><PERSON><PERSON>, Indian scholar and academic (d. 1906)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>charyya\" title=\"<PERSON><PERSON><PERSON> <PERSON> Bhat<PERSON>charyya\"><PERSON><PERSON><PERSON></a>, Indian scholar and academic (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>charyya\" title=\"<PERSON><PERSON><PERSON> <PERSON>hat<PERSON>charyya\"><PERSON><PERSON><PERSON></a>, Indian scholar and academic (d. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>cha<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, German theorist and politician (d. 1913)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>\" title=\"August Bebel\">August <PERSON></a>, German theorist and politician (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Bebel\" title=\"August Bebel\">August <PERSON></a>, German theorist and politician (d. 1913)", "links": [{"title": "August Bebel", "link": "https://wikipedia.org/wiki/August_Bebel"}]}, {"year": "1849", "text": "<PERSON><PERSON>, Russian mathematician and academic (d. 1915)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and academic (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and academic (d. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, 1st Baron <PERSON>, English general, co-founded The Scout Association (d. 1941)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English general, co-founded <a href=\"https://wikipedia.org/wiki/The_Scout_Association\" title=\"The Scout Association\">The Scout Association</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English general, co-founded <a href=\"https://wikipedia.org/wiki/The_Scout_Association\" title=\"The Scout Association\">The Scout Association</a> (d. 1941)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "The Scout Association", "link": "https://wikipedia.org/wiki/The_Scout_Association"}]}, {"year": "1857", "text": "<PERSON>, German physicist, philosopher, and academic (d. 1894)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist, philosopher, and academic (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist, philosopher, and academic (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, American physician and Baptist medical missionary (d. 1939)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and Baptist medical missionary (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and Baptist medical missionary (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American academic (d. 1961)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American historian, author, and academic (d. 1943)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, French author and playwright (d. 1910)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American baseball player and umpire (d. 1951)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, American author and activist (d. 1938)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Zitkala-Sa\" title=\"Zitkala-Sa\"><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, American author and activist (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zitkala-Sa\" title=\"Zitkala-Sa\"><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, American author and activist (d. 1938)", "links": [{"title": "Zitkala-Sa", "link": "https://wikipedia.org/wiki/Zitkala-Sa"}]}, {"year": "1879", "text": "<PERSON>, Danish chemist and academic (d. 1947)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%B8nsted\" title=\"<PERSON>\"><PERSON></a>, Danish chemist and academic (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%B8nsted\" title=\"<PERSON>\"><PERSON></a>, Danish chemist and academic (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Br%C3%B8nsted"}]}, {"year": "1880", "text": "<PERSON>, Swedish athlete (d. 1930)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish athlete (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish athlete (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American lawyer and politician, 52nd Governor of Massachusetts (d. 1956)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1881", "text": "<PERSON><PERSON>, Slovenian journalist and politician (d. 1937)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Albin_Prepeluh\" title=\"Albin Prepeluh\"><PERSON><PERSON></a>, Slovenian journalist and politician (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Albin_Prepeluh\" title=\"Albin Prepeluh\"><PERSON><PERSON></a>, Slovenian journalist and politician (d. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al<PERSON>_<PERSON>peluh"}]}, {"year": "1882", "text": "<PERSON>, English sculptor and illustrator (d. 1940)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and illustrator (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and illustrator (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American actress (d. 1940)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, German author and poet (d. 1927)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ball\"><PERSON></a>, German author and poet (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hugo Ball\"><PERSON></a>, German author and poet (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Polish journalist, author, and chess player (d. 1956)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Savielly_Tartakower\" title=\"Savielly Tartakower\"><PERSON><PERSON><PERSON></a>, Polish journalist, author, and chess player (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Savielly_Tartakower\" title=\"Savielly Tartakower\"><PERSON><PERSON><PERSON></a>, Polish journalist, author, and chess player (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>vie<PERSON>_<PERSON>wer"}]}, {"year": "1887", "text": "<PERSON>, Australian-American animator and producer (d. 1933)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)\" title=\"<PERSON> (film producer)\"><PERSON></a>, Australian-American animator and producer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)\" title=\"<PERSON> (film producer)\"><PERSON></a>, Australian-American animator and producer (d. 1933)", "links": [{"title": "<PERSON> (film producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)"}]}, {"year": "1888", "text": "<PERSON>, American captain and politician, 54th Governor of Maine (d. 1961)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Maine", "link": "https://wikipedia.org/wiki/Governor_of_Maine"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, English scout leader, first World Chief Guide (d. 1977)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English scout leader, first <a href=\"https://wikipedia.org/wiki/Chief_Guide\" title=\"Chief Guide\">World Chief Guide</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English scout leader, first <a href=\"https://wikipedia.org/wiki/Chief_Guide\" title=\"Chief Guide\">World Chief Guide</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>-Powell", "link": "https://wikipedia.org/wiki/<PERSON>lave_Baden-Powell"}, {"title": "Chief Guide", "link": "https://wikipedia.org/wiki/Chief_Guide"}]}, {"year": "1889", "text": "<PERSON><PERSON> <PERSON><PERSON>, English historian and philosopher (d. 1943)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"R. G. Collingwood\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and philosopher (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"R. G. Collingwood\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and philosopher (d. 1943)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON>, Russian economist and politician (d. 1939)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian economist and politician (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian economist and politician (d. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>las_Chubar"}]}, {"year": "1892", "text": "<PERSON>, American poet and playwright (d. 1950)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Edna_<PERSON>._<PERSON>_<PERSON>\" title=\"Edna <PERSON> Vincent <PERSON>\"><PERSON></a>, American poet and playwright (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edna_St._<PERSON>_<PERSON>\" title=\"Edna St. Vincent <PERSON>\"><PERSON></a>, American poet and playwright (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edna_St._<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Peruvian politician (d. 1979)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON><PERSON>_de_la_Torre\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian politician (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_Ra%C3%<PERSON><PERSON>_<PERSON><PERSON>_de_la_Torre\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian politician (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%ADctor_Ra%C3%BA<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_Torre"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Polish general (d. 1947)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%9Awiercz<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish general (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%9A<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish general (d. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Karol_%C5%9A<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American actor and screenwriter (d. 1966)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27H<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27H<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (d. 1966)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27H<PERSON>_(actor)"}]}, {"year": "1900", "text": "<PERSON>, Spanish-Mexican director and producer (d. 1983)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>u%C3%B1uel\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, Spanish-Mexican director and producer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>u%C3%B1uel\" title=\"<PERSON>\"><PERSON></a>, Spanish-Mexican director and producer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Bu%C3%B1uel"}]}, {"year": "1903", "text": "<PERSON>, Canadian author and playwright (d. 1990)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and playwright (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and playwright (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English economist, mathematician, and philosopher (d. 1930)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English economist, mathematician, and philosopher (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English economist, mathematician, and philosopher (d. 1930)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>(mathematician)"}]}, {"year": "1906", "text": "<PERSON>, Australian painter (d. 1991)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American actor, director, and producer (d. 1997)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American actor (d. 1998)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1998)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, Venezuelan politician, 56th President of Venezuela (d. 1981)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/R%C3%B3<PERSON><PERSON>_Betancourt\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan politician, 56th <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%B3<PERSON><PERSON>_Betancourt\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan politician, 56th <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%B3mu<PERSON>_Betancourt"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "1908", "text": "<PERSON>, English actor (d. 2005)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English footballer (d. 1996)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1910)\" title=\"<PERSON> (footballer, born 1910)\"><PERSON></a>, English footballer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1910)\" title=\"<PERSON> (footballer, born 1910)\"><PERSON></a>, English footballer (d. 1996)", "links": [{"title": "<PERSON> (footballer, born 1910)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1910)"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Italian-American virologist and academic, Nobel Prize laureate (d. 2012)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1915", "text": "<PERSON>, American boxer (d. 1964)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Canadian-American ice hockey player, coach, and manager (d. 2000)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player, coach, and manager (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player, coach, and manager (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American radio and television announcer (d. 2014)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television announcer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television announcer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American man, the tallest person in recorded history (d. 1940)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American man, the tallest person in recorded history (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American man, the tallest person in recorded history (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Central African general and politician, 2nd President of the Central African Republic (d. 1996)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Central African general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"President of the Central African Republic\">President of the Central African Republic</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Central African general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"President of the Central African Republic\">President of the Central African Republic</a> (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9del_<PERSON>"}, {"title": "President of the Central African Republic", "link": "https://wikipedia.org/wiki/President_of_the_Central_African_Republic"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian actress (d. 1994)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>lie<PERSON> Masin<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian actress (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Ma<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian actress (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giulie<PERSON>_Masina"}]}, {"year": "1921", "text": "<PERSON>, American race car driver (d. 1959)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 1959)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Cuban pianist (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Zenaida_Manfug%C3%A1s\" title=\"Zenaid<PERSON> Manfugás\"><PERSON><PERSON><PERSON></a>, Cuban pianist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zen<PERSON><PERSON>_Manfug%C3%A1s\" title=\"Zenaid<PERSON> Manfugás\"><PERSON><PERSON><PERSON></a>, Cuban pianist (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zenaida_Manfug%C3%A1s"}]}, {"year": "1922", "text": "<PERSON>, American trumpet player, composer, and bandleader (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and bandleader (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and bandleader (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, French author and editor (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Cavanna\" title=\"<PERSON>\"><PERSON></a>, French author and editor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Cavanna\" title=\"<PERSON>\"><PERSON></a>, French author and editor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Cavanna"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Welsh rugby player and sportscaster (d. 2009)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Welsh rugby player and sportscaster (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Welsh rugby player and sportscaster (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American illustrator and poet (d. 2000)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and poet (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and poet (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American poet and academic (d. 2022)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English actor and screenwriter (d. 1988)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipino political scientist and chess player (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>lore<PERSON><PERSON>_<PERSON>es\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Filipino political scientist and chess player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Filipino political scientist and chess player (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Florencio_Campomanes"}]}, {"year": "1927", "text": "<PERSON>, American singer (d. 1999)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American religious leader, founded the Nation of Gods and Earths (d. 1969)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_13X\" title=\"Clarence 13X\"><PERSON> 13X</a>, American religious leader, founded the <a href=\"https://wikipedia.org/wiki/Nation_of_Gods_and_Earths\" class=\"mw-redirect\" title=\"Nation of Gods and Earths\">Nation of Gods and Earths</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_13X\" title=\"Clarence 13X\"><PERSON> 13X</a>, American religious leader, founded the <a href=\"https://wikipedia.org/wiki/Nation_of_Gods_and_Earths\" class=\"mw-redirect\" title=\"Nation of Gods and Earths\">Nation of Gods and Earths</a> (d. 1969)", "links": [{"title": "Clarence 13X", "link": "https://wikipedia.org/wiki/Clarence_13X"}, {"title": "Nation of Gods and Earths", "link": "https://wikipedia.org/wiki/Nation_of_Gods_and_Earths"}]}, {"year": "1928", "text": "<PERSON> <PERSON>, American singer-songwriter and guitarist (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Texas_<PERSON>_<PERSON>\" title=\"Texas Johnny <PERSON>\">Texas <PERSON></a>, American singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Texas_<PERSON>_<PERSON>\" title=\"Texas Johnny <PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2013)", "links": [{"title": "Texas <PERSON>", "link": "https://wikipedia.org/wiki/Texas_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English singer and television host (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and television host (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and television host (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American computer scientist and educator (d. 2024)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and educator (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and educator (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American actor and director", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American stage, film, and television actress", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage, film, and television actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage, film, and television actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American soprano and actress (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American soldier, lawyer, and politician (d. 2009)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English actress and author", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Duchess of Kent", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Kent\" title=\"<PERSON><PERSON>, Duchess of Kent\"><PERSON><PERSON>, Duchess of Kent</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Kent\" title=\"<PERSON><PERSON>, Duchess of Kent\"><PERSON><PERSON>, Duchess of Kent</a>", "links": [{"title": "<PERSON><PERSON>, Duchess of Kent", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Kent"}]}, {"year": "1933", "text": "<PERSON>, American R&B singer (d. 2001)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English footballer (d. 2010)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1933)\" title=\"<PERSON> (footballer, born 1933)\"><PERSON></a>, English footballer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1933)\" title=\"<PERSON> (footballer, born 1933)\"><PERSON></a>, English footballer (d. 2010)", "links": [{"title": "<PERSON> (footballer, born 1933)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1933)"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American baseball player and manager (d. 2010)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American microbiologist and immunologist, Nobel Prize laureate", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American microbiologist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American microbiologist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1937", "text": "<PERSON>, American golfer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American author and activist (d. 2011)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American baseball player (d. 2007)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Gibraltarian born English footballer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gibraltarian born English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gibraltarian born English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, American poet, novelist, essayist", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet, novelist, essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet, novelist, essayist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American basketball player (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>et <PERSON>\"><PERSON><PERSON></a>, American basketball player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>et <PERSON>\"><PERSON><PERSON></a>, American basketball player (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chet_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, Dominican politician, 52nd President of the Dominican Republic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Hip%C3%B3lito_Mej%C3%ADa\" title=\"Hipólito Mejía\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican politician, 52nd <a href=\"https://wikipedia.org/wiki/President_of_the_Dominican_Republic\" title=\"President of the Dominican Republic\">President of the Dominican Republic</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hip%C3%B3lito_Mej%C3%ADa\" title=\"Hipólito Mejía\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican politician, 52nd <a href=\"https://wikipedia.org/wiki/President_of_the_Dominican_Republic\" title=\"President of the Dominican Republic\">President of the Dominican Republic</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hip%C3%B3lito_Mej%C3%ADa"}, {"title": "President of the Dominican Republic", "link": "https://wikipedia.org/wiki/President_of_the_Dominican_Republic"}]}, {"year": "1942", "text": "<PERSON>, English model and dancer (d. 2017)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and dancer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and dancer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English philosopher and critic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Polish-German economist and politician, 9th President of Germany (d. 2025)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6hler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German economist and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Germany\" title=\"President of Germany\">President of Germany</a> (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6hler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German economist and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Germany\" title=\"President of Germany\">President of Germany</a> (d. 2025)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Horst_K%C3%B6hler"}, {"title": "President of Germany", "link": "https://wikipedia.org/wiki/President_of_Germany"}]}, {"year": "1943", "text": "<PERSON>, American basketball player (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American basketball player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Japanese assassin of <PERSON><PERSON><PERSON> (d. 1960)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese assassin of <a href=\"https://wikipedia.org/wiki/Inejiro_Asanuma\" class=\"mw-redirect\" title=\"Inejiro Asanuma\"><PERSON><PERSON><PERSON></a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese assassin of <a href=\"https://wikipedia.org/wiki/Inejiro_Asanuma\" class=\"mw-redirect\" title=\"Inejiro Asanuma\"><PERSON><PERSON><PERSON></a> (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Inej<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American director, producer, and screenwriter (d. 2017)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English guitarist (d. 2010)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American lawyer and businessman (d. 2003)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English diplomat, British Ambassador to the United States (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to the United States\">British Ambassador to the United States</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to the United States\">British Ambassador to the United States</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Ambassadors of the United Kingdom to the United States", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States"}]}, {"year": "1944", "text": "<PERSON>, Dutch tennis player and painter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player and painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American pop singer (d. 2000)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American pop singer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American pop singer (d. 2000)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Danish footballer and manager (d. 2014)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Kresten_Bjerre\" title=\"<PERSON><PERSON><PERSON>jer<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Krest<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish footballer and manager (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Finnish director, cinematographer, and screenwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish director, cinematographer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish director, cinematographer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American drummer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English footballer and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1947", "text": "<PERSON>, Belgian philosopher and theorist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian philosopher and theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian philosopher and theorist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1948", "text": "<PERSON>, American basketball player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Scottish footballer and manager (d. 2022)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager (d. 2022)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Austrian racing driver (d. 2019)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian racing driver (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian racing driver (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_Lauda"}]}, {"year": "1949", "text": "<PERSON>, Russian tennis player and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American basketball player and sportscaster", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}]}, {"year": "1950", "text": "<PERSON>, Dutch singer-songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "1950", "text": "Genesis P-<PERSON><PERSON>, English singer-songwriter (d. 2020)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Genesis_P-Orridge\" title=\"Genesis P-Orridge\"><PERSON> P-Orridge</a>, English singer-songwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Genesis_P-Orridge\" title=\"Genesis P-Orridge\">Genesis P-Orridge</a>, English singer-songwriter (d. 2020)", "links": [{"title": "Genesis P-Orridge", "link": "https://wikipedia.org/wiki/Genesis_P-Orridge"}]}, {"year": "1950", "text": "<PERSON>, English actress and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer and actress", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American physician and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ist"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese Minister of Economy and Treasury and MP (d. 2020)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese <a href=\"https://wikipedia.org/wiki/Ministry_of_Economy_(Portugal)\" title=\"Ministry of Economy (Portugal)\">Minister of Economy and Treasury</a> and <a href=\"https://wikipedia.org/wiki/Assembly_of_the_Republic_(Portugal)\" title=\"Assembly of the Republic (Portugal)\">MP</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese <a href=\"https://wikipedia.org/wiki/Ministry_of_Economy_(Portugal)\" title=\"Ministry of Economy (Portugal)\">Minister of Economy and Treasury</a> and <a href=\"https://wikipedia.org/wiki/Assembly_of_the_Republic_(Portugal)\" title=\"Assembly of the Republic (Portugal)\">MP</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Economy (Portugal)", "link": "https://wikipedia.org/wiki/Ministry_of_Economy_(Portugal)"}, {"title": "Assembly of the Republic (Portugal)", "link": "https://wikipedia.org/wiki/Assembly_of_the_Republic_(Portugal)"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, Tuvaluan politician, 8th Prime Minister of Tuvalu (d. 2020)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Saufatu_Sopoanga\" title=\"Saufatu Sopoanga\"><PERSON><PERSON><PERSON><PERSON> Sopoanga</a>, Tuvaluan politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu\" title=\"Prime Minister of Tuvalu\">Prime Minister of Tuvalu</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saufatu_Sopoanga\" title=\"Saufatu Sopoanga\"><PERSON><PERSON><PERSON><PERSON> Sopoanga</a>, Tuvaluan politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu\" title=\"Prime Minister of Tuvalu\">Prime Minister of Tuvalu</a> (d. 2020)", "links": [{"title": "Saufatu Sopoanga", "link": "https://wikipedia.org/wiki/Saufatu_Sopoanga"}, {"title": "Prime Minister of Tuvalu", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu"}]}, {"year": "1953", "text": "<PERSON>, English actor and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American journalist and political adviser", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(political_consultant)\" class=\"mw-redirect\" title=\"<PERSON> (political consultant)\"><PERSON></a>, American journalist and political adviser", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(political_consultant)\" class=\"mw-redirect\" title=\"<PERSON> (political consultant)\"><PERSON></a>, American journalist and political adviser", "links": [{"title": "<PERSON> (political consultant)", "link": "https://wikipedia.org/wiki/<PERSON>_(political_consultant)"}]}, {"year": "1955", "text": "<PERSON>, Canadian ice hockey player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1957", "text": "<PERSON>, Dutch microbiologist and engineer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch microbiologist and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch microbiologist and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1958", "text": "<PERSON>, American bass player and songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Czech politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_%C4%8Cunek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_%C4%8Cunek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_%C4%8Cunek"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Australian sculptor (d. 2006)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Oliver\"><PERSON><PERSON><PERSON></a>, Australian sculptor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian sculptor (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Oliver"}]}, {"year": "1959", "text": "<PERSON>, American BMX racer (d. 2024)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American BMX racer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American BMX racer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, 2nd Baron <PERSON>, Scottish politician, Chancellor of the Duchy of Lancaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a>", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1961", "text": "<PERSON>, Japanese guitarist, songwriter, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian zoologist and television host (d. 2006)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian zoologist and television host (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian zoologist and television host (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Baron <PERSON>, English journalist and politician, Secretary of State for Transport", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a>", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>"}, {"title": "Secretary of State for Transport", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Transport"}]}, {"year": "1963", "text": "<PERSON>, Jamaican-English cricketer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English cricketer", "links": [{"title": "<PERSON> Malcolm", "link": "https://wikipedia.org/wiki/<PERSON>_Malcolm"}]}, {"year": "1963", "text": "<PERSON>, Fijian-American golfer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Fijian-American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Fijian-American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English singer-songwriter (d. 2015)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1964)\" title=\"<PERSON> (footballer, born 1964)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1964)\" title=\"<PERSON> (footballer, born 1964)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1964)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1964)"}]}, {"year": "1965", "text": "<PERSON>, American basketball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Irish jockey", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish jockey", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American ice hockey player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actress and comedian", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor, screenwriter, director, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, screenwriter, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, screenwriter, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican wrestler", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Psicosis_II\" title=\"Psicosis II\">Psicosis II</a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Psicosis_II\" title=\"Psicosis II\">Psicosis II</a>, Mexican wrestler", "links": [{"title": "Psicosis II", "link": "https://wikipedia.org/wiki/Psicosis_II"}]}, {"year": "1968", "text": "<PERSON>, Canadian politician, 31st Premier of New Brunswick", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New Brunswick", "link": "https://wikipedia.org/wiki/Premier_of_New_Brunswick"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American model and actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American basketball player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Danish footballer and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Belgian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Filipino actress and singer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ga\" title=\"<PERSON> Salonga\"><PERSON></a>, Filipino actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ga\" title=\"<PERSON> Salonga\"><PERSON></a>, Filipino actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lea_Salonga"}]}, {"year": "1972", "text": "<PERSON>, American tennis player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, German speed skater", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Haim_Revivo\" title=\"Haim Revivo\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hai<PERSON>_Revivo\" title=\"Haim Revivo\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "Haim Revivo", "link": "https://wikipedia.org/wiki/Haim_Revivo"}]}, {"year": "1972", "text": "<PERSON>, American politician and college administrator", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and college administrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and college administrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, French cyclist (d. 2013)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ista\" title=\"<PERSON>inho Paulista\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>inho Paulista\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Paulista"}]}, {"year": "1973", "text": "<PERSON>, American musician and songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1974", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English radio and television host", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress, director, producer, and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Swiss footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>kin"}]}, {"year": "1979", "text": "<PERSON>, Australian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, South Korean actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, German singer-songwriter and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Irish rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Serbian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Algerian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Printezis\" title=\"<PERSON><PERSON> Printezis\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Printezis\" title=\"<PERSON><PERSON> Printezis\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_Printezis"}]}, {"year": "1985", "text": "<PERSON>, American actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, South Korean actress and model", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-joo\" title=\"<PERSON> Hyo-joo\"><PERSON></a>, South Korean actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-joo\" title=\"<PERSON> Hyo-joo\"><PERSON>oo</a>, South Korean actress and model", "links": [{"title": "<PERSON>oo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-joo"}]}, {"year": "1987", "text": "<PERSON>, Argentine footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Belgian sprinter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, Belgian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, Belgian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A9e"}]}, {"year": "1989", "text": "<PERSON>, Argentine footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Franco_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_V%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Venezuelan baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>hado\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South Korean model and actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyuk\" title=\"<PERSON>hyuk\"><PERSON></a>, South Korean model and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyu<PERSON>\" title=\"<PERSON>hyuk\"><PERSON>yu<PERSON></a>, South Korean model and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yuk"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(basketball)"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>%27_Graham"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Canadian basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "556", "text": "<PERSON><PERSON><PERSON>, Bishop of Ravenna (b. 499)", "html": "556 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Ravenna\" title=\"<PERSON><PERSON><PERSON> of Ravenna\"><PERSON><PERSON><PERSON></a>, Bishop of Ravenna (b. 499)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Ravenna\" title=\"<PERSON><PERSON><PERSON> of Ravenna\"><PERSON><PERSON><PERSON></a>, Bishop of Ravenna (b. 499)", "links": [{"title": "<PERSON><PERSON><PERSON> of Ravenna", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Ravenna"}]}, {"year": "606", "text": "<PERSON><PERSON><PERSON>, Pope of the Catholic Church", "html": "606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON><PERSON><PERSON></a>, Pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"Pope <PERSON>\"><PERSON><PERSON><PERSON></a>, Pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "793", "text": "<PERSON><PERSON><PERSON>, Anglo-Saxon nobleman and regicide", "html": "793 - <a href=\"https://wikipedia.org/wiki/Sicga\" title=\"<PERSON>c<PERSON>\"><PERSON><PERSON><PERSON></a>, Anglo-Saxon nobleman and regicide", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sicga\" title=\"<PERSON>c<PERSON>\"><PERSON><PERSON><PERSON></a>, Anglo-Saxon nobleman and regicide", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sicga"}]}, {"year": "845", "text": "<PERSON>, Chinese Empress dowager", "html": "845 - <a href=\"https://wikipedia.org/wiki/Empress_Dow<PERSON>_<PERSON>_(Jingzong)\" title=\"Empress <PERSON><PERSON> (Jingzong)\"><PERSON></a>, Chinese Empress dowager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_Dow<PERSON>_<PERSON>_(Jingzong)\" title=\"Empress <PERSON><PERSON> (Jingzong)\"><PERSON></a>, Chinese Empress dowager", "links": [{"title": "Empress <PERSON><PERSON> (Jingzong)", "link": "https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>_(Jingzong)"}]}, {"year": "954", "text": "<PERSON>, Chinese Emperor (b. 904)", "html": "954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese Emperor (b. 904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese Emperor (b. 904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "965", "text": "<PERSON>, Duke of Burgundy (b. 944)", "html": "965 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON></a>, Duke of Burgundy (b. 944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON></a>, Duke of Burgundy (b. 944)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Burgundy"}]}, {"year": "970", "text": "<PERSON>, King of Pamplona", "html": "970 - <a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_S%C3%A1nchez_I_of_Pamplona\" title=\"<PERSON> of Pamplona\"><PERSON></a>, King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Pamplona\" class=\"mw-redirect\" title=\"Kingdom of Pamplona\">Pamplona</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_S%C3%A1nchez_I_of_Pamplona\" title=\"<PERSON> of Pamplona\"><PERSON></a>, King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Pamplona\" class=\"mw-redirect\" title=\"Kingdom of Pamplona\">Pamplona</a>", "links": [{"title": "<PERSON> of Pamplona", "link": "https://wikipedia.org/wiki/Garc%C3%ADa_S%C3%A1nchez_I_of_Pamplona"}, {"title": "Kingdom of Pamplona", "link": "https://wikipedia.org/wiki/Kingdom_of_Pamplona"}]}, {"year": "978", "text": "<PERSON>, Count of Chalon (b. 930)", "html": "978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Chalon\" title=\"<PERSON> of Chalon\"><PERSON></a>, Count of Chalon (b. 930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Chalon\" title=\"<PERSON> of Chalon\"><PERSON></a>, Count of Chalon (b. 930)", "links": [{"title": "<PERSON> of Chalon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Chalon"}]}, {"year": "1071", "text": "<PERSON><PERSON><PERSON>, Count of Flanders", "html": "1071 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III,_Count_of_Flanders\" title=\"<PERSON><PERSON><PERSON> III, Count of Flanders\"><PERSON><PERSON><PERSON></a>, Count of Flanders", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III,_Count_of_Flanders\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Flanders\"><PERSON><PERSON><PERSON></a>, Count of Flanders", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Flanders"}]}, {"year": "1072", "text": "<PERSON>, Italian cardinal", "html": "1072 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1079", "text": "<PERSON> of Fécamp, Italian Benedictine abbot", "html": "1079 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_F%C3%A9camp\" title=\"<PERSON> of Fécamp\"><PERSON> of Fécamp</a>, Italian Benedictine abbot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9camp\" title=\"<PERSON> of Fécamp\"><PERSON> of Fécamp</a>, Italian Benedictine abbot", "links": [{"title": "<PERSON> of Fécamp", "link": "https://wikipedia.org/wiki/John_of_F%C3%A9camp"}]}, {"year": "1111", "text": "<PERSON>, King of Sicily (b. 1078)", "html": "1111 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, King of Sicily (b. 1078)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, King of Sicily (b. 1078)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1297", "text": "<PERSON> of Cortona, Italian penitent (b. 1247)", "html": "1297 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cortona\" title=\"<PERSON> of Cortona\"><PERSON> of Cortona</a>, Italian penitent (b. 1247)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Cortona\"><PERSON> of Cortona</a>, Italian penitent (b. 1247)", "links": [{"title": "<PERSON> of Cortona", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1371", "text": "<PERSON>, King of Scotland (b. 1324)", "html": "1371 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON></a>, King of Scotland (b. 1324)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> II</a>, King of Scotland (b. 1324)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Scotland"}]}, {"year": "1452", "text": "<PERSON>, 8th Earl of Douglas (b. 1425)", "html": "1452 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Earl_<PERSON>_Douglas\" title=\"<PERSON>, 8th Earl of Douglas\"><PERSON>, 8th Earl of Douglas</a> (b. 1425)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Earl_<PERSON>_Douglas\" title=\"<PERSON>, 8th Earl of Douglas\"><PERSON>, 8th Earl of Douglas</a> (b. 1425)", "links": [{"title": "<PERSON>, 8th Earl of Douglas", "link": "https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_<PERSON>"}]}, {"year": "1500", "text": "<PERSON>, German nobleman (b. 1430)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Oldenburg\" title=\"<PERSON>, Count of Oldenburg\"><PERSON></a>, German nobleman (b. 1430)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Oldenburg\" title=\"<PERSON>, Count of Oldenburg\"><PERSON></a>, German nobleman (b. 1430)", "links": [{"title": "<PERSON>, Count of Oldenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Oldenburg"}]}, {"year": "1511", "text": "<PERSON>, Duke of Cornwall (b. 1511)", "html": "1511 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Cornwall\" title=\"<PERSON>, Duke of Cornwall\"><PERSON></a>, Duke of Cornwall (b. 1511)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Cornwall\" title=\"<PERSON>, Duke of Cornwall\"><PERSON></a>, Duke of Cornwall (b. 1511)", "links": [{"title": "<PERSON>, Duke of Cornwall", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Cornwall"}]}, {"year": "1512", "text": "<PERSON><PERSON><PERSON>, Italian cartographer and explorer (b. 1454)", "html": "1512 - <a href=\"https://wikipedia.org/wiki/Ameri<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cartographer and explorer (b. 1454)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ameri<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cartographer and explorer (b. 1454)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amerigo_<PERSON>"}]}, {"year": "1627", "text": "<PERSON>, Dutch explorer (b. 1558)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch explorer (b. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch explorer (b. 1558)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1674", "text": "<PERSON>, French poet and critic (b. 1595)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (b. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (b. 1595)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON>, French occultist (b. 1640)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Voisin\" title=\"La Voisin\"><PERSON></a>, French occultist (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Voisin\" title=\"La Voisin\"><PERSON></a>, French occultist (b. 1640)", "links": [{"title": "La Voisin", "link": "https://wikipedia.org/wiki/<PERSON>_Voisin"}]}, {"year": "1690", "text": "<PERSON>, French painter and theorist (b. 1619)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and theorist (b. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and theorist (b. 1619)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1731", "text": "<PERSON><PERSON><PERSON>, Dutch physician and anatomist (b. 1638)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physician and anatomist (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physician and anatomist (b. 1638)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1732", "text": "<PERSON>, English bishop (b. 1663)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1663)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, first American killed in the American Revolution (b. 1758)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first American killed in the <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a> (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first American killed in the <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a> (b. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, Chinese politician (b. 1750)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>she<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese politician (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>she<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese politician (b. 1750)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Heshen"}]}, {"year": "1816", "text": "<PERSON>, Scottish historian and philosopher (b. 1723)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and philosopher (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and philosopher (b. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French painter and illustrator (b. 1796)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French painter and illustrator (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French painter and illustrator (b. 1796)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Scottish geologist (b. 1797)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish geologist (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish geologist (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, English physician and activist (b. 1846)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and activist (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and activist (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American businessman and philanthropist (b. 1822)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Danish painter and academic (b. 1834)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and academic (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and academic (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, French tightrope walker and acrobat (b. 1824)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tightrope walker and acrobat (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tightrope walker and acrobat (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON><PERSON>, Korean king (b. 1820)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/He<PERSON>seon_Daewongun\" title=\"Heung<PERSON><PERSON>ewong<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Korean king (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON>seon_Daewongun\" title=\"Heung<PERSON><PERSON> Daewong<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Korean king (b. 1820)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>un"}]}, {"year": "1903", "text": "<PERSON>, Austrian composer (b. 1860)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wolf\"><PERSON></a>, Austrian composer (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English historian, author, and critic (b. 1832)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and critic (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and critic (b. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Swiss linguist and author (b. 1857)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss linguist and author (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss linguist and author (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Mexican president and author (b. 1873)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a>, Mexican president and author (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a>, Mexican president and author (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French politician, French Minister of Foreign Affairs (b. 1852)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9ophile_Delcass%C3%A9\" title=\"Théophile <PERSON>\">Thé<PERSON><PERSON><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9ophile_Delcass%C3%A9\" title=\"Théophile <PERSON>\">Thé<PERSON><PERSON><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a> (b. 1852)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9ophile_Delcass%C3%A9"}, {"title": "Minister of Foreign Affairs (France)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)"}]}, {"year": "1939", "text": "<PERSON>, Spanish-French poet and author (b. 1875)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-French poet and author (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-French poet and author (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Austrian journalist, author, and playwright (b. 1881)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist, author, and playwright (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist, author, and playwright (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, German activist (b. 1919)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, German activist (b. 1918)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, German activist (b. 1921)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian activist (b. 1869)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Indian activist (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Indian activist (b. 1869)", "links": [{"title": "Kast<PERSON><PERSON> Gandhi", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Gandhi"}]}, {"year": "1944", "text": "<PERSON>, anti-Nazi German who joined Soviet partisans (b. 1916)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, anti-Nazi German who joined Soviet partisans (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, anti-Nazi German who joined Soviet partisans (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Russian avant garde writer and literary critic (b. 1888)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian avant garde writer and literary critic (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian avant garde writer and literary critic (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Indian scholar and politician, Indian Minister of Education (b. 1888)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>zad\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Azad\"><PERSON><PERSON></a>, Indian scholar and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Human_Resource_Development_(India)\" class=\"mw-redirect\" title=\"Ministry of Human Resource Development (India)\">Indian Minister of Education</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Azad\"><PERSON><PERSON></a>, Indian scholar and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Human_Resource_Development_(India)\" class=\"mw-redirect\" title=\"Ministry of Human Resource Development (India)\">Indian Minister of Education</a> (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Ministry of Human Resource Development (India)", "link": "https://wikipedia.org/wiki/Ministry_of_Human_Resource_Development_(India)"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian-French painter and critic (b. 1905)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89mile_Bo<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian-French painter and critic (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian-French painter and critic (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-%C3%89mile_Bord<PERSON>s"}]}, {"year": "1961", "text": "<PERSON>, American trumpet player and composer (b. 1889)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Austrian-American lawyer and jurist (b. 1882)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American lawyer and jurist (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American lawyer and jurist (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French actor (b. 1883)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French actor (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French actor (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 21st Premier of Quebec (b. 1916)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/List_of_Quebec_premiers\" class=\"mw-redirect\" title=\"List of Quebec premiers\">Premier of Quebec</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/List_of_Quebec_premiers\" class=\"mw-redirect\" title=\"List of Quebec premiers\">Premier of Quebec</a> (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "List of Quebec premiers", "link": "https://wikipedia.org/wiki/List_of_Quebec_premiers"}]}, {"year": "1973", "text": "<PERSON>, Anglo-Irish author (b. 1899)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish author (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish author (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Greek actress (b. 1900)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actress (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actress (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American colonel and politician, 37th Governor of Arkansas (b. 1912)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American colonel and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American colonel and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1912)", "links": [{"title": "<PERSON><PERSON> Rockefeller", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Rockefeller"}, {"title": "Governor of Arkansas", "link": "https://wikipedia.org/wiki/Governor_of_Arkansas"}]}, {"year": "1976", "text": "<PERSON>, English actress (b. 1904)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer (b. 1943)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ballard\" title=\"<PERSON> Ballard\"><PERSON></a>, American singer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ballard\" title=\"<PERSON> Ballard\"><PERSON></a>, American singer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Ballard"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Austrian painter, poet and playwright (b. 1886)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian painter, poet and playwright (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian painter, poet and playwright (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Indian-Pakistani poet and author (b. 1898)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Pakistani poet and author (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Pakistani poet and author (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English conductor (b. 1889)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Belgian cyclist (b. 1913)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Spanish author, poet, and playwright (b. 1913)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Salvador_Espriu\" title=\"Salvador Espriu\"><PERSON></a>, Spanish author, poet, and playwright (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Espriu\" title=\"Salvador Espriu\"><PERSON></a>, Spanish author, poet, and playwright (b. 1913)", "links": [{"title": "Salvador Espriu", "link": "https://wikipedia.org/wiki/Salvador_Espriu"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Russian violinist, composer, and conductor (b. 1889)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Zimbalist\" title=\"<PERSON><PERSON><PERSON> Zimbalist\"><PERSON><PERSON><PERSON>bal<PERSON></a>, Russian violinist, composer, and conductor (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Zimbalist\" title=\"<PERSON><PERSON><PERSON> Zimbalist\"><PERSON><PERSON><PERSON>imbalist</a>, Russian violinist, composer, and conductor (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ef<PERSON>_Zimbalist"}]}, {"year": "1986", "text": "<PERSON>, Australian rugby league player (b. 1955)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (b. 1955)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1987", "text": "<PERSON>, American talk show host and producer (b. 1920)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and producer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and producer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American painter and photographer (b. 1928)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and photographer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and photographer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Greek general and politician (b. 1906)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mark<PERSON>_Vafiadis"}]}, {"year": "1994", "text": "<PERSON>, American violinist (b. 1917)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Papa <PERSON>\"><PERSON></a>, American violinist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Papa <PERSON>\"><PERSON></a>, American violinist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American actor (b. 1934)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Ed_<PERSON>\" title=\"Ed <PERSON>\"><PERSON></a>, American actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ed_<PERSON>\" title=\"Ed <PERSON>\"><PERSON></a>, American actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ed_Flanders"}]}, {"year": "1997", "text": "<PERSON>, American gangster (b. 1907)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American gangster (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American gangster (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American lawyer and politician, 4th United States Secretary of Health and Human Services (b. 1910)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of Health and Human Services", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services"}]}, {"year": "1999", "text": "<PERSON>, American poet and academic (b. 1918)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Dutch tennis player (b. 1964)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> O<PERSON>\"><PERSON><PERSON></a>, Dutch tennis player (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> O<PERSON>\"><PERSON><PERSON></a>, Dutch tennis player (b. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>no_Oosting"}]}, {"year": "2002", "text": "<PERSON>, American animator, producer, and screenwriter (b. 1912)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Angolan general, founded UNITA (b. 1934)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Angolan general, founded <a href=\"https://wikipedia.org/wiki/UNITA\" title=\"UNITA\">UNITA</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Angolan general, founded <a href=\"https://wikipedia.org/wiki/UNITA\" title=\"UNITA\">UNITA</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "UNITA", "link": "https://wikipedia.org/wiki/UNITA"}]}, {"year": "2004", "text": "<PERSON>, American baseball player, coach, and manager (b. 1920)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, South Korean actress and singer (b. 1980)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ju\" title=\"<PERSON>j<PERSON>\"><PERSON></a>, South Korean actress and singer (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>u\" title=\"<PERSON>-j<PERSON>\"><PERSON></a>, South Korean actress and singer (b. 1980)", "links": [{"title": "<PERSON>u", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ju"}]}, {"year": "2005", "text": "<PERSON>, French actress (b. 1910)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Singaporean politician, 1st Senior Minister of Singapore (b. 1915)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/S._Rajaratnam\" title=\"S. Rajaratnam\"><PERSON><PERSON></a>, Singaporean politician, 1st <a href=\"https://wikipedia.org/wiki/Senior_Minister_of_Singapore\" title=\"Senior Minister of Singapore\">Senior Minister of Singapore</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._Rajaratnam\" title=\"S. Rajaratnam\"><PERSON><PERSON></a>, Singaporean politician, 1st <a href=\"https://wikipedia.org/wiki/Senior_Minister_of_Singapore\" title=\"Senior Minister of Singapore\">Senior Minister of Singapore</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._<PERSON>nam"}, {"title": "Senior Minister of Singapore", "link": "https://wikipedia.org/wiki/Senior_Minister_of_Singapore"}]}, {"year": "2007", "text": "<PERSON>, 2nd Earl <PERSON>, English politician, Leader of the House of Lords (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_House_of_Lords\" title=\"Leader of the House of Lords\">Leader of the House of Lords</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_House_of_Lords\" title=\"Leader of the House of Lords\">Leader of the House of Lords</a> (b. 1918)", "links": [{"title": "<PERSON>, 2nd <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>"}, {"title": "Leader of the House of Lords", "link": "https://wikipedia.org/wiki/Leader_of_the_House_of_Lords"}]}, {"year": "2007", "text": "<PERSON>, American basketball player and coach (b. 1954)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Indian author and poet (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON></a>, Indian author and poet (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON></a>, Indian author and poet (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(writer)"}]}, {"year": "2012", "text": "<PERSON>, Irish-English comedian and actor (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English comedian and actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English comedian and actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American journalist (b. 1956)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, French photographer and journalist (b. 1983)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/R%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French photographer and journalist (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French photographer and journalist (b. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9mi_O<PERSON>lik"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Dutch speed skater (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/At<PERSON>_<PERSON>-<PERSON>\" title=\"At<PERSON>-<PERSON>\"><PERSON><PERSON></a>, Dutch speed skater (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/At<PERSON>_<PERSON>-<PERSON>\" title=\"Atje <PERSON>n-Deelstra\"><PERSON><PERSON></a>, Dutch speed skater (b. 1938)", "links": [{"title": "Atje <PERSON>-Deelstra", "link": "https://wikipedia.org/wiki/Atje_Keulen-Dee<PERSON>tra"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, French-Swiss scholar and translator (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Swiss scholar and translator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Swiss scholar and translator (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2013", "text": "<PERSON>, German pianist and conductor (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and conductor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and conductor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, New Zealand-Australian television host (b. 1966)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian television host (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian television host (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, American pianist and composer (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Tre<PERSON>_<PERSON>_<PERSON>\" title=\"Trebor <PERSON>\">Tre<PERSON> <PERSON></a>, American pianist and composer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tre<PERSON>_<PERSON>_<PERSON>\" title=\"Tre<PERSON>\">Tre<PERSON> <PERSON></a>, American pianist and composer (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Dutch-American hematologist, poet, and illustrator (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American hematologist, poet, and illustrator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American hematologist, poet, and illustrator (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_Vroman"}]}, {"year": "2015", "text": "<PERSON>, Scottish singer-songwriter and producer (b. 1946)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and producer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rainbow\"><PERSON></a>, Scottish singer-songwriter and producer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, American model and singer, Miss America 1951 (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American model and singer, <a href=\"https://wikipedia.org/wiki/Miss_America\" title=\"Miss America\">Miss America 1951</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American model and singer, <a href=\"https://wikipedia.org/wiki/Miss_America\" title=\"Miss America\">Miss America 1951</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Miss America", "link": "https://wikipedia.org/wiki/Miss_America"}]}, {"year": "2016", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Spanish cartoonist (b. 1942)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a>, Spanish cartoonist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a>, Spanish cartoonist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American comedian and actor (b. 1970)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American actor (b. 1925)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American poet, painter (b. 1919)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, painter (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, painter (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, English musician, pianist for The Quarrymen (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician, pianist for <a href=\"https://wikipedia.org/wiki/The_Quarrymen\" title=\"The Quarrymen\">The Quarrymen</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician, pianist for <a href=\"https://wikipedia.org/wiki/The_Quarrymen\" title=\"The Quarrymen\">The Quarrymen</a> (b. 1942)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}, {"title": "The Quarrymen", "link": "https://wikipedia.org/wiki/The_Quarrymen"}]}]}}