{"date": "June 16", "url": "https://wikipedia.org/wiki/June_16", "data": {"Events": [{"year": "632", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> III ascends the throne as king (shah) of the Persian Empire. He becomes the last ruler of the Sasanian dynasty (modern Iran).", "html": "632 - <a href=\"https://wikipedia.org/wiki/Yazdegerd_III\" title=\"Yazdegerd III\">Yazdegerd III</a> ascends the throne as king (<i><a href=\"https://wikipedia.org/wiki/Shah\" title=\"Shah\">shah</a></i>) of the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Persian Empire</a>. He becomes the last ruler of the <a href=\"https://wikipedia.org/wiki/Sasanian_family_tree\" title=\"Sasanian family tree\">Sasanian dynasty</a> (modern <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yazdegerd_III\" title=\"Yazdegerd III\">Yaz<PERSON>gerd III</a> ascends the throne as king (<i><a href=\"https://wikipedia.org/wiki/Shah\" title=\"Shah\">shah</a></i>) of the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Persian Empire</a>. He becomes the last ruler of the <a href=\"https://wikipedia.org/wiki/Sasanian_family_tree\" title=\"Sasanian family tree\">Sasanian dynasty</a> (modern <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>).", "links": [{"title": "Yazdegerd III", "link": "https://wikipedia.org/wiki/Yazdegerd_III"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Shah"}, {"title": "Sasanian Empire", "link": "https://wikipedia.org/wiki/Sasanian_Empire"}, {"title": "Sasanian family tree", "link": "https://wikipedia.org/wiki/Sasanian_family_tree"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}]}, {"year": "1407", "text": "Ming-Hồ War: Retired King <PERSON><PERSON> and his son King <PERSON><PERSON> of Hồ dynasty are captured by the Ming armies.", "html": "1407 - <a href=\"https://wikipedia.org/wiki/Ming%E2%80%93H%E1%BB%93_War\" class=\"mw-redirect\" title=\"Ming-Hồ War\">Ming-Hồ War</a>: Retired King <a href=\"https://wikipedia.org/wiki/H%E1%BB%93_Qu%C3%BD_Ly\" title=\"Hồ Quý Ly\"><PERSON><PERSON> Ly</a> and his son King <a href=\"https://wikipedia.org/wiki/H%E1%BB%93_H%C3%A1n_Th%C6%B0%C6%A1ng\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/H%E1%BB%93_dynasty\" title=\"Hồ dynasty\">Hồ dynasty</a> are captured by the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming</a> armies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ming%E2%80%93H%E1%BB%93_War\" class=\"mw-redirect\" title=\"Ming-Hồ War\">Ming-Hồ War</a>: Retired King <a href=\"https://wikipedia.org/wiki/H%E1%BB%93_Qu%C3%BD_Ly\" title=\"Hồ Quý Ly\"><PERSON><PERSON>uý Ly</a> and his son King <a href=\"https://wikipedia.org/wiki/H%E1%BB%93_H%C3%A1n_Th%C6%B0%C6%A1ng\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/H%E1%BB%93_dynasty\" title=\"Hồ dynasty\">Hồ dynasty</a> are captured by the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming</a> armies.", "links": [{"title": "Ming-Hồ War", "link": "https://wikipedia.org/wiki/Ming%E2%80%93H%E1%BB%93_War"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%E1%BB%93_Qu%C3%BD_Ly"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%E1%BB%93_H%C3%A1n_Th%C6%B0%C6%A1ng"}, {"title": "Hồ dynasty", "link": "https://wikipedia.org/wiki/H%E1%BB%93_dynasty"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}]}, {"year": "1487", "text": "Battle of Stoke Field: King <PERSON> of England defeats the leaders of a Yorkist rebellion in the final engagement of the Wars of the Roses.", "html": "1487 - <a href=\"https://wikipedia.org/wiki/Battle_of_Stoke_Field\" title=\"Battle of Stoke Field\">Battle of Stoke Field</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> VII of England\"><PERSON> of England</a> defeats the leaders of a Yorkist rebellion in the final engagement of the <a href=\"https://wikipedia.org/wiki/Wars_of_the_Roses\" title=\"Wars of the Roses\">Wars of the Roses</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Stoke_Field\" title=\"Battle of Stoke Field\">Battle of Stoke Field</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> VII of England\"><PERSON> of England</a> defeats the leaders of a Yorkist rebellion in the final engagement of the <a href=\"https://wikipedia.org/wiki/Wars_of_the_Roses\" title=\"Wars of the Roses\">Wars of the Roses</a>.", "links": [{"title": "Battle of Stoke Field", "link": "https://wikipedia.org/wiki/Battle_of_Stoke_Field"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Wars of the Roses", "link": "https://wikipedia.org/wiki/Wars_of_the_Roses"}]}, {"year": "1632", "text": "The Plymouth Company granted a land patent to <PERSON>, the first settler of Pejepscot, Maine, settling at the site of Fort Andross.", "html": "1632 - The <a href=\"https://wikipedia.org/wiki/Plymouth_Company\" title=\"Plymouth Company\">Plymouth Company</a> granted a land patent to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Purchase\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Settler\" title=\"Settler\">settler</a> of <a href=\"https://wikipedia.org/wiki/Pejepscot,_Maine\" class=\"mw-redirect\" title=\"Pejepscot, Maine\">Pejepscot, Maine</a>, settling at the site of <a href=\"https://wikipedia.org/wiki/Fort_Andross\" title=\"Fort Andross\">Fort Andross</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Plymouth_Company\" title=\"Plymouth Company\">Plymouth Company</a> granted a land patent to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Purchase\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Settler\" title=\"Settler\">settler</a> of <a href=\"https://wikipedia.org/wiki/Pejepscot,_Maine\" class=\"mw-redirect\" title=\"Pejepscot, Maine\">Pejepscot, Maine</a>, settling at the site of <a href=\"https://wikipedia.org/wiki/Fort_Andross\" title=\"Fort Andross\">Fort Andross</a>.", "links": [{"title": "Plymouth Company", "link": "https://wikipedia.org/wiki/Plymouth_Company"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Pejepscot, Maine", "link": "https://wikipedia.org/wiki/Pejepscot,_Maine"}, {"title": "Fort Andross", "link": "https://wikipedia.org/wiki/Fort_Andross"}]}, {"year": "1745", "text": "War of the Austrian Succession: New England colonial troops under the command of <PERSON> capture the Fortress of Louisbourg in Louisbourg, New France (Old Style date).", "html": "1745 - <a href=\"https://wikipedia.org/wiki/War_of_the_Austrian_Succession\" title=\"War of the Austrian Succession\">War of the Austrian Succession</a>: <a href=\"https://wikipedia.org/wiki/New_England\" title=\"New England\">New England</a> colonial troops under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> capture the <a href=\"https://wikipedia.org/wiki/Fortress_of_Louisbourg\" title=\"Fortress of Louisbourg\">Fortress of Louisbourg</a> in <a href=\"https://wikipedia.org/wiki/Louisbourg\" title=\"Louisbourg\">Louisbourg</a>, <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a> (<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">Old Style</a> date).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Austrian_Succession\" title=\"War of the Austrian Succession\">War of the Austrian Succession</a>: <a href=\"https://wikipedia.org/wiki/New_England\" title=\"New England\">New England</a> colonial troops under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> capture the <a href=\"https://wikipedia.org/wiki/Fortress_of_Louisbourg\" title=\"Fortress of Louisbourg\">Fortress of Louisbourg</a> in <a href=\"https://wikipedia.org/wiki/Louisbourg\" title=\"Louisbourg\">Louisbourg</a>, <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a> (<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">Old Style</a> date).", "links": [{"title": "War of the Austrian Succession", "link": "https://wikipedia.org/wiki/War_of_the_Austrian_Succession"}, {"title": "New England", "link": "https://wikipedia.org/wiki/New_England"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fortress of Louisbourg", "link": "https://wikipedia.org/wiki/Fortress_of_Louisbourg"}, {"title": "Louisbourg", "link": "https://wikipedia.org/wiki/Louisbourg"}, {"title": "New France", "link": "https://wikipedia.org/wiki/New_France"}, {"title": "Old Style", "link": "https://wikipedia.org/wiki/Old_Style"}]}, {"year": "1746", "text": "War of the Austrian Succession: Austria and Sardinia defeat a Franco-Spanish army at the Battle of Piacenza.", "html": "1746 - War of the Austrian Succession: Austria and <a href=\"https://wikipedia.org/wiki/Sardinia\" title=\"Sardinia\">Sardinia</a> defeat a Franco-Spanish army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Piacenza\" title=\"Battle of Piacenza\">Battle of Piacenza</a>.", "no_year_html": "War of the Austrian Succession: Austria and <a href=\"https://wikipedia.org/wiki/Sardinia\" title=\"Sardinia\">Sardinia</a> defeat a Franco-Spanish army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Piacenza\" title=\"Battle of Piacenza\">Battle of Piacenza</a>.", "links": [{"title": "Sardinia", "link": "https://wikipedia.org/wiki/Sardinia"}, {"title": "Battle of Piacenza", "link": "https://wikipedia.org/wiki/Battle_of_Piacenza"}]}, {"year": "1755", "text": "French and Indian War: The French surrender Fort Beauséjour to the British, leading to the expulsion of the Acadians.", "html": "1755 - <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: The <a href=\"https://wikipedia.org/wiki/Military_history_of_France\" title=\"Military history of France\">French</a> surrender <a href=\"https://wikipedia.org/wiki/Fort_Beaus%C3%A9jour\" title=\"Fort Beauséjour\">Fort Beauséjour</a> to the British, leading to the <a href=\"https://wikipedia.org/wiki/Expulsion_of_the_Acadians\" title=\"Expulsion of the Acadians\">expulsion</a> of the <a href=\"https://wikipedia.org/wiki/Acadians\" title=\"Acadians\">Acadians</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: The <a href=\"https://wikipedia.org/wiki/Military_history_of_France\" title=\"Military history of France\">French</a> surrender <a href=\"https://wikipedia.org/wiki/Fort_Beaus%C3%A9jour\" title=\"Fort Beauséjour\">Fort Beauséjour</a> to the British, leading to the <a href=\"https://wikipedia.org/wiki/Expulsion_of_the_Acadians\" title=\"Expulsion of the Acadians\">expulsion</a> of the <a href=\"https://wikipedia.org/wiki/Acadians\" title=\"Acadians\">Acadians</a>.", "links": [{"title": "French and Indian War", "link": "https://wikipedia.org/wiki/French_and_Indian_War"}, {"title": "Military history of France", "link": "https://wikipedia.org/wiki/Military_history_of_France"}, {"title": "Fort Beauséjour", "link": "https://wikipedia.org/wiki/Fort_Beaus%C3%A9jour"}, {"title": "Expulsion of the Acadians", "link": "https://wikipedia.org/wiki/Expulsion_of_the_Acadians"}, {"title": "Acadians", "link": "https://wikipedia.org/wiki/Acadians"}]}, {"year": "1760", "text": "French and Indian War: <PERSON> and his Rangers surprise French held Fort Sainte Thérèse on the Richelieu River near Lake Champlain. The fort is raided and burned.", "html": "1760 - French and Indian War: <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a> and his <a href=\"https://wikipedia.org/wiki/Rogers%27_Rangers\" title=\"Rogers' Rangers\">Rangers</a> surprise French held <a href=\"https://wikipedia.org/wiki/Fort_Sainte_Th%C3%A9r%C3%A8se\" title=\"Fort Sainte Thérèse\">Fort Sainte Thérèse</a> on the <a href=\"https://wikipedia.org/wiki/Richelieu_River\" title=\"Richelieu River\">Richelieu River</a> near <a href=\"https://wikipedia.org/wiki/Lake_Champlain\" title=\"Lake Champlain\">Lake Champlain</a>. The fort <a href=\"https://wikipedia.org/wiki/Sainte-Th%C3%A9r%C3%A8se_Raid\" title=\"Sainte-Thérèse Raid\">is raided and burned</a>.", "no_year_html": "French and Indian War: <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a> and his <a href=\"https://wikipedia.org/wiki/Rogers%27_Rangers\" title=\"Rogers' Rangers\">Rangers</a> surprise French held <a href=\"https://wikipedia.org/wiki/Fort_Sainte_Th%C3%A9r%C3%A8se\" title=\"Fort Sainte Thérèse\">Fort Sainte Thérèse</a> on the <a href=\"https://wikipedia.org/wiki/Richelieu_River\" title=\"Richelieu River\">Richelieu River</a> near <a href=\"https://wikipedia.org/wiki/Lake_Champlain\" title=\"Lake Champlain\">Lake Champlain</a>. The fort <a href=\"https://wikipedia.org/wiki/Sainte-Th%C3%A9r%C3%A8se_Raid\" title=\"Sainte-Thérèse Raid\">is raided and burned</a>.", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)"}, {"title": "Rogers' Rangers", "link": "https://wikipedia.org/wiki/Rogers%27_Rangers"}, {"title": "Fort Sainte Thérèse", "link": "https://wikipedia.org/wiki/Fort_Sainte_Th%C3%A9r%C3%A8se"}, {"title": "Richelieu River", "link": "https://wikipedia.org/wiki/Richelieu_River"}, {"title": "Lake Champlain", "link": "https://wikipedia.org/wiki/Lake_Champlain"}, {"title": "Sainte-Thérèse Raid", "link": "https://wikipedia.org/wiki/Sainte-Th%C3%A9r%C3%A8se_Raid"}]}, {"year": "1779", "text": "American Revolutionary War: Spain declares war on the Kingdom of Great Britain, and the Great Siege of Gibraltar begins.", "html": "1779 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: Spain <a href=\"https://wikipedia.org/wiki/Spain_and_the_American_Revolutionary_War#Declaration_of_war\" title=\"Spain and the American Revolutionary War\">declares war</a> on the Kingdom of Great Britain, and the <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Gibraltar\" title=\"Great Siege of Gibraltar\">Great Siege of Gibraltar</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: Spain <a href=\"https://wikipedia.org/wiki/Spain_and_the_American_Revolutionary_War#Declaration_of_war\" title=\"Spain and the American Revolutionary War\">declares war</a> on the Kingdom of Great Britain, and the <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Gibraltar\" title=\"Great Siege of Gibraltar\">Great Siege of Gibraltar</a> begins.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Spain and the American Revolutionary War", "link": "https://wikipedia.org/wiki/Spain_and_the_American_Revolutionary_War#Declaration_of_war"}, {"title": "Great Siege of Gibraltar", "link": "https://wikipedia.org/wiki/Great_Siege_of_Gibraltar"}]}, {"year": "1795", "text": "French Revolutionary Wars: In what became known as <PERSON><PERSON>'s Retreat, a British Royal Navy squadron led by Vice Admiral <PERSON> strongly resists a much larger French Navy force and withdraws largely intact, setting up the French Navy defeat at the Battle of Groix six days later.", "html": "1795 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: In what became known as <a href=\"https://wikipedia.org/wiki/Cornwallis%27s_Retreat\" title=\"<PERSON><PERSON>'s Retreat\"><PERSON><PERSON>'s Retreat</a>, a British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> squadron led by Vice Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> strongly resists a much larger <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">French Navy</a> force and withdraws largely intact, setting up the French Navy defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_Groix\" title=\"Battle of Groix\">Battle of Groix</a> six days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: In what became known as <a href=\"https://wikipedia.org/wiki/Cornwallis%27s_Retreat\" title=\"<PERSON><PERSON>'s Retreat\"><PERSON><PERSON>'s Retreat</a>, a British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> squadron led by Vice Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> strongly resists a much larger <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">French Navy</a> force and withdraws largely intact, setting up the French Navy defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_Groix\" title=\"Battle of Groix\">Battle of Groix</a> six days later.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "<PERSON><PERSON>'s Retreat", "link": "https://wikipedia.org/wiki/Cornwallis%27s_Retreat"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "French Navy", "link": "https://wikipedia.org/wiki/French_Navy"}, {"title": "Battle of Groix", "link": "https://wikipedia.org/wiki/Battle_of_Groix"}]}, {"year": "1811", "text": "Survivors of an attack the previous day by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on board the Pacific Fur Company's ship Tonquin, intentionally detonate a powder magazine on the ship, destroying it and killing about 100 attackers.", "html": "1811 - Survivors of an attack the previous day by <a href=\"https://wikipedia.org/wiki/Tla-o-qui-aht_First_Nations\" title=\"Tla-o-qui-aht First Nations\">Tla-o-qui-aht</a> on board the <a href=\"https://wikipedia.org/wiki/Pacific_Fur_Company\" title=\"Pacific Fur Company\">Pacific Fur Company</a>'s ship <i><a href=\"https://wikipedia.org/wiki/Tonquin_(1807)\" class=\"mw-redirect\" title=\"Tonquin (1807)\">Tonquin</a></i>, intentionally detonate a powder magazine on the ship, <a href=\"https://wikipedia.org/wiki/Battle_of_Woody_Point\" title=\"Battle of Woody Point\">destroying it</a> and killing about 100 attackers.", "no_year_html": "Survivors of an attack the previous day by <a href=\"https://wikipedia.org/wiki/Tla-o-qui-aht_First_Nations\" title=\"Tla-o-qui-aht First Nations\">Tla-o-qui-aht</a> on board the <a href=\"https://wikipedia.org/wiki/Pacific_Fur_Company\" title=\"Pacific Fur Company\">Pacific Fur Company</a>'s ship <i><a href=\"https://wikipedia.org/wiki/Tonquin_(1807)\" class=\"mw-redirect\" title=\"Tonquin (1807)\">Ton<PERSON></a></i>, intentionally detonate a powder magazine on the ship, <a href=\"https://wikipedia.org/wiki/Battle_of_Woody_Point\" title=\"Battle of Woody Point\">destroying it</a> and killing about 100 attackers.", "links": [{"title": "Tla-o-qui-aht First Nations", "link": "https://wikipedia.org/wiki/Tla-o-qui-aht_First_Nations"}, {"title": "Pacific Fur Company", "link": "https://wikipedia.org/wiki/Pacific_Fur_Company"}, {"title": "<PERSON><PERSON><PERSON> (1807)", "link": "https://wikipedia.org/wiki/Ton<PERSON>_(1807)"}, {"title": "Battle of Woody Point", "link": "https://wikipedia.org/wiki/Battle_of_Woody_Point"}]}, {"year": "1815", "text": "Battle of Ligny and Battle of Quatre Bras, two days before the Battle of Waterloo.", "html": "1815 - <a href=\"https://wikipedia.org/wiki/Battle_of_Ligny\" title=\"Battle of Ligny\">Battle of Ligny</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Quatre_Bras\" title=\"Battle of Quatre Bras\">Battle of Quatre Bras</a>, two days before the <a href=\"https://wikipedia.org/wiki/Battle_of_Waterloo\" title=\"Battle of Waterloo\">Battle of Waterloo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Ligny\" title=\"Battle of Ligny\">Battle of Ligny</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Quatre_Bras\" title=\"Battle of Quatre Bras\">Battle of Quatre Bras</a>, two days before the <a href=\"https://wikipedia.org/wiki/Battle_of_Waterloo\" title=\"Battle of Waterloo\">Battle of Waterloo</a>.", "links": [{"title": "Battle of Ligny", "link": "https://wikipedia.org/wiki/Battle_of_Ligny"}, {"title": "Battle of Quatre Bras", "link": "https://wikipedia.org/wiki/Battle_of_Quatre_Bras"}, {"title": "Battle of Waterloo", "link": "https://wikipedia.org/wiki/Battle_of_Waterloo"}]}, {"year": "1819", "text": "A major earthquake strikes the Kutch district of western India, killing over 1,543 people and raising a 6-metre-high (20 ft), 6-kilometre-wide (3.7 mi), ridge, extending for at least 80 kilometres (50 mi), that was known as the Allah Bund (\"Dam of God\").", "html": "1819 - A <a href=\"https://wikipedia.org/wiki/1819_Rann_of_Kutch_earthquake\" title=\"1819 Rann of Kutch earthquake\">major earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Kutch_district\" title=\"Kutch district\">Kutch district</a> of western India, killing over 1,543 people and raising a 6-metre-high (20 ft), 6-kilometre-wide (3.7 mi), ridge, extending for at least 80 kilometres (50 mi), that was known as the Allah Bund (\"Dam of God\").", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1819_Rann_of_Kutch_earthquake\" title=\"1819 Rann of Kutch earthquake\">major earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Kutch_district\" title=\"Kutch district\">Kutch district</a> of western India, killing over 1,543 people and raising a 6-metre-high (20 ft), 6-kilometre-wide (3.7 mi), ridge, extending for at least 80 kilometres (50 mi), that was known as the Allah Bund (\"Dam of God\").", "links": [{"title": "1819 Rann of Kutch earthquake", "link": "https://wikipedia.org/wiki/1819_Rann_of_Kutch_earthquake"}, {"title": "Kutch district", "link": "https://wikipedia.org/wiki/Kutch_district"}]}, {"year": "1824", "text": "A meeting at Old Slaughter's coffee house in London leads to the formation of what is now the Royal Society for the Prevention of Cruelty to Animals (RSPCA).", "html": "1824 - A meeting at Old Slaughter's coffee house in London leads to the formation of what is now the <a href=\"https://wikipedia.org/wiki/Royal_Society_for_the_Prevention_of_Cruelty_to_Animals\" class=\"mw-redirect\" title=\"Royal Society for the Prevention of Cruelty to Animals\">Royal Society for the Prevention of Cruelty to Animals</a> (RSPCA).", "no_year_html": "A meeting at Old Slaughter's coffee house in London leads to the formation of what is now the <a href=\"https://wikipedia.org/wiki/Royal_Society_for_the_Prevention_of_Cruelty_to_Animals\" class=\"mw-redirect\" title=\"Royal Society for the Prevention of Cruelty to Animals\">Royal Society for the Prevention of Cruelty to Animals</a> (RSPCA).", "links": [{"title": "Royal Society for the Prevention of Cruelty to Animals", "link": "https://wikipedia.org/wiki/Royal_Society_for_the_Prevention_of_Cruelty_to_Animals"}]}, {"year": "1836", "text": "The formation of the London Working Men's Association gives rise to the Chartist Movement.", "html": "1836 - The formation of the <a href=\"https://wikipedia.org/wiki/London_Working_Men%27s_Association\" title=\"London Working Men's Association\">London Working Men's Association</a> gives rise to the <a href=\"https://wikipedia.org/wiki/Chartism\" title=\"Chartism\">Chartist Movement</a>.", "no_year_html": "The formation of the <a href=\"https://wikipedia.org/wiki/London_Working_Men%27s_Association\" title=\"London Working Men's Association\">London Working Men's Association</a> gives rise to the <a href=\"https://wikipedia.org/wiki/Chartism\" title=\"Chartism\">Chartist Movement</a>.", "links": [{"title": "London Working Men's Association", "link": "https://wikipedia.org/wiki/London_Working_Men%27s_Association"}, {"title": "Chartism", "link": "https://wikipedia.org/wiki/Chartism"}]}, {"year": "1846", "text": "The Papal conclave of 1846 elects <PERSON>, beginning the longest reign in the history of the papacy.", "html": "1846 - The <a href=\"https://wikipedia.org/wiki/Papal_conclave,_1846\" class=\"mw-redirect\" title=\"Papal conclave, 1846\">Papal conclave of 1846</a> elects <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius IX\">Pope <PERSON></a>, beginning the longest reign in the history of the papacy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Papal_conclave,_1846\" class=\"mw-redirect\" title=\"Papal conclave, 1846\">Papal conclave of 1846</a> elects <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius IX\">Pope <PERSON></a>, beginning the longest reign in the history of the papacy.", "links": [{"title": "Papal conclave, 1846", "link": "https://wikipedia.org/wiki/Papal_conclave,_1846"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON> delivers his House Divided speech in Springfield, Illinois.", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham <PERSON>\"><PERSON></a> delivers his <i><a href=\"https://wikipedia.org/wiki/Lincoln%27s_House_Divided_Speech\" title=\"Lincoln's House Divided Speech\">House Divided</a></i> speech in <a href=\"https://wikipedia.org/wiki/Springfield,_Illinois\" title=\"Springfield, Illinois\">Springfield, Illinois</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> delivers his <i><a href=\"https://wikipedia.org/wiki/Lincoln%27s_House_Divided_Speech\" title=\"Lincoln's House Divided Speech\">House Divided</a></i> speech in <a href=\"https://wikipedia.org/wiki/Springfield,_Illinois\" title=\"Springfield, Illinois\">Springfield, Illinois</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lincoln's House Divided Speech", "link": "https://wikipedia.org/wiki/Lincoln%27s_House_Divided_Speech"}, {"title": "Springfield, Illinois", "link": "https://wikipedia.org/wiki/Springfield,_Illinois"}]}, {"year": "1871", "text": "The Universities Tests Act 1871 allows students to enter the universities of Oxford, Cambridge and Durham without religious tests (except for those intending to study theology).", "html": "1871 - The <a href=\"https://wikipedia.org/wiki/Universities_Tests_Act_1871\" title=\"Universities Tests Act 1871\">Universities Tests Act 1871</a> allows students to enter the universities of <a href=\"https://wikipedia.org/wiki/University_of_Oxford\" title=\"University of Oxford\">Oxford</a>, <a href=\"https://wikipedia.org/wiki/University_of_Cambridge\" title=\"University of Cambridge\">Cambridge</a> and <a href=\"https://wikipedia.org/wiki/Durham_University\" title=\"Durham University\">Durham</a> without religious tests (except for those intending to study <a href=\"https://wikipedia.org/wiki/Theology\" title=\"Theology\">theology</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Universities_Tests_Act_1871\" title=\"Universities Tests Act 1871\">Universities Tests Act 1871</a> allows students to enter the universities of <a href=\"https://wikipedia.org/wiki/University_of_Oxford\" title=\"University of Oxford\">Oxford</a>, <a href=\"https://wikipedia.org/wiki/University_of_Cambridge\" title=\"University of Cambridge\">Cambridge</a> and <a href=\"https://wikipedia.org/wiki/Durham_University\" title=\"Durham University\">Durham</a> without religious tests (except for those intending to study <a href=\"https://wikipedia.org/wiki/Theology\" title=\"Theology\">theology</a>).", "links": [{"title": "Universities Tests Act 1871", "link": "https://wikipedia.org/wiki/Universities_Tests_Act_1871"}, {"title": "University of Oxford", "link": "https://wikipedia.org/wiki/University_of_Oxford"}, {"title": "University of Cambridge", "link": "https://wikipedia.org/wiki/University_of_Cambridge"}, {"title": "Durham University", "link": "https://wikipedia.org/wiki/Durham_University"}, {"title": "Theology", "link": "https://wikipedia.org/wiki/Theology"}]}, {"year": "1883", "text": "The Victoria Hall theatre panic in Sunderland, England, kills 183 children.", "html": "1883 - The <a href=\"https://wikipedia.org/wiki/Victoria_Hall_stampede\" class=\"mw-redirect\" title=\"Victoria Hall stampede\">Victoria Hall theatre panic</a> in <a href=\"https://wikipedia.org/wiki/Sunderland,_Tyne_and_Wear\" class=\"mw-redirect\" title=\"Sunderland, Tyne and Wear\">Sunderland</a>, England, kills 183 children.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Victoria_Hall_stampede\" class=\"mw-redirect\" title=\"Victoria Hall stampede\">Victoria Hall theatre panic</a> in <a href=\"https://wikipedia.org/wiki/Sunderland,_Tyne_and_Wear\" class=\"mw-redirect\" title=\"Sunderland, Tyne and Wear\">Sunderland</a>, England, kills 183 children.", "links": [{"title": "Victoria Hall stampede", "link": "https://wikipedia.org/wiki/Victoria_Hall_stampede"}, {"title": "Sunderland, Tyne and Wear", "link": "https://wikipedia.org/wiki/Sunderland,_Tyne_and_Wear"}]}, {"year": "1884", "text": "The first purpose-built roller coaster, LaMarcus Adna <PERSON>'s \"Switchback Railway\", opens in New York's Coney Island amusement park.", "html": "1884 - The first purpose-built roller coaster, <a href=\"https://wikipedia.org/wiki/<PERSON>M<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"LaMarcus <PERSON>\">LaMarcus <PERSON></a>'s \"<a href=\"https://wikipedia.org/wiki/Switchback_Railway\" title=\"Switchback Railway\">Switchback Railway</a>\", opens in New York's <a href=\"https://wikipedia.org/wiki/Coney_Island\" title=\"Coney Island\">Coney Island</a> amusement park.", "no_year_html": "The first purpose-built roller coaster, <a href=\"https://wikipedia.org/wiki/<PERSON>M<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"LaMarcus <PERSON>\">LaMarcus <PERSON></a>'s \"<a href=\"https://wikipedia.org/wiki/Switchback_Railway\" title=\"Switchback Railway\">Switchback Railway</a>\", opens in New York's <a href=\"https://wikipedia.org/wiki/Coney_Island\" title=\"Coney Island\">Coney Island</a> amusement park.", "links": [{"title": "LaMar<PERSON>", "link": "https://wikipedia.org/wiki/LaMar<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Switchback Railway", "link": "https://wikipedia.org/wiki/Switchback_Railway"}, {"title": "Coney Island", "link": "https://wikipedia.org/wiki/Coney_Island"}]}, {"year": "1897", "text": "A treaty annexing the Republic of Hawaii to the United States is signed; the Republic would not be dissolved until a year later.", "html": "1897 - A treaty annexing the <a href=\"https://wikipedia.org/wiki/Republic_of_Hawaii\" title=\"Republic of Hawaii\">Republic of Hawaii</a> to the United States is signed; the Republic would not be dissolved until a year later.", "no_year_html": "A treaty annexing the <a href=\"https://wikipedia.org/wiki/Republic_of_Hawaii\" title=\"Republic of Hawaii\">Republic of Hawaii</a> to the United States is signed; the Republic would not be dissolved until a year later.", "links": [{"title": "Republic of Hawaii", "link": "https://wikipedia.org/wiki/Republic_of_Hawaii"}]}, {"year": "1903", "text": "The Ford Motor Company is incorporated.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> is incorporated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> is incorporated.", "links": [{"title": "Ford Motor Company", "link": "https://wikipedia.org/wiki/Ford_Motor_Company"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON> Am<PERSON>en leaves Oslo, Norway, to commence the first east-west navigation of the Northwest Passage.", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Roald_Amundsen\" title=\"Roald Amundsen\">R<PERSON><PERSON> Am<PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Oslo\" title=\"Oslo\">Oslo</a>, Norway, to commence the first east-west navigation of the <a href=\"https://wikipedia.org/wiki/Northwest_Passage\" title=\"Northwest Passage\">Northwest Passage</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roald_Amundsen\" title=\"Roald Amundsen\"><PERSON><PERSON><PERSON> Am<PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Oslo\" title=\"Oslo\">Oslo</a>, Norway, to commence the first east-west navigation of the <a href=\"https://wikipedia.org/wiki/Northwest_Passage\" title=\"Northwest Passage\">Northwest Passage</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roald_Am<PERSON>en"}, {"title": "Oslo", "link": "https://wikipedia.org/wiki/Oslo"}, {"title": "Northwest Passage", "link": "https://wikipedia.org/wiki/Northwest_Passage"}]}, {"year": "1904", "text": "<PERSON><PERSON> assassinates <PERSON><PERSON>, Governor-General of Finland.", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON>\">assassinates</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Finland\" title=\"Governor-General of Finland\">Governor-General of Finland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON>\">assassinates</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Finland\" title=\"Governor-General of Finland\">Governor-General of Finland</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Assassination of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor-General of Finland", "link": "https://wikipedia.org/wiki/Governor-General_of_Finland"}]}, {"year": "1904", "text": "Irish author <PERSON> begins a relationship with <PERSON> and subsequently uses the date to set the actions for his novel <PERSON>; this date is now traditionally called \"Bloomsday\".", "html": "1904 - Irish author <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins a relationship with <a href=\"https://wikipedia.org/wiki/Nora_<PERSON>nacle\" title=\"Nora Barnacle\"><PERSON></a> and subsequently uses the date to set the actions for his novel <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(novel)\" title=\"<PERSON> (novel)\">Ulysses</a></i>; this date is now traditionally called \"<a href=\"https://wikipedia.org/wiki/Bloomsday\" title=\"Bloomsday\">Bloomsday</a>\".", "no_year_html": "Irish author <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins a relationship with <a href=\"https://wikipedia.org/wiki/Nora_Barnacle\" title=\"Nora Barnacle\"><PERSON></a> and subsequently uses the date to set the actions for his novel <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(novel)\" title=\"<PERSON> (novel)\">Ulysses</a></i>; this date is now traditionally called \"<a href=\"https://wikipedia.org/wiki/Bloomsday\" title=\"Bloomsday\">Bloomsday</a>\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nora Barnacle", "link": "https://wikipedia.org/wiki/Nora_Barnacle"}, {"title": "<PERSON> (novel)", "link": "https://wikipedia.org/wiki/<PERSON>_(novel)"}, {"title": "Bloomsday", "link": "https://wikipedia.org/wiki/Bloomsday"}]}, {"year": "1911", "text": "IBM founded as the Computing-Tabulating-Recording Company in Endicott, New York.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/IBM\" title=\"IBM\">IBM</a> founded as the Computing-Tabulating-Recording Company in <a href=\"https://wikipedia.org/wiki/Endicott,_New_York\" title=\"Endicott, New York\">Endicott, New York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/IBM\" title=\"IBM\">IBM</a> founded as the Computing-Tabulating-Recording Company in <a href=\"https://wikipedia.org/wiki/Endicott,_New_York\" title=\"Endicott, New York\">Endicott, New York</a>.", "links": [{"title": "IBM", "link": "https://wikipedia.org/wiki/IBM"}, {"title": "Endicott, New York", "link": "https://wikipedia.org/wiki/Endicott,_New_York"}]}, {"year": "1922", "text": "General election in the Irish Free State: The pro-Treaty Sinn Féin party wins a large majority.", "html": "1922 - General election in the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a>: The pro-Treaty <a href=\"https://wikipedia.org/wiki/History_of_Sinn_F%C3%A9in\" title=\"History of Sinn Féin\">Sinn Féin</a> party wins a large majority.", "no_year_html": "General election in the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a>: The pro-Treaty <a href=\"https://wikipedia.org/wiki/History_of_Sinn_F%C3%A9in\" title=\"History of Sinn Féin\">Sinn Féin</a> party wins a large majority.", "links": [{"title": "Irish Free State", "link": "https://wikipedia.org/wiki/Irish_Free_State"}, {"title": "History of Sinn Féin", "link": "https://wikipedia.org/wiki/History_of_Sinn_F%C3%A9in"}]}, {"year": "1925", "text": "Artek, the most famous Young Pioneer camp of the Soviet Union, is established.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(camp)\" title=\"<PERSON><PERSON> (camp)\"><PERSON><PERSON></a>, the most famous <a href=\"https://wikipedia.org/wiki/Young_Pioneer\" class=\"mw-redirect\" title=\"Young Pioneer\">Young Pioneer</a> camp of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, is established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(camp)\" title=\"<PERSON><PERSON> (camp)\"><PERSON><PERSON></a>, the most famous <a href=\"https://wikipedia.org/wiki/Young_Pioneer\" class=\"mw-redirect\" title=\"Young Pioneer\">Young Pioneer</a> camp of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, is established.", "links": [{"title": "Artek (camp)", "link": "https://wikipedia.org/wiki/Arte<PERSON>_(camp)"}, {"title": "Young Pioneer", "link": "https://wikipedia.org/wiki/Young_Pioneer"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1930", "text": "Sovnarkom establishes decree time in the USSR.", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Sovnarkom\" class=\"mw-redirect\" title=\"Sovnarkom\">Sovnarkom</a> establishes <a href=\"https://wikipedia.org/wiki/Decree_time\" title=\"Decree time\">decree time</a> in the USSR.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sovnarkom\" class=\"mw-redirect\" title=\"Sovnarkom\">Sovnarkom</a> establishes <a href=\"https://wikipedia.org/wiki/Decree_time\" title=\"Decree time\">decree time</a> in the USSR.", "links": [{"title": "Sovnarkom", "link": "https://wikipedia.org/wiki/Sovnarkom"}, {"title": "Decree time", "link": "https://wikipedia.org/wiki/Decree_time"}]}, {"year": "1933", "text": "The National Industrial Recovery Act is passed in the United States, allowing businesses to avoid antitrust prosecution if they establish voluntary wage, price, and working condition regulations on an industry-wide basis.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/National_Industrial_Recovery_Act\" class=\"mw-redirect\" title=\"National Industrial Recovery Act\">National Industrial Recovery Act</a> is passed in the United States, allowing businesses to avoid <a href=\"https://wikipedia.org/wiki/Antitrust\" class=\"mw-redirect\" title=\"Antitrust\">antitrust</a> prosecution if they establish voluntary wage, price, and working condition regulations on an industry-wide basis.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Industrial_Recovery_Act\" class=\"mw-redirect\" title=\"National Industrial Recovery Act\">National Industrial Recovery Act</a> is passed in the United States, allowing businesses to avoid <a href=\"https://wikipedia.org/wiki/Antitrust\" class=\"mw-redirect\" title=\"Antitrust\">antitrust</a> prosecution if they establish voluntary wage, price, and working condition regulations on an industry-wide basis.", "links": [{"title": "National Industrial Recovery Act", "link": "https://wikipedia.org/wiki/National_Industrial_Recovery_Act"}, {"title": "Antitrust", "link": "https://wikipedia.org/wiki/Antitrust"}]}, {"year": "1940", "text": "World War II: Marshal <PERSON> becomes Chief of State of Vichy France (Chef de l'État Français).", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A9tain\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes Chief of State of <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy France</a> (<i>Chef de l'État Français</i>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A9tain\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes Chief of State of <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy France</a> (<i>Chef de l'État Français</i>).", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9tain"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vichy_France"}]}, {"year": "1940", "text": "A Communist government is installed in Lithuania.", "html": "1940 - A <a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">Communist</a> government is installed in <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">Communist</a> government is installed in <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "links": [{"title": "Communist", "link": "https://wikipedia.org/wiki/Communist"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}]}, {"year": "1948", "text": "Members of the Malayan Communist Party kill three British plantation managers in Sungai Siput; in response, British Malaya declares a state of emergency.", "html": "1948 - Members of the <a href=\"https://wikipedia.org/wiki/Malayan_Communist_Party\" title=\"Malayan Communist Party\">Malayan Communist Party</a> kill three British plantation managers in <a href=\"https://wikipedia.org/wiki/Sungai_Siput\" title=\"Sungai Siput\">Sungai Siput</a>; in response, <a href=\"https://wikipedia.org/wiki/British_Malaya\" title=\"British Malaya\">British Malaya</a> declares <a href=\"https://wikipedia.org/wiki/Malayan_Emergency\" title=\"Malayan Emergency\">a state of emergency</a>.", "no_year_html": "Members of the <a href=\"https://wikipedia.org/wiki/Malayan_Communist_Party\" title=\"Malayan Communist Party\">Malayan Communist Party</a> kill three British plantation managers in <a href=\"https://wikipedia.org/wiki/Sungai_Siput\" title=\"Sungai Siput\">Sungai Siput</a>; in response, <a href=\"https://wikipedia.org/wiki/British_Malaya\" title=\"British Malaya\">British Malaya</a> declares <a href=\"https://wikipedia.org/wiki/Malayan_Emergency\" title=\"Malayan Emergency\">a state of emergency</a>.", "links": [{"title": "Malayan Communist Party", "link": "https://wikipedia.org/wiki/Malayan_Communist_Party"}, {"title": "Sungai Siput", "link": "https://wikipedia.org/wiki/Sungai_Siput"}, {"title": "British Malaya", "link": "https://wikipedia.org/wiki/British_Malaya"}, {"title": "Malayan Emergency", "link": "https://wikipedia.org/wiki/Malayan_Emergency"}]}, {"year": "1955", "text": "In a futile effort to topple Argentine President <PERSON>, rogue aircraft pilots of the Argentine Navy drop several bombs upon an unarmed crowd demonstrating in favor of <PERSON><PERSON> in Buenos Aires, killing 364 and injuring at least 800. At the same time on the ground, some soldiers attempt to stage a coup but are suppressed by loyal forces.", "html": "1955 - In a futile effort to topple Argentine President <a href=\"https://wikipedia.org/wiki/Juan_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, rogue aircraft pilots of the <a href=\"https://wikipedia.org/wiki/Argentine_Navy\" title=\"Argentine Navy\">Argentine Navy</a> <a href=\"https://wikipedia.org/wiki/Bombing_of_Plaza_de_Mayo\" title=\"Bombing of Plaza de Mayo\">drop several bombs</a> upon an unarmed crowd demonstrating in favor of <PERSON><PERSON> in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, killing 364 and injuring at least 800. At the same time on the ground, some soldiers attempt to stage a coup but are suppressed by loyal forces.", "no_year_html": "In a futile effort to topple Argentine President <a href=\"https://wikipedia.org/wiki/Juan_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, rogue aircraft pilots of the <a href=\"https://wikipedia.org/wiki/Argentine_Navy\" title=\"Argentine Navy\">Argentine Navy</a> <a href=\"https://wikipedia.org/wiki/Bombing_of_Plaza_de_Mayo\" title=\"Bombing of Plaza de Mayo\">drop several bombs</a> upon an unarmed crowd demonstrating in favor of <PERSON><PERSON> in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, killing 364 and injuring at least 800. At the same time on the ground, some soldiers attempt to stage a coup but are suppressed by loyal forces.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Per%C3%B3n"}, {"title": "Argentine Navy", "link": "https://wikipedia.org/wiki/Argentine_Navy"}, {"title": "Bombing of Plaza de Mayo", "link": "https://wikipedia.org/wiki/Bombing_of_Plaza_de_Mayo"}, {"title": "Buenos Aires", "link": "https://wikipedia.org/wiki/Buenos_Aires"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and other leaders of the 1956 Hungarian Uprising are executed.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Im<PERSON>_Nagy\" title=\"<PERSON><PERSON><PERSON>gy\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/P%C3%A1l_Mal%C3%A9ter\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and other leaders of the 1956 <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Uprising</a> are executed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_Nagy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/P%C3%A1l_Mal%C3%A9ter\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and other leaders of the 1956 <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Uprising</a> are executed.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_Nagy"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A1l_Mal%C3%A9ter"}, {"title": "Hungarian Revolution of 1956", "link": "https://wikipedia.org/wiki/Hungarian_Revolution_of_1956"}]}, {"year": "1961", "text": "While on tour with the Kirov Ballet in Paris, <PERSON> defects from the Soviet Union.", "html": "1961 - While on tour with the <a href=\"https://wikipedia.org/wiki/Kirov_Ballet\" class=\"mw-redirect\" title=\"Kirov Ballet\">Kirov Ballet</a> in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defects from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "While on tour with the <a href=\"https://wikipedia.org/wiki/Kirov_Ballet\" class=\"mw-redirect\" title=\"Kirov Ballet\">Kirov Ballet</a> in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defects from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Kirov Ballet", "link": "https://wikipedia.org/wiki/<PERSON>rov_Ballet"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1963", "text": "Soviet Space Program: Vostok 6 mission: Cosmonaut <PERSON><PERSON> becomes the first woman in space.", "html": "1963 - Soviet Space Program: <i><a href=\"https://wikipedia.org/wiki/Vostok_6\" title=\"Vostok 6\">Vostok 6</a></i> mission: <a href=\"https://wikipedia.org/wiki/Cosmonaut\" class=\"mw-redirect\" title=\"Cosmonaut\">Cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first woman in space.", "no_year_html": "Soviet Space Program: <i><a href=\"https://wikipedia.org/wiki/Vostok_6\" title=\"Vostok 6\">Vostok 6</a></i> mission: <a href=\"https://wikipedia.org/wiki/Cosmonaut\" class=\"mw-redirect\" title=\"Cosmonaut\">Cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first woman in space.", "links": [{"title": "Vostok 6", "link": "https://wikipedia.org/wiki/Vostok_6"}, {"title": "Cosmonaut", "link": "https://wikipedia.org/wiki/Cosmonaut"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valentina_<PERSON>kova"}]}, {"year": "1963", "text": "In an attempt to resolve the Buddhist crisis in South Vietnam, a Joint Communique was signed between President <PERSON><PERSON> and Buddhist leaders.", "html": "1963 - In an attempt to resolve the <a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a> in <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>, a <a href=\"https://wikipedia.org/wiki/Joint_Communique\" class=\"mw-redirect\" title=\"Joint Communique\">Joint Communique</a> was signed between President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and Buddhist leaders.", "no_year_html": "In an attempt to resolve the <a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a> in <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>, a <a href=\"https://wikipedia.org/wiki/Joint_Communique\" class=\"mw-redirect\" title=\"Joint Communique\">Joint Communique</a> was signed between President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a> and Buddhist leaders.", "links": [{"title": "Buddhist crisis", "link": "https://wikipedia.org/wiki/Buddhist_crisis"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "Joint Communique", "link": "https://wikipedia.org/wiki/Joint_Communique"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "The largest single-site hydroelectric power project in Canada is inaugurated at Churchill Falls Generating Station.", "html": "1972 - The largest single-site hydroelectric power project in Canada is inaugurated at <a href=\"https://wikipedia.org/wiki/Churchill_Falls_Generating_Station\" title=\"Churchill Falls Generating Station\">Churchill Falls Generating Station</a>.", "no_year_html": "The largest single-site hydroelectric power project in Canada is inaugurated at <a href=\"https://wikipedia.org/wiki/Churchill_Falls_Generating_Station\" title=\"Churchill Falls Generating Station\">Churchill Falls Generating Station</a>.", "links": [{"title": "Churchill Falls Generating Station", "link": "https://wikipedia.org/wiki/Churchill_Falls_Generating_Station"}]}, {"year": "1976", "text": "Soweto uprising: A non-violent march by 15,000 students in Soweto, South Africa, turns into days of rioting when police open fire on the crowd.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Soweto_uprising\" title=\"Soweto uprising\">Soweto uprising</a>: A non-violent march by 15,000 students in <a href=\"https://wikipedia.org/wiki/Soweto\" title=\"Soweto\">Soweto</a>, South Africa, turns into days of rioting when police open fire on the crowd.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soweto_uprising\" title=\"Soweto uprising\">Soweto uprising</a>: A non-violent march by 15,000 students in <a href=\"https://wikipedia.org/wiki/Soweto\" title=\"Soweto\">Soweto</a>, South Africa, turns into days of rioting when police open fire on the crowd.", "links": [{"title": "Soweto uprising", "link": "https://wikipedia.org/wiki/Soweto_uprising"}, {"title": "Soweto", "link": "https://wikipedia.org/wiki/Soweto"}]}, {"year": "1977", "text": "Oracle Corporation is incorporated in Redwood Shores, California, as Software Development Laboratories (SDL), by <PERSON>, <PERSON> and <PERSON>.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Oracle_Corporation\" title=\"Oracle Corporation\">Oracle Corporation</a> is incorporated in <a href=\"https://wikipedia.org/wiki/Redwood_Shores,_California\" title=\"Redwood Shores, California\">Redwood Shores, California</a>, as Software Development Laboratories (SDL), by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oracle_Corporation\" title=\"Oracle Corporation\">Oracle Corporation</a> is incorporated in <a href=\"https://wikipedia.org/wiki/Redwood_Shores,_California\" title=\"Redwood Shores, California\">Redwood Shores, California</a>, as Software Development Laboratories (SDL), by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Oracle Corporation", "link": "https://wikipedia.org/wiki/Oracle_Corporation"}, {"title": "Redwood Shores, California", "link": "https://wikipedia.org/wiki/Redwood_Shores,_California"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1981", "text": "US President <PERSON> awards the Congressional Gold Medal to <PERSON>, Canada's former ambassador to Iran, for helping six Americans escape from Iran during the hostage crisis of 1979-81; he is the first foreign citizen bestowed the honor.", "html": "1981 - US President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> awards the <a href=\"https://wikipedia.org/wiki/Congressional_Gold_Medal\" title=\"Congressional Gold Medal\">Congressional Gold Medal</a> to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canada's former ambassador to <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>, for helping six Americans escape from Iran during the <a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">hostage crisis of 1979-81</a>; he is the first foreign citizen bestowed the honor.", "no_year_html": "US President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> awards the <a href=\"https://wikipedia.org/wiki/Congressional_Gold_Medal\" title=\"Congressional Gold Medal\">Congressional Gold Medal</a> to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canada's former ambassador to <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>, for helping six Americans escape from Iran during the <a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">hostage crisis of 1979-81</a>; he is the first foreign citizen bestowed the honor.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Congressional Gold Medal", "link": "https://wikipedia.org/wiki/Congressional_Gold_Medal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Iran hostage crisis", "link": "https://wikipedia.org/wiki/Iran_hostage_crisis"}]}, {"year": "1989", "text": "Revolutions of 1989: <PERSON><PERSON><PERSON>, the former Hungarian prime minister, is reburied in Budapest following the collapse of Communism in Hungary.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Revolutions_of_1989\" title=\"Revolutions of 1989\">Revolutions of 1989</a>: <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>gy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Hungarian prime minister</a>, is reburied in <a href=\"https://wikipedia.org/wiki/Budapest\" title=\"Budapest\">Budapest</a> following the collapse of Communism in Hungary.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Revolutions_of_1989\" title=\"Revolutions of 1989\">Revolutions of 1989</a>: <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>gy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Hungarian prime minister</a>, is reburied in <a href=\"https://wikipedia.org/wiki/Budapest\" title=\"Budapest\">Budapest</a> following the collapse of Communism in Hungary.", "links": [{"title": "Revolutions of 1989", "link": "https://wikipedia.org/wiki/Revolutions_of_1989"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_Nagy"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}, {"title": "Budapest", "link": "https://wikipedia.org/wiki/Budapest"}]}, {"year": "1995", "text": "The Astronomy Picture of the Day website is launched.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/Astronomy_Picture_of_the_Day\" title=\"Astronomy Picture of the Day\">Astronomy Picture of the Day</a> website is launched.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Astronomy_Picture_of_the_Day\" title=\"Astronomy Picture of the Day\">Astronomy Picture of the Day</a> website is launched.", "links": [{"title": "Astronomy Picture of the Day", "link": "https://wikipedia.org/wiki/Astronomy_Picture_of_the_Day"}]}, {"year": "1997", "text": "Fifty people are killed in the Daïat Labguer (M'sila) massacre in Algeria.", "html": "1997 - Fifty people are killed in the <a href=\"https://wikipedia.org/wiki/Da%C3%AFat_Labguer_(M%27sila)_massacre\" title=\"Daïat Labguer (M'sila) massacre\"><PERSON><PERSON><PERSON> Lab<PERSON> (M'sila) massacre</a> in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "no_year_html": "Fifty people are killed in the <a href=\"https://wikipedia.org/wiki/Da%C3%AFat_Labguer_(M%27sila)_massacre\" title=\"<PERSON>ï<PERSON> Labguer (M'sila) massacre\"><PERSON><PERSON><PERSON> (M'sila) massacre</a> in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (M'sila) massacre", "link": "https://wikipedia.org/wiki/Da%C3%AFat_Labguer_(M%27sila)_massacre"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}]}, {"year": "2000", "text": "The Secretary-General of the UN reports that Israel has complied with United Nations Security Council Resolution 425, 22 years after its issuance, and completely withdrew from Lebanon. The Resolution does not encompass the Shebaa farms, which is claimed by Israel, Syria and Lebanon.", "html": "2000 - The Secretary-General of the UN reports that Israel has complied with <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_425\" title=\"United Nations Security Council Resolution 425\">United Nations Security Council Resolution 425</a>, 22 years after its issuance, and completely withdrew from Lebanon. The Resolution does not encompass the <a href=\"https://wikipedia.org/wiki/Shebaa_farms\" class=\"mw-redirect\" title=\"Shebaa farms\">Shebaa farms</a>, which is claimed by Israel, Syria and Lebanon.", "no_year_html": "The Secretary-General of the UN reports that Israel has complied with <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_425\" title=\"United Nations Security Council Resolution 425\">United Nations Security Council Resolution 425</a>, 22 years after its issuance, and completely withdrew from Lebanon. The Resolution does not encompass the <a href=\"https://wikipedia.org/wiki/Shebaa_farms\" class=\"mw-redirect\" title=\"Shebaa farms\">Shebaa farms</a>, which is claimed by Israel, Syria and Lebanon.", "links": [{"title": "United Nations Security Council Resolution 425", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_425"}, {"title": "Shebaa farms", "link": "https://wikipedia.org/wiki/She<PERSON>a_farms"}]}, {"year": "2002", "text": "<PERSON><PERSON> is canonized by the Roman Catholic Church.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Pa<PERSON> Pi<PERSON>\"><PERSON><PERSON></a> is canonized by the Roman Catholic Church.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is canonized by the Roman Catholic Church.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "2010", "text": "Bhutan becomes the first country to institute a total ban on tobacco.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Bhutan\" title=\"Bhutan\">Bhutan</a> becomes the first country to institute a <a href=\"https://wikipedia.org/wiki/Tobacco_Control_Act_of_Bhutan_2010\" title=\"Tobacco Control Act of Bhutan 2010\">total ban on tobacco</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bhutan\" title=\"Bhutan\">Bhutan</a> becomes the first country to institute a <a href=\"https://wikipedia.org/wiki/Tobacco_Control_Act_of_Bhutan_2010\" title=\"Tobacco Control Act of Bhutan 2010\">total ban on tobacco</a>.", "links": [{"title": "Bhutan", "link": "https://wikipedia.org/wiki/Bhutan"}, {"title": "Tobacco Control Act of Bhutan 2010", "link": "https://wikipedia.org/wiki/Tobacco_Control_Act_of_Bhutan_2010"}]}, {"year": "2012", "text": "China successfully launches its Shenzhou 9 spacecraft, carrying three astronauts, including the first female Chinese astronaut <PERSON>, to the Tiangong-1 orbital module.", "html": "2012 - China successfully launches its <a href=\"https://wikipedia.org/wiki/Shenzhou_9\" title=\"Shenzhou 9\">Shenzhou 9</a> spacecraft, carrying three astronauts, including the first female Chinese astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>(astronaut)\" class=\"mw-redirect\" title=\"<PERSON> (astronaut)\"><PERSON></a>, to the <a href=\"https://wikipedia.org/wiki/Tiangong-1\" title=\"Tiangong-1\">Tiangong-1</a> orbital module.", "no_year_html": "China successfully launches its <a href=\"https://wikipedia.org/wiki/Shenzhou_9\" title=\"Shenzhou 9\">Shenzhou 9</a> spacecraft, carrying three astronauts, including the first female Chinese astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>(astronaut)\" class=\"mw-redirect\" title=\"<PERSON> (astronaut)\"><PERSON></a>, to the <a href=\"https://wikipedia.org/wiki/Tiangong-1\" title=\"Tiangong-1\">Tiangong-1</a> orbital module.", "links": [{"title": "Shenzhou 9", "link": "https://wikipedia.org/wiki/Shenzhou_9"}, {"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)"}, {"title": "Tiangong-1", "link": "https://wikipedia.org/wiki/Tiangong-1"}]}, {"year": "2012", "text": "The United States Air Force's robotic Boeing X-37B spaceplane returns to Earth after a classified 469-day orbital mission.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a>'s robotic <a href=\"https://wikipedia.org/wiki/Boeing_X-37B\" class=\"mw-redirect\" title=\"Boeing X-37B\">Boeing X-37B</a> <a href=\"https://wikipedia.org/wiki/Spaceplane\" title=\"Spaceplane\">spaceplane</a> returns to Earth after a <a href=\"https://wikipedia.org/wiki/USA-226\" class=\"mw-redirect\" title=\"USA-226\">classified 469-day orbital mission</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a>'s robotic <a href=\"https://wikipedia.org/wiki/Boeing_X-37B\" class=\"mw-redirect\" title=\"Boeing X-37B\">Boeing X-37B</a> <a href=\"https://wikipedia.org/wiki/Spaceplane\" title=\"Spaceplane\">spaceplane</a> returns to Earth after a <a href=\"https://wikipedia.org/wiki/USA-226\" class=\"mw-redirect\" title=\"USA-226\">classified 469-day orbital mission</a>.", "links": [{"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Boeing X-37B", "link": "https://wikipedia.org/wiki/Boeing_X-37B"}, {"title": "Spaceplane", "link": "https://wikipedia.org/wiki/Spaceplane"}, {"title": "USA-226", "link": "https://wikipedia.org/wiki/USA-226"}]}, {"year": "2013", "text": "A multi-day cloudburst, centered on the North Indian state of Uttarakhand, causes devastating floods and landslides, becoming the country's worst natural disaster since the 2004 tsunami.", "html": "2013 - A multi-day <a href=\"https://wikipedia.org/wiki/2013_North_India_floods\" title=\"2013 North India floods\">cloudburst</a>, centered on the North Indian state of <a href=\"https://wikipedia.org/wiki/Uttarakhand\" title=\"Uttarakhand\">Uttarakhand</a>, causes devastating floods and landslides, becoming the country's worst natural disaster since the 2004 tsunami.", "no_year_html": "A multi-day <a href=\"https://wikipedia.org/wiki/2013_North_India_floods\" title=\"2013 North India floods\">cloudburst</a>, centered on the North Indian state of <a href=\"https://wikipedia.org/wiki/Uttarakhand\" title=\"Uttarakhand\">Uttarakhand</a>, causes devastating floods and landslides, becoming the country's worst natural disaster since the 2004 tsunami.", "links": [{"title": "2013 North India floods", "link": "https://wikipedia.org/wiki/2013_North_India_floods"}, {"title": "Uttarakhand", "link": "https://wikipedia.org/wiki/Uttarakhand"}]}, {"year": "2015", "text": "American businessman <PERSON> announces his campaign to run for President of the United States in the upcoming election.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Business_career_of_<PERSON>_<PERSON>\" title=\"Business career of <PERSON>\">American businessman</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces his <a href=\"https://wikipedia.org/wiki/2016_<PERSON>_<PERSON>_presidential_campaign\" class=\"mw-redirect\" title=\"2016 <PERSON> presidential campaign\">campaign</a> to run for <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> in the upcoming <a href=\"https://wikipedia.org/wiki/2016_United_States_presidential_election\" title=\"2016 United States presidential election\">election</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Business_career_of_<PERSON>_<PERSON>\" title=\"Business career of <PERSON>\">American businessman</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces his <a href=\"https://wikipedia.org/wiki/2016_<PERSON>_<PERSON>_presidential_campaign\" class=\"mw-redirect\" title=\"2016 <PERSON> presidential campaign\">campaign</a> to run for <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> in the upcoming <a href=\"https://wikipedia.org/wiki/2016_United_States_presidential_election\" title=\"2016 United States presidential election\">election</a>.", "links": [{"title": "Business career of <PERSON>", "link": "https://wikipedia.org/wiki/Business_career_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "2016 <PERSON> presidential campaign", "link": "https://wikipedia.org/wiki/2016_<PERSON>_<PERSON>_presidential_campaign"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "2016 United States presidential election", "link": "https://wikipedia.org/wiki/2016_United_States_presidential_election"}]}, {"year": "2016", "text": "Shanghai Disneyland Park, the first Disney Park in Mainland China, opens to the public.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Shanghai_Disneyland_Park\" class=\"mw-redirect\" title=\"Shanghai Disneyland Park\">Shanghai Disneyland Park</a>, the first Disney Park in Mainland China, opens to the public.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shanghai_Disneyland_Park\" class=\"mw-redirect\" title=\"Shanghai Disneyland Park\">Shanghai Disneyland Park</a>, the first Disney Park in Mainland China, opens to the public.", "links": [{"title": "Shanghai Disneyland Park", "link": "https://wikipedia.org/wiki/Shanghai_Disneyland_Park"}]}, {"year": "2019", "text": "Upwards of 2,000,000 people participate in the 2019-20 Hong Kong protests, the largest in Hong Kong's history.", "html": "2019 - Upwards of 2,000,000 people participate in the <a href=\"https://wikipedia.org/wiki/2019%E2%80%9320_Hong_Kong_protests\" class=\"mw-redirect\" title=\"2019-20 Hong Kong protests\">2019-20 Hong Kong protests</a>, the largest in Hong Kong's history.", "no_year_html": "Upwards of 2,000,000 people participate in the <a href=\"https://wikipedia.org/wiki/2019%E2%80%9320_Hong_Kong_protests\" class=\"mw-redirect\" title=\"2019-20 Hong Kong protests\">2019-20 Hong Kong protests</a>, the largest in Hong Kong's history.", "links": [{"title": "2019-20 Hong Kong protests", "link": "https://wikipedia.org/wiki/2019%E2%80%9320_Hong_Kong_protests"}]}], "Births": [{"year": "1139", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (d. 1155)", "html": "1139 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1155)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1155)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1332", "text": "<PERSON>, English daughter of <PERSON> of England (d. 1379)", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1379)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1379)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1454", "text": "<PERSON> of Aragon, Queen of Naples (d. 1517)", "html": "1454 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Aragon,_Queen_of_Naples\" title=\"<PERSON> of Aragon, Queen of Naples\"><PERSON> of Aragon, Queen of Naples</a> (d. 1517)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Naples\" title=\"<PERSON> of Aragon, Queen of Naples\"><PERSON> of Aragon, Queen of Naples</a> (d. 1517)", "links": [{"title": "<PERSON> of Aragon, Queen of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon,_Queen_of_Naples"}]}, {"year": "1514", "text": "<PERSON>, English academic and politician, English Secretary of State (d. 1557)", "html": "1514 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">English Secretary of State</a> (d. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">English Secretary of State</a> (d. 1557)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State (England)", "link": "https://wikipedia.org/wiki/Secretary_of_State_(England)"}]}, {"year": "1516", "text": "<PERSON>, Ming dynasty official and Confucian martyr (d. 1555)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ming_dynasty)\" title=\"<PERSON> (Ming dynasty)\"><PERSON></a>, Ming dynasty official and Confucian martyr (d. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ming_dynasty)\" title=\"<PERSON> (Ming dynasty)\"><PERSON></a>, Ming dynasty official and Confucian martyr (d. 1555)", "links": [{"title": "<PERSON> (Ming dynasty)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ming_dynasty)"}]}, {"year": "1583", "text": "<PERSON>, Swedish politician, Lord High Chancellor of Sweden (d. 1654)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Chancellor_of_Sweden\" title=\"Lord High Chancellor of Sweden\">Lord High Chancellor of Sweden</a> (d. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Chancellor_of_Sweden\" title=\"Lord High Chancellor of Sweden\">Lord High Chancellor of Sweden</a> (d. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ier<PERSON>"}, {"title": "Lord High Chancellor of Sweden", "link": "https://wikipedia.org/wiki/Lord_High_Chancellor_of_Sweden"}]}, {"year": "1591", "text": "<PERSON>, Greek-Italian physician, mathematician, and theorist (d. 1655)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Italian physician, mathematician, and theorist (d. 1655)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Italian physician, mathematician, and theorist (d. 1655)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>igo"}]}, {"year": "1606", "text": "<PERSON>, 1st Earl of Donegall, Irish soldier and politician (d. 1675)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Donegall\" title=\"<PERSON>, 1st Earl of Donegall\"><PERSON>, 1st Earl of Donegall</a>, Irish soldier and politician (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Donegall\" title=\"<PERSON>, 1st Earl of Donegall\"><PERSON>, 1st Earl of Donegall</a>, Irish soldier and politician (d. 1675)", "links": [{"title": "<PERSON>, 1st Earl of Donegall", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Donegall"}]}, {"year": "1613", "text": "<PERSON>, English poet and educator (d. 1658)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and educator (d. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and educator (d. 1658)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1625", "text": "<PERSON>, French scholar (d. 1701)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar (d. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar (d. 1701)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1633", "text": "<PERSON>, French linguist and botanist (d. 1667)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>h%C3%A9venot\" title=\"<PERSON>\"><PERSON></a>, French linguist and botanist (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>h%C3%A9venot\" title=\"<PERSON>\"><PERSON></a>, French linguist and botanist (d. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>h%C3%A9venot"}]}, {"year": "1644", "text": "<PERSON>, Princess of Scotland, England and Ireland (d. 1670)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Princess of Scotland, England and Ireland (d. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Princess of Scotland, England and Ireland (d. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1653", "text": "<PERSON>, 1st Earl of Abingdon, English nobleman (d. 1699)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Abingdon\" title=\"<PERSON>, 1st Earl of Abingdon\"><PERSON>, 1st Earl of Abingdon</a>, English nobleman (d. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Abingdon\" title=\"<PERSON>, 1st Earl of Abingdon\"><PERSON>, 1st Earl of Abingdon</a>, English nobleman (d. 1699)", "links": [{"title": "<PERSON>, 1st Earl of Abingdon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Abingdon"}]}, {"year": "1713", "text": "<PERSON><PERSON><PERSON>, American farmer, lawyer, and politician, 1st Governor of New Hampshire (d. 1786)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/Meshe<PERSON>_Weare\" title=\"Meshech Weare\"><PERSON><PERSON><PERSON></a>, American farmer, lawyer, and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>she<PERSON>_<PERSON>are\" title=\"Meshech Weare\"><PERSON><PERSON><PERSON></a>, American farmer, lawyer, and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 1786)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>are"}, {"title": "Governor of New Hampshire", "link": "https://wikipedia.org/wiki/Governor_of_New_Hampshire"}]}, {"year": "1723", "text": "<PERSON>, Scottish philosopher and economist (d. 1790)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish philosopher and economist (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish philosopher and economist (d. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1738", "text": "<PERSON>, American publisher (d. 1816)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American publisher (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American publisher (d. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON><PERSON><PERSON>, Russian poet (d. 1800)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/Salawa<PERSON>_<PERSON>ev\" title=\"Salawa<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salawa<PERSON>_<PERSON>ev\" title=\"Sal<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet (d. 1800)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, English painter and engraver (d. 1882)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter and engraver (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter and engraver (d. 1882)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_(painter)"}]}, {"year": "1801", "text": "<PERSON>, German mathematician and physicist (d. 1868)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>l%C3%BCcker\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCcker\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julius_Pl%C3%BCcker"}]}, {"year": "1806", "text": "<PERSON>, English physician and chemist (d. 1885)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and chemist (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and chemist (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, German archaeologist and philologist (d. 1869)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist and philologist (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist and philologist (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON><PERSON><PERSON>, Dutch-French preacher and theologian (d. 1875)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/Athana<PERSON>_<PERSON><PERSON>%C3%A9_Coquerel\" title=\"Athana<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-French preacher and theologian (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Athana<PERSON>_<PERSON><PERSON>%C3%A9_Coquerel\" title=\"<PERSON>hana<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-French preacher and theologian (d. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Athana<PERSON>_Josu%C3%A9_Coquerel"}]}, {"year": "1821", "text": "<PERSON> <PERSON>, Scottish golfer and architect (d. 1908)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Old_Tom_Morris\" title=\"Old Tom Morris\">Old <PERSON></a>, Scottish golfer and architect (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Old_Tom_Morris\" title=\"Old Tom Morris\">Old <PERSON></a>, Scottish golfer and architect (d. 1908)", "links": [{"title": "Old <PERSON>", "link": "https://wikipedia.org/wiki/Old_<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON><PERSON><PERSON>, Austrian geologist and botanist (d. 1897)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_von_<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>\">Con<PERSON><PERSON> <PERSON></a>, Austrian geologist and botanist (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>\">Con<PERSON><PERSON> <PERSON></a>, Austrian geologist and botanist (d. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantin_<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, American general and politician, Military Governor of the Philippines (d. 1910)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Philippines\" title=\"Governor-General of the Philippines\">Military Governor of the Philippines</a> (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Philippines\" title=\"Governor-General of the Philippines\">Military Governor of the Philippines</a> (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor-General of the Philippines", "link": "https://wikipedia.org/wiki/Governor-General_of_the_Philippines"}]}, {"year": "1837", "text": "<PERSON>, German philosopher and academic (d. 1885)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, English organist, composer, and conductor (d. 1901)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist, composer, and conductor (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist, composer, and conductor (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American lieutenant and politician, 7th Governor of Minnesota (d. 1900)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Cush<PERSON>llo<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American lieutenant and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a> (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Cush<PERSON> Kello<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American lieutenant and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a> (d. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Minnesota", "link": "https://wikipedia.org/wiki/Governor_of_Minnesota"}]}, {"year": "1840", "text": "<PERSON>, German engineer and author (d. 1913)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and author (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and author (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, German chemist and academic (d. 1919)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, German chemist and academic (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, German chemist and academic (d. 1919)", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/Max_Delbr%C3%<PERSON><PERSON>_(chemist)"}]}, {"year": "1850", "text": "<PERSON> academic and agriculturist (d. 1932)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American academic and agriculturist (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American academic and agriculturist (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Austrian-Hungarian general (d. 1935)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>u%C3%9Fenburg\" title=\"<PERSON> Straußenburg\"><PERSON></a>, Austrian-Hungarian general (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%9Fenburg\" title=\"<PERSON> Straußenburg\"><PERSON></a>, Austrian-Hungarian general (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>%C3%9Fenburg"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON> of Sweden (d. 1950)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Gustaf_V_of_Sweden\" class=\"mw-redirect\" title=\"Gustaf V of Sweden\">Gustaf V of Sweden</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gustaf_V_of_Sweden\" class=\"mw-redirect\" title=\"Gustaf V of Sweden\">Gustaf V of Sweden</a> (d. 1950)", "links": [{"title": "Gustaf V of Sweden", "link": "https://wikipedia.org/wiki/Gustaf_V_of_Sweden"}]}, {"year": "1863", "text": "<PERSON>, Mexican politician and diplomat (d. 1939)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Francisco_Le%C3%B3n_de_la_Barra\" title=\"Francisco León de <PERSON> Barra\"><PERSON></a>, Mexican politician and diplomat (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Le%C3%B3n_de_la_Barra\" title=\"Francisco León de la Barra\"><PERSON></a>, Mexican politician and diplomat (d. 1939)", "links": [{"title": "<PERSON> Barra", "link": "https://wikipedia.org/wiki/Francisco_Le%C3%B3n_de_la_Barra"}]}, {"year": "1866", "text": "<PERSON><PERSON>, Greek-Austrian metropolitan (d. 1935)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gel<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-Austrian metropolitan (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-Austrian metropolitan (d. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Karavangelis"}]}, {"year": "1867", "text": "<PERSON>, Provençal painter (d. 1952)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Provençal painter (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Provençal painter (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Canadian lawyer and politician, 9th Prime Minister of Canada (d. 1960)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1880", "text": "<PERSON>, Austrian-American chemist and author (d. 1963)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American chemist and author (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American chemist and author (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Otto_E<PERSON>nschiml"}]}, {"year": "1882", "text": "<PERSON>, Iranian educator and politician, 60th Prime Minister of Iran (d. 1967)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian educator and politician, 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iran\" title=\"Prime Minister of Iran\">Prime Minister of Iran</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian educator and politician, 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iran\" title=\"Prime Minister of Iran\">Prime Minister of Iran</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Iran", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iran"}]}, {"year": "1885", "text": "<PERSON>, Estonian-Polish architect (d. 1941)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Polish architect (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Polish architect (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Russian physicist and mathematician (d. 1925)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and mathematician (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and mathematician (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American mathematician and astronomer (d. 1980)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and astronomer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and astronomer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, English actor and comedian (d. 1965)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American author and screenwriter (d. 1976)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Murray_Leinster\" title=\"Murray Leinster\"><PERSON></a>, American author and screenwriter (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murray_Leinster\" title=\"Murray Leinster\"><PERSON></a>, American author and screenwriter (d. 1976)", "links": [{"title": "Murray Leinster", "link": "https://wikipedia.org/wiki/Murray_Leinster"}]}, {"year": "1897", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 1987)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ittig"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1899", "text": "<PERSON>, American operatic soprano (d. 1972)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic soprano (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic soprano (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American geneticist and academic, Nobel Prize laureate (d. 1992)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1902", "text": "<PERSON>, American paleontologist and author (d. 1984)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist and author (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist and author (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Australian cricketer (d. 1955)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American actor (d. 1981)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American ecologist and zoologist (d. 1987)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ecologist and zoologist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ecologist and zoologist (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Peruvian general and politician, 1st President of Peru (d. 1977)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1912", "text": "<PERSON>, Canadian illustrator (d. 2004)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian illustrator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian illustrator (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, English soldier and politician, Secretary of State for Health (d. 1998)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Secretary of State for Health", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Health"}]}, {"year": "1914", "text": "<PERSON>, American pianist and teacher (d. 2020)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and teacher (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and teacher (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American mathematician and academic (d. 2000)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, German glass chemist (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Marga_Faul<PERSON>h\" title=\"<PERSON>ga Faulstich\"><PERSON><PERSON></a>, German glass chemist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marga_<PERSON>aul<PERSON>h\" title=\"Marga Faulstich\"><PERSON><PERSON></a>, German glass chemist (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marga_Faul<PERSON>h"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Greek general and politician, President of Greece (d. 1999)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Phaedon_Gizikis\" title=\"Phaedon Gizikis\">Phaedon Gizikis</a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Phaedon_Gizikis\" title=\"Phaedon Gizikis\">Phaedon Gizikis</a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 1999)", "links": [{"title": "Phaedon <PERSON>", "link": "https://wikipedia.org/wiki/Phaedon_Gizikis"}, {"title": "President of Greece", "link": "https://wikipedia.org/wiki/President_of_Greece"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American publisher (d. 2001)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American publisher (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American publisher (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Italian automobile and aircraft engine designer (d. 1989)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>rel<PERSON>_<PERSON>redi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian automobile and aircraft engine designer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rel<PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian automobile and aircraft engine designer (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurel<PERSON>_Lampredi"}]}, {"year": "1917", "text": "<PERSON>, American photographer (d. 2009)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Irving Penn\"><PERSON></a>, American photographer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Irving Penn\"><PERSON></a>, American photographer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Swiss-American author (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Canadian chemist and academic (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian chemist and academic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian chemist and academic (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Mexican lawyer and politician, 31st President of Mexico (d. 2004)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_L%C3%B3pez_Portillo\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_L%C3%B3pez_Portillo\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_L%C3%B3pez_Portillo"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Indian singer and music director (d. 1989)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer and music director (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer and music director (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Estonian basketball player and coach (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player and coach (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Scottish race car driver (d. 1962)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Scottish race car driver (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Scottish race car driver (d. 1962)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1923", "text": "<PERSON>, Polish architect, participant in the Warsaw Uprising (d. 2023)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish architect, participant in the Warsaw Uprising (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish architect, participant in the Warsaw Uprising (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress (d. 1999)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Faith_Domergue\" title=\"<PERSON> Domergue\"><PERSON></a>, American actress (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Faith_Domergue\" title=\"<PERSON> Domergue\"><PERSON></a>, American actress (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Faith_Domergue"}]}, {"year": "1925", "text": "<PERSON>, French journalist and author (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ormesson\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ormesson\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27O<PERSON><PERSON>on"}]}, {"year": "1925", "text": "<PERSON>, Austrian-Portuguese painter and director (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Portuguese painter and director (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Portuguese painter and director (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Guatemalan general and politician, 26th President of Guatemala (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Efra%C3%ADn_R%C3%ADos_Montt\" title=\"<PERSON>fraín <PERSON>t\"><PERSON><PERSON><PERSON></a>, Guatemalan general and politician, 26th <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Efra%C3%ADn_R%C3%ADos_Montt\" title=\"Efraín <PERSON>s Montt\"><PERSON><PERSON><PERSON></a>, Guatemalan general and politician, 26th <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Efra%C3%ADn_R%C3%ADos_Montt"}, {"title": "President of Guatemala", "link": "https://wikipedia.org/wiki/President_of_Guatemala"}]}, {"year": "1927", "text": "<PERSON>, English cricketer and sportscaster (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli footballer (d. 2006)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Ya%27<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>'<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli footballer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya%27<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>'<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli footballer (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya%27<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1927", "text": "<PERSON>, German author and screenwriter (d. 2001)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and screenwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and screenwriter (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Brazilian author and playwright (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Ariano_Suassuna\" title=\"Ariano Suassuna\"><PERSON><PERSON></a>, Brazilian author and playwright (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ariano_Suassuna\" title=\"Ariano Suassuna\"><PERSON><PERSON></a>, Brazilian author and playwright (d. 2014)", "links": [{"title": "Ariano <PERSON>", "link": "https://wikipedia.org/wiki/Ariano_Suassuna"}]}, {"year": "1929", "text": "Sabah Al-Ahmad <PERSON>, Emir of Kuwait (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Sabah_Al-Ahmad_Al-Jaber_Al-Sabah\" title=\"Sabah Al-Ahmad Al-Jaber Al-Sabah\"><PERSON> Al<PERSON>Ahmad Al-Jaber Al-Sabah</a>, Emir of Kuwait (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sabah_Al-Ahmad_Al-Jaber_Al-Sabah\" title=\"Sabah Al-Ahmad Al-Jaber Al-Sabah\"><PERSON>Ahmad <PERSON>Jaber Al-Sabah</a>, Emir of Kuwait (d. 2020)", "links": [{"title": "Sabah Al-Ahmad Al-Jaber Al-Sabah", "link": "https://wikipedia.org/wiki/Sabah_Al-Ahmad_<PERSON>-Jaber_Al-Sabah"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Hungarian-American cinematographer and producer (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American cinematographer and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American cinematographer and producer (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vil<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actress and screenwriter", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actor (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2003)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American painter and illustrator", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Bulgarian politician, 48th Prime Minister of Bulgaria", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Simeon_Saxe-Coburg-Gotha\" title=\"Simeon Saxe-Coburg-Gotha\"><PERSON><PERSON><PERSON>-Coburg-Gotha</a>, Bulgarian politician, 48th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria\" title=\"Prime Minister of Bulgaria\">Prime Minister of Bulgaria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Simeon_Saxe-Coburg-Gotha\" title=\"Simeon Saxe-Coburg-Gotha\"><PERSON><PERSON><PERSON> Sa<PERSON>-Coburg-Gotha</a>, Bulgarian politician, 48th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria\" title=\"Prime Minister of Bulgaria\">Prime Minister of Bulgaria</a>", "links": [{"title": "Simeon Saxe-Coburg-Gotha", "link": "https://wikipedia.org/wiki/Simeon_Saxe-Coburg-Gotha"}, {"title": "Prime Minister of Bulgaria", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria"}]}, {"year": "1937", "text": "<PERSON>, American author and screenwriter (d. 2010)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English general", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English general", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Swedish author and poet (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and poet (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and poet (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American novelist, short story writer, critic, and poet", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, critic, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, critic, and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON> \"<PERSON>\" <PERSON>, American singer-songwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Crash%22_Craddock\" title='<PERSON> \"Crash\" Craddock'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Crash%22_Craddock\" title='<PERSON> \"Crash\" Craddock'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/Billy_%22Crash%22_<PERSON><PERSON>dock"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Latvian poet, writer, and journalist (d. 2003)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/M%C4%81ris_%C4%8Caklais\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian poet, writer, and journalist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C4%81ris_%C4%8Caklais\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian poet, writer, and journalist (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C4%81ris_%C4%8Caklais"}]}, {"year": "1940", "text": "<PERSON>, American lawyer and politician, 33rd Governor of Oregon (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1940", "text": "<PERSON>, British actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American songwriter and producer (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Lamont_Dozier\" title=\"Lamont Dozier\"><PERSON><PERSON></a>, American songwriter and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lamont_Dozier\" title=\"Lamont Dozier\"><PERSON><PERSON></a>, American songwriter and producer (d. 2022)", "links": [{"title": "Lamont Dozier", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zier"}]}, {"year": "1941", "text": "<PERSON>, English golfer (d. 2017)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Pakistani journalist (d. 2011)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani journalist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani journalist (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Italian motorcycle racer and manager", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American R&B/soul singer-songwriter, musician, and actor", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B/soul singer-songwriter, musician, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B/soul singer-songwriter, musician, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, French painter and etcher (d. 2020)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and etcher (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and etcher (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Canadian social worker and politician, 59th Secretary of State for Canada", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian social worker and politician, 59th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian social worker and politician, 59th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Secretary of State for Canada", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Canada"}]}, {"year": "1946", "text": "<PERSON>, American basketball player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, 3rd Baron <PERSON> of Hever, English businessman and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>_of_Hever\" title=\"<PERSON>, 3rd Baron <PERSON> of Hever\"><PERSON>, 3rd Baron <PERSON> of Hever</a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>_of_Hever\" title=\"<PERSON>, 3rd Baron <PERSON> of Hever\"><PERSON>, 3rd Baron <PERSON> of Hever</a>, English businessman and politician", "links": [{"title": "<PERSON>, 3rd Baron <PERSON> of Hever", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>_of_Hever"}]}, {"year": "1946", "text": "<PERSON>, English statistician and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statistician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statistician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American trumpet player and composer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Scottish historian and curator", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and curator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and curator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American politician, 87th Governor of Connecticut (d. 2024)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician, 87th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician, 87th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll"}, {"title": "Governor of Connecticut", "link": "https://wikipedia.org/wiki/Governor_of_Connecticut"}]}, {"year": "1946", "text": "<PERSON>, American actor, puppeteer, and producer (d. 2009)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, puppeteer, and producer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, puppeteer, and producer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actor and playwright", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and playwright", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1947", "text": "<PERSON>, American football player and actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Al_Cowlings\" title=\"Al Cowlings\"><PERSON></a>, American football player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Cowlings\" title=\"Al Cowlings\"><PERSON></a>, American football player and actor", "links": [{"title": "Al Cowlings", "link": "https://wikipedia.org/wiki/Al_Cowlings"}]}, {"year": "1947", "text": "<PERSON>, American trombonist, composer, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trombonist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trombonist, composer, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1947", "text": "<PERSON>, American wrestler (d. 2012)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American baseball player and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_1949)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1949)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_1949)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1949)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1949)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_1949)"}]}, {"year": "1949", "text": "<PERSON>, American hurdler and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Indian actor and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ty"}]}, {"year": "1950", "text": "<PERSON>, Canadian lawyer and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American politician and farmer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and farmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and farmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer and guitarist (d. 2023)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Panamanian boxer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Panamanian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Panamanian boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n"}]}, {"year": "1952", "text": "<PERSON>, Greek sociologist and politician, 182nd Prime Minister of Greece", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek sociologist and politician, 182nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek sociologist and politician, 182nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Canadian singer-songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English drummer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American boxer and trainer (d. 2014)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Irish guitarist (d. 2022)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish guitarist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish guitarist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Norwegian politician, Norwegian Minister of Defence", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Norway)\" title=\"Minister of Defence (Norway)\">Norwegian Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Norway)\" title=\"Minister of Defence (Norway)\">Norwegian Minister of Defence</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Faremo"}, {"title": "Minister of Defence (Norway)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Norway)"}]}, {"year": "1955", "text": "<PERSON>, American actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Russian journalist and critic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>tsky\" title=\"<PERSON><PERSON> T<PERSON>tsky\"><PERSON><PERSON></a>, Russian journalist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>tsky\" title=\"<PERSON><PERSON> T<PERSON>tsky\"><PERSON><PERSON></a>, Russian journalist and critic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Scottish-American actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, <PERSON>, Scottish lawyer and judge", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>, <PERSON>\"><PERSON><PERSON>, <PERSON></a>, Scottish lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>, <PERSON>\"><PERSON><PERSON>, <PERSON></a>, Scottish lawyer and judge", "links": [{"title": "<PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American basketball player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, German swimmer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German swimmer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Australian soldier, educator and musician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian soldier, educator and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian soldier, educator and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "The Ultimate Warrior, American wrestler (d. 2014)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/The_Ultimate_Warrior\" title=\"The Ultimate Warrior\">The Ultimate Warrior</a>, American wrestler (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Ultimate_Warrior\" title=\"The Ultimate Warrior\">The Ultimate Warrior</a>, American wrestler (d. 2014)", "links": [{"title": "The Ultimate Warrior", "link": "https://wikipedia.org/wiki/The_Ultimate_Warrior"}]}, {"year": "1960", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1961", "text": "<PERSON>, Turkish journalist and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Can_D%C3%BCndar\" title=\"Can Dündar\"><PERSON></a>, Turkish journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Can_D%C3%BCndar\" title=\"Can Dündar\"><PERSON></a>, Turkish journalist and author", "links": [{"title": "Can Dündar", "link": "https://wikipedia.org/wiki/Can_D%C3%BCndar"}]}, {"year": "1961", "text": "<PERSON>, Australian cricketer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (Australian cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(Australian_cricketer)"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Estonian basketball player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Margus_Metstak\" title=\"Margus Metstak\"><PERSON><PERSON></a>, Estonian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Margus_Metstak\" title=\"Margus Metstak\"><PERSON><PERSON></a>, Estonian basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Margus_Metstak"}]}, {"year": "1962", "text": "<PERSON>, American baseball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, South African-American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Hong Kong singer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, Hong Kong singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, Hong Kong singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1963", "text": "<PERSON>, American wrestler", "html": "1963 - <a href=\"https://wikipedia.org/wiki/The_Sandman_(wrestler)\" title=\"The Sandman (wrestler)\"><PERSON> Sandman</a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Sandman_(wrestler)\" title=\"The Sandman (wrestler)\"><PERSON> Sandman</a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(wrestler)"}]}, {"year": "1964", "text": "<PERSON>, American actor and singer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Irish computer scientist and entrepreneur; co-founded HP Autonomy", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish computer scientist and entrepreneur; co-founded <a href=\"https://wikipedia.org/wiki/HP_Autonomy\" title=\"HP Autonomy\">HP Autonomy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish computer scientist and entrepreneur; co-founded <a href=\"https://wikipedia.org/wiki/HP_Autonomy\" title=\"HP Autonomy\">HP Autonomy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "HP Autonomy", "link": "https://wikipedia.org/wiki/HP_Autonomy"}]}, {"year": "1965", "text": "<PERSON>, American politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian surfer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, French rugby player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American voice actor, director, producer, and screenwriter, co-created VeggieTales", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, director, producer, and screenwriter, co-created <i><a href=\"https://wikipedia.org/wiki/VeggieTales\" title=\"VeggieTales\">VeggieTales</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, director, producer, and screenwriter, co-created <i><a href=\"https://wikipedia.org/wiki/VeggieTales\" title=\"VeggieTales\">VeggieTales</a></i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_Vischer"}, {"title": "VeggieTales", "link": "https://wikipedia.org/wiki/VeggieTales"}]}, {"year": "1966", "text": "<PERSON>, Czech javelin thrower and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Jan_%C5%BDelezn%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech javelin thrower and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_%C5%BDelezn%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech javelin thrower and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_%C5%BDelezn%C3%BD"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, Cypriot footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter, musician, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, English lawyer and academic", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English lawyer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English lawyer and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English-Welsh footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Pakistani poet and academic, co-founded Messiah Foundation International", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_AlG<PERSON>ar\" title=\"<PERSON>nus AlGohar\"><PERSON><PERSON></a>, Pakistani poet and academic, co-founded <a href=\"https://wikipedia.org/wiki/Messiah_Foundation_International\" title=\"Messiah Foundation International\">Messiah Foundation International</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Al<PERSON>ar\" title=\"<PERSON>nus AlGohar\"><PERSON><PERSON></a>, Pakistani poet and academic, co-founded <a href=\"https://wikipedia.org/wiki/Messiah_Foundation_International\" title=\"Messiah Foundation International\">Messiah Foundation International</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Al<PERSON>"}, {"title": "Messiah Foundation International", "link": "https://wikipedia.org/wiki/Messiah_Foundation_International"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American actor", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1970", "text": "<PERSON><PERSON>, American soccer player and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American golfer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American rapper and producer (d. 1996)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ur\"><PERSON><PERSON><PERSON></a>, American rapper and producer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ur\"><PERSON><PERSON><PERSON></a>, American rapper and producer (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ur"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Brazilian guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Saint Lucian cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Saint Lucian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Saint Lucian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, British-born Canadian-American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-born Canadian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-born Canadian-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "1975", "text": "<PERSON>, American basketball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1977", "text": "<PERSON>, Australian rugby league player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English accountant and politician", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Spanish-German actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%<PERSON>hl\" title=\"<PERSON>\"><PERSON></a>, Spanish-German actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%<PERSON>hl\" title=\"<PERSON>\"><PERSON></a>, Spanish-German actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Br%C3%BChl"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Lithuanian ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Zubrus\" title=\"<PERSON><PERSON> Zubrus\"><PERSON><PERSON></a>, Lithuanian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>nius_Zubrus\" title=\"Dainius Zubrus\"><PERSON><PERSON></a>, Lithuanian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dainius_Zubrus"}]}, {"year": "1978", "text": "<PERSON>, Malaysian singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Leong\" title=\"Fish Leong\"><PERSON></a>, Malaysian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Leong\" title=\"Fish Leong\"><PERSON></a>, Malaysian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German-English rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, New Zealand rugby league player and referee", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Austrian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Hong Kong singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, German tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Norwegian violinist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian violinist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Peruvian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Danish model and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Canadian model and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Peregrym\" title=\"<PERSON><PERSON>egrym\"><PERSON><PERSON></a>, Canadian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>egrym\" title=\"<PERSON><PERSON>rym\"><PERSON><PERSON></a>, Canadian model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Missy_Peregrym"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Albanian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Armend_Dallku\" title=\"Armend Dallku\"><PERSON><PERSON></a>, Albanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armend_Dallku\" title=\"Armend Dallku\"><PERSON><PERSON></a>, Albanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Armend_Dallku"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American writer and entertainer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and entertainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Scottish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Brazilian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Uruguayan footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American singer-songwriter and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON> <PERSON><PERSON><PERSON>, Norwegian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON> <PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON> <PERSON><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Belgian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_<PERSON><PERSON>_Kabeya"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Canadian singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Keshia_Chant%C3%A9"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>am\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>am\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Odion_Ighalo\" title=\"Odion Ighalo\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Odion_Ighalo\" title=\"Odion Ighalo\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odion_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English musician, singer, songwriter and record producer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English musician, singer, songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English musician, singer, songwriter and record producer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1991", "text": "<PERSON>, English singer-songwriter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, South African rugby player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, German politician", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Maik_Br%C3%BCckner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mai<PERSON>_Br%C3%<PERSON><PERSON>ner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maik_Br%C3%<PERSON><PERSON>ner"}]}, {"year": "1992", "text": "<PERSON>, Russian swimmer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Russian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Russian swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1993", "text": "<PERSON>, South Korean actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Park_Bo-gum\" title=\"Park Bo-gum\"><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Park_Bo-gum\" title=\"Park Bo-gum\"><PERSON></a>, South Korean actor", "links": [{"title": "Park Bo-gum", "link": "https://wikipedia.org/wiki/Park_Bo-gum"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, American singer, songwriter, rapper, DJ and record producer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, American singer, songwriter, rapper, DJ and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, American singer, songwriter, rapper, DJ and record producer", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Estonian footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Grete-Lilijane_K%C3%BCppas\" title=\"<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grete-Lilijane_K%C3%BCppas\" title=\"<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "Grete-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Grete-Lilijane_K%C3%BCppas"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Albanian wrestler", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, New Zealand rugby Union player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby Union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby Union player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Singaporean swimmer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, South Korean singer-songwriter and actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyeon\" title=\"<PERSON>hyeon\"><PERSON>ye<PERSON></a>, South Korean singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyeon\" title=\"<PERSON>hyeon\"><PERSON>ye<PERSON></a>, South Korean singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>on"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Indian tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Malian footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n%C3%A9_(footballer,_born_1999)\" title=\"<PERSON><PERSON> (footballer, born 1999)\"><PERSON><PERSON></a>, Malian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n%C3%A9_(footballer,_born_1999)\" title=\"<PERSON><PERSON> (footballer, born 1999)\"><PERSON><PERSON></a>, Malian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1999)", "link": "https://wikipedia.org/wiki/Ibrahim<PERSON>_Kon%C3%A9_(footballer,_born_1999)"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> <PERSON> (musician)\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> <PERSON> (musician)\"><PERSON><PERSON> <PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)"}]}, {"year": "2000", "text": "<PERSON>, Canadian tennis player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, English-Australian rugby league player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, English-Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, English-Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "2003", "text": "<PERSON>, Canadian actress", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "840", "text": "<PERSON><PERSON><PERSON>, Frankish nobleman (or 839)", "html": "840 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Maine\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Maine\">Rorgon I</a>, Frankish nobleman (or <a href=\"https://wikipedia.org/wiki/839\" title=\"839\">839</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Maine\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Maine\">Rorgon I</a>, Frankish nobleman (or <a href=\"https://wikipedia.org/wiki/839\" title=\"839\">839</a>)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Maine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Maine"}, {"title": "839", "link": "https://wikipedia.org/wiki/839"}]}, {"year": "924", "text": "<PERSON>, general of Later Tang (b. 862)", "html": "924 - <a href=\"https://wikipedia.org/wiki/Li_<PERSON>\" title=\"Li Cunshen\"><PERSON></a>, general of Later Tang (b. 862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_C<PERSON>\" title=\"Li Cunshen\"><PERSON></a>, general of Later Tang (b. 862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_<PERSON>hen"}]}, {"year": "956", "text": "<PERSON> the <PERSON>, Frankish nobleman (b. 898)", "html": "956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the <PERSON></a>, Frankish nobleman (b. 898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Great\"><PERSON> the <PERSON></a>, Frankish nobleman (b. 898)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}]}, {"year": "1185", "text": "<PERSON><PERSON><PERSON> of Poland, queen of León (b. c. 1140)", "html": "1185 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Poland,_Queen_of_Castile\" title=\"<PERSON><PERSON><PERSON> of Poland, Queen of Castile\"><PERSON><PERSON><PERSON> of Poland</a>, queen of León (b. c. 1140)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Poland,_Queen_of_Castile\" title=\"<PERSON><PERSON><PERSON> of Poland, Queen of Castile\"><PERSON><PERSON><PERSON> of Poland</a>, queen of León (b. c. 1140)", "links": [{"title": "<PERSON><PERSON><PERSON> of Poland, Queen of Castile", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Poland,_Queen_of_Castile"}]}, {"year": "1286", "text": "<PERSON>, English bishop", "html": "1286 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1332", "text": "<PERSON>, founder of Oriel College, Oxford", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of <a href=\"https://wikipedia.org/wiki/Oriel_College,_Oxford\" title=\"Oriel College, Oxford\">Oriel College, Oxford</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of <a href=\"https://wikipedia.org/wiki/Oriel_College,_Oxford\" title=\"Oriel College, Oxford\">Oriel College, Oxford</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Oriel College, Oxford", "link": "https://wikipedia.org/wiki/Oriel_College,_Oxford"}]}, {"year": "1361", "text": "<PERSON>, German mystic theologian", "html": "1361 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mystic theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mystic theologian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1397", "text": "<PERSON> Artois, Count of Eu, French soldier (b. 1358)", "html": "1397 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Eu\" title=\"<PERSON> Artois, Count of Eu\"><PERSON> Artois, Count of Eu</a>, French soldier (b. 1358)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Eu\" title=\"<PERSON>ois, Count of Eu\"><PERSON> Artois, Count of Eu</a>, French soldier (b. 1358)", "links": [{"title": "<PERSON> Artois, Count of Eu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_<PERSON><PERSON>"}]}, {"year": "1424", "text": "<PERSON>, archbishop of Riga", "html": "1424 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, archbishop of Riga", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, archbishop of Riga", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Johannes_<PERSON>i"}]}, {"year": "1468", "text": "<PERSON>, Burgundian historian and author (b. 1395)", "html": "1468 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8<PERSON>_de_Saint-Remy\" title=\"<PERSON> de Saint-Remy\"><PERSON> Saint-Remy</a>, Burgundian historian and author (b. 1395)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8vre_de_Saint-Remy\" title=\"<PERSON> de Saint-Remy\"><PERSON> Saint-Remy</a>, Burgundian historian and author (b. 1395)", "links": [{"title": "<PERSON> Saint-Remy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8<PERSON>_de_<PERSON>-<PERSON>my"}]}, {"year": "1487", "text": "<PERSON>, 1st Earl of Lincoln (b. c. 1463)", "html": "1487 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Lincoln\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl of Lincoln\"><PERSON>, 1st Earl of Lincoln</a> (b. c. 1463)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Lincoln\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl <PERSON> Lincoln\"><PERSON>, 1st Earl of Lincoln</a> (b. c. 1463)", "links": [{"title": "<PERSON>, 1st Earl of Lincoln", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Lincoln"}]}, {"year": "1540", "text": "<PERSON>, German nobleman (b. c. 1466)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>h%C3%BCngen\" title=\"<PERSON>\"><PERSON></a>, German nobleman (b. c. 1466)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Th%C3%BCngen\" title=\"<PERSON>\"><PERSON></a>, German nobleman (b. c. 1466)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>h%C3%BCngen"}]}, {"year": "1622", "text": "<PERSON>, 1st Earl of Dunfermline, Scottish lawyer, judge, and politician, Lord Chancellor of Scotland (b. 1555)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Dunfermline\" title=\"<PERSON>, 1st Earl of Dunfermline\"><PERSON>, 1st Earl of Dunfermline</a>, Scottish lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland\" title=\"Lord Chancellor of Scotland\">Lord Chancellor of Scotland</a> (b. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Dunfermline\" title=\"<PERSON>, 1st Earl of Dunfermline\"><PERSON>, 1st Earl of Dunfermline</a>, Scottish lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland\" title=\"Lord Chancellor of Scotland\">Lord Chancellor of Scotland</a> (b. 1555)", "links": [{"title": "<PERSON>, 1st Earl of Dunfermline", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Dunfermline"}, {"title": "Lord Chancellor of Scotland", "link": "https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland"}]}, {"year": "1626", "text": "<PERSON>, Duke of Brunswick-Lüneburg-<PERSON><PERSON><PERSON><PERSON>, German Protestant military leader (b. 1599)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/Christian_the_Younger\" class=\"mw-redirect\" title=\"Christian the Younger\"><PERSON>, Duke of Brunswick-Lüneburg-Wolfen<PERSON>ü<PERSON></a>, German Protestant military leader (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_the_Younger\" class=\"mw-redirect\" title=\"Christian the Younger\"><PERSON>, Duke of Brunswick-Lüneburg-Wolfen<PERSON></a>, German Protestant military leader (b. 1599)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_the_Younger"}]}, {"year": "1666", "text": "Sir <PERSON>, 1st Baronet, English poet and diplomat, English Ambassador to Spain (b. 1608)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English poet and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_Spain\" title=\"List of ambassadors of the United Kingdom to Spain\">English Ambassador to Spain</a> (b. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English poet and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_Spain\" title=\"List of ambassadors of the United Kingdom to Spain\">English Ambassador to Spain</a> (b. 1608)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet"}, {"title": "List of ambassadors of the United Kingdom to Spain", "link": "https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_Spain"}]}, {"year": "1674", "text": "<PERSON><PERSON>, Spanish painter (b. 1595 or 1600)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Yepes\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish painter (b. 1595 or 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Yepes\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish painter (b. 1595 or 1600)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Yepes"}]}, {"year": "1722", "text": "<PERSON>, 1st Duke of Marlborough, English general and politician, Lord Lieutenant of Oxfordshire (b. 1650)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Marlborough\" title=\"<PERSON>, 1st Duke of Marlborough\"><PERSON>, 1st Duke of Marlborough</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Oxfordshire\" title=\"Lord Lieutenant of Oxfordshire\">Lord Lieutenant of Oxfordshire</a> (b. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Marlborough\" title=\"<PERSON>, 1st Duke of Marlborough\"><PERSON>, 1st Duke of Marlborough</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Oxfordshire\" title=\"Lord Lieutenant of Oxfordshire\">Lord Lieutenant of Oxfordshire</a> (b. 1650)", "links": [{"title": "<PERSON>, 1st Duke of Marlborough", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Marlborough"}, {"title": "Lord Lieutenant of Oxfordshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Oxfordshire"}]}, {"year": "1743", "text": "<PERSON><PERSON><PERSON><PERSON>, eldest daughter of King <PERSON> of France (b. 1673)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, eldest daughter of King <a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"<PERSON> XIV of France\"><PERSON> of France</a> (b. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, eldest daughter of King <a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a> (b. 1673)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A7<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XIV_of_France"}]}, {"year": "1752", "text": "<PERSON>, English bishop and philosopher (b. 1692)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and philosopher (b. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and philosopher (b. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, Countess of Jersey (formerly Duchess of Bedford) (b. c.1705)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Bedford\" title=\"<PERSON>, Duchess of Bedford\"><PERSON>, Countess of Jersey (formerly Duchess of Bedford)</a> (b. c.1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Bedford\" title=\"<PERSON>, Duchess of Bedford\"><PERSON>, Countess of Jersey (formerly Duchess of Bedford)</a> (b. c.1705)", "links": [{"title": "<PERSON>, Duchess of Bedford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Bedford"}]}, {"year": "1777", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French poet and playwright (b. 1709)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French poet and playwright (b. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French poet and playwright (b. 1709)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1779", "text": "Sir <PERSON>, 1st Baronet, English lawyer and politician, Governor of the Province of Massachusetts Bay (b. 1712)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts\" title=\"List of colonial governors of Massachusetts\">Governor of the Province of Massachusetts Bay</a> (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts\" title=\"List of colonial governors of Massachusetts\">Governor of the Province of Massachusetts Bay</a> (b. 1712)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}, {"title": "List of colonial governors of Massachusetts", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts"}]}, {"year": "1804", "text": "<PERSON>, German composer and conductor (b. 1728)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON><PERSON><PERSON>, duc <PERSON>, French lawyer and politician (b. 1739)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>,_duc_de_<PERSON>laisance\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, duc de Plaisance\"><PERSON><PERSON><PERSON>, duc de <PERSON></a>, French lawyer and politician (b. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>,_duc_de_<PERSON>sance\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, duc de <PERSON><PERSON>sance\"><PERSON><PERSON><PERSON>, duc de <PERSON></a>, French lawyer and politician (b. 1739)", "links": [{"title": "<PERSON><PERSON><PERSON>, duc de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>,_duc_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1849", "text": "<PERSON>, German theologian and scholar (b. 1780)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and scholar (b. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and scholar (b. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, English-Australian explorer and politician (b. 1774)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English-Australian explorer and politician (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English-Australian explorer and politician (b. 1774)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_(explorer)"}]}, {"year": "1858", "text": "<PERSON>, English epidemiologist and physician (b. 1813)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English epidemiologist and physician (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English epidemiologist and physician (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 9th <PERSON><PERSON><PERSON><PERSON> (b. 1808)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Hidenoyama_Raigor%C5%8D\" title=\"Hidenoyama Raigorō\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 9th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hidenoyama_Raigor%C5%8D\" title=\"Hidenoyama Raigorō\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 9th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1808)", "links": [{"title": "Hidenoyama Raigorō", "link": "https://wikipedia.org/wiki/Hidenoyama_Raigor%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1869", "text": "<PERSON>, Indian-English botanist and explorer (b. 1795)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English botanist and explorer (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, Indian-English botanist and explorer (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Scottish minister and author (b. 1812)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1812%E2%80%931872)\" class=\"mw-redirect\" title=\"<PERSON> (1812-1872)\"><PERSON></a>, Scottish minister and author (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1812%E2%80%931872)\" class=\"mw-redirect\" title=\"<PERSON> (1812-1872)\"><PERSON></a>, Scottish minister and author (b. 1812)", "links": [{"title": "<PERSON> (1812-1872)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1812%E2%80%931872)"}]}, {"year": "1878", "text": "<PERSON>, American surgeon and pharmacist (b. 1815)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and pharmacist (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and pharmacist (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Japanese painter (b. 1781)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Kikuchi_Y%C5%8Dsai\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kiku<PERSON>_Y%C5%8Dsai\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter (b. 1781)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kikuchi_Y%C5%8Dsai"}]}, {"year": "1881", "text": "<PERSON>, English businessman and philanthropist (b. 1795)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, German painter and academic (b. 1818)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Wilhelm_Camp<PERSON>\" title=\"Wilhelm Camphausen\"><PERSON></a>, German painter and academic (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilhelm_Camphausen\" title=\"Wilhelm Camphausen\"><PERSON></a>, German painter and academic (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wilhelm_Camphausen"}]}, {"year": "1886", "text": "<PERSON>, Scottish-Australian politician, 9th Premier of New South Wales (b. 1824)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Scottish-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Scottish-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1824)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_politician)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1902", "text": "<PERSON>, German mathematician and academic (b. 1841)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, German mathematician and academic (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, German mathematician and academic (b. 1841)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hr%C3%B6<PERSON>_(mathematician)"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Romanian engineer and explorer (b. 1860)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Ba<PERSON><PERSON>_<PERSON>san\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian engineer and explorer (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ba<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian engineer and explorer (b. 1860)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician (b. 1870)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (b. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, English 2nd General of The Salvation Army (b. 1856)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Bramwell Booth\"><PERSON><PERSON></a>, English 2nd <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Bramwell Booth\"><PERSON><PERSON></a>, English 2nd <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1856)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1929", "text": "<PERSON>, American historian and scholar (b. 1871)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and scholar (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and scholar (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American lawyer and businessman, co-founded Abercrombie & Fitch (b. 1866)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Abercrombie_%26_Fitch\" title=\"Abercrom<PERSON> &amp; Fitch\">Abercrombie &amp; Fitch</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Abercrombie_%26_Fitch\" title=\"Abercrom<PERSON> &amp; Fitch\">Abercrombie &amp; Fitch</a> (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Abercrombie & Fitch", "link": "https://wikipedia.org/wiki/Abercrombie_%26_Fitch"}]}, {"year": "1930", "text": "<PERSON>, American inventor, co-invented the gyrocompass (b. 1860)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, co-invented the <a href=\"https://wikipedia.org/wiki/Gyrocompass\" title=\"Gyrocompass\">gyrocompass</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, co-invented the <a href=\"https://wikipedia.org/wiki/Gyrocompass\" title=\"Gyrocompass\">gyrocompass</a> (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Gyrocompass", "link": "https://wikipedia.org/wiki/Gyrocompass"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American drummer and bandleader (b. 1905)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer and bandleader (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer and bandleader (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, American author (b. 1885)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"DuBose Heyward\"><PERSON><PERSON><PERSON></a>, American author (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Heyward\" title=\"Du<PERSON>ose Heyward\"><PERSON><PERSON><PERSON></a>, American author (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ward"}]}, {"year": "1944", "text": "<PERSON>, French historian and academic (b. 1886)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and academic (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and academic (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, wrongfully convicted African-American teenager (b. 1929)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, wrongfully convicted African-American teenager (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, wrongfully convicted African-American teenager (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Greek general (b. 1905)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Velouchiotis"}]}, {"year": "1946", "text": "<PERSON>, Irish cartoonist (b 1889)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cartoonist (b 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cartoonist (b 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Scottish-American geologist and academic (b. 1861)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American geologist and academic (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American geologist and academic (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English politician, Secretary of State for Work and Pensions (b. 1873)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Work_and_Pensions\" title=\"Secretary of State for Work and Pensions\">Secretary of State for Work and Pensions</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Work_and_Pensions\" title=\"Secretary of State for Work and Pensions\">Secretary of State for Work and Pensions</a> (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Work and Pensions", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Work_and_Pensions"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Canadian painter (b. 1864)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>uc"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Hungarian general and politician, Minister of Defence of Hungary (b. 1917)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/P%C3%A1l_Mal%C3%A9ter\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)\" class=\"mw-redirect\" title=\"Ministry of Defence (Hungary)\">Minister of Defence of Hungary</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A1l_Mal%C3%A9ter\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)\" class=\"mw-redirect\" title=\"Ministry of Defence (Hungary)\">Minister of Defence of Hungary</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A1l_Mal%C3%A9ter"}, {"title": "Ministry of Defence (Hungary)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Hungarian politician, 3rd Prime Minister of Hungary (b. 1895)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_Nagy"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1959", "text": "<PERSON>, American actor and director (b. 1914)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Swiss physician and anesthesiologist (b. 1904)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and anesthesiologist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and anesthesiologist (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English actor (b. 1891)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1891)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1969", "text": "<PERSON>, 1st Earl <PERSON> of Tunis, English field marshal and politician, 17th Governor General of Canada (b. 1891)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>_of_Tunis\" title=\"<PERSON>, 1st Earl <PERSON> of Tunis\"><PERSON>, 1st Earl <PERSON> of Tunis</a>, English field marshal and politician, 17th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>_of_Tunis\" title=\"<PERSON>, 1st Earl <PERSON> of Tunis\"><PERSON>, 1st Earl <PERSON> of Tunis</a>, English field marshal and politician, 17th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1891)", "links": [{"title": "<PERSON>, 1st Earl <PERSON> of Tunis", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>_of_Tunis"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1970", "text": "<PERSON>, English mathematician and geophysicist (b. 1888)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and geophysicist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and geophysicist (b. 1888)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)"}]}, {"year": "1970", "text": "<PERSON>, American football player (b. 1943)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, 1st Baron <PERSON>, Scottish broadcaster, co-founded BBC (b. 1889)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish broadcaster, co-founded <a href=\"https://wikipedia.org/wiki/BBC\" title=\"BBC\">BBC</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish broadcaster, co-founded <a href=\"https://wikipedia.org/wiki/BBC\" title=\"BBC\">BBC</a> (b. 1889)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "BBC", "link": "https://wikipedia.org/wiki/BBC"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Australian landscape and portrait painter (b. 1894)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian landscape and portrait painter (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian landscape and portrait painter (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON> <PERSON>, German-American physicist and engineer (b. 1912)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German-American physicist and engineer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German-American physicist and engineer (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/We<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Ghanaian general and politician, 6th Head of state of Ghana (b. 1931)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ghanaian general and politician, 6th <a href=\"https://wikipedia.org/wiki/Head_of_state_of_Ghana\" class=\"mw-redirect\" title=\"Head of state of Ghana\">Head of state of Ghana</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">I<PERSON><PERSON></a>, Ghanaian general and politician, 6th <a href=\"https://wikipedia.org/wiki/Head_of_state_of_Ghana\" class=\"mw-redirect\" title=\"Head of state of Ghana\">Head of state of Ghana</a> (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Head of state of Ghana", "link": "https://wikipedia.org/wiki/Head_of_state_of_Ghana"}]}, {"year": "1979", "text": "<PERSON>, American actor, director, and screenwriter (b. 1911)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian politician, 33rd Premier of South Australia (b. 1896)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ford IV\"><PERSON></a>, Australian politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ford IV\"><PERSON></a>, Australian politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1896)", "links": [{"title": "<PERSON> IV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1982", "text": "<PERSON>-<PERSON>, English guitarist and songwriter (b. 1956)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English guitarist and songwriter (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English guitarist and songwriter (b. 1956)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American football player and coach (b. 1895)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>w_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Estonian author and poet (b. 1900)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian author and poet (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian author and poet (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, French organist and composer (b. 1902)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1987", "text": "<PERSON>, American author and illustrator (b. 1889)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Puerto Rican-American actor and playwright (b. 1946)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1ero\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actor and playwright (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1ero\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actor and playwright (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miguel_Pi%C3%B1ero"}]}, {"year": "1993", "text": "<PERSON>, Australian cricketer and soldier (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and soldier (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and soldier (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American bass player and songwriter  (b. 1967)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American sportscaster and game show host (b. 1913)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and game show host (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and game show host (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American race car driver and engineer (b. 1918)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and engineer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and engineer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "Screaming <PERSON>, English singer and activist (b. 1940)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Screaming_Lord_Sutch\" title=\"Screaming Lord Sutch\">Screaming Lord <PERSON>tch</a>, English singer and activist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Screaming_Lord_Sutch\" title=\"Screaming Lord Sutch\">Screaming Lord Sutch</a>, English singer and activist (b. 1940)", "links": [{"title": "Screaming Lord <PERSON>", "link": "https://wikipedia.org/wiki/Screaming_Lord_<PERSON>tch"}]}, {"year": "2003", "text": "<PERSON>, Canadian journalist and politician (b. 1934)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Finnish-Swedish philosopher and author (b. 1916)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish-Swedish philosopher and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish-Swedish philosopher and author (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Thai field marshal and politician, 10th Prime Minister of Thailand (b. 1911)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai field marshal and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai field marshal and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Kit<PERSON>kachorn"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "2004", "text": "<PERSON>, Canadian lawyer and judge (b. 1911)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and judge (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and judge (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Puerto Rican-American author and critic (b. 1906)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American author and critic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American author and critic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Italian soldier and author (b. 1921)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Haitian lawyer and politician, 49th President of Haiti (b. 1932)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian lawyer and politician, 49th <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian lawyer and politician, 49th <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Haiti", "link": "https://wikipedia.org/wiki/President_of_Haiti"}]}, {"year": "2010", "text": "<PERSON>, Canadian singer and academic (b. 1930)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English director, producer, cinematographer, and screenwriter (b. 1911)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, cinematographer, and screenwriter (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, cinematographer, and screenwriter (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Swedish engineer and academic (b. 1938)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/%C3%96sten_M%C3%A4kitalo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish engineer and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96sten_M%C3%A4kitalo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish engineer and academic (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96sten_M%C3%A4kitalo"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Swedish skier (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish skier (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish skier (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Mexican banker and businessman (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican banker and businessman (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican banker and businessman (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish general (b. 1946)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/S%C5%82aw<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish general (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C5%82aw<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish general (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C5%82awomi<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American actress (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American businessman, co-founded OXO (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/OXO_(kitchen_utensils_brand)\" title=\"OXO (kitchen utensils brand)\">OXO</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/OXO_(kitchen_utensils_brand)\" title=\"OXO (kitchen utensils brand)\">OXO</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "OXO (kitchen utensils brand)", "link": "https://wikipedia.org/wiki/OXO_(kitchen_utensils_brand)"}]}, {"year": "2013", "text": "<PERSON>, Austrian biologist and diver (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian biologist and diver (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian biologist and diver (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladesh poet and academic (b. 1950)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Khondakar_<PERSON><PERSON>_<PERSON>\" title=\"Khondakar Ash<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladesh poet and academic (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Khondakar_<PERSON><PERSON>_<PERSON>\" title=\"Khondakar Ash<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladesh poet and academic (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English journalist and author (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, German footballer (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player and coach (b. 1960)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON> (b. 1960), Equatoguinean politician and diplomat, Prime Minister of Equatorial Guinea", "html": "2014 - <a href=\"https://wikipedia.org/wiki/C%C3%A1ndido_Muatetema_Rivas\" title=\"<PERSON><PERSON><PERSON><PERSON> Muatetem<PERSON> Rivas\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1960), Equatoguinean politician and diplomat, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Equatorial_Guinea\" class=\"mw-redirect\" title=\"Prime Minister of Equatorial Guinea\">Prime Minister of Equatorial Guinea</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A1ndido_Muatetema_Rivas\" title=\"Cán<PERSON><PERSON> Muatetema Rivas\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1960), Equatoguinean politician and diplomat, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Equatorial_Guinea\" class=\"mw-redirect\" title=\"Prime Minister of Equatorial Guinea\">Prime Minister of Equatorial Guinea</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A1ndido_Muatetema_Rivas"}, {"title": "Prime Minister of Equatorial Guinea", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Equatorial_Guinea"}]}, {"year": "2015", "text": "<PERSON>, Indian architect and urban planner (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian architect and urban planner (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian architect and urban planner (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, French director, screenwriter, and critic (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, screenwriter, and critic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, screenwriter, and critic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English political activist and MP (b. 1974)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English political activist and MP (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English political activist and MP (b. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, German politician, Chancellor of Germany (b. 1930)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chancellor of Germany", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany"}]}, {"year": "2020", "text": "<PERSON>, Filipino businessman and politician (b. 1935)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON>.</a>, Filipino businessman and politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, Filipino businessman and politician (b. 1935)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2023", "text": "<PERSON><PERSON>, Swiss cyclist (b. 1997)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Gino_M%C3%A4der\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss cyclist (b. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gino_M%C3%A4der\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss cyclist (b. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gino_M%C3%A4der"}]}, {"year": "2024", "text": "<PERSON>, Austrian constitutional scholar (b. 1932)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Austrian constitutional scholar (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, Austrian constitutional scholar (b. 1932)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2024", "text": "<PERSON>, American art dealer and film producer (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art dealer and film producer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art dealer and film producer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}