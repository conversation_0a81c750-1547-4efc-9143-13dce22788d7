{"date": "October 1", "url": "https://wikipedia.org/wiki/October_1", "data": {"Events": [{"year": "331 BC", "text": "<PERSON> the Great defeats <PERSON> III of Persia in the Battle of Gaugamela.", "html": "331 BC - 331 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Persia\" class=\"mw-redirect\" title=\"<PERSON> III of Persia\"><PERSON> III of Persia</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Gaugamela\" title=\"Battle of Gaugamela\">Battle of Gaugamela</a>.", "no_year_html": "331 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Persia\" class=\"mw-redirect\" title=\"<PERSON> III of Persia\"><PERSON> of Persia</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Gaugamela\" title=\"Battle of Gaugamela\">Battle of Gaugamela</a>.", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}, {"title": "<PERSON> of Persia", "link": "https://wikipedia.org/wiki/Darius_III_of_Persia"}, {"title": "Battle of Gaugamela", "link": "https://wikipedia.org/wiki/Battle_of_Gaugamela"}]}, {"year": "366", "text": "<PERSON> <PERSON><PERSON> I is consecrated.", "html": "366 - <a href=\"https://wikipedia.org/wiki/Pope_Damasus_I\" title=\"Pope Damasus I\">Pope Damasus I</a> is consecrated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Damasus_I\" title=\"Pope Damasus I\">Pope Damasus I</a> is consecrated.", "links": [{"title": "Pope Damasus I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_I"}]}, {"year": "959", "text": "<PERSON> the Peaceful becomes king of all England, in succession to <PERSON><PERSON><PERSON>.", "html": "959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Peaceful\" class=\"mw-redirect\" title=\"<PERSON> the Peaceful\"><PERSON> the Peaceful</a> becomes king of all England, in succession to <a href=\"https://wikipedia.org/wiki/Eadwig\" title=\"<PERSON>adwig\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Peaceful\" class=\"mw-redirect\" title=\"<PERSON> the Peaceful\"><PERSON> the Peaceful</a> becomes king of all England, in succession to <a href=\"https://wikipedia.org/wiki/Eadwig\" title=\"<PERSON><PERSON>wig\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Peaceful"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eadwig"}]}, {"year": "965", "text": "<PERSON> <PERSON> is consecrated.", "html": "965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> is consecrated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> is consecrated.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1553", "text": "The coronation of Queen <PERSON> of England is held at Westminster Abbey.", "html": "1553 - The <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_I_of_England\" title=\"Coronation of <PERSON> I of England\">coronation</a> of Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> of England</a> is held at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_I_of_England\" title=\"Coronation of <PERSON> I of England\">coronation</a> of Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> is held at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "links": [{"title": "Coronation of <PERSON> of England", "link": "https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1588", "text": "The coronation of <PERSON> of Persia occurs.", "html": "1588 - The coronation of <PERSON> <a href=\"https://wikipedia.org/wiki/Abbas_I_of_Persia\" class=\"mw-redirect\" title=\"<PERSON> I of Persia\"><PERSON> of Persia</a> occurs.", "no_year_html": "The coronation of <PERSON> <a href=\"https://wikipedia.org/wiki/Abbas_I_of_Persia\" class=\"mw-redirect\" title=\"<PERSON> I of Persia\"><PERSON> of Persia</a> occurs.", "links": [{"title": "<PERSON> of Persia", "link": "https://wikipedia.org/wiki/Abbas_I_of_Persia"}]}, {"year": "1730", "text": "<PERSON> is forced to abdicate as the Ottoman sultan.", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON> III</a> is forced to abdicate as the Ottoman sultan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON> III</a> is forced to abdicate as the Ottoman sultan.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "The city of Tampere, Finland (belonging to Sweden at this time) is founded by King <PERSON> of Sweden.", "html": "1779 - The city of <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\">Tampere</a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> (belonging to <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a> at this time) is founded by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\">Tampere</a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> (belonging to <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a> at this time) is founded by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a>.", "links": [{"title": "Tampere", "link": "https://wikipedia.org/wiki/Tampere"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Sweden", "link": "https://wikipedia.org/wiki/Sweden"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1787", "text": "Russians under <PERSON> defeat the Turks at Kinburn.", "html": "1787 - Russians under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat the Turks <a href=\"https://wikipedia.org/wiki/Battle_of_Kinburn_(1787)\" title=\"Battle of Kinburn (1787)\">at Kinburn</a>.", "no_year_html": "Russians under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat the Turks <a href=\"https://wikipedia.org/wiki/Battle_of_Kinburn_(1787)\" title=\"Battle of Kinburn (1787)\">at Kinburn</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Battle of Kinburn (1787)", "link": "https://wikipedia.org/wiki/Battle_of_Ki<PERSON>burn_(1787)"}]}, {"year": "1791", "text": "First session of the French Legislative Assembly.", "html": "1791 - First session of the French <a href=\"https://wikipedia.org/wiki/Legislative_Assembly_(France)\" title=\"Legislative Assembly (France)\">Legislative Assembly</a>.", "no_year_html": "First session of the French <a href=\"https://wikipedia.org/wiki/Legislative_Assembly_(France)\" title=\"Legislative Assembly (France)\">Legislative Assembly</a>.", "links": [{"title": "Legislative Assembly (France)", "link": "https://wikipedia.org/wiki/Legislative_Assembly_(France)"}]}, {"year": "1795", "text": "More than a year after the Battle of Sprimont, the Austrian Netherlands (present-day Belgium) are officially annexed by Revolutionary France.", "html": "1795 - More than a year after the <a href=\"https://wikipedia.org/wiki/Battle_of_Sprimont\" title=\"Battle of Sprimont\">Battle of Sprimont</a>, the <a href=\"https://wikipedia.org/wiki/Austrian_Netherlands\" title=\"Austrian Netherlands\">Austrian Netherlands</a> (present-day <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>) are <a href=\"https://wikipedia.org/wiki/History_of_Belgium#French_control\" title=\"History of Belgium\">officially annexed</a> by <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">Revolutionary</a> <a href=\"https://wikipedia.org/wiki/First_French_Republic\" class=\"mw-redirect\" title=\"First French Republic\">France</a>.", "no_year_html": "More than a year after the <a href=\"https://wikipedia.org/wiki/Battle_of_Sprimont\" title=\"Battle of Sprimont\">Battle of Sprimont</a>, the <a href=\"https://wikipedia.org/wiki/Austrian_Netherlands\" title=\"Austrian Netherlands\">Austrian Netherlands</a> (present-day <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>) are <a href=\"https://wikipedia.org/wiki/History_of_Belgium#French_control\" title=\"History of Belgium\">officially annexed</a> by <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">Revolutionary</a> <a href=\"https://wikipedia.org/wiki/First_French_Republic\" class=\"mw-redirect\" title=\"First French Republic\">France</a>.", "links": [{"title": "Battle of Sprimont", "link": "https://wikipedia.org/wiki/Battle_of_Sprimont"}, {"title": "Austrian Netherlands", "link": "https://wikipedia.org/wiki/Austrian_Netherlands"}, {"title": "Belgium", "link": "https://wikipedia.org/wiki/Belgium"}, {"title": "History of Belgium", "link": "https://wikipedia.org/wiki/History_of_Belgium#French_control"}, {"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "First French Republic", "link": "https://wikipedia.org/wiki/First_French_Republic"}]}, {"year": "1800", "text": "Via the Third Treaty of San Ildefonso, Spain cedes Louisiana to France, which would sell the land to the United States thirty months later.", "html": "1800 - Via the <a href=\"https://wikipedia.org/wiki/Third_Treaty_of_San_Ildefonso\" title=\"Third Treaty of San Ildefonso\">Third Treaty of San Ildefonso</a>, Spain cedes Louisiana to France, which would <a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">sell the land</a> to the United States thirty months later.", "no_year_html": "Via the <a href=\"https://wikipedia.org/wiki/Third_Treaty_of_San_Ildefonso\" title=\"Third Treaty of San Ildefonso\">Third Treaty of San Ildefonso</a>, Spain cedes Louisiana to France, which would <a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">sell the land</a> to the United States thirty months later.", "links": [{"title": "Third Treaty of San Ildefonso", "link": "https://wikipedia.org/wiki/Third_Treaty_of_San_Ildefonso"}, {"title": "Louisiana Purchase", "link": "https://wikipedia.org/wiki/Louisiana_Purchase"}]}, {"year": "1814", "text": "The Congress of Vienna opens with the intention of redrawing Europe's political map after the defeat of <PERSON> in the previous spring.", "html": "1814 - The <a href=\"https://wikipedia.org/wiki/Congress_of_Vienna\" title=\"Congress of Vienna\">Congress of Vienna</a> opens with the intention of redrawing Europe's political map after the defeat of Napoleon in the previous spring.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Congress_of_Vienna\" title=\"Congress of Vienna\">Congress of Vienna</a> opens with the intention of redrawing Europe's political map after the defeat of <PERSON> in the previous spring.", "links": [{"title": "Congress of Vienna", "link": "https://wikipedia.org/wiki/Congress_of_Vienna"}]}, {"year": "1827", "text": "Russo-Persian War: The Russian army under <PERSON> storms Yerevan, ending a millennium of Muslim domination of Armenia.", "html": "1827 - <a href=\"https://wikipedia.org/wiki/Russo-Persian_War_(1826%E2%80%9328)\" class=\"mw-redirect\" title=\"Russo-Persian War (1826-28)\">Russo-Persian War</a>: The Russian army under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> storms <a href=\"https://wikipedia.org/wiki/Yerevan\" title=\"Yerevan\">Yerevan</a>, ending a millennium of Muslim domination of <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Persian_War_(1826%E2%80%9328)\" class=\"mw-redirect\" title=\"Russo-Persian War (1826-28)\">Russo-Persian War</a>: The Russian army under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> storms <a href=\"https://wikipedia.org/wiki/Yerevan\" title=\"Yerevan\">Yerevan</a>, ending a millennium of Muslim domination of <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>.", "links": [{"title": "Russo-Persian War (1826-28)", "link": "https://wikipedia.org/wiki/Russo-Persian_War_(1826%E2%80%9328)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Yerevan", "link": "https://wikipedia.org/wiki/Yerevan"}, {"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}]}, {"year": "1829", "text": "The South African College is founded in Cape Town, later separating into the University of Cape Town and the South African College Schools.", "html": "1829 - The <a href=\"https://wikipedia.org/wiki/South_African_College\" title=\"South African College\">South African College</a> is founded in Cape Town, later separating into the <a href=\"https://wikipedia.org/wiki/University_of_Cape_Town\" title=\"University of Cape Town\">University of Cape Town</a> and the <a href=\"https://wikipedia.org/wiki/South_African_College_Schools\" title=\"South African College Schools\">South African College Schools</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/South_African_College\" title=\"South African College\">South African College</a> is founded in Cape Town, later separating into the <a href=\"https://wikipedia.org/wiki/University_of_Cape_Town\" title=\"University of Cape Town\">University of Cape Town</a> and the <a href=\"https://wikipedia.org/wiki/South_African_College_Schools\" title=\"South African College Schools\">South African College Schools</a>.", "links": [{"title": "South African College", "link": "https://wikipedia.org/wiki/South_African_College"}, {"title": "University of Cape Town", "link": "https://wikipedia.org/wiki/University_of_Cape_Town"}, {"title": "South African College Schools", "link": "https://wikipedia.org/wiki/South_African_College_Schools"}]}, {"year": "1832", "text": "Texian political delegates convene at San Felipe de Austin to petition for changes in the governance of Mexican Texas.", "html": "1832 - <a href=\"https://wikipedia.org/wiki/Texian\" class=\"mw-redirect\" title=\"Texian\">Texian</a> political delegates <a href=\"https://wikipedia.org/wiki/Convention_of_1832\" title=\"Convention of 1832\">convene</a> at <a href=\"https://wikipedia.org/wiki/San_Felipe_de_Austin\" class=\"mw-redirect\" title=\"San Felipe de Austin\">San Felipe de Austin</a> to petition for changes in the governance of <a href=\"https://wikipedia.org/wiki/Mexican_Texas\" title=\"Mexican Texas\">Mexican Texas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Texian\" class=\"mw-redirect\" title=\"Texian\">Texian</a> political delegates <a href=\"https://wikipedia.org/wiki/Convention_of_1832\" title=\"Convention of 1832\">convene</a> at <a href=\"https://wikipedia.org/wiki/San_Felipe_de_Austin\" class=\"mw-redirect\" title=\"San Felipe de Austin\">San Felipe de Austin</a> to petition for changes in the governance of <a href=\"https://wikipedia.org/wiki/Mexican_Texas\" title=\"Mexican Texas\">Mexican Texas</a>.", "links": [{"title": "Texian", "link": "https://wikipedia.org/wiki/Texian"}, {"title": "Convention of 1832", "link": "https://wikipedia.org/wiki/Convention_of_1832"}, {"title": "San Felipe de Austin", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mexican Texas", "link": "https://wikipedia.org/wiki/Mexican_Texas"}]}, {"year": "1861", "text": "Mrs <PERSON><PERSON>'s Book of Household Management is published, going on to sell 60,000 copies in its first year and remaining in print until the present day.", "html": "1861 - <i><a href=\"https://wikipedia.org/wiki/Mrs_<PERSON><PERSON>%27s_Book_of_Household_Management\" class=\"mw-redirect\" title=\"Mrs <PERSON><PERSON>'s Book of Household Management\">Mrs <PERSON><PERSON>'s Book of Household Management</a></i> is published, going on to sell 60,000 copies in its first year and remaining in print until the present day.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Mrs_<PERSON><PERSON>%27s_Book_of_Household_Management\" class=\"mw-redirect\" title=\"Mrs <PERSON><PERSON>'s Book of Household Management\">Mrs <PERSON><PERSON>'s Book of Household Management</a></i> is published, going on to sell 60,000 copies in its first year and remaining in print until the present day.", "links": [{"title": "Mrs <PERSON><PERSON>'s Book of Household Management", "link": "https://wikipedia.org/wiki/Mrs_<PERSON><PERSON>%27s_Book_of_Household_Management"}]}, {"year": "1887", "text": "Balochistan is conquered by the British Empire.", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Balochistan\" title=\"Balochistan\">Balochistan</a> is conquered by the British Empire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Balochistan\" title=\"Balochistan\">Balochistan</a> is conquered by the British Empire.", "links": [{"title": "Balochistan", "link": "https://wikipedia.org/wiki/Balochistan"}]}, {"year": "1890", "text": "Yosemite National Park is established by the U.S. Congress.", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Yosemite_National_Park\" title=\"Yosemite National Park\">Yosemite National Park</a> is established by the U.S. Congress.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yosemite_National_Park\" title=\"Yosemite National Park\">Yosemite National Park</a> is established by the U.S. Congress.", "links": [{"title": "Yosemite National Park", "link": "https://wikipedia.org/wiki/Yosemite_National_Park"}]}, {"year": "1891", "text": "Stanford University opens its doors in California, United States.", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Stanford_University\" title=\"Stanford University\">Stanford University</a> opens its doors in California, United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanford_University\" title=\"Stanford University\">Stanford University</a> opens its doors in California, United States.", "links": [{"title": "Stanford University", "link": "https://wikipedia.org/wiki/Stanford_University"}]}, {"year": "1898", "text": "The Vienna University of Economics and Business Administration is founded under the name k.u.k. Exportakademie.", "html": "1898 - The <a href=\"https://wikipedia.org/wiki/Vienna_University_of_Economics_and_Business_Administration\" class=\"mw-redirect\" title=\"Vienna University of Economics and Business Administration\">Vienna University of Economics and Business Administration</a> is founded under the name <i>k.u.k. Exportakademie</i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Vienna_University_of_Economics_and_Business_Administration\" class=\"mw-redirect\" title=\"Vienna University of Economics and Business Administration\">Vienna University of Economics and Business Administration</a> is founded under the name <i>k.u.k. Exportakademie</i>.", "links": [{"title": "Vienna University of Economics and Business Administration", "link": "https://wikipedia.org/wiki/Vienna_University_of_Economics_and_Business_Administration"}]}, {"year": "1903", "text": "Baseball: The Boston Americans play the Pittsburgh Pirates in the first game of the modern World Series.", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">Baseball</a>: The <a href=\"https://wikipedia.org/wiki/Boston_Americans\" class=\"mw-redirect\" title=\"Boston Americans\">Boston Americans</a> play the <a href=\"https://wikipedia.org/wiki/Pittsburgh_Pirates\" title=\"Pittsburgh Pirates\">Pittsburgh Pirates</a> in the first game of the modern <a href=\"https://wikipedia.org/wiki/1903_World_Series\" title=\"1903 World Series\">World Series</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">Baseball</a>: The <a href=\"https://wikipedia.org/wiki/Boston_Americans\" class=\"mw-redirect\" title=\"Boston Americans\">Boston Americans</a> play the <a href=\"https://wikipedia.org/wiki/Pittsburgh_Pirates\" title=\"Pittsburgh Pirates\">Pittsburgh Pirates</a> in the first game of the modern <a href=\"https://wikipedia.org/wiki/1903_World_Series\" title=\"1903 World Series\">World Series</a>.", "links": [{"title": "Baseball", "link": "https://wikipedia.org/wiki/Baseball"}, {"title": "Boston Americans", "link": "https://wikipedia.org/wiki/Boston_Americans"}, {"title": "Pittsburgh Pirates", "link": "https://wikipedia.org/wiki/Pittsburgh_Pirates"}, {"title": "1903 World Series", "link": "https://wikipedia.org/wiki/1903_World_Series"}]}, {"year": "1908", "text": "Ford Model T automobiles are offered for sale at a price of US$825.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Ford_Model_T\" title=\"Ford Model T\">Ford Model T</a> automobiles are offered for sale at a price of US$825.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ford_Model_T\" title=\"Ford Model T\">Ford Model T</a> automobiles are offered for sale at a price of US$825.", "links": [{"title": "Ford Model T", "link": "https://wikipedia.org/wiki/Ford_Model_T"}]}, {"year": "1910", "text": "A large bomb destroys the Los Angeles Times building, killing 21.", "html": "1910 - A <a href=\"https://wikipedia.org/wiki/Los_Angeles_Times_bombing\" title=\"Los Angeles Times bombing\">large bomb destroys</a> the <i>Los Angeles Times</i> building, killing 21.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Los_Angeles_Times_bombing\" title=\"Los Angeles Times bombing\">large bomb destroys</a> the <i>Los Angeles Times</i> building, killing 21.", "links": [{"title": "Los Angeles Times bombing", "link": "https://wikipedia.org/wiki/Los_Angeles_Times_bombing"}]}, {"year": "1915", "text": "The Metamorphosis written by <PERSON> is published in the German journal Die Weißen Blätter", "html": "1915 - <a href=\"https://wikipedia.org/wiki/The_Metamorphosis\" title=\"The Metamorphosis\">The Metamorphosis</a> written by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is published in the German journal <a href=\"https://wikipedia.org/wiki/Die_Wei%C3%9Fen_Bl%C3%A4tter\" title=\"Die Weißen Blätter\">Die Weißen Blätter</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Metamorphosis\" title=\"The Metamorphosis\">The Metamorphosis</a> written by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is published in the German journal <a href=\"https://wikipedia.org/wiki/Die_Wei%C3%9Fen_Bl%C3%A4tter\" title=\"Die Weißen Blätter\">Die Weißen Blätter</a>", "links": [{"title": "The Metamorphosis", "link": "https://wikipedia.org/wiki/The_Metamorphosis"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Die Weißen Blätter", "link": "https://wikipedia.org/wiki/<PERSON>_Wei%C3%9Fen_Bl%C3%A4tter"}]}, {"year": "1918", "text": "World War I: The Egyptian Expeditionary Force captures Damascus.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The Egyptian Expeditionary Force <a href=\"https://wikipedia.org/wiki/Capture_of_Damascus_(1918)\" title=\"Capture of Damascus (1918)\">captures Damascus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The Egyptian Expeditionary Force <a href=\"https://wikipedia.org/wiki/Capture_of_Damascus_(1918)\" title=\"Capture of Damascus (1918)\">captures Damascus</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Capture of Damascus (1918)", "link": "https://wikipedia.org/wiki/Capture_of_Damascus_(1918)"}]}, {"year": "1918", "text": "<PERSON><PERSON> becomes the last Khan of Khiva.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the last Khan of Khiva.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the last <PERSON> of Khiva.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "The 1923 Imperial Conference opened in London.", "html": "1923 - The <a href=\"https://wikipedia.org/wiki/1923_Imperial_Conference\" title=\"1923 Imperial Conference\">1923 Imperial Conference</a> opened in London.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1923_Imperial_Conference\" title=\"1923 Imperial Conference\">1923 Imperial Conference</a> opened in London.", "links": [{"title": "1923 Imperial Conference", "link": "https://wikipedia.org/wiki/1923_Imperial_Conference"}]}, {"year": "1923", "text": "<PERSON> knocked out former British heavyweight champion <PERSON> a mere twenty seconds into the first round of their boxing match at Olympia in London.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> knocked out former <a href=\"https://wikipedia.org/wiki/List_of_British_heavyweight_boxing_champions\" title=\"List of British heavyweight boxing champions\">British heavyweight champion</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> a mere twenty seconds into the first round of their boxing match at <a href=\"https://wikipedia.org/wiki/Olympia_(London)\" class=\"mw-redirect\" title=\"Olympia (London)\">Olympia</a> in London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> knocked out former <a href=\"https://wikipedia.org/wiki/List_of_British_heavyweight_boxing_champions\" title=\"List of British heavyweight boxing champions\">British heavyweight champion</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> a mere twenty seconds into the first round of their boxing match at <a href=\"https://wikipedia.org/wiki/Olympia_(London)\" class=\"mw-redirect\" title=\"Olympia (London)\">Olympia</a> in London.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of British heavyweight boxing champions", "link": "https://wikipedia.org/wiki/List_of_British_heavyweight_boxing_champions"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Olympia (London)", "link": "https://wikipedia.org/wiki/Olympia_(London)"}]}, {"year": "1928", "text": "The Soviet Union introduces its first five-year plan.", "html": "1928 - The Soviet Union introduces its <a href=\"https://wikipedia.org/wiki/First_five-year_plan_(Soviet_Union)\" title=\"First five-year plan (Soviet Union)\">first five-year plan</a>.", "no_year_html": "The Soviet Union introduces its <a href=\"https://wikipedia.org/wiki/First_five-year_plan_(Soviet_Union)\" title=\"First five-year plan (Soviet Union)\">first five-year plan</a>.", "links": [{"title": "First five-year plan (Soviet Union)", "link": "https://wikipedia.org/wiki/First_five-year_plan_(Soviet_Union)"}]}, {"year": "1928", "text": "Newark Liberty International Airport opens, becoming the first airport in the New York City metro area.", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Newark_Liberty_International_Airport\" title=\"Newark Liberty International Airport\">Newark Liberty International Airport</a> opens, becoming the first airport in the <a href=\"https://wikipedia.org/wiki/New_York_metropolitan_area\" title=\"New York metropolitan area\">New York City metro area</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Newark_Liberty_International_Airport\" title=\"Newark Liberty International Airport\">Newark Liberty International Airport</a> opens, becoming the first airport in the <a href=\"https://wikipedia.org/wiki/New_York_metropolitan_area\" title=\"New York metropolitan area\">New York City metro area</a>.", "links": [{"title": "Newark Liberty International Airport", "link": "https://wikipedia.org/wiki/Newark_Liberty_International_Airport"}, {"title": "New York metropolitan area", "link": "https://wikipedia.org/wiki/New_York_metropolitan_area"}]}, {"year": "1931", "text": "The George Washington Bridge in the United States is opened, linking New Jersey and New York.", "html": "1931 - The <a href=\"https://wikipedia.org/wiki/George_<PERSON>_Bridge\" title=\"George Washington Bridge\">George Washington Bridge</a> in the United States is opened, linking <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a> and New York.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/George_<PERSON>_Bridge\" title=\"George Washington Bridge\">George Washington Bridge</a> in the United States is opened, linking <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a> and New York.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington_Bridge"}, {"title": "New Jersey", "link": "https://wikipedia.org/wiki/New_Jersey"}]}, {"year": "1931", "text": "<PERSON> persuades the Constituent Cortes to enfranchise women in Spain's new constitution.", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> persuades the <a href=\"https://wikipedia.org/wiki/Constituent_Cortes\" title=\"Constituent Cortes\">Constituent Cortes</a> to enfranchise women in Spain's new constitution.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> persuades the <a href=\"https://wikipedia.org/wiki/Constituent_Cortes\" title=\"Constituent Cortes\">Constituent Cortes</a> to enfranchise women in Spain's new constitution.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Constituent Cortes", "link": "https://wikipedia.org/wiki/Constituent_Cortes"}]}, {"year": "1936", "text": "Spanish Civil War: <PERSON> is named head of the Nationalist government of Spain.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is named head of the Nationalist government of Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is named head of the Nationalist government of Spain.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Franco"}]}, {"year": "1936", "text": "Spanish Civil War: The Central Committee of Antifascist Militias of Catalonia dissolves itself, handing control of Catalan defence militias over to the Generalitat.", "html": "1936 - Spanish Civil War: The <a href=\"https://wikipedia.org/wiki/Central_Committee_of_Antifascist_Militias_of_Catalonia\" title=\"Central Committee of Antifascist Militias of Catalonia\">Central Committee of Antifascist Militias of Catalonia</a> dissolves itself, handing control of <a href=\"https://wikipedia.org/wiki/Revolutionary_Catalonia\" title=\"Revolutionary Catalonia\">Catalan</a> <a href=\"https://wikipedia.org/wiki/Confederal_militias\" title=\"Confederal militias\">defence militias</a> over to the <a href=\"https://wikipedia.org/wiki/Generalitat_de_Catalunya\" title=\"Generalitat de Catalunya\">Generalitat</a>.", "no_year_html": "Spanish Civil War: The <a href=\"https://wikipedia.org/wiki/Central_Committee_of_Antifascist_Militias_of_Catalonia\" title=\"Central Committee of Antifascist Militias of Catalonia\">Central Committee of Antifascist Militias of Catalonia</a> dissolves itself, handing control of <a href=\"https://wikipedia.org/wiki/Revolutionary_Catalonia\" title=\"Revolutionary Catalonia\">Catalan</a> <a href=\"https://wikipedia.org/wiki/Confederal_militias\" title=\"Confederal militias\">defence militias</a> over to the <a href=\"https://wikipedia.org/wiki/Generalitat_de_Catalunya\" title=\"Generalitat de Catalunya\">Generalitat</a>.", "links": [{"title": "Central Committee of Antifascist Militias of Catalonia", "link": "https://wikipedia.org/wiki/Central_Committee_of_Antifascist_Militias_of_Catalonia"}, {"title": "Revolutionary Catalonia", "link": "https://wikipedia.org/wiki/Revolutionary_Catalonia"}, {"title": "Confederal militias", "link": "https://wikipedia.org/wiki/Confederal_militias"}, {"title": "Generalitat de Catalunya", "link": "https://wikipedia.org/wiki/Generalitat_de_Catalunya"}]}, {"year": "1938", "text": "Pursuant to the Munich Agreement signed the day before, Nazi Germany begins the military occupation and annexation of Czechoslovakia's Sudetenland.", "html": "1938 - Pursuant to the <a href=\"https://wikipedia.org/wiki/Munich_Agreement\" title=\"Munich Agreement\">Munich Agreement</a> signed the day before, <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> begins the military occupation and annexation of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a><span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> <a href=\"https://wikipedia.org/wiki/Sudetenland\" title=\"Sudetenland\">Sudetenland</a>.", "no_year_html": "Pursuant to the <a href=\"https://wikipedia.org/wiki/Munich_Agreement\" title=\"Munich Agreement\">Munich Agreement</a> signed the day before, <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> begins the military occupation and annexation of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a><span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> <a href=\"https://wikipedia.org/wiki/Sudetenland\" title=\"Sudetenland\">Sudetenland</a>.", "links": [{"title": "Munich Agreement", "link": "https://wikipedia.org/wiki/Munich_Agreement"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "Sudetenland", "link": "https://wikipedia.org/wiki/Sudetenland"}]}, {"year": "1939", "text": "World War II: After a one-month siege, German troops occupy Warsaw.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: After a one-month <a href=\"https://wikipedia.org/wiki/Siege_of_Warsaw_(1939)\" title=\"Siege of Warsaw (1939)\">siege</a>, German troops occupy Warsaw.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: After a one-month <a href=\"https://wikipedia.org/wiki/Siege_of_Warsaw_(1939)\" title=\"Siege of Warsaw (1939)\">siege</a>, German troops occupy Warsaw.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Siege of Warsaw (1939)", "link": "https://wikipedia.org/wiki/Siege_of_Warsaw_(1939)"}]}, {"year": "1940", "text": "The Pennsylvania Turnpike, often considered the first superhighway in the United States, opens to traffic.", "html": "1940 - The <a href=\"https://wikipedia.org/wiki/Pennsylvania_Turnpike\" title=\"Pennsylvania Turnpike\">Pennsylvania Turnpike</a>, often considered the first superhighway in the United States, opens to traffic.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pennsylvania_Turnpike\" title=\"Pennsylvania Turnpike\">Pennsylvania Turnpike</a>, often considered the first superhighway in the United States, opens to traffic.", "links": [{"title": "Pennsylvania Turnpike", "link": "https://wikipedia.org/wiki/Pennsylvania_Turnpike"}]}, {"year": "1942", "text": "World War II: USS Grouper torpedoes Lisbon Maru, not knowing that she is carrying British prisoners of war from Hong Kong.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/USS_Grouper\" title=\"USS Grouper\">USS <i>Grouper</i></a> torpedoes <i><a href=\"https://wikipedia.org/wiki/Lisbon_Maru\" title=\"Lisbon Maru\">Lisbon Maru</a></i>, not knowing that she is carrying British prisoners of war from Hong Kong.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/USS_Grouper\" title=\"USS Grouper\">USS <i>Grouper</i></a> torpedoes <i><a href=\"https://wikipedia.org/wiki/Lisbon_Maru\" title=\"Lisbon Maru\">Lisbon Maru</a></i>, not knowing that she is carrying British prisoners of war from Hong Kong.", "links": [{"title": "USS Grouper", "link": "https://wikipedia.org/wiki/USS_Grouper"}, {"title": "Lisbon Maru", "link": "https://wikipedia.org/wiki/Lisbon_Maru"}]}, {"year": "1943", "text": "World War II: After the Four Days of Naples, Allied troops enter the city.", "html": "1943 - World War II: After the <a href=\"https://wikipedia.org/wiki/Four_Days_of_Naples\" title=\"Four Days of Naples\">Four Days of Naples</a>, Allied troops enter the city.", "no_year_html": "World War II: After the <a href=\"https://wikipedia.org/wiki/Four_Days_of_Naples\" title=\"Four Days of Naples\">Four Days of Naples</a>, Allied troops enter the city.", "links": [{"title": "Four Days of Naples", "link": "https://wikipedia.org/wiki/Four_Days_of_Naples"}]}, {"year": "1946", "text": "Nazi leaders are sentenced at the Nuremberg trials.", "html": "1946 - Nazi leaders are sentenced at the <a href=\"https://wikipedia.org/wiki/Nuremberg_trials\" title=\"Nuremberg trials\">Nuremberg trials</a>.", "no_year_html": "Nazi leaders are sentenced at the <a href=\"https://wikipedia.org/wiki/Nuremberg_trials\" title=\"Nuremberg trials\">Nuremberg trials</a>.", "links": [{"title": "Nuremberg trials", "link": "https://wikipedia.org/wiki/Nuremberg_trials"}]}, {"year": "1946", "text": "The Daegu October incident occurs in Allied-occupied Korea.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Autumn_Uprising_of_1946\" title=\"Autumn Uprising of 1946\">Daegu October incident</a> occurs in Allied-occupied Korea.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Autumn_Uprising_of_1946\" title=\"Autumn Uprising of 1946\">Daegu October incident</a> occurs in Allied-occupied Korea.", "links": [{"title": "Autumn Uprising of 1946", "link": "https://wikipedia.org/wiki/Autumn_Uprising_of_1946"}]}, {"year": "1947", "text": "The North American F-86 Sabre flies for the first time.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/North_American_F-86_Sabre\" title=\"North American F-86 Sabre\">North American F-86 Sabre</a> flies for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/North_American_F-86_Sabre\" title=\"North American F-86 Sabre\">North American F-86 Sabre</a> flies for the first time.", "links": [{"title": "North American F-86 Sabre", "link": "https://wikipedia.org/wiki/North_American_F-86_Sabre"}]}, {"year": "1949", "text": "The People's Republic of China is established.", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">People's Republic of China</a> is <a href=\"https://wikipedia.org/wiki/Proclamation_of_the_People%27s_Republic_of_China\" title=\"Proclamation of the People's Republic of China\">established</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">People's Republic of China</a> is <a href=\"https://wikipedia.org/wiki/Proclamation_of_the_People%27s_Republic_of_China\" title=\"Proclamation of the People's Republic of China\">established</a>.", "links": [{"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "Proclamation of the People's Republic of China", "link": "https://wikipedia.org/wiki/Proclamation_of_the_People%27s_Republic_of_China"}]}, {"year": "1953", "text": "Andhra State is formed, consisting of a Telugu-speaking area carved out of India's Madras State.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Andhra_State\" title=\"Andhra State\">Andhra State</a> is formed, consisting of a Telugu-speaking area carved out of India's Madras State.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andhra_State\" title=\"Andhra State\">Andhra State</a> is formed, consisting of a Telugu-speaking area carved out of India's Madras State.", "links": [{"title": "Andhra State", "link": "https://wikipedia.org/wiki/Andhra_State"}]}, {"year": "1953", "text": "A United States-South Korea mutual defense treaty is concluded in Washington, D.C.", "html": "1953 - A <a href=\"https://wikipedia.org/wiki/Mutual_Defense_Treaty_(United_States%E2%80%93South_Korea)\" title=\"Mutual Defense Treaty (United States-South Korea)\">United States-South Korea mutual defense treaty</a> is concluded in Washington, D.C.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Mutual_Defense_Treaty_(United_States%E2%80%93South_Korea)\" title=\"Mutual Defense Treaty (United States-South Korea)\">United States-South Korea mutual defense treaty</a> is concluded in Washington, D.C.", "links": [{"title": "Mutual Defense Treaty (United States-South Korea)", "link": "https://wikipedia.org/wiki/Mutual_Defense_Treaty_(United_States%E2%80%93South_Korea)"}]}, {"year": "1955", "text": "The Xinjiang Uyghur Autonomous Region is established.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Xinjiang\" title=\"Xinjiang\">Xinjiang Uyghur Autonomous Region</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Xinjiang\" title=\"Xinjiang\">Xinjiang Uyghur Autonomous Region</a> is established.", "links": [{"title": "Xinjiang", "link": "https://wikipedia.org/wiki/Xinjiang"}]}, {"year": "1957", "text": "The motto In God We Trust first appears on U.S. paper currency.", "html": "1957 - The motto <i><a href=\"https://wikipedia.org/wiki/In_God_We_Trust\" title=\"In God We Trust\">In God We Trust</a></i> first appears on U.S. paper currency.", "no_year_html": "The motto <i><a href=\"https://wikipedia.org/wiki/In_God_We_Trust\" title=\"In God We Trust\">In God We Trust</a></i> first appears on U.S. paper currency.", "links": [{"title": "In God We Trust", "link": "https://wikipedia.org/wiki/In_God_We_Trust"}]}, {"year": "1958", "text": "The National Advisory Committee for Aeronautics is replaced by NASA.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/National_Advisory_Committee_for_Aeronautics\" title=\"National Advisory Committee for Aeronautics\">National Advisory Committee for Aeronautics</a> is replaced by NASA.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Advisory_Committee_for_Aeronautics\" title=\"National Advisory Committee for Aeronautics\">National Advisory Committee for Aeronautics</a> is replaced by NASA.", "links": [{"title": "National Advisory Committee for Aeronautics", "link": "https://wikipedia.org/wiki/National_Advisory_Committee_for_Aeronautics"}]}, {"year": "1960", "text": "Nigeria gains independence from the United Kingdom.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> gains independence from the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> gains independence from the United Kingdom.", "links": [{"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}]}, {"year": "1961", "text": "The United States Defense Intelligence Agency is formed, becoming the country's first centralized military intelligence organization.", "html": "1961 - The United States <a href=\"https://wikipedia.org/wiki/Defense_Intelligence_Agency\" title=\"Defense Intelligence Agency\">Defense Intelligence Agency</a> is formed, becoming the country's first centralized military intelligence organization.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Defense_Intelligence_Agency\" title=\"Defense Intelligence Agency\">Defense Intelligence Agency</a> is formed, becoming the country's first centralized military intelligence organization.", "links": [{"title": "Defense Intelligence Agency", "link": "https://wikipedia.org/wiki/Defense_Intelligence_Agency"}]}, {"year": "1961", "text": "East and West Cameroon merge to form the Federal Republic of Cameroon.", "html": "1961 - East and West Cameroon merge to form the <a href=\"https://wikipedia.org/wiki/Cameroon\" title=\"Cameroon\">Federal Republic of Cameroon</a>.", "no_year_html": "East and West Cameroon merge to form the <a href=\"https://wikipedia.org/wiki/Cameroon\" title=\"Cameroon\">Federal Republic of Cameroon</a>.", "links": [{"title": "Cameroon", "link": "https://wikipedia.org/wiki/Cameroon"}]}, {"year": "1961", "text": "The CTV Television Network, Canada's first private television network, is launched.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/CTV_Television_Network\" title=\"CTV Television Network\">CTV Television Network</a>, Canada's first private television network, is launched.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/CTV_Television_Network\" title=\"CTV Television Network\">CTV Television Network</a>, Canada's first private television network, is launched.", "links": [{"title": "CTV Television Network", "link": "https://wikipedia.org/wiki/CTV_Television_Network"}]}, {"year": "1962", "text": "<PERSON> enters the University of Mississippi, defying racial segregation rules.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> enters the University of Mississippi, defying racial segregation rules.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> enters the University of Mississippi, defying racial segregation rules.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "On its third anniversary as an independent nation, Nigeria became a republic.", "html": "1963 - On its third anniversary as an independent nation, <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> became a <a href=\"https://wikipedia.org/wiki/Nigerian_First_Republic\" class=\"mw-redirect\" title=\"Nigerian First Republic\">republic</a>.", "no_year_html": "On its third anniversary as an independent nation, <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> became a <a href=\"https://wikipedia.org/wiki/Nigerian_First_Republic\" class=\"mw-redirect\" title=\"Nigerian First Republic\">republic</a>.", "links": [{"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}, {"title": "Nigerian First Republic", "link": "https://wikipedia.org/wiki/Nigerian_First_Republic"}]}, {"year": "1964", "text": "The Free Speech Movement is launched on the campus of the University of California, Berkeley.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/Free_Speech_Movement\" title=\"Free Speech Movement\">Free Speech Movement</a> is launched on the campus of the University of California, Berkeley.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Free_Speech_Movement\" title=\"Free Speech Movement\">Free Speech Movement</a> is launched on the campus of the University of California, Berkeley.", "links": [{"title": "Free Speech Movement", "link": "https://wikipedia.org/wiki/Free_Speech_Movement"}]}, {"year": "1964", "text": "Japanese Shinkansen (\"bullet trains\") begin high-speed rail service from Tokyo to Osaka.", "html": "1964 - Japanese <i><a href=\"https://wikipedia.org/wiki/Shinkansen\" title=\"Shinkansen\">Shinkansen</a></i> (\"bullet trains\") begin high-speed rail service from Tokyo to Osaka.", "no_year_html": "Japanese <i><a href=\"https://wikipedia.org/wiki/Shinkansen\" title=\"Shinkansen\">Shinkansen</a></i> (\"bullet trains\") begin high-speed rail service from Tokyo to Osaka.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>sen"}]}, {"year": "1966", "text": "West Coast Airlines Flight 956 crashes with no survivors in Oregon.  This accident marks the first loss of a DC-9.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/West_Coast_Airlines_Flight_956\" title=\"West Coast Airlines Flight 956\">West Coast Airlines Flight 956</a> crashes with no survivors in Oregon. This accident marks the first loss of a <a href=\"https://wikipedia.org/wiki/DC-9\" class=\"mw-redirect\" title=\"DC-9\">DC-9</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/West_Coast_Airlines_Flight_956\" title=\"West Coast Airlines Flight 956\">West Coast Airlines Flight 956</a> crashes with no survivors in Oregon. This accident marks the first loss of a <a href=\"https://wikipedia.org/wiki/DC-9\" class=\"mw-redirect\" title=\"DC-9\">DC-9</a>.", "links": [{"title": "West Coast Airlines Flight 956", "link": "https://wikipedia.org/wiki/West_Coast_Airlines_Flight_956"}, {"title": "DC-9", "link": "https://wikipedia.org/wiki/DC-9"}]}, {"year": "1968", "text": "Guyana nationalizes the British Guiana Broadcasting Service, which would eventually become part of the National Communications Network, Guyana.", "html": "1968 - Guyana nationalizes the British Guiana Broadcasting Service, which would eventually become part of the <a href=\"https://wikipedia.org/wiki/National_Communications_Network,_Guyana\" title=\"National Communications Network, Guyana\">National Communications Network, Guyana</a>.", "no_year_html": "Guyana nationalizes the British Guiana Broadcasting Service, which would eventually become part of the <a href=\"https://wikipedia.org/wiki/National_Communications_Network,_Guyana\" title=\"National Communications Network, Guyana\">National Communications Network, Guyana</a>.", "links": [{"title": "National Communications Network, Guyana", "link": "https://wikipedia.org/wiki/National_Communications_Network,_Guyana"}]}, {"year": "1969", "text": "<PERSON><PERSON> breaks the sound barrier for the first time.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> breaks the sound barrier for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> breaks the sound barrier for the first time.", "links": [{"title": "Concorde", "link": "https://wikipedia.org/wiki/Concorde"}]}, {"year": "1971", "text": "Walt Disney World opens near Orlando, Florida.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Walt_Disney_World\" title=\"Walt Disney World\">Walt Disney World</a> opens near Orlando, Florida.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walt_Disney_World\" title=\"Walt Disney World\">Walt Disney World</a> opens near Orlando, Florida.", "links": [{"title": "Walt Disney World", "link": "https://wikipedia.org/wiki/Walt_Disney_World"}]}, {"year": "1971", "text": "The first practical CT scanner is used to diagnose a patient.", "html": "1971 - The first <a href=\"https://wikipedia.org/wiki/History_of_computed_tomography#Commercial_scanners\" title=\"History of computed tomography\">practical CT scanner</a> is used to diagnose a patient.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/History_of_computed_tomography#Commercial_scanners\" title=\"History of computed tomography\">practical CT scanner</a> is used to diagnose a patient.", "links": [{"title": "History of computed tomography", "link": "https://wikipedia.org/wiki/History_of_computed_tomography#Commercial_scanners"}]}, {"year": "1975", "text": "<PERSON> defeats <PERSON> in a boxing match in Manila, Philippines.", "html": "1975 - <PERSON> <a href=\"https://wikipedia.org/wiki/Thrilla_in_Manila\" title=\"Thrilla in Manila\">defeats</a> <PERSON> in a boxing match in Manila, Philippines.", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/Thrilla_in_Manila\" title=\"Thrilla in Manila\">defeats</a> <PERSON> in a boxing match in Manila, Philippines.", "links": [{"title": "Thrilla in Manila", "link": "https://wikipedia.org/wiki/Thrilla_in_Manila"}]}, {"year": "1978", "text": "Tuvalu gains independence from the United Kingdom.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Tuvalu\" title=\"Tuvalu\">Tuvalu</a> gains independence from the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tuvalu\" title=\"Tuvalu\">Tuvalu</a> gains independence from the United Kingdom.", "links": [{"title": "Tuvalu", "link": "https://wikipedia.org/wiki/Tuvalu"}]}, {"year": "1979", "text": "<PERSON> <PERSON> begins his first pastoral visit to the United States.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> begins his first pastoral visit to the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> begins his first pastoral visit to the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "The MTR, Hong Kong's rapid transit railway system, opens.", "html": "1979 - The <a href=\"https://wikipedia.org/wiki/MTR\" title=\"MTR\">MTR</a>, Hong Kong's rapid transit railway system, opens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/MTR\" title=\"MTR\">MTR</a>, Hong Kong's rapid transit railway system, opens.", "links": [{"title": "MTR", "link": "https://wikipedia.org/wiki/MTR"}]}, {"year": "1982", "text": "<PERSON> replaces <PERSON> as Chancellor of Germany through a constructive vote of no confidence.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> replaces <PERSON> as Chancellor of Germany through a constructive vote of no confidence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> replaces <PERSON> as Chancellor of Germany through a constructive vote of no confidence.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "EPCOT (Experimental Prototype Community of Tomorrow) opens at Walt Disney World in Florida.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Epcot\" title=\"Epcot\">EPCOT</a> (Experimental Prototype Community of Tomorrow) opens at Walt Disney World in Florida.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Epcot\" title=\"Epcot\">EPCOT</a> (Experimental Prototype Community of Tomorrow) opens at Walt Disney World in Florida.", "links": [{"title": "Epcot", "link": "https://wikipedia.org/wiki/Epcot"}]}, {"year": "1982", "text": "Sony and Phillips launch the compact disc in Japan; on the same day, Sony releases the model CDP-101 compact disc player, the first player of its kind.", "html": "1982 - Sony and Phillips launch the <a href=\"https://wikipedia.org/wiki/Compact_disc\" title=\"Compact disc\">compact disc</a> in Japan; on the same day, Sony releases the model <a href=\"https://wikipedia.org/wiki/Sony_CDP-101\" title=\"Sony CDP-101\">CDP-101</a> compact disc player, the first player of its kind.", "no_year_html": "Sony and Phillips launch the <a href=\"https://wikipedia.org/wiki/Compact_disc\" title=\"Compact disc\">compact disc</a> in Japan; on the same day, Sony releases the model <a href=\"https://wikipedia.org/wiki/Sony_CDP-101\" title=\"Sony CDP-101\">CDP-101</a> compact disc player, the first player of its kind.", "links": [{"title": "Compact disc", "link": "https://wikipedia.org/wiki/Compact_disc"}, {"title": "Sony CDP-101", "link": "https://wikipedia.org/wiki/Sony_CDP-101"}]}, {"year": "1985", "text": "Israel-Palestinian conflict: Israel attacks the Palestine Liberation Organization's Tunisia headquarters during Operation Wooden Leg.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israel-Palestinian conflict</a>: <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel</a> attacks the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a>'s <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a> headquarters during <a href=\"https://wikipedia.org/wiki/Operation_Wooden_Leg\" title=\"Operation Wooden Leg\">Operation Wooden Leg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israel-Palestinian conflict</a>: <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel</a> attacks the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a>'s <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a> headquarters during <a href=\"https://wikipedia.org/wiki/Operation_Wooden_Leg\" title=\"Operation Wooden Leg\">Operation Wooden Leg</a>.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "Israel Defense Forces", "link": "https://wikipedia.org/wiki/Israel_Defense_Forces"}, {"title": "Palestine Liberation Organization", "link": "https://wikipedia.org/wiki/Palestine_Liberation_Organization"}, {"title": "Tunisia", "link": "https://wikipedia.org/wiki/Tunisia"}, {"title": "Operation Wooden Leg", "link": "https://wikipedia.org/wiki/Operation_Wooden_Leg"}]}, {"year": "1987", "text": "The 5.9 Mw  Whittier Narrows earthquake shakes the San Gabriel Valley with a Mercalli intensity of VIII (Severe), killing eight and injuring 200.", "html": "1987 - The 5.9 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1987_Whittier_Narrows_earthquake\" title=\"1987 Whittier Narrows earthquake\">Whittier Narrows earthquake</a> shakes the San Gabriel Valley with a <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>), killing eight and injuring 200.", "no_year_html": "The 5.9 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1987_Whittier_Narrows_earthquake\" title=\"1987 Whittier Narrows earthquake\">Whittier Narrows earthquake</a> shakes the San Gabriel Valley with a <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>), killing eight and injuring 200.", "links": [{"title": "1987 Whittier Narrows earthquake", "link": "https://wikipedia.org/wiki/1987_Whittier_Narrows_earthquake"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1989", "text": "Denmark introduces the world's first legal same-sex registered partnerships.", "html": "1989 - Denmark introduces the world's first legal <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_Denmark\" title=\"Same-sex marriage in Denmark\">same-sex registered partnerships</a>.", "no_year_html": "Denmark introduces the world's first legal <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_Denmark\" title=\"Same-sex marriage in Denmark\">same-sex registered partnerships</a>.", "links": [{"title": "Same-sex marriage in Denmark", "link": "https://wikipedia.org/wiki/Same-sex_marriage_in_Denmark"}]}, {"year": "1991", "text": "Croatian War of Independence: The Siege of Dubrovnik begins.", "html": "1991 - Croatian War of Independence: The <a href=\"https://wikipedia.org/wiki/Siege_of_Dubrovnik\" title=\"Siege of Dubrovnik\">Siege of Dubrovnik</a> begins.", "no_year_html": "Croatian War of Independence: The <a href=\"https://wikipedia.org/wiki/Siege_of_Dubrovnik\" title=\"Siege of Dubrovnik\">Siege of Dubrovnik</a> begins.", "links": [{"title": "Siege of Dubrovnik", "link": "https://wikipedia.org/wiki/Siege_of_Dubrovnik"}]}, {"year": "1994", "text": "<PERSON><PERSON> enters a Compact of Free Association with the United States.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Palau\" title=\"Palau\"><PERSON><PERSON></a> enters a Compact of Free Association with the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Palau\" title=\"Palau\"><PERSON><PERSON></a> enters a Compact of Free Association with the United States.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Palau"}]}, {"year": "2000", "text": "Israel-Palestinian conflict: Palestinians protest the murder of 12-year-old <PERSON> by Israeli police  in northern Israel, beginning the \"October 2000 events\".", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israel-Palestinian conflict</a>: Palestinians protest the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_incident\" class=\"mw-redirect\" title=\"<PERSON> incident\">murder of 12-year-old <PERSON></a> by <a href=\"https://wikipedia.org/wiki/Israel_National_Police\" class=\"mw-redirect\" title=\"Israel National Police\">Israeli police</a> in northern Israel, beginning the \"<a href=\"https://wikipedia.org/wiki/October_2000_protests_in_Israel\" title=\"October 2000 protests in Israel\">October 2000 events</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israel-Palestinian conflict</a>: Palestinians protest the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_incident\" class=\"mw-redirect\" title=\"<PERSON> incident\">murder of 12-year-old <PERSON></a> by <a href=\"https://wikipedia.org/wiki/Israel_National_Police\" class=\"mw-redirect\" title=\"Israel National Police\">Israeli police</a> in northern Israel, beginning the \"<a href=\"https://wikipedia.org/wiki/October_2000_protests_in_Israel\" title=\"October 2000 protests in Israel\">October 2000 events</a>\".", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "<PERSON> incident", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_incident"}, {"title": "Israel National Police", "link": "https://wikipedia.org/wiki/Israel_National_Police"}, {"title": "October 2000 protests in Israel", "link": "https://wikipedia.org/wiki/October_2000_protests_in_Israel"}]}, {"year": "2001", "text": "Militants attack the state legislature building in Kashmir, killing 38.", "html": "2001 - Militants <a href=\"https://wikipedia.org/wiki/2001_Jammu_and_Kashmir_legislative_assembly_car_bombing\" class=\"mw-redirect\" title=\"2001 Jammu and Kashmir legislative assembly car bombing\">attack</a> the state legislature building in Kashmir, killing 38.", "no_year_html": "Militants <a href=\"https://wikipedia.org/wiki/2001_Jammu_and_Kashmir_legislative_assembly_car_bombing\" class=\"mw-redirect\" title=\"2001 Jammu and Kashmir legislative assembly car bombing\">attack</a> the state legislature building in Kashmir, killing 38.", "links": [{"title": "2001 Jammu and Kashmir legislative assembly car bombing", "link": "https://wikipedia.org/wiki/2001_Jammu_and_Kashmir_legislative_assembly_car_bombing"}]}, {"year": "2001", "text": " 3G wireless technology first becomes available when it is adopted by Japanese telecommunications company NTT Docomo.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/3G\" title=\"3G\">3G</a> wireless technology first becomes available when it is <a href=\"https://wikipedia.org/wiki/3G_adoption\" title=\"3G adoption\">adopted</a> by Japanese telecommunications company <a href=\"https://wikipedia.org/wiki/NTT_Docomo\" title=\"NTT Docomo\">NTT Docomo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/3G\" title=\"3G\">3G</a> wireless technology first becomes available when it is <a href=\"https://wikipedia.org/wiki/3G_adoption\" title=\"3G adoption\">adopted</a> by Japanese telecommunications company <a href=\"https://wikipedia.org/wiki/NTT_Docomo\" title=\"NTT Docomo\">NTT Docomo</a>.", "links": [{"title": "3G", "link": "https://wikipedia.org/wiki/3G"}, {"title": "3G adoption", "link": "https://wikipedia.org/wiki/3G_adoption"}, {"title": "NTT Docomo", "link": "https://wikipedia.org/wiki/NTT_Docomo"}]}, {"year": "2003", "text": "The popular and controversial English-language imageboard 4chan is launched.", "html": "2003 - The popular and controversial English-language <a href=\"https://wikipedia.org/wiki/Imageboard\" title=\"Imageboard\">imageboard</a> <a href=\"https://wikipedia.org/wiki/4chan\" title=\"4chan\">4chan</a> is launched.", "no_year_html": "The popular and controversial English-language <a href=\"https://wikipedia.org/wiki/Imageboard\" title=\"Imageboard\">imageboard</a> <a href=\"https://wikipedia.org/wiki/4chan\" title=\"4chan\">4chan</a> is launched.", "links": [{"title": "Imageboard", "link": "https://wikipedia.org/wiki/Imageboard"}, {"title": "4chan", "link": "https://wikipedia.org/wiki/4chan"}]}, {"year": "2009", "text": "The Supreme Court of the United Kingdom takes over the judicial functions of the House of Lords.", "html": "2009 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_Kingdom\" title=\"Supreme Court of the United Kingdom\">Supreme Court of the United Kingdom</a> takes over the judicial functions of the House of Lords.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_Kingdom\" title=\"Supreme Court of the United Kingdom\">Supreme Court of the United Kingdom</a> takes over the judicial functions of the House of Lords.", "links": [{"title": "Supreme Court of the United Kingdom", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_Kingdom"}]}, {"year": "2012", "text": "A ferry collision off the coast of Hong Kong kills 38 people and injures 102 others.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/2012_Lamma_Island_ferry_collision\" title=\"2012 Lamma Island ferry collision\">ferry collision</a> off the coast of Hong Kong kills 38 people and injures 102 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2012_Lamma_Island_ferry_collision\" title=\"2012 Lamma Island ferry collision\">ferry collision</a> off the coast of Hong Kong kills 38 people and injures 102 others.", "links": [{"title": "2012 Lamma Island ferry collision", "link": "https://wikipedia.org/wiki/2012_Lamma_Island_ferry_collision"}]}, {"year": "2014", "text": "A series of explosions at a gunpowder plant in Bulgaria completely destroys the factory, killing 15 people.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/2014_Gorni_Lom_explosions\" title=\"2014 Gorni Lom explosions\">series of explosions</a> at a gunpowder plant in Bulgaria completely destroys the factory, killing 15 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2014_Gorni_Lom_explosions\" title=\"2014 Gorni Lom explosions\">series of explosions</a> at a gunpowder plant in Bulgaria completely destroys the factory, killing 15 people.", "links": [{"title": "2014 Go<PERSON><PERSON> explosions", "link": "https://wikipedia.org/wiki/2014_Gorni_Lom_explosions"}]}, {"year": "2014", "text": "A double bombing of an elementary school in Homs, Syria kills over 50 people.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/Homs_school_bombing\" title=\"Homs school bombing\">double bombing</a> of an <a href=\"https://wikipedia.org/wiki/Elementary_school\" class=\"mw-redirect\" title=\"Elementary school\">elementary school</a> in <a href=\"https://wikipedia.org/wiki/Homs\" title=\"Homs\">Homs</a>, <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> kills over 50 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Homs_school_bombing\" title=\"Homs school bombing\">double bombing</a> of an <a href=\"https://wikipedia.org/wiki/Elementary_school\" class=\"mw-redirect\" title=\"Elementary school\">elementary school</a> in <a href=\"https://wikipedia.org/wiki/Homs\" title=\"Homs\">Homs</a>, <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a> kills over 50 people.", "links": [{"title": "Homs school bombing", "link": "https://wikipedia.org/wiki/Homs_school_bombing"}, {"title": "Elementary school", "link": "https://wikipedia.org/wiki/Elementary_school"}, {"title": "Homs", "link": "https://wikipedia.org/wiki/Homs"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}, {"year": "2015", "text": "A gunman kills nine people at a community college in Oregon.", "html": "2015 - A gunman <a href=\"https://wikipedia.org/wiki/2015_Umpqua_Community_College_shooting\" title=\"2015 Umpqua Community College shooting\">kills nine people</a> at a community college in Oregon.", "no_year_html": "A gunman <a href=\"https://wikipedia.org/wiki/2015_Umpqua_Community_College_shooting\" title=\"2015 Umpqua Community College shooting\">kills nine people</a> at a community college in Oregon.", "links": [{"title": "2015 Umpqua Community College shooting", "link": "https://wikipedia.org/wiki/2015_Umpqua_Community_College_shooting"}]}, {"year": "2015", "text": "Heavy rains trigger a major landslide in Guatemala, killing 280 people.", "html": "2015 - Heavy rains trigger a major <a href=\"https://wikipedia.org/wiki/2015_Guatemala_landslide\" title=\"2015 Guatemala landslide\">landslide</a> in <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a>, killing 280 people.", "no_year_html": "Heavy rains trigger a major <a href=\"https://wikipedia.org/wiki/2015_Guatemala_landslide\" title=\"2015 Guatemala landslide\">landslide</a> in <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a>, killing 280 people.", "links": [{"title": "2015 Guatemala landslide", "link": "https://wikipedia.org/wiki/2015_Guatemala_landslide"}, {"title": "Guatemala", "link": "https://wikipedia.org/wiki/Guatemala"}]}, {"year": "2015", "text": "The American cargo vessel SS El Faro sinks with all of its 33 crew after steaming into the eyewall of Hurricane Joaquin.", "html": "2015 - The American cargo vessel <a href=\"https://wikipedia.org/wiki/SS_El_Faro\" title=\"SS El Faro\">SS <i>El Faro</i></a> sinks with all of its 33 crew after steaming into the eyewall of <a href=\"https://wikipedia.org/wiki/Hurricane_Joaquin\" title=\"Hurricane Joaquin\">Hurricane Joaquin</a>.", "no_year_html": "The American cargo vessel <a href=\"https://wikipedia.org/wiki/SS_El_Faro\" title=\"SS El Faro\">SS <i>El Faro</i></a> sinks with all of its 33 crew after steaming into the eyewall of <a href=\"https://wikipedia.org/wiki/Hurricane_Joaquin\" title=\"Hurricane Joaquin\">Hurricane Joaquin</a>.", "links": [{"title": "SS El Faro", "link": "https://wikipedia.org/wiki/SS_El_Faro"}, {"title": "Hurricane Joaquin", "link": "https://wikipedia.org/wiki/Hurricane_Joaquin"}]}, {"year": "2016", "text": "The leader of the Spanish Socialist Workers' Party, <PERSON>, resigns. He would return to the position a year later.", "html": "2016 - The leader of the <a href=\"https://wikipedia.org/wiki/Spanish_Socialist_Workers%27_Party\" title=\"Spanish Socialist Workers' Party\">Spanish Socialist Workers' Party</a>, <a href=\"https://wikipedia.org/wiki/Pedro_S%C3%<PERSON><PERSON><PERSON>_(Spanish_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Spanish politician)\"><PERSON></a>, resigns. He would return to the position a year later.", "no_year_html": "The leader of the <a href=\"https://wikipedia.org/wiki/Spanish_Socialist_Workers%27_Party\" title=\"Spanish Socialist Workers' Party\">Spanish Socialist Workers' Party</a>, <a href=\"https://wikipedia.org/wiki/Pedro_S%C3%<PERSON><PERSON><PERSON>_(Spanish_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Spanish politician)\"><PERSON></a>, resigns. He would return to the position a year later.", "links": [{"title": "Spanish Socialist Workers' Party", "link": "https://wikipedia.org/wiki/Spanish_Socialist_Workers%27_Party"}, {"title": "<PERSON> (Spanish politician)", "link": "https://wikipedia.org/wiki/Pedro_S%C3%<PERSON><PERSON><PERSON>_(Spanish_politician)"}]}, {"year": "2017", "text": "An independence referendum, later declared illegal by the Constitutional Court of Spain, takes place in Catalonia.", "html": "2017 - An <a href=\"https://wikipedia.org/wiki/2017_Catalan_independence_referendum\" title=\"2017 Catalan independence referendum\">independence referendum</a>, later declared illegal by the Constitutional Court of <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>, takes place in <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2017_Catalan_independence_referendum\" title=\"2017 Catalan independence referendum\">independence referendum</a>, later declared illegal by the Constitutional Court of <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>, takes place in <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>.", "links": [{"title": "2017 Catalan independence referendum", "link": "https://wikipedia.org/wiki/2017_Catalan_independence_referendum"}, {"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}, {"title": "Catalonia", "link": "https://wikipedia.org/wiki/Catalonia"}]}, {"year": "2017", "text": "Sixty people are killed and 867 others injured in a mass shooting at a country music festival at the Las Vegas Strip in the United States; the gunman, <PERSON>, later commits suicide.", "html": "2017 - Sixty people are killed and 867 others injured in <a href=\"https://wikipedia.org/wiki/2017_Las_Vegas_shooting\" title=\"2017 Las Vegas shooting\">a mass shooting</a> at <a href=\"https://wikipedia.org/wiki/Route_91_Harvest\" title=\"Route 91 Harvest\">a country music festival</a> at the <a href=\"https://wikipedia.org/wiki/Las_Vegas_Strip\" title=\"Las Vegas Strip\">Las Vegas Strip</a> in the United States; the gunman, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, later commits suicide.", "no_year_html": "Sixty people are killed and 867 others injured in <a href=\"https://wikipedia.org/wiki/2017_Las_Vegas_shooting\" title=\"2017 Las Vegas shooting\">a mass shooting</a> at <a href=\"https://wikipedia.org/wiki/Route_91_Harvest\" title=\"Route 91 Harvest\">a country music festival</a> at the <a href=\"https://wikipedia.org/wiki/Las_Vegas_Strip\" title=\"Las Vegas Strip\">Las Vegas Strip</a> in the United States; the gunman, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, later commits suicide.", "links": [{"title": "2017 Las Vegas shooting", "link": "https://wikipedia.org/wiki/2017_Las_Vegas_shooting"}, {"title": "Route 91 Harvest", "link": "https://wikipedia.org/wiki/Route_91_Harvest"}, {"title": "Las Vegas Strip", "link": "https://wikipedia.org/wiki/Las_Vegas_Strip"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2018", "text": "The International Court of Justice rules that Chile is not obliged to negotiate access to the Pacific Ocean with Bolivia.", "html": "2018 - The <a href=\"https://wikipedia.org/wiki/International_Court_of_Justice\" title=\"International Court of Justice\">International Court of Justice</a> rules that Chile is <a href=\"https://wikipedia.org/wiki/Obligation_to_Negotiate_Access_to_the_Pacific_Ocean\" title=\"Obligation to Negotiate Access to the Pacific Ocean\">not obliged to negotiate access to the Pacific Ocean</a> with Bolivia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Court_of_Justice\" title=\"International Court of Justice\">International Court of Justice</a> rules that Chile is <a href=\"https://wikipedia.org/wiki/Obligation_to_Negotiate_Access_to_the_Pacific_Ocean\" title=\"Obligation to Negotiate Access to the Pacific Ocean\">not obliged to negotiate access to the Pacific Ocean</a> with Bolivia.", "links": [{"title": "International Court of Justice", "link": "https://wikipedia.org/wiki/International_Court_of_Justice"}, {"title": "Obligation to Negotiate Access to the Pacific Ocean", "link": "https://wikipedia.org/wiki/Obligation_to_Negotiate_Access_to_the_Pacific_Ocean"}]}, {"year": "2019", "text": "Kuopio school stabbing: One dies and ten are injured when <PERSON>, armed with a sabre, attacks a school class at Savo Vocational College in Kuopio, Finland.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Kuopio_school_stabbing\" title=\"Kuopio school stabbing\">Kuopio school stabbing</a>: One dies and ten are injured when <PERSON>, armed with a <a href=\"https://wikipedia.org/wiki/Sabre\" title=\"Sabre\">sabre</a>, attacks a school class at Savo Vocational College in <a href=\"https://wikipedia.org/wiki/Kuopio,_Finland\" class=\"mw-redirect\" title=\"Kuopio, Finland\">Kuopio, Finland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kuopio_school_stabbing\" title=\"Kuopio school stabbing\">Kuopio school stabbing</a>: One dies and ten are injured when <PERSON>, armed with a <a href=\"https://wikipedia.org/wiki/Sabre\" title=\"Sabre\">sabre</a>, attacks a school class at Savo Vocational College in <a href=\"https://wikipedia.org/wiki/Kuopio,_Finland\" class=\"mw-redirect\" title=\"Kuopio, Finland\">Kuopio, Finland</a>.", "links": [{"title": "Kuopio school stabbing", "link": "https://wikipedia.org/wiki/Kuopio_school_stabbing"}, {"title": "Sabre", "link": "https://wikipedia.org/wiki/Sabre"}, {"title": "Kuopio, Finland", "link": "https://wikipedia.org/wiki/Kuopio,_Finland"}]}, {"year": "2021", "text": "The 2020 World Expo in Dubai begins. Its opening was originally scheduled for 20 October 2020 but was delayed due to the COVID-19 pandemic.", "html": "2021 - The <a href=\"https://wikipedia.org/wiki/Expo_2020\" title=\"Expo 2020\">2020 World Expo</a> in <a href=\"https://wikipedia.org/wiki/Dubai\" title=\"Dubai\">Dubai</a> begins. Its opening was originally scheduled for 20 October 2020 but was delayed due to the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Expo_2020\" title=\"Expo 2020\">2020 World Expo</a> in <a href=\"https://wikipedia.org/wiki/Dubai\" title=\"Dubai\">Dubai</a> begins. Its opening was originally scheduled for 20 October 2020 but was delayed due to the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>.", "links": [{"title": "Expo 2020", "link": "https://wikipedia.org/wiki/Expo_2020"}, {"title": "Dubai", "link": "https://wikipedia.org/wiki/Dubai"}, {"title": "COVID-19 pandemic", "link": "https://wikipedia.org/wiki/COVID-19_pandemic"}]}, {"year": "2022", "text": "One hundred and thirty-five are killed in a human crush following a football match at Kanjuruhan Stadium in East Java, Indonesia.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Kanjuruhan_Stadium_disaster\" title=\"Kanjuruhan Stadium disaster\">One hundred and thirty-five are killed</a> in a <a href=\"https://wikipedia.org/wiki/Human_crush\" class=\"mw-redirect\" title=\"Human crush\">human crush</a> following a football match at <a href=\"https://wikipedia.org/wiki/Kanjuruhan_Stadium\" title=\"Kanjuruhan Stadium\">Kanjuruhan Stadium</a> in <a href=\"https://wikipedia.org/wiki/East_Java\" title=\"East Java\">East Java</a>, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kanjuruhan_Stadium_disaster\" title=\"Kanjuruhan Stadium disaster\">One hundred and thirty-five are killed</a> in a <a href=\"https://wikipedia.org/wiki/Human_crush\" class=\"mw-redirect\" title=\"Human crush\">human crush</a> following a football match at <a href=\"https://wikipedia.org/wiki/Kanjuruhan_Stadium\" title=\"Kanjuruhan Stadium\">Kanjuruhan Stadium</a> in <a href=\"https://wikipedia.org/wiki/East_Java\" title=\"East Java\">East Java</a>, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>.", "links": [{"title": "Kanjuruhan Stadium disaster", "link": "https://wikipedia.org/wiki/Kanjuruhan_Stadium_disaster"}, {"title": "Human crush", "link": "https://wikipedia.org/wiki/Human_crush"}, {"title": "Kanjuruhan Stadium", "link": "https://wikipedia.org/wiki/Kanjuruhan_Stadium"}, {"title": "East Java", "link": "https://wikipedia.org/wiki/East_Java"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}]}, {"year": "2024", "text": "Israel invaded Southern Lebanon, marking the fifth Israeli invasion of Lebanon since 1978.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> <a href=\"https://wikipedia.org/wiki/2024_Israeli_invasion_of_Lebanon\" class=\"mw-redirect\" title=\"2024 Israeli invasion of Lebanon\">invaded</a> <a href=\"https://wikipedia.org/wiki/Southern_Lebanon\" title=\"Southern Lebanon\">Southern Lebanon</a>, marking the fifth <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Lebanese_conflict\" title=\"Israeli-Lebanese conflict\">Israeli invasion of Lebanon</a> since 1978.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> <a href=\"https://wikipedia.org/wiki/2024_Israeli_invasion_of_Lebanon\" class=\"mw-redirect\" title=\"2024 Israeli invasion of Lebanon\">invaded</a> <a href=\"https://wikipedia.org/wiki/Southern_Lebanon\" title=\"Southern Lebanon\">Southern Lebanon</a>, marking the fifth <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Lebanese_conflict\" title=\"Israeli-Lebanese conflict\">Israeli invasion of Lebanon</a> since 1978.", "links": [{"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "2024 Israeli invasion of Lebanon", "link": "https://wikipedia.org/wiki/2024_Israeli_invasion_of_Lebanon"}, {"title": "Southern Lebanon", "link": "https://wikipedia.org/wiki/Southern_Lebanon"}, {"title": "Israeli-Lebanese conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Lebanese_conflict"}]}], "Births": [{"year": "86 BC", "text": "<PERSON><PERSON><PERSON>, Roman historian (d. 34 BC)", "html": "86 BC - 86 BC - <a href=\"https://wikipedia.org/wiki/Sallust\" title=\"Sallust\"><PERSON><PERSON><PERSON></a>, Roman historian (d. 34 BC)", "no_year_html": "86 BC - <a href=\"https://wikipedia.org/wiki/Sallust\" title=\"Sallust\"><PERSON><PERSON><PERSON></a>, Roman historian (d. 34 BC)", "links": [{"title": "Sallust", "link": "https://wikipedia.org/wiki/Sallust"}]}, {"year": "208", "text": "<PERSON>, Roman emperor (d. 235)", "html": "208 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Roman emperor (d. 235)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Roman emperor (d. 235)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1207", "text": "<PERSON> of England (d. 1272)", "html": "1207 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> (d. 1272)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> (d. 1272)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1476", "text": "<PERSON>, Count of Laval (d. 1531)", "html": "1476 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Count of Laval (d. 1531)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Count of Laval (d. 1531)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1480", "text": "<PERSON>, Italian Catholic priest and religious reformer (d. 1547)", "html": "1480 - <a href=\"https://wikipedia.org/wiki/Saint_Cajetan\" title=\"Saint Cajetan\"><PERSON></a>, Italian Catholic priest and religious reformer (d. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Cajetan\" title=\"Saint Cajetan\"><PERSON></a>, Italian Catholic priest and religious reformer (d. 1547)", "links": [{"title": "Saint Cajetan", "link": "https://wikipedia.org/wiki/Saint_Cajetan"}]}, {"year": "1507", "text": "<PERSON>ola, Italian architect who designed the Church of the Gesù (d. 1573)", "html": "1507 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Vignola\" title=\"<PERSON> Vignola\"><PERSON> Vignola</a>, Italian architect who designed the <a href=\"https://wikipedia.org/wiki/Church_of_the_Ges%C3%B9\" title=\"Church of the Gesù\">Church of the Gesù</a> (d. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Vignola\" title=\"<PERSON> Vignola\"><PERSON> Vignola</a>, Italian architect who designed the <a href=\"https://wikipedia.org/wiki/Church_of_the_Ges%C3%B9\" title=\"Church of the Gesù\">Church of the Gesù</a> (d. 1573)", "links": [{"title": "<PERSON> Vignola", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Church of the Gesù", "link": "https://wikipedia.org/wiki/Church_of_the_Ges%C3%B9"}]}, {"year": "1526", "text": "<PERSON>, English noble (d. 1604)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noble (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noble (d. 1604)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1540", "text": "<PERSON>, Swiss pastor and theologian (d. 1617)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pastor and theologian (d. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pastor and theologian (d. 1617)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1542", "text": "<PERSON><PERSON><PERSON>, Spanish explorer (d. 1595)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>da%C3%B1a_<PERSON>_<PERSON>eira\" title=\"<PERSON><PERSON><PERSON> de Neira\"><PERSON><PERSON><PERSON></a>, Spanish explorer (d. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>da%C3%B1a_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON> de Neira\"><PERSON><PERSON><PERSON></a>, Spanish explorer (d. 1595)", "links": [{"title": "<PERSON><PERSON><PERSON> Neira", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>_<PERSON>%C3%B1a_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1550", "text": "<PERSON> of Saint Bartholomew, Spanish Discalced Carmelite nun (d. 1626)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saint_Bartholomew\" title=\"<PERSON> of Saint Bartholomew\"><PERSON> of Saint Bartholomew</a>, Spanish Discalced Carmelite nun (d. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Bartholomew\" title=\"<PERSON> of Saint Bartholomew\"><PERSON> of Saint Bartholomew</a>, Spanish Discalced Carmelite nun (d. 1626)", "links": [{"title": "<PERSON> of Saint Bartholomew", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Bartholomew"}]}, {"year": "1554", "text": "<PERSON><PERSON>, Jesuit theologian (d. 1623)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jesuit theologian (d. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jesuit theologian (d. 1623)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1620", "text": "<PERSON><PERSON>, Dutch painter (d. 1683)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter (d. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter (d. 1683)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1671", "text": "<PERSON>, Italian monk, mathematician, and engineer (d. 1742)", "html": "1671 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian monk, mathematician, and engineer (d. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian monk, mathematician, and engineer (d. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1681", "text": "<PERSON><PERSON><PERSON>, Italian painter (d. 1747)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1747)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lama"}]}, {"year": "1685", "text": "<PERSON>, Holy Roman Emperor (d. 1740)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VI, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VI, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1740)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1691", "text": "<PERSON>, English lawyer and politician, Speaker of the House of Commons (d. 1768)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a> (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a> (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the House of Commons (United Kingdom)", "link": "https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)"}]}, {"year": "1712", "text": "<PERSON>, American physician and politician (d. 1801)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician (d. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician (d. 1801)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1719", "text": "<PERSON>, 3rd Earl of Darnley, British parliamentarian (d. 1781)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Darnley\" title=\"<PERSON>, 3rd Earl of Darnley\"><PERSON>, 3rd Earl of Darnley</a>, British parliamentarian (d. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Darnley\" title=\"<PERSON>, 3rd Earl of Darnley\"><PERSON>, 3rd Earl of Darnley</a>, British parliamentarian (d. 1781)", "links": [{"title": "<PERSON>, 3rd Earl of Darnley", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Darnley"}]}, {"year": "1724", "text": "<PERSON>, Italian cellist and composer (d. 1808)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (d. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, German organist and composer (d. 1777)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON>, American lawyer, jurist, and politician (d. 1781)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congressman)\" title=\"<PERSON> (Continental Congressman)\"><PERSON></a>, American lawyer, jurist, and politician (d. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congressman)\" title=\"<PERSON> (Continental Congressman)\"><PERSON></a>, American lawyer, jurist, and politician (d. 1781)", "links": [{"title": "<PERSON> (Continental Congressman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Continental_Congressman)"}]}, {"year": "1760", "text": "<PERSON>, English author and politician (d. 1844)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English author and politician (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English author and politician (d. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, Slovak priest and linguist (d. 1813)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1k\" title=\"<PERSON>\"><PERSON></a>, Slovak priest and linguist (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1k\" title=\"<PERSON>\"><PERSON></a>, Slovak priest and linguist (d. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anton_Bernol%C3%A1k"}]}, {"year": "1771", "text": "<PERSON>, French violinist and composer (d. 1842)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (d. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1791", "text": "<PERSON>, Russian soldier and author (d. 1859)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and author (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and author (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, American wife of <PERSON> (d. 1873)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, American educator, 24th First Lady of the United States (d. 1892)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, 24th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, 24th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of First Ladies of the United States", "link": "https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States"}]}, {"year": "1832", "text": "<PERSON>, American composer and songwriter (d. 1884)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian-Austrian physician and anatomist (d. 1920)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/%C3%81d%C3%A1<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-Austrian physician and anatomist (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81d%C3%A1<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-Austrian physician and anatomist (d. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81d%C3%A1m_Politzer"}]}, {"year": "1842", "text": "<PERSON><PERSON>, Indian lawyer and jurist (d. 1924)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/S._Subramania_Iyer\" title=\"S. Subramania Iyer\">S. Subramania <PERSON></a>, Indian lawyer and jurist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._Subramania_Iyer\" title=\"S. Subramania Iyer\">S. Subramania <PERSON></a>, Indian lawyer and jurist (d. 1924)", "links": [{"title": "S. Subramania Iyer", "link": "https://wikipedia.org/wiki/S._Subramania_Iyer"}]}, {"year": "1842", "text": "<PERSON>, French poet and author (d. 1888)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON><PERSON><PERSON><PERSON> of Aegina, Greek metropolitan and saint (d. 1920)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Nectarios_of_Aegina\" title=\"N<PERSON>tar<PERSON> of Aegina\"><PERSON><PERSON><PERSON><PERSON> of Aegina</a>, Greek metropolitan and saint (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nectarios_of_Aegina\" title=\"N<PERSON><PERSON><PERSON> of Aegina\"><PERSON><PERSON><PERSON><PERSON> of Aegina</a>, Greek metropolitan and saint (d. 1920)", "links": [{"title": "Nectarios of Aegina", "link": "https://wikipedia.org/wiki/Nectarios_of_Aegina"}]}, {"year": "1847", "text": "<PERSON>, English-Indian activist and author (d. 1933)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Indian activist and author (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Indian activist and author (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, French composer, scholar, and critic (d. 1935)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer, scholar, and critic (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer, scholar, and critic (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Austrian economist, sociologist, and philosopher (d. 1950)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian economist, sociologist, and philosopher (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian economist, sociologist, and philosopher (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nn"}]}, {"year": "1881", "text": "<PERSON>, American engineer and businessman who founded the Boeing Company (d. 1956)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/William_Boeing\" class=\"mw-redirect\" title=\"William Boeing\"><PERSON></a>, American engineer and businessman who founded the <a href=\"https://wikipedia.org/wiki/Boeing\" title=\"Boeing\">Boeing Company</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Boeing\" class=\"mw-redirect\" title=\"William Boeing\"><PERSON></a>, American engineer and businessman who founded the <a href=\"https://wikipedia.org/wiki/Boeing\" title=\"Boeing\">Boeing Company</a> (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_Boeing"}, {"title": "Boeing", "link": "https://wikipedia.org/wiki/Boeing"}]}, {"year": "1885", "text": "<PERSON>,  American poet, anthologist, and critic (d. 1977)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, anthologist, and critic (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, anthologist, and critic (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Australian politician, 26th Premier of Queensland (d. 1952)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1952)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Japanese general (d. 1945)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, English actor (d. 1982)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American pianist and songwriter (d. 1974)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Friend\" title=\"<PERSON> Friend\"><PERSON></a>, American pianist and songwriter (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Friend\" title=\"<PERSON> Friend\"><PERSON></a>, American pianist and songwriter (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Friend"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Chinese martial artist (d. 1972)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Ip_<PERSON>\" title=\"Ip Man\"><PERSON><PERSON> <PERSON></a>, Chinese martial artist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ip_<PERSON>\" title=\"Ip Man\"><PERSON><PERSON> <PERSON></a>, Chinese martial artist (d. 1972)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Ip_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Estonian mathematician and academic (d. 1961)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian mathematician and academic (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian mathematician and academic (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Indian-Pakistani lawyer and politician, 1st Prime Minister of Pakistan (d. 1951)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}]}, {"year": "1896", "text": "<PERSON>, American actor, singer, and screenwriter (d. 1937)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and screenwriter (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and screenwriter (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American author (d. 1950)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, English cricketer (d. 1966)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian activist (d. 1927)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian activist (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian activist (d. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Russian-born American pianist and composer (d. 1989)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born American pianist and composer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born American pianist and composer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, French race car driver (d. 1970)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Austrian-English physicist and academic (d. 1979)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English physicist and academic (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English physicist and academic (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian educator and politician (d. 1977)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian educator and politician (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian educator and politician (d. 1977)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian composer and singer (d. 1975)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/S._D._Burman\" title=\"S. D. Burman\">S. D. <PERSON></a>, Indian composer and singer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._D._Burman\" title=\"S. D. Burman\">S. D<PERSON></a>, Indian composer and singer (d. 1975)", "links": [{"title": "S. D. <PERSON>n", "link": "https://wikipedia.org/wiki/S._D._Burman"}]}, {"year": "1907", "text": "<PERSON>, French journalist, author, and critic (d. 1998)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8che\" title=\"<PERSON>\"><PERSON></a>, French journalist, author, and critic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8che\" title=\"<PERSON>\"><PERSON></a>, French journalist, author, and critic (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8che"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian-Israeli viola player and composer (d. 1977)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6n_P%C3%A1rtos\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-Israeli viola player and composer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6n_P%C3%A1rtos\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-Israeli viola player and composer (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96d%C3%B6n_P%C3%A1rtos"}]}, {"year": "1908", "text": "<PERSON>, Danish pianist and composer (d. 1998)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish pianist and composer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish pianist and composer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American captain, politician, and 37th Mayor of Los Angeles (d. 1998)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, politician, and 37th <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, politician, and 37th <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a> (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>y"}, {"title": "Mayor of Los Angeles", "link": "https://wikipedia.org/wiki/Mayor_of_Los_Angeles"}]}, {"year": "1910", "text": "<PERSON>, Austrian-Brazilian physician and pathologist (d. 1983)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Brazilian physician and pathologist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Brazilian physician and pathologist (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fritz_K%C3%B<PERSON><PERSON><PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Australian physicist and engineer (d. 1998)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physicist and engineer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physicist and engineer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American criminal (d. 1934)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American criminal (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American criminal (d. 1934)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Polish-Israeli rabbi and scholar (d. 2012)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli rabbi and scholar (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli rabbi and scholar (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American songwriter, screenwriter, and publisher (d. 1994)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, screenwriter, and publisher (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, screenwriter, and publisher (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Estonian lawyer and politician, 5th Prime Minister of Estonia in exile (d. 2004)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Estonia in exile", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile"}]}, {"year": "1912", "text": "<PERSON>, English mathematician, astronomer, and politician, Lord Mayor of Manchester (d. 2014)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, astronomer, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Manchester\" class=\"mw-redirect\" title=\"Lord Mayor of Manchester\">Lord Mayor of Manchester</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, astronomer, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Manchester\" class=\"mw-redirect\" title=\"Lord Mayor of Manchester\">Lord Mayor of Manchester</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Mayor of Manchester", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_Manchester"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Brazilian martial artist (d. 2009)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_Gracie\" title=\"<PERSON><PERSON><PERSON> Gracie\"><PERSON><PERSON><PERSON></a>, Brazilian martial artist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9lio_Gracie\" title=\"<PERSON><PERSON><PERSON> Gracie\"><PERSON><PERSON><PERSON></a>, Brazilian martial artist (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9lio_Gracie"}]}, {"year": "1913", "text": "<PERSON>, American violinist and producer (d. 1998)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and producer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and producer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American historian, lawyer, author, and 12th Librarian of Congress (d. 2004)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, lawyer, author, and 12th <a href=\"https://wikipedia.org/wiki/Li<PERSON>rian_of_Congress\" title=\"Librarian of Congress\"><PERSON><PERSON>rian of Congress</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, lawyer, author, and 12th <a href=\"https://wikipedia.org/wiki/Li<PERSON>rian_of_Congress\" title=\"Li<PERSON>rian of Congress\">Librarian of Congress</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Librarian of Congress", "link": "https://wikipedia.org/wiki/Librarian_of_Congress"}]}, {"year": "1915", "text": "<PERSON>, American psychologist and author (d. 2016)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Irish cardinal and theologian (d. 2009)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish cardinal and theologian (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish cardinal and theologian (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American baseball player (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 2004)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Indian poet and songwriter (d. 2000)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Majrooh_Sultanpuri\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and songwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Majroo<PERSON>_Sultanpuri\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and songwriter (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Majrooh_Sultanpuri"}]}, {"year": "1920", "text": "<PERSON>, American historian and author (d. 2009)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actor (d. 2009)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, Chinese-American physicist, academic, and Nobel Prize laureate", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American physicist, academic, and <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American physicist, academic, and <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1924", "text": "<PERSON>, American naval lieutenant, politician, 39th President of the United States, and Nobel Prize laureate (d. 2024)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American naval <a href=\"https://wikipedia.org/wiki/Lieutenant_(navy)\" title=\"Lieutenant (navy)\">lieutenant</a>, politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American naval <a href=\"https://wikipedia.org/wiki/Lieutenant_(navy)\" title=\"Lieutenant (navy)\">lieutenant</a>, politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant (navy)", "link": "https://wikipedia.org/wiki/Lieutenant_(navy)"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1924", "text": "<PERSON>, American wrestler and promoter (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and promoter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and promoter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Australian academic (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian academic (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American lawyer and jurist, 16th Chief Justice of the United States (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, 16th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, 16th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1924", "text": "<PERSON>, American pianist (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American pianist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American pianist (d. 2011)", "links": [{"title": "<PERSON> (pianist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)"}]}, {"year": "1927", "text": "<PERSON>, American actor (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American lawyer and judge (d. 2006)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>lve<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>silve<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Malaysian-Scottish journalist and author", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian-Scottish journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian-Scottish journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Lithuanian-English actor, director, and producer (d. 1973)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-English actor, director, and producer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-English actor, director, and producer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Belgian race car driver (d. 1969)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor (d. 1994)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Indian actor (d. 2001)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Chinese engineer and politician, 5th Premier of the People's Republic of China", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese engineer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Premier of the People's Republic of China\">Premier of the People's Republic of China</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese engineer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Premier of the People's Republic of China\">Premier of the People's Republic of China</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of the People's Republic of China", "link": "https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China"}]}, {"year": "1929", "text": "<PERSON>, Australian rugby player and coach", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer (d. 2011)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer-songwriter (d. 2006)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Australian race car driver and manager (d. 2009)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver and manager (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver and manager (d. 2009)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(racing_driver)"}]}, {"year": "1930", "text": "<PERSON>, Irish actor (d. 2002)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani lawyer and politician, Mayor of Karachi (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Karachi\" title=\"Mayor of Karachi\">Mayor of Karachi</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Karachi\" title=\"Mayor of Karachi\">Mayor of Karachi</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Karachi", "link": "https://wikipedia.org/wiki/Mayor_of_Karachi"}]}, {"year": "1930", "text": "<PERSON>, French actor (d. 2006)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian violinist and composer (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian violinist and composer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian violinist and composer (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Pakistani general (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani general (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani general (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American radio host and critic (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host and critic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host and critic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1993)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Spanish banker and businessman (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish banker and businessman (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish banker and businessman (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emilio_Bo<PERSON>%C3%ADn"}]}, {"year": "1935", "text": "<PERSON>, English actress and singer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American sculptor and drummer (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and drummer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and drummer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English footballer (d. 1958)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Pakistani cricketer (d. 2024)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cricketer,_born_1937)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (cricketer, born 1937)\"><PERSON><PERSON></a>, Pakistani cricketer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cricketer,_born_1937)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (cricketer, born 1937)\"><PERSON><PERSON></a>, Pakistani cricketer (d. 2024)", "links": [{"title": "<PERSON><PERSON> (cricketer, born 1937)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cricketer,_born_1937)"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Turkish actor, director, producer, and screenwriter (d. 2019)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Tun%C3%A7_Ba%C5%9Faran\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor, director, producer, and screenwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tun%C3%A7_Ba%C5%9Faran\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor, director, producer, and screenwriter (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tun%C3%A7_Ba%C5%9Faran"}]}, {"year": "1938", "text": "<PERSON>, American fashion designer (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actress and director (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American golfer (d. 2005)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English actor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American feminist psychologist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American feminist psychologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American feminist psychologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English race car driver and manager (d. 2003)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke\" title=\"<PERSON>\"><PERSON></a>, English race car driver and manager (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke\" title=\"<PERSON>\"><PERSON></a>, English race car driver and manager (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke"}]}, {"year": "1940", "text": "<PERSON>, American accordion player, created the Cajun accordion", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player, created the <a href=\"https://wikipedia.org/wiki/Cajun_accordion\" title=\"Cajun accordion\">Cajun accordion</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player, created the <a href=\"https://wikipedia.org/wiki/Cajun_accordion\" title=\"Cajun accordion\">Cajun accordion</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cajun accordion", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>un_accordion"}]}, {"year": "1942", "text": "<PERSON>, American R&B singer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Peaches_%26_Herb\" title=\"Peach<PERSON> &amp; Herb\"><PERSON></a>, American R&amp;B singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peaches_%26_<PERSON>\" title=\"Peach<PERSON> &amp; Herb\"><PERSON></a>, American R&amp;B singer", "links": [{"title": "Peaches & Herb", "link": "https://wikipedia.org/wiki/Peaches_%26_Herb"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, French race car driver and engineer (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French race car driver and engineer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French race car driver and engineer (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian rugby league player (d. 2024)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, French singer-songwriter and guitarist (d. 1973)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8vre\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8vre\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8vre"}]}, {"year": "1942", "text": "<PERSON>, English bishop and scholar", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and scholar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, German journalist and author", "html": "1942 - <a href=\"https://wikipedia.org/wiki/G%C3%BCnter_Wallraff\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON>nter_Wallraff\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCnter_Wallraff"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, French director, producer, and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Canadian singer-songwriter (d. 2014)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Ang%C3%A8le_Arsenault\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ang%C3%A8<PERSON>_Arsenault\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ang%C3%A8le_Arsenault"}]}, {"year": "1943", "text": "<PERSON>, American saxophonist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American author and journalist (d. 2014)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Panamanian-American baseball player and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian-American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian-American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American singer-songwriter, pianist, and producer (d. 1979)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, pianist, and producer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, pianist, and producer (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, 14th President of India", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 14th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 14th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1946", "text": "<PERSON>, English bassist, composer, and bandleader", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, English bassist, composer, and bandleader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, English bassist, composer, and bandleader", "links": [{"title": "<PERSON> (bassist)", "link": "https://wikipedia.org/wiki/<PERSON>_(bassist)"}]}, {"year": "1946", "text": "<PERSON>, American novelist and short story writer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_O%27B<PERSON>_(author)"}]}, {"year": "1947", "text": "<PERSON>, American game designer, co-created Dungeons & Dragons (d. 2009)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, co-created <i><a href=\"https://wikipedia.org/wiki/Dungeons_%26_Dragons\" title=\"Dungeons &amp; Dragons\">Dungeons &amp; Dragons</a></i> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, co-created <i><a href=\"https://wikipedia.org/wiki/Dungeons_%26_Dragons\" title=\"Dungeons &amp; Dragons\">Dungeons &amp; Dragons</a></i> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dungeons & Dragons", "link": "https://wikipedia.org/wiki/Dungeons_%26_Dragons"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and judge", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bhand<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Bhand<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and judge", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American baseball player and coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Capra\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Capra\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Israeli biologist and physician, Nobel Prize laureate", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli biologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli biologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1947", "text": "<PERSON>, American actor and director", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, English-Australian journalist and publisher (d. 2013)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Nev<PERSON>_Dr<PERSON>\" title=\"Nevill Drury\"><PERSON><PERSON><PERSON></a>, English-Australian journalist and publisher (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ill_Dr<PERSON>\" title=\"Nevill Drury\"><PERSON><PERSON><PERSON></a>, English-Australian journalist and publisher (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nevill_<PERSON>ury"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Italian politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Italian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Italian politician", "links": [{"title": "<PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter and bass player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Dutch singer (d. 2006)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Vere<PERSON>\"><PERSON><PERSON></a>, Dutch singer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Veres\"><PERSON><PERSON></a>, Dutch singer (d. 2006)", "links": [{"title": "Mariska Veres", "link": "https://wikipedia.org/wiki/Mariska_Veres"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 2000)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>da\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Koda"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter, liturgist, and author (d. 2010)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, liturgist, and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, liturgist, and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Scottish lawyer and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Dutch violinist, composer, and conductor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch violinist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch violinist, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, <PERSON>, English neuroscientist, academic, and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English neuroscientist, academic, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English neuroscientist, academic, and politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American bassist and composer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian politician, Norwegian Minister of Finance", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Sigbj%C3%B8<PERSON>_<PERSON>\" title=\"Sigbj<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Norway)\" title=\"Minister of Finance (Norway)\">Norwegian Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sigbj%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON>gb<PERSON><PERSON><PERSON>\"><PERSON>g<PERSON><PERSON><PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Norway)\" title=\"Minister of Finance (Norway)\">Norwegian Minister of Finance</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sigbj%C3%B8rn_<PERSON><PERSON>"}, {"title": "Minister of Finance (Norway)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Norway)"}]}, {"year": "1950", "text": "<PERSON>, Russian physician and astronaut (d. 2015)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and astronaut (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and astronaut (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian ice hockey player, coach, and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player, coach, and manager", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1952", "text": "<PERSON>, American baseball player (d. 2012)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Czech singer-songwriter and guitarist  (d. 2012)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech singer-songwriter and guitarist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech singer-songwriter and guitarist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American rock guitarist and songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American baseball player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Estonian volleyball player (d. 2011)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian volleyball player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian volleyball player (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Salvadorian-American soccer player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer,_born_1953)\" title=\"<PERSON> (soccer, born 1953)\"><PERSON></a>, Salvadorian-American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(soccer,_born_1953)\" title=\"<PERSON> (soccer, born 1953)\"><PERSON></a>, Salvadorian-American soccer player", "links": [{"title": "<PERSON> (soccer, born 1953)", "link": "https://wikipedia.org/wiki/<PERSON>_(soccer,_born_1953)"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Norwegian runner and coach (d. 2011)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian runner and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian runner and coach (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>z"}]}, {"year": "1953", "text": "<PERSON>, German civil servant and politician, Governing Mayor of Berlin", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German civil servant and politician, <a href=\"https://wikipedia.org/wiki/Governing_Mayor_of_Berlin\" title=\"Governing Mayor of Berlin\">Governing Mayor of Berlin</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German civil servant and politician, <a href=\"https://wikipedia.org/wiki/Governing_Mayor_of_Berlin\" title=\"Governing Mayor of Berlin\">Governing Mayor of Berlin</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governing Mayor of Berlin", "link": "https://wikipedia.org/wiki/Governing_Mayor_of_Berlin"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Norwegian pianist and composer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American baseball player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Estonian engineer and politician, 15th Prime Minister of Estonia", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian engineer and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian engineer and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>p"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1956", "text": "<PERSON>, English politician, former Prime Minister of the United Kingdom", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON> May\"><PERSON> May</a>, English politician, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON> May\"><PERSON> May</a>, English politician, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1957", "text": "<PERSON>, South Korean actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo\" title=\"<PERSON>-woo\"><PERSON>-woo</a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo\" title=\"<PERSON>-woo\"><PERSON>-woo</a>, South Korean actor", "links": [{"title": "<PERSON>-woo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Hungarian mathematician and educator", "html": "1957 - <a href=\"https://wikipedia.org/wiki/%C3%89va_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian mathematician and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89va_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian mathematician and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89va_Tardos"}]}, {"year": "1958", "text": "<PERSON>, English saxophonist, composer, and painter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English saxophonist, composer, and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English saxophonist, composer, and painter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Japanese bass player and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese bass player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese bass player and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Welsh footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American author and poet", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Senegalese singer-songwriter, musician, and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Youssou_N%27Dour\" title=\"<PERSON><PERSON><PERSON>Do<PERSON>\"><PERSON><PERSON><PERSON></a>, Senegalese singer-songwriter, musician, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Youssou_N%27Dour\" title=\"<PERSON><PERSON><PERSON>Do<PERSON>\"><PERSON><PERSON><PERSON></a>, Senegalese singer-songwriter, musician, and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Youssou_N%27Dour"}]}, {"year": "1960", "text": "<PERSON>, American scientist, Doppler on Wheels inventor, and storm chaser", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist, <a href=\"https://wikipedia.org/wiki/Dopp<PERSON>_on_Wheels\" title=\"Doppler on Wheels\"><PERSON><PERSON><PERSON> on Wheels</a> inventor, and storm chaser", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist, <a href=\"https://wikipedia.org/wiki/Do<PERSON><PERSON>_on_Wheels\" title=\"Doppler on Wheels\"><PERSON><PERSON><PERSON> on Wheels</a> inventor, and storm chaser", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Doppler on Wheels", "link": "https://wikipedia.org/wiki/Do<PERSON><PERSON>_on_Wheels"}]}, {"year": "1961", "text": "<PERSON>, Australian footballer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1961", "text": "<PERSON>, American wrestler and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Con<PERSON>ino\" title=\"<PERSON> Constantino\"><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>stantino\"><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rico_Constantino"}]}, {"year": "1961", "text": "<PERSON><PERSON>, South African cricketer and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> van <PERSON>\"><PERSON><PERSON></a>, South African cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "Attaph<PERSON>m, Thai footballer and manager (d. 2015)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Attaphol_Buspakom\" title=\"Attaphol Buspakom\">Attaphol Buspakom</a>, Thai footballer and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Attaphol_Buspakom\" title=\"Attaphol Buspakom\">Attaphol Buspakom</a>, Thai footballer and manager (d. 2015)", "links": [{"title": "Attaphol Buspakom", "link": "https://wikipedia.org/wiki/Attaphol_Buspakom"}]}, {"year": "1962", "text": "<PERSON>, Belgian footballer and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English footballer and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Swiss race car driver", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9traz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9traz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9traz"}]}, {"year": "1963", "text": "<PERSON>, American baseball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English comedian and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Japanese songwriter, producer, and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese songwriter, producer, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese songwriter, producer, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian-New Zealand chess player and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand chess player and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand chess player and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, German field hockey player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actress and model", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Barbadian prime minister", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian prime minister", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian prime minister", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian journalist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chris Reason\"><PERSON></a>, Australian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chris Reason\"><PERSON></a>, Australian journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Liberian footballer and politician, 25th President of Liberia", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian footballer and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian footballer and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Liberia", "link": "https://wikipedia.org/wiki/President_of_Liberia"}]}, {"year": "1966", "text": "<PERSON>, Spanish footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_%C3%81ngel_Ziganda\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_%C3%81ngel_Ziganda\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_%C3%81ngel_Ziganda"}]}, {"year": "1967", "text": "<PERSON>, American-Canadian football player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" class=\"mw-redirect\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, American-Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" class=\"mw-redirect\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, American-Canadian football player", "links": [{"title": "<PERSON> (Canadian football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)"}]}, {"year": "1967", "text": "<PERSON>, American ice hockey player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1967)\" title=\"<PERSON> (ice hockey, born 1967)\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1967)\" title=\"<PERSON> (ice hockey, born 1967)\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey, born 1967)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1967)"}]}, {"year": "1968", "text": "<PERSON>, English race car driver", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English rugby player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>-<PERSON>, British television presenter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, British television presenter", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American author and engineer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor and pastor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and pastor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and pastor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor, comedian, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American musician, composer, and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, composer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Israeli-American saxophonist and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-American saxophonist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-American saxophonist and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Nauruan weightlifter and politician, 27th President of Nauru", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nauruan weightlifter and politician, 27th <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nauruan weightlifter and politician, 27th <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Nauru", "link": "https://wikipedia.org/wiki/President_of_Nauru"}]}, {"year": "1969", "text": "<PERSON>, Russian ice hockey player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Welsh footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Russian ice hockey player and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Swedish journalist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian lawyer and television host", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Kee<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Kee<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Israeli author and poet", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>dar\" title=\"<PERSON>en <PERSON>dar\"><PERSON><PERSON></a>, Israeli author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>dar\"><PERSON><PERSON></a>, Israeli author and poet", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>dar"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Finnish singer-songwriter and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1972", "text": "<PERSON>, Brazilian footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1972)\" title=\"<PERSON> (footballer, born 1972)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1972)\" title=\"<PERSON> (footballer, born 1972)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born 1972)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1972)"}]}, {"year": "1972", "text": "<PERSON>, British politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Ay%C5%9Fe_Yi%C4%9Fit\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ay%C5%9Fe_Yi%C4%9Fit\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ay%C5%9Fe_Yi%C4%9Fit"}]}, {"year": "1973", "text": "<PERSON>, American actor and singer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, French runner", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Rachid_Ch%C3%A9kh%C3%A9mani\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rachid_Ch%C3%A9kh%C3%A9mani\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rachid_Ch%C3%A9kh%C3%A9mani"}]}, {"year": "1973", "text": "<PERSON>, German swimmer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American composer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1973", "text": "<PERSON>, American baseball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1974", "text": "<PERSON>, Irish singer-songwriter, dancer, and actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, dancer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, dancer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian rugby player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Swedish ice hockey player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian rules footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, German footballer and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Sebescen\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Sebescen\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zolt%C3%A1n_Sebescen"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/%C3%9Cmit_Karan\" title=\"<PERSON><PERSON> Karan\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%9Cmit_Karan\" title=\"<PERSON><PERSON> Karan\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%9Cmit_Karan"}]}, {"year": "1976", "text": "<PERSON>, English guitarist and songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)"}]}, {"year": "1976", "text": "<PERSON>, American painter and sculptor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Estonian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Mark_%C5%A0vets\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mark_%C5%A0vets\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mark_%C5%A0vets"}]}, {"year": "1977", "text": "<PERSON><PERSON>, French-Japanese journalist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Japanese journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Japanese journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Belgian race car driver", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American model and journalist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Letici<PERSON>_<PERSON>line\" title=\"Leticia Cline\"><PERSON><PERSON><PERSON></a>, American model and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Letici<PERSON>_<PERSON><PERSON>\" title=\"Leticia Cline\"><PERSON><PERSON><PERSON></a>, American model and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leticia_Cline"}]}, {"year": "1978", "text": "<PERSON>, English footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American wrestler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Axel\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Axel\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Costa Rican footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Gilberto_Mart%C3%ADnez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gilberto_Mart%C3%ADnez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Costa Rican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gilberto_Mart%C3%ADnez"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, English-Italian rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Italian rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Italian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/J%C3%BAlio_Baptista\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BAlio_Baptista\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BAlio_Baptista"}]}, {"year": "1981", "text": "<PERSON>, New Zealand rugby player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1981", "text": "<PERSON>, Swedish ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Arnau_Riera\" title=\"Arnau Riera\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arnau_Riera\" title=\"Arnau Riera\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "Arnau Riera", "link": "https://wikipedia.org/wiki/Arnau_Riera"}]}, {"year": "1981", "text": "<PERSON>, German-American soccer player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian author and playwright", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Aleksandar_%C4%90uri%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksandar_%C4%90uri%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian author and playwright", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleksandar_%C4%90uri%C4%8Di%C4%87"}]}, {"year": "1983", "text": "<PERSON>, Egyptian footballer (d. 2006)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Montenegrin footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vu%C4%8Dini%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Montenegrin footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vu%C4%8Dini%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Montenegrin footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mirko_Vu%C4%8Dini%C4%87"}]}, {"year": "1984", "text": "<PERSON>, American actor and comedian", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Spanish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_G<PERSON>l%C3%A9n_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Bangladeshi cricketer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Japanese actress and singer (d. 2021)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nda"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Portuguese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_T%C3%AA\" title=\"<PERSON> Vaz Tê\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_T%C3%AA\" title=\"<PERSON> Vaz Tê\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_Vaz_T%C3%AA"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Japanese actor and singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hi<PERSON>i_Aiba"}]}, {"year": "1987", "text": "<PERSON>, Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Indonesian businessman and politician, 14th Vice President of Indonesia", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Gibran_Rakabuming_Raka\" title=\"Gibran Rakabuming Raka\"><PERSON><PERSON><PERSON></a>, Indonesian businessman and politician, 14th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ran_Rakabuming_Raka\" title=\"Gibran Rakabuming Raka\"><PERSON><PERSON><PERSON></a>, Indonesian businessman and politician, 14th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ra<PERSON><PERSON>ming_Raka"}, {"title": "Vice President of Indonesia", "link": "https://wikipedia.org/wiki/Vice_President_of_Indonesia"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Portuguese footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Estonian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert <PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Irish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Aruban baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>ander_Bo<PERSON>erts\" title=\"Xander Bogaerts\"><PERSON><PERSON></a>, Aruban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ander_Bo<PERSON>erts\" title=\"Xander Bo<PERSON>\"><PERSON><PERSON></a>, Aruban baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ander_<PERSON>s"}]}, {"year": "1993", "text": "<PERSON>, South African-Australian cricketer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African-Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African-Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1993", "text": "<PERSON><PERSON>, South African cricketer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Egyptian footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Tr%C3%A9z%C3%A9<PERSON><PERSON>_(Egyptian_footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Egyptian footballer)\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tr%C3%A9z%C3%A9<PERSON><PERSON>_(Egyptian_footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Egyptian footballer)\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Egyptian footballer)", "link": "https://wikipedia.org/wiki/Tr%C3%A9z%C3%A9<PERSON><PERSON>_(Egyptian_footballer)"}]}, {"year": "1997", "text": "<PERSON>, English singer, songwriter, and musician", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bird\"><PERSON></a>, English singer, songwriter, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bird\"><PERSON></a>, English singer, songwriter, and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Australian-Tongan rugby league player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Ha<PERSON><PERSON>_<PERSON>au%27atu\" title=\"<PERSON><PERSON><PERSON>'at<PERSON>\"><PERSON><PERSON><PERSON>at<PERSON></a>, Australian-Tongan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ha<PERSON><PERSON>_<PERSON>au%27atu\" title=\"<PERSON><PERSON><PERSON>at<PERSON>\"><PERSON><PERSON><PERSON>at<PERSON></a>, Australian-Tongan rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Haumole_Olakau%27atu"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Finnish professional rally driver", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON>_<PERSON>nper%C3%A4\" title=\"Ka<PERSON>\"><PERSON><PERSON></a>, Finnish professional <a href=\"https://wikipedia.org/wiki/Rally_driver\" class=\"mw-redirect\" title=\"Rally driver\">rally driver</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON>_<PERSON>nper%C3%A4\" title=\"Ka<PERSON>\"><PERSON><PERSON></a>, Finnish professional <a href=\"https://wikipedia.org/wiki/Rally_driver\" class=\"mw-redirect\" title=\"Rally driver\">rally driver</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON>_<PERSON>ovanper%C3%A4"}, {"title": "Rally driver", "link": "https://wikipedia.org/wiki/Rally_driver"}]}, {"year": "2001", "text": "<PERSON>, American actress and singer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Luna Blaise\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Luna Blaise\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON>, English footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American gymnast and social media personality", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast and social media personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast and social media personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "630", "text": "<PERSON><PERSON><PERSON>', Mayan king", "html": "630 - <a href=\"https://wikipedia.org/wiki/Tajoom_Uk%27ab_K%27ahk%27\" class=\"mw-redirect\" title=\"Ta<PERSON>om Uk'ab K'ahk'\"><PERSON><PERSON><PERSON>'ab <PERSON>'ahk'</a>, Mayan king", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tajoom_Uk%27ab_K%27ahk%27\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> U<PERSON>'ab <PERSON>'ahk'\"><PERSON><PERSON><PERSON>'ab <PERSON>ahk'</a>, Mayan king", "links": [{"title": "<PERSON><PERSON><PERSON>a<PERSON>ah<PERSON>'", "link": "https://wikipedia.org/wiki/Tajoom_Uk%27ab_K%27ahk%27"}]}, {"year": "686", "text": "Emperor <PERSON><PERSON> of Japan (b. 631)", "html": "686 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (b. 631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (b. 631)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "804", "text": "<PERSON><PERSON><PERSON>, archbishop of Trier", "html": "804 - <a href=\"https://wikipedia.org/wiki/Richbod\" title=\"Richbod\"><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Trier\" title=\"Roman Catholic Diocese of Trier\">Trier</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Richbod\" title=\"Richbod\"><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Trier\" title=\"Roman Catholic Diocese of Trier\">Trier</a>", "links": [{"title": "Richbod", "link": "https://wikipedia.org/wiki/Richbod"}, {"title": "Roman Catholic Diocese of Trier", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Trier"}]}, {"year": "895", "text": "<PERSON>, chancellor of the Tang dynasty", "html": "895 - <a href=\"https://wikipedia.org/wiki/Kong_Wei\" title=\"Kong Wei\"><PERSON></a>, chancellor of the Tang dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kong_Wei\" title=\"Kong Wei\"><PERSON></a>, chancellor of the Tang dynasty", "links": [{"title": "Kong Wei", "link": "https://wikipedia.org/wiki/Kong_Wei"}]}, {"year": "918", "text": "<PERSON>, empress of Former Shu", "html": "918 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(Former_Shu)\" title=\"Empress <PERSON> (Former Shu)\"><PERSON></a>, empress of <a href=\"https://wikipedia.org/wiki/Former_Shu\" title=\"Former Shu\">Former Shu</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(Former_Shu)\" title=\"Empress <PERSON> (Former Shu)\"><PERSON></a>, empress of <a href=\"https://wikipedia.org/wiki/Former_Shu\" title=\"Former Shu\">Former Shu</a>", "links": [{"title": "<PERSON> <PERSON> (Former Shu)", "link": "https://wikipedia.org/wiki/Empress_<PERSON>_(Former_Shu)"}, {"title": "Former <PERSON>", "link": "https://wikipedia.org/wiki/Former_Shu"}]}, {"year": "959", "text": "<PERSON><PERSON><PERSON>, English king (b. 941)", "html": "959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English king (b. 941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English king (b. 941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eadwig"}]}, {"year": "961", "text": "<PERSON><PERSON>, archbishop of Reims", "html": "961 - <a href=\"https://wikipedia.org/wiki/Art<PERSON>_of_Reims\" title=\"<PERSON><PERSON> of Reims\"><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Reims\" title=\"Roman Catholic Archdiocese of Reims\">Reims</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art<PERSON>_of_Reims\" title=\"<PERSON><PERSON> of Reims\"><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Reims\" title=\"Roman Catholic Archdiocese of Reims\">Reims</a>", "links": [{"title": "<PERSON><PERSON> of Reims", "link": "https://wikipedia.org/wiki/Artald_of_Reims"}, {"title": "Roman Catholic Archdiocese of Reims", "link": "https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Reims"}]}, {"year": "1040", "text": "<PERSON>, Duke of Brittany (b. 997)", "html": "1040 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 997)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1126", "text": "<PERSON><PERSON><PERSON> <PERSON> Melitene, Queen of Jerusalem", "html": "1126 - <a href=\"https://wikipedia.org/wiki/Morphia_of_Melitene\" title=\"Morphia of Melitene\"><PERSON><PERSON><PERSON> of Melitene</a>, Queen of Jerusalem", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morphia_of_Melitene\" title=\"Morphia of Melitene\"><PERSON><PERSON><PERSON> of Melitene</a>, Queen of Jerusalem", "links": [{"title": "Morphia of Melitene", "link": "https://wikipedia.org/wiki/Morphia_of_Melitene"}]}, {"year": "1310", "text": "<PERSON> of Burgundy, Lady of Bourbon (b. 1257)", "html": "1310 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Burgundy,_Lady_of_Bourbon\" title=\"<PERSON> of Burgundy, Lady of Bourbon\"><PERSON> of Burgundy, Lady of Bourbon</a> (b. 1257)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Burgundy,_Lady_of_Bourbon\" title=\"Beatrice of Burgundy, Lady of Bourbon\"><PERSON> of Burgundy, Lady of Bourbon</a> (b. 1257)", "links": [{"title": "<PERSON> of Burgundy, Lady of Bourbon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Burgundy,_Lady_<PERSON>_<PERSON>"}]}, {"year": "1404", "text": "<PERSON> <PERSON><PERSON><PERSON> (b. 1356)", "html": "1404 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON><PERSON><PERSON>_IX\" title=\"<PERSON>ace IX\"><PERSON> <PERSON><PERSON> IX</a> (b. 1356)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_IX\" title=\"Pope Bon<PERSON>ace IX\"><PERSON> <PERSON><PERSON><PERSON> IX</a> (b. 1356)", "links": [{"title": "<PERSON> IX", "link": "https://wikipedia.org/wiki/<PERSON>_Boniface_IX"}]}, {"year": "1416", "text": "<PERSON><PERSON><PERSON>, Albanian ruler", "html": "1416 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian ruler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yaq<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian ruler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yaq<PERSON>_<PERSON>ta"}]}, {"year": "1450", "text": "<PERSON><PERSON>, Marquis of Ferrara, Italian noble (b. 1407)", "html": "1450 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27<PERSON><PERSON>,_Marquis_of_Ferrara\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Marquis of Ferrara\"><PERSON><PERSON>, Marquis of Ferrara</a>, Italian noble (b. 1407)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27<PERSON><PERSON>,_Marquis_of_Ferrara\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Marquis of Ferrara\"><PERSON><PERSON>, Marquis of Ferrara</a>, Italian noble (b. 1407)", "links": [{"title": "<PERSON><PERSON>, Marquis of Ferrara", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_d%27<PERSON><PERSON>,_<PERSON>_of_Ferrara"}]}, {"year": "1499", "text": "<PERSON><PERSON><PERSON>, Italian astrologer and philosopher (b. 1433)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian astrologer and philosopher (b. 1433)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian astrologer and philosopher (b. 1433)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1500", "text": "<PERSON>, English bishop and politician, Lord Chancellor of the United Kingdom (b. 1430)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of the United Kingdom</a> (b. 1430)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of the United Kingdom</a> (b. 1430)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1532", "text": "<PERSON>, Flemish painter", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Flemish painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Flemish painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1567", "text": "<PERSON>, Italian humanist (b. 1508)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian humanist (b. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian humanist (b. 1508)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1570", "text": "<PERSON><PERSON>, Flemish painter (b. 1520)", "html": "1570 - <a href=\"https://wikipedia.org/wiki/Frans_Floris\" title=\"Frans Floris\"><PERSON><PERSON></a>, Flemish painter (b. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr<PERSON>_Floris\" title=\"Frans Floris\"><PERSON><PERSON></a>, Flemish painter (b. 1520)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Frans_Floris"}]}, {"year": "1574", "text": "<PERSON><PERSON><PERSON>, Dutch painter (b. 1498)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1498)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1498)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1578", "text": "<PERSON> of Austria (b. 1547)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/John_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (b. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (b. 1547)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/John_of_Austria"}]}, {"year": "1588", "text": "<PERSON>, English priest and martyr (b. 1557)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, English priest and martyr (b. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, English priest and martyr (b. 1557)", "links": [{"title": "<PERSON> (martyr)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)"}]}, {"year": "1602", "text": "<PERSON><PERSON><PERSON>, Spanish organist and composer (b. 1541)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/Hernan<PERSON>_de_<PERSON>abez%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish organist and composer (b. 1541)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hernan<PERSON>_<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish organist and composer (b. 1541)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nan<PERSON>_<PERSON>_Cabez%C3%B3n"}]}, {"year": "1609", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian priest and composer (b. 1532)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian priest and composer (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian priest and composer (b. 1532)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ola"}]}, {"year": "1652", "text": "<PERSON>, Dutch painter (b. 1610)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1610)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON>, colonial American merchant and politician (b. 1624)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(merchant)\" title=\"<PERSON> (merchant)\"><PERSON></a>, colonial American merchant and politician (b. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(merchant)\" title=\"<PERSON> (merchant)\"><PERSON></a>, colonial American merchant and politician (b. 1624)", "links": [{"title": "<PERSON> (merchant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(merchant)"}]}, {"year": "1684", "text": "<PERSON>, French playwright (b. 1606)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright (b. 1606)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1690", "text": "<PERSON><PERSON><PERSON>, Venetian statesman and military commander (b. 1632)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/Girolamo_Corner\" title=\"Girolamo Corner\"><PERSON><PERSON><PERSON></a>, Venetian statesman and military commander (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Girolamo_Corner\" title=\"Girolamo Corner\"><PERSON><PERSON><PERSON></a>, Venetian statesman and military commander (b. 1632)", "links": [{"title": "Girolamo Corner", "link": "https://wikipedia.org/wiki/Girolamo_Corner"}]}, {"year": "1693", "text": "<PERSON>, Spanish theologian and academic (b. 1619)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish theologian and academic (b. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish theologian and academic (b. 1619)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>ca"}]}, {"year": "1708", "text": "<PERSON>, English organist and composer (b. 1649)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1649)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, Scottish mathematician and academic (b. 1687)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mathematician and academic (b. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mathematician and academic (b. 1687)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, Scottish businessman and politician (b. 1741)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician (b. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician (b. 1741)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, American politician (b. 1777)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(U.S._politician)\" class=\"mw-redirect\" title=\"<PERSON> (U.S. politician)\"><PERSON></a>, American politician (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(U.S._politician)\" class=\"mw-redirect\" title=\"<PERSON> (U.S. politician)\"><PERSON></a>, American politician (b. 1777)", "links": [{"title": "<PERSON> (U.S. politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(U.S._politician)"}]}, {"year": "1838", "text": "<PERSON>, Scottish chemist and businessman (b. 1768)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and businessman (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and businessman (b. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, American spy (b. 1817)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Neal_Greenhow\" title=\"<PERSON>\"><PERSON></a>, American spy (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Neal_Greenhow\" title=\"<PERSON>\"><PERSON></a>, American spy (b. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rose_O%27Neal_Greenhow"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Burmese king (b. 1808)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Mindon_Min\" title=\"Mindon Min\"><PERSON><PERSON></a>, Burmese king (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mindon_Min\" title=\"Mindon Min\"><PERSON><PERSON></a>, Burmese king (b. 1808)", "links": [{"title": "Mind<PERSON> Min", "link": "https://wikipedia.org/wiki/Mind<PERSON>_Min"}]}, {"year": "1885", "text": "<PERSON>, American physician and surgeon (b. 1799)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and surgeon (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and surgeon (b. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>lee"}]}, {"year": "1895", "text": "<PERSON>, Jr., American chemist, physicist, and academic (b. 1836)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American chemist, physicist, and academic (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American chemist, physicist, and academic (b. 1836)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1901", "text": "<PERSON><PERSON>, Afghan emir (b. 1844)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan emir (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan emir (b. 1844)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Canadian businessman and philanthropist (b. 1827)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eugene_O%27Keefe"}]}, {"year": "1929", "text": "<PERSON>, French sculptor and painter (b. 1861)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and painter (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and painter (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Chinese mathematician (b. 1898)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Chiungtze_<PERSON>._Tsen\" title=\"Chiungtze C. Tsen\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Chinese mathematician (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chiungtze_<PERSON>._Tsen\" title=\"Chiungtze C. Tsen\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Chinese mathematician (b. 1898)", "links": [{"title": "Chiungtze C. Tsen", "link": "https://wikipedia.org/wiki/<PERSON>ungtze_<PERSON><PERSON>_Tsen"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Estonian lawyer and politician, 7th Prime Minister of Estonia (b. 1884)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pi<PERSON>\" title=\"<PERSON><PERSON> Piip\"><PERSON><PERSON></a>, Estonian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Pi<PERSON>\"><PERSON><PERSON></a>, Estonian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a> (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ants_Piip"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Turkish poet, educator, and politician (b. 1876)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet, educator, and politician (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet, educator, and politician (b. 1876)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Scottish-English footballer and manager (b. 1878)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English footballer and manager (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English footballer and manager (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American painter (b. 1870)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American film producer who founded Christie Film Company (b. 1880)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American film producer who founded <a href=\"https://wikipedia.org/wiki/Christie_Film_Company\" title=\"Christie Film Company\">Christie Film Company</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American film producer who founded <a href=\"https://wikipedia.org/wiki/Christie_Film_Company\" title=\"Christie Film Company\">Christie Film Company</a> (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Christie Film Company", "link": "https://wikipedia.org/wiki/Christie_Film_Company"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish civil servant, politician, and sixth Turkish Minister of National Defence (b. 1881)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Abd%C3%<PERSON>lhalik_Renda\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Renda\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish civil servant, politician, and sixth <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_National_Defence_of_Turkey\" class=\"mw-redirect\" title=\"List of Ministers of National Defence of Turkey\">Turkish Minister of National Defence</a> (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abd%C3%<PERSON><PERSON><PERSON><PERSON>_Renda\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>da\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish civil servant, politician, and sixth <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_National_Defence_of_Turkey\" class=\"mw-redirect\" title=\"List of Ministers of National Defence of Turkey\">Turkish Minister of National Defence</a> (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abd%C3%<PERSON><PERSON>halik_Renda"}, {"title": "List of Ministers of National Defence of Turkey", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_National_Defence_of_Turkey"}]}, {"year": "1958", "text": "<PERSON>, Russian painter and educator (b. 1886)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Italian journalist, lawyer, politician, and first President of Italy (b. 1877)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist, lawyer, politician, and first <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist, lawyer, politician, and first <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}]}, {"year": "1961", "text": "<PERSON>, Italian-American author and illustrator (b. 1898)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American author and illustrator (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American author and illustrator (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Italian-German Catholic priest, author, and academic (b. 1885)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-German Catholic priest, author, and academic (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-German Catholic priest, author, and academic (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Romano_Guardini"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Argentinian race car driver (b. 1893)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BA<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian race car driver (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BA<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian race car driver (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_<PERSON>nti"}]}, {"year": "1972", "text": "<PERSON>, Kenyan-English archaeologist and paleontologist (b. 1903)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English archaeologist and paleontologist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English archaeologist and paleontologist (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Greek archaeologist and academic (b. 1901)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>rid<PERSON>_Marinatos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek archaeologist and academic (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rid<PERSON>_Marinatos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek archaeologist and academic (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Spyridon_Marinatos"}]}, {"year": "1975", "text": "<PERSON>, Jr., American drummer, songwriter, and producer (b. 1935)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American drummer, songwriter, and producer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American drummer, songwriter, and producer (b. 1935)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1984", "text": "<PERSON>, American baseball player and manager (b. 1911)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Scottish race car driver (b. 1925)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish race car driver (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish race car driver (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON> <PERSON><PERSON>, American essayist and journalist (b. 1899)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"E. B<PERSON> White\"><PERSON><PERSON> <PERSON><PERSON></a>, American essayist and journalist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"E. B<PERSON> White\"><PERSON><PERSON> <PERSON><PERSON></a>, American essayist and journalist (b. 1899)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American air traffic controller (b. 1907)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Archie_League\" title=\"Archie League\">Archie League</a>, American air traffic controller (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archie_League\" title=\"Archie League\"><PERSON></a>, American air traffic controller (b. 1907)", "links": [{"title": "Archie League", "link": "https://wikipedia.org/wiki/Archie_League"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, English author, poet, and critic (b. 1897)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>well\"><PERSON><PERSON><PERSON><PERSON></a>, English author, poet, and critic (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Sitwell\"><PERSON><PERSON><PERSON><PERSON></a>, English author, poet, and critic (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American general (b. 1906)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, German activist and politician (b. 1947)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist and politician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist and politician (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, German mathematician and philosopher (b. 1915)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and philosopher (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and philosopher (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American engineer and philanthropist (b. 1923)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and philanthropist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and philanthropist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American publisher, diplomat, and United States Ambassador to the United Kingdom (b. 1908)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, diplomat, and <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, diplomat, and <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Kingdom", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom"}]}, {"year": "2004", "text": "<PERSON>, American photographer (b. 1923)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Canadian bass player (b. 1946)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Estonian journalist and author (b. 1921)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Saudi Arabian terrorist (b. 1979)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_al-<PERSON>beiee\" title=\"<PERSON><PERSON><PERSON> al-Rabeiee\"><PERSON><PERSON><PERSON></a>, Saudi Arabian terrorist (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_al-Rabeiee\" title=\"<PERSON><PERSON><PERSON> al-Rabeiee\"><PERSON><PERSON><PERSON> <PERSON></a>, Saudi Arabian terrorist (b. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, American author and activist (b. 1938)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and activist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and activist (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English conductor and composer (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and composer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and composer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Australian footballer and journalist (b. 1965)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and journalist (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and journalist (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American discus thrower (b. 1936)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "2008", "text": "<PERSON>, American cinematographer (b. 1925)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(yachting_cinematographer)\" title=\"<PERSON> (yachting cinematographer)\"><PERSON></a>, American cinematographer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(yachting_cinematographer)\" title=\"<PERSON> (yachting cinematographer)\"><PERSON></a>, American cinematographer (b. 1925)", "links": [{"title": "<PERSON> (yachting cinematographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(yachting_cinematographer)"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Cuban poet and author (b. 1921)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban poet and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban poet and author (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>intio_Vitier"}]}, {"year": "2010", "text": "<PERSON>, English footballer and cricketer (b. 1938)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and cricketer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and cricketer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Swedish ice hockey player and golfer (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and golfer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and golfer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Spanish-Argentinian director and screenwriter (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Octavio_<PERSON>\" title=\"Octavio <PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-Argentinian director and screenwriter (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octavio_<PERSON>\" title=\"Octavi<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-Argentinian director and screenwriter (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Octavi<PERSON>_<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Egyptian-English historian and author (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-English historian and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-English historian and author (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and judge (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Hungarian-Israeli economist and banker (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Mo<PERSON>\"><PERSON><PERSON></a>, Hungarian-Israeli economist and banker (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-Israeli economist and banker (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>bar"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Greek-Italian Holocaust survivor and author (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Shlomo_Venezia\" title=\"Shlomo Venezia\">Shlomo Venezia</a>, Greek-Italian <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">Holocaust</a> survivor and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shlomo_Venezia\" title=\"Shlomo Venezia\">Shlomo Venezia</a>, Greek-Italian <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">Holocaust</a> survivor and author (b. 1923)", "links": [{"title": "Shlomo Venezia", "link": "https://wikipedia.org/wiki/Shlomo_Venezia"}, {"title": "Holocaust", "link": "https://wikipedia.org/wiki/Holocaust"}]}, {"year": "2013", "text": "<PERSON>, American lawyer, politician, and 21st United States Deputy Attorney General (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and 21st <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Attorney_General\" title=\"United States Deputy Attorney General\">United States Deputy Attorney General</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and 21st <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Attorney_General\" title=\"United States Deputy Attorney General\">United States Deputy Attorney General</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Deputy Attorney General", "link": "https://wikipedia.org/wiki/United_States_Deputy_Attorney_General"}]}, {"year": "2013", "text": "<PERSON>, American author (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American lighting designer (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Imer<PERSON>_Fi<PERSON>ntino\" title=\"Imero Fiorentino\"><PERSON><PERSON><PERSON></a>, American lighting designer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Imer<PERSON>_<PERSON>ntino\" title=\"Imero <PERSON>\"><PERSON><PERSON><PERSON></a>, American lighting designer (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>mer<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Polish-Israeli historian and author (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Israel_Gutman\" title=\"Israel Gutman\"><PERSON></a>, Polish-Israeli historian and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Gutman\" title=\"Israel Gutman\"><PERSON></a>, Polish-Israeli historian and author (b. 1923)", "links": [{"title": "Israel Gutman", "link": "https://wikipedia.org/wiki/Israel_Gutman"}]}, {"year": "2013", "text": "<PERSON>, Norwegian physician, academic, and politician (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>j%C3%B8s\" title=\"<PERSON>\"><PERSON></a>, Norwegian physician, academic, and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mj%C3%B8s\" title=\"<PERSON>\"><PERSON></a>, Norwegian physician, academic, and politician (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mj%C3%B8s"}]}, {"year": "2013", "text": "<PERSON>, American football player and coach (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, English singer-songwriter, pianist, and actress (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer-songwriter, pianist, and actress (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer-songwriter, pianist, and actress (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Israeli general and politician (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Shlomo_<PERSON>hat\" title=\"Shlomo Lahat\"><PERSON><PERSON><PERSON></a>, Israeli general and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S<PERSON><PERSON>_<PERSON>\" title=\"Shl<PERSON> Lahat\"><PERSON><PERSON><PERSON></a>, Israeli general and politician (b. 1927)", "links": [{"title": "Shlomo <PERSON>", "link": "https://wikipedia.org/wiki/Shlomo_<PERSON>hat"}]}, {"year": "2014", "text": "<PERSON>, Cuban-American baseball player and coach (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%<PERSON><PERSON><PERSON>_(infielder)\" class=\"mw-redirect\" title=\"<PERSON> (infielder)\"><PERSON></a>, Cuban-American baseball player and coach (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%<PERSON><PERSON><PERSON>_(infielder)\" class=\"mw-redirect\" title=\"<PERSON> (infielder)\"><PERSON></a>, Cuban-American baseball player and coach (b. 1942)", "links": [{"title": "<PERSON> (infielder)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD<PERSON><PERSON>_(infielder)"}]}, {"year": "2014", "text": "<PERSON>, Venezuelan criminologist and politician (b. 1987)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan criminologist and politician (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan criminologist and politician (b. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Croatian footballer (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Bo%C5%BEo_Bakota\" title=\"Božo Bakota\"><PERSON><PERSON><PERSON></a>, Croatian footballer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bo%C5%BEo_Bakota\" title=\"Božo Bakota\"><PERSON><PERSON><PERSON></a>, Croatian footballer (b. 1950)", "links": [{"title": "Božo Bakota", "link": "https://wikipedia.org/wiki/Bo%C5%BEo_Bakota"}]}, {"year": "2015", "text": "<PERSON>, American soldier, lawyer, and politician (b. 1915)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Iranian footballer (b. 1985)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian footballer (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian footballer (b. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American rabbi and academic, co-founded American Jewish University (b. 1919)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rabbi and academic, co-founded <a href=\"https://wikipedia.org/wiki/American_Jewish_University\" title=\"American Jewish University\">American Jewish University</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rabbi and academic, co-founded <a href=\"https://wikipedia.org/wiki/American_Jewish_University\" title=\"American Jewish University\">American Jewish University</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacob_Pressman"}, {"title": "American Jewish University", "link": "https://wikipedia.org/wiki/American_Jewish_University"}]}, {"year": "2017", "text": "<PERSON>, American sportscaster (b. 1955)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, French-Armenian singer, composer, writer, filmmaker and public figure (b. 1924)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Armenian singer, composer, writer, filmmaker and public figure (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Armenian singer, composer, writer, filmmaker and public figure (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Сzeсh singer (b. 1939)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON><PERSON><PERSON><PERSON> singer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON><PERSON><PERSON><PERSON> singer (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Japanese professional wrestler and politician (b. 1943)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese professional wrestler and politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese professional wrestler and politician (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American professional baseball player (b. 1966)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional baseball player (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional baseball player (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, English lawyer and politician (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}