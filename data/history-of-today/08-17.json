{"date": "August 17", "url": "https://wikipedia.org/wiki/August_17", "data": {"Events": [{"year": "310", "text": "Pope <PERSON><PERSON><PERSON> dies, possibly from a hunger strike, shortly after being banished by the Emperor <PERSON><PERSON><PERSON> to Sicily.", "html": "310 - <a href=\"https://wikipedia.org/wiki/<PERSON>_E<PERSON>bius\" title=\"<PERSON> Eusebius\">Pope <PERSON></a> dies, possibly from a <a href=\"https://wikipedia.org/wiki/Hunger_strike\" title=\"Hunger strike\">hunger strike</a>, shortly after being banished by the Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Sicilia_(Roman_province)\" title=\"Sicilia (Roman province)\">Sicily</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_E<PERSON>\" title=\"<PERSON> E<PERSON>bius\"><PERSON></a> dies, possibly from a <a href=\"https://wikipedia.org/wiki/Hunger_strike\" title=\"Hunger strike\">hunger strike</a>, shortly after being banished by the Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Sicilia_(Roman_province)\" title=\"Sicilia (Roman province)\">Sicily</a>.", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>bius"}, {"title": "Hunger strike", "link": "https://wikipedia.org/wiki/Hunger_strike"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ent<PERSON>"}, {"title": "Sicilia (Roman province)", "link": "https://wikipedia.org/wiki/Sicilia_(Roman_province)"}]}, {"year": "682", "text": "<PERSON> begins his pontificate.", "html": "682 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Leo II\">Pope <PERSON> II</a> begins his pontificate.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Leo <PERSON>\">Pope <PERSON> II</a> begins his pontificate.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "986", "text": "Byzantine-Bulgarian wars: Battle of the Gates of Trajan: The Bulgarians under the <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> defeat the Byzantine forces at the Gate of Trajan, with Byzantine Emperor <PERSON> barely escaping.", "html": "986 - <a href=\"https://wikipedia.org/wiki/Byzantine%E2%80%93Bulgarian_wars\" title=\"Byzantine-Bulgarian wars\">Byzantine-Bulgarian wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Gates_of_Trajan\" title=\"Battle of the Gates of Trajan\">Battle of the Gates of Trajan</a>: The <a href=\"https://wikipedia.org/wiki/First_Bulgarian_Empire\" title=\"First Bulgarian Empire\">Bulgarians</a> under the <a href=\"https://wikipedia.org/wiki/Cometopuli_dynasty\" title=\"Cometopuli dynasty\">Comitopuli</a> <a href=\"https://wikipedia.org/wiki/Samuel_of_Bulgaria\" title=\"Samuel of Bulgaria\">Samuel</a> and <a href=\"https://wikipedia.org/wiki/Aron_(noble)\" class=\"mw-redirect\" title=\"<PERSON>ron (noble)\"><PERSON>ron</a> defeat the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> forces at the <a href=\"https://wikipedia.org/wiki/Gate_of_Trajan\" title=\"Gate of Trajan\">Gate of Trajan</a>, with <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/Basil_II\" title=\"Basil II\">Basil II</a> barely escaping.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byzantine%E2%80%93Bulgarian_wars\" title=\"Byzantine-Bulgarian wars\">Byzantine-Bulgarian wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Gates_of_Trajan\" title=\"Battle of the Gates of Trajan\">Battle of the Gates of Trajan</a>: The <a href=\"https://wikipedia.org/wiki/First_Bulgarian_Empire\" title=\"First Bulgarian Empire\">Bulgarians</a> under the <a href=\"https://wikipedia.org/wiki/Cometopuli_dynasty\" title=\"Cometopuli dynasty\">Comitopuli</a> <a href=\"https://wikipedia.org/wiki/Samuel_of_Bulgaria\" title=\"Samuel of Bulgaria\">Samuel</a> and <a href=\"https://wikipedia.org/wiki/Aron_(noble)\" class=\"mw-redirect\" title=\"<PERSON>ron (noble)\"><PERSON>ron</a> defeat the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> forces at the <a href=\"https://wikipedia.org/wiki/Gate_of_Trajan\" title=\"Gate of Trajan\">Gate of Trajan</a>, with <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/Basil_II\" title=\"Basil II\">Basil II</a> barely escaping.", "links": [{"title": "Byzantine-Bulgarian wars", "link": "https://wikipedia.org/wiki/Byzantine%E2%80%93Bulgarian_wars"}, {"title": "Battle of the Gates of Trajan", "link": "https://wikipedia.org/wiki/Battle_of_the_Gates_of_<PERSON><PERSON><PERSON>"}, {"title": "First Bulgarian Empire", "link": "https://wikipedia.org/wiki/First_Bulgarian_Empire"}, {"title": "Cometopuli dynasty", "link": "https://wikipedia.org/wiki/Cometopuli_dynasty"}, {"title": "Samuel of Bulgaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bulgaria"}, {"title": "<PERSON><PERSON> (noble)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(noble)"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "Gate of Trajan", "link": "https://wikipedia.org/wiki/<PERSON>_of_<PERSON><PERSON><PERSON>"}, {"title": "Byzantine Emperor", "link": "https://wikipedia.org/wiki/Byzantine_Emperor"}, {"title": "Basil II", "link": "https://wikipedia.org/wiki/Basil_II"}]}, {"year": "1186", "text": "Georgenberg Pact: <PERSON><PERSON>, Duke of Styria and <PERSON>, Duke of Austria sign a heritage agreement in which <PERSON><PERSON> gives his duchy to <PERSON> and to his son <PERSON> under the stipulation that Austria and Styria would henceforth remain undivided.", "html": "1186 - <a href=\"https://wikipedia.org/wiki/George<PERSON>_Pact\" title=\"Georgenberg Pact\">Georgenberg Pact</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_IV,_Duke_of_Styria\" title=\"<PERSON><PERSON> IV, Duke of Styria\"><PERSON><PERSON> IV, Duke of Styria</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> sign a heritage agreement in which <PERSON><PERSON> gives his duchy to <PERSON> and to his son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria_(Babenberg)\" title=\"<PERSON>, Duke of Austria (Babenberg)\"><PERSON></a> under the stipulation that Austria and Styria would henceforth remain undivided.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George<PERSON>_Pact\" title=\"Georgenberg Pact\">Georgenberg Pact</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Styria\" title=\"<PERSON><PERSON> IV, Duke of Styria\"><PERSON><PERSON> IV, Duke of Styria</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> sign a heritage agreement in which <PERSON><PERSON> gives his duchy to <PERSON> and to his son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria_(Babenberg)\" title=\"<PERSON>, Duke of Austria (Babenberg)\"><PERSON></a> under the stipulation that Austria and Styria would henceforth remain undivided.", "links": [{"title": "Georgenberg Pact", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Pact"}, {"title": "<PERSON><PERSON>, Duke of Styria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_IV,_Duke_of_Styria"}, {"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/Leopold_<PERSON>,_Duke_of_Austria"}, {"title": "<PERSON>, Duke of Austria (Babenberg)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria_(Babenberg)"}]}, {"year": "1386", "text": "<PERSON>, the ruler of Princedom of Albania forges an alliance with the Republic of Venice, committing to participate in all wars of the Republic and receiving coastal protection against the Ottomans in return.", "html": "1386 - <a href=\"https://wikipedia.org/wiki/Karl_<PERSON>\" class=\"mw-redirect\" title=\"Karl Topia\"><PERSON></a>, the ruler of <a href=\"https://wikipedia.org/wiki/Princedom_of_Albania\" class=\"mw-redirect\" title=\"Princedom of Albania\">Princedom of Albania</a> forges an alliance with the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>, committing to participate in all wars of the Republic and receiving coastal protection against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottomans</a> in return.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karl_<PERSON>\" class=\"mw-redirect\" title=\"Karl Top<PERSON>\"><PERSON></a>, the ruler of <a href=\"https://wikipedia.org/wiki/Princedom_of_Albania\" class=\"mw-redirect\" title=\"Princedom of Albania\">Princedom of Albania</a> forges an alliance with the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>, committing to participate in all wars of the Republic and receiving coastal protection against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottomans</a> in return.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_Topia"}, {"title": "Princedom of Albania", "link": "https://wikipedia.org/wiki/Princedom_of_Albania"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1424", "text": "Hundred Years' War: Battle of Verneuil: An English force under <PERSON>, Duke of Bedford defeats a larger French army under <PERSON>, Duke of Alençon, <PERSON>, and Earl <PERSON> of Douglas.", "html": "1424 - <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Verneuil\" title=\"Battle of Verneuil\">Battle of Verneuil</a>: An English force under <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Bedford\"><PERSON>, Duke of Bedford</a> defeats a larger French army under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Alen%C3%A7on\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Alençon\"><PERSON>, Duke of Alençon</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>_Buch<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Earl of Buchan\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Earl <PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Verneuil\" title=\"Battle of Verneuil\">Battle of Verneuil</a>: An English force under <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Bedford\"><PERSON>, Duke of Bedford</a> defeats a larger French army under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Alen%C3%A7on\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Alençon\"><PERSON>, Duke of Alençon</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>_Buch<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Earl of Buchan\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Earl <PERSON>\">Earl <PERSON></a>.", "links": [{"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}, {"title": "Battle of Verneuil", "link": "https://wikipedia.org/wiki/Battle_of_Verneuil"}, {"title": "<PERSON>, Duke of Bedford", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_of_Bedford"}, {"title": "<PERSON>, Duke of Alençon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_Alen%C3%A7on"}, {"title": "<PERSON>, 2nd Earl of Buchan", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>_B<PERSON>"}, {"title": "<PERSON>, 4th Earl of Douglas", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_<PERSON>_<PERSON>"}]}, {"year": "1488", "text": "<PERSON>, the Bishop of Turku, marks the date of his preface to Missale Aboense, the oldest known book of Finland.", "html": "1488 - <a href=\"https://wikipedia.org/wiki/List_of_bishops_of_Turku#Catholic_bishops_of_Turku_(%C3%85bo,_Aboa)\" title=\"List of bishops of Turku\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Archdiocese_of_Turku\" title=\"Archdiocese of Turku\">Bishop of Turku</a>, marks the date of his preface to <i><a href=\"https://wikipedia.org/wiki/Missale_Aboense\" title=\"Missale Aboense\">Missale Aboense</a></i>, the oldest known book of <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/List_of_bishops_of_Turku#Catholic_bishops_of_Turku_(%C3%85bo,_Aboa)\" title=\"List of bishops of Turku\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Archdiocese_of_Turku\" title=\"Archdiocese of Turku\">Bishop of Turku</a>, marks the date of his preface to <i><a href=\"https://wikipedia.org/wiki/Missale_Aboense\" title=\"Missale Aboense\">Missale Aboense</a></i>, the oldest known book of <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "links": [{"title": "List of bishops of Turku", "link": "https://wikipedia.org/wiki/List_of_bishops_of_Turku#Catholic_bishops_of_Turku_(Å<PERSON>,_Aboa)"}, {"title": "Archdiocese of Turku", "link": "https://wikipedia.org/wiki/Archdiocese_of_Turku"}, {"title": "Missale Aboense", "link": "https://wikipedia.org/wiki/Missale_Aboense"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}]}, {"year": "1498", "text": "<PERSON><PERSON><PERSON>, son of Pope <PERSON>, becomes the first person in history to resign the cardinalate; later that same day, King <PERSON> of France names him Duke of Valentinois.", "html": "1498 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a>, becomes the first person in history to resign the <a href=\"https://wikipedia.org/wiki/Cardinal_(Catholic_Church)\" title=\"Cardinal (Catholic Church)\">cardinalate</a>; later that same day, King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a> names him <a href=\"https://wikipedia.org/wiki/Duke_of_Valentinois\" title=\"Duke of Valentinois\">Duke of Valentinois</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> VI\">Pope <PERSON> VI</a>, becomes the first person in history to resign the <a href=\"https://wikipedia.org/wiki/Cardinal_(Catholic_Church)\" title=\"Cardinal (Catholic Church)\">cardinalate</a>; later that same day, King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a> names him <a href=\"https://wikipedia.org/wiki/Duke_of_Valentinois\" title=\"Duke of Valentinois\">Duke of Valentinois</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_<PERSON>rgia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> (Catholic Church)", "link": "https://wikipedia.org/wiki/<PERSON>_(Catholic_Church)"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_<PERSON>_of_France"}, {"title": "Duke of Valentinois", "link": "https://wikipedia.org/wiki/<PERSON>_of_Valentinois"}]}, {"year": "1549", "text": "Battle of Sampford Courtenay: The Prayer Book Rebellion is quashed in England.", "html": "1549 - <a href=\"https://wikipedia.org/wiki/Battle_of_Sampford_Courtenay\" title=\"Battle of Sampford Courtenay\">Battle of Sampford Courtenay</a>: The <a href=\"https://wikipedia.org/wiki/Prayer_Book_Rebellion\" title=\"Prayer Book Rebellion\">Prayer Book Rebellion</a> is quashed in <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Sampford_Courtenay\" title=\"Battle of Sampford Courtenay\">Battle of Sampford Courtenay</a>: The <a href=\"https://wikipedia.org/wiki/Prayer_Book_Rebellion\" title=\"Prayer Book Rebellion\">Prayer Book Rebellion</a> is quashed in <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "links": [{"title": "Battle of Sampford Courtenay", "link": "https://wikipedia.org/wiki/Battle_of_Sampford_Courtenay"}, {"title": "Prayer Book Rebellion", "link": "https://wikipedia.org/wiki/Prayer_Book_Rebellion"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}]}, {"year": "1560", "text": "The Catholic Church is overthrown and Protestantism is established as the national religion in Scotland.", "html": "1560 - The <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> is overthrown and <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestantism</a> is <a href=\"https://wikipedia.org/wiki/Scottish_Reformation_Parliament\" title=\"Scottish Reformation Parliament\">established</a> as the national religion in <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> is overthrown and <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestantism</a> is <a href=\"https://wikipedia.org/wiki/Scottish_Reformation_Parliament\" title=\"Scottish Reformation Parliament\">established</a> as the national religion in <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a>.", "links": [{"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "Protestantism", "link": "https://wikipedia.org/wiki/Protestantism"}, {"title": "Scottish Reformation Parliament", "link": "https://wikipedia.org/wiki/Scottish_Reformation_Parliament"}, {"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}]}, {"year": "1585", "text": "Eighty Years' War: Siege of Antwerp: Antwerp is captured by Spanish forces under <PERSON>, Duke of Parma, who orders Protestants to leave the city and as a result over half of the 100,000 inhabitants flee to the northern provinces.", "html": "1585 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Antwerp_(1584%E2%80%9385)\" class=\"mw-redirect\" title=\"Siege of Antwerp (1584-85)\">Siege of Antwerp</a>: <a href=\"https://wikipedia.org/wiki/Antwerp\" title=\"Antwerp\">Antwerp</a> is captured by <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish</a> forces under <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a>, who orders <a href=\"https://wikipedia.org/wiki/Protestants\" class=\"mw-redirect\" title=\"Protestants\">Protestants</a> to leave the city and as a result over half of the 100,000 inhabitants flee to the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">northern provinces</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Antwerp_(1584%E2%80%9385)\" class=\"mw-redirect\" title=\"Siege of Antwerp (1584-85)\">Siege of Antwerp</a>: <a href=\"https://wikipedia.org/wiki/Antwerp\" title=\"Antwerp\">Antwerp</a> is captured by <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish</a> forces under <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a>, who orders <a href=\"https://wikipedia.org/wiki/Protestants\" class=\"mw-redirect\" title=\"Protestants\">Protestants</a> to leave the city and as a result over half of the 100,000 inhabitants flee to the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">northern provinces</a>.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Siege of Antwerp (1584-85)", "link": "https://wikipedia.org/wiki/Siege_of_Antwerp_(1584%E2%80%9385)"}, {"title": "Antwerp", "link": "https://wikipedia.org/wiki/Antwerp"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}, {"title": "<PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma"}, {"title": "Protestants", "link": "https://wikipedia.org/wiki/Protestants"}, {"title": "Dutch Republic", "link": "https://wikipedia.org/wiki/Dutch_Republic"}]}, {"year": "1585", "text": "A first group of colonists sent by Sir <PERSON> under the charge of <PERSON> lands in the New World to create Roanoke Colony on Roanoke Island, off the coast of present-day North Carolina.", "html": "1585 - A first group of colonists sent by <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\">Sir <PERSON></a> under the charge of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in the <a href=\"https://wikipedia.org/wiki/New_World\" title=\"New World\">New World</a> to create <a href=\"https://wikipedia.org/wiki/Roanoke_Colony\" title=\"Roanoke Colony\">Roanoke Colony</a> on <a href=\"https://wikipedia.org/wiki/Roanoke_Island\" title=\"Roanoke Island\">Roanoke Island</a>, off the coast of present-day <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>.", "no_year_html": "A first group of colonists sent by <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\">Sir <PERSON></a> under the charge of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in the <a href=\"https://wikipedia.org/wiki/New_World\" title=\"New World\">New World</a> to create <a href=\"https://wikipedia.org/wiki/Roanoke_Colony\" title=\"Roanoke Colony\">Roanoke Colony</a> on <a href=\"https://wikipedia.org/wiki/Roanoke_Island\" title=\"Roanoke Island\">Roanoke Island</a>, off the coast of present-day <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>.", "links": [{"title": "Sir <PERSON>", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New World", "link": "https://wikipedia.org/wiki/New_World"}, {"title": "Roanoke Colony", "link": "https://wikipedia.org/wiki/Roanoke_Colony"}, {"title": "Roanoke Island", "link": "https://wikipedia.org/wiki/Roanoke_Island"}, {"title": "North Carolina", "link": "https://wikipedia.org/wiki/North_Carolina"}]}, {"year": "1597", "text": "Islands Voyage: <PERSON>, 2nd Earl of Essex, and Sir <PERSON> set sail on an expedition to the Azores.", "html": "1597 - <a href=\"https://wikipedia.org/wiki/Islands_Voyage\" title=\"Islands Voyage\">Islands Voyage</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Essex\" title=\"<PERSON>, 2nd Earl of Essex\"><PERSON>, 2nd Earl of Essex</a>, and Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Walter <PERSON>\"><PERSON></a> set sail on an expedition to the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Islands_Voyage\" title=\"Islands Voyage\">Islands Voyage</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Essex\" title=\"<PERSON>, 2nd Earl of Essex\"><PERSON>, 2nd Earl of Essex</a>, and Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Walter <PERSON>\"><PERSON></a> set sail on an expedition to the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a>.", "links": [{"title": "Islands Voyage", "link": "https://wikipedia.org/wiki/Islands_Voyage"}, {"title": "<PERSON>, 2nd Earl of Essex", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Essex"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Raleigh"}, {"title": "Azores", "link": "https://wikipedia.org/wiki/Azores"}]}, {"year": "1668", "text": "The magnitude 8.0 North Anatolia earthquake causes 8,000 deaths in northern Anatolia, Ottoman Empire.", "html": "1668 - The magnitude 8.0 <a href=\"https://wikipedia.org/wiki/1668_North_Anatolia_earthquake\" title=\"1668 North Anatolia earthquake\">North Anatolia earthquake</a> causes 8,000 deaths in northern <a href=\"https://wikipedia.org/wiki/Anatolia\" title=\"Anatolia\">Anatolia</a>, <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "The magnitude 8.0 <a href=\"https://wikipedia.org/wiki/1668_North_Anatolia_earthquake\" title=\"1668 North Anatolia earthquake\">North Anatolia earthquake</a> causes 8,000 deaths in northern <a href=\"https://wikipedia.org/wiki/Anatolia\" title=\"Anatolia\">Anatolia</a>, <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "1668 North Anatolia earthquake", "link": "https://wikipedia.org/wiki/1668_North_Anatolia_earthquake"}, {"title": "Anatolia", "link": "https://wikipedia.org/wiki/Anatolia"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1717", "text": "Austro-Turkish War of 1716-18: The month-long Siege of Belgrade ends with Prince <PERSON> of Savoy's Austrian troops capturing the city from the Ottoman Empire.", "html": "1717 - <a href=\"https://wikipedia.org/wiki/Austro-Turkish_War_of_1716%E2%80%9318\" class=\"mw-redirect\" title=\"Austro-Turkish War of 1716-18\">Austro-Turkish War of 1716-18</a>: The month-long <a href=\"https://wikipedia.org/wiki/Siege_of_Belgrade_(1717)\" title=\"Siege of Belgrade (1717)\">Siege of Belgrade</a> ends with <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Savoy\" title=\"Prince <PERSON> of Savoy\">Prince <PERSON> of Savoy</a>'s <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Austrian</a> troops capturing the city from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austro-Turkish_War_of_1716%E2%80%9318\" class=\"mw-redirect\" title=\"Austro-Turkish War of 1716-18\">Austro-Turkish War of 1716-18</a>: The month-long <a href=\"https://wikipedia.org/wiki/Siege_of_Belgrade_(1717)\" title=\"Siege of Belgrade (1717)\">Siege of Belgrade</a> ends with <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Savoy\" title=\"Prince <PERSON> of Savoy\">Prince <PERSON> of Savoy</a>'s <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Austrian</a> troops capturing the city from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "Austro-Turkish War of 1716-18", "link": "https://wikipedia.org/wiki/Austro-Turkish_War_of_1716%E2%80%9318"}, {"title": "Siege of Belgrade (1717)", "link": "https://wikipedia.org/wiki/Siege_of_Belgrade_(1717)"}, {"title": "Prince <PERSON> of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Savoy"}, {"title": "Habsburg monarchy", "link": "https://wikipedia.org/wiki/Habsburg_monarchy"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1723", "text": "<PERSON><PERSON> becomes Bishop of Făgăraș and is festively installed in his position at the St. Nicolas Cathedral in Făgăraș, after being formally confirmed earlier by <PERSON>.", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Bishop_of_F%C4%83g%C4%83ra%C8%99\" class=\"mw-redirect\" title=\"Bishop of Făgăraș\">Bishop of Făgăraș</a> and is festively installed in his position at the St. Nicolas Cathedral in <a href=\"https://wikipedia.org/wiki/F%C4%83g%C4%83ra%C8%99\" title=\"Făgăraș\">Făgăraș</a>, after being formally confirmed earlier by <a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_XI\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Bishop_of_F%C4%83g%C4%83ra%C8%99\" class=\"mw-redirect\" title=\"Bishop of Făgăraș\">Bishop of Făgăraș</a> and is festively installed in his position at the St. Nicolas Cathedral in <a href=\"https://wikipedia.org/wiki/F%C4%83g%C4%83ra%C8%99\" title=\"Făgăraș\">F<PERSON>g<PERSON><PERSON><PERSON></a>, after being formally confirmed earlier by <a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Bishop of Făgăraș", "link": "https://wikipedia.org/wiki/Bishop_of_F%C4%83g%C4%83ra%C8%99"}, {"title": "Făgăraș", "link": "https://wikipedia.org/wiki/F%C4%83g%C4%83ra%C8%99"}, {"title": "Pope <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON> <PERSON>, previously known as <PERSON><PERSON><PERSON>, succeeds <PERSON> as the 247th Pope.", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a>, previously known as <PERSON><PERSON><PERSON>, succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_XII\" class=\"mw-redirect\" title=\"<PERSON> XII\"><PERSON></a> as the 247th Pope.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Benedict <PERSON>\">Pope <PERSON></a>, previously known as <PERSON><PERSON><PERSON>, succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_XII\" class=\"mw-redirect\" title=\"<PERSON> XII\"><PERSON></a> as the 247th Pope.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Clement <PERSON>", "link": "https://wikipedia.org/wiki/Clement_XII"}]}, {"year": "1784", "text": "Classical composer <PERSON> receives a pay rise of 12,000 reals from his employer, the <PERSON><PERSON><PERSON>, Count of Chinchón.", "html": "1784 - Classical composer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a pay rise of 12,000 <a href=\"https://wikipedia.org/wiki/Spanish_real\" title=\"Spanish real\">reals</a> from his employer, the <a href=\"https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>,_Count_of_Chinch%C3%B3n\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Count of Chinchón\"><PERSON><PERSON><PERSON>, Count of Chinchón</a>.", "no_year_html": "Classical composer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a pay rise of 12,000 <a href=\"https://wikipedia.org/wiki/Spanish_real\" title=\"Spanish real\">reals</a> from his employer, the <a href=\"https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>,_Count_of_Chinch%C3%B3n\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Count of Chinchón\"><PERSON><PERSON><PERSON>, Count of Chinchón</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Spanish real", "link": "https://wikipedia.org/wiki/Spanish_real"}, {"title": "<PERSON><PERSON><PERSON>, Count of Chinchón", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_<PERSON><PERSON>%C3%B3n"}]}, {"year": "1798", "text": "The Vietnamese Catholics report a Marian apparition in Quảng Trị, an event which is called Our Lady of La Vang.", "html": "1798 - The <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> Catholics report a <a href=\"https://wikipedia.org/wiki/Marian_apparition\" title=\"Marian apparition\">Marian apparition</a> in <a href=\"https://wikipedia.org/wiki/Qu%E1%BA%A3ng_Tr%E1%BB%8B\" title=\"Quảng Trị\">Quảng Trị</a>, an event which is called <a href=\"https://wikipedia.org/wiki/Our_Lady_of_La_Vang\" title=\"Our Lady of La Vang\">Our Lady of La Vang</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> Catholics report a <a href=\"https://wikipedia.org/wiki/Marian_apparition\" title=\"Marian apparition\">Marian apparition</a> in <a href=\"https://wikipedia.org/wiki/Qu%E1%BA%A3ng_Tr%E1%BB%8B\" title=\"Quảng Trị\">Quảng Trị</a>, an event which is called <a href=\"https://wikipedia.org/wiki/Our_Lady_of_La_Vang\" title=\"Our Lady of La Vang\">Our Lady of La Vang</a>.", "links": [{"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}, {"title": "Marian apparition", "link": "https://wikipedia.org/wiki/Marian_apparition"}, {"title": "Quảng Trị", "link": "https://wikipedia.org/wiki/Qu%E1%BA%A3ng_Tr%E1%BB%8B"}, {"title": "Our Lady of La Vang", "link": "https://wikipedia.org/wiki/Our_Lady_of_<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>'s North River Steamboat leaves New York City for Albany, New York, on the Hudson River, inaugurating the first commercial steamboat service in the world.", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/North_River_Steamboat\" title=\"North River Steamboat\">North River Steamboat</a></i> leaves New York City for <a href=\"https://wikipedia.org/wiki/Albany,_New_York\" title=\"Albany, New York\">Albany, New York</a>, on the <a href=\"https://wikipedia.org/wiki/Hudson_River\" title=\"Hudson River\">Hudson River</a>, inaugurating the first commercial <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamboat</a> service in the world.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/North_River_Steamboat\" title=\"North River Steamboat\">North River Steamboat</a></i> leaves New York City for <a href=\"https://wikipedia.org/wiki/Albany,_New_York\" title=\"Albany, New York\">Albany, New York</a>, on the <a href=\"https://wikipedia.org/wiki/Hudson_River\" title=\"Hudson River\">Hudson River</a>, inaugurating the first commercial <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamboat</a> service in the world.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "North River Steamboat", "link": "https://wikipedia.org/wiki/North_River_Steamboat"}, {"title": "Albany, New York", "link": "https://wikipedia.org/wiki/Albany,_New_York"}, {"title": "Hudson River", "link": "https://wikipedia.org/wiki/Hudson_River"}, {"title": "Steamboat", "link": "https://wikipedia.org/wiki/Steamboat"}]}, {"year": "1808", "text": "The Finnish War: The Battle of Alavus is fought.", "html": "1808 - The <a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Alavus\" title=\"Battle of Alavus\">Battle of Alavus</a> is fought.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Alavus\" title=\"Battle of Alavus\">Battle of Alavus</a> is fought.", "links": [{"title": "Finnish War", "link": "https://wikipedia.org/wiki/Finnish_War"}, {"title": "Battle of Alavus", "link": "https://wikipedia.org/wiki/Battle_of_Alavus"}]}, {"year": "1827", "text": "Dutch King <PERSON> and <PERSON> <PERSON> sign concord.", "html": "1827 - Dutch King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands\" title=\"William I of the Netherlands\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_Leo<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> sign concord.", "no_year_html": "Dutch King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands\" title=\"William I of the Netherlands\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> sign concord.", "links": [{"title": "<PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "British parliament accepts registration of births, marriages and deaths.", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom\" title=\"Parliament of the United Kingdom\">British parliament</a> <a href=\"https://wikipedia.org/wiki/Marriage_Act_1836\" title=\"Marriage Act 1836\">accepts</a> registration of births, marriages and deaths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom\" title=\"Parliament of the United Kingdom\">British parliament</a> <a href=\"https://wikipedia.org/wiki/Marriage_Act_1836\" title=\"Marriage Act 1836\">accepts</a> registration of births, marriages and deaths.", "links": [{"title": "Parliament of the United Kingdom", "link": "https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom"}, {"title": "Marriage Act 1836", "link": "https://wikipedia.org/wiki/Marriage_Act_1836"}]}, {"year": "1862", "text": "American Indian Wars: The Dakota War of 1862 begins in Minnesota as Dakota warriors attack white settlements along the Minnesota River.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: The <a href=\"https://wikipedia.org/wiki/Dakota_War_of_1862\" title=\"Dakota War of 1862\">Dakota War of 1862</a> begins in <a href=\"https://wikipedia.org/wiki/Minnesota\" title=\"Minnesota\">Minnesota</a> as <a href=\"https://wikipedia.org/wiki/Dakota_people\" title=\"Dakota people\">Dakota</a> warriors attack white settlements along the <a href=\"https://wikipedia.org/wiki/Minnesota_River\" title=\"Minnesota River\">Minnesota River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: The <a href=\"https://wikipedia.org/wiki/Dakota_War_of_1862\" title=\"Dakota War of 1862\">Dakota War of 1862</a> begins in <a href=\"https://wikipedia.org/wiki/Minnesota\" title=\"Minnesota\">Minnesota</a> as <a href=\"https://wikipedia.org/wiki/Dakota_people\" title=\"Dakota people\">Dakota</a> warriors attack white settlements along the <a href=\"https://wikipedia.org/wiki/Minnesota_River\" title=\"Minnesota River\">Minnesota River</a>.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Dakota War of 1862", "link": "https://wikipedia.org/wiki/Dakota_War_of_1862"}, {"title": "Minnesota", "link": "https://wikipedia.org/wiki/Minnesota"}, {"title": "Dakota people", "link": "https://wikipedia.org/wiki/Dakota_people"}, {"title": "Minnesota River", "link": "https://wikipedia.org/wiki/Minnesota_River"}]}, {"year": "1862", "text": "American Civil War: Major General <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> is assigned command of all the cavalry of the Confederate Army of Northern Virginia.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Major General <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a> is assigned command of all the cavalry of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Major General <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a> is assigned command of all the cavalry of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "J. E<PERSON> <PERSON><PERSON> Stuart", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Army of Northern Virginia", "link": "https://wikipedia.org/wiki/Army_of_Northern_Virginia"}]}, {"year": "1863", "text": "American Civil War: In Charleston, South Carolina, Union batteries and ships bombard Confederate-held Fort Sumter.", "html": "1863 - American Civil War: In <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a>, <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> batteries and ships bombard Confederate-held <a href=\"https://wikipedia.org/wiki/Fort_Sumter\" title=\"Fort Sumter\">Fort Sumter</a>.", "no_year_html": "American Civil War: In <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a>, <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> batteries and ships bombard Confederate-held <a href=\"https://wikipedia.org/wiki/Fort_Sumter\" title=\"Fort Sumter\">Fort Sumter</a>.", "links": [{"title": "Charleston, South Carolina", "link": "https://wikipedia.org/wiki/Charleston,_South_Carolina"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Fort Sumter", "link": "https://wikipedia.org/wiki/Fort_Sumter"}]}, {"year": "1864", "text": "American Civil War: Battle of Gainesville: Confederate forces defeat Union troops near Gainesville, Florida.", "html": "1864 - American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Gainesville\" title=\"Battle of Gainesville\">Battle of Gainesville</a>: Confederate forces defeat Union troops near <a href=\"https://wikipedia.org/wiki/Gainesville,_Florida\" title=\"Gainesville, Florida\">Gainesville, Florida</a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Gainesville\" title=\"Battle of Gainesville\">Battle of Gainesville</a>: Confederate forces defeat Union troops near <a href=\"https://wikipedia.org/wiki/Gainesville,_Florida\" title=\"Gainesville, Florida\">Gainesville, Florida</a>.", "links": [{"title": "Battle of Gainesville", "link": "https://wikipedia.org/wiki/Battle_of_Gainesville"}, {"title": "Gainesville, Florida", "link": "https://wikipedia.org/wiki/Gainesville,_Florida"}]}, {"year": "1866", "text": "The Grand Duchy of Baden announces its withdrawal from the German Confederation and signs a treaty of peace and alliance with Prussia.", "html": "1866 - The <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Baden\" title=\"Grand Duchy of Baden\">Grand Duchy of Baden</a> announces its withdrawal from the <a href=\"https://wikipedia.org/wiki/German_Confederation\" title=\"German Confederation\">German Confederation</a> and signs a <a href=\"https://wikipedia.org/wiki/History_of_Baden-W%C3%BCrttemberg\" title=\"History of Baden-Württemberg\">treaty of peace and alliance</a> with <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Baden\" title=\"Grand Duchy of Baden\">Grand Duchy of Baden</a> announces its withdrawal from the <a href=\"https://wikipedia.org/wiki/German_Confederation\" title=\"German Confederation\">German Confederation</a> and signs a <a href=\"https://wikipedia.org/wiki/History_of_Baden-W%C3%BCrttemberg\" title=\"History of Baden-Württemberg\">treaty of peace and alliance</a> with <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>.", "links": [{"title": "Grand Duchy of Baden", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Baden"}, {"title": "German Confederation", "link": "https://wikipedia.org/wiki/German_Confederation"}, {"title": "History of Baden-Württemberg", "link": "https://wikipedia.org/wiki/History_of_Baden-W%C3%BCrttemberg"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}]}, {"year": "1876", "text": "<PERSON>'s <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, the last opera in his Ringcycle, premieres at the Bayreuth Festspielhaus.", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/G%C3%B6tterd%C3%A4mmerung\" title=\"G<PERSON>tterdämmerung\">Götterdämmerung</a></i>, the last opera in his <i><a href=\"https://wikipedia.org/wiki/Der_Ring_des_Nibelungen\" title=\"Der Ring des Nibelungen\">Ring</a></i>cycle, premieres at the <a href=\"https://wikipedia.org/wiki/Bayreuth_Festspielhaus\" title=\"Bayreuth Festspielhaus\">Bayreuth Festspielhaus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/G%C3%B6tterd%C3%A4mmerung\" title=\"G<PERSON>tterdämmerung\">G<PERSON>tterd<PERSON>mmerung</a></i>, the last opera in his <i><a href=\"https://wikipedia.org/wiki/Der_Ring_des_Nibelungen\" title=\"Der Ring des Nibelungen\">Ring</a></i>cycle, premieres at the <a href=\"https://wikipedia.org/wiki/Bayreuth_Festspielhaus\" title=\"Bayreuth Festspielhaus\">Bayreuth Festspielhaus</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Götterdämmerung", "link": "https://wikipedia.org/wiki/G%C3%B6tterd%C3%A4mmerung"}, {"title": "Der Ring des Nibelungen", "link": "https://wikipedia.org/wiki/Der_Ring_des_Nibelungen"}, {"title": "Bayreuth Festspielhaus", "link": "https://wikipedia.org/wiki/Bayreuth_Festspielhaus"}]}, {"year": "1883", "text": "The first public performance of the Dominican Republic's national anthem, Himno Nacional.", "html": "1883 - The first public performance of the <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a>'s <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a>, <i><a href=\"https://wikipedia.org/wiki/National_Anthem_of_the_Dominican_Republic\" title=\"National Anthem of the Dominican Republic\">Himno Nacional</a></i>.", "no_year_html": "The first public performance of the <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a>'s <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a>, <i><a href=\"https://wikipedia.org/wiki/National_Anthem_of_the_Dominican_Republic\" title=\"National Anthem of the Dominican Republic\">Himno Nacional</a></i>.", "links": [{"title": "Dominican Republic", "link": "https://wikipedia.org/wiki/Dominican_Republic"}, {"title": "National anthem", "link": "https://wikipedia.org/wiki/National_anthem"}, {"title": "National Anthem of the Dominican Republic", "link": "https://wikipedia.org/wiki/National_Anthem_of_the_Dominican_Republic"}]}, {"year": "1896", "text": "<PERSON> became the first recorded case of a pedestrian killed in a collision with a motor car in the United Kingdom.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> became the first recorded case of a pedestrian killed in a collision with a motor car in the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> became the first recorded case of a pedestrian killed in a collision with a motor car in the United Kingdom.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "World War I: Battle of Stallupönen: The German army of General <PERSON> defeats the Russian force commanded by <PERSON> near modern-day Nesterov, Russia.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Stallup%C3%B6nen\" title=\"Battle of Stallupönen\">Battle of Stallupönen</a>: The German army of <a href=\"https://wikipedia.org/wiki/General_officer\" title=\"General officer\">General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois\" title=\"<PERSON>\"><PERSON></a> defeats the Russian force commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> near modern-day <a href=\"https://wikipedia.org/wiki/Nesterov\" title=\"Nesterov\">Nesterov</a>, Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Stallup%C3%B6nen\" title=\"Battle of Stallupönen\">Battle of Stallupönen</a>: The German army of <a href=\"https://wikipedia.org/wiki/General_officer\" title=\"General officer\">General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A7ois\" title=\"<PERSON>\"><PERSON></a> defeats the Russian force commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> near modern-day <a href=\"https://wikipedia.org/wiki/Nesterov\" title=\"Nester<PERSON>\">Nesterov</a>, Russia.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Stallupönen", "link": "https://wikipedia.org/wiki/Battle_of_Stallup%C3%B6nen"}, {"title": "General officer", "link": "https://wikipedia.org/wiki/General_officer"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1915", "text": "Jewish American <PERSON> is lynched in Marietta, Georgia, USA after his death sentence is commuted by Governor <PERSON>.", "html": "1915 - Jewish American <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Lynching\" title=\"Lynching\">lynched</a> in <a href=\"https://wikipedia.org/wiki/Marietta,_Georgia\" title=\"Marietta, Georgia\">Marietta, Georgia, USA</a> after his death sentence is commuted by Governor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>on\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Jewish American <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Lynching\" title=\"Lynching\">lynched</a> in <a href=\"https://wikipedia.org/wiki/Marietta,_Georgia\" title=\"Marietta, Georgia\">Marietta, Georgia, USA</a> after his death sentence is commuted by Governor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>on\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lynching", "link": "https://wikipedia.org/wiki/Lynching"}, {"title": "Marietta, Georgia", "link": "https://wikipedia.org/wiki/Marietta,_Georgia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "A Category 4 hurricane hits Galveston, Texas with winds at 135 miles per hour (217 km/h).", "html": "1915 - A <a href=\"https://wikipedia.org/wiki/1915_Galveston_hurricane\" title=\"1915 Galveston hurricane\">Category 4 hurricane</a> hits <a href=\"https://wikipedia.org/wiki/Galveston,_Texas\" title=\"Galveston, Texas\">Galveston, Texas</a> with winds at 135 miles per hour (217 km/h).", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1915_Galveston_hurricane\" title=\"1915 Galveston hurricane\">Category 4 hurricane</a> hits <a href=\"https://wikipedia.org/wiki/Galveston,_Texas\" title=\"Galveston, Texas\">Galveston, Texas</a> with winds at 135 miles per hour (217 km/h).", "links": [{"title": "1915 Galveston hurricane", "link": "https://wikipedia.org/wiki/1915_Galveston_hurricane"}, {"title": "Galveston, Texas", "link": "https://wikipedia.org/wiki/Galveston,_Texas"}]}, {"year": "1916", "text": "World War I: Romania signs a secret treaty with the Entente Powers. According to the treaty, Romania agreed to join the war on the Allied side.", "html": "1916 - World War I: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a> signs a <a href=\"https://wikipedia.org/wiki/Treaty_of_Bucharest_(1916)\" title=\"Treaty of Bucharest (1916)\">secret treaty</a> with the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Entente Powers</a>. According to the treaty, Romania agreed to join the war on the Allied side.", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a> signs a <a href=\"https://wikipedia.org/wiki/Treaty_of_Bucharest_(1916)\" title=\"Treaty of Bucharest (1916)\">secret treaty</a> with the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Entente Powers</a>. According to the treaty, Romania agreed to join the war on the Allied side.", "links": [{"title": "Kingdom of Romania", "link": "https://wikipedia.org/wiki/Kingdom_of_Romania"}, {"title": "Treaty of Bucharest (1916)", "link": "https://wikipedia.org/wiki/Treaty_of_Bucharest_(1916)"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}]}, {"year": "1918", "text": "Bolshevik revolutionary leader <PERSON><PERSON><PERSON> is assassinated.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> revolutionary leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is assassinated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> revolutionary leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is assassinated.", "links": [{"title": "Bolshevik", "link": "https://wikipedia.org/wiki/Bolshevik"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "World War II: U.S. Marines raid the Japanese-held Pacific island of Makin.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> <a href=\"https://wikipedia.org/wiki/Makin_Island_raid\" class=\"mw-redirect\" title=\"Makin Island raid\">raid</a> the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a>-held Pacific island of Makin.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> <a href=\"https://wikipedia.org/wiki/Makin_Island_raid\" class=\"mw-redirect\" title=\"Makin Island raid\">raid</a> the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a>-held Pacific island of Makin.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "Makin Island raid", "link": "https://wikipedia.org/wiki/Makin_Island_raid"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}]}, {"year": "1943", "text": "World War II: The U.S. Eighth Air Force suffers the loss of 60 bombers on the Schweinfurt-Regensburg mission.", "html": "1943 - World War II: The U.S. <a href=\"https://wikipedia.org/wiki/Eighth_Air_Force\" title=\"Eighth Air Force\">Eighth Air Force</a> suffers the loss of 60 bombers on the <a href=\"https://wikipedia.org/wiki/Schweinfurt%E2%80%93Regensburg_mission\" title=\"Schweinfurt-Regensburg mission\">Schweinfurt-Regensburg mission</a>.", "no_year_html": "World War II: The U.S. <a href=\"https://wikipedia.org/wiki/Eighth_Air_Force\" title=\"Eighth Air Force\">Eighth Air Force</a> suffers the loss of 60 bombers on the <a href=\"https://wikipedia.org/wiki/Schweinfurt%E2%80%93Regensburg_mission\" title=\"Schweinfurt-Regensburg mission\">Schweinfurt-Regensburg mission</a>.", "links": [{"title": "Eighth Air Force", "link": "https://wikipedia.org/wiki/Eighth_Air_Force"}, {"title": "Schweinfurt-Regensburg mission", "link": "https://wikipedia.org/wiki/Schweinfurt%E2%80%93Regensburg_mission"}]}, {"year": "1943", "text": "World War II: The U.S. Seventh Army under General <PERSON> arrives in Messina, Italy, followed several hours later by the British 8th Army under Field Marshal <PERSON>, thus completing the Allied conquest of Sicily.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/U.S._Seventh_Army\" class=\"mw-redirect\" title=\"U.S. Seventh Army\">U.S. Seventh Army</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Messina\" title=\"Messina\">Messina</a>, Italy, followed several hours later by the <a href=\"https://wikipedia.org/wiki/Eighth_Army_(United_Kingdom)\" title=\"Eighth Army (United Kingdom)\">British 8th Army</a> under Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, thus completing the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> <a href=\"https://wikipedia.org/wiki/Allied_invasion_of_Sicily\" title=\"Allied invasion of Sicily\">conquest of Sicily</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/U.S._Seventh_Army\" class=\"mw-redirect\" title=\"U.S. Seventh Army\">U.S. Seventh Army</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Messina\" title=\"Messina\">Messina</a>, Italy, followed several hours later by the <a href=\"https://wikipedia.org/wiki/Eighth_Army_(United_Kingdom)\" title=\"Eighth Army (United Kingdom)\">British 8th Army</a> under Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, thus completing the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> <a href=\"https://wikipedia.org/wiki/Allied_invasion_of_Sicily\" title=\"Allied invasion of Sicily\">conquest of Sicily</a>.", "links": [{"title": "U.S. Seventh Army", "link": "https://wikipedia.org/wiki/U.S._Seventh_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Messina", "link": "https://wikipedia.org/wiki/Messina"}, {"title": "Eighth Army (United Kingdom)", "link": "https://wikipedia.org/wiki/Eighth_Army_(United_Kingdom)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "Allied invasion of Sicily", "link": "https://wikipedia.org/wiki/Allied_invasion_of_Sicily"}]}, {"year": "1943", "text": "World War II: First Québec Conference of <PERSON>, <PERSON>, and <PERSON> begins.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/Quebec_Conference,_1943\" class=\"mw-redirect\" title=\"Quebec Conference, 1943\">First Québec Conference</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Quebec_Conference,_1943\" class=\"mw-redirect\" title=\"Quebec Conference, 1943\">First Québec Conference</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins.", "links": [{"title": "Quebec Conference, 1943", "link": "https://wikipedia.org/wiki/Quebec_Conference,_1943"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> King", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "World War II: The Royal Air Force begins Operation Hydra, the first air raid of the Operation Crossbow strategic bombing campaign against Germany's V-weapon program.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> begins <a href=\"https://wikipedia.org/wiki/Operation_Hydra_(1943)\" title=\"Operation Hydra (1943)\">Operation Hydra</a>, the first air raid of the <a href=\"https://wikipedia.org/wiki/Operation_Crossbow\" title=\"Operation Crossbow\">Operation Crossbow</a> <a href=\"https://wikipedia.org/wiki/Strategic_bombing_during_World_War_II\" title=\"Strategic bombing during World War II\">strategic bombing campaign</a> against Germany's <a href=\"https://wikipedia.org/wiki/V-weapons\" title=\"V-weapons\">V-weapon program</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> begins <a href=\"https://wikipedia.org/wiki/Operation_Hydra_(1943)\" title=\"Operation Hydra (1943)\">Operation Hydra</a>, the first air raid of the <a href=\"https://wikipedia.org/wiki/Operation_Crossbow\" title=\"Operation Crossbow\">Operation Crossbow</a> <a href=\"https://wikipedia.org/wiki/Strategic_bombing_during_World_War_II\" title=\"Strategic bombing during World War II\">strategic bombing campaign</a> against Germany's <a href=\"https://wikipedia.org/wiki/V-weapons\" title=\"V-weapons\">V-weapon program</a>.", "links": [{"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}, {"title": "Operation Hydra (1943)", "link": "https://wikipedia.org/wiki/Operation_Hydra_(1943)"}, {"title": "Operation Crossbow", "link": "https://wikipedia.org/wiki/Operation_Crossbow"}, {"title": "Strategic bombing during World War II", "link": "https://wikipedia.org/wiki/Strategic_bombing_during_World_War_II"}, {"title": "V-weapons", "link": "https://wikipedia.org/wiki/V-weapons"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON> and <PERSON> proclaim the independence of Indonesia, igniting the Indonesian National Revolution against the Dutch Empire.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Sukarno\" title=\"Sukarno\">Sukarno</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Proclamation_of_Indonesian_Independence\" title=\"Proclamation of Indonesian Independence\">proclaim the independence</a> of <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, igniting the <a href=\"https://wikipedia.org/wiki/Indonesian_National_Revolution\" title=\"Indonesian National Revolution\">Indonesian National Revolution</a> against the <a href=\"https://wikipedia.org/wiki/Dutch_Empire\" class=\"mw-redirect\" title=\"Dutch Empire\">Dutch Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sukarno\" title=\"Sukarno\">Sukarno</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Proclamation_of_Indonesian_Independence\" title=\"Proclamation of Indonesian Independence\">proclaim the independence</a> of <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, igniting the <a href=\"https://wikipedia.org/wiki/Indonesian_National_Revolution\" title=\"Indonesian National Revolution\">Indonesian National Revolution</a> against the <a href=\"https://wikipedia.org/wiki/Dutch_Empire\" class=\"mw-redirect\" title=\"Dutch Empire\">Dutch Empire</a>.", "links": [{"title": "Sukar<PERSON>", "link": "https://wikipedia.org/wiki/Sukarno"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Proclamation of Indonesian Independence", "link": "https://wikipedia.org/wiki/Proclamation_of_Indonesian_Independence"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Indonesian National Revolution", "link": "https://wikipedia.org/wiki/Indonesian_National_Revolution"}, {"title": "Dutch Empire", "link": "https://wikipedia.org/wiki/Dutch_Empire"}]}, {"year": "1945", "text": "The novella Animal Farm by <PERSON> is first published.", "html": "1945 - The novella <i><a href=\"https://wikipedia.org/wiki/Animal_Farm\" title=\"Animal Farm\">Animal Farm</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is first published.", "no_year_html": "The novella <i><a href=\"https://wikipedia.org/wiki/Animal_Farm\" title=\"Animal Farm\">Animal Farm</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is first published.", "links": [{"title": "Animal Farm", "link": "https://wikipedia.org/wiki/Animal_Farm"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "Evacuation of Manchukuo: At Talitzou by the Sino-Korean border, Puyi, then the Kangde Emperor of Manchukuo, formally renounces the imperial throne, dissolves the state, and cedes its territory to the Republic of China.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Evacuation_of_Manchukuo\" title=\"Evacuation of Manchukuo\">Evacuation of Manchukuo</a>: At Talitzou by the <a href=\"https://wikipedia.org/wiki/China-North_Korea_border\" class=\"mw-redirect\" title=\"China-North Korea border\">Sino-Korean border</a>, <a href=\"https://wikipedia.org/wiki/Puyi\" title=\"Puyi\"><PERSON><PERSON><PERSON></a>, then the Kangde Emperor of <a href=\"https://wikipedia.org/wiki/Manchukuo\" title=\"Manchukuo\">Manchukuo</a>, <a href=\"https://wikipedia.org/wiki/Evacuation_of_Manchukuo#The_fate_of_Manchukuo\" title=\"Evacuation of Manchukuo\">formally renounces the imperial throne, dissolves the state, and cedes its territory</a> to the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic of China</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Evacuation_of_Manchukuo\" title=\"Evacuation of Manchukuo\">Evacuation of Manchukuo</a>: At Talitzou by the <a href=\"https://wikipedia.org/wiki/China-North_Korea_border\" class=\"mw-redirect\" title=\"China-North Korea border\">Sino-Korean border</a>, <a href=\"https://wikipedia.org/wiki/Puyi\" title=\"Puyi\"><PERSON><PERSON><PERSON></a>, then the Kangde Emperor of <a href=\"https://wikipedia.org/wiki/Manchukuo\" title=\"Manchukuo\">Manchukuo</a>, <a href=\"https://wikipedia.org/wiki/Evacuation_of_Manchukuo#The_fate_of_Manchukuo\" title=\"Evacuation of Manchukuo\">formally renounces the imperial throne, dissolves the state, and cedes its territory</a> to the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic of China</a>.", "links": [{"title": "Evacuation of Manchukuo", "link": "https://wikipedia.org/wiki/Evacuation_of_Manchukuo"}, {"title": "China-North Korea border", "link": "https://wikipedia.org/wiki/China-North_Korea_border"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Puyi"}, {"title": "Manchukuo", "link": "https://wikipedia.org/wiki/Manchukuo"}, {"title": "Evacuation of Manchukuo", "link": "https://wikipedia.org/wiki/Evacuation_of_Manchukuo#The_fate_of_Manchukuo"}, {"title": "Republic of China (1912-1949)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)"}]}, {"year": "1947", "text": "The Radcliffe Line, the border between the Dominions of India and Pakistan, is revealed.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Radcliffe_Line\" title=\"Radcliffe Line\">Radcliffe Line</a>, the border between the <a href=\"https://wikipedia.org/wiki/Dominion_of_India\" title=\"Dominion of India\">Dominions of India</a> and <a href=\"https://wikipedia.org/wiki/Dominion_of_Pakistan\" title=\"Dominion of Pakistan\">Pakistan</a>, is revealed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Radcliffe_Line\" title=\"Radcliffe Line\">Radcliffe Line</a>, the border between the <a href=\"https://wikipedia.org/wiki/Dominion_of_India\" title=\"Dominion of India\">Dominions of India</a> and <a href=\"https://wikipedia.org/wiki/Dominion_of_Pakistan\" title=\"Dominion of Pakistan\">Pakistan</a>, is revealed.", "links": [{"title": "Radcliffe Line", "link": "https://wikipedia.org/wiki/Radcliffe_Line"}, {"title": "Dominion of India", "link": "https://wikipedia.org/wiki/Dominion_of_India"}, {"title": "Dominion of Pakistan", "link": "https://wikipedia.org/wiki/Dominion_of_Pakistan"}]}, {"year": "1949", "text": "The 6.7 Ms  Karlıova earthquake shakes eastern Turkey with a maximum Mercalli intensity of X (Extreme), leaving 320-450 dead.", "html": "1949 - The 6.7 M<sub>s</sub>  <a href=\"https://wikipedia.org/wiki/1949_Karl%C4%B1ova_earthquake\" title=\"1949 Karlıova earthquake\">Karlıova earthquake</a> shakes eastern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>), leaving 320-450 dead.", "no_year_html": "The 6.7 M<sub>s</sub>  <a href=\"https://wikipedia.org/wiki/1949_Karl%C4%B1ova_earthquake\" title=\"1949 Karlıova earthquake\">Karlıova earthquake</a> shakes eastern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>), leaving 320-450 dead.", "links": [{"title": "1949 Karlıova earthquake", "link": "https://wikipedia.org/wiki/1949_Karl%C4%B1ova_earthquake"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1949", "text": "Matsukawa derailment: Unknown saboteurs cause a passenger train to derail and overturn in Fukushima Prefecture, Japan, killing three crew members and igniting a political firestorm between the Japanese Communist Party and the government of Occupied Japan that will eventually lead to the Japanese Red Purge.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Matsu<PERSON>_derailment\" title=\"Matsukawa derailment\">Matsu<PERSON> derailment</a>: Unknown saboteurs cause a <a href=\"https://wikipedia.org/wiki/T%C5%8Dhoku_Main_Line\" title=\"Tōhoku Main Line\">passenger train</a> to derail and overturn in <a href=\"https://wikipedia.org/wiki/Fukushima_Prefecture\" title=\"Fukushima Prefecture\">Fukushima Prefecture</a>, <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>, killing three crew members and igniting a political firestorm between the <a href=\"https://wikipedia.org/wiki/Japanese_Communist_Party\" title=\"Japanese Communist Party\">Japanese Communist Party</a> and the <a href=\"https://wikipedia.org/wiki/Occupation_of_Japan\" title=\"Occupation of Japan\">government of Occupied Japan</a> that will eventually lead to the <a href=\"https://wikipedia.org/wiki/Red_Purge\" title=\"Red Purge\">Japanese Red Purge</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_derailment\" title=\"Matsukawa derailment\">Mat<PERSON><PERSON> derailment</a>: Unknown saboteurs cause a <a href=\"https://wikipedia.org/wiki/T%C5%8Dhoku_Main_Line\" title=\"Tōhoku Main Line\">passenger train</a> to derail and overturn in <a href=\"https://wikipedia.org/wiki/Fukushima_Prefecture\" title=\"Fukushima Prefecture\">Fukushima Prefecture</a>, <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>, killing three crew members and igniting a political firestorm between the <a href=\"https://wikipedia.org/wiki/Japanese_Communist_Party\" title=\"Japanese Communist Party\">Japanese Communist Party</a> and the <a href=\"https://wikipedia.org/wiki/Occupation_of_Japan\" title=\"Occupation of Japan\">government of Occupied Japan</a> that will eventually lead to the <a href=\"https://wikipedia.org/wiki/Red_Purge\" title=\"Red Purge\">Japanese Red Purge</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> derailment", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_derailment"}, {"title": "Tōhoku Main Line", "link": "https://wikipedia.org/wiki/T%C5%8Dhoku_Main_Line"}, {"title": "Fukushima Prefecture", "link": "https://wikipedia.org/wiki/Fukushima_Prefecture"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}, {"title": "Japanese Communist Party", "link": "https://wikipedia.org/wiki/Japanese_Communist_Party"}, {"title": "Occupation of Japan", "link": "https://wikipedia.org/wiki/Occupation_of_Japan"}, {"title": "Red Purge", "link": "https://wikipedia.org/wiki/Red_Purge"}]}, {"year": "1953", "text": "First meeting of Narcotics Anonymous takes place, in Southern California.", "html": "1953 - First meeting of <a href=\"https://wikipedia.org/wiki/Narcotics_Anonymous\" title=\"Narcotics Anonymous\">Narcotics Anonymous</a> takes place, in <a href=\"https://wikipedia.org/wiki/Southern_California\" title=\"Southern California\">Southern California</a>.", "no_year_html": "First meeting of <a href=\"https://wikipedia.org/wiki/Narcotics_Anonymous\" title=\"Narcotics Anonymous\">Narcotics Anonymous</a> takes place, in <a href=\"https://wikipedia.org/wiki/Southern_California\" title=\"Southern California\">Southern California</a>.", "links": [{"title": "Narcotics Anonymous", "link": "https://wikipedia.org/wiki/Narcotics_Anonymous"}, {"title": "Southern California", "link": "https://wikipedia.org/wiki/Southern_California"}]}, {"year": "1955", "text": "Hurricane <PERSON> made landfall near Wilmington, North Carolina, and it went on to cause major floods and kill more than 184 people.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Hurricane_Diane\" title=\"Hurricane Diane\">Hurricane <PERSON></a> made landfall near <a href=\"https://wikipedia.org/wiki/Wilmington,_North_Carolina\" title=\"Wilmington, North Carolina\">Wilmington, North Carolina</a>, and it went on to cause major floods and kill more than 184 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Diane\" title=\"Hurricane Diane\">Hurricane <PERSON></a> made landfall near <a href=\"https://wikipedia.org/wiki/Wilmington,_North_Carolina\" title=\"Wilmington, North Carolina\">Wilmington, North Carolina</a>, and it went on to cause major floods and kill more than 184 people.", "links": [{"title": "Hurricane Diane", "link": "https://wikipedia.org/wiki/Hurricane_Diane"}, {"title": "Wilmington, North Carolina", "link": "https://wikipedia.org/wiki/Wilmington,_North_Carolina"}]}, {"year": "1958", "text": "Pioneer 0, America's first attempt at lunar orbit, is launched using the first Thor-Able rocket and fails. Notable as one of the first attempted launches beyond Earth orbit by any country.", "html": "1958 - <i><a href=\"https://wikipedia.org/wiki/Pioneer_0\" title=\"Pioneer 0\">Pioneer 0</a></i>, America's first attempt at lunar orbit, is launched using the first Thor-Able rocket and fails. Notable as one of the first attempted launches beyond Earth orbit by any country.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Pioneer_0\" title=\"Pioneer 0\">Pioneer 0</a></i>, America's first attempt at lunar orbit, is launched using the first Thor-Able rocket and fails. Notable as one of the first attempted launches beyond Earth orbit by any country.", "links": [{"title": "Pioneer 0", "link": "https://wikipedia.org/wiki/Pioneer_0"}]}, {"year": "1959", "text": "Quake Lake is formed by the magnitude 7.2 1959 Hebgen Lake earthquake near Hebgen Lake in Montana.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Quake_Lake\" title=\"Quake Lake\">Quake Lake</a> is formed by the magnitude 7.2 <a href=\"https://wikipedia.org/wiki/1959_Hebgen_Lake_earthquake\" title=\"1959 Hebgen Lake earthquake\">1959 Hebgen Lake earthquake</a> near <a href=\"https://wikipedia.org/wiki/Hebgen_Lake\" title=\"Hebgen Lake\">Hebgen Lake</a> in <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quake_Lake\" title=\"Quake Lake\">Quake Lake</a> is formed by the magnitude 7.2 <a href=\"https://wikipedia.org/wiki/1959_Hebgen_Lake_earthquake\" title=\"1959 Hebgen Lake earthquake\">1959 Hebgen Lake earthquake</a> near <a href=\"https://wikipedia.org/wiki/Hebgen_Lake\" title=\"Hebgen Lake\">Hebgen Lake</a> in <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a>.", "links": [{"title": "Quake Lake", "link": "https://wikipedia.org/wiki/Quake_Lake"}, {"title": "1959 Hebgen Lake earthquake", "link": "https://wikipedia.org/wiki/1959_Hebgen_Lake_earthquake"}, {"title": "<PERSON><PERSON>gen Lake", "link": "https://wikipedia.org/wiki/Hebgen_Lake"}, {"title": "Montana", "link": "https://wikipedia.org/wiki/Montana"}]}, {"year": "1960", "text": "Aeroflot Flight 036 crashes in Soviet Ukraine, killing 34.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_036\" title=\"Aeroflot Flight 036\">Aeroflot Flight 036</a> crashes in <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Soviet Ukraine</a>, killing 34.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_036\" title=\"Aeroflot Flight 036\">Aeroflot Flight 036</a> crashes in <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Soviet Ukraine</a>, killing 34.", "links": [{"title": "Aeroflot Flight 036", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_036"}, {"title": "Ukrainian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic"}]}, {"year": "1962", "text": "<PERSON> is shot and bleeds to death while trying to cross the new Berlin Wall.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON>\" title=\"Killing of <PERSON>\"><PERSON></a> is shot and bleeds to death while <a href=\"https://wikipedia.org/wiki/List_of_deaths_at_the_Berlin_Wall\" title=\"List of deaths at the Berlin Wall\">trying to cross the new Berlin Wall</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON>\" title=\"Killing of <PERSON>\"><PERSON></a> is shot and bleeds to death while <a href=\"https://wikipedia.org/wiki/List_of_deaths_at_the_Berlin_Wall\" title=\"List of deaths at the Berlin Wall\">trying to cross the new Berlin Wall</a>.", "links": [{"title": "Killing of <PERSON>", "link": "https://wikipedia.org/wiki/Killing_of_<PERSON>_<PERSON>"}, {"title": "List of deaths at the Berlin Wall", "link": "https://wikipedia.org/wiki/List_of_deaths_at_the_Berlin_Wall"}]}, {"year": "1969", "text": "Category 5 Hurricane Camille hits the U.S. Gulf Coast, killing 256 and causing $1.42 billion in damage.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Saffir%E2%80%93Simpson_hurricane_wind_scale\" class=\"mw-redirect\" title=\"Saffir-Simpson hurricane wind scale\">Category 5</a> <a href=\"https://wikipedia.org/wiki/Hurricane_Camille\" title=\"Hurricane Camille\">Hurricane Camille</a> hits the <a href=\"https://wikipedia.org/wiki/Gulf_Coast_of_the_United_States\" title=\"Gulf Coast of the United States\">U.S. Gulf Coast</a>, killing 256 and causing $1.42 billion in damage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saffir%E2%80%93Simpson_hurricane_wind_scale\" class=\"mw-redirect\" title=\"Saffir-Simpson hurricane wind scale\">Category 5</a> <a href=\"https://wikipedia.org/wiki/Hurricane_Camille\" title=\"Hurricane Camille\">Hurricane Camille</a> hits the <a href=\"https://wikipedia.org/wiki/Gulf_Coast_of_the_United_States\" title=\"Gulf Coast of the United States\">U.S. Gulf Coast</a>, killing 256 and causing $1.42 billion in damage.", "links": [{"title": "Saffir-Simpson hurricane wind scale", "link": "https://wikipedia.org/wiki/Saffir%E2%80%93Simpson_hurricane_wind_scale"}, {"title": "Hurricane Camille", "link": "https://wikipedia.org/wiki/Hurricane_Camille"}, {"title": "Gulf Coast of the United States", "link": "https://wikipedia.org/wiki/Gulf_Coast_of_the_United_States"}]}, {"year": "1970", "text": "Soviet Union Venera program: Venera 7 launched. It will become the first spacecraft to successfully transmit data from the surface of another planet (Venus).", "html": "1970 - Soviet Union <a href=\"https://wikipedia.org/wiki/Venera\" title=\"Venera\">Venera</a> program: <i><a href=\"https://wikipedia.org/wiki/Venera_7\" title=\"Venera 7\">Venera 7</a></i> launched. It will become the first spacecraft to successfully transmit data from the surface of another planet (<a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>).", "no_year_html": "Soviet Union <a href=\"https://wikipedia.org/wiki/Venera\" title=\"Venera\">Venera</a> program: <i><a href=\"https://wikipedia.org/wiki/Venera_7\" title=\"Venera 7\">Venera 7</a></i> launched. It will become the first spacecraft to successfully transmit data from the surface of another planet (<a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>).", "links": [{"title": "Venera", "link": "https://wikipedia.org/wiki/Venera"}, {"title": "Venera 7", "link": "https://wikipedia.org/wiki/Venera_7"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}]}, {"year": "1976", "text": "A magnitude 7.9 earthquake hits off the coast of Mindanao, Philippines, triggering a destructive tsunami, killing between 5,000 and 8,000 people and leaving more than 90,000 homeless.", "html": "1976 - A <a href=\"https://wikipedia.org/wiki/1976_Moro_Gulf_earthquake\" title=\"1976 Moro Gulf earthquake\">magnitude 7.9 earthquake</a> hits off the coast of <a href=\"https://wikipedia.org/wiki/Mindanao\" title=\"Mindanao\">Mindanao</a>, <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, triggering a destructive tsunami, killing between 5,000 and 8,000 people and leaving more than 90,000 homeless.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1976_Moro_Gulf_earthquake\" title=\"1976 Moro Gulf earthquake\">magnitude 7.9 earthquake</a> hits off the coast of <a href=\"https://wikipedia.org/wiki/Mindanao\" title=\"Mindanao\">Mindanao</a>, <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, triggering a destructive tsunami, killing between 5,000 and 8,000 people and leaving more than 90,000 homeless.", "links": [{"title": "1976 Moro Gulf earthquake", "link": "https://wikipedia.org/wiki/1976_Moro_Gulf_earthquake"}, {"title": "Mindanao", "link": "https://wikipedia.org/wiki/Mindanao"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1977", "text": "The Soviet icebreaker Arktika becomes the first surface ship to reach the North Pole.", "html": "1977 - The Soviet icebreaker <i><a href=\"https://wikipedia.org/wiki/Arktika_(1972_icebreaker)\" title=\"Arkti<PERSON> (1972 icebreaker)\"><PERSON><PERSON><PERSON></a></i> becomes the first surface ship to reach the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a>.", "no_year_html": "The Soviet icebreaker <i><a href=\"https://wikipedia.org/wiki/Arktika_(1972_icebreaker)\" title=\"Arkti<PERSON> (1972 icebreaker)\"><PERSON><PERSON><PERSON></a></i> becomes the first surface ship to reach the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (1972 icebreaker)", "link": "https://wikipedia.org/wiki/Ark<PERSON><PERSON>_(1972_icebreaker)"}, {"title": "North Pole", "link": "https://wikipedia.org/wiki/North_Pole"}]}, {"year": "1978", "text": "Double Eagle II becomes first balloon to cross the Atlantic Ocean when it lands in Miserey, France near Paris, 137 hours after leaving Presque Isle, Maine.", "html": "1978 - <i><a href=\"https://wikipedia.org/wiki/Double_Eagle_II\" title=\"Double Eagle II\">Double Eagle II</a></i> becomes first <a href=\"https://wikipedia.org/wiki/Balloon_(aeronautics)\" title=\"Balloon (aeronautics)\">balloon</a> to cross the Atlantic Ocean when it lands in <a href=\"https://wikipedia.org/wiki/Miserey\" title=\"Miserey\">Miserey</a>, France near Paris, 137 hours after leaving <a href=\"https://wikipedia.org/wiki/Presque_Isle,_Maine\" title=\"Presque Isle, Maine\">Presque Isle, Maine</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Double_Eagle_II\" title=\"Double Eagle II\">Double Eagle II</a></i> becomes first <a href=\"https://wikipedia.org/wiki/Balloon_(aeronautics)\" title=\"Balloon (aeronautics)\">balloon</a> to cross the Atlantic Ocean when it lands in <a href=\"https://wikipedia.org/wiki/Miserey\" title=\"Miserey\">Miserey</a>, France near Paris, 137 hours after leaving <a href=\"https://wikipedia.org/wiki/Presque_Isle,_Maine\" title=\"Presque Isle, Maine\">Presque Isle, Maine</a>.", "links": [{"title": "Double Eagle II", "link": "https://wikipedia.org/wiki/Double_Eagle_II"}, {"title": "Balloon (aeronautics)", "link": "https://wikipedia.org/wiki/<PERSON>oon_(aeronautics)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miserey"}, {"title": "Presque Isle, Maine", "link": "https://wikipedia.org/wiki/Presque_Isle,_Maine"}]}, {"year": "1985", "text": "The 1985-86 Hormel strike begins in Austin, Minnesota.", "html": "1985 - The <a href=\"https://wikipedia.org/wiki/1985%E2%80%9386_<PERSON><PERSON><PERSON>_strike\" class=\"mw-redirect\" title=\"1985-86 Hormel strike\">1985-86 Hormel strike</a> begins in <a href=\"https://wikipedia.org/wiki/Austin,_Minnesota\" title=\"Austin, Minnesota\">Austin, Minnesota</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1985%E2%80%9386_Horm<PERSON>_strike\" class=\"mw-redirect\" title=\"1985-86 Hormel strike\">1985-86 Hormel strike</a> begins in <a href=\"https://wikipedia.org/wiki/Austin,_Minnesota\" title=\"Austin, Minnesota\">Austin, Minnesota</a>.", "links": [{"title": "1985-86 Hormel strike", "link": "https://wikipedia.org/wiki/1985%E2%80%9386_<PERSON><PERSON><PERSON>_strike"}, {"title": "Austin, Minnesota", "link": "https://wikipedia.org/wiki/Austin,_Minnesota"}]}, {"year": "1988", "text": "President of Pakistan <PERSON> and U.S. Ambassador <PERSON> are killed in a plane crash.", "html": "1988 - President of Pakistan <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and U.S. Ambassador <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are killed in a <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON><PERSON>-<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"Death and state funeral of <PERSON>\">plane crash</a>.", "no_year_html": "President of Pakistan <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and U.S. Ambassador <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are killed in a <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON><PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"Death and state funeral of <PERSON>\">plane crash</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>-<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Death and state funeral of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON><PERSON>-<PERSON><PERSON>"}]}, {"year": "1991", "text": "Strathfield massacre: In Sydney, New South Wales, Australia, taxi driver <PERSON> shoots seven people and injures six others before turning the gun on himself.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Strathfield_massacre\" title=\"Strathfield massacre\">Strathfield massacre</a>: In Sydney, New South Wales, Australia, taxi driver <PERSON> shoots seven people and injures six others before turning the gun on himself.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Strathfield_massacre\" title=\"Strathfield massacre\">Strathfield massacre</a>: In Sydney, New South Wales, Australia, taxi driver <PERSON> shoots seven people and injures six others before turning the gun on himself.", "links": [{"title": "Strathfield massacre", "link": "https://wikipedia.org/wiki/Strat<PERSON>field_massacre"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON> scandal: US President <PERSON> admits in taped testimony that he had an \"improper physical relationship\" with White House intern <PERSON>; later that same day he admits before the nation that he \"misled people\" about the relationship.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_scandal\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> scandal\"><PERSON><PERSON>sky scandal</a>: US President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> admits in taped testimony that he had an \"improper physical relationship\" with <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> intern <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>; later that same day he admits before the nation that he \"misled people\" about the relationship.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_scandal\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> scandal\"><PERSON><PERSON>sky scandal</a>: US President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> admits in taped testimony that he had an \"improper physical relationship\" with <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> intern <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>; later that same day he admits before the nation that he \"misled people\" about the relationship.", "links": [{"title": "<PERSON><PERSON><PERSON> scandal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_scandal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "The 7.6 Mw  İzmit earthquake shakes northwestern Turkey with a maximum Mercalli intensity of IX (Violent), leaving 17,118-17,127 dead and 43,953-50,000 injured.", "html": "1999 - The 7.6 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1999_%C4%B0zmit_earthquake\" title=\"1999 İzmit earthquake\">İzmit earthquake</a> shakes northwestern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), leaving 17,118-17,127 dead and 43,953-50,000 injured.", "no_year_html": "The 7.6 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1999_%C4%B0zmit_earthquake\" title=\"1999 İzmit earthquake\">İzmit earthquake</a> shakes northwestern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), leaving 17,118-17,127 dead and 43,953-50,000 injured.", "links": [{"title": "1999 İzmit earthquake", "link": "https://wikipedia.org/wiki/1999_%C4%B0zmit_earthquake"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "2004", "text": "The National Assembly of Serbia unanimously adopts new state symbols for Serbia: <PERSON><PERSON><PERSON> pravde becomes the new anthem and the coat of arms is adopted for the whole country.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Serbia\" class=\"mw-redirect\" title=\"National Assembly of Serbia\">National Assembly of Serbia</a> unanimously adopts new state symbols for <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>: <a href=\"https://wikipedia.org/wiki/Bo%C5%BEe_pravde\" title=\"Bože pravde\"><PERSON><PERSON><PERSON> pravde</a> becomes the new anthem and the <a href=\"https://wikipedia.org/wiki/Coat_of_arms_of_Serbia\" title=\"Coat of arms of Serbia\">coat of arms</a> is adopted for the whole country.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Serbia\" class=\"mw-redirect\" title=\"National Assembly of Serbia\">National Assembly of Serbia</a> unanimously adopts new state symbols for <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>: <a href=\"https://wikipedia.org/wiki/Bo%C5%BEe_pravde\" title=\"Bože pravde\">Bo<PERSON><PERSON> pravde</a> becomes the new anthem and the <a href=\"https://wikipedia.org/wiki/Coat_of_arms_of_Serbia\" title=\"Coat of arms of Serbia\">coat of arms</a> is adopted for the whole country.", "links": [{"title": "National Assembly of Serbia", "link": "https://wikipedia.org/wiki/National_Assembly_of_Serbia"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}, {"title": "Bože pravde", "link": "https://wikipedia.org/wiki/Bo%C5%BEe_pravde"}, {"title": "Coat of arms of Serbia", "link": "https://wikipedia.org/wiki/Coat_of_arms_of_Serbia"}]}, {"year": "2005", "text": "The first forced evacuation of settlers, as part of Israeli disengagement from Gaza, starts.", "html": "2005 - The first forced evacuation of <a href=\"https://wikipedia.org/wiki/Israeli_settlement\" title=\"Israeli settlement\">settlers</a>, as part of <a href=\"https://wikipedia.org/wiki/Israeli_disengagement_from_Gaza\" class=\"mw-redirect\" title=\"Israeli disengagement from Gaza\">Israeli disengagement from Gaza</a>, starts.", "no_year_html": "The first forced evacuation of <a href=\"https://wikipedia.org/wiki/Israeli_settlement\" title=\"Israeli settlement\">settlers</a>, as part of <a href=\"https://wikipedia.org/wiki/Israeli_disengagement_from_Gaza\" class=\"mw-redirect\" title=\"Israeli disengagement from Gaza\">Israeli disengagement from Gaza</a>, starts.", "links": [{"title": "Israeli settlement", "link": "https://wikipedia.org/wiki/Israeli_settlement"}, {"title": "Israeli disengagement from Gaza", "link": "https://wikipedia.org/wiki/Israeli_disengagement_from_Gaza"}]}, {"year": "2005", "text": "Over 500 bombs are set off by terrorists at 300 locations in 63 out of the 64 districts of Bangladesh.", "html": "2005 - Over 500 bombs are <a href=\"https://wikipedia.org/wiki/2005_Bangladesh_bombings\" title=\"2005 Bangladesh bombings\">set off by terrorists</a> at 300 locations in 63 out of the 64 districts of <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "no_year_html": "Over 500 bombs are <a href=\"https://wikipedia.org/wiki/2005_Bangladesh_bombings\" title=\"2005 Bangladesh bombings\">set off by terrorists</a> at 300 locations in 63 out of the 64 districts of <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "links": [{"title": "2005 Bangladesh bombings", "link": "https://wikipedia.org/wiki/2005_Bangladesh_bombings"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}]}, {"year": "2008", "text": "American swimmer <PERSON> becomes the first person to win eight gold medals at one Olympic Games.", "html": "2008 - American swimmer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to win eight gold medals at one Olympic Games.", "no_year_html": "American swimmer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to win eight gold medals at one Olympic Games.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2009", "text": "An accident at the Sayano-Shushenskaya Dam in Khakassia, Russia, kills 75 and shuts down the hydroelectric power station, leading to widespread power failure in the local area.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/2009_Sayano-Shushenskaya_power_station_accident\" class=\"mw-redirect\" title=\"2009 Sayano-Shushenskaya power station accident\">An accident</a> at the <a href=\"https://wikipedia.org/wiki/Sayano-Shushenskaya_Dam\" title=\"Sayano-Shushenskaya Dam\">Sayano-Shushenskaya Dam</a> in <a href=\"https://wikipedia.org/wiki/Khakassia\" title=\"Khakassia\">Khakassia</a>, Russia, kills 75 and shuts down the hydroelectric power station, leading to widespread power failure in the local area.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2009_Sayano-Shushenskaya_power_station_accident\" class=\"mw-redirect\" title=\"2009 Sayano-Shushenskaya power station accident\">An accident</a> at the <a href=\"https://wikipedia.org/wiki/Sayano-Shushenskaya_Dam\" title=\"Sayano-Shushenskaya Dam\">Sayano-Shushenskaya Dam</a> in <a href=\"https://wikipedia.org/wiki/Khakassia\" title=\"Khakassia\">Khakassia</a>, Russia, kills 75 and shuts down the hydroelectric power station, leading to widespread power failure in the local area.", "links": [{"title": "2009 Sayano-Shushenskaya power station accident", "link": "https://wikipedia.org/wiki/2009_Sayano-Shushenskaya_power_station_accident"}, {"title": "Sayano-Shushenskaya Dam", "link": "https://wikipedia.org/wiki/Sayano-Shushenskaya_Dam"}, {"title": "Khakassia", "link": "https://wikipedia.org/wiki/Khakassia"}]}, {"year": "2015", "text": "A bomb explodes near the Erawan Shrine in Bangkok, Thailand, killing at least 19 people and injuring 123 others.", "html": "2015 - A <a href=\"https://wikipedia.org/wiki/2015_Bangkok_bombing\" title=\"2015 Bangkok bombing\">bomb explodes</a> near the <a href=\"https://wikipedia.org/wiki/Erawan_Shrine\" title=\"Erawan Shrine\">Erawan Shrine</a> in <a href=\"https://wikipedia.org/wiki/Bangkok\" title=\"Bangkok\">Bangkok</a>, Thailand, killing at least 19 people and injuring 123 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2015_Bangkok_bombing\" title=\"2015 Bangkok bombing\">bomb explodes</a> near the <a href=\"https://wikipedia.org/wiki/Erawan_Shrine\" title=\"Erawan Shrine\">Erawan Shrine</a> in <a href=\"https://wikipedia.org/wiki/Bangkok\" title=\"Bangkok\">Bangkok</a>, Thailand, killing at least 19 people and injuring 123 others.", "links": [{"title": "2015 Bangkok bombing", "link": "https://wikipedia.org/wiki/2015_Bangkok_bombing"}, {"title": "Erawan Shrine", "link": "https://wikipedia.org/wiki/Erawan_Shrine"}, {"title": "Bangkok", "link": "https://wikipedia.org/wiki/Bangkok"}]}, {"year": "2017", "text": "Barcelona attacks: A van is driven into pedestrians in La Rambla, killing 14 and injuring at least 100.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/2017_Barcelona_attacks\" title=\"2017 Barcelona attacks\">Barcelona attacks</a>: A van is driven into pedestrians in La Rambla, killing 14 and injuring at least 100.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2017_Barcelona_attacks\" title=\"2017 Barcelona attacks\">Barcelona attacks</a>: A van is driven into pedestrians in La Rambla, killing 14 and injuring at least 100.", "links": [{"title": "2017 Barcelona attacks", "link": "https://wikipedia.org/wiki/2017_Barcelona_attacks"}]}, {"year": "2019", "text": "A bomb explodes at a wedding in Kabul killing 63 people and leaving 182 injured.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/17_August_2019_Kabul_bombing\" title=\"17 August 2019 Kabul bombing\">A bomb explodes at a wedding in Kabul</a> killing 63 people and leaving 182 injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/17_August_2019_Kabul_bombing\" title=\"17 August 2019 Kabul bombing\">A bomb explodes at a wedding in Kabul</a> killing 63 people and leaving 182 injured.", "links": [{"title": "17 August 2019 Kabul bombing", "link": "https://wikipedia.org/wiki/17_August_2019_Kabul_bombing"}]}], "Births": [{"year": "1153", "text": "<PERSON>, Count of Poitiers (d. 1156)", "html": "1153 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Poitiers\" title=\"<PERSON>, Count of Poitiers\"><PERSON>, Count of Poitiers</a> (d. 1156)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Poitiers\" title=\"<PERSON>, Count of Poitiers\"><PERSON>, Count of Poitiers</a> (d. 1156)", "links": [{"title": "<PERSON>, Count of Poitiers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1465", "text": "<PERSON><PERSON><PERSON>, Duke of Savoy (d. 1482)", "html": "1465 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Savoy\"><PERSON><PERSON><PERSON>, Duke of Savoy</a> (d. 1482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Savoy\"><PERSON><PERSON><PERSON>, Duke of Savoy</a> (d. 1482)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_of_Savoy"}]}, {"year": "1473", "text": "<PERSON> Shrewsbury, Duke of York (d. 1483)", "html": "1473 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_York\" title=\"<PERSON> of Shrewsbury, Duke of York\"><PERSON> Shrewsbury, Duke of York</a> (d. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Shrewsbury,_Duke_of_York\" title=\"<PERSON> of Shrewsbury, Duke of York\"><PERSON> Shrewsbury, Duke of York</a> (d. 1483)", "links": [{"title": "<PERSON> Shrewsbury, Duke of York", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_York"}]}, {"year": "1501", "text": "<PERSON>, Count of Hanau-Münzenberg (d. 1529)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-M%C3%BCnz<PERSON>\" title=\"<PERSON>, Count of Hanau-Münzenberg\"><PERSON>, Count of Hanau-Münzenberg</a> (d. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-M%C3%BCnz<PERSON>\" title=\"<PERSON>, Count of Hanau-Münzenberg\"><PERSON>, Count of Hanau-Münzenberg</a> (d. 1529)", "links": [{"title": "<PERSON>, Count of Hanau-Münzenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_Hanau-M%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1556", "text": "<PERSON>, English martyr and saint (d. 1581)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English martyr and saint (d. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English martyr and saint (d. 1581)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1578", "text": "<PERSON>, Italian painter (d. 1660)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1578", "text": "<PERSON>, Prince of Hohenzollern-Sigmaringen, first prince of Hohenzollern-Sigmaringen (d. 1638)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Hohenzollern-Sigmaringen\" title=\"<PERSON>, Prince of Hohenzollern-Sigmaringen\"><PERSON>, Prince of Hohenzollern-Sigmaringen</a>, first prince of Hohenzollern-Sigmaringen (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Hohenzollern-Sigmaringen\" title=\"<PERSON>, Prince of Hohenzollern-Sigmaringen\"><PERSON>, Prince of Hohenzollern-Sigmaringen</a>, first prince of Hohenzollern-Sigmaringen (d. 1638)", "links": [{"title": "<PERSON>, Prince of Hohenzollern-Sigmaringen", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Hohenzollern-Sigmaringen"}]}, {"year": "1582", "text": "<PERSON>, Maltese philosopher (d. 1639)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese philosopher (d. 1639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese philosopher (d. 1639)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1586", "text": "<PERSON>, German theologian (d. 1654)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1603", "text": "<PERSON><PERSON><PERSON>, Swedish Field Marshal, Privy Councillour and Governor-General (d. 1651)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish Field Marshal, Privy Councillour and Governor-General (d. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish Field Marshal, Privy Councillour and Governor-General (d. 1651)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1629", "text": "<PERSON>, Polish-Lithuanian king (d. 1696)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Lithuanian king (d. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Lithuanian king (d. 1696)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, Italian composer and educator (d. 1768)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nicola_Porpora"}]}, {"year": "1753", "text": "<PERSON>, Bohemian philologist and historian (d. 1828)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Bohemian philologist and historian (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Bohemian philologist and historian (d. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD"}]}, {"year": "1768", "text": "<PERSON>, French general (d. 1800)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "<PERSON>, American soldier and politician (d. 1836)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "Princess <PERSON> of Saxe-Coburg-Saalfeld (d. 1861)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Saxe-Coburg-Saalfeld\" title=\"Princess <PERSON> of Saxe-Coburg-Saalfeld\">Princess <PERSON> of Saxe-Coburg-Saalfeld</a> (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Saxe-Coburg-Saalfeld\" title=\"Princess <PERSON> of Saxe-Coburg-Saalfeld\">Princess <PERSON> of Saxe-Coburg-Saalfeld</a> (d. 1861)", "links": [{"title": "Princess <PERSON> of Saxe-Coburg-Saalfeld", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Saxe-Coburg-Saalfeld"}]}, {"year": "1801", "text": "<PERSON><PERSON>, Swedish writer and feminist (d. 1865)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish writer and feminist (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> B<PERSON>\"><PERSON><PERSON></a>, Swedish writer and feminist (d. 1865)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, French neurologist and physician (d. 1897)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French neurologist and physician (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French neurologist and physician (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON><PERSON>, English poet and activist (d. 1922)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">Wil<PERSON><PERSON></a>, English poet and activist (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">Wil<PERSON><PERSON></a>, English poet and activist (d. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, American physician and naturalist (d. 1909)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and naturalist (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and naturalist (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, Scottish-Australian politician, 17th Premier of Queensland (d. 1919)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1863", "text": "<PERSON>-<PERSON>, American author and photographer (d. 1924)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and photographer (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and photographer (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, English-American actress (d. 1950)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Indian 6th Nizam of Hyderabad (d. 1911)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>_VI\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Asaf Jah VI\"><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Jah VI</a>, Indian 6th <a href=\"https://wikipedia.org/wiki/Nizam_of_Hyderabad\" title=\"Nizam of Hyderabad\"><PERSON><PERSON> of Hyderabad</a> (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>_VI\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Asaf Jah VI\"><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> VI</a>, Indian 6th <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hyderabad\" title=\"Nizam of Hyderabad\"><PERSON><PERSON> of Hyderabad</a> (d. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> of Hyderabad", "link": "https://wikipedia.org/wiki/Nizam_of_Hyderabad"}]}, {"year": "1873", "text": "<PERSON>, American gynecologist and academic (d. 1946)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gynecologist and academic (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gynecologist and academic (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American golfer and tennis player (d. 1923)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and tennis player (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and tennis player (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Australian cricketer (d. 1911)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, South African cricketer and tennis player (d. 1948)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and tennis player (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and tennis player (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON> of Austria (d. 1922)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1922)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Austria"}]}, {"year": "1887", "text": "<PERSON>, Jamaican journalist and activist, founded Black Star Line (d. 1940)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican journalist and activist, founded <a href=\"https://wikipedia.org/wiki/Black_Star_Line\" title=\"Black Star Line\">Black Star Line</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican journalist and activist, founded <a href=\"https://wikipedia.org/wiki/Black_Star_Line\" title=\"Black Star Line\">Black Star Line</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Black Star Line", "link": "https://wikipedia.org/wiki/Black_Star_Line"}]}, {"year": "1888", "text": "<PERSON>, American actor, raconteur, and pundit (d. 1963)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, raconteur, and pundit (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, raconteur, and pundit (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON>, Norwegian singer and actress (d. 1967)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer and actress (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer and actress (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Polish soldier and pilot (d. 1920)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish soldier and pilot (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish soldier and pilot (d. 1920)", "links": [{"title": "Stefan <PERSON>r", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American politician and diplomat, 8th United States Secretary of Commerce (d. 1946)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 8th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 8th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Commerce", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Commerce"}]}, {"year": "1893", "text": "<PERSON>, German-American director and production manager (d. 1982)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American director and production manager (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American director and production manager (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American stage and film actress (d. 1980)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Mae_West\" title=\"Mae West\"><PERSON></a>, American stage and film actress (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mae_West\" title=\"Mae West\"><PERSON></a>, American stage and film actress (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mae_West"}]}, {"year": "1894", "text": "<PERSON>, 1st Baron <PERSON>, English businessman, founded Rootes Group (d. 1964)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Rootes_Group\" title=\"Rootes Group\">Rootes Group</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Rootes_Group\" title=\"Rootes Group\">Rootes Group</a> (d. 1964)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Rootes Group", "link": "https://wikipedia.org/wiki/Rootes_Group"}]}, {"year": "1896", "text": "<PERSON>, American general and engineer (d. 1970)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and engineer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and engineer (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leslie_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Estonian lieutenant and politician, Prime Minister of Estonia in exile (d. 1991)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/T%C3%B5<PERSON>_Kint\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%B5nis_Kint\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%B5nis_Kint"}, {"title": "Prime Minister of Estonia in exile", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile"}]}, {"year": "1896", "text": "<PERSON>, American historian and author (d. 1970)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>man <PERSON>rkin\"><PERSON></a>, American historian and author (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Waterman <PERSON>rkin\"><PERSON></a>, American historian and author (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American poet and novelist (d. 1998)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and novelist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and novelist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, British travel writer and adventurer (d. 1957)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British travel writer and adventurer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British travel writer and adventurer (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American teacher, historian, aviator and activist (d. 1991)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American teacher, historian, aviator and activist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American teacher, historian, aviator and activist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American journalist and politician (d. 1984)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)\" title=\"<PERSON> (editor)\"><PERSON></a>, American journalist and politician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)\" title=\"<PERSON> (editor)\"><PERSON></a>, American journalist and politician (d. 1984)", "links": [{"title": "<PERSON> (editor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)"}]}, {"year": "1904", "text": "<PERSON>, Austrian composer and musicologist (d. 1991)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and musicologist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and musicologist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American trumpet player and bandleader (d. 1985)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, English footballer (d. 1980)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Wilf_Copping\" title=\"Wilf Copping\">Wil<PERSON></a>, English footballer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilf_Copping\" title=\"Wilf Copping\">Wil<PERSON></a>, English footballer (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilf_Copping"}]}, {"year": "1911", "text": "<PERSON>, Russian chess player and engineer (d. 1995)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and engineer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and engineer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, German colonel and lawyer (d. 2010)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and lawyer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and lawyer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American lawyer and agent, 2nd Deputy Director of the Federal Bureau of Investigation (d. 2008)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and agent, 2nd <a href=\"https://wikipedia.org/wiki/Deputy_Director_of_the_Federal_Bureau_of_Investigation\" title=\"Deputy Director of the Federal Bureau of Investigation\">Deputy Director of the Federal Bureau of Investigation</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and agent, 2nd <a href=\"https://wikipedia.org/wiki/Deputy_Director_of_the_Federal_Bureau_of_Investigation\" title=\"Deputy Director of the Federal Bureau of Investigation\">Deputy Director of the Federal Bureau of Investigation</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Director of the Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Deputy_Director_of_the_Federal_Bureau_of_Investigation"}]}, {"year": "1913", "text": "<PERSON>, Argentinian race car driver (d. 1989)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1lvez\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1lvez\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1lvez"}]}, {"year": "1913", "text": "<PERSON>, American baseball player and manager (d. 1970)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American journalist (d. 1978)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American lawyer and politician (d. 1988)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American lawyer and politician (d. 1988)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1916", "text": "<PERSON>, Nigerian physician and politician (d. 2012)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian physician and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian physician and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, British-American actress (d. 1985)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, American saxophonist and pianist (d. 1963)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Ike_<PERSON>\" title=\"Ike <PERSON>\"><PERSON><PERSON></a>, American saxophonist and pianist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ike_<PERSON>\" title=\"Ike <PERSON>\"><PERSON><PERSON></a>, American saxophonist and pianist (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ike_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English geographer and academic (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geographer and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geographer and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American singer (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gibbs\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gibbs\" title=\"<PERSON> Gibbs\"><PERSON></a>, American singer (d. 2006)", "links": [{"title": "<PERSON> Gibbs", "link": "https://wikipedia.org/wiki/Georgia_Gibbs"}]}, {"year": "1920", "text": "<PERSON>, Irish-American actress and singer (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, Irish-American actress and singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, Irish-American actress and singer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American photographer and author (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer and author (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>da_Moser"}]}, {"year": "1921", "text": "<PERSON>, German-English historian and academic (d. 1994)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English historian and academic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English historian and academic (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English cricketer (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Venezuelan artist (d. 2019)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan artist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan artist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American painter and sculptor (d. 2002)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American novelist, poet, and short story writer (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and short story writer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and short story writer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English businesswoman (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Chinese engineer and politician, former General Secretary of the Chinese Communist Party (paramount leader) and 5th President of China (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese engineer and politician, former <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> (<a href=\"https://wikipedia.org/wiki/Paramount_leader\" title=\"Paramount leader\">paramount leader</a>) and 5th <a href=\"https://wikipedia.org/wiki/President_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"President of the People's Republic of China\">President of China</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese engineer and politician, former <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> (<a href=\"https://wikipedia.org/wiki/Paramount_leader\" title=\"Paramount leader\">paramount leader</a>) and 5th <a href=\"https://wikipedia.org/wiki/President_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"President of the People's Republic of China\">President of China</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General Secretary of the Chinese Communist Party", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party"}, {"title": "Paramount leader", "link": "https://wikipedia.org/wiki/Paramount_leader"}, {"title": "President of the People's Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_People%27s_Republic_of_China"}]}, {"year": "1927", "text": "<PERSON>, American saxophonist and bandleader (d. 2009)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and bandleader (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and bandleader (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON> <PERSON>, American lawyer and politician, Governor of Vermont (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> <PERSON> Jr<PERSON>\"><PERSON><PERSON> <PERSON>.</a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Vermont\" title=\"Governor of Vermont\">Governor of Vermont</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> <PERSON> Jr<PERSON>\"><PERSON><PERSON> Jr.</a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Vermont\" title=\"Governor of Vermont\">Governor of Vermont</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON> <PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Governor of Vermont", "link": "https://wikipedia.org/wiki/Governor_of_Vermont"}]}, {"year": "1928", "text": "<PERSON><PERSON> <PERSON><PERSON>, American composer, conductor, and educator", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American composer, conductor, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American composer, conductor, and educator", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Dutch tennis player, sportscaster, and producer (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player, sportscaster, and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player, sportscaster, and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American captain and pilot (d. 1977)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American screenwriter and producer (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American screenwriter and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American screenwriter and producer (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English poet and playwright (d. 1998)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English historian, demographer, and academic (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, demographer, and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, demographer, and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON> <PERSON><PERSON>, Trinidadian-English novelist and essayist, Nobel Prize laureate (d. 2018)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/V._S._Naipaul\" title=\"V. S. Naipaul\"><PERSON><PERSON> <PERSON><PERSON></a>, Trinidadian-English novelist and essayist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V._S._Naipaul\" title=\"V. S. Naipaul\"><PERSON><PERSON> <PERSON><PERSON></a>, Trinidadian-English novelist and essayist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2018)", "links": [{"title": "<PERSON>. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V._<PERSON><PERSON>_Naipaul"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1932", "text": "<PERSON>, American pianist and composer (d. 1980)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, French cartoonist (d. 2022)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cartoonist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cartoonist (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9"}]}, {"year": "1933", "text": "<PERSON>, American pop singer (d. 1986)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Din<PERSON>\"><PERSON></a>, American pop singer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop singer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ning"}]}, {"year": "1934", "text": "<PERSON>, Brazilian pianist and composer (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian pianist and composer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian pianist and composer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON>ato"}]}, {"year": "1934", "text": "<PERSON>, English footballer (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Irish educator and politician, Deputy First Minister of Northern Ireland (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Seamus_Mallon\" title=\"Seamus Mallon\"><PERSON><PERSON></a>, Irish educator and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_and_deputy_First_Minister\" class=\"mw-redirect\" title=\"First Minister and deputy First Minister\">Deputy First Minister of Northern Ireland</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seamus_Mallon\" title=\"Sea<PERSON> Mall<PERSON>\"><PERSON><PERSON></a>, Irish educator and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_and_deputy_First_Minister\" class=\"mw-redirect\" title=\"First Minister and deputy First Minister\">Deputy First Minister of Northern Ireland</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Seamus_Mallon"}, {"title": "First Minister and deputy First Minister", "link": "https://wikipedia.org/wiki/First_Minister_and_deputy_First_Minister"}]}, {"year": "1936", "text": "<PERSON>, American computer scientist, systems engineer, and business owner", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, systems engineer, and business owner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, systems engineer, and business owner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Greek lawyer and politician, Deputy Prime Minister of Greece (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece\" title=\"Deputy Prime Minister of Greece\">Deputy Prime Minister of Greece</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece\" title=\"Deputy Prime Minister of Greece\">Deputy Prime Minister of Greece</a> (d. 2023)", "links": [{"title": "<PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)"}, {"title": "Deputy Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece"}]}, {"year": "1939", "text": "<PERSON>, American blues guitarist and singer (d. 1997)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues guitarist and singer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues guitarist and singer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Argentinian director and screenwriter (d. 2006)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and screenwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English academic and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, German businessman and politician (d. 2013)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Lothar_Bisky\" title=\"Lothar Bisky\"><PERSON><PERSON></a>, German businessman and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lothar_Bisky\" title=\"Lothar Bisky\"><PERSON><PERSON></a>, German businessman and politician (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lothar_Bisky"}]}, {"year": "1941", "text": "<PERSON>, Canadian director and screenwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American baseball player", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian actor, animator, and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, animator, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, animator, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English composer, painter, and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, painter, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, painter, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor, entrepreneur, director, and producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, entrepreneur, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, entrepreneur, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Welsh journalist and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, American singer-songwriter and guitarist (d. 2002)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22S<PERSON><PERSON>%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON>\" Ray'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter and guitarist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22S<PERSON><PERSON>%22_<PERSON>\" title='<PERSON> \"<PERSON>\" Ray'><PERSON> \"<PERSON><PERSON>\" <PERSON></a>, American singer-songwriter and guitarist (d. 2002)", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22S<PERSON><PERSON>%22_Ray"}]}, {"year": "1944", "text": "<PERSON>, American businessman, co-founded the Oracle Corporation", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Oracle_Corporation\" title=\"Oracle Corporation\">Oracle Corporation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Oracle_Corporation\" title=\"Oracle Corporation\">Oracle Corporation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Oracle Corporation", "link": "https://wikipedia.org/wiki/Oracle_Corporation"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, French pianist and conductor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French pianist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French pianist and conductor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American author, poet, and educator (d. 2023)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and educator (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and educator (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, South African golfer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Trinidadian-Tobagonian politician, 4th Prime Minister of Trinidad and Tobago (d. 2016)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-Tobagonian politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"List of Prime Ministers of Trinidad and Tobago\">Prime Minister of Trinidad and Tobago</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-Tobagonian politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"List of Prime Ministers of Trinidad and Tobago\">Prime Minister of Trinidad and Tobago</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Trinidad and Tobago", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Trinidad_and_Tobago"}]}, {"year": "1947", "text": "<PERSON>, Sahrawi politician, President of the Sahrawi Arab Democratic Republic (d. 2016)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Sahrawi_politician)\" title=\"<PERSON> (Sahrawi politician)\"><PERSON></a>, Sahrawi politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Sahrawi_Arab_Democratic_Republic\" title=\"President of the Sahrawi Arab Democratic Republic\">President of the Sahrawi Arab Democratic Republic</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Sahrawi_politician)\" title=\"<PERSON> (Sahrawi politician)\"><PERSON></a>, Sahrawi politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Sahrawi_Arab_Democratic_Republic\" title=\"President of the Sahrawi Arab Democratic Republic\">President of the Sahrawi Arab Democratic Republic</a> (d. 2016)", "links": [{"title": "<PERSON> (Sahrawi politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_(Sahrawi_politician)"}, {"title": "President of the Sahrawi Arab Democratic Republic", "link": "https://wikipedia.org/wiki/President_of_the_Sahrawi_Arab_Democratic_Republic"}]}, {"year": "1947", "text": "<PERSON>, American guitarist, singer-songwriter, and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, singer-songwriter, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, singer-songwriter, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Russian-English cellist and conductor (d. 2014)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English cellist and conductor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English cellist and conductor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American lawyer and politician, 52nd Mayor of St. Paul", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Saint_Paul,_Minnesota\" title=\"List of mayors of Saint Paul, Minnesota\">Mayor of St. Paul</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Saint_Paul,_Minnesota\" title=\"List of mayors of Saint Paul, Minnesota\">Mayor of St. Paul</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of mayors of Saint Paul, Minnesota", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Saint_Paul,_Minnesota"}]}, {"year": "1949", "text": "<PERSON>, American fiddler and composer (d. 2013)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fiddler and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fiddler and composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English actor, director, screenwriter, and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, screenwriter, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, screenwriter, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American rock drummer (d. 2017)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rock drummer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American rock drummer (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Welsh musician, poet and television producer (d. 2025)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh musician, poet and television producer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh musician, poet and television producer (d. 2025)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Geraint_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American Muppet performer (d. 1992)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)\" title=\"<PERSON> (puppeteer)\"><PERSON></a>, American Muppet performer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)\" title=\"<PERSON> (puppeteer)\"><PERSON></a>, American Muppet performer (d. 1992)", "links": [{"title": "<PERSON> (puppeteer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)"}]}, {"year": "1951", "text": "<PERSON>, Canadian actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Joy\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Russian footballer and coach (d. 2012)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and coach (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Brazilian race car driver and businessman", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, German engineer and businessman", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Argentinian tennis player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian footballer and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Romanian-German poet and author, Nobel Prize laureate", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Herta_M%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-German poet and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Her<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-German poet and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Herta_M%C3%BCller"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Indonesian author, poet, and critic (d. 2015)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian author, poet, and critic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Ko<PERSON>\"><PERSON><PERSON></a>, Indonesian author, poet, and critic (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(guitarist,_born_1954)\" class=\"mw-redirect\" title=\"<PERSON> (guitarist, born 1954)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(guitarist,_born_1954)\" class=\"mw-redirect\" title=\"<PERSON> (guitarist, born 1954)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (guitarist, born 1954)", "link": "https://wikipedia.org/wiki/<PERSON>_(guitarist,_born_1954)"}]}, {"year": "1954", "text": "<PERSON>, Colombian lawyer and politician, 38th President of Colombia", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Pastrana_Arango\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Pastrana_Arango\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Pastrana_Arango"}, {"title": "President of Colombia", "link": "https://wikipedia.org/wiki/President_of_Colombia"}]}, {"year": "1955", "text": "<PERSON>, English singer-songwriter and bassist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bassist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bassist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American businessman, co-founded BermanBraun", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>Braun\" class=\"mw-redirect\" title=\"<PERSON><PERSON>Braun\"><PERSON><PERSON>Braun</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>Braun\" class=\"mw-redirect\" title=\"<PERSON><PERSON>Braun\"><PERSON><PERSON>Braun</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Spanish cyclist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American director and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American poet, author, and actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, author, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, author, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, British competitive figure skater", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British competitive figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British competitive figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Scottish banker and accountant", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish banker and accountant", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish banker and accountant", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Brazilian race car driver", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American novelist and essayist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Polish footballer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1959", "text": "<PERSON>, American journalist and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American cult leader (d. 1993)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Swiss singer-songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor, director, and political activist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and political activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and political activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player, coach, and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player, coach, and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American politician (d. 2022)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian singer-songwriter, guitarist, and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American drummer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American golfer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Estonian politician and diplomat, 18th Estonian Minister of Defense", "html": "1966 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian politician and diplomat, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defense</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian politician and diplomat, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defense</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Minister of Defence (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)"}]}, {"year": "1966", "text": "<PERSON>, American skateboarder and stuntman", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder and stuntman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder and stuntman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, German footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Ukrainian singer-songwriter (d. 2015)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian singer-songwriter (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American football player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English actress (d. 2021)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American basketball player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American rapper, songwriter and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper, songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper, songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American singer-songwriter, actor and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, actor and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American tennis player and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Co<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Co<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Estonian author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4hk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4hk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andrus_Kivir%C3%A4hk"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian footballer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/%C3%98yvin<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%98yvin<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%98yvin<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, South Korean singer and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hwa\" title=\"<PERSON><PERSON> <PERSON>-hwa\"><PERSON><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hwa\" title=\"<PERSON><PERSON> <PERSON>-hwa\"><PERSON><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hwa"}]}, {"year": "1971", "text": "<PERSON>, Puerto Rican-American baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian footballer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Bangladeshi cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Habibul_Bashar\" title=\"Habibul Bashar\">Habibul Bashar</a>, Bangladeshi cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Habibul_Bashar\" title=\"Habibul Bashar\">Habi<PERSON> Bashar</a>, Bangladeshi cricketer", "links": [{"title": "Habibul Bashar", "link": "https://wikipedia.org/wiki/Habibul_Bashar"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian-American journalist and television personality", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-American journalist and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-American journalist and television personality", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Austrian composer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Dutch journalist and director", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch journalist and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch journalist and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer and manager (d. 2014)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian race walker", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race walker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, French footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>hi<PERSON>_<PERSON>\" title=\"Thi<PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Welsh guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Welsh guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Welsh guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Finnish singer-songwriter and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player and journalist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>t<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player and journalist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Austrian politician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Zimbabwean cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Spanish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCiza\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCiza\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Daniel_G%C3%BCiza"}]}, {"year": "1980", "text": "<PERSON>, Dutch footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Norwegian singer-songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "Cheerleader <PERSON>, American wrestler and manager", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Cheerleader_<PERSON>\" title=\"Cheerleader <PERSON>\">Cheerleader <PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cheerleader_<PERSON>\" title=\"Cheerleader <PERSON>\">Cheerleader <PERSON></a>, American wrestler and manager", "links": [{"title": "Cheerleader <PERSON>", "link": "https://wikipedia.org/wiki/Cheerleader_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor and musician (d. 2018)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1984)\" title=\"<PERSON> (basketball, born 1984)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1984)\" title=\"<PERSON> (basketball, born 1984)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1984)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1984)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Russian ice dancer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1984", "text": "<PERSON>, British sprint canoeist", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British sprint canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British sprint canoeist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Japanese actress and model", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Y%C5%AB_Aoi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y%C5%AB_Aoi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y%C5%AB_Aoi"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Thomas\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Thomas\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Thomas"}]}, {"year": "1988", "text": "<PERSON>, American actor and director", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Corbet\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Corbet\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>et"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Kuwaiti-British member of ISIS (d. 2015)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Kuwaiti-British member of <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">ISIS</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Kuwaiti-British member of <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">ISIS</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}]}, {"year": "1988", "text": "<PERSON>, Norwegian singer-songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Japanese actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American rapper", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B\" title=\"Lil B\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B\" title=\"Lil B\"><PERSON></a>, American rapper", "links": [{"title": "Lil B", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Scottish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Butler\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Butler\" title=\"<PERSON> Butler\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Butler"}]}, {"year": "1992", "text": "<PERSON><PERSON>, English wrestler", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>vis"}]}, {"year": "1992", "text": "<PERSON>, New Zealand-Australian rugby player (d. 2013)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian rugby player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian rugby player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27utia\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27utia\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chanel_Mata%27utia"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Israeli marathon runner", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli marathon runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli marathon runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Swedish swimmer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Sarah_Sj%C3%B6str%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sj%C3%B6str%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sj%C3%B6str%C3%B6m"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Chinese athlete", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese athlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American singer/songwriter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bridge<PERSON>\"><PERSON></a>, American singer/songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bridgers\"><PERSON></a>, American singer/songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Taissa_Farmiga\" title=\"Taissa Farmiga\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taissa_Farmiga\" title=\"Taissa Farmiga\"><PERSON><PERSON> Farmiga</a>, American actress", "links": [{"title": "Taissa Farmiga", "link": "https://wikipedia.org/wiki/Taissa_Farmiga"}]}, {"year": "1995", "text": "<PERSON>, American figure skater", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Gracie_Gold\" title=\"Gracie Gold\"><PERSON> Gold</a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gracie_Gold\" title=\"Gracie Gold\"><PERSON> Gold</a>, American figure skater", "links": [{"title": "Gracie Gold", "link": "https://wikipedia.org/wiki/Gracie_Gold"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, New Zealand rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Zelezniak\" title=\"<PERSON><PERSON> Watene-Zelezniak\"><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ne-Zelezniak\" title=\"<PERSON><PERSON>ne-Zelezniak\"><PERSON><PERSON>Zele<PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON> W<PERSON>ne-Zelezniak", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Watene-Zelezniak"}]}, {"year": "1996", "text": "<PERSON>, Canadian ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American rapper and songwriter", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Pump\" title=\"<PERSON> Pump\"><PERSON></a>, American rapper and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Pump\" title=\"<PERSON> Pump\"><PERSON></a>, American rapper and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON><PERSON>, German tennis player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>k"}]}, {"year": "2003", "text": "<PERSON> <PERSON>, Australian rapper and songwriter", "html": "2003 - <a href=\"https://wikipedia.org/wiki/The_Kid_<PERSON>\" title=\"The Kid La<PERSON>\">The <PERSON></a>, Australian rapper and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Kid_<PERSON>\" title=\"The Kid <PERSON>\">The <PERSON></a>, Australian rapper and songwriter", "links": [{"title": "The <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "754", "text": "<PERSON><PERSON>, mayor of the palace of Austrasia", "html": "754 - <a href=\"https://wikipedia.org/wiki/Carlo<PERSON>_(mayor_of_the_palace)\" title=\"<PERSON><PERSON> (mayor of the palace)\"><PERSON><PERSON></a>, mayor of the palace of <a href=\"https://wikipedia.org/wiki/Austrasia\" title=\"Austrasia\">Austrasia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carloman_(mayor_of_the_palace)\" title=\"<PERSON><PERSON> (mayor of the palace)\"><PERSON><PERSON></a>, mayor of the palace of <a href=\"https://wikipedia.org/wiki/Austrasia\" title=\"Austrasia\">Austrasia</a>", "links": [{"title": "<PERSON><PERSON> (mayor of the palace)", "link": "https://wikipedia.org/wiki/Carloman_(mayor_of_the_palace)"}, {"title": "Austrasia", "link": "https://wikipedia.org/wiki/Austrasia"}]}, {"year": "949", "text": "<PERSON>, Chinese general and governor", "html": "949 - <a href=\"https://wikipedia.org/wiki/Li_<PERSON>\" title=\"Li Shouzhen\"><PERSON></a>, Chinese general and governor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_<PERSON>\" title=\"Li Shouzhen\"><PERSON></a>, Chinese general and governor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_<PERSON>zhen"}]}, {"year": "1153", "text": "<PERSON><PERSON><PERSON>, Count of Boulogne (b. 1130)", "html": "1153 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV,_Count_of_Boulogne\" title=\"<PERSON><PERSON><PERSON> IV, Count of Boulogne\"><PERSON><PERSON><PERSON> IV, Count of Boulogne</a> (b. 1130)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV,_Count_of_Boulogne\" title=\"<PERSON><PERSON><PERSON> IV, Count of Boulogne\"><PERSON><PERSON><PERSON> IV, Count of Boulogne</a> (b. 1130)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Boulogne", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV,_Count_<PERSON>_Boulogne"}]}, {"year": "1304", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (b. 1243)", "html": "1304 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Fu<PERSON>\" title=\"Emperor Go-Fukakusa\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1243)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Fukakusa\" title=\"Emperor Go-Fukakusa\">Emperor <PERSON><PERSON>Fu<PERSON></a> of Japan (b. 1243)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>"}]}, {"year": "1324", "text": "<PERSON> Brunswick (b. 1293)", "html": "1324 - <a href=\"https://wikipedia.org/wiki/Irene_<PERSON>_Brunswick\" title=\"<PERSON> of Brunswick\"><PERSON> of Brunswick</a> (b. 1293)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irene_<PERSON>_Brunswick\" title=\"<PERSON> of Brunswick\"><PERSON> of Brunswick</a> (b. 1293)", "links": [{"title": "Irene of Brunswick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brunswick"}]}, {"year": "1338", "text": "<PERSON><PERSON>, Japanese samurai (b. 1301)", "html": "1338 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>shi<PERSON>\" title=\"<PERSON><PERSON> Yoshisada\"><PERSON><PERSON></a>, Japanese samurai (b. 1301)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Yoshisada\"><PERSON><PERSON></a>, Japanese samurai (b. 1301)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>a"}]}, {"year": "1424", "text": "<PERSON>, Earl of Buchan (b. c. 1381)", "html": "1424 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Buchan\" title=\"<PERSON>, Earl of Buchan\"><PERSON>, Earl of Buchan</a> (b. c. 1381)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Buchan\" title=\"<PERSON>, Earl of Buchan\"><PERSON>, Earl of Buchan</a> (b. c. 1381)", "links": [{"title": "<PERSON>, Earl of Buchan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1510", "text": "<PERSON>, English politician, Speaker of the House of Commons (b. 1462)", "html": "1510 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a> (b. 1462)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a> (b. 1462)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the House of Commons (United Kingdom)", "link": "https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)"}]}, {"year": "1510", "text": "<PERSON>, English statesman", "html": "1510 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statesman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statesman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1547", "text": "<PERSON><PERSON><PERSON>, Swiss sovereign abbess (b. 1478)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/Kat<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Kat<PERSON>na von <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Swiss sovereign abbess (b. 1478)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kat<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Kat<PERSON>na von <PERSON>\">Kat<PERSON><PERSON> <PERSON></a>, Swiss sovereign abbess (b. 1478)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1673", "text": "<PERSON><PERSON>, Dutch physician and anatomist (b. 1641)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_Graaf\" title=\"Regnier de Graaf\"><PERSON><PERSON></a>, Dutch physician and anatomist (b. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_G<PERSON>af\" title=\"Regnier de Graaf\"><PERSON><PERSON></a>, Dutch physician and anatomist (b. 1641)", "links": [{"title": "Regnier de Graaf", "link": "https://wikipedia.org/wiki/Regnier_de_G<PERSON>af"}]}, {"year": "1676", "text": "<PERSON> von <PERSON>, German author (b. 1621)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Grimmelshausen\" title=\"<PERSON> Grimmelshausen\"><PERSON> Grimmelshausen</a>, German author (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Grimmelshausen\" title=\"<PERSON> Grimmelshausen\"><PERSON> von Grimmelshausen</a>, German author (b. 1621)", "links": [{"title": "<PERSON> von <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1720", "text": "<PERSON>, French scholar and translator (b. 1654)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and translator (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and translator (b. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1723", "text": "<PERSON>, English scholar and academic (b. 1668)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and academic (b. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and academic (b. 1668)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON><PERSON>, Russian poet and playwright (b. 1703)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and playwright (b. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and playwright (b. 1703)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, English-American merchant and politician, 16th Governor of Connecticut (b. 1710)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American merchant and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American merchant and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Connecticut", "link": "https://wikipedia.org/wiki/Governor_of_Connecticut"}]}, {"year": "1786", "text": "<PERSON> the <PERSON>, Prussian king (b. 1712)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, Prussian king (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, Prussian king (b. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}]}, {"year": "1809", "text": "<PERSON>, English businessman and engineer, co-founded Boulton and Watt (b. 1728)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and engineer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a> (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and engineer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a> (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Boulton and Watt", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, English architect and surveyor (b. 1732)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect,_born_1732)\" title=\"<PERSON> (architect, born 1732)\"><PERSON></a>, English architect and surveyor (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(architect,_born_1732)\" title=\"<PERSON> (architect, born 1732)\"><PERSON></a>, English architect and surveyor (b. 1732)", "links": [{"title": "<PERSON> (architect, born 1732)", "link": "https://wikipedia.org/wiki/<PERSON>_(architect,_born_1732)"}]}, {"year": "1834", "text": "<PERSON><PERSON><PERSON>, Ottoman general (b. 1802)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/Husein_Grada%C5%A1%C4%8Devi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ottoman general (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Husein_Grada%C5%A1%C4%8Devi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ottoman general (b. 1802)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Husein_Grada%C5%A1%C4%8Devi%C4%87"}]}, {"year": "1838", "text": "<PERSON>, Italian playwright and poet (b. 1749)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian playwright and poet (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian playwright and poet (b. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, Argentinian general and politician, 1st President of Peru (b. 1778)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (b. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1861", "text": "<PERSON><PERSON>, American politician and diplomat, 1st United States Ambassador to Texas (b. 1806)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Alc%C3%A9e_<PERSON>_la_Branche\" title=\"Alcée Louis la Branche\">Alcée <PERSON></a>, American politician and diplomat, 1st <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Texas\" class=\"mw-redirect\" title=\"United States Ambassador to Texas\">United States Ambassador to Texas</a> (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alc%C3%A9e_<PERSON>_la_Branche\" title=\"Alcée <PERSON> Branche\">Al<PERSON></a>, American politician and diplomat, 1st <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Texas\" class=\"mw-redirect\" title=\"United States Ambassador to Texas\">United States Ambassador to Texas</a> (b. 1806)", "links": [{"title": "Alcée Louis la Branche", "link": "https://wikipedia.org/wiki/Alc%C3%A9e_<PERSON>_la_Branche"}, {"title": "United States Ambassador to Texas", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Texas"}]}, {"year": "1870", "text": "<PERSON><PERSON>, Cuban poet and activist (b. 1818)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban poet and activist (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peru<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban poet and activist (b. 1818)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Peru<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, German linguist and anthropologist (b. 1827)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German linguist and anthropologist (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German linguist and anthropologist (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English engineer and diplomat, 10th Governor of South Australia (b. 1821)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and diplomat, 10th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and diplomat, 10th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of South Australia", "link": "https://wikipedia.org/wiki/Governor_of_South_Australia"}]}, {"year": "1901", "text": "<PERSON>, French organist and composer (b. 1842)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Norwegian-German painter and academic (b. 1825)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-German painter and academic (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-German painter and academic (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Serbian satirist and journalist (b. 1873)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian satirist and journalist (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian satirist and journalist (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>vi%C4%87"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Indian activist (b. 1883)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Russian activist and politician (b. 1873)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian activist and politician (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian activist and politician (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American baseball player (b. 1891)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English-Australian cricketer and journalist (b. 1851)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer and journalist (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer and journalist (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Romanian journalist and author (b. 1848)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian journalist and author (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian journalist and author (b. 1848)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1935", "text": "<PERSON>, American decathlete (b. 1872)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gunn\"><PERSON></a>, American decathlete (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American sociologist and author (b. 1860)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON> of Manila, Spanish-Filipino priest and martyr (b. 1880)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_of_Manila\" title=\"<PERSON> of Manila\"><PERSON> of Manila</a>, Spanish-Filipino priest and martyr (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_of_Manila\" title=\"<PERSON> of Manila\"><PERSON> of Manila</a>, Spanish-Filipino priest and martyr (b. 1880)", "links": [{"title": "<PERSON> of Manila", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_of_Manila"}]}, {"year": "1940", "text": "<PERSON>, American soldier and pilot (b. 1911)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Norwegian police officer and soldier (b. 1919)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian police officer and soldier (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian police officer and soldier (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Filipino journalist, jurist, and politician (b. 1891)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist, jurist, and politician (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist, jurist, and politician (b. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Perfecto"}]}, {"year": "1958", "text": "<PERSON>, English-American fencer (b. 1878)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, English-American fencer (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, English-American fencer (b. 1878)", "links": [{"title": "<PERSON> (fencer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)"}]}, {"year": "1966", "text": "<PERSON>, English race car driver and engineer (b. 1918)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and engineer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and engineer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (b. 1888)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Stern"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Thai director and producer (b. 1908)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Rattana_Pestonji\" title=\"Rattana Pestonji\"><PERSON><PERSON> Pestonji</a>, Thai director and producer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rattana_Pestonji\" title=\"Rattana Pestonji\"><PERSON><PERSON> Pestonji</a>, Thai director and producer (b. 1908)", "links": [{"title": "Rattana Pestonji", "link": "https://wikipedia.org/wiki/Rattana_Pestonji"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 39th <PERSON><PERSON><PERSON><PERSON> (b. 1914)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Maedayama_Eigor%C5%8D\" title=\"Maedayama Eigorō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 39th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON><PERSON>na\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maedayama_Eigor%C5%8D\" title=\"Maedayama Eigorō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 39th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1914)", "links": [{"title": "Maedayama Eigorō", "link": "https://wikipedia.org/wiki/Maedayama_Eigor%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1971", "text": "<PERSON>, German field marshal (b. 1880)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Wilhelm_List\" title=\"Wilhelm List\"><PERSON></a>, German field marshal (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilhelm_List\" title=\"Wilhelm List\"><PERSON></a>, German field marshal (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wilhelm_List"}]}, {"year": "1973", "text": "<PERSON>, American novelist, short story writer, critic, and poet (b. 1889)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, critic, and poet (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, critic, and poet (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, French pianist and composer (b. 1928)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1973", "text": "<PERSON>, American singer and choreographer (b. 1939)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(The_Temptations)\" class=\"mw-redirect\" title=\"<PERSON> (The Temptations)\"><PERSON></a>, American singer and choreographer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(The_Temptations)\" class=\"mw-redirect\" title=\"<PERSON> (The Temptations)\"><PERSON></a>, American singer and choreographer (b. 1939)", "links": [{"title": "<PERSON> (The Temptations)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(The_Temptations)"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American screenwriter, director and producer (b. 1904)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dave<PERSON>\"><PERSON><PERSON></a>, American screenwriter, director and producer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dave<PERSON>\"><PERSON><PERSON></a>, American screenwriter, director and producer (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American roller coaster designer (b. 1907)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Roller_coaster\" title=\"Roller coaster\">roller coaster</a> designer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Roller_coaster\" title=\"Roller coaster\">roller coaster</a> designer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Roller coaster", "link": "https://wikipedia.org/wiki/Roller_coaster"}]}, {"year": "1979", "text": "<PERSON>, American actress and singer (b. 1909)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American songwriter (b. 1896)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Italian drummer and educator (b. 1924)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian drummer and educator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian drummer and educator (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German soldier and politician (b. 1894)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Israeli actor and screenwriter (b. 1929)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli actor and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli actor and screenwriter (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hir"}]}, {"year": "1988", "text": "<PERSON>, Pakistani general and politician, 6th President of Pakistan (b. 1924)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani general and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani general and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>-<PERSON>"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1988", "text": "<PERSON>, American lawyer and politician (b. 1914)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American lawyer and politician (b. 1914)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1988", "text": "<PERSON>, Australian-American actress (b. 1935)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Australian-American actress (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Australian-American actress (b. 1935)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1990", "text": "<PERSON>, American actress and singer (b. 1918)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Chinese mathematician and academic (b. 1920)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese mathematician and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese mathematician and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Italian-American race car driver and businessman (b. 1901)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American race car driver and businessman (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American race car driver and businessman (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby league player (b. 1905)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (b. 1905)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1994", "text": "<PERSON>, American boxer and referee (b. 1902)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and referee (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and referee (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American playwright and screenwriter (b. 1902)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Australian footballer and coach (b. 1933)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish shot putter and actor (b. 1940)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish shot putter and actor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish shot putter and actor (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Komar"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Polish pole vaulter (b. 1950)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Tad<PERSON>z_%C5%9Alusarski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pole vaulter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%9Alusarski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pole vaulter (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadeusz_%C5%9Alusarski"}]}, {"year": "2000", "text": "<PERSON>, English businessman (b. 1929)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Australian author and educator (b. 1925)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian author and educator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian author and educator (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American astrophysicist and academic (b. 1934)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>l"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi poet and journalist (b. 1929)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(poet)\" title=\"<PERSON><PERSON><PERSON><PERSON> (poet)\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi poet and journalist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(poet)\" title=\"<PERSON><PERSON><PERSON><PERSON> (poet)\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi poet and journalist (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(poet)"}]}, {"year": "2007", "text": "<PERSON>, English journalist and politician (b. 1913)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American basketball player (b. 1982)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1982)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2008", "text": "<PERSON>, Italian businessman and politician (b. 1926)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian businessman and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian businessman and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Italian lawyer and politician, 8th President of Italy (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_<PERSON>ga"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Norwegian politician, Minister of Children, Equality and Social Inclusion (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Children,_Equality_and_Social_Inclusion\" class=\"mw-redirect\" title=\"Minister of Children, Equality and Social Inclusion\">Minister of Children, Equality and Social Inclusion</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Children,_Equality_and_Social_Inclusion\" class=\"mw-redirect\" title=\"Minister of Children, Equality and Social Inclusion\">Minister of Children, Equality and Social Inclusion</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Children, Equality and Social Inclusion", "link": "https://wikipedia.org/wiki/Minister_of_Children,_Equality_and_Social_Inclusion"}]}, {"year": "2012", "text": "<PERSON>, American engineer, developed the Datapoint 2200 (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Poor\" title=\"Victor Poor\"><PERSON></a>, American engineer, developed the <a href=\"https://wikipedia.org/wiki/Datapoint_2200\" title=\"Datapoint 2200\">Datapoint 2200</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Poor\" title=\"Victor Poor\"><PERSON></a>, American engineer, developed the <a href=\"https://wikipedia.org/wiki/Datapoint_2200\" title=\"Datapoint 2200\">Datapoint 2200</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Datapoint 2200", "link": "https://wikipedia.org/wiki/Datapoint_2200"}]}, {"year": "2012", "text": "<PERSON>, French businessman (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entrepreneur)\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, French businessman (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(entrepreneur)\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, French businessman (b. 1945)", "links": [{"title": "<PERSON> (entrepreneur)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entrepreneur)"}]}, {"year": "2012", "text": "<PERSON>, Canadian lawyer and politician (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>Staunton\"><PERSON></a>, Canadian lawyer and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>Staunton\"><PERSON></a>, Canadian lawyer and politician (b. 1930)", "links": [{"title": "<PERSON>St<PERSON>on", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American educator and politician (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American educator and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American educator and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odilia_Dank"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American poet and critic (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Jewish-American historian and economist (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish-American historian and economist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish-American historian and economist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American painter (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Frank_<PERSON>%C3%AD<PERSON><PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frank_Mart%C3%<PERSON><PERSON><PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter (b. 1924)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/Frank_Mart%C3%<PERSON><PERSON><PERSON>_(artist)"}]}, {"year": "2013", "text": "<PERSON>, Dutch lieutenant and pilot (b. 1912)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lieutenant and pilot (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lieutenant and pilot (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Norwegian minister and activist (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/B%C3%B8<PERSON>_<PERSON>nu<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian minister and activist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%B8<PERSON>_<PERSON>nu<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian minister and activist (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%B8rre_<PERSON>en"}]}, {"year": "2014", "text": "<PERSON>, German historian and author (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American civil servant and politician, 56th Mayor of Pittsburgh (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil servant and politician, 56th <a href=\"https://wikipedia.org/wiki/Mayor_of_Pittsburgh\" class=\"mw-redirect\" title=\"Mayor of Pittsburgh\">Mayor of Pittsburgh</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil servant and politician, 56th <a href=\"https://wikipedia.org/wiki/Mayor_of_Pittsburgh\" class=\"mw-redirect\" title=\"Mayor of Pittsburgh\">Mayor of Pittsburgh</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Pittsburgh", "link": "https://wikipedia.org/wiki/Mayor_of_Pittsburgh"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Serbian poet and critic (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian poet and critic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian poet and critic (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miodrag_Pavlovi%C4%87"}]}, {"year": "2014", "text": "<PERSON>, French singer-songwriter (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American ballet dancer and actress (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballet dancer and actress (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballet dancer and actress (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German businessman (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian cardinal (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Paskai\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian cardinal (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Paskai\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian cardinal (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Paskai"}]}, {"year": "2016", "text": "<PERSON>, Canadian actor, director, and producer (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and producer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and producer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Countess of Airlie, British countess (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Virginia_<PERSON><PERSON>,_Countess_of_Airlie\" title=\"<PERSON>, Countess of Airlie\"><PERSON>, Countess of Airlie</a>, British countess (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_<PERSON><PERSON>,_Countess_of_Airlie\" title=\"<PERSON>, Countess of Airlie\"><PERSON>, Countess of Airlie</a>, British countess (b. 1933)", "links": [{"title": "<PERSON>, Countess of Airlie", "link": "https://wikipedia.org/wiki/Virginia_<PERSON><PERSON><PERSON><PERSON>,_Countess_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Brazilian media mogul and television host (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian media mogul and television host (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian media mogul and television host (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}