{"date": "December 3", "url": "https://wikipedia.org/wiki/December_3", "data": {"Events": [{"year": "915", "text": "<PERSON> <PERSON> crowns <PERSON><PERSON><PERSON> <PERSON> of Italy as Holy Roman Emperor (probable date).", "html": "915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John <PERSON>\">Pope <PERSON> X</a> crowns <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Italy\" title=\"<PERSON><PERSON><PERSON> I of Italy\"><PERSON><PERSON><PERSON> of Italy</a> as <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> (probable date).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John <PERSON>\">Pope <PERSON> X</a> crowns <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Italy\" title=\"<PERSON><PERSON><PERSON> <PERSON> of Italy\"><PERSON><PERSON><PERSON> of Italy</a> as <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> (probable date).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> of Italy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Italy"}, {"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}]}, {"year": "1775", "text": "American Revolutionary War: <PERSON> Alfred becomes the first vessel to fly the Grand Union Flag (the precursor to the Stars and Stripes); the flag is hoisted by <PERSON>.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/USS_Alfred\" title=\"USS Alfred\">USS <i><PERSON></i></a> becomes the first vessel to fly the <a href=\"https://wikipedia.org/wiki/Grand_Union_Flag\" title=\"Grand Union Flag\">Grand Union Flag</a> (the precursor to the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">Stars and Stripes</a>); the flag is hoisted by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/USS_Alfred\" title=\"USS Alfred\">USS <i><PERSON></i></a> becomes the first vessel to fly the <a href=\"https://wikipedia.org/wiki/Grand_Union_Flag\" title=\"Grand Union Flag\">Grand Union Flag</a> (the precursor to the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">Stars and Stripes</a>); the flag is hoisted by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "USS Alfred", "link": "https://wikipedia.org/wiki/USS_Alfred"}, {"title": "Grand Union Flag", "link": "https://wikipedia.org/wiki/Grand_Union_Flag"}, {"title": "Flag of the United States", "link": "https://wikipedia.org/wiki/Flag_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "War of the Second Coalition: Battle of Wiesloch: Austrian Lieutenant Field Marshal <PERSON> defeats the French at Wiesloch.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/War_of_the_Second_Coalition\" title=\"War of the Second Coalition\">War of the Second Coalition</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Wiesloch_(1799)\" title=\"Battle of Wiesloch (1799)\">Battle of Wiesloch</a>: <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> Lieutenant Field Marshal <a href=\"https://wikipedia.org/wiki/Anton_Szt%C3%A1ray\" title=\"<PERSON>\"><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">French</a> at <a href=\"https://wikipedia.org/wiki/Wiesloch\" title=\"Wiesloch\">Wiesloch</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Second_Coalition\" title=\"War of the Second Coalition\">War of the Second Coalition</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Wiesloch_(1799)\" title=\"Battle of Wiesloch (1799)\">Battle of Wiesloch</a>: <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> Lieutenant Field Marshal <a href=\"https://wikipedia.org/wiki/Anton_Szt%C3%A1ray\" title=\"<PERSON>\"><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">French</a> at <a href=\"https://wikipedia.org/wiki/Wiesloch\" title=\"Wiesloch\">Wiesloch</a>.", "links": [{"title": "War of the Second Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Second_Coalition"}, {"title": "Battle of Wiesloch (1799)", "link": "https://wikipedia.org/wiki/Battle_of_Wiesloch_(1799)"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anton_Szt%C3%A1ray"}, {"title": "First French Empire", "link": "https://wikipedia.org/wiki/First_French_Empire"}, {"title": "Wiesloch", "link": "https://wikipedia.org/wiki/Wies<PERSON>h"}]}, {"year": "1800", "text": "War of the Second Coalition: Battle of Hohenlinden: French General <PERSON> decisively defeats the Archduke <PERSON> of Austria near Munich. Coupled with First Consul <PERSON>'s earlier victory at Marengo, this will force the Austrians to sign an armistice and end the war.", "html": "1800 - War of the Second Coalition: <a href=\"https://wikipedia.org/wiki/Battle_of_Hohenlinden\" title=\"Battle of Hohenlinden\">Battle of Hohenlinden</a>: French General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jean <PERSON>\"><PERSON></a> decisively defeats the <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON> of Austria</a> near <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>. Coupled with <a href=\"https://wikipedia.org/wiki/First_Consul\" class=\"mw-redirect\" title=\"First Consul\">First Consul</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>'s earlier victory at <a href=\"https://wikipedia.org/wiki/Battle_of_Marengo\" title=\"Battle of Marengo\">Marengo</a>, this will force the Austrians to sign <a href=\"https://wikipedia.org/wiki/Treaty_of_Lun%C3%A9ville\" title=\"Treaty of Lunéville\">an armistice</a> and end the war.", "no_year_html": "War of the Second Coalition: <a href=\"https://wikipedia.org/wiki/Battle_of_Hohenlinden\" title=\"Battle of Hohenlinden\">Battle of Hohenlinden</a>: French General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jean <PERSON>\"><PERSON></a> decisively defeats the <a href=\"https://wikipedia.org/wiki/Archduke_John<PERSON>_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON> of Austria</a> near <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>. Coupled with <a href=\"https://wikipedia.org/wiki/First_Consul\" class=\"mw-redirect\" title=\"First Consul\">First Consul</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>'s earlier victory at <a href=\"https://wikipedia.org/wiki/Battle_of_Marengo\" title=\"Battle of Marengo\">Marengo</a>, this will force the Austrians to sign <a href=\"https://wikipedia.org/wiki/Treaty_of_Lun%C3%A9ville\" title=\"Treaty of Lunéville\">an armistice</a> and end the war.", "links": [{"title": "Battle of Hohenlinden", "link": "https://wikipedia.org/wiki/Battle_of_Hohenlinden"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Archdu<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Austria"}, {"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}, {"title": "First Consul", "link": "https://wikipedia.org/wiki/First_Consul"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Marengo", "link": "https://wikipedia.org/wiki/Battle_of_Marengo"}, {"title": "Treaty of Lunéville", "link": "https://wikipedia.org/wiki/Treaty_of_Lun%C3%A9ville"}]}, {"year": "1800", "text": "United States presidential election: The Electoral College casts votes for president and vice president that result in a tie between <PERSON> and <PERSON>.", "html": "1800 - <a href=\"https://wikipedia.org/wiki/1800_United_States_presidential_election\" title=\"1800 United States presidential election\">United States presidential election</a>: The Electoral College casts votes for president and vice president that result in a tie between <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1800_United_States_presidential_election\" title=\"1800 United States presidential election\">United States presidential election</a>: The Electoral College casts votes for president and vice president that result in a tie between <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "1800 United States presidential election", "link": "https://wikipedia.org/wiki/1800_United_States_presidential_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "Illinois becomes the 21st U.S. state.", "html": "1818 - <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a> becomes the 21st <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a> becomes the 21st <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Illinois", "link": "https://wikipedia.org/wiki/Illinois"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1834", "text": "The Zollverein (German Customs Union) begins the first regular census in Germany.", "html": "1834 - The <a href=\"https://wikipedia.org/wiki/Zollverein\" title=\"Zollverein\">Zollverein</a> (German Customs Union) begins the first regular <a href=\"https://wikipedia.org/wiki/Census_in_Germany\" title=\"Census in Germany\">census in Germany</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Zollverein\" title=\"Zollverein\">Zollverein</a> (German Customs Union) begins the first regular <a href=\"https://wikipedia.org/wiki/Census_in_Germany\" title=\"Census in Germany\">census in Germany</a>.", "links": [{"title": "Zollverein", "link": "https://wikipedia.org/wiki/Zollverein"}, {"title": "Census in Germany", "link": "https://wikipedia.org/wiki/Census_in_Germany"}]}, {"year": "1854", "text": "Battle of the Eureka Stockade: More than 20 gold miners at Ballarat, Victoria, are killed by state troopers in an uprising over mining licences.", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Eureka_Stockade\" title=\"Battle of the Eureka Stockade\">Battle of the Eureka Stockade</a>: More than 20 gold miners at <a href=\"https://wikipedia.org/wiki/Ballarat,_Victoria\" class=\"mw-redirect\" title=\"Ballarat, Victoria\">Ballarat, Victoria</a>, are killed by state troopers in an uprising over mining licences.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Eureka_Stockade\" title=\"Battle of the Eureka Stockade\">Battle of the Eureka Stockade</a>: More than 20 gold miners at <a href=\"https://wikipedia.org/wiki/Ballarat,_Victoria\" class=\"mw-redirect\" title=\"Ballarat, Victoria\">Ballarat, Victoria</a>, are killed by state troopers in an uprising over mining licences.", "links": [{"title": "Battle of the Eureka Stockade", "link": "https://wikipedia.org/wiki/Battle_of_the_Eureka_Stockade"}, {"title": "Ballarat, Victoria", "link": "https://wikipedia.org/wiki/Ballarat,_Victoria"}]}, {"year": "1859", "text": "Nigeria's first newspaper, missionary <PERSON>'s <PERSON><PERSON>, was published.", "html": "1859 - Nigeria's first newspaper, missionary <a href=\"https://wikipedia.org/wiki/<PERSON>(missionary)\" title=\"<PERSON> (missionary)\"><PERSON>'s</a> <i><PERSON><PERSON></i>, was published.", "no_year_html": "Nigeria's first newspaper, missionary <a href=\"https://wikipedia.org/wiki/<PERSON>(missionary)\" title=\"<PERSON> (missionary)\"><PERSON>'s</a> <i><PERSON><PERSON></i>, was published.", "links": [{"title": "<PERSON> (missionary)", "link": "https://wikipedia.org/wiki/<PERSON>_(missionary)"}]}, {"year": "1881", "text": "The first issue of Tamperean daily newspaper Aamulehti (\"Morning Paper\") is published.", "html": "1881 - The first issue of <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\">Tamperean</a> daily newspaper <i><a href=\"https://wikipedia.org/wiki/Aamulehti\" title=\"Aamulehti\">A<PERSON>ulehti</a></i> (\"Morning Paper\") is published.", "no_year_html": "The first issue of <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\">Tamperean</a> daily newspaper <i><a href=\"https://wikipedia.org/wiki/Aamulehti\" title=\"Aamulehti\">Aamulehti</a></i> (\"Morning Paper\") is published.", "links": [{"title": "Tampere", "link": "https://wikipedia.org/wiki/Tampere"}, {"title": "<PERSON><PERSON>ule<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aamulehti"}]}, {"year": "1898", "text": "The Duquesne Country and Athletic Club defeats an all-star collection of early football players 16-0, in what is considered to be the first all-star game for professional American football.", "html": "1898 - The <a href=\"https://wikipedia.org/wiki/Duquesne_Country_and_Athletic_Club\" title=\"Duquesne Country and Athletic Club\">Duquesne Country and Athletic Club</a> defeats <a href=\"https://wikipedia.org/wiki/1898_Western_Pennsylvania_All-Star_football_team\" title=\"1898 Western Pennsylvania All-Star football team\">an all-star collection of early football players</a> 16-0, in what is considered to be the first <a href=\"https://wikipedia.org/wiki/All-star_game\" title=\"All-star game\">all-star game</a> for professional <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Duquesne_Country_and_Athletic_Club\" title=\"Duquesne Country and Athletic Club\">Duquesne Country and Athletic Club</a> defeats <a href=\"https://wikipedia.org/wiki/1898_Western_Pennsylvania_All-Star_football_team\" title=\"1898 Western Pennsylvania All-Star football team\">an all-star collection of early football players</a> 16-0, in what is considered to be the first <a href=\"https://wikipedia.org/wiki/All-star_game\" title=\"All-star game\">all-star game</a> for professional <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a>.", "links": [{"title": "Duquesne Country and Athletic Club", "link": "https://wikipedia.org/wiki/Duquesne_Country_and_Athletic_Club"}, {"title": "1898 Western Pennsylvania All-Star football team", "link": "https://wikipedia.org/wiki/1898_Western_Pennsylvania_All-Star_football_team"}, {"title": "All-star game", "link": "https://wikipedia.org/wiki/All-star_game"}, {"title": "American football", "link": "https://wikipedia.org/wiki/American_football"}]}, {"year": "1901", "text": "In a State of the Union message, U.S. President <PERSON> delivers a 20,000-word report to the House of Representatives asking Congress to curb the power of trusts \"within reasonable limits\".  The speech was not delivered in person.", "html": "1901 - In a <a href=\"https://wikipedia.org/wiki/1901_State_of_the_Union_Address\" title=\"1901 State of the Union Address\">State of the Union message</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers a 20,000-word report to the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House of Representatives</a> asking Congress to curb the power of trusts \"within reasonable limits\". The speech was not delivered in person.", "no_year_html": "In a <a href=\"https://wikipedia.org/wiki/1901_State_of_the_Union_Address\" title=\"1901 State of the Union Address\">State of the Union message</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers a 20,000-word report to the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House of Representatives</a> asking Congress to curb the power of trusts \"within reasonable limits\". The speech was not delivered in person.", "links": [{"title": "1901 State of the Union Address", "link": "https://wikipedia.org/wiki/1901_State_of_the_Union_Address"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}]}, {"year": "1904", "text": "The Jovian moon <PERSON><PERSON> is discovered by <PERSON> at California's Lick Observatory.", "html": "1904 - The <a href=\"https://wikipedia.org/wiki/Moons_of_Jupiter\" title=\"Moons of Jupiter\">Jovian moon</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(moon)\" title=\"<PERSON><PERSON> (moon)\"><PERSON><PERSON></a> is discovered by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at California's <a href=\"https://wikipedia.org/wiki/Lick_Observatory\" title=\"Lick Observatory\">Lick Observatory</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Moons_of_Jupiter\" title=\"Moons of Jupiter\">Jovian moon</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(moon)\" title=\"<PERSON><PERSON> (moon)\"><PERSON><PERSON></a> is discovered by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at California's <a href=\"https://wikipedia.org/wiki/Lick_Observatory\" title=\"Lick Observatory\">Lick Observatory</a>.", "links": [{"title": "Moons of Jupiter", "link": "https://wikipedia.org/wiki/Moons_of_Jupiter"}, {"title": "<PERSON><PERSON> (moon)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(moon)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lick Observatory", "link": "https://wikipedia.org/wiki/Lick_Observatory"}]}, {"year": "1910", "text": "Modern neon lighting is first demonstrated by <PERSON> at the Paris Motor Show.", "html": "1910 - Modern <a href=\"https://wikipedia.org/wiki/Neon_lighting\" title=\"Neon lighting\">neon lighting</a> is first demonstrated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Paris_Motor_Show\" title=\"Paris Motor Show\">Paris Motor Show</a>.", "no_year_html": "Modern <a href=\"https://wikipedia.org/wiki/Neon_lighting\" title=\"Neon lighting\">neon lighting</a> is first demonstrated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Paris_Motor_Show\" title=\"Paris Motor Show\">Paris Motor Show</a>.", "links": [{"title": "Neon lighting", "link": "https://wikipedia.org/wiki/Neon_lighting"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Paris Motor Show", "link": "https://wikipedia.org/wiki/Paris_Motor_Show"}]}, {"year": "1912", "text": "Bulgaria, Greece, Montenegro, and Serbia (the Balkan League) sign an armistice with the Ottoman Empire, temporarily halting the First Balkan War. (The armistice will expire on February 3, 1913, and hostilities will resume.)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bulgaria\" class=\"mw-redirect\" title=\"Kingdom of Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Greece\" title=\"Kingdom of Greece\">Greece</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Montenegro\" title=\"Kingdom of Montenegro\">Montenegro</a>, and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia\" title=\"Kingdom of Serbia\">Serbia</a> (the <a href=\"https://wikipedia.org/wiki/Balkan_League\" title=\"Balkan League\">Balkan League</a>) sign an armistice with the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, temporarily halting the <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>. (The armistice will expire on February 3, 1913, and hostilities will resume.)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_Bulgaria\" class=\"mw-redirect\" title=\"Kingdom of Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Greece\" title=\"Kingdom of Greece\">Greece</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Montenegro\" title=\"Kingdom of Montenegro\">Montenegro</a>, and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia\" title=\"Kingdom of Serbia\">Serbia</a> (the <a href=\"https://wikipedia.org/wiki/Balkan_League\" title=\"Balkan League\">Balkan League</a>) sign an armistice with the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, temporarily halting the <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>. (The armistice will expire on February 3, 1913, and hostilities will resume.)", "links": [{"title": "Kingdom of Bulgaria", "link": "https://wikipedia.org/wiki/Kingdom_of_Bulgaria"}, {"title": "Kingdom of Greece", "link": "https://wikipedia.org/wiki/Kingdom_of_Greece"}, {"title": "Kingdom of Montenegro", "link": "https://wikipedia.org/wiki/Kingdom_of_Montenegro"}, {"title": "Kingdom of Serbia", "link": "https://wikipedia.org/wiki/Kingdom_of_Serbia"}, {"title": "Balkan League", "link": "https://wikipedia.org/wiki/Balkan_League"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}]}, {"year": "1919", "text": "After nearly 20 years of planning and construction, including two collapses causing 89 deaths, the Quebec Bridge opens to traffic.", "html": "1919 - After nearly 20 years of planning and construction, including two collapses causing 89 deaths, the <a href=\"https://wikipedia.org/wiki/Quebec_Bridge\" title=\"Quebec Bridge\">Quebec Bridge</a> opens to traffic.", "no_year_html": "After nearly 20 years of planning and construction, including two collapses causing 89 deaths, the <a href=\"https://wikipedia.org/wiki/Quebec_Bridge\" title=\"Quebec Bridge\">Quebec Bridge</a> opens to traffic.", "links": [{"title": "Quebec Bridge", "link": "https://wikipedia.org/wiki/Quebec_Bridge"}]}, {"year": "1920", "text": "Following more than a month of Turkish-Armenian War, the Turkish-dictated Treaty of Alexandropol is concluded.", "html": "1920 - Following more than a month of <a href=\"https://wikipedia.org/wiki/Turkish%E2%80%93Armenian_War\" title=\"Turkish-Armenian War\">Turkish-Armenian War</a>, the Turkish-dictated <a href=\"https://wikipedia.org/wiki/Treaty_of_Alexandropol\" title=\"Treaty of Alexandropol\">Treaty of Alexandropol</a> is concluded.", "no_year_html": "Following more than a month of <a href=\"https://wikipedia.org/wiki/Turkish%E2%80%93Armenian_War\" title=\"Turkish-Armenian War\">Turkish-Armenian War</a>, the Turkish-dictated <a href=\"https://wikipedia.org/wiki/Treaty_of_Alexandropol\" title=\"Treaty of Alexandropol\">Treaty of Alexandropol</a> is concluded.", "links": [{"title": "Turkish-Armenian War", "link": "https://wikipedia.org/wiki/Turkish%E2%80%93Armenian_War"}, {"title": "Treaty of Alexandropol", "link": "https://wikipedia.org/wiki/Treaty_of_Alexandropol"}]}, {"year": "1925", "text": "Final agreement is signed between the Irish Free State, Northern Ireland and the United Kingdom formalizing the Partition of Ireland.", "html": "1925 - Final agreement is signed between the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> and the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> formalizing the <a href=\"https://wikipedia.org/wiki/Partition_of_Ireland\" title=\"Partition of Ireland\">Partition of Ireland</a>.", "no_year_html": "Final agreement is signed between the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> and the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> formalizing the <a href=\"https://wikipedia.org/wiki/Partition_of_Ireland\" title=\"Partition of Ireland\">Partition of Ireland</a>.", "links": [{"title": "Irish Free State", "link": "https://wikipedia.org/wiki/Irish_Free_State"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Partition of Ireland", "link": "https://wikipedia.org/wiki/Partition_of_Ireland"}]}, {"year": "1929", "text": "President <PERSON> delivers his first State of the Union message to Congress. It is presented in the form of a written message rather than a speech.", "html": "1929 - President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his first <a href=\"https://wikipedia.org/wiki/1929_State_of_the_Union_Address\" title=\"1929 State of the Union Address\">State of the Union message</a> to Congress. It is presented in the form of a written message rather than a speech.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his first <a href=\"https://wikipedia.org/wiki/1929_State_of_the_Union_Address\" title=\"1929 State of the Union Address\">State of the Union message</a> to Congress. It is presented in the form of a written message rather than a speech.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1929 State of the Union Address", "link": "https://wikipedia.org/wiki/1929_State_of_the_Union_Address"}]}, {"year": "1938", "text": "Nazi Germany issues the Decree on the Utilization of Jewish Property forcing Jews to sell real property, businesses, and stocks at below market value as part of Aryanization.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> issues the Decree on the Utilization of Jewish Property forcing Jews to sell <a href=\"https://wikipedia.org/wiki/Real_property\" title=\"Real property\">real property</a>, <a href=\"https://wikipedia.org/wiki/Business\" title=\"Business\">businesses</a>, and <a href=\"https://wikipedia.org/wiki/Stocks\" title=\"Stocks\">stocks</a> at below market value as part of <a href=\"https://wikipedia.org/wiki/Aryanization\" title=\"Aryanization\">Aryanization</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> issues the Decree on the Utilization of Jewish Property forcing Jews to sell <a href=\"https://wikipedia.org/wiki/Real_property\" title=\"Real property\">real property</a>, <a href=\"https://wikipedia.org/wiki/Business\" title=\"Business\">businesses</a>, and <a href=\"https://wikipedia.org/wiki/Stocks\" title=\"Stocks\">stocks</a> at below market value as part of <a href=\"https://wikipedia.org/wiki/Aryanization\" title=\"Aryanization\">Aryanization</a>.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Real property", "link": "https://wikipedia.org/wiki/Real_property"}, {"title": "Business", "link": "https://wikipedia.org/wiki/Business"}, {"title": "Stocks", "link": "https://wikipedia.org/wiki/Stocks"}, {"title": "Aryanization", "link": "https://wikipedia.org/wiki/Aryanization"}]}, {"year": "1944", "text": "Greek Civil War: Fighting breaks out in Athens between the ELAS and government forces supported by the British Army.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Greek_Civil_War\" title=\"Greek Civil War\">Greek Civil War</a>: <a href=\"https://wikipedia.org/wiki/Deke<PERSON>vriana\" title=\"Dekemvriana\">Fighting</a> breaks out in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a> between the <a href=\"https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army\" class=\"mw-redirect\" title=\"Greek People's Liberation Army\">ELAS</a> and government forces supported by the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_Civil_War\" title=\"Greek Civil War\">Greek Civil War</a>: <a href=\"https://wikipedia.org/wiki/Dekemvriana\" title=\"Dekemvriana\">Fighting</a> breaks out in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a> between the <a href=\"https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army\" class=\"mw-redirect\" title=\"Greek People's Liberation Army\">ELAS</a> and government forces supported by the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>.", "links": [{"title": "Greek Civil War", "link": "https://wikipedia.org/wiki/Greek_Civil_War"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/De<PERSON><PERSON><PERSON><PERSON>a"}, {"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}, {"title": "Greek People's Liberation Army", "link": "https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>'s war novel The Unknown Soldier (<PERSON><PERSON><PERSON><PERSON>) is published.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/V%C3%A4in%C3%B6_Linna\" title=\"Väinö Linna\"><PERSON>ä<PERSON><PERSON></a>'s war novel <i><a href=\"https://wikipedia.org/wiki/The_Unknown_Soldier_(novel)\" title=\"The Unknown Soldier (novel)\">The Unknown Soldier</a></i> (<i><PERSON><PERSON><PERSON><PERSON> so<PERSON></i>) is published.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%A4in%C3%B6_Linna\" title=\"Väinö Linna\"><PERSON>ä<PERSON><PERSON></a>'s war novel <i><a href=\"https://wikipedia.org/wiki/The_Unknown_Soldier_(novel)\" title=\"The Unknown Soldier (novel)\">The Unknown Soldier</a></i> (<i>Tu<PERSON><PERSON><PERSON> sotil<PERSON></i>) is published.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A4in%C3%B6_Linna"}, {"title": "The Unknown Soldier (novel)", "link": "https://wikipedia.org/wiki/The_Unknown_Soldier_(novel)"}]}, {"year": "1959", "text": "The current flag of Singapore is adopted, six months after Singapore became self-governing within the British Empire.", "html": "1959 - The current <a href=\"https://wikipedia.org/wiki/Flag_of_Singapore\" title=\"Flag of Singapore\">flag of Singapore</a> is adopted, six months after <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> became self-governing within the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "no_year_html": "The current <a href=\"https://wikipedia.org/wiki/Flag_of_Singapore\" title=\"Flag of Singapore\">flag of Singapore</a> is adopted, six months after <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> became self-governing within the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "links": [{"title": "Flag of Singapore", "link": "https://wikipedia.org/wiki/Flag_of_Singapore"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}]}, {"year": "1960", "text": "The musical Camelot debuts at the Majestic Theatre on Broadway. It will become associated with the <PERSON> administration.", "html": "1960 - The musical <i><a href=\"https://wikipedia.org/wiki/Camelot_(musical)\" title=\"Camelot (musical)\">Camelot</a></i> debuts at the <a href=\"https://wikipedia.org/wiki/Majestic_Theatre_(Broadway)\" title=\"Majestic Theatre (Broadway)\">Majestic Theatre</a> on <a href=\"https://wikipedia.org/wiki/Broadway_theatre\" title=\"Broadway theatre\">Broadway</a>. It will become associated with the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> administration</a>.", "no_year_html": "The musical <i><a href=\"https://wikipedia.org/wiki/Camelot_(musical)\" title=\"Camelot (musical)\">Camelot</a></i> debuts at the <a href=\"https://wikipedia.org/wiki/Majestic_Theatre_(Broadway)\" title=\"Majestic Theatre (Broadway)\">Majestic Theatre</a> on <a href=\"https://wikipedia.org/wiki/Broadway_theatre\" title=\"Broadway theatre\">Broadway</a>. It will become associated with the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> administration</a>.", "links": [{"title": "<PERSON><PERSON> (musical)", "link": "https://wikipedia.org/wiki/Came<PERSON>_(musical)"}, {"title": "Majestic Theatre (Broadway)", "link": "https://wikipedia.org/wiki/Majestic_Theatre_(Broadway)"}, {"title": "Broadway theatre", "link": "https://wikipedia.org/wiki/Broadway_theatre"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "Soviet Union, Space probe of the Luna program, called Luna 8, is launched, but crashes on the Moon.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">Space probe</a> of the <a href=\"https://wikipedia.org/wiki/Luna_program\" class=\"mw-redirect\" title=\"Luna program\">Luna program</a>, called <a href=\"https://wikipedia.org/wiki/Luna_8\" title=\"Luna 8\">Luna 8</a>, is launched, but crashes on the Moon.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">Space probe</a> of the <a href=\"https://wikipedia.org/wiki/Luna_program\" class=\"mw-redirect\" title=\"Luna program\">Luna program</a>, called <a href=\"https://wikipedia.org/wiki/Luna_8\" title=\"Luna 8\">Luna 8</a>, is launched, but crashes on the Moon.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Space probe", "link": "https://wikipedia.org/wiki/Space_probe"}, {"title": "Luna program", "link": "https://wikipedia.org/wiki/Luna_program"}, {"title": "Luna 8", "link": "https://wikipedia.org/wiki/Luna_8"}]}, {"year": "1967", "text": "At Groote Schuur Hospital in Cape Town, South Africa, a transplant team headed by <PERSON><PERSON> carries out the first heart transplant on a human (53-year-old <PERSON>).", "html": "1967 - At <a href=\"https://wikipedia.org/wiki/Groote_Schuur_Hospital\" title=\"Groote Schuur Hospital\">Groote Schuur Hospital</a> in <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>, South Africa, a transplant team headed by <a href=\"https://wikipedia.org/wiki/Christiaan_Barnard\" title=\"Christiaan Barnard\"><PERSON><PERSON></a> carries out the first <a href=\"https://wikipedia.org/wiki/Heart_transplant\" class=\"mw-redirect\" title=\"Heart transplant\">heart transplant</a> on a human (53-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>).", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/Groote_Schuur_Hospital\" title=\"Groote Schuur Hospital\">Groote Schuur Hospital</a> in <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>, South Africa, a transplant team headed by <a href=\"https://wikipedia.org/wiki/Christiaan_Barnard\" title=\"Christiaan Barnard\"><PERSON><PERSON></a> carries out the first <a href=\"https://wikipedia.org/wiki/Heart_transplant\" class=\"mw-redirect\" title=\"Heart transplant\">heart transplant</a> on a human (53-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>).", "links": [{"title": "Groote Sc<PERSON>ur Hospital", "link": "https://wikipedia.org/wiki/Groote_Sc<PERSON>ur_Hospital"}, {"title": "Cape Town", "link": "https://wikipedia.org/wiki/Cape_Town"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Heart transplant", "link": "https://wikipedia.org/wiki/Heart_transplant"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "Indo-Pakistani War of 1971: Pakistan launches a pre-emptive strike against India and a full-scale war begins.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1971\">Indo-Pakistani War of 1971</a>: Pakistan launches <a href=\"https://wikipedia.org/wiki/Operation_<PERSON><PERSON>_Khan\" title=\"Operation <PERSON><PERSON> Khan\">a pre-emptive strike</a> against India and a full-scale war begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1971\">Indo-Pakistani War of 1971</a>: Pakistan launches <a href=\"https://wikipedia.org/wiki/Operation_<PERSON><PERSON>_Khan\" title=\"Operation <PERSON><PERSON> Khan\">a pre-emptive strike</a> against India and a full-scale war begins.", "links": [{"title": "Indo-Pakistani War of 1971", "link": "https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971"}, {"title": "Operation <PERSON><PERSON> Khan", "link": "https://wikipedia.org/wiki/Operation_<PERSON><PERSON>_Khan"}]}, {"year": "1972", "text": "Spantax Flight 275 crashes during takeoff from Tenerife North-Ciudad de La Laguna Airport, killing all 155 people on board.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Spantax_Flight_275\" class=\"mw-redirect\" title=\"Spantax Flight 275\">Spantax Flight 275</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Tenerife_North%E2%80%93Ciudad_de_La_Laguna_Airport\" title=\"Tenerife North-Ciudad de La Laguna Airport\">Tenerife North-Ciudad de La Laguna Airport</a>, killing all 155 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spantax_Flight_275\" class=\"mw-redirect\" title=\"Spantax Flight 275\">Spantax Flight 275</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Tenerife_North%E2%80%93Ciudad_de_La_Laguna_Airport\" title=\"Tenerife North-Ciudad de La Laguna Airport\">Tenerife North-Ciudad de La Laguna Airport</a>, killing all 155 people on board.", "links": [{"title": "Spantax Flight 275", "link": "https://wikipedia.org/wiki/Spantax_Flight_275"}, {"title": "Tenerife North-Ciudad de La Laguna Airport", "link": "https://wikipedia.org/wiki/Tenerife_North%E2%80%93Ciudad_de_La_Laguna_Airport"}]}, {"year": "1973", "text": "Pioneer program: Pioneer 10 sends back the first close-up images of Jupiter.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Pioneer_program\" title=\"Pioneer program\">Pioneer program</a>: <a href=\"https://wikipedia.org/wiki/Pioneer_10\" title=\"Pioneer 10\">Pioneer 10</a> sends back the first close-up images of <a href=\"https://wikipedia.org/wiki/Jupiter\" title=\"Jupiter\">Jupiter</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pioneer_program\" title=\"Pioneer program\">Pioneer program</a>: <a href=\"https://wikipedia.org/wiki/Pioneer_10\" title=\"Pioneer 10\">Pioneer 10</a> sends back the first close-up images of <a href=\"https://wikipedia.org/wiki/Jupiter\" title=\"Jupiter\">Jupiter</a>.", "links": [{"title": "Pioneer program", "link": "https://wikipedia.org/wiki/Pioneer_program"}, {"title": "Pioneer 10", "link": "https://wikipedia.org/wiki/Pioneer_10"}, {"title": "Jupiter", "link": "https://wikipedia.org/wiki/Jupiter"}]}, {"year": "1979", "text": "In Cincinnati, 11 fans are suffocated in a crush for seats on the concourse outside Riverfront Coliseum before a Who concert.", "html": "1979 - In <a href=\"https://wikipedia.org/wiki/Cincinnati\" title=\"Cincinnati\">Cincinnati</a>, 11 fans are suffocated in <a href=\"https://wikipedia.org/wiki/The_Who_concert_disaster\" title=\"The Who concert disaster\">a crush for seats</a> on the concourse outside <a href=\"https://wikipedia.org/wiki/Riverfront_Coliseum\" class=\"mw-redirect\" title=\"Riverfront Coliseum\">Riverfront Coliseum</a> before a <a href=\"https://wikipedia.org/wiki/The_Who\" title=\"The Who\">Who</a> concert.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Cincinnati\" title=\"Cincinnati\">Cincinnati</a>, 11 fans are suffocated in <a href=\"https://wikipedia.org/wiki/The_Who_concert_disaster\" title=\"The Who concert disaster\">a crush for seats</a> on the concourse outside <a href=\"https://wikipedia.org/wiki/Riverfront_Coliseum\" class=\"mw-redirect\" title=\"Riverfront Coliseum\">Riverfront Coliseum</a> before a <a href=\"https://wikipedia.org/wiki/The_Who\" title=\"The Who\">Who</a> concert.", "links": [{"title": "Cincinnati", "link": "https://wikipedia.org/wiki/Cincinnati"}, {"title": "The Who concert disaster", "link": "https://wikipedia.org/wiki/The_Who_concert_disaster"}, {"title": "Riverfront Coliseum", "link": "https://wikipedia.org/wiki/Riverfront_Coliseum"}, {"title": "The Who", "link": "https://wikipedia.org/wiki/The_Who"}]}, {"year": "1979", "text": "Iranian Revolution: <PERSON><PERSON><PERSON><PERSON> becomes the first Supreme Leader of Iran.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Iranian_Revolution\" class=\"mw-redirect\" title=\"Iranian Revolution\">Iranian Revolution</a>: <PERSON><PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Khomeini\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Supreme_Leader_of_Iran\" title=\"Supreme Leader of Iran\">Supreme Leader of Iran</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iranian_Revolution\" class=\"mw-redirect\" title=\"Iranian Revolution\">Iranian Revolution</a>: <PERSON><PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>homeini\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Supreme_Leader_of_Iran\" title=\"Supreme Leader of Iran\">Supreme Leader of Iran</a>.", "links": [{"title": "Iranian Revolution", "link": "https://wikipedia.org/wiki/Iranian_Revolution"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>ollah_Khomeini"}, {"title": "Supreme Leader of Iran", "link": "https://wikipedia.org/wiki/Supreme_Leader_of_Iran"}]}, {"year": "1982", "text": "A soil sample is taken from Times Beach, Missouri, that will be found to contain 300 times the safe level of dioxin.", "html": "1982 - A soil sample is taken from <a href=\"https://wikipedia.org/wiki/Times_Beach,_Missouri\" title=\"Times Beach, Missouri\">Times Beach, Missouri</a>, that will be found to contain 300 times the safe level of <a href=\"https://wikipedia.org/wiki/Polychlorinated_dibenzodioxins\" title=\"Polychlorinated dibenzodioxins\">dioxin</a>.", "no_year_html": "A soil sample is taken from <a href=\"https://wikipedia.org/wiki/Times_Beach,_Missouri\" title=\"Times Beach, Missouri\">Times Beach, Missouri</a>, that will be found to contain 300 times the safe level of <a href=\"https://wikipedia.org/wiki/Polychlorinated_dibenzodioxins\" title=\"Polychlorinated dibenzodioxins\">dioxin</a>.", "links": [{"title": "Times Beach, Missouri", "link": "https://wikipedia.org/wiki/Times_Beach,_Missouri"}, {"title": "Polychlorinated dibenzodioxins", "link": "https://wikipedia.org/wiki/Polychlorinated_dibenzodioxins"}]}, {"year": "1984", "text": "Bhopal disaster: A methyl isocyanate leak from a Union Carbide pesticide plant in Bhopal, India, kills more than 3,800 people outright and injures 150,000-600,000 others (some 6,000 of whom later died from their injuries) in one of the worst industrial disasters in history.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Bhopal_disaster\" title=\"Bhopal disaster\">Bhopal disaster</a>: A <a href=\"https://wikipedia.org/wiki/Methyl_isocyanate\" title=\"Methyl isocyanate\">methyl isocyanate</a> leak from a <a href=\"https://wikipedia.org/wiki/Union_Carbide\" title=\"Union Carbide\">Union Carbide</a> pesticide plant in <a href=\"https://wikipedia.org/wiki/Bhopal\" title=\"Bhopal\">Bhopal</a>, India, kills more than 3,800 people outright and injures 150,000-600,000 others (some 6,000 of whom later died from their injuries) in one of the worst industrial disasters in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bhopal_disaster\" title=\"Bhopal disaster\">Bhopal disaster</a>: A <a href=\"https://wikipedia.org/wiki/Methyl_isocyanate\" title=\"Methyl isocyanate\">methyl isocyanate</a> leak from a <a href=\"https://wikipedia.org/wiki/Union_Carbide\" title=\"Union Carbide\">Union Carbide</a> pesticide plant in <a href=\"https://wikipedia.org/wiki/Bhopal\" title=\"Bhopal\">Bhopal</a>, India, kills more than 3,800 people outright and injures 150,000-600,000 others (some 6,000 of whom later died from their injuries) in one of the worst industrial disasters in history.", "links": [{"title": "Bhopal disaster", "link": "https://wikipedia.org/wiki/Bhopal_disaster"}, {"title": "Methyl isocyanate", "link": "https://wikipedia.org/wiki/Methyl_isocyanate"}, {"title": "Union Carbide", "link": "https://wikipedia.org/wiki/Union_Carbide"}, {"title": "Bhopal", "link": "https://wikipedia.org/wiki/Bhopal"}]}, {"year": "1989", "text": "In a meeting off the coast of Malta, U.S. President <PERSON> and Soviet General Secretary <PERSON> release statements indicating that the Cold War between NATO and the Warsaw Pact may be coming to an end.", "html": "1989 - In a <a href=\"https://wikipedia.org/wiki/Malta_Summit\" title=\"Malta Summit\">meeting</a> off the coast of Malta, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Soviet General Secretary <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> release statements indicating that the <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a> between <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> and the <a href=\"https://wikipedia.org/wiki/Warsaw_Pact\" title=\"Warsaw Pact\">Warsaw Pact</a> may be coming to an end.", "no_year_html": "In a <a href=\"https://wikipedia.org/wiki/Malta_Summit\" title=\"Malta Summit\">meeting</a> off the coast of Malta, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Soviet General Secretary <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> release statements indicating that the <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a> between <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> and the <a href=\"https://wikipedia.org/wiki/Warsaw_Pact\" title=\"Warsaw Pact\">Warsaw Pact</a> may be coming to an end.", "links": [{"title": "Malta Summit", "link": "https://wikipedia.org/wiki/Malta_Summit"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}, {"title": "Warsaw Pact", "link": "https://wikipedia.org/wiki/Warsaw_Pact"}]}, {"year": "1992", "text": "The Greek oil tanker Aegean Sea, carrying 80,000 tonnes of crude oil, runs aground in a storm while approaching A Coruña, Spain, and spills much of its cargo.", "html": "1992 - The Greek <a href=\"https://wikipedia.org/wiki/Oil_tanker\" title=\"Oil tanker\">oil tanker</a> <i>Aegean Sea</i>, carrying 80,000 tonnes of <a href=\"https://wikipedia.org/wiki/Crude_oil\" class=\"mw-redirect\" title=\"Crude oil\">crude oil</a>, runs aground in a storm while approaching <a href=\"https://wikipedia.org/wiki/A_Coru%C3%B1a\" title=\"A Coruña\">A Coruña</a>, Spain, and <a href=\"https://wikipedia.org/wiki/Aegean_Sea_oil_spill\" class=\"mw-redirect\" title=\"Aegean Sea oil spill\">spills much of its cargo</a>.", "no_year_html": "The Greek <a href=\"https://wikipedia.org/wiki/Oil_tanker\" title=\"Oil tanker\">oil tanker</a> <i>Aegean Sea</i>, carrying 80,000 tonnes of <a href=\"https://wikipedia.org/wiki/Crude_oil\" class=\"mw-redirect\" title=\"Crude oil\">crude oil</a>, runs aground in a storm while approaching <a href=\"https://wikipedia.org/wiki/A_Coru%C3%B1a\" title=\"A Coruña\">A Coruña</a>, Spain, and <a href=\"https://wikipedia.org/wiki/Aegean_Sea_oil_spill\" class=\"mw-redirect\" title=\"Aegean Sea oil spill\">spills much of its cargo</a>.", "links": [{"title": "Oil tanker", "link": "https://wikipedia.org/wiki/Oil_tanker"}, {"title": "Crude oil", "link": "https://wikipedia.org/wiki/Crude_oil"}, {"title": "A Coruña", "link": "https://wikipedia.org/wiki/A_Coru%C3%B1a"}, {"title": "Aegean Sea oil spill", "link": "https://wikipedia.org/wiki/Aegean_Sea_oil_spill"}]}, {"year": "1992", "text": "A test engineer for Sema Group uses a personal computer to send the world's first text message via the Vodafone network to the phone of a colleague.", "html": "1992 - A test engineer for <a href=\"https://wikipedia.org/wiki/Sema_Group\" title=\"Sema Group\">Sema Group</a> uses a personal computer to send the world's first <a href=\"https://wikipedia.org/wiki/Text_message\" class=\"mw-redirect\" title=\"Text message\">text message</a> via the <a href=\"https://wikipedia.org/wiki/Vodafone\" title=\"Vodafone\">Vodafone</a> network to the phone of a colleague.", "no_year_html": "A test engineer for <a href=\"https://wikipedia.org/wiki/Sema_Group\" title=\"Sema Group\">Sema Group</a> uses a personal computer to send the world's first <a href=\"https://wikipedia.org/wiki/Text_message\" class=\"mw-redirect\" title=\"Text message\">text message</a> via the <a href=\"https://wikipedia.org/wiki/Vodafone\" title=\"Vodafone\">Vodafone</a> network to the phone of a colleague.", "links": [{"title": "Sema Group", "link": "https://wikipedia.org/wiki/Sema_Group"}, {"title": "Text message", "link": "https://wikipedia.org/wiki/Text_message"}, {"title": "Vodafone", "link": "https://wikipedia.org/wiki/Vodafone"}]}, {"year": "1994", "text": "Taiwan holds its first full local elections; <PERSON> elected as the first and only directly elected Governor of Taiwan, <PERSON> became the first directly elected Mayor of Taipei, <PERSON> became the first directly elected Mayor of Kaohsiung.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> holds its first full local elections; <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> elected as the first and only directly elected Governor of <a href=\"https://wikipedia.org/wiki/Taiwan_Province\" title=\"Taiwan Province\">Taiwan</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a> became the first directly elected <a href=\"https://wikipedia.org/wiki/Mayor_of_Taipei\" title=\"Mayor of Taipei\">Mayor</a> of <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a>, <a href=\"https://wikipedia.org/wiki/Wu_Den-yih\" title=\"<PERSON> Den-yih\"><PERSON></a> became the first directly elected <a href=\"https://wikipedia.org/wiki/Mayor_of_Kaohsiung\" title=\"Mayor of Kaohsiung\">Mayor</a> of <a href=\"https://wikipedia.org/wiki/Kaohsiung\" title=\"Kaohsiung\">Kaohsiung</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> holds its first full local elections; <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> elected as the first and only directly elected Governor of <a href=\"https://wikipedia.org/wiki/Taiwan_Province\" title=\"Taiwan Province\">Taiwan</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a> became the first directly elected <a href=\"https://wikipedia.org/wiki/Mayor_of_Taipei\" title=\"Mayor of Taipei\">Mayor</a> of <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a>, <a href=\"https://wikipedia.org/wiki/Wu_Den-yih\" title=\"<PERSON>-yih\"><PERSON></a> became the first directly elected <a href=\"https://wikipedia.org/wiki/Mayor_of_Kaohsiung\" title=\"Mayor of Kaohsiung\">Mayor</a> of <a href=\"https://wikipedia.org/wiki/Kaohsiung\" title=\"Kaohsiung\">Kaohsiung</a>.", "links": [{"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Taiwan Province", "link": "https://wikipedia.org/wiki/Taiwan_Province"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>an"}, {"title": "Mayor of Taipei", "link": "https://wikipedia.org/wiki/Mayor_of_Taipei"}, {"title": "Taipei", "link": "https://wikipedia.org/wiki/Taipei"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-yih"}, {"title": "Mayor of Kaohsiung", "link": "https://wikipedia.org/wiki/Mayor_<PERSON>_Ka<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1994", "text": "Sony releases the PlayStation game console in Japan.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> releases the <a href=\"https://wikipedia.org/wiki/PlayStation_(console)\" title=\"PlayStation (console)\">PlayStation</a> game console in Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> releases the <a href=\"https://wikipedia.org/wiki/PlayStation_(console)\" title=\"PlayStation (console)\">PlayStation</a> game console in Japan.", "links": [{"title": "Sony", "link": "https://wikipedia.org/wiki/Sony"}, {"title": "PlayStation (console)", "link": "https://wikipedia.org/wiki/PlayStation_(console)"}]}, {"year": "1995", "text": "Cameroon Airlines Flight 3701 crashes on approach to Douala International Airport in Douala, Cameroon, killing 71 of the 76 people on board.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Cameroon_Airlines_Flight_3701\" title=\"Cameroon Airlines Flight 3701\">Cameroon Airlines Flight 3701</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Douala_International_Airport\" title=\"Douala International Airport\">Douala International Airport</a> in <a href=\"https://wikipedia.org/wiki/Douala\" title=\"Douala\">Douala</a>, <a href=\"https://wikipedia.org/wiki/Cameroon\" title=\"Cameroon\">Cameroon</a>, killing 71 of the 76 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cameroon_Airlines_Flight_3701\" title=\"Cameroon Airlines Flight 3701\">Cameroon Airlines Flight 3701</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Douala_International_Airport\" title=\"Douala International Airport\">Douala International Airport</a> in <a href=\"https://wikipedia.org/wiki/Douala\" title=\"Douala\">Douala</a>, <a href=\"https://wikipedia.org/wiki/Cameroon\" title=\"Cameroon\">Cameroon</a>, killing 71 of the 76 people on board.", "links": [{"title": "Cameroon Airlines Flight 3701", "link": "https://wikipedia.org/wiki/Cameroon_Airlines_Flight_3701"}, {"title": "Douala International Airport", "link": "https://wikipedia.org/wiki/Douala_International_Airport"}, {"title": "Douala", "link": "https://wikipedia.org/wiki/Douala"}, {"title": "Cameroon", "link": "https://wikipedia.org/wiki/Cameroon"}]}, {"year": "1997", "text": "In Ottawa, Ontario, Canada, representatives from 121 countries sign the Ottawa Treaty prohibiting manufacture and deployment of anti-personnel landmines. The United States, People's Republic of China, and Russia do not sign the treaty, however.", "html": "1997 - In <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa, Ontario</a>, Canada, representatives from 121 countries sign the <a href=\"https://wikipedia.org/wiki/Ottawa_Treaty\" title=\"Ottawa Treaty\">Ottawa Treaty</a> prohibiting manufacture and deployment of <a href=\"https://wikipedia.org/wiki/Anti-personnel_landmine\" class=\"mw-redirect\" title=\"Anti-personnel landmine\">anti-personnel landmines</a>. The United States, People's Republic of China, and Russia do not sign the treaty, however.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa, Ontario</a>, Canada, representatives from 121 countries sign the <a href=\"https://wikipedia.org/wiki/Ottawa_Treaty\" title=\"Ottawa Treaty\">Ottawa Treaty</a> prohibiting manufacture and deployment of <a href=\"https://wikipedia.org/wiki/Anti-personnel_landmine\" class=\"mw-redirect\" title=\"Anti-personnel landmine\">anti-personnel landmines</a>. The United States, People's Republic of China, and Russia do not sign the treaty, however.", "links": [{"title": "Ottawa", "link": "https://wikipedia.org/wiki/Ottawa"}, {"title": "Ottawa Treaty", "link": "https://wikipedia.org/wiki/Ottawa_Treaty"}, {"title": "Anti-personnel landmine", "link": "https://wikipedia.org/wiki/Anti-personnel_landmine"}]}, {"year": "1999", "text": "NASA loses radio contact with the Mars Polar Lander moments before the spacecraft enters the Martian atmosphere.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> loses radio contact with the <a href=\"https://wikipedia.org/wiki/Mars_Polar_Lander\" title=\"Mars Polar Lander\">Mars Polar Lander</a> moments before the spacecraft enters the <a href=\"https://wikipedia.org/wiki/Atmosphere_of_Mars\" title=\"Atmosphere of Mars\">Martian atmosphere</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> loses radio contact with the <a href=\"https://wikipedia.org/wiki/Mars_Polar_Lander\" title=\"Mars Polar Lander\">Mars Polar Lander</a> moments before the spacecraft enters the <a href=\"https://wikipedia.org/wiki/Atmosphere_of_Mars\" title=\"Atmosphere of Mars\">Martian atmosphere</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mars Polar Lander", "link": "https://wikipedia.org/wiki/Mars_Polar_Lander"}, {"title": "Atmosphere of Mars", "link": "https://wikipedia.org/wiki/Atmosphere_of_Mars"}]}, {"year": "1999", "text": "In Worcester, Massachusetts, firefighters responded to a fire at the Worcester Cold Storage and Warehouse Co. facility, which takes the lives of 6 firefighters.", "html": "1999 - In <a href=\"https://wikipedia.org/wiki/Worcester,_Massachusetts\" title=\"Worcester, Massachusetts\">Worcester, Massachusetts</a>, firefighters responded to a fire at the <a href=\"https://wikipedia.org/wiki/Worcester_Cold_Storage_and_Warehouse_Co._fire\" title=\"Worcester Cold Storage and Warehouse Co. fire\">Worcester Cold Storage and Warehouse Co.</a> facility, which takes the lives of 6 firefighters.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Worcester,_Massachusetts\" title=\"Worcester, Massachusetts\">Worcester, Massachusetts</a>, firefighters responded to a fire at the <a href=\"https://wikipedia.org/wiki/Worcester_Cold_Storage_and_Warehouse_Co._fire\" title=\"Worcester Cold Storage and Warehouse Co. fire\">Worcester Cold Storage and Warehouse Co.</a> facility, which takes the lives of 6 firefighters.", "links": [{"title": "Worcester, Massachusetts", "link": "https://wikipedia.org/wiki/Worcester,_Massachusetts"}, {"title": "Worcester Cold Storage and Warehouse Co. fire", "link": "https://wikipedia.org/wiki/Worcester_Cold_Storage_and_Warehouse_Co._fire"}]}, {"year": "2005", "text": "XCOR Aerospace makes the first crewed rocket aircraft delivery of U.S. Mail in Kern County, California.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/XCOR_Aerospace\" title=\"XCOR Aerospace\">XCOR Aerospace</a> makes the <a href=\"https://wikipedia.org/wiki/Rocket_mail#Reusable_launch_vehicles\" title=\"Rocket mail\">first crewed rocket aircraft delivery of U.S. Mail</a> in <a href=\"https://wikipedia.org/wiki/Kern_County,_California\" title=\"Kern County, California\">Kern County, California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/XCOR_Aerospace\" title=\"XCOR Aerospace\">XCOR Aerospace</a> makes the <a href=\"https://wikipedia.org/wiki/Rocket_mail#Reusable_launch_vehicles\" title=\"Rocket mail\">first crewed rocket aircraft delivery of U.S. Mail</a> in <a href=\"https://wikipedia.org/wiki/Kern_County,_California\" title=\"Kern County, California\">Kern County, California</a>.", "links": [{"title": "XCOR Aerospace", "link": "https://wikipedia.org/wiki/XCOR_Aerospace"}, {"title": "Rocket mail", "link": "https://wikipedia.org/wiki/Rocket_mail#Reusable_launch_vehicles"}, {"title": "Kern County, California", "link": "https://wikipedia.org/wiki/Kern_County,_California"}]}, {"year": "2007", "text": "Winter storms cause the Chehalis River to flood many cities in Lewis County, Washington, and close a 32-kilometre (20 mi) portion of Interstate 5 for several days. At least eight deaths and billions of dollars in damages are blamed on the floods.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Great_Coastal_Gale_of_2007\" title=\"Great Coastal Gale of 2007\">Winter storms</a> cause the <a href=\"https://wikipedia.org/wiki/Chehalis_River_(Washington)\" title=\"Chehalis River (Washington)\">Chehalis River</a> to flood many cities in <a href=\"https://wikipedia.org/wiki/Lewis_County,_Washington\" title=\"Lewis County, Washington\">Lewis County, Washington</a>, and close a 32-kilometre (20 mi) portion of <a href=\"https://wikipedia.org/wiki/Interstate_5_in_Washington\" title=\"Interstate 5 in Washington\">Interstate 5</a> for several days. At least eight deaths and billions of dollars in damages are blamed on the floods.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Coastal_Gale_of_2007\" title=\"Great Coastal Gale of 2007\">Winter storms</a> cause the <a href=\"https://wikipedia.org/wiki/Chehalis_River_(Washington)\" title=\"Chehalis River (Washington)\">Chehalis River</a> to flood many cities in <a href=\"https://wikipedia.org/wiki/Lewis_County,_Washington\" title=\"Lewis County, Washington\">Lewis County, Washington</a>, and close a 32-kilometre (20 mi) portion of <a href=\"https://wikipedia.org/wiki/Interstate_5_in_Washington\" title=\"Interstate 5 in Washington\">Interstate 5</a> for several days. At least eight deaths and billions of dollars in damages are blamed on the floods.", "links": [{"title": "Great Coastal Gale of 2007", "link": "https://wikipedia.org/wiki/Great_Coastal_Gale_of_2007"}, {"title": "Chehalis River (Washington)", "link": "https://wikipedia.org/wiki/Chehalis_River_(Washington)"}, {"title": "Lewis County, Washington", "link": "https://wikipedia.org/wiki/Lewis_County,_Washington"}, {"title": "Interstate 5 in Washington", "link": "https://wikipedia.org/wiki/Interstate_5_in_Washington"}]}, {"year": "2009", "text": "A suicide bombing at a hotel in Mogadishu, Somalia, kills 25 people, including three ministers of the Transitional Federal Government.", "html": "2009 - A <a href=\"https://wikipedia.org/wiki/2009_Hotel_Shamo_bombing\" title=\"2009 Hotel Shamo bombing\">suicide bombing</a> at a hotel in <a href=\"https://wikipedia.org/wiki/Mogadishu\" title=\"Mogadishu\">Mogadishu</a>, <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>, kills 25 people, including three ministers of the <a href=\"https://wikipedia.org/wiki/Transitional_Federal_Government\" class=\"mw-redirect\" title=\"Transitional Federal Government\">Transitional Federal Government</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2009_Hotel_Shamo_bombing\" title=\"2009 Hotel Shamo bombing\">suicide bombing</a> at a hotel in <a href=\"https://wikipedia.org/wiki/Mogadishu\" title=\"Mogadishu\">Mogadishu</a>, <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>, kills 25 people, including three ministers of the <a href=\"https://wikipedia.org/wiki/Transitional_Federal_Government\" class=\"mw-redirect\" title=\"Transitional Federal Government\">Transitional Federal Government</a>.", "links": [{"title": "2009 Hotel Shamo bombing", "link": "https://wikipedia.org/wiki/2009_Hotel_Shamo_bombing"}, {"title": "Mogadishu", "link": "https://wikipedia.org/wiki/Mogadishu"}, {"title": "Somalia", "link": "https://wikipedia.org/wiki/Somalia"}, {"title": "Transitional Federal Government", "link": "https://wikipedia.org/wiki/Transitional_Federal_Government"}]}, {"year": "2012", "text": "At least 475 people are killed after Typhoon <PERSON><PERSON> makes landfall in the Philippines.", "html": "2012 - At least 475 people are killed after <a href=\"https://wikipedia.org/wiki/Typhoon_Bopha\" title=\"Typhoon Bopha\">Typhoon <PERSON>pha</a> makes landfall in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "no_year_html": "At least 475 people are killed after <a href=\"https://wikipedia.org/wiki/Typhoon_Bopha\" title=\"Typhoon Bopha\">Typhoon <PERSON></a> makes landfall in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "links": [{"title": "Typhoon <PERSON>pha", "link": "https://wikipedia.org/wiki/<PERSON>_Bo<PERSON>"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "2014", "text": "The Japanese space agency, JAXA, launches the space explorer <PERSON><PERSON><PERSON><PERSON> from the Tanegashima Space Center on a six-year round trip mission to an asteroid to collect rock samples.", "html": "2014 - The Japanese space agency, <a href=\"https://wikipedia.org/wiki/JAXA\" title=\"JAXA\">JAXA</a>, launches the space explorer <a href=\"https://wikipedia.org/wiki/Hayabusa2\" title=\"Hayabusa2\"><PERSON><PERSON>usa2</a> from the <a href=\"https://wikipedia.org/wiki/Tanegashima_Space_Center\" title=\"Tanegashima Space Center\">Tanegashima Space Center</a> on a six-year round trip mission to an <a href=\"https://wikipedia.org/wiki/162173_Ryugu\" title=\"162173 Ryugu\">asteroid</a> to collect rock samples.", "no_year_html": "The Japanese space agency, <a href=\"https://wikipedia.org/wiki/JAXA\" title=\"JAXA\">JAXA</a>, launches the space explorer <a href=\"https://wikipedia.org/wiki/Hayabusa2\" title=\"Hayabusa2\"><PERSON><PERSON><PERSON>2</a> from the <a href=\"https://wikipedia.org/wiki/Tanegashima_Space_Center\" title=\"Tanegashima Space Center\">Tanegashima Space Center</a> on a six-year round trip mission to an <a href=\"https://wikipedia.org/wiki/162173_Ryugu\" title=\"162173 Ryugu\">asteroid</a> to collect rock samples.", "links": [{"title": "JAXA", "link": "https://wikipedia.org/wiki/JAXA"}, {"title": "Hayabusa2", "link": "https://wikipedia.org/wiki/Hayabusa2"}, {"title": "Tanegashima Space Center", "link": "https://wikipedia.org/wiki/Tanegashima_Space_Center"}, {"title": "162173 <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/162173_<PERSON>yugu"}]}, {"year": "2022", "text": "Massive power outage after Moore County substation attack, that leaves 45,000 people, without power, for five days, leading to a FBI probe.", "html": "2022 - Massive power outage after <a href=\"https://wikipedia.org/wiki/Moore_County_substation_attack\" title=\"Moore County substation attack\">Moore County substation attack</a>, that leaves 45,000 people, without power, for five days, leading to a <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> probe.", "no_year_html": "Massive power outage after <a href=\"https://wikipedia.org/wiki/Moore_County_substation_attack\" title=\"Moore County substation attack\">Moore County substation attack</a>, that leaves 45,000 people, without power, for five days, leading to a <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> probe.", "links": [{"title": "Moore County substation attack", "link": "https://wikipedia.org/wiki/Moore_County_substation_attack"}, {"title": "Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Federal_Bureau_of_Investigation"}]}, {"year": "2023", "text": "Mount Marapi located in West Sumatra, Indonesia begins a sporadic series of eruptions. 23 people were killed, and 12 were injured.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Mount_Marapi\" title=\"Mount Marapi\">Mount Marapi</a> located in <a href=\"https://wikipedia.org/wiki/West_Sumatra\" title=\"West Sumatra\">West Sumatra</a>, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> begins a <a href=\"https://wikipedia.org/wiki/2023_eruption_of_Mount_Marapi\" title=\"2023 eruption of Mount Marapi\">sporadic series of eruptions</a>. 23 people were killed, and 12 were injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mount_Marapi\" title=\"Mount Marapi\">Mount Marapi</a> located in <a href=\"https://wikipedia.org/wiki/West_Sumatra\" title=\"West Sumatra\">West Sumatra</a>, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> begins a <a href=\"https://wikipedia.org/wiki/2023_eruption_of_Mount_Marapi\" title=\"2023 eruption of Mount Marapi\">sporadic series of eruptions</a>. 23 people were killed, and 12 were injured.", "links": [{"title": "Mount Marapi", "link": "https://wikipedia.org/wiki/Mount_Marapi"}, {"title": "West Sumatra", "link": "https://wikipedia.org/wiki/West_Sumatra"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "2023 eruption of Mount Marapi", "link": "https://wikipedia.org/wiki/2023_eruption_of_Mount_Marapi"}]}, {"year": "2024", "text": "Martial law is declared in South Korea.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024_South_Korean_martial_law\" class=\"mw-redirect\" title=\"2024 South Korean martial law\">Martial law is declared</a> in <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024_South_Korean_martial_law\" class=\"mw-redirect\" title=\"2024 South Korean martial law\">Martial law is declared</a> in <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>.", "links": [{"title": "2024 South Korean martial law", "link": "https://wikipedia.org/wiki/2024_South_Korean_martial_law"}, {"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}]}], "Births": [{"year": "1368", "text": "<PERSON> of France (d. 1422)", "html": "1368 - <a href=\"https://wikipedia.org/wiki/Charles_<PERSON>_of_France\" title=\"Charles VI of France\"><PERSON> of France</a> (d. 1422)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_<PERSON>_of_France\" title=\"Charles VI of France\"><PERSON> of France</a> (d. 1422)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_VI_of_France"}]}, {"year": "1447", "text": "<PERSON><PERSON><PERSON>, Ottoman sultan (d. 1512)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/Bayezid_II\" title=\"Bayezid II\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (d. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bayezid_II\" title=\"Bayezid II\"><PERSON><PERSON><PERSON> II</a>, Ottoman sultan (d. 1512)", "links": [{"title": "Bayezid II", "link": "https://wikipedia.org/wiki/Bayezid_II"}]}, {"year": "1483", "text": "<PERSON><PERSON>, German theologian and Protestant reformer (d. 1565)", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>dorf\" title=\"<PERSON><PERSON> von Am<PERSON>\"><PERSON><PERSON></a>, German theologian and Protestant reformer (d. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> von Am<PERSON>dorf\"><PERSON><PERSON></a>, German theologian and Protestant reformer (d. 1565)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1560", "text": "<PERSON>, Dutch scholar and critic (d. 1627)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch scholar and critic (d. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch scholar and critic (d. 1627)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1590", "text": "<PERSON>, Flemish Jesuit brother and painter (d. 1661)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish Jesuit brother and painter (d. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish Jesuit brother and painter (d. 1661)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1616", "text": "<PERSON>, English mathematician and cryptographer (d. 1703)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and cryptographer (d. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and cryptographer (d. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1684", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian historian and writer (d. 1754)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>d<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>d<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian historian and writer (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>d<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>d<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian historian and writer (d. 1754)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ludvi<PERSON>_<PERSON>"}]}, {"year": "1722", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian poet, composer, and philosopher (d. 1794)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Skovoroda\" title=\"<PERSON><PERSON><PERSON><PERSON>oroda\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian poet, composer, and philosopher (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Skovoroda\" title=\"<PERSON><PERSON><PERSON><PERSON> Skovoroda\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian poet, composer, and philosopher (d. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>oro<PERSON>", "link": "https://wikipedia.org/wiki/Hryhorii_Skovoroda"}]}, {"year": "1729", "text": "<PERSON>, Spanish composer and theorist (d. 1783)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish composer and theorist (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish composer and theorist (d. 1783)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON><PERSON><PERSON><PERSON>, Maratha ruler of Gwalior (d. 1794)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Mara<PERSON></a> ruler of <a href=\"https://wikipedia.org/wiki/Gwalior\" title=\"Gwalior\">Gwalior</a> (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Mara<PERSON></a> ruler of <a href=\"https://wikipedia.org/wiki/Gwalior\" title=\"Gwalior\">Gwalior</a> (d. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}, {"title": "Gwalior", "link": "https://wikipedia.org/wiki/Gwalior"}]}, {"year": "1755", "text": "<PERSON>, American painter (d. 1828)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, English painter and academic (d. 1867)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (d. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, American politician (d. 1873)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1873)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1800", "text": "<PERSON>, Slovenian poet and lawyer (d. 1849)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/France_Pre%C5%A1eren\" title=\"France Prešeren\">France Prešeren</a>, Slovenian poet and lawyer (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France_Pre%C5%A1eren\" title=\"France Prešeren\">France Prešeren</a>, Slovenian poet and lawyer (d. 1849)", "links": [{"title": "France Prešeren", "link": "https://wikipedia.org/wiki/France_Pre%C5%A1eren"}]}, {"year": "1810", "text": "<PERSON>, American author and political essayist (d. 1879)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>cCord\" class=\"mw-redirect\" title=\"<PERSON> McCord\"><PERSON></a>, American author and political essayist (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>cCord\" class=\"mw-redirect\" title=\"<PERSON> McCord\"><PERSON></a>, American author and political essayist (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, American general and politician, 24th Governor of New Jersey (d. 1885)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1827", "text": "<PERSON><PERSON>, Northern Irish obstetrician and gynaecologist (d. 1910)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll\" title=\"<PERSON><PERSON> Atthill\"><PERSON><PERSON></a>, Northern Irish obstetrician and gynaecologist (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll\" title=\"<PERSON><PERSON> Atthill\"><PERSON><PERSON></a>, Northern Irish obstetrician and gynaecologist (d. 1910)", "links": [{"title": "<PERSON>mbe <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll"}]}, {"year": "1833", "text": "<PERSON>, Cuban epidemiologist and physician (d. 1915)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban epidemiologist and physician (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban epidemiologist and physician (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, American meteorologist and academic (d. 1916)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Cleveland_Abbe\" title=\"Cleveland Abbe\"><PERSON> Abbe</a>, American meteorologist and academic (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cleveland_Abbe\" title=\"Cleveland Abbe\"><PERSON> Abbe</a>, American meteorologist and academic (d. 1916)", "links": [{"title": "Cleveland Abbe", "link": "https://wikipedia.org/wiki/Cleveland_Abbe"}]}, {"year": "1838", "text": "<PERSON><PERSON>, English activist and author (d. 1912)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Octavia_Hill\" title=\"Octavia Hill\">Octavia Hill</a>, English activist and author (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octavia_Hill\" title=\"Octavia Hill\">Octavia Hill</a>, English activist and author (d. 1912)", "links": [{"title": "Octavia Hill", "link": "https://wikipedia.org/wiki/Octavia_Hill"}]}, {"year": "1838", "text": "Princess <PERSON> of Prussia (d. 1923)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Princess_Louise_of_Prussia\" title=\"Princess Louise of Prussia\">Princess <PERSON> of Prussia</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Louise_of_Prussia\" title=\"Princess <PERSON> of Prussia\">Princess <PERSON> of Prussia</a> (d. 1923)", "links": [{"title": "Princess <PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Princess_Louise_<PERSON>_Prussia"}]}, {"year": "1842", "text": "<PERSON>, American philanthropist and activist (d. 1919)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>st\" title=\"<PERSON> Hearst\"><PERSON></a>, American philanthropist and activist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hearst\"><PERSON></a>, American philanthropist and activist (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>st"}]}, {"year": "1842", "text": "<PERSON>, American businessman, founded the Pillsbury Company (d. 1899)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Pillsbury_Company\" class=\"mw-redirect\" title=\"Pillsbury Company\">Pillsbury Company</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Pillsbury_Company\" class=\"mw-redirect\" title=\"Pillsbury Company\">Pillsbury Company</a> (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Pillsbury Company", "link": "https://wikipedia.org/wiki/Pillsbury_Company"}]}, {"year": "1842", "text": "<PERSON>, American chemist, ecologist, and educator (d. 1911)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist, ecologist, and educator (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist, ecologist, and educator (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Irish-Australian politician, 16th Premier of Victoria (d. 1904)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1850", "text": "<PERSON>, English-Australian politician, 23rd Premier of South Australia (d. 1925)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1925)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1856", "text": "<PERSON>, Australian politician, 3rd Premier of Western Australia (d. 1902)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1857", "text": "<PERSON>, Polish-born British novelist (d. 1924)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born British novelist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born British novelist (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON>, Austrian pianist and composer (d. 1944)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian pianist and composer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian pianist and composer (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON><PERSON>, African-American songwriter (d. 1899)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American songwriter (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American songwriter (d. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, Dutch author and playwright (d. 1924)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and playwright (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and playwright (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Canadian lawyer and politician, 17th Premier of British Columbia (d. 1933)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/List_of_premiers_of_British_Columbia\" title=\"List of premiers of British Columbia\">Premier of British Columbia</a> (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/List_of_premiers_of_British_Columbia\" title=\"List of premiers of British Columbia\">Premier of British Columbia</a> (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of premiers of British Columbia", "link": "https://wikipedia.org/wiki/List_of_premiers_of_British_Columbia"}]}, {"year": "1872", "text": "<PERSON>, Canadian lawyer and politician, Canadian Speaker of the Senate (d. 1962)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Senate_(Canada)\" class=\"mw-redirect\" title=\"Speaker of the Senate (Canada)\">Canadian Speaker of the Senate</a> (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Senate_(Canada)\" class=\"mw-redirect\" title=\"Speaker of the Senate (Canada)\">Canadian Speaker of the Senate</a> (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the Senate (Canada)", "link": "https://wikipedia.org/wiki/Speaker_of_the_Senate_(Canada)"}]}, {"year": "1872", "text": "<PERSON>, English cartoonist (d. 1953)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Scottish-Australian painter and educator (d. 1955)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian painter and educator (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian painter and educator (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>drum"}]}, {"year": "1878", "text": "<PERSON>, American businessman (d. 1956)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, New Zealand rugby player (d. 1965)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, American actor, director, and screenwriter (d. 1949)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, Japanese author and playwright (d. 1959)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Kaf%C5%AB_Nagai\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and playwright (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kaf%C5%AB_Nagai\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and playwright (d. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kaf%C5%AB_Nagai"}]}, {"year": "1879", "text": "<PERSON>, Canadian physician and politician, 5th Canadian Minister of National Defence (d. 1970)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and politician, 5th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and politician, 5th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of National Defence (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)"}]}, {"year": "1880", "text": "<PERSON><PERSON>, German field marshal (d. 1945)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field marshal (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field marshal (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Austrian composer and conductor (d. 1945)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Indian lawyer and politician, 1st President of India (d. 1963)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Swiss lawyer and politician, 50th President of the Swiss Confederation (d. 1965)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>li"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1886", "text": "<PERSON><PERSON>, Swedish physicist and academic, Nobel Prize laureate (d. 1978)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Manne_Siegbahn\" title=\"Manne Siegbahn\"><PERSON>e Siegbahn</a>, Swedish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manne_Siegbahn\" title=\"Manne Siegbahn\"><PERSON><PERSON> Siegbahn</a>, Swedish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1978)", "links": [{"title": "Manne Siegbahn", "link": "https://wikipedia.org/wiki/Manne_Siegbahn"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1887", "text": "Prince <PERSON><PERSON><PERSON>, Japanese general and politician, 43rd Prime Minister of Japan (d. 1990)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Prince <PERSON><PERSON><PERSON>\">Prince <PERSON><PERSON><PERSON></a>, Japanese general and politician, 43rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Prince <PERSON><PERSON><PERSON>\">Prince <PERSON><PERSON><PERSON></a>, Japanese general and politician, 43rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1990)", "links": [{"title": "Prince <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1891", "text": "<PERSON>, American general (d. 1967)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" class=\"mw-redirect\" title=\"<PERSON> (general)\"><PERSON></a>, American general (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" class=\"mw-redirect\" title=\"<PERSON> (general)\"><PERSON></a>, American general (d. 1967)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}]}, {"year": "1894", "text": "<PERSON><PERSON>, Indian lawyer and politician (d. 1975)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Deiva_Z<PERSON>tinam\" title=\"<PERSON>iva Zivarattinam\"><PERSON><PERSON></a>, Indian lawyer and politician (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Deiva_Z<PERSON>tinam\" title=\"Deiva Zivarattinam\"><PERSON><PERSON></a>, Indian lawyer and politician (d. 1975)", "links": [{"title": "Deiva Zivarattinam", "link": "https://wikipedia.org/wiki/Deiva_Zivarattinam"}]}, {"year": "1895", "text": "<PERSON>, Austrian-English psychologist and psychoanalyst (d. 1982)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Anna_Freud\" title=\"Anna Freud\"><PERSON></a>, Austrian-English psychologist and psychoanalyst (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anna_Freud\" title=\"Anna Freud\"><PERSON></a>, Austrian-English psychologist and psychoanalyst (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Chinese warlord (d. 1970)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Sheng_<PERSON>i\" title=\"Sheng Shicai\"><PERSON><PERSON></a>, Chinese warlord (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheng_<PERSON>\" title=\"Sheng Shi<PERSON>\"><PERSON><PERSON></a>, Chinese warlord (d. 1970)", "links": [{"title": "Sheng Shi<PERSON>i", "link": "https://wikipedia.org/wiki/Sheng_<PERSON>i"}]}, {"year": "1897", "text": "<PERSON>, American cartoonist and painter (d. 1977)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist and painter (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist and painter (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Japanese politician, 58th Prime Minister of Japan (d. 1965)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Iked<PERSON>\"><PERSON><PERSON></a>, Japanese politician, 58th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> I<PERSON>\"><PERSON><PERSON></a>, Japanese politician, 58th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1899", "text": "<PERSON>, American tennis player (d. 1966)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Australian politician, 18th Premier of Western Australia (d. 1986)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1900", "text": "<PERSON>, Swiss mountaineer (d. 2004)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mountaineer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mountaineer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Austrian-German biochemist and academic, Nobel Prize laureate (d. 1967)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1901", "text": "<PERSON>, American shot putter and discus thrower (d. 1970)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter and discus thrower (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter and discus thrower (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, American high jumper (d. 2000)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American high jumper (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American high jumper (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Japanese captain and pilot (d. 1976)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese captain and pilot (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese captain and pilot (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Estonian chess player and philologist (d. 1993)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player and philologist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player and philologist (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Australian tennis player (d. 1976)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, English cricketer (d. 1990)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American jazz singer (d. 1976)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jazz singer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jazz singer (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Italian pianist, composer, conductor, and academic (d. 1979)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist, composer, conductor, and academic (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist, composer, conductor, and academic (d. 1979)", "links": [{"title": "<PERSON>no <PERSON>", "link": "https://wikipedia.org/wiki/Nino_Rota"}]}, {"year": "1914", "text": "<PERSON>, American composer and academic (d. 1962)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fine\"><PERSON></a>, American composer and academic (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Indonesian general and politician, 12th Indonesian Minister of Defence (d. 2000)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian general and politician, 12th <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Indonesia)\" class=\"mw-redirect\" title=\"Ministry of Defence (Indonesia)\">Indonesian Minister of Defence</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian general and politician, 12th <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Indonesia)\" class=\"mw-redirect\" title=\"Ministry of Defence (Indonesia)\">Indonesian Minister of Defence</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Ministry of Defence (Indonesia)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Indonesia)"}]}, {"year": "1919", "text": "<PERSON>, Canadian journalist and author (d. 1994)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Canadian journalist and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Canadian journalist and author (d. 1994)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1921", "text": "<PERSON>, American soprano and academic (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American lawyer and activist (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actor (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lesser\"><PERSON></a>, American actor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Canadian poet, critic, and academic (d. 1992)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet, critic, and academic (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet, critic, and academic (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Swedish director and cinematographer (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director and cinematographer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director and cinematographer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English cricketer and sportscaster (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Croatian-Serbian footballer and manager (d. 2010)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-Serbian footballer and manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-Serbian footballer and manager (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Australian-English actress, singer, and dancer (d. 2009)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian-English actress, singer, and dancer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian-English actress, singer, and dancer (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": " <PERSON>, American computer scientist, led the team that developed FORTRAN (d. 2007) ", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, led the team that developed <a href=\"https://wikipedia.org/wiki/Fortran\" title=\"Fortran\">FORTRAN</a> (d. 2007) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, led the team that developed <a href=\"https://wikipedia.org/wiki/Fortran\" title=\"Fortran\">FORTRAN</a> (d. 2007) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fortran", "link": "https://wikipedia.org/wiki/Fortran"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Dutch footballer and manager (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Wiel_Coerver\" title=\"Wiel Coerver\"><PERSON><PERSON></a>, Dutch footballer and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wiel_Coerver\" title=\"Wiel Coerver\"><PERSON><PERSON></a>, Dutch footballer and manager (d. 2011)", "links": [{"title": "<PERSON><PERSON>r", "link": "https://wikipedia.org/wiki/Wiel_Coerver"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Filipino journalist, writer and author (d. 2022)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/F._<PERSON><PERSON>l_Jos%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist, writer and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F._<PERSON><PERSON>l_Jos%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist, writer and author (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F._Sionil_Jos%C3%A9"}]}, {"year": "1924", "text": "<PERSON>, Argentinian race car driver and sailor (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver and sailor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver and sailor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American country music singer (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country music singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country music singer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Australian radio and television host (d. 2024)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)\" title=\"<PERSON> (DJ)\"><PERSON></a>, Australian radio and television host (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)\" title=\"<PERSON> (DJ)\"><PERSON></a>, Australian radio and television host (d. 2024)", "links": [{"title": "<PERSON> (DJ)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)"}]}, {"year": "1927", "text": "<PERSON>, American singer (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American politician and diplomat, United States Ambassador to Italy (d. 2004)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Italy\" class=\"mw-redirect\" title=\"United States Ambassador to Italy\">United States Ambassador to Italy</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Italy\" class=\"mw-redirect\" title=\"United States Ambassador to Italy\">United States Ambassador to Italy</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Italy", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Italy"}]}, {"year": "1928", "text": "<PERSON>, Indian-Bangladeshi jurist and politician, Prime Minister of Bangladesh (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Bangladeshi jurist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bangladesh\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bangladesh\">Prime Minister of Bangladesh</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Bangladeshi jurist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bangladesh\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bangladesh\">Prime Minister of Bangladesh</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Bangladesh", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bangladesh"}]}, {"year": "1929", "text": "<PERSON>, American priest and theologian (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and theologian (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and theologian (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, French-Swiss director and screenwriter (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Swiss director and screenwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Swiss director and screenwriter (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Filipino lawyer and politician, 42nd Filipino Secretary of Justice (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician, 42nd <a href=\"https://wikipedia.org/wiki/Secretary_of_Justice_(Philippines)\" title=\"Secretary of Justice (Philippines)\">Filipino Secretary of Justice</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician, 42nd <a href=\"https://wikipedia.org/wiki/Secretary_of_Justice_(Philippines)\" title=\"Secretary of Justice (Philippines)\">Filipino Secretary of Justice</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Secretary of Justice (Philippines)", "link": "https://wikipedia.org/wiki/Secretary_of_Justice_(Philippines)"}]}, {"year": "1930", "text": "<PERSON>, Canadian sculptor (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Canadian sculptor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Canadian sculptor (d. 2017)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1931", "text": "<PERSON>, German author and poet (d. 2011)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American singer and actress", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Japanese lawyer and politician (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Takao_<PERSON>nami"}]}, {"year": "1933", "text": "<PERSON>, British-American actor (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Dutch chemist and engineer, Nobel Prize laureate (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1934", "text": "<PERSON>, Russian general, pilot and cosmonaut (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general, pilot and cosmonaut (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general, pilot and cosmonaut (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON>, Peruvian philosopher and academic (d. 2021)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>uzm%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Peruvian philosopher and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Peruvian philosopher and academic (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abimael_Guzm%C3%A1n"}]}, {"year": "1935", "text": "<PERSON>, American nurse and politician (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and politician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American race car driver and businessman (d. 2024)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American-Irish model and author", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Irish model and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Irish model and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Morgan_Llywelyn"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician (d. 1989)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9part\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9part\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9part"}]}, {"year": "1938", "text": "<PERSON>, American mathematician and engineer (d. 1998)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and engineer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and engineer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Dutch-American race car driver", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON>.</a>, Dutch-American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON>.</a>, Dutch-American race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1939", "text": "<PERSON>, English chemist and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English chemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English chemist and academic", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_(chemist)"}]}, {"year": "1940", "text": "<PERSON>, American academic and religious leader", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and religious leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and religious leader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Northern Irish-Irish rugby player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Northern Irish-Irish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Northern Irish-Irish rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1942", "text": "<PERSON>, Uruguayan footballer and manager (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Uruguayan_footballer)\" title=\"<PERSON> (Uruguayan footballer)\"><PERSON></a>, Uruguayan footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Uruguayan_footballer)\" title=\"<PERSON> (Uruguayan footballer)\"><PERSON></a>, Uruguayan footballer and manager (d. 2013)", "links": [{"title": "<PERSON> (Uruguayan footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Uruguayan_footballer)"}]}, {"year": "1942", "text": "<PERSON>, German journalist and publisher, founded EMMA Magazine", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and publisher, founded <i><a href=\"https://wikipedia.org/wiki/EMMA_(magazine)\" title=\"EMMA (magazine)\">EMMA Magazine</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and publisher, founded <i><a href=\"https://wikipedia.org/wiki/EMMA_(magazine)\" title=\"EMMA (magazine)\">EMMA Magazine</a></i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "EMMA (magazine)", "link": "https://wikipedia.org/wiki/EMMA_(magazine)"}]}, {"year": "1942", "text": "<PERSON>, American journalist and author", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American lawyer and politician, 5th Governor of Guam", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Governor_of_Guam\" class=\"mw-redirect\" title=\"Governor of Guam\">Governor of Guam</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Governor_of_Guam\" class=\"mw-redirect\" title=\"Governor of Guam\">Governor of Guam</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Guam", "link": "https://wikipedia.org/wiki/Governor_of_Guam"}]}, {"year": "1943", "text": "<PERSON><PERSON> <PERSON>, English-Canadian psychologist and academic (d. 2012)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Canadian psychologist and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, English-Canadian psychologist and academic (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English poet, author, and playwright", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, author, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, author, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese musician (d. 1984)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Varia%C3%A7%C3%B5es\" title=\"António Variações\"><PERSON><PERSON><PERSON><PERSON> Variações</a>, Portuguese musician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Varia%C3%A7%C3%B5es\" title=\"António Variações\"><PERSON>t<PERSON><PERSON> Variações</a>, Portuguese musician (d. 1984)", "links": [{"title": "António Variações", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_Varia%C3%A7%C3%B5es"}]}, {"year": "1948", "text": "<PERSON>, Czech violinist and songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>b%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_Hrub%C3%BD"}]}, {"year": "1948", "text": "<PERSON>, English architect and television host", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, English singer-songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Oz<PERSON>_<PERSON>bourne\" title=\"<PERSON><PERSON> Osbourne\"><PERSON><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oz<PERSON>_<PERSON>bourne\" title=\"<PERSON><PERSON> Osbourne\"><PERSON><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oz<PERSON>_<PERSON>bourne"}]}, {"year": "1949", "text": "<PERSON>, Canadian-American actress (d. 2017)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1950", "text": "<PERSON>, Cuban runner", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American basketball player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American basketball player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1951", "text": "<PERSON>, American wrestler and trainer (d. 1994)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Candy\"><PERSON></a>, American wrestler and trainer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ray Candy\"><PERSON></a>, American wrestler and trainer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American race car driver", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English songwriter, record producer, and musician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, English songwriter, record producer, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, English songwriter, record producer, and musician", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Israeli-American evangelist and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American evangelist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American evangelist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American guitarist and songwriter (d. 2006)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist and songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist and songwriter (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English comedian, actor, director, and producer (d. 2013)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, director, and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, director, and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Austrian skier and race car driver", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier and race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier and race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American-Norwegian vibraphonist and contemporary composer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Norwegian vibraphonist and contemporary composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ing\"><PERSON></a>, American-Norwegian vibraphonist and contemporary composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American-English author, poet, and playwright", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English author, poet, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English author, poet, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Central African politician, Mayor of Bangui", "html": "1956 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"É<PERSON>\"><PERSON><PERSON></a>, Central African politician, Mayor of Bangui", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"É<PERSON>\"><PERSON><PERSON></a>, Central African politician, Mayor of Bangui", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Polish physician and politician, 15th Prime Minister of Poland", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish physician and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish physician and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "1957", "text": "<PERSON>, Russian businessman and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Korobov\" title=\"<PERSON> Korobov\"><PERSON></a>, Russian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Korobov\" title=\"<PERSON> Korobov\"><PERSON></a>, Russian businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maxim_Korobov"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Irish journalist and game show host", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish journalist and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish journalist and game show host", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actress and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Russian ice hockey player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American actress and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American ice hockey player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1961", "text": "<PERSON>, American economist and business executive (d. 2024)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and business executive (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and business executive (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English banker, journalist, and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English banker, journalist, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English banker, journalist, and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Ukrainian hurdler", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(hurdler)\" title=\"<PERSON><PERSON> (hurdler)\"><PERSON><PERSON></a>, Ukrainian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(hurdler)\" title=\"<PERSON><PERSON> (hurdler)\"><PERSON><PERSON></a>, Ukrainian hurdler", "links": [{"title": "<PERSON><PERSON> (hurdler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(hurdler)"}]}, {"year": "1962", "text": "<PERSON>, American basketball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American medical patient (d. 2005)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American medical patient (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American medical patient (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Te<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player and sportscaster (d. 2015)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American voice actor, director, producer, screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, director, producer, screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, director, producer, screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, German figure skater and actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>itt\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German figure skater and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German figure skater and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>itt"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Danish footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Povlsen\" title=\"<PERSON>lemming Povlsen\"><PERSON><PERSON><PERSON></a>, Danish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lem<PERSON>_Povlsen\" title=\"Flemming Povlsen\"><PERSON><PERSON><PERSON></a>, Danish footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Russian figure skater and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Burkinabé mathematician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burkinabé mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burkinabé mathematician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English guitarist and songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American businessman", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nner"}]}, {"year": "1970", "text": "<PERSON>, American baseball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, French footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English-Jamaican footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Jamaican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Jamaican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Dutch footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/He<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1971", "text": "<PERSON>, American mixed martial artist and wrestler", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fighter)\" title=\"<PERSON> (fighter)\"><PERSON></a>, American mixed martial artist and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fighter)\" title=\"<PERSON> (fighter)\"><PERSON></a>, American mixed martial artist and wrestler", "links": [{"title": "<PERSON> (fighter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fighter)"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Italian runner", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1973", "text": "<PERSON>, Brazilian-American actor and lawyer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American actor and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American actor and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bruno_<PERSON>s"}]}, {"year": "1973", "text": "<PERSON>, American actress and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON> <PERSON><PERSON><PERSON>, American rapper", "html": "1973 - <a href=\"https://wikipedia.org/wiki/MC_Frontalot\" title=\"MC Frontalot\">MC <PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/MC_Frontalot\" title=\"MC Frontalot\">MC <PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "MC Frontalot", "link": "https://wikipedia.org/wiki/MC_Frontalot"}]}, {"year": "1973", "text": "<PERSON><PERSON>, South African cricketer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Cha<PERSON>_<PERSON>\" title=\"Charl <PERSON>\"><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cha<PERSON>_<PERSON>\" title=\"Charl <PERSON>\">Cha<PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cha<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Swedish journalist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Lucette_R%C3%A5dstr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucette_R%C3%A5dstr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucette_R%C3%A5dstr%C3%B6m"}]}, {"year": "1976", "text": "<PERSON>, South African cricketer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, New Zealand rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Japanese soprano", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Japanese soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Japanese soprano", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_(singer)"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Chad_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chad_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chad_Durbin"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1977", "text": "<PERSON>, Polish ski jumper and race car driver", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Adam_Ma%C5%82ysz\" title=\"<PERSON>\"><PERSON></a>, Polish ski jumper and race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adam_Ma%C5%82ysz\" title=\"<PERSON>\"><PERSON></a>, Polish ski jumper and race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adam_Ma%C5%82ysz"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Russian runner", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aya"}]}, {"year": "1978", "text": "<PERSON>, Swedish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Bicek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Bicek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Bicek"}]}, {"year": "1978", "text": "<PERSON>, Dutch cyclist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ink"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American rapper and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Trina\" title=\"<PERSON>na\"><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trina\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trina"}]}, {"year": "1979", "text": "<PERSON>, New Zealand-English singer-songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Rock_Cartwright\" title=\"<PERSON> Cartwright\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rock_Cartwright\" title=\"<PERSON> Cartwright\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Cartwright"}]}, {"year": "1979", "text": "<PERSON>, American comedian and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American entrepreneur and philanthropist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress and dancer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Bosnian-Irish diarist ", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>lata_Filipovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian-Irish diarist ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lata_Filipovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian-Irish diarist ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zlata_Filipovi%C4%87"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor and musician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Tyju<PERSON>_Hagler\" title=\"Tyjuan Hagler\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tyju<PERSON>_Hagler\" title=\"Tyjuan Hagler\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Venezuelan boxer (d. 2010)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan boxer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan boxer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Spanish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/David_<PERSON>\" title=\"David Villa\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/David_Villa\" title=\"David Villa\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/David_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Panamanian baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manny_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Ghanaian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Dominican-American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>cha_Polanco"}]}, {"year": "1982", "text": "<PERSON>, Argentinian-Italian rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, New Zealand rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Russian decathlete", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian decathlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American singer, songwriter, and record producer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter, and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Spanish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Av<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Av<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American martial artist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian swimmer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Cseh\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Cseh\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> C<PERSON>h\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian swimmer", "links": [{"title": "László <PERSON>h", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Cseh"}]}, {"year": "1985", "text": "<PERSON>, American soccer player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1985", "text": "<PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Swift\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1985)\" title=\"<PERSON> (basketball, born 1985)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1985)\" title=\"<PERSON> (basketball, born 1985)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1985)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1985)"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Swedish singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6nwall\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6nwall\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Erik_Gr%C3%B6nwall"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American gymnast", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Chilean saxophonist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean saxophonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean saxophonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Sel%C3%A7uk_<PERSON>z\" title=\"Selçuk Alibaz\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sel%C3%A7uk_Alibaz\" title=\"Selçuk Alibaz\"><PERSON><PERSON><PERSON><PERSON> Ali<PERSON>z</a>, Turkish footballer", "links": [{"title": "Selçuk Alibaz", "link": "https://wikipedia.org/wiki/Sel%C3%A7uk_Ali<PERSON>z"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Polish mixed martial artist", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mixed martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Belgian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian-Israeli tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Israeli tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Israeli tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (infielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Georgian tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Ekateri<PERSON>_<PERSON>\" title=\"Ekateri<PERSON> Gorgo<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Georgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ekateri<PERSON>_<PERSON>\" title=\"Ekateri<PERSON> Gorgo<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Georgian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ekaterine_<PERSON>ze"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English singer-songwriter, musician and actor[citation needed]", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, musician and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, musician and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American rapper", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Baby\" title=\"Lil Baby\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Baby\" title=\"Lil Baby\"><PERSON></a>, American rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, New Zealand rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Solomon<PERSON>_Kata\" title=\"Solomone Kata\"><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solomon<PERSON>_Kata\" title=\"Solomone Kata\"><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Solomon<PERSON>_Kata"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Finnish ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "311", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman emperor (b. 244)", "html": "311 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>ian\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (b. 244)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>ian\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (b. 244)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian"}]}, {"year": "649", "text": "<PERSON><PERSON><PERSON>, French-English bishop and saint (b. 600)", "html": "649 - <a href=\"https://wikipedia.org/wiki/<PERSON>iri<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-English bishop and saint (b. 600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>iri<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-English bishop and saint (b. 600)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birinus"}]}, {"year": "860", "text": "<PERSON><PERSON><PERSON>, bishop of Auxerre", "html": "860 - <a href=\"https://wikipedia.org/wiki/Abbo_of_Auxerre\" title=\"<PERSON><PERSON><PERSON> of Auxerre\"><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Ancient_Diocese_of_Auxerre\" class=\"mw-redirect\" title=\"Ancient Diocese of Auxerre\">Auxerre</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abbo_of_Auxerre\" title=\"<PERSON><PERSON><PERSON> of Auxerre\"><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Ancient_Diocese_of_Auxerre\" class=\"mw-redirect\" title=\"Ancient Diocese of Auxerre\">Auxerre</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Auxerre", "link": "https://wikipedia.org/wiki/Abbo_of_Auxerre"}, {"title": "Ancient Diocese of Auxerre", "link": "https://wikipedia.org/wiki/Ancient_Diocese_of_Auxerre"}]}, {"year": "937", "text": "<PERSON><PERSON><PERSON>, Frankish nobleman", "html": "937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Merseburg\" title=\"<PERSON><PERSON><PERSON>, Count of Merseburg\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Merseburg\" title=\"<PERSON><PERSON><PERSON>, Count of Merseburg\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Merseburg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Merseburg"}, {"title": "Nobility", "link": "https://wikipedia.org/wiki/Nobility"}]}, {"year": "978", "text": "<PERSON>, Coptic pope of Alexandria", "html": "978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Alexandria\" title=\"Pope <PERSON> of Alexandria\"><PERSON></a>, Coptic pope of <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Alexandria\" title=\"Pope <PERSON> of Alexandria\"><PERSON></a>, Coptic pope of <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>", "links": [{"title": "<PERSON> Alexandria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Alexandria"}, {"title": "Alexandria", "link": "https://wikipedia.org/wiki/Alexandria"}]}, {"year": "1038", "text": "<PERSON> of Lesum, Saxon countess and Saint", "html": "1038 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Lesum\" title=\"<PERSON> of Lesum\"><PERSON> of Lesum</a>, Saxon countess and Saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lesum\"><PERSON> of Lesum</a>, Saxon countess and Saint", "links": [{"title": "Emma of Lesum", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1099", "text": "<PERSON> (b. 1065)", "html": "1099 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Salisbury)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (bishop of Salisbury)\"><PERSON></a> (b. 1065)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Salisbury)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (bishop of Salisbury)\"><PERSON></a> (b. 1065)", "links": [{"title": "<PERSON><PERSON><PERSON> (bishop of Salisbury)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Salisbury)"}]}, {"year": "1154", "text": "<PERSON> <PERSON><PERSON><PERSON> (b. 1073)", "html": "1154 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ana<PERSON>sius_IV\" title=\"<PERSON> <PERSON><PERSON>sius IV\">Pope <PERSON><PERSON><PERSON> IV</a> (b. 1073)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ana<PERSON>sius_IV\" title=\"Pope <PERSON><PERSON>sius IV\">Pope <PERSON><PERSON><PERSON> IV</a> (b. 1073)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_IV"}]}, {"year": "1265", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian lawyer and jurist", "html": "1265 - <a href=\"https://wikipedia.org/wiki/O<PERSON>fredus\" title=\"<PERSON>dofred<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian lawyer and jurist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O<PERSON>fred<PERSON>\" title=\"<PERSON><PERSON>fred<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian lawyer and jurist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON><PERSON>us"}]}, {"year": "1266", "text": "<PERSON> the White, Duke of Wroclaw", "html": "1266 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_White\" title=\"<PERSON> III the White\"><PERSON> the <PERSON></a>, Duke of Wroclaw", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_White\" title=\"<PERSON> the White\"><PERSON> the <PERSON></a>, Duke of Wroclaw", "links": [{"title": "<PERSON> the White", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_White"}]}, {"year": "1309", "text": "<PERSON>, Duke of Głogów (b. 1251/60)", "html": "1309 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_G%C5%82og%C3%B3w\" title=\"<PERSON>, Duke of Głogów\"><PERSON>, Duke of Głogów</a> (b. 1251/60)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_G%C5%82og%C3%B3w\" title=\"<PERSON>, Duke of Głogów\"><PERSON>, Duke of Głogów</a> (b. 1251/60)", "links": [{"title": "<PERSON>, Duke of Głogów", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_G%C5%82og%C3%B3w"}]}, {"year": "1322", "text": "<PERSON>, Countess of Leicester (b. 1282)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Countess of Leicester (b. 1282)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Countess of Leicester (b. 1282)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1532", "text": "<PERSON>, Count <PERSON><PERSON> of Zweibrücken (b. 1502)", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>, Count <PERSON><PERSON> of Zweibrücken\"><PERSON>, Count <PERSON><PERSON> of Zweibrücken</a> (b. 1502)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>, Count <PERSON><PERSON> of Zweibrücken\"><PERSON>, Count <PERSON><PERSON> of Zweibrücken</a> (b. 1502)", "links": [{"title": "<PERSON>, Count <PERSON><PERSON> of Zweibrücken", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1533", "text": "<PERSON><PERSON><PERSON> of Russia (b. 1479)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Russia\" title=\"<PERSON><PERSON><PERSON> III of Russia\"><PERSON><PERSON><PERSON> of Russia</a> (b. 1479)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Russia\" title=\"<PERSON><PERSON><PERSON> III of Russia\"><PERSON><PERSON><PERSON> of Russia</a> (b. 1479)", "links": [{"title": "<PERSON><PERSON><PERSON> of Russia", "link": "https://wikipedia.org/wiki/V<PERSON>li_III_of_Russia"}]}, {"year": "1542", "text": "<PERSON>, French scholar and academic (b. 1470)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and academic (b. 1470)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and academic (b. 1470)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1552", "text": "<PERSON>, Spanish missionary and saint (b. 1506)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish missionary and saint (b. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish missionary and saint (b. 1506)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1592", "text": "<PERSON>, Duke of Parma (b. 1545)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (b. 1545)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (b. 1545)", "links": [{"title": "<PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma"}]}, {"year": "1610", "text": "<PERSON>, Japanese general and daimyō (b. 1548)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/Honda_Tadakatsu\" title=\"Honda Tadakatsu\"><PERSON> Tadakatsu</a>, Japanese general and daimyō (b. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honda_Tadakatsu\" title=\"Honda Tadakatsu\"><PERSON> Tadakatsu</a>, Japanese general and daimyō (b. 1548)", "links": [{"title": "Honda Tadakatsu", "link": "https://wikipedia.org/wiki/Honda_Tadakatsu"}]}, {"year": "1668", "text": "<PERSON>, 2nd Earl of Salisbury (b. 1591)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Salisbury\" title=\"<PERSON>, 2nd Earl of Salisbury\"><PERSON>, 2nd Earl of Salisbury</a> (b. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Salisbury\" title=\"<PERSON>, 2nd Earl of Salisbury\"><PERSON>, 2nd Earl of Salisbury</a> (b. 1591)", "links": [{"title": "<PERSON>, 2nd Earl of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Salisbury"}]}, {"year": "1691", "text": "<PERSON>, Viscountess <PERSON>, British scientist (b. 1615)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscountess_<PERSON><PERSON>\" title=\"<PERSON>, Viscountess Ra<PERSON>\"><PERSON>, Viscountess <PERSON></a>, British scientist (b. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscountess_<PERSON>\" title=\"<PERSON>, Viscountess <PERSON>\"><PERSON>, Viscountess <PERSON></a>, British scientist (b. 1615)", "links": [{"title": "<PERSON>, Viscountess <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscountess_<PERSON>"}]}, {"year": "1706", "text": "Countess <PERSON><PERSON> of Barby-Mühlingen (b. 1637)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON>_of_Barby-M%C3%BChlingen\" class=\"mw-redirect\" title=\"Countess <PERSON><PERSON> of Barby-Mühlingen\">Countess <PERSON><PERSON> of Barby-Mühlingen</a> (b. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON>_of_Barby-M%C3%BChlingen\" class=\"mw-redirect\" title=\"Countess <PERSON><PERSON> of Barby-Mühlingen\">Countess <PERSON><PERSON> of Barby-Mühlingen</a> (b. 1637)", "links": [{"title": "Countess <PERSON><PERSON> of Barby-Mühlingen", "link": "https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON><PERSON>_of_Barby-M%C3%BChlingen"}]}, {"year": "1752", "text": "<PERSON><PERSON><PERSON>, Walloon musician and composer (b. 1685)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Walloon musician and composer (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Walloon musician and composer (b. 1685)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1765", "text": "Lord <PERSON>, English cricketer and politician (b. 1713)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English cricketer and politician (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English cricketer and politician (b. 1713)", "links": [{"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, French painter (b. 1714)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, American archbishop (b. 1735)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>(bishop)\" class=\"mw-redirect\" title=\"<PERSON> (bishop)\"><PERSON></a>, American archbishop (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bishop)\" class=\"mw-redirect\" title=\"<PERSON> (bishop)\"><PERSON></a>, American archbishop (b. 1735)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>(bishop)"}]}, {"year": "1854", "text": "<PERSON>, German emigrant to Australia (b. 1827)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German emigrant to Australia (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German emigrant to Australia (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American general (b. 1798)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (b. 1798)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}]}, {"year": "1882", "text": "<PERSON>, Scottish-English archbishop (b. 1811)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish-English archbishop (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish-English archbishop (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, German physicist and lens maker, created the optical instrument (b. 1816)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and lens maker, created the <a href=\"https://wikipedia.org/wiki/Optical_instrument\" title=\"Optical instrument\">optical instrument</a> (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and lens maker, created the <a href=\"https://wikipedia.org/wiki/Optical_instrument\" title=\"Optical instrument\">optical instrument</a> (b. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Optical instrument", "link": "https://wikipedia.org/wiki/Optical_instrument"}]}, {"year": "1890", "text": "<PERSON>, English-Australian cricketer (b. 1851)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>winter"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian author and poet (b. 1820)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Afanasy_Fet\" title=\"Afanas<PERSON> Fet\"><PERSON><PERSON><PERSON><PERSON></a>, Russian author and poet (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Afanasy_Fet\" title=\"Afanasy Fet\"><PERSON><PERSON><PERSON><PERSON></a>, Russian author and poet (b. 1820)", "links": [{"title": "A<PERSON><PERSON><PERSON> Fet", "link": "https://wikipedia.org/wiki/Afanasy_Fet"}]}, {"year": "1894", "text": "<PERSON>, Scottish novelist, poet, and essayist (b. 1850)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish novelist, poet, and essayist (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish novelist, poet, and essayist (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, New Zealand architect, designed the Otago Boys' High School and Knox Church (b. 1833)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, New Zealand architect, designed the <a href=\"https://wikipedia.org/wiki/Otago_Boys%27_High_School\" title=\"Otago Boys' High School\">Otago Boys' High School</a> and <a href=\"https://wikipedia.org/wiki/Knox_Church,_Dunedin\" title=\"Knox Church, Dunedin\">Knox Church</a> (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, New Zealand architect, designed the <a href=\"https://wikipedia.org/wiki/Otago_Boys%27_High_School\" title=\"Otago Boys' High School\">Otago Boys' High School</a> and <a href=\"https://wikipedia.org/wiki/Knox_Church,_Dunedin\" title=\"Knox Church, Dunedin\">Knox Church</a> (b. 1833)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_(architect)"}, {"title": "Otago Boys' High School", "link": "https://wikipedia.org/wiki/Otago_Boys%27_High_School"}, {"title": "Knox Church, Dunedin", "link": "https://wikipedia.org/wiki/Knox_Church,_Dunedin"}]}, {"year": "1904", "text": "<PERSON>, American water polo player (b. 1869)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American water polo player (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American water polo player (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American religious leader and author, founded Christian Science (b. 1821)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and author, founded <a href=\"https://wikipedia.org/wiki/Christian_Science\" title=\"Christian Science\">Christian Science</a> (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and author, founded <a href=\"https://wikipedia.org/wiki/Christian_Science\" title=\"Christian Science\">Christian Science</a> (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Christian Science", "link": "https://wikipedia.org/wiki/Christian_Science"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Brazilian lawyer and politician, 3rd President of Brazil (b. 1841)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Prud<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prud<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prud<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1917", "text": "<PERSON>, English-French cricketer (b. 1879)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French cricketer (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French cricketer (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, French painter and sculptor (b. 1841)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and sculptor (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and sculptor (b. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American farmer and politician (b. 1830)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Irish lawyer and politician (b. 1849)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Donnell"}]}, {"year": "1935", "text": "Princess <PERSON> of the United Kingdom (b. 1868)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (b. 1868)", "links": [{"title": "Princess <PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_the_United_Kingdom"}]}, {"year": "1937", "text": "<PERSON>, Australian politician, 20th Premier of Tasmania (b. 1861)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1941", "text": "<PERSON>, Russian painter and poet (b. 1883)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and poet (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and poet (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Russian-American actress and educator (b. 1876)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American actress and educator (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American actress and educator (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Czech lawyer and politician (b. 1913)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech lawyer and politician (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech lawyer and politician (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Indian author, poet, and playwright (b. 1908)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author, poet, and playwright (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author, poet, and playwright (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>adhyay"}]}, {"year": "1956", "text": "<PERSON>, Russian sculptor, photographer, and graphic designer (b. 1891)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian sculptor, photographer, and graphic designer (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian sculptor, photographer, and graphic designer (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American football player and sportscaster (b. 1913)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American bassist (b. 1872)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American trombonist, cornet player, and composer (b. 1895)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trombonist, cornet player, and composer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trombonist, cornet player, and composer (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, President of Mexico, 1952-1958 (b. 1889)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>, 1952-1958 (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>, 1952-1958 (b. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Indian field hockey player and coach (b. 1905)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player and coach (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player and coach (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d"}]}, {"year": "1980", "text": "<PERSON>, English lieutenant, fascist, and politician, Chancellor of the Duchy of Lancaster (b. 1896)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant, fascist, and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant, fascist, and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1981", "text": "<PERSON>, American farmer, founded <PERSON><PERSON><PERSON>'s Berry Farm (b. 1889)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer, founded <a href=\"https://wikipedia.org/wiki/Knott%27s_Berry_Farm\" title=\"Knott's Berry Farm\">Knott's Berry Farm</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer, founded <a href=\"https://wikipedia.org/wiki/Knott%27s_Berry_Farm\" title=\"Knott's Berry Farm\">Knott's Berry Farm</a> (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Knott's Berry Farm", "link": "https://wikipedia.org/wiki/Knott%27s_Berry_Farm"}]}, {"year": "1981", "text": "<PERSON>, Finnish actor (b. 1897)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish actor (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish actor (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Azerbaijani-Russian mathematician and academic (b. 1919)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Azerbaijani-Russian mathematician and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Azerbaijani-Russian mathematician and academic (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Spanish basketball player (b. 1962)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Fernando_<PERSON>%C3%ADn_Espina\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fernando_Mart%C3%ADn_Espina\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_Mart%C3%ADn_Espina"}]}, {"year": "1989", "text": "<PERSON>, American businessman, founded the Country Music Association (b. 1914)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Country_Music_Association\" title=\"Country Music Association\">Country Music Association</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Country_Music_Association\" title=\"Country Music Association\">Country Music Association</a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Country Music Association", "link": "https://wikipedia.org/wiki/Country_Music_Association"}]}, {"year": "1993", "text": "<PERSON>, American physician, etymologist, and academic (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, etymologist, and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, etymologist, and academic (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, French historian and author (b. 1919)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Canadian pianist and conductor (b. 1936)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tu\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and conductor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tu\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and conductor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_H%C3%A9tu"}]}, {"year": "1999", "text": "<PERSON>, American actor (b. 1915)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1915)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and pianist (b. 1942)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_John\" title=\"Scatman John\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_John\" title=\"Scatman John\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actress, comedian, and singer (b. 1942)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and singer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and singer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Polish footballer (b. 1934)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Finnish 12th General of The Salvation Army (b. 1918)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Jar<PERSON>_Wahlstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish 12th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jar<PERSON>_Wahlstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish 12th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jarl_Wahlstr%C3%B6m"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American poet and educator (b. 1917)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet and educator (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet and educator (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American composer and producer (b. 1922)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Curtin\"><PERSON><PERSON></a>, American composer and producer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Curtin\"><PERSON><PERSON></a>, American composer and producer (b. 1922)", "links": [{"title": "<PERSON><PERSON> Curtin", "link": "https://wikipedia.org/wiki/Ho<PERSON>_C<PERSON>in"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, American illustrator (b. 1906)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(illustrator)\" title=\"<PERSON><PERSON><PERSON> (illustrator)\"><PERSON><PERSON><PERSON></a>, American illustrator (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(illustrator)\" title=\"<PERSON><PERSON><PERSON> (illustrator)\"><PERSON><PERSON><PERSON></a>, American illustrator (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON> (illustrator)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(illustrator)"}]}, {"year": "2002", "text": "<PERSON>, Irish-American actor (b. 1970)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English actor (b. 1941)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Indian historian, publisher and writer (b. 1921)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Sit<PERSON>_<PERSON>_Goel\" title=\"Sita Ram Goel\"><PERSON><PERSON></a>, Indian historian, publisher and writer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sit<PERSON>_<PERSON>_Goel\" title=\"Sita Ram Goel\"><PERSON><PERSON></a>, Indian historian, publisher and writer (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, Chinese-American mathematician and academic (b. 1911)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-Shen Chern\"><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American mathematician and academic (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON> Chern\"><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American mathematician and academic (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American admiral (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frederick_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American baseball player (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Finnish pop/schlager singer (b. 1964)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9n\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish pop/schlager singer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9n\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish pop/schlager singer (b. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Sir%C3%A9n"}]}, {"year": "2007", "text": "<PERSON>, Australian cartoonist and actor (b. 1948)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cartoonist and actor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cartoonist and actor (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Polish-American psychologist and author (b. 1923)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American psychologist and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American psychologist and author (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Brazilian actress and journalist (b. 1959)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Brazilian_actress)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Brazilian actress)\"><PERSON><PERSON></a>, Brazilian actress and journalist (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Brazilian_actress)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Brazilian actress)\"><PERSON><PERSON></a>, Brazilian actress and journalist (b. 1959)", "links": [{"title": "<PERSON><PERSON> (Brazilian actress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Brazilian_actress)"}]}, {"year": "2009", "text": "<PERSON>, Irish-born British soldier and actor (b. 1919)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born British soldier and actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born British soldier and actor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Azerbaijani poet and author (b. 1927)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>hor<PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani poet and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>hor<PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani poet and author (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hori"}]}, {"year": "2011", "text": "<PERSON>, Indian actor, director, and producer (b. 1923)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Anand\"><PERSON></a>, Indian actor, director, and producer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Anand\"><PERSON></a>, Indian actor, director, and producer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Anand"}]}, {"year": "2012", "text": "<PERSON>, Iraqi-Lebanese archbishop (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Lebanese archbishop (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Lebanese archbishop (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Bangladeshi cricketer (b. 1984)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi cricketer (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi cricketer (b. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Russian animator, director, and screenwriter (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian animator, director, and screenwriter (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian animator, director, and screenwriter (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Paraguayan footballer (b. 1980)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego Mendieta\"><PERSON></a>, Paraguayan footballer (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego Mendieta\"><PERSON></a>, Paraguayan footballer (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_Mendieta"}]}, {"year": "2012", "text": "<PERSON>, Australian cyclist and author (b. 1966)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Australian cyclist and author (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Australian cyclist and author (b. 1966)", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)"}]}, {"year": "2013", "text": "<PERSON>, French general (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Egyptian air marshal (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Egyptian air marshal (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian air marshal (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Egyptian poet and educator (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian poet and educator (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian poet and educator (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Puerto Rican-American lawyer and politician (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American lawyer and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American lawyer and politician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, French politician, French European Commissioner (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/List_of_European_Commissioners_by_nationality#France\" class=\"mw-redirect\" title=\"List of European Commissioners by nationality\">French European Commissioner</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/List_of_European_Commissioners_by_nationality#France\" class=\"mw-redirect\" title=\"List of European Commissioners by nationality\">French European Commissioner</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of European Commissioners by nationality", "link": "https://wikipedia.org/wiki/List_of_European_Commissioners_by_nationality#France"}]}, {"year": "2014", "text": "<PERSON>, Canadian-American psychotherapist and author (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American psychotherapist and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American psychotherapist and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English-American singer-songwriter and keyboard player (b. 1945)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer-songwriter and keyboard player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer-songwriter and keyboard player (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian mathematician and academic (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Canadian mathematician and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Canadian mathematician and academic (b. 1941)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>(mathematician)"}]}, {"year": "2015", "text": "<PERSON>, Jamaican singer and pianist (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer and pianist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer and pianist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Finnish speed skater (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish speed skater (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish speed skater (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eev<PERSON>_<PERSON><PERSON>n"}]}, {"year": "2015", "text": "<PERSON>, American singer-songwriter (b. 1967)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, Norwegian journalist and war historian (b. 1920)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist and war historian (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist and war historian (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Somali politician (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Somali politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Somali politician (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}]}}