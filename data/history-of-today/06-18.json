{"date": "June 18", "url": "https://wikipedia.org/wiki/June_18", "data": {"Events": [{"year": "618", "text": "<PERSON> becomes Emperor <PERSON><PERSON> of Tang, initiating three centuries of Tang dynasty rule over China.", "html": "618 - <PERSON> becomes <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON> of Tang</a>, initiating three centuries of <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> rule over China.", "no_year_html": "<PERSON> becomes <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON> of Tang</a>, initiating three centuries of <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> rule over China.", "links": [{"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_<PERSON>_Tang"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "656", "text": "<PERSON> becomes <PERSON><PERSON><PERSON> of the Rashidun Caliphate.", "html": "656 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <PERSON><PERSON><PERSON> of the Rashidun Caliphate.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <PERSON><PERSON><PERSON> of the Rashidun Caliphate.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ali"}]}, {"year": "860", "text": "Byzantine-Rus' War: A fleet of about 200 Rus' vessels sails into the Bosphorus and starts pillaging the suburbs of the Byzantine capital Constantinople.", "html": "860 - <a href=\"https://wikipedia.org/wiki/Siege_of_Constantinople_(860)\" title=\"Siege of Constantinople (860)\">Byzantine-Rus' War</a>: A fleet of about 200 <a href=\"https://wikipedia.org/wiki/Rus%27_Khaganate\" title=\"Rus' Khaganate\">Rus'</a> vessels sails into the <a href=\"https://wikipedia.org/wiki/Bosphorus\" class=\"mw-redirect\" title=\"Bosphorus\">Bosphorus</a> and starts pillaging the suburbs of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> capital <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Constantinople_(860)\" title=\"Siege of Constantinople (860)\">Byzantine-Rus' War</a>: A fleet of about 200 <a href=\"https://wikipedia.org/wiki/Rus%27_Khaganate\" title=\"Rus' Khaganate\">Rus'</a> vessels sails into the <a href=\"https://wikipedia.org/wiki/Bosphorus\" class=\"mw-redirect\" title=\"Bosphorus\">Bosphorus</a> and starts pillaging the suburbs of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> capital <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>.", "links": [{"title": "Siege of Constantinople (860)", "link": "https://wikipedia.org/wiki/Siege_of_Constantinople_(860)"}, {"title": "Rus' Khaganate", "link": "https://wikipedia.org/wiki/Rus%27_Khaganate"}, {"title": "Bosphor<PERSON>", "link": "https://wikipedia.org/wiki/Bosphorus"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}]}, {"year": "1053", "text": "Battle of Civitate: Three thousand Norman horsemen of Count <PERSON> rout the troops of <PERSON>.", "html": "1053 - <a href=\"https://wikipedia.org/wiki/Battle_of_Civitate\" title=\"Battle of Civitate\">Battle of Civitate</a>: Three thousand <a href=\"https://wikipedia.org/wiki/Normans\" title=\"Normans\">Norman</a> horsemen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hauteville\" title=\"<PERSON> of Hauteville\">Count <PERSON></a> rout the troops of <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Civitate\" title=\"Battle of Civitate\">Battle of Civitate</a>: Three thousand <a href=\"https://wikipedia.org/wiki/Normans\" title=\"Normans\">Norman</a> horsemen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hauteville\" title=\"<PERSON> of Hauteville\">Count <PERSON></a> rout the troops of <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Battle of Civitate", "link": "https://wikipedia.org/wiki/Battle_of_Civitate"}, {"title": "Normans", "link": "https://wikipedia.org/wiki/Normans"}, {"title": "<PERSON> of Hauteville", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hauteville"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1264", "text": "The Parliament of Ireland meets at Castledermot in County Kildare, the first definitively known meeting of this Irish legislature.", "html": "1264 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_Ireland\" title=\"Parliament of Ireland\">Parliament of Ireland</a> meets at <a href=\"https://wikipedia.org/wiki/Castledermot\" title=\"Castledermot\">Castledermot</a> in <a href=\"https://wikipedia.org/wiki/County_Kildare\" title=\"County Kildare\">County Kildare</a>, the first definitively known meeting of this Irish <a href=\"https://wikipedia.org/wiki/Legislature\" title=\"Legislature\">legislature</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_Ireland\" title=\"Parliament of Ireland\">Parliament of Ireland</a> meets at <a href=\"https://wikipedia.org/wiki/Castledermot\" title=\"Castledermot\">Castledermot</a> in <a href=\"https://wikipedia.org/wiki/County_Kildare\" title=\"County Kildare\">County Kildare</a>, the first definitively known meeting of this Irish <a href=\"https://wikipedia.org/wiki/Legislature\" title=\"Legislature\">legislature</a>.", "links": [{"title": "Parliament of Ireland", "link": "https://wikipedia.org/wiki/Parliament_of_Ireland"}, {"title": "Castledermot", "link": "https://wikipedia.org/wiki/Castledermot"}, {"title": "County Kildare", "link": "https://wikipedia.org/wiki/County_Kildare"}, {"title": "Legislature", "link": "https://wikipedia.org/wiki/Legislature"}]}, {"year": "1265", "text": "A draft Byzantine-Venetian treaty is concluded between Venetian envoys and Emperor <PERSON>, but is not ratified by <PERSON><PERSON> Ren<PERSON><PERSON>.", "html": "1265 - A draft <a href=\"https://wikipedia.org/wiki/Byzantine%E2%80%93Venetian_treaty_of_1265\" class=\"mw-redirect\" title=\"Byzantine-Venetian treaty of 1265\">Byzantine-Venetian treaty</a> is concluded between Venetian envoys and Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Michael VIII Palaiologos\"><PERSON></a>, but is not ratified by <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Reniero_Zeno\" title=\"<PERSON>ier<PERSON> Zeno\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "A draft <a href=\"https://wikipedia.org/wiki/Byzantine%E2%80%93Venetian_treaty_of_1265\" class=\"mw-redirect\" title=\"Byzantine-Venetian treaty of 1265\">Byzantine-Venetian treaty</a> is concluded between Venetian envoys and Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Michael VIII <PERSON>olo<PERSON>\"><PERSON></a>, but is not ratified by <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>ier<PERSON>_Zeno\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Byzantine-Venetian treaty of 1265", "link": "https://wikipedia.org/wiki/Byzantine%E2%80%93Venetian_treaty_of_1265"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Renier<PERSON>_Zeno"}]}, {"year": "1391", "text": "Tokhtamysh-Timur war: Battle of the Kondurcha River: <PERSON><PERSON> defeats Tokh<PERSON>ys<PERSON> of the Golden Horde in present-day southeast Russia.", "html": "1391 - <a href=\"https://wikipedia.org/wiki/Tokhtamysh%E2%80%93Timur_war\" title=\"Tokhtamysh-Timur war\">Tokhtamysh-Timur war</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Kondurcha_River\" title=\"Battle of the Kondurcha River\">Battle of the Kondurcha River</a>: <a href=\"https://wikipedia.org/wiki/Timur\" title=\"Timur\"><PERSON>ur</a> defeats <a href=\"https://wikipedia.org/wiki/Tokhtamysh\" title=\"Tokhtamysh\">Tokhtamysh</a> of the <a href=\"https://wikipedia.org/wiki/Golden_Horde\" title=\"Golden Horde\">Golden Horde</a> in present-day southeast Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokhtamysh%E2%80%93Timur_war\" title=\"Tokhtamysh-Timur war\">Tokhtamysh-Timur war</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Kondurcha_River\" title=\"Battle of the Kondurcha River\">Battle of the Kondurcha River</a>: <a href=\"https://wikipedia.org/wiki/Timur\" title=\"Timur\"><PERSON><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/Tokhtamysh\" title=\"Tokhtamysh\">Tokhtamysh</a> of the <a href=\"https://wikipedia.org/wiki/Golden_Horde\" title=\"Golden Horde\">Golden Horde</a> in present-day southeast Russia.", "links": [{"title": "Tokhtamysh-Timur war", "link": "https://wikipedia.org/wiki/Tokhtamysh%E2%80%93Timur_war"}, {"title": "Battle of the Kondurcha River", "link": "https://wikipedia.org/wiki/Battle_of_the_Kondurcha_River"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ur"}, {"title": "Tokh<PERSON>ysh", "link": "https://wikipedia.org/wiki/Tokhtamysh"}, {"title": "Golden Horde", "link": "https://wikipedia.org/wiki/Golden_Horde"}]}, {"year": "1429", "text": "<PERSON>'s army defeats an English army under <PERSON> at the Battle of Patay during the Hundred Years' War. The English lost 2,200 men, over half their army, crippling their efforts during this segment of the war.", "html": "1429 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France\" title=\"<PERSON> VII of France\"><PERSON>'s</a> army defeats an <a href=\"https://wikipedia.org/wiki/English_Army\" title=\"English Army\">English army</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl of <PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Patay\" title=\"Battle of Patay\">Battle of Patay</a> during the <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>. The English lost 2,200 men, over half their army, crippling their efforts during this segment of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France\" title=\"<PERSON> VII of France\"><PERSON>'s</a> army defeats an <a href=\"https://wikipedia.org/wiki/English_Army\" title=\"English Army\">English army</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl of <PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Patay\" title=\"Battle of Patay\">Battle of Patay</a> during the <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>. The English lost 2,200 men, over half their army, crippling their efforts during this segment of the war.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_France"}, {"title": "English Army", "link": "https://wikipedia.org/wiki/English_Army"}, {"title": "<PERSON>, 1st Earl of Shrewsbury", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Shrewsbury"}, {"title": "Battle of Patay", "link": "https://wikipedia.org/wiki/Battle_of_Patay"}, {"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}]}, {"year": "1633", "text": "<PERSON> is crowned King of Scots at St Giles' Cathedral, Edinburgh.", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> I</a> is crowned King of Scots at <a href=\"https://wikipedia.org/wiki/St_Giles%27_Cathedral\" title=\"St Giles' Cathedral\">St Giles' Cathedral</a>, <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> I</a> is crowned King of Scots at <a href=\"https://wikipedia.org/wiki/St_Giles%27_Cathedral\" title=\"St Giles' Cathedral\">St Giles' Cathedral</a>, <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "St Giles' Cathedral", "link": "https://wikipedia.org/wiki/St_Giles%27_Cathedral"}, {"title": "Edinburgh", "link": "https://wikipedia.org/wiki/Edinburgh"}]}, {"year": "1684", "text": "The charter of the Massachusetts Bay Colony is revoked via a scire facias writ issued by an English court.", "html": "1684 - The charter of the <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> is revoked via a <i><a href=\"https://wikipedia.org/wiki/Scire_facias\" title=\"Scire facias\">scire facias</a></i> writ issued by an English court.", "no_year_html": "The charter of the <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> is revoked via a <i><a href=\"https://wikipedia.org/wiki/Scire_facias\" title=\"Scire facias\">scire facias</a></i> writ issued by an English court.", "links": [{"title": "Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Massachusetts_Bay_Colony"}, {"title": "Scire facias", "link": "https://wikipedia.org/wiki/Scire_facias"}]}, {"year": "1757", "text": "Battle of Kolín between Prussian forces under <PERSON> and an Austrian army under the command of Field Marshal Count <PERSON> in the Seven Years' War.", "html": "1757 - <a href=\"https://wikipedia.org/wiki/Battle_of_Kol%C3%ADn\" title=\"Battle of Kolín\">Battle of Kolín</a> between <a href=\"https://wikipedia.org/wiki/Prussian_Army\" title=\"Prussian Army\">Prussian forces</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> and an <a href=\"https://wikipedia.org/wiki/Imperial_Army_of_the_Holy_Roman_Emperor\" title=\"Imperial Army of the Holy Roman Emperor\">Austrian army</a> under the command of Field Marshal <a href=\"https://wikipedia.org/wiki/Count_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON>\">Count <PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Kol%C3%ADn\" title=\"Battle of Kolín\">Battle of Kolín</a> between <a href=\"https://wikipedia.org/wiki/Prussian_Army\" title=\"Prussian Army\">Prussian forces</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> and an <a href=\"https://wikipedia.org/wiki/Imperial_Army_of_the_Holy_Roman_Emperor\" title=\"Imperial Army of the Holy Roman Emperor\">Austrian army</a> under the command of Field Marshal <a href=\"https://wikipedia.org/wiki/Count_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON>\">Count <PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>.", "links": [{"title": "Battle of Kolín", "link": "https://wikipedia.org/wiki/Battle_of_Kol%C3%ADn"}, {"title": "Prussian Army", "link": "https://wikipedia.org/wiki/Prussian_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Imperial Army of the Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Imperial_Army_of_the_Holy_Roman_Emperor"}, {"title": "Count <PERSON>", "link": "https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}]}, {"year": "1778", "text": "American Revolutionary War: The British Army abandons Philadelphia.", "html": "1778 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> abandons <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> abandons <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "1799", "text": "Action of 18 June 1799: A frigate squadron under Rear-admiral <PERSON><PERSON><PERSON> is captured by the British fleet under Lord <PERSON>.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/Action_of_18_June_1799\" title=\"Action of 18 June 1799\">Action of 18 June 1799</a>: A frigate squadron under Rear-admiral <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is captured by the British fleet under <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\">Lord <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Action_of_18_June_1799\" title=\"Action of 18 June 1799\">Action of 18 June 1799</a>: A frigate squadron under Rear-admiral <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is captured by the British fleet under <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\">Lord <PERSON></a>.", "links": [{"title": "Action of 18 June 1799", "link": "https://wikipedia.org/wiki/Action_of_18_June_1799"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9e"}, {"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1803", "text": "Haitian Revolution: The Royal Navy led by Rear-Admiral <PERSON> commence the blockade of Saint-Domingue against French forces.", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> led by Rear-Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> commence the <a href=\"https://wikipedia.org/wiki/Blockade_of_Saint-Domingue\" title=\"Blockade of Saint-Domingue\">blockade of Saint-Domingue</a> against French forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> led by Rear-Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> commence the <a href=\"https://wikipedia.org/wiki/Blockade_of_Saint-Domingue\" title=\"Blockade of Saint-Domingue\">blockade of Saint-Domingue</a> against French forces.", "links": [{"title": "Haitian Revolution", "link": "https://wikipedia.org/wiki/Haitian_Revolution"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Blockade of Saint-Domingue", "link": "https://wikipedia.org/wiki/Blockade_of_Saint-Domingue"}]}, {"year": "1812", "text": "The United States declaration of war upon the United Kingdom is signed by President <PERSON>, beginning the War of 1812.", "html": "1812 - The United States <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_on_the_United_Kingdom\" title=\"United States declaration of war on the United Kingdom\">declaration of war</a> upon the United Kingdom is signed by President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, beginning the <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_on_the_United_Kingdom\" title=\"United States declaration of war on the United Kingdom\">declaration of war</a> upon the United Kingdom is signed by President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, beginning the <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>.", "links": [{"title": "United States declaration of war on the United Kingdom", "link": "https://wikipedia.org/wiki/United_States_declaration_of_war_on_the_United_Kingdom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}]}, {"year": "1815", "text": "Napoleonic Wars: The Battle of Waterloo results in the defeat of <PERSON> by the <PERSON> Wellington and <PERSON><PERSON><PERSON> forcing him to abdicate the throne of France for the second and last time.", "html": "1815 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Waterloo\" title=\"Battle of Waterloo\">Battle of Waterloo</a> results in the defeat of <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> by the <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Duke of Wellington\">Duke of Wellington</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_von_Bl%C3%BCcher\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> forcing him to abdicate the throne of <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">France</a> for the second and last time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Waterloo\" title=\"Battle of Waterloo\">Battle of Waterloo</a> results in the defeat of <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> by the <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Duke of Wellington\">Duke of Wellington</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_von_Bl%C3%BCcher\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> forcing him to abdicate the throne of <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">France</a> for the second and last time.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Battle of Waterloo", "link": "https://wikipedia.org/wiki/Battle_of_Waterloo"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "<PERSON>, 1st Duke of Wellington", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Wellington"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_von_Bl%C3%BCcher"}, {"title": "First French Empire", "link": "https://wikipedia.org/wiki/First_French_Empire"}]}, {"year": "1822", "text": "<PERSON><PERSON> blows up the Ottoman navy's flagship at Chios, killing the <PERSON>pudan <PERSON>.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> blows up the <a href=\"https://wikipedia.org/wiki/Ottoman_navy\" class=\"mw-redirect\" title=\"Ottoman navy\">Ottoman navy</a>'s flagship at <a href=\"https://wikipedia.org/wiki/Chios\" title=\"<PERSON>os\"><PERSON><PERSON></a>, killing the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> blows up the <a href=\"https://wikipedia.org/wiki/Ottoman_navy\" class=\"mw-redirect\" title=\"Ottoman navy\">Ottoman navy</a>'s flagship at <a href=\"https://wikipedia.org/wiki/Chios\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, killing the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ottoman navy", "link": "https://wikipedia.org/wiki/Ottoman_navy"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chios"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON> receives a paper from <PERSON> that includes nearly identical conclusions about evolution as <PERSON>'s own, prompting <PERSON> to publish his theory.", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a paper from <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> that includes nearly identical conclusions about <a href=\"https://wikipedia.org/wiki/Evolution\" title=\"Evolution\">evolution</a> as <PERSON>'s own, prompting <PERSON> to publish his theory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a paper from <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> that includes nearly identical conclusions about <a href=\"https://wikipedia.org/wiki/Evolution\" title=\"Evolution\">evolution</a> as <PERSON>'s own, prompting <PERSON> to publish his theory.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Evolution", "link": "https://wikipedia.org/wiki/Evolution"}]}, {"year": "1859", "text": "First ascent of Al<PERSON>chhorn, second summit of the Bernese Alps.", "html": "1859 - First ascent of <a href=\"https://wikipedia.org/wiki/Aletschhorn\" title=\"Aletschhorn\">Aletschhorn</a>, second summit of the <a href=\"https://wikipedia.org/wiki/Bernese_Alps\" title=\"Bernese Alps\">Bernese Alps</a>.", "no_year_html": "First ascent of <a href=\"https://wikipedia.org/wiki/Aletschhorn\" title=\"Aletschhorn\">Aletschhorn</a>, second summit of the <a href=\"https://wikipedia.org/wiki/Bernese_Alps\" title=\"Bernese Alps\">Bernese Alps</a>.", "links": [{"title": "Aletschhorn", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>horn"}, {"title": "Bernese Alps", "link": "https://wikipedia.org/wiki/Bernese_Alps"}]}, {"year": "1873", "text": "<PERSON> is fined $100 for attempting to vote in the 1872 presidential election.", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is fined $100 for attempting to vote in the <a href=\"https://wikipedia.org/wiki/1872_United_States_presidential_election\" title=\"1872 United States presidential election\">1872 presidential election</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is fined $100 for attempting to vote in the <a href=\"https://wikipedia.org/wiki/1872_United_States_presidential_election\" title=\"1872 United States presidential election\">1872 presidential election</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "1872 United States presidential election", "link": "https://wikipedia.org/wiki/1872_United_States_presidential_election"}]}, {"year": "1887", "text": "The Reinsurance Treaty between Germany and Russia is signed.", "html": "1887 - The <a href=\"https://wikipedia.org/wiki/Reinsurance_Treaty\" title=\"Reinsurance Treaty\">Reinsurance Treaty</a> between Germany and Russia is signed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Reinsurance_Treaty\" title=\"Reinsurance Treaty\">Reinsurance Treaty</a> between Germany and Russia is signed.", "links": [{"title": "Reinsurance Treaty", "link": "https://wikipedia.org/wiki/Reinsurance_Treaty"}]}, {"year": "1900", "text": "Empress <PERSON><PERSON> <PERSON><PERSON> of China orders all foreigners killed, including foreign diplomats and their families.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>ix<PERSON>\" title=\"Empress <PERSON><PERSON> Cixi\">Empress <PERSON><PERSON>i</a> of China orders all foreigners killed, including foreign <a href=\"https://wikipedia.org/wiki/Diplomat\" title=\"Diplomat\">diplomats</a> and their families.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>\" title=\"Empress <PERSON><PERSON>ixi\">Empress <PERSON><PERSON>i</a> of China orders all foreigners killed, including foreign <a href=\"https://wikipedia.org/wiki/Diplomat\" title=\"Diplomat\">diplomats</a> and their families.", "links": [{"title": "Empress <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>i"}, {"title": "Diplomat", "link": "https://wikipedia.org/wiki/Diplomat"}]}, {"year": "1908", "text": "Japanese immigration to Brazil begins when 781 people arrive in Santos aboard the ship Kasato-Maru.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Japanese_Brazilians\" title=\"Japanese Brazilians\">Japanese immigration to Brazil</a> begins when 781 people arrive in <a href=\"https://wikipedia.org/wiki/<PERSON>,_S%C3%A3o_<PERSON>\" title=\"Santos, São Paulo\"><PERSON></a> aboard the ship <i>Kasato-Maru</i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japanese_Brazilians\" title=\"Japanese Brazilians\">Japanese immigration to Brazil</a> begins when 781 people arrive in <a href=\"https://wikipedia.org/wiki/<PERSON>,_S%C3%A3o_Paulo\" title=\"Santos, São Paulo\"><PERSON></a> aboard the ship <i>Kasato-Maru</i>.", "links": [{"title": "Japanese Brazilians", "link": "https://wikipedia.org/wiki/Japanese_Brazilians"}, {"title": "Santos, São Paulo", "link": "https://wikipedia.org/wiki/Santos,_S%C3%A3o_Paulo"}]}, {"year": "1908", "text": "The University of the Philippines is established.", "html": "1908 - The <a href=\"https://wikipedia.org/wiki/University_of_the_Philippines\" title=\"University of the Philippines\">University of the Philippines</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/University_of_the_Philippines\" title=\"University of the Philippines\">University of the Philippines</a> is established.", "links": [{"title": "University of the Philippines", "link": "https://wikipedia.org/wiki/University_of_the_Philippines"}]}, {"year": "1920", "text": "The Troubles in Northern Ireland (1920-1922) begin with a week of sectarian violence in Derry.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/The_Troubles_in_Ulster_(1920%E2%80%931922)\" title=\"The Troubles in Ulster (1920-1922)\">The Troubles in Northern Ireland (1920-1922)</a> begin with a week of sectarian violence in <a href=\"https://wikipedia.org/wiki/Derry\" title=\"Derry\">Derry</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles_in_Ulster_(1920%E2%80%931922)\" title=\"The Troubles in Ulster (1920-1922)\">The Troubles in Northern Ireland (1920-1922)</a> begin with a week of sectarian violence in <a href=\"https://wikipedia.org/wiki/Derry\" title=\"Derry\">Derry</a>.", "links": [{"title": "The Troubles in Ulster (1920-1922)", "link": "https://wikipedia.org/wiki/The_Troubles_in_Ulster_(1920%E2%80%931922)"}, {"title": "Derry", "link": "https://wikipedia.org/wiki/Derry"}]}, {"year": "1928", "text": "Aviator <PERSON> becomes the first woman to fly in an aircraft across the Atlantic Ocean (she is a passenger; <PERSON><PERSON><PERSON> is the pilot and <PERSON> the mechanic).", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Aviator\" class=\"mw-redirect\" title=\"Aviator\">Aviator</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to <a href=\"https://wikipedia.org/wiki/Transatlantic_flight\" title=\"Transatlantic flight\">fly in an aircraft across the Atlantic Ocean</a> (she is a passenger; <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"Wil<PERSON>\">W<PERSON><PERSON></a> is the pilot and <PERSON> the mechanic).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aviator\" class=\"mw-redirect\" title=\"Aviator\">Aviator</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to <a href=\"https://wikipedia.org/wiki/Transatlantic_flight\" title=\"Transatlantic flight\">fly in an aircraft across the Atlantic Ocean</a> (she is a passenger; <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"Wil<PERSON>\">W<PERSON><PERSON></a> is the pilot and <PERSON> the mechanic).", "links": [{"title": "Aviator", "link": "https://wikipedia.org/wiki/Aviator"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Transatlantic flight", "link": "https://wikipedia.org/wiki/Transatlantic_flight"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>z"}]}, {"year": "1935", "text": "Police in Vancouver, British Columbia, Canada, clash with striking longshoremen, resulting in a total of 60 injuries and 24 arrests.", "html": "1935 - Police in Vancouver, British Columbia, Canada, <a href=\"https://wikipedia.org/wiki/Battle_of_Ballantyne_Pier\" title=\"Battle of Ballantyne Pier\">clash with striking longshoremen</a>, resulting in a total of 60 injuries and 24 arrests.", "no_year_html": "Police in Vancouver, British Columbia, Canada, <a href=\"https://wikipedia.org/wiki/Battle_of_Ballantyne_Pier\" title=\"Battle of Ballantyne Pier\">clash with striking longshoremen</a>, resulting in a total of 60 injuries and 24 arrests.", "links": [{"title": "Battle of Ballantyne Pier", "link": "https://wikipedia.org/wiki/Battle_of_Ballantyne_Pier"}]}, {"year": "1940", "text": "Appeal of 18 June by <PERSON>.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Appeal_of_18_June\" title=\"Appeal of 18 June\">Appeal of 18 June</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Appeal_of_18_June\" title=\"Appeal of 18 June\">Appeal of 18 June</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Appeal of 18 June", "link": "https://wikipedia.org/wiki/Appeal_of_18_June"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "The \"Finest Hour\" speech is delivered by <PERSON>.", "html": "1940 - The <a href=\"https://wikipedia.org/wiki/This_was_their_finest_hour\" title=\"This was their finest hour\">\"Finest Hour\" speech</a> is delivered by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/This_was_their_finest_hour\" title=\"This was their finest hour\">\"Finest Hour\" speech</a> is delivered by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "This was their finest hour", "link": "https://wikipedia.org/wiki/This_was_their_finest_hour"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON> (\"<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>\") is charged with treason for his pro-German propaganda broadcasting during World War II.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (\"<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON><PERSON>\" title=\"Lord <PERSON><PERSON>-<PERSON>w\">Lord <PERSON><PERSON><PERSON><PERSON><PERSON></a>\") is charged with <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a> for his pro-German propaganda broadcasting during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (\"<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-Haw\" title=\"<PERSON> <PERSON><PERSON>-<PERSON>w\">Lord <PERSON><PERSON><PERSON>w</a>\") is charged with <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a> for his pro-German propaganda broadcasting during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-Haw"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treason"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1946", "text": "Dr. <PERSON>, a Socialist, calls for a Direct Action Day against the Portuguese in Goa.", "html": "1946 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Socialist\" class=\"mw-redirect\" title=\"Socialist\">Socialist</a>, calls for a <a href=\"https://wikipedia.org/wiki/Direct_Action_Day\" title=\"Direct Action Day\">Direct Action Day</a> against the Portuguese in <a href=\"https://wikipedia.org/wiki/Goa\" title=\"Goa\">Goa</a>.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Socialist\" class=\"mw-redirect\" title=\"Socialist\">Socialist</a>, calls for a <a href=\"https://wikipedia.org/wiki/Direct_Action_Day\" title=\"Direct Action Day\">Direct Action Day</a> against the Portuguese in <a href=\"https://wikipedia.org/wiki/Goa\" title=\"Goa\">Goa</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Socialist", "link": "https://wikipedia.org/wiki/Socialist"}, {"title": "Direct Action Day", "link": "https://wikipedia.org/wiki/Direct_Action_Day"}, {"title": "Goa", "link": "https://wikipedia.org/wiki/Goa"}]}, {"year": "1948", "text": "Columbia Records introduces the long-playing record album in a public demonstration at the Waldorf-Astoria Hotel in New York City.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Columbia_Records\" title=\"Columbia Records\">Columbia Records</a> introduces the <a href=\"https://wikipedia.org/wiki/LP_record\" title=\"LP record\">long-playing record album</a> in a public demonstration at the <a href=\"https://wikipedia.org/wiki/Waldorf-Astoria_Hotel\" class=\"mw-redirect\" title=\"Waldorf-Astoria Hotel\">Waldorf-Astoria Hotel</a> in New York City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Columbia_Records\" title=\"Columbia Records\">Columbia Records</a> introduces the <a href=\"https://wikipedia.org/wiki/LP_record\" title=\"LP record\">long-playing record album</a> in a public demonstration at the <a href=\"https://wikipedia.org/wiki/Waldorf-Astoria_Hotel\" class=\"mw-redirect\" title=\"Waldorf-Astoria Hotel\">Waldorf-Astoria Hotel</a> in New York City.", "links": [{"title": "Columbia Records", "link": "https://wikipedia.org/wiki/Columbia_Records"}, {"title": "LP record", "link": "https://wikipedia.org/wiki/LP_record"}, {"title": "Waldorf-Astoria Hotel", "link": "https://wikipedia.org/wiki/Waldorf-Astoria_Hotel"}]}, {"year": "1948", "text": "Britain, France and the United States announce that on June 21, the Deutsche Mark will be introduced in western Germany and West Berlin. Over the next six days, Communists increasingly restrict access to Berlin.", "html": "1948 - Britain, France and the United States announce that on June 21, the <i><a href=\"https://wikipedia.org/wiki/Deutsche_Mark\" title=\"Deutsche Mark\">Deutsche Mark</a></i> will be introduced in western Germany and West Berlin. Over the next six days, Communists increasingly restrict access to Berlin.", "no_year_html": "Britain, France and the United States announce that on June 21, the <i><a href=\"https://wikipedia.org/wiki/Deutsche_Mark\" title=\"Deutsche Mark\">Deutsche Mark</a></i> will be introduced in western Germany and West Berlin. Over the next six days, Communists increasingly restrict access to Berlin.", "links": [{"title": "Deutsche Mark", "link": "https://wikipedia.org/wiki/Deutsche_Mark"}]}, {"year": "1953", "text": "The Egyptian revolution of 1952 ends with the overthrow of the <PERSON> dynasty and the declaration of the Republic of Egypt.", "html": "1953 - The <a href=\"https://wikipedia.org/wiki/Egyptian_revolution_of_1952\" class=\"mw-redirect\" title=\"Egyptian revolution of 1952\">Egyptian revolution of 1952</a> ends with the overthrow of the <a href=\"https://wikipedia.org/wiki/Muhammad_Ali_dynasty\" title=\"Muhammad Ali dynasty\">Muhammad Ali dynasty</a> and the declaration of the <a href=\"https://wikipedia.org/wiki/Egypt#Modern_history\" title=\"Egypt\">Republic of Egypt</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Egyptian_revolution_of_1952\" class=\"mw-redirect\" title=\"Egyptian revolution of 1952\">Egyptian revolution of 1952</a> ends with the overthrow of the <a href=\"https://wikipedia.org/wiki/Muhammad_Ali_dynasty\" title=\"Muhammad Ali dynasty\">Muhammad Ali dynasty</a> and the declaration of the <a href=\"https://wikipedia.org/wiki/Egypt#Modern_history\" title=\"Egypt\">Republic of Egypt</a>.", "links": [{"title": "Egyptian revolution of 1952", "link": "https://wikipedia.org/wiki/Egyptian_revolution_of_1952"}, {"title": "Muhammad Ali dynasty", "link": "https://wikipedia.org/wiki/Muhammad_Ali_dynasty"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt#Modern_history"}]}, {"year": "1953", "text": "A United States Air Force C-124 crashes and burns near Tachikawa, Japan, killing 129.", "html": "1953 - A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Douglas_C-124_Globemaster_II\" title=\"Douglas C-124 Globemaster II\">C-124</a> <a href=\"https://wikipedia.org/wiki/Tachikawa_air_disaster\" title=\"Tachikawa air disaster\">crashes and burns</a> near <a href=\"https://wikipedia.org/wiki/Tachikawa\" class=\"mw-redirect\" title=\"Tachikawa\">Tachikawa</a>, Japan, killing 129.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Douglas_C-124_Globemaster_II\" title=\"Douglas C-124 Globemaster II\">C-124</a> <a href=\"https://wikipedia.org/wiki/Tachikawa_air_disaster\" title=\"Tachikawa air disaster\">crashes and burns</a> near <a href=\"https://wikipedia.org/wiki/Tachikawa\" class=\"mw-redirect\" title=\"Tachikawa\">Tachikawa</a>, Japan, killing 129.", "links": [{"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Douglas C-124 Globemaster II", "link": "https://wikipedia.org/wiki/Douglas_C-124_Globemaster_II"}, {"title": "Tachikawa air disaster", "link": "https://wikipedia.org/wiki/Tachikawa_air_disaster"}, {"title": "Tachikawa", "link": "https://wikipedia.org/wiki/Tachikawa"}]}, {"year": "1954", "text": "<PERSON> leads an invasion force across the Guatemalan border, setting in motion the 1954 Guatemalan coup d'état.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads an invasion force across the Guatemalan border, setting in motion the <a href=\"https://wikipedia.org/wiki/1954_Guatemalan_coup_d%27%C3%A9tat\" title=\"1954 Guatemalan coup d'état\">1954 Guatemalan coup d'état</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads an invasion force across the Guatemalan border, setting in motion the <a href=\"https://wikipedia.org/wiki/1954_Guatemalan_coup_d%27%C3%A9tat\" title=\"1954 Guatemalan coup d'état\">1954 Guatemalan coup d'état</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1954 Guatemalan coup d'état", "link": "https://wikipedia.org/wiki/1954_Guatemalan_coup_d%27%C3%A9tat"}]}, {"year": "1958", "text": "<PERSON>'s one-act opera <PERSON><PERSON>'s <PERSON><PERSON><PERSON><PERSON> premiered at the Aldeburgh Festival.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s one-act opera <i><a href=\"https://wikipedia.org/wiki/<PERSON>ye%27s_Fludde\" title=\"<PERSON><PERSON>'s <PERSON>ludde\"><PERSON><PERSON>'s <PERSON><PERSON><PERSON><PERSON></a></i> premiered at the <a href=\"https://wikipedia.org/wiki/Aldeburgh_Festival\" title=\"Aldeburgh Festival\">Aldeburgh Festival</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s one-act opera <i><a href=\"https://wikipedia.org/wiki/<PERSON>ye%27s_Fludde\" title=\"<PERSON><PERSON>'s <PERSON>ludde\"><PERSON><PERSON>'s <PERSON><PERSON><PERSON><PERSON></a></i> premiered at the <a href=\"https://wikipedia.org/wiki/Aldeburgh_Festival\" title=\"Aldeburgh Festival\">Aldeburgh Festival</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>'s <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Noye%27s_Fludde"}, {"title": "Aldeburgh Festival", "link": "https://wikipedia.org/wiki/Aldeburgh_Festival"}]}, {"year": "1965", "text": "Vietnam War: The United States Air Force uses B-52 bombers to attack guerrilla fighters in South Vietnam.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> uses <a href=\"https://wikipedia.org/wiki/Boeing_B-52_Stratofortress\" title=\"Boeing B-52 Stratofortress\">B-52 bombers</a> to <a href=\"https://wikipedia.org/wiki/Operation_Arc_Light\" title=\"Operation Arc Light\">attack guerrilla fighters</a> in <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> uses <a href=\"https://wikipedia.org/wiki/Boeing_B-52_Stratofortress\" title=\"Boeing B-52 Stratofortress\">B-52 bombers</a> to <a href=\"https://wikipedia.org/wiki/Operation_Arc_Light\" title=\"Operation Arc Light\">attack guerrilla fighters</a> in <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Boeing B-52 Stratofortress", "link": "https://wikipedia.org/wiki/Boeing_B-52_Stratofortress"}, {"title": "Operation Arc Light", "link": "https://wikipedia.org/wiki/Operation_Arc_Light"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1972", "text": "Staines air disaster: One hundred eighteen people are killed when a BEA H.S. Trident crashes minutes after takeoff from London's Heathrow Airport.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/British_European_Airways_Flight_548\" title=\"British European Airways Flight 548\">Staines air disaster</a>: One hundred eighteen people are killed when a <a href=\"https://wikipedia.org/wiki/British_European_Airways\" title=\"British European Airways\">BEA</a> <a href=\"https://wikipedia.org/wiki/Hawker_Siddeley_Trident\" title=\"Hawker Siddeley Trident\">H.S. Trident</a> crashes minutes after takeoff from London's <a href=\"https://wikipedia.org/wiki/Heathrow_Airport\" title=\"Heathrow Airport\">Heathrow Airport</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_European_Airways_Flight_548\" title=\"British European Airways Flight 548\">Staines air disaster</a>: One hundred eighteen people are killed when a <a href=\"https://wikipedia.org/wiki/British_European_Airways\" title=\"British European Airways\">BEA</a> <a href=\"https://wikipedia.org/wiki/Hawker_Siddeley_Trident\" title=\"Hawker Siddeley Trident\">H.S. Trident</a> crashes minutes after takeoff from London's <a href=\"https://wikipedia.org/wiki/Heathrow_Airport\" title=\"Heathrow Airport\">Heathrow Airport</a>.", "links": [{"title": "British European Airways Flight 548", "link": "https://wikipedia.org/wiki/British_European_Airways_Flight_548"}, {"title": "British European Airways", "link": "https://wikipedia.org/wiki/British_European_Airways"}, {"title": "Hawker <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>r_Siddeley_Trident"}, {"title": "Heathrow Airport", "link": "https://wikipedia.org/wiki/Heathrow_Airport"}]}, {"year": "1979", "text": "SALT II is signed by the United States and the Soviet Union.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Strategic_Arms_Limitation_Talks\" title=\"Strategic Arms Limitation Talks\">SALT II</a> is signed by the United States and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Strategic_Arms_Limitation_Talks\" title=\"Strategic Arms Limitation Talks\">SALT II</a> is signed by the United States and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Strategic Arms Limitation Talks", "link": "https://wikipedia.org/wiki/Strategic_Arms_Limitation_Talks"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1981", "text": "The Lockheed F-117 Nighthawk, the first operational aircraft initially designed around stealth technology, makes its first flight.", "html": "1981 - The <a href=\"https://wikipedia.org/wiki/Lockheed_F-117_Nighthawk\" title=\"Lockheed F-117 Nighthawk\">Lockheed F-117 Nighthawk</a>, the first operational aircraft initially designed around <a href=\"https://wikipedia.org/wiki/Stealth_technology\" title=\"Stealth technology\">stealth technology</a>, makes its first flight.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lockheed_F-117_Nighthawk\" title=\"Lockheed F-117 Nighthawk\">Lockheed F-117 Nighthawk</a>, the first operational aircraft initially designed around <a href=\"https://wikipedia.org/wiki/Stealth_technology\" title=\"Stealth technology\">stealth technology</a>, makes its first flight.", "links": [{"title": "Lockheed F-117 Nighthawk", "link": "https://wikipedia.org/wiki/Lockheed_F-117_Nighthawk"}, {"title": "Stealth technology", "link": "https://wikipedia.org/wiki/Stealth_technology"}]}, {"year": "1982", "text": "Italian banker <PERSON>'s body is discovered hanging beneath Blackfriars Bridge in London, England.", "html": "1982 - Italian banker <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s body is discovered hanging beneath <a href=\"https://wikipedia.org/wiki/Blackfriars_Bridge\" title=\"Blackfriars Bridge\">Blackfriars Bridge</a> in London, England.", "no_year_html": "Italian banker <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s body is discovered hanging beneath <a href=\"https://wikipedia.org/wiki/Blackfriars_Bridge\" title=\"Blackfriars Bridge\">Blackfriars Bridge</a> in London, England.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Blackfriars Bridge", "link": "https://wikipedia.org/wiki/Blackfriars_Bridge"}]}, {"year": "1983", "text": "Space Shuttle program: STS-7, Astronaut <PERSON> becomes the first American woman in space.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-7\" title=\"STS-7\">STS-7</a>, <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">Astronaut</a> <a href=\"https://wikipedia.org/wiki/Sally_Ride\" title=\"Sally Ride\"><PERSON></a> becomes the first American woman in space.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-7\" title=\"STS-7\">STS-7</a>, <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">Astronaut</a> <a href=\"https://wikipedia.org/wiki/Sally_Ride\" title=\"Sally Ride\"><PERSON> Ride</a> becomes the first American woman in space.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-7", "link": "https://wikipedia.org/wiki/STS-7"}, {"title": "Astronaut", "link": "https://wikipedia.org/wiki/Astronaut"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, together with nine other women of the Baháʼí Faith, is sentenced to death and hanged in Shiraz, Iran over her religious beliefs.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, together with nine other women of the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a>, is sentenced to death and hanged in <a href=\"https://wikipedia.org/wiki/Shiraz\" title=\"Shiraz\">Shiraz</a>, <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> over her religious beliefs.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, together with nine other women of the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a>, is sentenced to death and hanged in <a href=\"https://wikipedia.org/wiki/Shiraz\" title=\"Shiraz\">Shiraz</a>, <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> over her religious beliefs.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mona_Ma<PERSON>"}, {"title": "Baháʼí Faith", "link": "https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith"}, {"title": "Shiraz", "link": "https://wikipedia.org/wiki/Shiraz"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}]}, {"year": "1984", "text": "A major clash between about 5,000 police and a similar number of striking miners takes place at Orgreave, South Yorkshire, during the 1984-85 UK miners' strike.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Battle_of_Orgreave\" title=\"Battle of Orgreave\">A major clash</a> between about 5,000 police and a similar number of striking miners takes place at <a href=\"https://wikipedia.org/wiki/Orgreave,_South_Yorkshire\" title=\"Orgreave, South Yorkshire\">Orgreave</a>, <a href=\"https://wikipedia.org/wiki/South_Yorkshire\" title=\"South Yorkshire\">South Yorkshire</a>, during the <a href=\"https://wikipedia.org/wiki/UK_miners%27_strike_(1984%E2%80%9385)\" class=\"mw-redirect\" title=\"UK miners' strike (1984-85)\">1984-85 UK miners' strike</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Orgreave\" title=\"Battle of Orgreave\">A major clash</a> between about 5,000 police and a similar number of striking miners takes place at <a href=\"https://wikipedia.org/wiki/Orgreave,_South_Yorkshire\" title=\"Orgreave, South Yorkshire\">Orgreave</a>, <a href=\"https://wikipedia.org/wiki/South_Yorkshire\" title=\"South Yorkshire\">South Yorkshire</a>, during the <a href=\"https://wikipedia.org/wiki/UK_miners%27_strike_(1984%E2%80%9385)\" class=\"mw-redirect\" title=\"UK miners' strike (1984-85)\">1984-85 UK miners' strike</a>.", "links": [{"title": "Battle of Orgreave", "link": "https://wikipedia.org/wiki/Battle_of_Orgreave"}, {"title": "Orgreave, South Yorkshire", "link": "https://wikipedia.org/wiki/Orgreave,_South_Yorkshire"}, {"title": "South Yorkshire", "link": "https://wikipedia.org/wiki/South_Yorkshire"}, {"title": "UK miners' strike (1984-85)", "link": "https://wikipedia.org/wiki/UK_miners%27_strike_(1984%E2%80%9385)"}]}, {"year": "1994", "text": "The Troubles: Members of the Ulster Volunteer Force (UVF) attack a crowded pub with assault rifles in Loughinisland, Northern Ireland. Six Catholic civilians are killed and five wounded. It was crowded with people watching the 1994 FIFA World Cup.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: Members of the <a href=\"https://wikipedia.org/wiki/Ulster_Volunteer_Force\" title=\"Ulster Volunteer Force\">Ulster Volunteer Force</a> (UVF) <a href=\"https://wikipedia.org/wiki/Loughinisland_massacre\" title=\"Loughinisland massacre\">attack a crowded pub</a> with assault rifles in <a href=\"https://wikipedia.org/wiki/Loughinisland\" title=\"Loughinisland\">Loughinisland</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>. Six Catholic civilians are killed and five wounded. It was crowded with people watching the <a href=\"https://wikipedia.org/wiki/1994_FIFA_World_Cup\" title=\"1994 FIFA World Cup\">1994 FIFA World Cup</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: Members of the <a href=\"https://wikipedia.org/wiki/Ulster_Volunteer_Force\" title=\"Ulster Volunteer Force\">Ulster Volunteer Force</a> (UVF) <a href=\"https://wikipedia.org/wiki/Loughinisland_massacre\" title=\"Loughinisland massacre\">attack a crowded pub</a> with assault rifles in <a href=\"https://wikipedia.org/wiki/Loughinisland\" title=\"Loughinisland\">Loughinisland</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>. Six Catholic civilians are killed and five wounded. It was crowded with people watching the <a href=\"https://wikipedia.org/wiki/1994_FIFA_World_Cup\" title=\"1994 FIFA World Cup\">1994 FIFA World Cup</a>.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Ulster Volunteer Force", "link": "https://wikipedia.org/wiki/Ulster_Volunteer_Force"}, {"title": "Loughinisland massacre", "link": "https://wikipedia.org/wiki/Loughinisland_massacre"}, {"title": "Loughinisland", "link": "https://wikipedia.org/wiki/Loughinisland"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "1994 FIFA World Cup", "link": "https://wikipedia.org/wiki/1994_FIFA_World_Cup"}]}, {"year": "1998", "text": "Propair Flight 420 crashes near Montréal-Mirabel International Airport in Quebec, Canada, killing 11.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Propair_Flight_420\" title=\"Propair Flight 420\">Propair Flight 420</a> crashes near <a href=\"https://wikipedia.org/wiki/Montr%C3%A9al%E2%80%93Mirabel_International_Airport\" title=\"Montréal-Mirabel International Airport\">Montréal-Mirabel International Airport</a> in <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a>, <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a>, killing 11.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Propair_Flight_420\" title=\"Propair Flight 420\">Propair Flight 420</a> crashes near <a href=\"https://wikipedia.org/wiki/Montr%C3%A9al%E2%80%93Mirabel_International_Airport\" title=\"Montréal-Mirabel International Airport\">Montréal-Mirabel International Airport</a> in <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a>, <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a>, killing 11.", "links": [{"title": "Propair Flight 420", "link": "https://wikipedia.org/wiki/Propair_Flight_420"}, {"title": "Montréal-Mirabel International Airport", "link": "https://wikipedia.org/wiki/Montr%C3%A9al%E2%80%93Mirabel_International_Airport"}, {"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}, {"title": "Canada", "link": "https://wikipedia.org/wiki/Canada"}]}, {"year": "2006", "text": "The first Kazakh space satellite, KazSat-1 is launched.", "html": "2006 - The first <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakh</a> space <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellite</a>, <a href=\"https://wikipedia.org/wiki/KazSat-1\" title=\"KazSat-1\">KazSat-1</a> is launched.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakh</a> space <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellite</a>, <a href=\"https://wikipedia.org/wiki/KazSat-1\" title=\"KazSat-1\">KazSat-1</a> is launched.", "links": [{"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}, {"title": "Satellite", "link": "https://wikipedia.org/wiki/Satellite"}, {"title": "KazSat-1", "link": "https://wikipedia.org/wiki/KazSat-1"}]}, {"year": "2007", "text": "The Charleston Sofa Super Store fire happened in Charleston, South Carolina, killing nine firefighters.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/Charleston_Sofa_Super_Store_fire\" title=\"Charleston Sofa Super Store fire\">Charleston Sofa Super Store fire</a> happened in <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a>, killing nine <a href=\"https://wikipedia.org/wiki/Firefighter\" title=\"Firefighter\">firefighters</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Charleston_Sofa_Super_Store_fire\" title=\"Charleston Sofa Super Store fire\">Charleston Sofa Super Store fire</a> happened in <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a>, killing nine <a href=\"https://wikipedia.org/wiki/Firefighter\" title=\"Firefighter\">firefighters</a>.", "links": [{"title": "Charleston Sofa Super Store fire", "link": "https://wikipedia.org/wiki/Charleston_Sofa_Super_Store_fire"}, {"title": "Charleston, South Carolina", "link": "https://wikipedia.org/wiki/Charleston,_South_Carolina"}, {"title": "Firefighter", "link": "https://wikipedia.org/wiki/Firefighter"}]}, {"year": "2009", "text": "The Lunar Reconnaissance Orbiter (LRO), a NASA robotic spacecraft is launched.", "html": "2009 - The <a href=\"https://wikipedia.org/wiki/Lunar_Reconnaissance_Orbiter\" title=\"Lunar Reconnaissance Orbiter\">Lunar Reconnaissance Orbiter</a> (LRO), a NASA robotic spacecraft is launched.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lunar_Reconnaissance_Orbiter\" title=\"Lunar Reconnaissance Orbiter\">Lunar Reconnaissance Orbiter</a> (LRO), a NASA robotic spacecraft is launched.", "links": [{"title": "Lunar Reconnaissance Orbiter", "link": "https://wikipedia.org/wiki/Lunar_Reconnaissance_Orbiter"}]}, {"year": "2018", "text": "An earthquake of magnitude 6.1 strikes northern Osaka.", "html": "2018 - An <a href=\"https://wikipedia.org/wiki/2018_Osaka_earthquake\" title=\"2018 Osaka earthquake\">earthquake of magnitude 6.1</a> strikes northern <a href=\"https://wikipedia.org/wiki/Osaka_prefecture\" class=\"mw-redirect\" title=\"Osaka prefecture\">Osaka</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2018_Osaka_earthquake\" title=\"2018 Osaka earthquake\">earthquake of magnitude 6.1</a> strikes northern <a href=\"https://wikipedia.org/wiki/Osaka_prefecture\" class=\"mw-redirect\" title=\"Osaka prefecture\">Osaka</a>.", "links": [{"title": "2018 Osaka earthquake", "link": "https://wikipedia.org/wiki/2018_Osaka_earthquake"}, {"title": "Osaka prefecture", "link": "https://wikipedia.org/wiki/Osaka_prefecture"}]}, {"year": "2023", "text": "Titan, a submersible operated by OceanGate Expeditions, imploded while attempting to view the wreck of the Titanic, killing all five people on board including the co-founder and CEO of the company, Stockton Rush in the North Atlantic Ocean.", "html": "2023 - <i>Titan</i>, a submersible operated by <a href=\"https://wikipedia.org/wiki/OceanGate\" title=\"OceanGate\">OceanGate Expeditions</a>, <a href=\"https://wikipedia.org/wiki/Titan_submersible_implosion\" title=\"Titan submersible implosion\">imploded</a> while attempting to view the <a href=\"https://wikipedia.org/wiki/Wreck_of_the_Titanic\" title=\"Wreck of the Titanic\">wreck of the Titanic</a>, killing all five people on board including the co-founder and CEO of the company, <a href=\"https://wikipedia.org/wiki/Stockton_Rush\" title=\"Stockton Rush\">Stockton Rush</a> in the <a href=\"https://wikipedia.org/wiki/North_Atlantic_Ocean\" class=\"mw-redirect\" title=\"North Atlantic Ocean\">North Atlantic Ocean</a>.", "no_year_html": "<i>Titan</i>, a submersible operated by <a href=\"https://wikipedia.org/wiki/OceanGate\" title=\"OceanGate\">OceanGate Expeditions</a>, <a href=\"https://wikipedia.org/wiki/Titan_submersible_implosion\" title=\"Titan submersible implosion\">imploded</a> while attempting to view the <a href=\"https://wikipedia.org/wiki/Wreck_of_the_Titanic\" title=\"Wreck of the Titanic\">wreck of the Titanic</a>, killing all five people on board including the co-founder and CEO of the company, <a href=\"https://wikipedia.org/wiki/Stockton_Rush\" title=\"Stockton Rush\">Stockton Rush</a> in the <a href=\"https://wikipedia.org/wiki/North_Atlantic_Ocean\" class=\"mw-redirect\" title=\"North Atlantic Ocean\">North Atlantic Ocean</a>.", "links": [{"title": "OceanGate", "link": "https://wikipedia.org/wiki/OceanGate"}, {"title": "Titan submersible implosion", "link": "https://wikipedia.org/wiki/Titan_submersible_implosion"}, {"title": "Wreck of the Titanic", "link": "https://wikipedia.org/wiki/Wreck_of_the_Titanic"}, {"title": "Stockton Rush", "link": "https://wikipedia.org/wiki/Stockton_Rush"}, {"title": "North Atlantic Ocean", "link": "https://wikipedia.org/wiki/North_Atlantic_Ocean"}]}], "Births": [{"year": "1269", "text": "<PERSON> of England, Countess of Bar (d. 1298)", "html": "1269 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_England,_Countess_of_Bar\" title=\"<PERSON> of England, Countess of Bar\"><PERSON> of England, Countess of Bar</a> (d. 1298)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_England,_Countess_of_Bar\" title=\"<PERSON> of England, Countess of Bar\"><PERSON> of England, Countess of Bar</a> (d. 1298)", "links": [{"title": "<PERSON> of England, Countess of Bar", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_England,_Countess_<PERSON>_Bar"}]}, {"year": "1318", "text": "<PERSON> Woodstock (d. 1355)", "html": "1318 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock\" title=\"<PERSON> of Woodstock\"><PERSON> Woodstock</a> (d. 1355)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock\" title=\"<PERSON> Woodstock\"><PERSON> Woodstock</a> (d. 1355)", "links": [{"title": "<PERSON> Woodstock", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woodstock"}]}, {"year": "1332", "text": "<PERSON>, Byzantine Emperor (d. 1391)", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a> (d. 1391)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>olo<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a> (d. 1391)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Byzantine Emperor", "link": "https://wikipedia.org/wiki/Byzantine_Emperor"}]}, {"year": "1466", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian printer (d. 1539)", "html": "1466 - <a href=\"https://wikipedia.org/wiki/<PERSON>tta<PERSON><PERSON>_Petrucci\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian printer (d. 1539)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>tta<PERSON><PERSON>_<PERSON>rucci\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian printer (d. 1539)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ottaviano_Petrucci"}]}, {"year": "1511", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian architect and sculptor, designed the Ponte Santa Trinita (d. 1592)", "html": "1511 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian architect and sculptor, designed the <a href=\"https://wikipedia.org/wiki/Ponte_Santa_Trinita\" title=\"Ponte Santa Trinita\">Ponte Santa Trinita</a> (d. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian architect and sculptor, designed the <a href=\"https://wikipedia.org/wiki/Ponte_Santa_Trinita\" title=\"Ponte Santa Trinita\">Ponte Santa Trinita</a> (d. 1592)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ponte Santa Trinita", "link": "https://wikipedia.org/wiki/Ponte_Santa_Trinita"}]}, {"year": "1517", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (d. 1593)", "html": "1517 - <a href=\"https://wikipedia.org/wiki/Emperor_%C5%8Cgi<PERSON><PERSON>\" title=\"Emperor <PERSON>gi<PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_%C5%8C<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON>gima<PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1593)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_%C5%8Cgimachi"}]}, {"year": "1521", "text": "<PERSON> of Portugal, Duchess of Viseu (d. 1577)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/Maria_of_Portugal,_Duchess_of_Viseu\" title=\"<PERSON> of Portugal, Duchess of Viseu\"><PERSON> of Portugal, Duchess of Viseu</a> (d. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_of_Portugal,_Duchess_of_Viseu\" title=\"<PERSON> of Portugal, Duchess of Viseu\"><PERSON> of Portugal, Duchess of Viseu</a> (d. 1577)", "links": [{"title": "<PERSON> of Portugal, Duchess of Viseu", "link": "https://wikipedia.org/wiki/Maria_of_Portugal,_Duchess_of_Viseu"}]}, {"year": "1667", "text": "<PERSON>, Russian field marshal (d. 1750)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian field marshal (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian field marshal (d. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1673", "text": "<PERSON>, Spanish composer (d. 1747)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish composer (d. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish composer (d. 1747)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1677", "text": "<PERSON>, Italian cellist and composer (d. 1726)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (d. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (d. 1726)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1716", "text": "<PERSON><PERSON><PERSON>, French painter and educator (d. 1809)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and educator (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and educator (d. 1809)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON>, Czech violinist and composer (d. 1757)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and composer (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and composer (d. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON><PERSON><PERSON>, Austrian-French pianist and composer (d. 1831)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-French pianist and composer (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-French pianist and composer (d. 1831)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentine lawyer and politician 1st Supreme Director of the United Provinces of the Río de la Plata (d. 1833)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine lawyer and politician 1st <a href=\"https://wikipedia.org/wiki/Supreme_Director_of_the_United_Provinces_of_the_R%C3%ADo_de_la_Plata\" title=\"Supreme Director of the United Provinces of the Río de la Plata\">Supreme Director of the United Provinces of the Río de la Plata</a> (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine lawyer and politician 1st <a href=\"https://wikipedia.org/wiki/Supreme_Director_of_the_United_Provinces_of_the_R%C3%ADo_de_la_Plata\" title=\"Supreme Director of the United Provinces of the Río de la Plata\">Supreme Director of the United Provinces of the Río de la Plata</a> (d. 1833)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ger<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Supreme Director of the United Provinces of the Río de la Plata", "link": "https://wikipedia.org/wiki/Supreme_Director_of_the_United_Provinces_of_the_R%C3%ADo_de_la_Plata"}]}, {"year": "1769", "text": "<PERSON>, Viscount <PERSON>, Irish-English politician, Secretary of State for Foreign and Commonwealth Affairs (d. 1822)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscount_Castlereagh\" title=\"<PERSON>, Viscount Castlereagh\"><PERSON>, Viscount Castlereagh</a>, Irish-English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a> (d. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Viscount_<PERSON>\" title=\"<PERSON>, Viscount Castlereagh\"><PERSON>, Viscount Castlereagh</a>, Irish-English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a> (d. 1822)", "links": [{"title": "<PERSON>, Viscount <PERSON>rea<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscount_<PERSON>"}, {"title": "Secretary of State for Foreign and Commonwealth Affairs", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs"}]}, {"year": "1799", "text": "<PERSON>, English astronomer and merchant (d. 1880)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and merchant (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and merchant (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, Russian journalist and author (d. 1891)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist and author (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist and author (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON> Tann-Rathsamhausen, German general (d. 1881)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_von_und_zu_der_Tann-Rathsamhausen\" title=\"<PERSON> von und zu der Tann-Rathsamhausen\"><PERSON> und zu der Tann-Rathsamhausen</a>, German general (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_von_und_zu_der_Tann-Rathsamhausen\" title=\"<PERSON> von und zu der Tann-Rathsamhausen\"><PERSON> von und zu der Tann-Rathsamhausen</a>, German general (d. 1881)", "links": [{"title": "<PERSON> zu der Tann-Rathsamhausen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_und_<PERSON>_<PERSON>_<PERSON>-<PERSON>hsam<PERSON>"}]}, {"year": "1816", "text": "<PERSON><PERSON><PERSON><PERSON>, French daughter of <PERSON> (d. 1907)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}]}, {"year": "1816", "text": "<PERSON>, Nepali ruler (d. 1877)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nepali ruler (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nepali ruler (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, Mexican general and President (1880-1884) (d. 1893)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Flores\" title=\"<PERSON>\"><PERSON></a>, Mexican general and President (1880-1884) (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Flores\" title=\"<PERSON>\"><PERSON></a>, Mexican general and President (1880-1884) (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Flores"}]}, {"year": "1834", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French philosopher and academic (d. 1895)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>h%C3%<PERSON><PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, French philosopher and academic (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>h%C3%A<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, French philosopher and academic (d. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Auguste-Th%C3%A<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, American general and banker (d. 1920)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American general and banker (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American general and banker (d. 1920)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>."}]}, {"year": "1845", "text": "<PERSON>, French physician and parasitologist, Nobel Prize laureate (d. 1922)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and parasitologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and parasitologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1850", "text": "<PERSON>, Austrian composer and critic (d. 1914)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and critic (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and critic (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON><PERSON> <PERSON><PERSON>, American publisher, founded the E. W. Scripps Company (d. 1926)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/E<PERSON>_<PERSON><PERSON>_<PERSON>rip<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American publisher, founded the <a href=\"https://wikipedia.org/wiki/E._W._Scripps_Company\" title=\"E. W. Scripps Company\">E. W. Scripps Company</a> (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American publisher, founded the <a href=\"https://wikipedia.org/wiki/E._W._Scripps_Company\" title=\"E. W. Scripps Company\">E. W. Scripps Company</a> (d. 1926)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "E. W. Scripps Company", "link": "https://wikipedia.org/wiki/E._<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Company"}]}, {"year": "1857", "text": "<PERSON>, American businessman and philanthropist, founded the Folger Shakespeare Library (d. 1930)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>ol<PERSON>_Shakespeare_Library\" title=\"Folger Shakespeare Library\">Folger Shakespeare Library</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Shakespeare_Library\" title=\"Folger Shakespeare Library\">Folger Shakespeare Library</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>ger Shakespeare Library", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Library"}]}, {"year": "1858", "text": "<PERSON>, Scottish-English mathematician and academic (d. 1942)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English mathematician and academic (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English mathematician and academic (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, English-Australian politician, 7th Premier of Western Australia (d. 1927)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1862", "text": "<PERSON>, American novelist and poet (d. 1942)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, English-Australian poet and author (d. 1909)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian poet and author (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian poet and author (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Evans"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian admiral and politician, Regent of Hungary (d. 1957)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Horthy\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian admiral and politician, <a href=\"https://wikipedia.org/wiki/Regent_of_Hungary\" title=\"Regent of Hungary\">Regent of Hungary</a> (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Horthy\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian admiral and politician, <a href=\"https://wikipedia.org/wiki/Regent_of_Hungary\" title=\"Regent of Hungary\">Regent of Hungary</a> (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikl%C3%B3s_Horthy"}, {"title": "Regent of Hungary", "link": "https://wikipedia.org/wiki/Regent_of_Hungary"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON>, French mathematician and philosopher (d. 1954)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and philosopher (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and philosopher (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American painter and illustrator (d. 1960)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Hungarian swimmer (d. 1956)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Halmay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian swimmer (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Halmay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian swimmer (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zolt%C3%A1n_Halmay"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Bulgarian compositor and politician, 32nd Prime Minister of Bulgaria (d. 1949)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian compositor and politician, 32nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bulgaria\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bulgaria\">Prime Minister of Bulgaria</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian compositor and politician, 32nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bulgaria\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bulgaria\">Prime Minister of Bulgaria</a> (d. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Bulgaria", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bulgaria"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, French captain and politician, Prime Minister of France (d. 1970)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French captain and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French captain and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1886", "text": "<PERSON>, English lieutenant and mountaineer (d. 1924)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and mountaineer (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and mountaineer (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American ornithologist and paleontologist (d. 1978)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and paleontologist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and paleontologist (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian businessman and politician (d. 1956)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Tancr%C3%A8de_Labb%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian businessman and politician (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tancr%C3%A8de_Labb%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian businessman and politician (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tancr%C3%A8de_Labb%C3%A9"}]}, {"year": "1896", "text": "<PERSON>, American actress (d. 1986)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sweet\"><PERSON></a>, American actress (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Finnish runner (d. 1940)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish runner (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish runner (d. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Czech-American relief worker, editor, and fundraiser (d. 1989)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Vlasta_Vraz\" title=\"Vlasta Vraz\"><PERSON><PERSON><PERSON> Vraz</a>, Czech-American relief worker, editor, and fundraiser (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vlasta_Vraz\" title=\"Vlasta Vraz\"><PERSON><PERSON><PERSON> Vraz</a>, Czech-American relief worker, editor, and fundraiser (d. 1989)", "links": [{"title": "Vlasta V<PERSON>", "link": "https://wikipedia.org/wiki/Vlasta_Vraz"}]}, {"year": "1901", "text": "Grand Duchess <PERSON> of Russia (d. 1918)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (d. 1918)", "links": [{"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON>, English actor (d. 1994)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English actor (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American musician (d. 1980)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Finnish decathlete (d. 1980)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Paavo_Yrj%C3%B6l%C3%A4\" title=\"Paavo Yrjölä\"><PERSON><PERSON><PERSON></a>, Finnish decathlete (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paavo_Yrj%C3%B6l%C3%A4\" title=\"Paavo Yrjölä\"><PERSON><PERSON><PERSON></a>, Finnish decathlete (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Paavo_Yrj%C3%B6l%C3%A4"}]}, {"year": "1903", "text": "<PERSON><PERSON>, American actress and singer (d. 1965)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, French author and poet (d. 1923)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Chinese-American actor (d. 1991)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese-American actor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese-American actor (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, French conductor and composer (d. 2003)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor and composer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor and composer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Estonian composer and conductor (d. 1982)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian composer and conductor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian composer and conductor (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss-American metaphysicist, philosopher, and author (d. 1998)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>ith<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss-American metaphysicist, philosopher, and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ith<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss-American metaphysicist, philosopher, and author (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ith<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American actor and game show host (d. 1969)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American-Canadian academic and politician (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian academic and politician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian academic and politician (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American actress (d. 2003)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ra_<PERSON>z\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2003)", "links": [{"title": "Nedra Volz", "link": "https://wikipedia.org/wiki/Nedra_Volz"}]}, {"year": "1910", "text": "<PERSON>, American actor and singer (d. 1979)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actor and singer (d. 1984)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Avon_Long\" title=\"<PERSON> Long\"><PERSON></a>, American actor and singer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Avon_Long\" title=\"<PERSON> Long\"><PERSON></a>, American actor and singer (d. 1984)", "links": [{"title": "Avon Long", "link": "https://wikipedia.org/wiki/Avon_Long"}]}, {"year": "1910", "text": "<PERSON>, American singer, drummer, and bandleader (d. 1995)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, drummer, and bandleader (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, drummer, and bandleader (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American decathlete (d. 1974)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Canadian soldier and surgeon (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>_<PERSON>\" title=\"Wilfred <PERSON>\">Wil<PERSON></a>, Canadian soldier and surgeon (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Wil<PERSON>\">Wil<PERSON></a>, Canadian soldier and surgeon (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>elo<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American pianist and composer (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American economist and journalist (d. 1991)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and journalist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and journalist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Canadian playwright and producer (d. 1995)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian playwright and producer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian playwright and producer (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>er"}]}, {"year": "1913", "text": "<PERSON>, American winemaker and philanthropist (d. 2008)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American winemaker and philanthropist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American winemaker and philanthropist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, German mathematician (d. 1943)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German mathematician (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German mathematician (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller"}]}, {"year": "1914", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor (d. 1998)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor (d. 1998)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Mexican poet (d. 1982)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Efra%C3%ADn_Huert<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican poet (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Efra%C3%ADn_Hu<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican poet (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Efra%C3%ADn_Huerta"}]}, {"year": "1915", "text": "<PERSON>, American firefighter (d. 2004)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Red_Adair\" title=\"Red Adair\"><PERSON> Adair</a>, American firefighter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Adair\" title=\"Red Adair\">Red Adair</a>, American firefighter (d. 2004)", "links": [{"title": "Red Adair", "link": "https://wikipedia.org/wiki/Red_Adair"}]}, {"year": "1915", "text": "<PERSON>, American author (d. 2002)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American mathematician (d. 2009)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Colombian lawyer and politician, 25th President of Colombia (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Julio_<PERSON>%C3%A9sar_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julio_C%C3%A9<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Colombia", "link": "https://wikipedia.org/wiki/President_of_Colombia"}]}, {"year": "1917", "text": "<PERSON>, American actor, singer, and director (d. 1981)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English snooker player and sportscaster (d. 2002)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player and sportscaster (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player and sportscaster (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Danish painter and illustrator (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and illustrator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and illustrator (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, West Prussia-born, English motor racing mechanic and race car constructor (d. 1983)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, West Prussia-born, English motor racing mechanic and race car constructor (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, West Prussia-born, English motor racing mechanic and race car constructor (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alf_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1918", "text": "<PERSON>, Italian-American economist and academic, Nobel Prize laureate (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Estonian actor and screenwriter (d. 1995)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/J%C3%BCri_J%C3%A4rvet\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian actor and screenwriter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BCri_J%C3%A4rvet\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian actor and screenwriter (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCri_J%C3%A4rvet"}]}, {"year": "1920", "text": "<PERSON>, English actor and singer (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Belgian author and academic (d. 2020)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Aster_Berkhof\" title=\"Aster Berkhof\"><PERSON><PERSON></a>, Belgian author and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aster_Berkhof\" title=\"Aster Berkhof\"><PERSON><PERSON></a>, Belgian author and academic (d. 2020)", "links": [{"title": "Aster Berkhof", "link": "https://wikipedia.org/wiki/Aster_Berkhof"}]}, {"year": "1922", "text": "<PERSON>, French pianist and educator (d. 2004)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and educator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and educator (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American basketball player and coach (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American composer and educator (d. 1994)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American businessman and author (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American astronomer and cosmologist (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and cosmologist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and cosmologist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American journalist and author (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>icker\" title=\"<PERSON> Wicker\"><PERSON></a>, American journalist and author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>icker\" title=\"<PERSON> Wicker\"><PERSON></a>, American journalist and author (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wicker"}]}, {"year": "1927", "text": "<PERSON>, Hungarian-English actress (d. 1998)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English actress (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English actress (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actor (d. 1995)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Australian actor, director, and screenwriter (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and screenwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American geneticist and academic (d. 2006)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and academic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, German sociologist and philosopher", "html": "1929 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sociologist and philosopher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sociologist and philosopher", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Hungarian-American soldier, Medal of Honor recipient (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1931", "text": "<PERSON>, Brazilian sociologist, academic, and politician, 34th President of Brazil", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian sociologist, academic, and politician, 34th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian sociologist, academic, and politician, 34th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1932", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1932", "text": "<PERSON>, English poet and academic (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Australian composer and conductor (d. 2018)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and conductor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and conductor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hunt\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tommy Hunt\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English general (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (d. 2017)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese author and illustrator (d. 2004)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese author and illustrator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese author and illustrator (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Kenyan economist (d. 1982)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Barack_Obama_Sr.\" title=\"Barack Obama Sr.\"><PERSON> Sr.</a>, Kenyan economist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barack_Obama_Sr.\" title=\"Barack Obama Sr.\"><PERSON> Sr.</a>, Kenyan economist (d. 1982)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/Barack_Obama_Sr."}]}, {"year": "1936", "text": "<PERSON>, New Zealand race car driver (d. 1992)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand race car driver (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand race car driver (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Surinamese politician, 6th President of Suriname", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Suriname\" title=\"President of Suriname\">President of Suriname</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Suriname\" title=\"President of Suriname\">President of Suriname</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Suriname", "link": "https://wikipedia.org/wiki/President_of_Suriname"}]}, {"year": "1937", "text": "<PERSON>, American basketball player and coach", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American lawyer and politician, 29th Governor of West Virginia", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of West Virginia", "link": "https://wikipedia.org/wiki/Governor_of_West_Virginia"}]}, {"year": "1937", "text": "<PERSON>, Canadian archaeologist, anthropologist and historian (d. 2006)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archaeologist, anthropologist and historian (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archaeologist, anthropologist and historian (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Ukrainian colonel, engineer, and astronaut", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian colonel, engineer, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian colonel, engineer, and astronaut", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Australian footballer and coach", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_footballer)"}]}, {"year": "1939", "text": "<PERSON>, American baseball player and sportscaster (d. 2020)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Canadian historian, author, and journalist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian historian, author, and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian historian, author, and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American businessman and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Firestone\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Firestone\"><PERSON></a>, American businessman and politician", "links": [{"title": "Brooks Firestone", "link": "https://wikipedia.org/wiki/Brooks_Firestone"}]}, {"year": "1941", "text": "<PERSON>, French footballer and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English director and screenwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, English chef and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English chef and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Scottish painter (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American journalist, critic, and screenwriter (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, critic, and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, critic, and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English author and illustrator (d. 2017)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, South African politician and 2nd President of South Africa", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Thabo_Mbeki\" title=\"<PERSON>hab<PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician and 2nd <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thabo_Mbeki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician and 2nd <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thab<PERSON>_<PERSON><PERSON>i"}, {"title": "President of South Africa", "link": "https://wikipedia.org/wiki/President_of_South_Africa"}]}, {"year": "1942", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American record producer (d. 2024)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American bass player and producer (d. 1980)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian actor and director", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Dutch conductor (d. 2004)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, Dutch conductor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, Dutch conductor (d. 2004)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)"}]}, {"year": "1943", "text": "<PERSON>, English actor (d. 1997)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 1997)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian singer, dancer, and actress (d. 2021)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Carr%C3%A0\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian singer, dancer, and actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C3%A0\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian singer, dancer, and actress (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Raffaella_Carr%C3%A0"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Hungarian soprano and actress", "html": "1943 - <a href=\"https://wikipedia.org/wiki/%C3%89va_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89va_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian soprano and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89va_Marton"}]}, {"year": "1944", "text": "<PERSON>, American broadcaster and political analyst", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster and political analyst", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster and political analyst", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American pop/country singer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop/country singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop/country singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English journalist and author (d. 2010)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, American wrestler (d. 1988)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler (d. 1988)", "links": [{"title": "Bruiser Brody", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Italian footballer and manager", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Brazilian singer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Beth%C3%A2nia\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A2nia\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Beth%C3%A2nia"}]}, {"year": "1946", "text": "<PERSON>, British automobile designer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British automobile designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British automobile designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Puerto Rican-American model and actress, Miss Puerto Rico 1967", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American model and actress, <a href=\"https://wikipedia.org/wiki/Miss_Puerto_Rico\" title=\"Miss Puerto Rico\">Miss Puerto Rico 1967</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American model and actress, <a href=\"https://wikipedia.org/wiki/Miss_Puerto_Rico\" title=\"Miss Puerto Rico\">Miss Puerto Rico 1967</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>l"}, {"title": "Miss Puerto Rico", "link": "https://wikipedia.org/wiki/Miss_Puerto_Rico"}]}, {"year": "1947", "text": "<PERSON>, French actor, director, producer, and screenwriter (d. 2010)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American academic, psychologist, and sociologist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/She<PERSON>_<PERSON>\" title=\"She<PERSON>\"><PERSON><PERSON></a>, American academic, psychologist, and sociologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/She<PERSON>_<PERSON>\" title=\"She<PERSON>\"><PERSON><PERSON></a>, American academic, psychologist, and sociologist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/She<PERSON>_<PERSON>le"}]}, {"year": "1949", "text": "<PERSON>, American author and illustrator", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Polish lawyer and politician, 13th Prime Minister of Poland", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Jaros%C5%82aw_<PERSON><PERSON><PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaros%C5%82aw_<PERSON><PERSON><PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaros%C5%82aw_<PERSON><PERSON>y%C5%84ski"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Polish lawyer and politician, 4th President of Poland (d. 2010)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lech_Kaczy%C5%84ski"}, {"title": "President of Poland", "link": "https://wikipedia.org/wiki/President_of_Poland"}]}, {"year": "1950", "text": "<PERSON>, Welsh drummer and producer (d. 2014)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ath\" title=\"<PERSON>\"><PERSON></a>, Welsh drummer and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ath\" title=\"<PERSON>\"><PERSON></a>, Welsh drummer and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rod_de%27Ath"}]}, {"year": "1950", "text": "<PERSON><PERSON>, German hurdler", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German hurdler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American lawyer and politician, 28th United States Secretary of Agriculture", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture\" title=\"United States Secretary of Agriculture\">United States Secretary of Agriculture</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture\" title=\"United States Secretary of Agriculture\">United States Secretary of Agriculture</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Agriculture", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture"}]}, {"year": "1950", "text": "<PERSON>, Scottish singer-songwriter and guitarist (d. 2011)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Kuwaiti journalist and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kuwaiti journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kuwaiti journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mohammed_Al-Sage<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actress and comedian", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English-Welsh journalist and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian botanist and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian botanist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian botanist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Hungarian chess player (d. 2014)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian chess player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian chess player (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>la_Sax"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Estonian physician and politician, Estonian Minister of Social Affairs", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Tiiu_<PERSON><PERSON>\" title=\"Tiiu <PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian physician and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)\" class=\"mw-redirect\" title=\"Minister of Social Affairs (Estonia)\">Estonian Minister of Social Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tiiu_<PERSON><PERSON>\" title=\"Tiiu <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian physician and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)\" class=\"mw-redirect\" title=\"Minister of Social Affairs (Estonia)\">Estonian Minister of Social Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tiiu_Aro"}, {"title": "Minister of Social Affairs (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)"}]}, {"year": "1952", "text": "<PERSON>, Canadian ice hockey player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Italian actress, director, producer, and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, South Korean singer and businessman, founded S.M. Entertainment", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and businessman, founded <a href=\"https://wikipedia.org/wiki/S.M._Entertainment\" class=\"mw-redirect\" title=\"S.M. Entertainment\">S.M. Entertainment</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and businessman, founded <a href=\"https://wikipedia.org/wiki/S.M._Entertainment\" class=\"mw-redirect\" title=\"S.M. Entertainment\">S.M. Entertainment</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "S.M. Entertainment", "link": "https://wikipedia.org/wiki/S.M._Entertainment"}]}, {"year": "1953", "text": "<PERSON>, English pianist and educator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, English pianist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, English pianist and educator", "links": [{"title": "<PERSON> (pianist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)"}]}, {"year": "1955", "text": "<PERSON>, Canadian lawyer and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fast\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English organist and conductor (d. 2015)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, English organist and conductor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, English organist and conductor (d. 2015)", "links": [{"title": "<PERSON> (organist)", "link": "https://wikipedia.org/wiki/<PERSON>(organist)"}]}, {"year": "1957", "text": "<PERSON>, Spanish footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON>ina\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON>ina\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON>ina"}]}, {"year": "1957", "text": "<PERSON>, American novelist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, German jurist and politician, Federal Minister for Special Affairs of Germany", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and politician, <a href=\"https://wikipedia.org/wiki/Federal_Minister_for_Special_Affairs_of_Germany\" title=\"Federal Minister for Special Affairs of Germany\">Federal Minister for Special Affairs of Germany</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and politician, <a href=\"https://wikipedia.org/wiki/Federal_Minister_for_Special_Affairs_of_Germany\" title=\"Federal Minister for Special Affairs of Germany\">Federal Minister for Special Affairs of Germany</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Federal Minister for Special Affairs of Germany", "link": "https://wikipedia.org/wiki/Federal_Minister_for_Special_Affairs_of_Germany"}]}, {"year": "1958", "text": "<PERSON>, British voice actor and actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British voice actor and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British voice actor and actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1959", "text": "<PERSON>, American animation screenwriter and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animation screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animation screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American director and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian journalist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(news_anchor)\" title=\"<PERSON> (news anchor)\"><PERSON></a>, Canadian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(news_anchor)\" title=\"<PERSON> (news anchor)\"><PERSON></a>, Canadian journalist", "links": [{"title": "<PERSON> (news anchor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(news_anchor)"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Oz_Fox\" title=\"Oz Fox\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oz_Fox\" title=\"Oz Fox\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oz_Fox"}]}, {"year": "1961", "text": "<PERSON>, Venezuelan baseball player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_<PERSON>ga\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_<PERSON>ga\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Galarraga"}]}, {"year": "1961", "text": "<PERSON>, American novelist and poet", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist and poet", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "1961", "text": "<PERSON>, English singer-songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American physicist and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American keyboard player and songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American keyboard player and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)\" class=\"mw-redirect\" title=\"<PERSON> (defensive end)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)\" class=\"mw-redirect\" title=\"<PERSON> (defensive end)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (defensive end)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Iraqi commander (d. 2003)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iraqi commander (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iraqi commander (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American publicist and author (d. 2013)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publicist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publicist and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian figure skater, choreographer, and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater, choreographer, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater, choreographer, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Bahamian high jumper", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American sprinter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German decathlete", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_M%C3%BCller"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Albanian cyclist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American journalist and author (d. 2012)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English journalist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Slovak footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1k\" title=\"<PERSON>\"><PERSON></a>, Slovak footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1k\" title=\"<PERSON>\"><PERSON></a>, Slovak footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1k"}]}, {"year": "1970", "text": "<PERSON>, American director and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress and singer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English-Irish footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American soul singer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Welsh rugby referee and TV presenter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby referee and TV presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby referee and TV presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Estonian pianist and conductor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pianist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pianist and conductor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, South African actor, director, and composer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Wikus_du_Toit\" title=\"Wikus du Toit\">Wik<PERSON> du Toit</a>, South African actor, director, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wikus_du_Toit\" title=\"Wikus du Toit\">Wik<PERSON> du Toit</a>, South African actor, director, and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wikus_<PERSON>_<PERSON>it"}]}, {"year": "1973", "text": "<PERSON>, French actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American author and music critic", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and music critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and music critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Austrian skier", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian rugby league player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Italian footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Russian fencer and coach (d. 2015)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian fencer and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian fencer and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Belgian actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Latvian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C5%86ko\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C5%86ko\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C5%86ko"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Martin_St._Louis\" title=\"Martin St. Louis\"><PERSON> Louis</a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Martin_St._Louis\" title=\"Martin St. Louis\"><PERSON>. Louis</a>, Canadian ice hockey player", "links": [{"title": "Martin St. Louis", "link": "https://wikipedia.org/wiki/Martin_St._Louis"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Chinese table tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese table tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese table tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actress, producer, and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Hong Kong singer-songwriter and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Russian race walker", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian race walker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian runner", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4ki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4ki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antero_Niittym%C3%A4ki"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American music executive", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American music executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American music executive", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American-Australian rugby league player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Swiss footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, French-Algerian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"Na<PERSON>\"><PERSON><PERSON></a>, French-Algerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>_<PERSON>j"}]}, {"year": "1982", "text": "<PERSON>, Italian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian singer-songwriter, guitarist, and actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian rugby league player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian rugby league player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1983)\" title=\"<PERSON> (rugby league, born 1983)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1983)\" title=\"<PERSON> (rugby league, born 1983)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league, born 1983)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1983)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Canadian rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Nanyak_<PERSON>a\" title=\"Nanyak Dala\"><PERSON><PERSON><PERSON></a>, Canadian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ya<PERSON>_<PERSON>a\" title=\"Nanyak Dala\"><PERSON><PERSON><PERSON></a>, Canadian rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nanyak_Dala"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American animator and television producer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and television producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and television producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Latvian decathlete", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C5%86%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C5%86%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian decathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edgars_Eri%C5%86%C5%A1"}]}, {"year": "1986", "text": "<PERSON>, French tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Scottish actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Mexican footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON> (footballer, born 1987)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, English cricketer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ali\"><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Greek footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American musician", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, French-born Gabonese footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-born Gabonese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-born Gabonese footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American football player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Adam\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Romanian gymnast", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99a\" title=\"<PERSON>\"><PERSON></a>, Romanian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99a\" title=\"<PERSON>\"><PERSON></a>, Romanian gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99a"}]}, {"year": "1990", "text": "<PERSON>, American ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American triple jumper", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American triple jumper", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1993", "text": "<PERSON>, Israeli musician, producer, singer, songwriter, and multi-instrumentalist", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli musician, producer, singer, songwriter, and multi-instrumentalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli musician, producer, singer, songwriter, and multi-instrumentalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "Takeoff, American rapper (d. 2022)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Take<PERSON>_(rapper)\" title=\"Takeoff (rapper)\">Take<PERSON></a>, American rapper (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Take<PERSON>_(rapper)\" title=\"Takeoff (rapper)\">Takeoff</a>, American rapper (d. 2022)", "links": [{"title": "Take<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/Take<PERSON>_(rapper)"}]}, {"year": "1995", "text": "<PERSON>, Russian figure skater", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kovtun\"><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kovtun\"><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>vtun"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Croatian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alen_Halilovi%C4%87"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Dutch figure skater", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Niki_Wories\" title=\"Nik<PERSON> W<PERSON>\"><PERSON><PERSON></a>, Dutch figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niki_Wories\" title=\"<PERSON><PERSON> W<PERSON>\"><PERSON><PERSON></a>, Dutch figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niki_Wories"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, German tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, South Korean singer and actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American rapper", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Redd\" title=\"<PERSON><PERSON> Redd\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d\" title=\"<PERSON><PERSON> Redd\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Redd"}]}], "Deaths": [{"year": "741", "text": "<PERSON> the Isaurian, Byzantine emperor (b. 685)", "html": "741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Isaurian\" title=\"<PERSON> III the Isaurian\"><PERSON> the Isaurian</a>, Byzantine emperor (b. 685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Isaurian\" title=\"<PERSON> the Isaurian\"><PERSON> the Isaurian</a>, Byzantine emperor (b. 685)", "links": [{"title": "<PERSON> the Isaurian", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Isaurian"}]}, {"year": "908", "text": "<PERSON>, general of Yang Wu", "html": "908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, general of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, general of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1095", "text": "<PERSON> of Hungary (b. c. 1050)", "html": "1095 - <a href=\"https://wikipedia.org/wiki/Sophia_of_Hungary\" title=\"Sophia of Hungary\">Sophia of Hungary</a> (b. c. 1050)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sophia_of_Hungary\" title=\"Sophia of Hungary\">Sophia of Hungary</a> (b. c. 1050)", "links": [{"title": "Sophia of Hungary", "link": "https://wikipedia.org/wiki/Sophia_of_Hungary"}]}, {"year": "1164", "text": "<PERSON> Schönau, German Benedictine visionary (b. c. 1129)", "html": "1164 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Sch%C3%B6nau\" title=\"<PERSON> of Schönau\"><PERSON> of Schönau</a>, German Benedictine visionary (b. c. 1129)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_<PERSON>h%C3%B6nau\" title=\"<PERSON> of Schönau\"><PERSON> of Schönau</a>, German Benedictine visionary (b. c. 1129)", "links": [{"title": "Elisabeth of Schönau", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sch%C3%B6nau"}]}, {"year": "1234", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (b. 1218)", "html": "1234 - <a href=\"https://wikipedia.org/wiki/Emperor_Ch%C5%ABky%C5%8D\" title=\"Emperor Chūkyō\">Emperor <PERSON><PERSON>ky<PERSON></a> of Japan (b. 1218)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Ch%C5%ABky%C5%8D\" title=\"Emperor Chūky<PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1218)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Ch%C5%ABky%C5%8D"}]}, {"year": "1250", "text": "<PERSON> of Portugal, Queen of León", "html": "1250 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Queen_of_Le%C3%B3n\" title=\"<PERSON> of Portugal, Queen of León\"><PERSON> of Portugal, Queen of León</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Queen_of_Le%C3%B3n\" title=\"<PERSON> of Portugal, Queen of León\"><PERSON> of Portugal, Queen of León</a>", "links": [{"title": "<PERSON> of Portugal, Queen of León", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Portugal,_Queen_of_Le%C3%B3n"}]}, {"year": "1291", "text": "<PERSON> of Aragon (b. 1265)", "html": "1291 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (b. 1265)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (b. 1265)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_III_of_Aragon"}]}, {"year": "1333", "text": "<PERSON>, Duke of Bavaria (b. 1312)", "html": "1333 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1312)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1312)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1464", "text": "<PERSON><PERSON><PERSON>, Flemish painter (b. 1400)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> van <PERSON> Weyd<PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish painter (b. 1400)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> van <PERSON> We<PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Flemish painter (b. 1400)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1588", "text": "<PERSON>, English minister and poet (b. 1517)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>, English minister and poet (b. 1517)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>, English minister and poet (b. 1517)", "links": [{"title": "<PERSON> (printer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(printer)"}]}, {"year": "1629", "text": "<PERSON><PERSON>, Dutch admiral (b. 1577)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch admiral (b. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch admiral (b. 1577)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1650", "text": "<PERSON>, German priest, physicist, and astronomer (b. 1575)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest, physicist, and astronomer (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest, physicist, and astronomer (b. 1575)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1673", "text": "<PERSON>, French-Canadian nurse, founded the Hôtel-Dieu de Montréal (b. 1606)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian nurse, founded the <a href=\"https://wikipedia.org/wiki/H%C3%B4tel-Dieu_de_Montr%C3%A9al\" title=\"Hôtel-Dieu de Montréal\">Hôtel-Dieu de Montréal</a> (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian nurse, founded the <a href=\"https://wikipedia.org/wiki/H%C3%B4tel-Dieu_de_Montr%C3%A9al\" title=\"Hôtel-Dieu de Montréal\">Hôtel-Dieu de Montréal</a> (b. 1606)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hôtel-Dieu de Montréal", "link": "https://wikipedia.org/wiki/H%C3%B4tel-Dieu_de_Montr%C3%A9al"}]}, {"year": "1704", "text": "<PERSON>, English author and translator (b. 1662)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(satirist)\" title=\"<PERSON> (satirist)\"><PERSON></a>, English author and translator (b. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(satirist)\" title=\"<PERSON> (satirist)\"><PERSON></a>, English author and translator (b. 1662)", "links": [{"title": "<PERSON> (satirist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(satirist)"}]}, {"year": "1726", "text": "<PERSON>, French organist and composer (b. 1657)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1657)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1742", "text": "<PERSON>, English politician, Chancellor of the Exchequer (b. 1670)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1749", "text": "<PERSON>, English poet and politician (b. 1674)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and politician (b. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and politician (b. 1674)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1772", "text": "<PERSON>, German jurist and scholar (b. 1706)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and scholar (b. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and scholar (b. 1706)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1772", "text": "<PERSON>, Dutch-Austrian physician and reformer (b. 1700)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Austrian physician and reformer (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Austrian physician and reformer (b. 1700)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, Scottish religious leader (b. 1714)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> G<PERSON>\"><PERSON></a>, Scottish religious leader (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_G<PERSON>\" title=\"Adam Gib\"><PERSON></a>, Scottish religious leader (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ib"}]}, {"year": "1794", "text": "<PERSON>, French lawyer and politician (b. 1760)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Buzot\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Buzot\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Buzot"}]}, {"year": "1794", "text": "<PERSON>, Scottish-English general and politician, 20th Governor of the Province of Quebec (b. 1721)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1721)\" title=\"<PERSON> (British Army officer, born 1721)\"><PERSON></a>, Scottish-English general and politician, 20th <a href=\"https://wikipedia.org/wiki/List_of_Governors_General_of_Canada\" class=\"mw-redirect\" title=\"List of Governors General of Canada\">Governor of the Province of Quebec</a> (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer,_born_1721)\" title=\"<PERSON> (British Army officer, born 1721)\"><PERSON></a>, Scottish-English general and politician, 20th <a href=\"https://wikipedia.org/wiki/List_of_Governors_General_of_Canada\" class=\"mw-redirect\" title=\"List of Governors General of Canada\">Governor of the Province of Quebec</a> (b. 1721)", "links": [{"title": "<PERSON> (British Army officer, born 1721)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1721)"}, {"title": "List of Governors General of Canada", "link": "https://wikipedia.org/wiki/List_of_Governors_General_of_Canada"}]}, {"year": "1804", "text": "<PERSON>, Duchess of Parma (b. 1746)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Parma\" title=\"<PERSON>, Duchess of Parma\"><PERSON>, Duchess of Parma</a> (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Parma\" title=\"<PERSON>, Duchess of Parma\"><PERSON>, Duchess of Parma</a> (b. 1746)", "links": [{"title": "<PERSON>, Duchess of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Parma"}]}, {"year": "1815", "text": "<PERSON>, Welsh-English general and politician (b. 1758)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English general and politician (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English general and politician (b. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, American minister, missionary, and academic (b. 1771)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, missionary, and academic (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, missionary, and academic (b. 1771)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, English farmer and journalist (b. 1763)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English farmer and journalist (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English farmer and journalist (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, German army officer and writer (b. 1783)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German army officer and writer (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German army officer and writer (b. 1783)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON> <PERSON><PERSON><PERSON> of Prussia (b. 1864)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_of_Prussia_(1864%E2%80%931866)\" title=\"Prince <PERSON><PERSON><PERSON> of Prussia (1864-1866)\">Prince <PERSON><PERSON><PERSON> of Prussia</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_of_Prussia_(1864%E2%80%931866)\" title=\"Prince <PERSON><PERSON> of Prussia (1864-1866)\">Prince <PERSON><PERSON><PERSON> of Prussia</a> (b. 1864)", "links": [{"title": "Prince <PERSON><PERSON><PERSON> of Prussia (1864-1866)", "link": "https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_of_Prussia_(1864%E2%80%931866)"}]}, {"year": "1902", "text": "<PERSON>, English novelist, satirist, and critic (b. 1835)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, English novelist, satirist, and critic (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, English novelist, satirist, and critic (b. 1835)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_(novelist)"}]}, {"year": "1905", "text": "<PERSON>, Italian soldier (b. 1830)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, German lieutenant and pilot (b. 1890)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and pilot (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and pilot (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Romanian critic and politician, 23rd Prime Minister of Romania (b. 1840)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Tit<PERSON>_<PERSON>\" title=\"Tit<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian critic and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tit<PERSON>_<PERSON>\" title=\"Tit<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian critic and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (b. 1840)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tit<PERSON>_<PERSON>"}, {"title": "Prime Minister of Romania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Romania"}]}, {"year": "1921", "text": "<PERSON>, Indian Islamic scholar and author (b. 1867)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian Islamic scholar and author (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian Islamic scholar and author (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>aunpuri"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Dutch astronomer and academic (b. 1851)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch astronomer and academic (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch astronomer and academic (b. 1851)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON> of Russia, Queen consort of the Hellenes (b. 1851)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a>, Queen consort of the Hellenes (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a>, Queen consort of the Hellenes (b. 1851)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_Con<PERSON>ino<PERSON>na_of_Russia"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Norwegian pilot and explorer (b. 1872)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Roald_Am<PERSON>en\" title=\"Roald Amundsen\"><PERSON><PERSON><PERSON></a>, Norwegian pilot and explorer (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roa<PERSON>_Am<PERSON>\" title=\"Roald Am<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian pilot and explorer (b. 1872)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roald_Am<PERSON>en"}]}, {"year": "1936", "text": "<PERSON>, Russian novelist, short story writer, and playwright (b. 1868)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Maxim_Gorky\" title=\"Maxim Gorky\"><PERSON></a>, Russian novelist, short story writer, and playwright (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maxim_Gorky\" title=\"Maxim Gorky\"><PERSON> Gorky</a>, Russian novelist, short story writer, and playwright (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maxim_Gorky"}]}, {"year": "1937", "text": "<PERSON>, French politician, 13th President of France (b. 1863)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gaston_<PERSON>"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1942", "text": "<PERSON>, American trombonist, bandleader, and politician (b. 1870)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, bandleader, and politician (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, bandleader, and politician (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Greek commander (b. 1912)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek commander (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek commander (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American geologist and educator (b. 1862)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Florence_Bascom\" title=\"Florence Bascom\"><PERSON></a>, American geologist and educator (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_Bascom\" title=\"Florence Bascom\"><PERSON></a>, American geologist and educator (b. 1862)", "links": [{"title": "Florence Bascom", "link": "https://wikipedia.org/wiki/Florence_Bascom"}]}, {"year": "1945", "text": "<PERSON>, American general (b. 1886)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American general (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American general (b. 1886)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese admiral (b. 1898)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English-Australian politician, 31st Premier of Tasmania (b. 1891)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1959", "text": "<PERSON>, American actress (b. 1879)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Mexican-American actor (b. 1912)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actor (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Armend%C3%A1riz"}]}, {"year": "1964", "text": "<PERSON>, Italian painter (b. 1890)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Italian race car driver (b. 1937)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON><PERSON><PERSON> (racing driver)\"><PERSON><PERSON><PERSON></a>, Italian race car driver (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON><PERSON><PERSON> (racing driver)\"><PERSON><PERSON><PERSON></a>, Italian race car driver (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(racing_driver)"}]}, {"year": "1967", "text": "<PERSON>, Swiss race car driver (b. 1942)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss race car driver (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, Swiss race car driver (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor (b. 1905)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Russian-Swiss chemist and academic, Nobel Prize laureate (b. 1889)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Brazilian mathematician and academic (b. 1896)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/J%C3%BAlio_C%C3%A9<PERSON>_de_Mello_e_Souza\" title=\"<PERSON><PERSON><PERSON> Souza\"><PERSON><PERSON><PERSON></a>, Brazilian mathematician and academic (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BAlio_C%C3%A9sar_de_Mello_e_Souza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian mathematician and academic (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON> Souza", "link": "https://wikipedia.org/wiki/J%C3%BAlio_C%C3%A9sar_de_Mello_e_Souza"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Russian marshal and politician, Minister of Defence for the Soviet Union (b. 1896)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian marshal and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)\" title=\"Minister of Defence (Soviet Union)\">Minister of Defence for the Soviet Union</a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian marshal and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)\" title=\"Minister of Defence (Soviet Union)\">Minister of Defence for the Soviet Union</a> (b. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Defence (Soviet Union)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)"}]}, {"year": "1975", "text": "<PERSON>, German-Israeli philosopher and author (b. 1883)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli philosopher and author (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli philosopher and author (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American physician and author (b. 1884)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English director and screenwriter (b. 1904)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, French cyclist (b. 1904)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>q"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American novelist, journalist, and playwright (b. 1892)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist, journalist, and playwright (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist, journalist, and playwright (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American novelist and short story writer (b. 1912)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, German-Austrian actor and director (b. 1915)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Curd_J%C3%BCrgens\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Austrian actor and director (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Curd_J%C3%BCrgens\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Austrian actor and director (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Curd_J%C3%BCrgens"}]}, {"year": "1984", "text": "<PERSON>, American lawyer and radio host (b. 1934)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and radio host (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and radio host (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, French illustrator (b. 1892)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, French illustrator (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, French illustrator (b. 1892)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(artist)"}]}, {"year": "1986", "text": "<PERSON>, American journalist (b. 1921)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON> <PERSON><PERSON>, American journalist and author (b. 1907)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"I. <PERSON><PERSON> Stone\"><PERSON><PERSON> <PERSON><PERSON></a>, American journalist and author (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"I. F<PERSON> Stone\"><PERSON><PERSON> <PERSON><PERSON></a>, American journalist and author (b. 1907)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, the first black Chief Nursing Officer of Nigeria (b. 1910)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, the first black <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Health_(Nigeria)\" class=\"mw-redirect\" title=\"Federal Ministry of Health (Nigeria)\">Chief Nursing Officer</a> of Nigeria (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, the first black <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Health_(Nigeria)\" class=\"mw-redirect\" title=\"Federal Ministry of Health (Nigeria)\">Chief Nursing Officer</a> of Nigeria (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Federal Ministry of Health (Nigeria)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Health_(Nigeria)"}]}, {"year": "1992", "text": "<PERSON>, Australian singer-songwriter and pianist (b. 1944)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian singer-songwriter and pianist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian singer-songwriter and pianist (b. 1944)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish-Israeli painter and educator (b. 1896)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Mordecai_Ardon\" title=\"Mordecai Ardon\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-Israeli painter and educator (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mordecai_Ardon\" title=\"Mordecai Ardon\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-Israeli painter and educator (b. 1896)", "links": [{"title": "Mordecai Ardon", "link": "https://wikipedia.org/wiki/Mordeca<PERSON>_<PERSON>rdon"}]}, {"year": "1993", "text": "<PERSON>, American activist, founded the Oscar Wilde Bookshop (b. 1940)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hop\" title=\"Oscar Wilde Bookshop\"><PERSON> Bookshop</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bookshop\" title=\"Oscar Wilde Bookshop\"><PERSON> Bookshop</a> (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Oscar Wilde Bookshop", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bookshop"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Estonian-Soviet military pilot and politician (b. 1909)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pu<PERSON>\"><PERSON><PERSON></a>, Estonian-Soviet military pilot and politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-Soviet military pilot and politician (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>pp"}]}, {"year": "1997", "text": "<PERSON>, Ukrainian-German author and academic (b. 1912)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German author and academic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German author and academic (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor and tenor (b. 1908)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and tenor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and tenor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actress (b. 1928)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}]}, {"year": "2003", "text": "<PERSON>, American baseball player and manager (b. 1923)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cricketer (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Argentinian mathematician and academic (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian mathematician and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian mathematician and academic (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actor, director, and screenwriter (b. 1906)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Martinique-French author (b. 1915)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Martinique-French author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Martinique-French author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English comedian and actor (b. 1930)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American singer and producer (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and producer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and producer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Canadian singer-songwriter (b. 1951)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, French actor, director, and screenwriter (b. 1908)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American author and illustrator (b. 1915)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, German sculptor (b. 1928)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, German sculptor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, German sculptor (b. 1928)", "links": [{"title": "<PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)"}]}, {"year": "2010", "text": "<PERSON>, American wrestler (b. 1980)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Trent Acid\"><PERSON></a>, American wrestler (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Trent Acid\"><PERSON></a>, American wrestler (b. 1980)", "links": [{"title": "Trent Acid", "link": "https://wikipedia.org/wiki/Trent_Acid"}]}, {"year": "2010", "text": "<PERSON>, Portuguese novelist Nobel Prize laureate (b. 1922)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Sara<PERSON>go\" title=\"<PERSON>\"><PERSON></a>, Portuguese novelist <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese novelist <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Saramago"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Turkish composer (b. 1942)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Ok<PERSON>_<PERSON><PERSON>%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish composer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish composer (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ok<PERSON>_<PERSON><PERSON>i%C5%9F"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Russian activist (b. 1923)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian activist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian activist (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Zambian politician, 2nd President of Zambia (b. 1943)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frederick_<PERSON>"}, {"title": "President of Zambia", "link": "https://wikipedia.org/wiki/President_of_Zambia"}]}, {"year": "2011", "text": "<PERSON>, American saxophonist (b. 1942)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Argentinian photographer and director (b. 1906)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian photographer and director (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian photographer and director (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, German author and activist (b. 1907)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and activist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and activist (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Welsh cricketer (b. 1989)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh cricketer (b. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh cricketer (b. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Peruvian general and politician, 109th Prime Minister of Peru (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>do_Jarr%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Peruvian general and politician, 109th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Peru\" title=\"Prime Minister of Peru\">Prime Minister of Peru</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>do_Jarr%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Peruvian general and politician, 109th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Peru\" title=\"Prime Minister of Peru\">Prime Minister of Peru</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mercado_Jarr%C3%ADn"}, {"title": "Prime Minister of Peru", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Peru"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Greek footballer and manager (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>ket<PERSON>_<PERSON>agoulias\" title=\"Alketas Panagoulias\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ago<PERSON>as\" title=\"Alket<PERSON> Panagoulias\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager (b. 1934)", "links": [{"title": "Alketas Panagoulias", "link": "https://wikipedia.org/wiki/Alketas_Panagoulias"}]}, {"year": "2012", "text": "<PERSON>, American businessman and politician (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American engineer and politician (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brent_F._<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician (b. 1932)", "links": [{"title": "Brent <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Scottish bass player (b. 1955)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish bass player (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish bass player (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 26th Lieutenant Governor of British Columbia (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Garde_G<PERSON>om\" title=\"Garde Gardom\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_British_Columbia\" title=\"Lieutenant Governor of British Columbia\">Lieutenant Governor of British Columbia</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gard<PERSON>_<PERSON>\" title=\"<PERSON>arde Gardom\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_British_Columbia\" title=\"Lieutenant Governor of British Columbia\">Lieutenant Governor of British Columbia</a> (b. 1924)", "links": [{"title": "Garde Gardom", "link": "https://wikipedia.org/wiki/Garde_Gardom"}, {"title": "Lieutenant Governor of British Columbia", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_British_Columbia"}]}, {"year": "2013", "text": "<PERSON>, American journalist and author (b. 1980)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (b. 1980)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_(journalist)"}]}, {"year": "2013", "text": "<PERSON>, English ballet dancer (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, English ballet dancer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, English ballet dancer (b. 1946)", "links": [{"title": "<PERSON> (dancer)", "link": "https://wikipedia.org/wiki/<PERSON>(dancer)"}]}, {"year": "2014", "text": "<PERSON>, American chemist and engineer (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American singer-songwriter and conductor (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and conductor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mann\"><PERSON></a>, American singer-songwriter and conductor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian author (b. 1914)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Canadian author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Canadian author (b. 1914)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2014", "text": "<PERSON>, Russian general (b. 1957)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American pianist and composer (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Silver\"><PERSON></a>, American pianist and composer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American comedian, actor, and screenwriter (b. 1941)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Phil_<PERSON>\" title=\"Phil Austin\"><PERSON></a>, American comedian, actor, and screenwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Phil_Austin\" title=\"Phil Austin\"><PERSON></a>, American comedian, actor, and screenwriter (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_Austin"}]}, {"year": "2015", "text": "<PERSON>, American businessman, co-founded Comcast (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Comcast\" title=\"Comcast\">Comcast</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Comcast\" title=\"Comcast\">Comcast</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Comcast", "link": "https://wikipedia.org/wiki/Comcast"}]}, {"year": "2015", "text": "<PERSON>, American football player and broadcaster, co-founded Univision (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and broadcaster, co-founded <a href=\"https://wikipedia.org/wiki/Univision\" title=\"Univision\">Univision</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and broadcaster, co-founded <a href=\"https://wikipedia.org/wiki/Univision\" title=\"Univision\">Univision</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Univision", "link": "https://wikipedia.org/wiki/Univision"}]}, {"year": "2015", "text": "<PERSON>, American historian and academic (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian educationist, founder and chancellor of Sathyabama University (b. 1931)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Jeppiaar\" title=\"Je<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian educationist, founder and chancellor of <a href=\"https://wikipedia.org/wiki/Sathyabama_University\" class=\"mw-redirect\" title=\"Sathyabama University\">Sathyabama University</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ia<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian educationist, founder and chancellor of <a href=\"https://wikipedia.org/wiki/Sathyabama_University\" class=\"mw-redirect\" title=\"Sathyabama University\">Sathyabama University</a> (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jeppiaar"}, {"title": "Sathyabama University", "link": "https://wikipedia.org/wiki/Sathyabama_University"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, American rapper (b. 1998)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/XXXTentacion\" title=\"XXXTentacion\">XXXTentac<PERSON></a>, American rapper (b. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/XXXTentacion\" title=\"XXXTentacion\">XXXTentac<PERSON></a>, American rapper (b. 1998)", "links": [{"title": "XXXTentacion", "link": "https://wikipedia.org/wiki/XXXTentacion"}]}, {"year": "2018", "text": "<PERSON> <PERSON> (also known as <PERSON><PERSON>) American professional wrestler (b. 1955)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Van_Vader\" title=\"Big Van Vader\"><PERSON> <PERSON>ader</a> (also known as <PERSON><PERSON>) American professional wrestler (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Van_Vader\" title=\"Big Van Vader\"><PERSON> Van Vader</a> (also known as <PERSON><PERSON>) American professional wrestler (b. 1955)", "links": [{"title": "Big Van Vader", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American rapper (b. 1997)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper (b. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper (b. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>o"}]}, {"year": "2020", "text": "<PERSON>, English singer who was the \"Forces' Sweetheart\" in World War II (b. 1917)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer who was the \"Forces' Sweetheart\" in World War II (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer who was the \"Forces' Sweetheart\" in World War II (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Danish politician, minister of foreign affairs (b. 1941)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Denmark)\" title=\"Minister of Foreign Affairs (Denmark)\">minister of foreign affairs</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Denmark)\" title=\"Minister of Foreign Affairs (Denmark)\">minister of foreign affairs</a> (b. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "Minister of Foreign Affairs (Denmark)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Denmark)"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Malaysian actress, singer, master of ceremonies (b. 1970)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian actress, singer, <a href=\"https://wikipedia.org/wiki/Master_of_ceremonies\" title=\"Master of ceremonies\">master of ceremonies</a> (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian actress, singer, <a href=\"https://wikipedia.org/wiki/Master_of_ceremonies\" title=\"Master of ceremonies\">master of ceremonies</a> (b. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Master of ceremonies", "link": "https://wikipedia.org/wiki/Master_of_ceremonies"}]}, {"year": "2023", "text": "Notable victims of the Titan submersible implosion:\n<PERSON><PERSON><PERSON>, Pakistani-British businessman (b. 1975)\n<PERSON><PERSON>, British businessman (b. 1964)\n<PERSON><PERSON><PERSON>, French navy commander and explorer (b. 1946)\n<PERSON>, American businessman, CEO and founder of OceanGate (b. 1962)", "html": "2023 - Notable victims of the <a href=\"https://wikipedia.org/wiki/Titan_submersible_implosion\" title=\"Titan submersible implosion\"><i>Titan</i> submersible implosion</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani-British businessman (b. 1975)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British businessman (b. 1964)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">navy</a> commander and explorer (b. 1946)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Stockton_Rush\" title=\"<PERSON> Rush\"><PERSON></a>, American businessman, CEO and founder of <a href=\"https://wikipedia.org/wiki/OceanGate\" title=\"OceanGate\">OceanGate</a> (b. 1962)</li>\n</ul>", "no_year_html": "Notable victims of the <a href=\"https://wikipedia.org/wiki/Titan_submersible_implosion\" title=\"Titan submersible implosion\"><i>Titan</i> submersible implosion</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani-British businessman (b. 1975)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British businessman (b. 1964)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">navy</a> commander and explorer (b. 1946)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Stockton_Rush\" title=\"<PERSON> Rush\"><PERSON></a>, American businessman, CEO and founder of <a href=\"https://wikipedia.org/wiki/OceanGate\" title=\"OceanGate\">OceanGate</a> (b. 1962)</li>\n</ul>", "links": [{"title": "Titan submersible implosion", "link": "https://wikipedia.org/wiki/Titan_submersible_implosion"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "French Navy", "link": "https://wikipedia.org/wiki/French_Navy"}, {"title": "Stockton Rush", "link": "https://wikipedia.org/wiki/Stockton_Rush"}, {"title": "OceanGate", "link": "https://wikipedia.org/wiki/OceanGate"}]}, {"year": "<PERSON><PERSON><PERSON>, Pakistani-British businessman (b. 1975)", "text": null, "html": "<PERSON><PERSON><PERSON>, Pakistani-British businessman (b. 1975) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani-British businessman (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani-British businessman (b. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, British businessman (b. 1964)", "text": null, "html": "<PERSON><PERSON>, British businessman (b. 1964) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British businessman (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British businessman (b. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, French navy commander and explorer (b. 1946)", "text": null, "html": "<PERSON><PERSON><PERSON>, French navy commander and explorer (b. 1946) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">navy</a> commander and explorer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">navy</a> commander and explorer (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>"}, {"title": "French Navy", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/French_Navy"}]}, {"year": "<PERSON>, American businessman, CEO and founder of OceanGate (b. 1962)", "text": null, "html": "<PERSON>, American businessman, CEO and founder of OceanGate (b. 1962) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Stockton_Rush\" title=\"Stockton Rush\"><PERSON> Rush</a>, American businessman, CEO and founder of <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/OceanGate\" title=\"OceanGate\">OceanGate</a> (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Stockton_Rush\" title=\"Stockton Rush\">Stockton Rush</a>, American businessman, CEO and founder of <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/OceanGate\" title=\"OceanGate\">OceanGate</a> (b. 1962)", "links": [{"title": "Stockton Rush", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Stockton_Rush"}, {"title": "OceanGate", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/OceanGate"}]}, {"year": "2024", "text": "<PERSON>, American musician (b. 1953)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chance\"><PERSON></a>, American musician (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, French actress (b. 1932)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Aim%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Aim%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anouk_Aim%C3%A9e"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Filipino basketball player (b. 1946)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Yoyong_Martirez\" title=\"<PERSON>yong Martirez\"><PERSON><PERSON><PERSON></a>, Filipino basketball player (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yoyong_<PERSON>irez\" title=\"<PERSON>yong Martirez\"><PERSON><PERSON><PERSON></a>, Filipino basketball player (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yoyong_Martirez"}]}, {"year": "2024", "text": "<PERSON>, American baseball player (b. 1931)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}]}}