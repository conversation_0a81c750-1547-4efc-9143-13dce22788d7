{"date": "January 23", "url": "https://wikipedia.org/wiki/January_23", "data": {"Events": [{"year": "393", "text": "Roman emperor <PERSON><PERSON><PERSON> I proclaims his eight-year-old son <PERSON><PERSON> co-emperor.", "html": "393 - <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/Theo<PERSON><PERSON>_I\" title=\"<PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON> I</a> proclaims his eight-year-old son <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a> co-emperor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/Theo<PERSON><PERSON>_I\" title=\"<PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON> I</a> proclaims his eight-year-old son <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a> co-emperor.", "links": [{"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}, {"title": "<PERSON><PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)"}]}, {"year": "971", "text": "Using crossbows, Song dynasty troops soundly defeat a war elephant corps of the Southern Han at Shao.", "html": "971 - Using <a href=\"https://wikipedia.org/wiki/Crossbow\" title=\"Crossbow\">crossbows</a>, <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a> troops soundly <a href=\"https://wikipedia.org/wiki/History_of_the_Song_dynasty#Founding_of_the_Song\" title=\"History of the Song dynasty\">defeat</a> a <a href=\"https://wikipedia.org/wiki/War_elephant\" title=\"War elephant\">war elephant</a> corps of the <a href=\"https://wikipedia.org/wiki/Southern_Han\" title=\"Southern Han\">Southern Han</a> at Shao.", "no_year_html": "Using <a href=\"https://wikipedia.org/wiki/Crossbow\" title=\"Crossbow\">crossbows</a>, <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a> troops soundly <a href=\"https://wikipedia.org/wiki/History_of_the_Song_dynasty#Founding_of_the_Song\" title=\"History of the Song dynasty\">defeat</a> a <a href=\"https://wikipedia.org/wiki/War_elephant\" title=\"War elephant\">war elephant</a> corps of the <a href=\"https://wikipedia.org/wiki/Southern_Han\" title=\"Southern Han\">Southern Han</a> at Shao.", "links": [{"title": "Crossbow", "link": "https://wikipedia.org/wiki/Crossbow"}, {"title": "Song dynasty", "link": "https://wikipedia.org/wiki/Song_dynasty"}, {"title": "History of the Song dynasty", "link": "https://wikipedia.org/wiki/History_of_the_Song_dynasty#Founding_of_the_Song"}, {"title": "War elephant", "link": "https://wikipedia.org/wiki/War_elephant"}, {"title": "Southern Han", "link": "https://wikipedia.org/wiki/Southern_Han"}]}, {"year": "1229", "text": "The episcopal seat is moved from Nousiainen to Koroinen (located near the current centre of Turku) by the permission of Pope <PERSON>. The date is starting to be considered as the founding of Turku.", "html": "1229 - The episcopal seat is moved from <a href=\"https://wikipedia.org/wiki/Nousiainen\" title=\"<PERSON>usiainen\"><PERSON>us<PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Koroinen\" title=\"Koroinen\">Koroinen</a> (located near the current centre of <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a>) by the permission of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> IX\"><PERSON> IX</a>. The date is starting to be considered as the founding of Turku.", "no_year_html": "The episcopal seat is moved from <a href=\"https://wikipedia.org/wiki/Nousiainen\" title=\"Nousiainen\"><PERSON><PERSON><PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Koroinen\" title=\"Koroinen\">Koroinen</a> (located near the current centre of <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a>) by the permission of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Gregory IX\"><PERSON> IX</a>. The date is starting to be considered as the founding of Turku.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>usiainen"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Turku", "link": "https://wikipedia.org/wiki/Turku"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1264", "text": "In the conflict between King <PERSON> of England and his rebellious barons led by <PERSON>, King <PERSON> of France issues the Mise of Amiens, a one-sided decision in favour of <PERSON> that later leads to the Second Barons' War.", "html": "1264 - In the conflict between King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> III of England</a> and his rebellious barons led by <a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Earl_<PERSON>_Leicester\" title=\"<PERSON>, 6th Earl of <PERSON>\"><PERSON></a>, King <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a> issues the <a href=\"https://wikipedia.org/wiki/Mise_of_Amiens\" title=\"Mise of Amiens\"><PERSON><PERSON> of Amiens</a>, a one-sided decision in favour of <PERSON> that later leads to the <a href=\"https://wikipedia.org/wiki/Second_Barons%27_War\" title=\"Second Barons' War\">Second Barons' War</a>.", "no_year_html": "In the conflict between King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> and his rebellious barons led by <a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Earl_<PERSON>_Leicester\" title=\"<PERSON>, 6th Earl of <PERSON>\"><PERSON></a>, King <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> issues the <a href=\"https://wikipedia.org/wiki/Mise_of_Amiens\" title=\"Mise of Amiens\"><PERSON><PERSON> of Amiens</a>, a one-sided decision in favour of <PERSON> that later leads to the <a href=\"https://wikipedia.org/wiki/Second_Barons%27_War\" title=\"Second Barons' War\">Second Barons' War</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON>, 6th Earl of Leicester", "link": "https://wikipedia.org/wiki/<PERSON>,_6th_Earl_of_Leicester"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IX_of_France"}, {"title": "<PERSON><PERSON> of Amiens", "link": "https://wikipedia.org/wiki/Mise_of_Amiens"}, {"title": "Second Barons' War", "link": "https://wikipedia.org/wiki/Second_Barons%27_War"}]}, {"year": "1368", "text": "<PERSON> proclaims himself the Hongwu Emperor, beginning the Ming dynasty.", "html": "1368 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> proclaims himself the <a href=\"https://wikipedia.org/wiki/Hongwu_Emperor\" title=\"Hongwu Emperor\">Hong<PERSON> Emperor</a>, beginning the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> proclaims himself the <a href=\"https://wikipedia.org/wiki/Hongwu_Emperor\" title=\"Hongwu Emperor\">Hong<PERSON> Emperor</a>, beginning the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Zhu_<PERSON>"}, {"title": "Hongwu Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}]}, {"year": "1546", "text": "Having published nothing for eleven years, <PERSON> publishes the Tiers Livre, his sequel to Gargantua and Pantagruel.", "html": "1546 - Having published nothing for eleven years, <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON>ois_Rabelais\" title=\"<PERSON>\"><PERSON></a> publishes the <i>Tiers Livre</i>, his sequel to <i><a href=\"https://wikipedia.org/wiki/Gargantua_and_Pantagruel\" title=\"Gargantua and Pantagruel\">Gargantua and Pantagruel</a></i>.", "no_year_html": "Having published nothing for eleven years, <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_Rabelais\" title=\"<PERSON>\"><PERSON></a> publishes the <i>Tiers Livre</i>, his sequel to <i><a href=\"https://wikipedia.org/wiki/Gargantua_and_Pantagruel\" title=\"Gargantua and Pantagruel\">Gargantua and Pantagruel</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "Gargantua and Pantagruel", "link": "https://wikipedia.org/wiki/Gargantua_and_Pantagruel"}]}, {"year": "1556", "text": "The deadliest earthquake in history, the Shaanxi earthquake, hits Shaanxi province, China. The death toll may have been as high as 830,000.", "html": "1556 - The deadliest <a href=\"https://wikipedia.org/wiki/Earthquake\" title=\"Earthquake\">earthquake</a> in history, the <a href=\"https://wikipedia.org/wiki/1556_Shaanxi_earthquake\" title=\"1556 Shaanxi earthquake\">Shaanxi earthquake</a>, hits <a href=\"https://wikipedia.org/wiki/Shaanxi\" title=\"Shaanxi\">Shaanxi</a> province, China. The death toll may have been as high as 830,000.", "no_year_html": "The deadliest <a href=\"https://wikipedia.org/wiki/Earthquake\" title=\"Earthquake\">earthquake</a> in history, the <a href=\"https://wikipedia.org/wiki/1556_Shaanxi_earthquake\" title=\"1556 Shaanxi earthquake\">Shaanxi earthquake</a>, hits <a href=\"https://wikipedia.org/wiki/Shaanxi\" title=\"Shaanxi\">Shaanxi</a> province, China. The death toll may have been as high as 830,000.", "links": [{"title": "Earthquake", "link": "https://wikipedia.org/wiki/Earthquake"}, {"title": "1556 Shaanxi earthquake", "link": "https://wikipedia.org/wiki/1556_Shaanxi_earthquake"}, {"title": "Shaanxi", "link": "https://wikipedia.org/wiki/Shaanxi"}]}, {"year": "1565", "text": "The Deccan Sultanates defeat <PERSON> of the Vijayanagara Empire at the Battle of Talikota, resulting in over 100,000 casualties and the destruction of the capital Vijayanagara.", "html": "1565 - The <a href=\"https://wikipedia.org/wiki/Deccan_Sultanates\" class=\"mw-redirect\" title=\"Deccan Sultanates\">Deccan Sultanates</a> defeat <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Vijayanagara_Empire\" title=\"Vijayanagara Empire\">Vijayanagara Empire</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Talikota\" title=\"Battle of Talikota\">Battle of Talikota</a>, resulting in over 100,000 casualties and the destruction of the capital <a href=\"https://wikipedia.org/wiki/Vijayanagara\" title=\"Vijayanagara\">Vijayanagara</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Deccan_Sultanates\" class=\"mw-redirect\" title=\"Deccan Sultanates\">Deccan Sultanates</a> defeat <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Vijayanagara_Empire\" title=\"Vijayanagara Empire\">Vijayanagara Empire</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Talikota\" title=\"Battle of Talikota\">Battle of Talikota</a>, resulting in over 100,000 casualties and the destruction of the capital <a href=\"https://wikipedia.org/wiki/Vijayanagara\" title=\"Vijayanagara\">Vijayanagara</a>.", "links": [{"title": "Deccan Sultanates", "link": "https://wikipedia.org/wiki/Deccan_Sultanates"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vijayanagara Empire", "link": "https://wikipedia.org/wiki/Vijayanagara_Empire"}, {"title": "Battle of Talikota", "link": "https://wikipedia.org/wiki/Battle_of_Talikota"}, {"title": "Vijayanagara", "link": "https://wikipedia.org/wiki/Vijayanagara"}]}, {"year": "1570", "text": "<PERSON>, 1st Earl of Moray, regent for the infant King <PERSON> of Scotland, is assassinated by firearm, the first recorded instance of such.", "html": "1570 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Moray\" title=\"<PERSON>, 1st Earl of Moray\"><PERSON>, 1st Earl of Moray</a>, <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> for the infant King <a href=\"https://wikipedia.org/wiki/James_VI_of_Scotland\" class=\"mw-redirect\" title=\"James VI of Scotland\"><PERSON> of Scotland</a>, is assassinated by firearm, the first recorded instance of such.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Moray\" title=\"<PERSON>, 1st Earl of Moray\"><PERSON>, 1st Earl of Moray</a>, <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> for the infant King <a href=\"https://wikipedia.org/wiki/James_VI_of_Scotland\" class=\"mw-redirect\" title=\"James VI of Scotland\"><PERSON> of Scotland</a>, is assassinated by firearm, the first recorded instance of such.", "links": [{"title": "<PERSON>, 1st Earl of Moray", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Moray"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regent"}, {"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/James_VI_of_Scotland"}]}, {"year": "1571", "text": "The Royal Exchange opens in London.", "html": "1571 - The <a href=\"https://wikipedia.org/wiki/Royal_Exchange,_London\" title=\"Royal Exchange, London\">Royal Exchange</a> opens in London.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Exchange,_London\" title=\"Royal Exchange, London\">Royal Exchange</a> opens in London.", "links": [{"title": "Royal Exchange, London", "link": "https://wikipedia.org/wiki/Royal_Exchange,_London"}]}, {"year": "1579", "text": "The Union of Utrecht forms a Protestant republic in the Netherlands.", "html": "1579 - The <a href=\"https://wikipedia.org/wiki/Union_of_Utrecht\" title=\"Union of Utrecht\">Union of Utrecht</a> forms a <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestant</a> republic in the Netherlands.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Union_of_Utrecht\" title=\"Union of Utrecht\">Union of Utrecht</a> forms a <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestant</a> republic in the Netherlands.", "links": [{"title": "Union of Utrecht", "link": "https://wikipedia.org/wiki/Union_of_Utrecht"}, {"title": "Protestantism", "link": "https://wikipedia.org/wiki/Protestantism"}]}, {"year": "1656", "text": "<PERSON><PERSON><PERSON> publishes the first of his Lettres provinciales.", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> publishes the first of his <i><a href=\"https://wikipedia.org/wiki/Lettres_provinciales\" title=\"Lettres provinciales\">Lettres provinciales</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> publishes the first of his <i><a href=\"https://wikipedia.org/wiki/Lettres_provinciales\" title=\"Lettres provinciales\">Lettres provinciales</a></i>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Lettres provinciales", "link": "https://wikipedia.org/wiki/Lettres_provinciales"}]}, {"year": "1719", "text": "The Principality of Liechtenstein is created within the Holy Roman Empire.", "html": "1719 - The <a href=\"https://wikipedia.org/wiki/Principality\" title=\"Principality\">Principality</a> of <a href=\"https://wikipedia.org/wiki/Liechtenstein\" title=\"Liechtenstein\">Liechtenstein</a> is created within the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Principality\" title=\"Principality\">Principality</a> of <a href=\"https://wikipedia.org/wiki/Liechtenstein\" title=\"Liechtenstein\">Liechtenstein</a> is created within the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a>.", "links": [{"title": "Principality", "link": "https://wikipedia.org/wiki/Principality"}, {"title": "Liechtenstein", "link": "https://wikipedia.org/wiki/Liechtenstein"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}]}, {"year": "1755", "text": "Moscow University is established (12 January 1755 O.S.).", "html": "1755 - <a href=\"https://wikipedia.org/wiki/Moscow_State_University\" title=\"Moscow State University\">Moscow University</a> is established (12 January 1755 <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moscow_State_University\" title=\"Moscow State University\">Moscow University</a> is established (12 January 1755 <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>).", "links": [{"title": "Moscow State University", "link": "https://wikipedia.org/wiki/Moscow_State_University"}, {"title": "Old Style and New Style dates", "link": "https://wikipedia.org/wiki/Old_Style_and_New_Style_dates"}]}, {"year": "1789", "text": "Georgetown College, the first Catholic university in the United States, is founded in Georgetown, Maryland (now a part of Washington, D.C.) when Bishop <PERSON>, Rev. <PERSON>, and Rev. <PERSON> purchase land for the proposed academy for the education of youth.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/Georgetown_University\" title=\"Georgetown University\">Georgetown College</a>, the first <a href=\"https://wikipedia.org/wiki/Catholic_university\" class=\"mw-redirect\" title=\"Catholic university\">Catholic university</a> in the United States, is founded in <a href=\"https://wikipedia.org/wiki/Georgetown_(Washington,_D.C.)\" title=\"Georgetown (Washington, D.C.)\">Georgetown, Maryland</a> (now a part of Washington, D.C.) when Bishop <a href=\"https://wikipedia.org/wiki/<PERSON>(archbishop_of_Baltimore)\" class=\"mw-redirect\" title=\"<PERSON> (archbishop of Baltimore)\"><PERSON></a>, Rev. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and Rev. <PERSON> purchase land for the proposed academy for the education of youth.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgetown_University\" title=\"Georgetown University\">Georgetown College</a>, the first <a href=\"https://wikipedia.org/wiki/Catholic_university\" class=\"mw-redirect\" title=\"Catholic university\">Catholic university</a> in the United States, is founded in <a href=\"https://wikipedia.org/wiki/Georgetown_(Washington,_D.C.)\" title=\"Georgetown (Washington, D.C.)\">Georgetown, Maryland</a> (now a part of Washington, D.C.) when Bishop <a href=\"https://wikipedia.org/wiki/<PERSON>(archbishop_of_Baltimore)\" class=\"mw-redirect\" title=\"<PERSON> (archbishop of Baltimore)\"><PERSON></a>, Rev. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and Rev. <PERSON> purchase land for the proposed academy for the education of youth.", "links": [{"title": "Georgetown University", "link": "https://wikipedia.org/wiki/Georgetown_University"}, {"title": "Catholic university", "link": "https://wikipedia.org/wiki/Catholic_university"}, {"title": "Georgetown (Washington, D.C.)", "link": "https://wikipedia.org/wiki/Georgetown_(Washington,_D.C.)"}, {"title": "<PERSON> (archbishop of Baltimore)", "link": "https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Baltimore)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1793", "text": "Second Partition of Poland.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/Second_Partition_of_Poland\" title=\"Second Partition of Poland\">Second Partition of Poland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Partition_of_Poland\" title=\"Second Partition of Poland\">Second Partition of Poland</a>.", "links": [{"title": "Second Partition of Poland", "link": "https://wikipedia.org/wiki/Second_Partition_of_Poland"}]}, {"year": "1795", "text": "After crossing the frozen Zuiderzee, the French cavalry captured 14 Dutch ships and 850 guns, in a rare occurrence of surrender of naval vessels to land forces.", "html": "1795 - After crossing the frozen <a href=\"https://wikipedia.org/wiki/Zuiderzee\" title=\"Zuiderzee\">Zuiderzee</a>, the French cavalry <a href=\"https://wikipedia.org/wiki/Capture_of_the_Dutch_fleet_at_Den_Helder\" title=\"Capture of the Dutch fleet at Den Helder\">captured 14 Dutch ships and 850 guns</a>, in a rare occurrence of surrender of naval vessels to land forces.", "no_year_html": "After crossing the frozen <a href=\"https://wikipedia.org/wiki/Zuiderzee\" title=\"Zuiderzee\">Zuiderzee</a>, the French cavalry <a href=\"https://wikipedia.org/wiki/Capture_of_the_Dutch_fleet_at_Den_Helder\" title=\"Capture of the Dutch fleet at Den Helder\">captured 14 Dutch ships and 850 guns</a>, in a rare occurrence of surrender of naval vessels to land forces.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zuiderzee"}, {"title": "Capture of the Dutch fleet at Den Helder", "link": "https://wikipedia.org/wiki/Capture_of_the_Dutch_fleet_at_Den_Helder"}]}, {"year": "1846", "text": "Slavery in Tunisia is abolished.", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Slavery_in_Tunisia\" title=\"Slavery in Tunisia\">Slavery in Tunisia</a> is abolished.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slavery_in_Tunisia\" title=\"Slavery in Tunisia\">Slavery in Tunisia</a> is abolished.", "links": [{"title": "Slavery in Tunisia", "link": "https://wikipedia.org/wiki/Slavery_in_Tunisia"}]}, {"year": "1849", "text": "<PERSON> is awarded her M.D. by the Geneva Medical College of Geneva, New York, becoming the United States' first female doctor.", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded her M.D. by the <a href=\"https://wikipedia.org/wiki/Geneva_Medical_College\" title=\"Geneva Medical College\">Geneva Medical College</a> of <a href=\"https://wikipedia.org/wiki/Geneva,_New_York\" title=\"Geneva, New York\">Geneva, New York</a>, becoming the United States' first female doctor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded her M.D. by the <a href=\"https://wikipedia.org/wiki/Geneva_Medical_College\" title=\"Geneva Medical College\">Geneva Medical College</a> of <a href=\"https://wikipedia.org/wiki/Geneva,_New_York\" title=\"Geneva, New York\">Geneva, New York</a>, becoming the United States' first female doctor.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Geneva Medical College", "link": "https://wikipedia.org/wiki/Geneva_Medical_College"}, {"title": "Geneva, New York", "link": "https://wikipedia.org/wiki/Geneva,_New_York"}]}, {"year": "1870", "text": "In Montana, U.S. cavalrymen kill 173 Native Americans, mostly women and children, in what becomes known as the Marias Massacre.", "html": "1870 - In <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a>, U.S. cavalrymen kill 173 <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native Americans</a>, mostly women and children, in what becomes known as the <a href=\"https://wikipedia.org/wiki/Marias_Massacre\" title=\"Marias Massacre\">Marias Massacre</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a>, U.S. cavalrymen kill 173 <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native Americans</a>, mostly women and children, in what becomes known as the <a href=\"https://wikipedia.org/wiki/Marias_Massacre\" title=\"Marias Massacre\">Marias Massacre</a>.", "links": [{"title": "Montana", "link": "https://wikipedia.org/wiki/Montana"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}, {"title": "Marias Massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Massacre"}]}, {"year": "1879", "text": "Anglo-Zulu War: The Battle of Rorke's Drift ends.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Rorke%27s_Drift\" title=\"Battle of Rorke's Drift\">Battle of Rorke's Drift</a> ends.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Rorke%27s_Drift\" title=\"Battle of Rorke's Drift\">Battle of Rorke's Drift</a> ends.", "links": [{"title": "Anglo-Zulu War", "link": "https://wikipedia.org/wiki/Anglo-Zulu_War"}, {"title": "Battle of Rorke's Drift", "link": "https://wikipedia.org/wiki/Battle_of_Rorke%27s_Drift"}]}, {"year": "1899", "text": "The Malolos Constitution is inaugurated, establishing the First Philippine Republic. <PERSON> is sworn in as its first president.", "html": "1899 - The <a href=\"https://wikipedia.org/wiki/Malolos_Constitution\" title=\"Malolos Constitution\">Malolos Constitution</a> is inaugurated, establishing the <a href=\"https://wikipedia.org/wiki/First_Philippine_Republic\" title=\"First Philippine Republic\">First Philippine Republic</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as its <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_the_Philippines\" title=\"List of presidents of the Philippines\">first</a> <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">president</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Malolos_Constitution\" title=\"Malolos Constitution\">Malolos Constitution</a> is inaugurated, establishing the <a href=\"https://wikipedia.org/wiki/First_Philippine_Republic\" title=\"First Philippine Republic\">First Philippine Republic</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as its <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_the_Philippines\" title=\"List of presidents of the Philippines\">first</a> <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">president</a>.", "links": [{"title": "Malolos Constitution", "link": "https://wikipedia.org/wiki/Malolos_Constitution"}, {"title": "First Philippine Republic", "link": "https://wikipedia.org/wiki/First_Philippine_Republic"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of presidents of the Philippines", "link": "https://wikipedia.org/wiki/List_of_presidents_of_the_Philippines"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1900", "text": "Second Boer War: The Battle of Spion Kop between the forces of the South African Republic and the Orange Free State and British forces ends in a British defeat.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Spion_Kop\" title=\"Battle of Spion Kop\">Battle of Spion Kop</a> between the forces of the <a href=\"https://wikipedia.org/wiki/South_African_Republic\" title=\"South African Republic\">South African Republic</a> and the <a href=\"https://wikipedia.org/wiki/Orange_Free_State\" title=\"Orange Free State\">Orange Free State</a> and British forces ends in a British defeat.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Spion_Kop\" title=\"Battle of Spion Kop\">Battle of Spion Kop</a> between the forces of the <a href=\"https://wikipedia.org/wiki/South_African_Republic\" title=\"South African Republic\">South African Republic</a> and the <a href=\"https://wikipedia.org/wiki/Orange_Free_State\" title=\"Orange Free State\">Orange Free State</a> and British forces ends in a British defeat.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Battle of Spion Ko<PERSON>", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON>_<PERSON>p"}, {"title": "South African Republic", "link": "https://wikipedia.org/wiki/South_African_Republic"}, {"title": "Orange Free State", "link": "https://wikipedia.org/wiki/Orange_Free_State"}]}, {"year": "1904", "text": "Ålesund Fire: The Norwegian coastal town Ålesund is devastated by fire, leaving 10,000 people homeless and one person dead. Kaiser <PERSON> funds the rebuilding of the town in Jugendstil style.", "html": "1904 - <a href=\"https://wikipedia.org/wiki/%C3%85lesund_Fire\" class=\"mw-redirect\" title=\"Ålesund Fire\">Ålesund Fire</a>: The Norwegian coastal town <a href=\"https://wikipedia.org/wiki/%C3%85lesund\" title=\"Ålesund\">Ålesund</a> is devastated by fire, leaving 10,000 people homeless and one person dead. <a href=\"https://wikipedia.org/wiki/Kaiser\" title=\"Kaiser\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Wilhelm_II,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\"><PERSON> II</a> funds the rebuilding of the town in <a href=\"https://wikipedia.org/wiki/Art_Nouveau\" title=\"Art Nouveau\">Jugendstil</a> style.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%85lesund_Fire\" class=\"mw-redirect\" title=\"Ålesund Fire\">Ålesund Fire</a>: The Norwegian coastal town <a href=\"https://wikipedia.org/wiki/%C3%85lesund\" title=\"Ålesund\">Ålesund</a> is devastated by fire, leaving 10,000 people homeless and one person dead. <a href=\"https://wikipedia.org/wiki/Kaiser\" title=\"Kaiser\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Wilhelm_II,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\"><PERSON></a> funds the rebuilding of the town in <a href=\"https://wikipedia.org/wiki/Art_Nouveau\" title=\"Art Nouveau\">Jugendstil</a> style.", "links": [{"title": "Ålesund Fire", "link": "https://wikipedia.org/wiki/%C3%85lesund_Fire"}, {"title": "Ålesund", "link": "https://wikipedia.org/wiki/%C3%85lesund"}, {"title": "Kaiser", "link": "https://wikipedia.org/wiki/Kaiser"}, {"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}, {"title": "Art Nouveau", "link": "https://wikipedia.org/wiki/Art_Nouveau"}]}, {"year": "1909", "text": "RMS Republic, a passenger ship of the White Star Line, becomes the first ship to use the CQD distress signal after colliding with another ship, the SS Florida, off the Massachusetts coastline, an event that kills six people. The Republic sinks the next day.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/RMS_Republic_(1903)\" title=\"RMS Republic (1903)\">RMS <i>Republic</i></a>, a passenger ship of the <a href=\"https://wikipedia.org/wiki/White_Star_Line\" title=\"White Star Line\">White Star Line</a>, becomes the first ship to use the <a href=\"https://wikipedia.org/wiki/CQD\" title=\"CQD\">CQD</a> distress signal after colliding with another ship, the <a href=\"https://wikipedia.org/wiki/SS_Florida_(1905)\" title=\"SS Florida (1905)\">SS <i>Florida</i></a>, off the Massachusetts coastline, an event that kills six people. The <i>Republic</i> sinks the next day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/RMS_Republic_(1903)\" title=\"RMS Republic (1903)\">RMS <i>Republic</i></a>, a passenger ship of the <a href=\"https://wikipedia.org/wiki/White_Star_Line\" title=\"White Star Line\">White Star Line</a>, becomes the first ship to use the <a href=\"https://wikipedia.org/wiki/CQD\" title=\"CQD\">CQD</a> distress signal after colliding with another ship, the <a href=\"https://wikipedia.org/wiki/SS_Florida_(1905)\" title=\"SS Florida (1905)\">SS <i>Florida</i></a>, off the Massachusetts coastline, an event that kills six people. The <i>Republic</i> sinks the next day.", "links": [{"title": "RMS Republic (1903)", "link": "https://wikipedia.org/wiki/RMS_Republic_(1903)"}, {"title": "White Star Line", "link": "https://wikipedia.org/wiki/White_Star_Line"}, {"title": "CQD", "link": "https://wikipedia.org/wiki/CQD"}, {"title": "SS Florida (1905)", "link": "https://wikipedia.org/wiki/SS_Florida_(1905)"}]}, {"year": "1912", "text": "The International Opium Convention is signed at The Hague.", "html": "1912 - The <a href=\"https://wikipedia.org/wiki/International_Opium_Convention\" title=\"International Opium Convention\">International Opium Convention</a> is signed at <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Opium_Convention\" title=\"International Opium Convention\">International Opium Convention</a> is signed at <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a>.", "links": [{"title": "International Opium Convention", "link": "https://wikipedia.org/wiki/International_Opium_Convention"}, {"title": "The Hague", "link": "https://wikipedia.org/wiki/The_Hague"}]}, {"year": "1919", "text": "The First Regional Congress of Peasants, Workers and Insurgents is held by the Makhnovshchina at Velykomykhailivka.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Regional_Congress_of_Peasants,_Workers_and_Insurgents#First_Congress_(January_1919)\" title=\"Regional Congress of Peasants, Workers and Insurgents\">First Regional Congress of Peasants, Workers and Insurgents</a> is held by the <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a> at <a href=\"https://wikipedia.org/wiki/Velykomykhailivka\" title=\"Velykomykhailivka\">Velykomykhailivka</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Regional_Congress_of_Peasants,_Workers_and_Insurgents#First_Congress_(January_1919)\" title=\"Regional Congress of Peasants, Workers and Insurgents\">First Regional Congress of Peasants, Workers and Insurgents</a> is held by the <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a> at <a href=\"https://wikipedia.org/wiki/Velykomykhailivka\" title=\"Velykomykhailivka\">Velykomykhailivka</a>.", "links": [{"title": "Regional Congress of Peasants, Workers and Insurgents", "link": "https://wikipedia.org/wiki/Regional_Congress_of_Peasants,_Workers_and_Insurgents#First_Congress_(January_1919)"}, {"title": "Makhnovshchina", "link": "https://wikipedia.org/wiki/Makhnovshchina"}, {"title": "Velykomykhailivka", "link": "https://wikipedia.org/wiki/Velykomykhailivka"}]}, {"year": "1920", "text": "The Netherlands refuses to surrender the exiled Kaiser <PERSON> of Germany to the Allies.", "html": "1920 - The Netherlands refuses to surrender the exiled <a href=\"https://wikipedia.org/wiki/Wilhelm_II,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\">Kaiser Wilhelm II</a> of Germany to the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allies</a>.", "no_year_html": "The Netherlands refuses to surrender the exiled <a href=\"https://wikipedia.org/wiki/Wilhelm_II,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\">Kaiser Wilhelm <PERSON></a> of Germany to the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allies</a>.", "links": [{"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}]}, {"year": "1937", "text": "The trial of the anti-Soviet Trotskyist center sees seventeen mid-level Communists accused of sympathizing with <PERSON> and plotting to overthrow <PERSON>'s regime.", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/Moscow_Trials\" class=\"mw-redirect\" title=\"Moscow Trials\">trial of the anti-Soviet Trotskyist center</a> sees seventeen mid-level Communists accused of sympathizing with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and plotting to overthrow <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s regime.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Moscow_Trials\" class=\"mw-redirect\" title=\"Moscow Trials\">trial of the anti-Soviet Trotskyist center</a> sees seventeen mid-level Communists accused of sympathizing with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and plotting to overthrow <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s regime.", "links": [{"title": "Moscow Trials", "link": "https://wikipedia.org/wiki/Moscow_Trials"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON> testifies before the U.S. Congress and recommends that the United States negotiate a neutrality pact with <PERSON>.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> testifies before the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> and recommends that the United States negotiate a <a href=\"https://wikipedia.org/wiki/Neutrality_pact\" class=\"mw-redirect\" title=\"Neutrality pact\">neutrality pact</a> with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Adolf <PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> testifies before the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> and recommends that the United States negotiate a <a href=\"https://wikipedia.org/wiki/Neutrality_pact\" class=\"mw-redirect\" title=\"Neutrality pact\">neutrality pact</a> with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Neutrality pact", "link": "https://wikipedia.org/wiki/Neutrality_pact"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "World War II: The Battle of Rabaul commences Japan's invasion of Australia's Territory of New Guinea.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Rabaul_(1942)\" title=\"Battle of Rabaul (1942)\">Battle of Rabaul</a> commences Japan's <a href=\"https://wikipedia.org/wiki/New_Guinea_campaign\" title=\"New Guinea campaign\">invasion</a> of Australia's <a href=\"https://wikipedia.org/wiki/Territory_of_New_Guinea\" title=\"Territory of New Guinea\">Territory of New Guinea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Rabaul_(1942)\" title=\"Battle of Rabaul (1942)\">Battle of Rabaul</a> commences Japan's <a href=\"https://wikipedia.org/wiki/New_Guinea_campaign\" title=\"New Guinea campaign\">invasion</a> of Australia's <a href=\"https://wikipedia.org/wiki/Territory_of_New_Guinea\" title=\"Territory of New Guinea\">Territory of New Guinea</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Rabaul (1942)", "link": "https://wikipedia.org/wiki/Battle_of_Rabaul_(1942)"}, {"title": "New Guinea campaign", "link": "https://wikipedia.org/wiki/New_Guinea_campaign"}, {"title": "Territory of New Guinea", "link": "https://wikipedia.org/wiki/Territory_of_New_Guinea"}]}, {"year": "1943", "text": "World War II: Troops of the British Eighth Army capture Tripoli in Libya from the German-Italian Panzer Army.", "html": "1943 - World War II: Troops of the British <a href=\"https://wikipedia.org/wiki/Eighth_Army_(United_Kingdom)\" title=\"Eighth Army (United Kingdom)\">Eighth Army</a> capture <a href=\"https://wikipedia.org/wiki/Tripoli,_Libya\" title=\"Tripoli, Libya\">Tripoli</a> in <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a> from the <a href=\"https://wikipedia.org/wiki/German%E2%80%93Italian_Panzer_Army\" class=\"mw-redirect\" title=\"German-Italian Panzer Army\">German-Italian Panzer Army</a>.", "no_year_html": "World War II: Troops of the British <a href=\"https://wikipedia.org/wiki/Eighth_Army_(United_Kingdom)\" title=\"Eighth Army (United Kingdom)\">Eighth Army</a> capture <a href=\"https://wikipedia.org/wiki/Tripoli,_Libya\" title=\"Tripoli, Libya\">Tripoli</a> in <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a> from the <a href=\"https://wikipedia.org/wiki/German%E2%80%93Italian_Panzer_Army\" class=\"mw-redirect\" title=\"German-Italian Panzer Army\">German-Italian Panzer Army</a>.", "links": [{"title": "Eighth Army (United Kingdom)", "link": "https://wikipedia.org/wiki/Eighth_Army_(United_Kingdom)"}, {"title": "Tripoli, Libya", "link": "https://wikipedia.org/wiki/Tripoli,_Libya"}, {"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}, {"title": "German-Italian Panzer Army", "link": "https://wikipedia.org/wiki/German%E2%80%93Italian_Panzer_Army"}]}, {"year": "1945", "text": "World War II: German admiral <PERSON> launches Operation Hannibal.", "html": "1945 - World War II: German admiral <a href=\"https://wikipedia.org/wiki/Karl_D%C3%B6<PERSON>\" title=\"<PERSON>\"><PERSON></a> launches <a href=\"https://wikipedia.org/wiki/Operation_Hannibal\" title=\"Operation Hannibal\">Operation Hannibal</a>.", "no_year_html": "World War II: German admiral <a href=\"https://wikipedia.org/wiki/Karl_D%C3%B6<PERSON>\" title=\"<PERSON>\"><PERSON></a> launches <a href=\"https://wikipedia.org/wiki/Operation_Hannibal\" title=\"Operation Hannibal\">Operation Hannibal</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_D%C3%B6nitz"}, {"title": "Operation Hannibal", "link": "https://wikipedia.org/wiki/Operation_Hannibal"}]}, {"year": "1950", "text": "The Knesset resolves that Jerusalem is the capital of Israel.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a> resolves that <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a> is the capital of Israel.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a> resolves that <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a> is the capital of Israel.", "links": [{"title": "Knesset", "link": "https://wikipedia.org/wiki/Knesset"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "1957", "text": "American inventor <PERSON> sells the rights to his flying disc to the Wham-O toy company, which later renames it the \"Frisbee\".", "html": "1957 - American inventor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sells the rights to his <a href=\"https://wikipedia.org/wiki/Frisbee\" title=\"Frisbee\">flying disc</a> to the <a href=\"https://wikipedia.org/wiki/Wham-O\" title=\"Wham-O\">Wham-O</a> toy company, which later renames it the \"Frisbee\".", "no_year_html": "American inventor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sells the rights to his <a href=\"https://wikipedia.org/wiki/Frisbee\" title=\"Frisbee\">flying disc</a> to the <a href=\"https://wikipedia.org/wiki/Wham-O\" title=\"Wham-O\">Wham-O</a> toy company, which later renames it the \"Frisbee\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Frisbee", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Wham-<PERSON>", "link": "https://wikipedia.org/wiki/Wham-O"}]}, {"year": "1958", "text": "After a general uprising and rioting in the streets, President <PERSON> leaves Venezuela.", "html": "1958 - After a <a href=\"https://wikipedia.org/wiki/1958_Venezuelan_coup_d%27%C3%A9tat\" title=\"1958 Venezuelan coup d'état\">general uprising and rioting in the streets</a>, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President</a> <a href=\"https://wikipedia.org/wiki/Marcos_P%C3%A9rez_Jim%C3%A9nez\" title=\"<PERSON>\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>.", "no_year_html": "After a <a href=\"https://wikipedia.org/wiki/1958_Venezuelan_coup_d%27%C3%A9tat\" title=\"1958 Venezuelan coup d'état\">general uprising and rioting in the streets</a>, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President</a> <a href=\"https://wikipedia.org/wiki/Marcos_P%C3%A9rez_Jim%C3%A9nez\" title=\"<PERSON>\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>.", "links": [{"title": "1958 Venezuelan coup d'état", "link": "https://wikipedia.org/wiki/1958_Venezuelan_coup_d%27%C3%A9tat"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcos_P%C3%A9<PERSON>_<PERSON>%C3%A9nez"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}]}, {"year": "1960", "text": "The bathyscaphe USS Trieste breaks a depth record by descending to 10,911 metres (35,797 ft) in the Pacific Ocean.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/Bathyscaphe\" title=\"Bathyscaphe\">bathyscaphe</a> <a href=\"https://wikipedia.org/wiki/Bathyscaphe_Trieste\" class=\"mw-redirect\" title=\"Bathyscaphe Trieste\">USS <i>Trieste</i></a> breaks a depth record by descending to 10,911 metres (35,797 ft) in the Pacific Ocean.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bathyscaphe\" title=\"Bathyscaphe\">bathyscaphe</a> <a href=\"https://wikipedia.org/wiki/Bathyscaphe_Trieste\" class=\"mw-redirect\" title=\"Bathyscaphe Trieste\">USS <i>Trieste</i></a> breaks a depth record by descending to 10,911 metres (35,797 ft) in the Pacific Ocean.", "links": [{"title": "Bathyscaphe", "link": "https://wikipedia.org/wiki/Bathyscaphe"}, {"title": "Bathyscaphe Trieste", "link": "https://wikipedia.org/wiki/Bathyscaphe_Trieste"}]}, {"year": "1963", "text": "The Guinea-Bissau War of Independence officially begins when PAIGC guerrilla fighters attack the Portuguese Army stationed in Tite.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Guinea-Bissau_War_of_Independence\" title=\"Guinea-Bissau War of Independence\">Guinea-Bissau War of Independence</a> officially begins when <a href=\"https://wikipedia.org/wiki/PAIGC\" class=\"mw-redirect\" title=\"PAIGC\">PAIGC</a> guerrilla fighters attack the <a href=\"https://wikipedia.org/wiki/Portuguese_Army\" title=\"Portuguese Army\">Portuguese Army</a> stationed in <a href=\"https://wikipedia.org/wiki/Tite_(Guinea-Bissau)\" title=\"Tite (Guinea-Bissau)\">Tite</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Guinea-Bissau_War_of_Independence\" title=\"Guinea-Bissau War of Independence\">Guinea-Bissau War of Independence</a> officially begins when <a href=\"https://wikipedia.org/wiki/PAIGC\" class=\"mw-redirect\" title=\"PAIGC\">PAIGC</a> guerrilla fighters attack the <a href=\"https://wikipedia.org/wiki/Portuguese_Army\" title=\"Portuguese Army\">Portuguese Army</a> stationed in <a href=\"https://wikipedia.org/wiki/Tite_(Guinea-Bissau)\" title=\"Tite (Guinea-Bissau)\">Tite</a>.", "links": [{"title": "Guinea-Bissau War of Independence", "link": "https://wikipedia.org/wiki/Guinea-Bissau_War_of_Independence"}, {"title": "PAIGC", "link": "https://wikipedia.org/wiki/PAIGC"}, {"title": "Portuguese Army", "link": "https://wikipedia.org/wiki/Portuguese_Army"}, {"title": "Tite (Guinea-Bissau)", "link": "https://wikipedia.org/wiki/Tite_(Guinea-Bissau)"}]}, {"year": "1964", "text": "The 24th Amendment to the United States Constitution, prohibiting the use of poll taxes in national elections, is ratified.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/Twenty-fourth_Amendment_to_the_United_States_Constitution\" title=\"Twenty-fourth Amendment to the United States Constitution\">24th Amendment</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a>, prohibiting the use of <a href=\"https://wikipedia.org/wiki/Poll_tax_(United_States)\" class=\"mw-redirect\" title=\"Poll tax (United States)\">poll taxes</a> in national elections, is ratified.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Twenty-fourth_Amendment_to_the_United_States_Constitution\" title=\"Twenty-fourth Amendment to the United States Constitution\">24th Amendment</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a>, prohibiting the use of <a href=\"https://wikipedia.org/wiki/Poll_tax_(United_States)\" class=\"mw-redirect\" title=\"Poll tax (United States)\">poll taxes</a> in national elections, is ratified.", "links": [{"title": "Twenty-fourth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Twenty-fourth_Amendment_to_the_United_States_Constitution"}, {"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}, {"title": "Poll tax (United States)", "link": "https://wikipedia.org/wiki/Poll_tax_(United_States)"}]}, {"year": "1967", "text": "Diplomatic relations between the Soviet Union and Ivory Coast are established.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Diplomatic_relations\" class=\"mw-redirect\" title=\"Diplomatic relations\">Diplomatic relations</a> between the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> and <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a> are established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diplomatic_relations\" class=\"mw-redirect\" title=\"Diplomatic relations\">Diplomatic relations</a> between the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> and <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a> are established.", "links": [{"title": "Diplomatic relations", "link": "https://wikipedia.org/wiki/Diplomatic_relations"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Ivory Coast", "link": "https://wikipedia.org/wiki/Ivory_Coast"}]}, {"year": "1967", "text": "Milton Keynes (England) is founded as a new town by Order in Council, with a planning brief to become a city of 250,000 people. Its initial designated area enclosed three existing towns and twenty-one villages. The area to be developed was largely farmland, with evidence of continuous settlement dating back to the Bronze Age.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Milton_Keynes\" title=\"Milton Keynes\">Milton Keynes</a> (England) is founded as a <a href=\"https://wikipedia.org/wiki/New_towns_in_the_United_Kingdom\" title=\"New towns in the United Kingdom\">new town</a> by <a href=\"https://wikipedia.org/wiki/Order_in_Council\" title=\"Order in Council\">Order in Council</a>, with a planning brief to become a city of 250,000 people. Its initial designated area enclosed three existing towns and twenty-one villages. The area to be developed was largely farmland, with <a href=\"https://wikipedia.org/wiki/History_of_Milton_Keynes\" title=\"History of Milton Keynes\">evidence of continuous settlement</a> dating back to the <a href=\"https://wikipedia.org/wiki/Bronze_Age\" title=\"Bronze Age\">Bronze Age</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milton_Keynes\" title=\"Milton Keynes\">Milton Keynes</a> (England) is founded as a <a href=\"https://wikipedia.org/wiki/New_towns_in_the_United_Kingdom\" title=\"New towns in the United Kingdom\">new town</a> by <a href=\"https://wikipedia.org/wiki/Order_in_Council\" title=\"Order in Council\">Order in Council</a>, with a planning brief to become a city of 250,000 people. Its initial designated area enclosed three existing towns and twenty-one villages. The area to be developed was largely farmland, with <a href=\"https://wikipedia.org/wiki/History_of_Milton_Keynes\" title=\"History of Milton Keynes\">evidence of continuous settlement</a> dating back to the <a href=\"https://wikipedia.org/wiki/Bronze_Age\" title=\"Bronze Age\">Bronze Age</a>.", "links": [{"title": "Milton Keynes", "link": "https://wikipedia.org/wiki/Milton_Keynes"}, {"title": "New towns in the United Kingdom", "link": "https://wikipedia.org/wiki/New_towns_in_the_United_Kingdom"}, {"title": "Order in Council", "link": "https://wikipedia.org/wiki/Order_in_Council"}, {"title": "History of Milton Keynes", "link": "https://wikipedia.org/wiki/History_of_Milton_Keynes"}, {"title": "Bronze Age", "link": "https://wikipedia.org/wiki/Bronze_Age"}]}, {"year": "1968", "text": "USS Pueblo (AGER-2) is attacked and seized by the Korean People's Navy.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/USS_Pueblo_(AGER-2)\" title=\"USS Pueblo (AGER-2)\">USS <i>Pueblo</i> (AGER-2)</a> is attacked and seized by the <a href=\"https://wikipedia.org/wiki/Korean_People%27s_Navy\" title=\"Korean People's Navy\">Korean People's Navy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USS_Pueblo_(AGER-2)\" title=\"USS Pueblo (AGER-2)\">USS <i>Pueblo</i> (AGER-2)</a> is attacked and seized by the <a href=\"https://wikipedia.org/wiki/Korean_People%27s_Navy\" title=\"Korean People's Navy\">Korean People's Navy</a>.", "links": [{"title": "USS Pueblo (AGER-2)", "link": "https://wikipedia.org/wiki/USS_Pueblo_(AGER-2)"}, {"title": "Korean People's Navy", "link": "https://wikipedia.org/wiki/Korean_People%27s_Navy"}]}, {"year": "1982", "text": "World Airways Flight 30 overshoots the runway at Logan International Airport in Boston, Massachusetts, and crashes into Boston Harbor. Two people are missing and presumed dead.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/World_Airways_Flight_30\" class=\"mw-redirect\" title=\"World Airways Flight 30\">World Airways Flight 30</a> overshoots the runway at <a href=\"https://wikipedia.org/wiki/Logan_International_Airport\" title=\"Logan International Airport\">Logan International Airport</a> in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a>, and crashes into <a href=\"https://wikipedia.org/wiki/Boston_Harbor\" title=\"Boston Harbor\">Boston Harbor</a>. Two people are missing and presumed dead.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_Airways_Flight_30\" class=\"mw-redirect\" title=\"World Airways Flight 30\">World Airways Flight 30</a> overshoots the runway at <a href=\"https://wikipedia.org/wiki/Logan_International_Airport\" title=\"Logan International Airport\">Logan International Airport</a> in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a>, and crashes into <a href=\"https://wikipedia.org/wiki/Boston_Harbor\" title=\"Boston Harbor\">Boston Harbor</a>. Two people are missing and presumed dead.", "links": [{"title": "World Airways Flight 30", "link": "https://wikipedia.org/wiki/World_Airways_Flight_30"}, {"title": "Logan International Airport", "link": "https://wikipedia.org/wiki/Logan_International_Airport"}, {"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}, {"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}, {"title": "Boston Harbor", "link": "https://wikipedia.org/wiki/Boston_Harbor"}]}, {"year": "1986", "text": "The Rock and Roll Hall of Fame inducts its first members: <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, The Everly Brothers, <PERSON>, <PERSON> and <PERSON>.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/Rock_and_Roll_Hall_of_Fame\" title=\"Rock and Roll Hall of Fame\">Rock and Roll Hall of Fame</a> inducts its first members: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Richard\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Berry\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Fats_Domino\" title=\"Fats Domino\">Fats Domino</a>, <a href=\"https://wikipedia.org/wiki/The_<PERSON>ly_Brothers\" title=\"The Everly Brothers\">The Everly Brothers</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Jerry Lee <PERSON>\"><PERSON> Lee <PERSON></a> and <a href=\"https://wikipedia.org/wiki/Elvis_<PERSON>\" title=\"Elvis <PERSON>\">Elvis <PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rock_and_Roll_Hall_of_Fame\" title=\"Rock and Roll Hall of Fame\">Rock and Roll Hall of Fame</a> inducts its first members: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Richard\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Berry\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Fats_Domino\" title=\"Fats Domino\">Fats Domino</a>, <a href=\"https://wikipedia.org/wiki/The_<PERSON><PERSON>_Brothers\" title=\"The Everly Brothers\">The Everly Brothers</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Holly\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Lee_<PERSON>\" title=\"<PERSON> Lee Lewis\">Jerry Lee <PERSON></a> and <a href=\"https://wikipedia.org/wiki/Elvis_<PERSON>\" title=\"Elvis <PERSON>\">Elvis <PERSON></a>.", "links": [{"title": "Rock and Roll Hall of Fame", "link": "https://wikipedia.org/wiki/Rock_and_Roll_Hall_of_Fame"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fats_Domino"}, {"title": "The Everly Brothers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Brothers"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON> sends a \"letter of death\" to Somali President <PERSON><PERSON>, proposing the genocide of the Isaaq people.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends a \"<a href=\"https://wikipedia.org/wiki/Isaaq_genocide#Letter_of_Death\" title=\"Isaaq genocide\">letter of death</a>\" to Somali President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, proposing the genocide of the Isaaq people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends a \"<a href=\"https://wikipedia.org/wiki/Isaaq_genocide#Letter_of_Death\" title=\"Isaaq genocide\">letter of death</a>\" to Somali President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, proposing the genocide of the Isaaq people.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Isaaq genocide", "link": "https://wikipedia.org/wiki/Isaaq_genocide#Letter_of_Death"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ad_Barre"}]}, {"year": "1997", "text": "<PERSON> becomes the first woman to serve as United States Secretary of State.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to serve as <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to serve as <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1998", "text": "Netscape announces Mozilla, with the intention to release Communicator code as open source.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Netscape\" title=\"Netscape\">Netscape</a> announces <a href=\"https://wikipedia.org/wiki/Mozilla\" title=\"Mozilla\">Mozilla</a>, with the intention to release Communicator code as <a href=\"https://wikipedia.org/wiki/Open_source_software\" class=\"mw-redirect\" title=\"Open source software\">open source</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Netscape\" title=\"Netscape\">Netscape</a> announces <a href=\"https://wikipedia.org/wiki/Mozilla\" title=\"Mozilla\">Mozilla</a>, with the intention to release Communicator code as <a href=\"https://wikipedia.org/wiki/Open_source_software\" class=\"mw-redirect\" title=\"Open source software\">open source</a>.", "links": [{"title": "Netscape", "link": "https://wikipedia.org/wiki/Netscape"}, {"title": "Mozilla", "link": "https://wikipedia.org/wiki/Mozilla"}, {"title": "Open source software", "link": "https://wikipedia.org/wiki/Open_source_software"}]}, {"year": "2001", "text": "Five people attempt to set themselves on fire in Beijing's Tiananmen Square, an act that many people later claim is staged by the Chinese Communist Party to frame <PERSON><PERSON><PERSON> and thus escalate their persecution.", "html": "2001 - Five people attempt to <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_self-immolation_incident\" class=\"mw-redirect\" title=\"Tiananmen Square self-immolation incident\">set themselves on fire</a> in Beijing's <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a>, an act that many people later claim is staged by the <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> to frame <a href=\"https://wikipedia.org/wiki/Falun_Gong\" title=\"Falun Gong\">Falun Gong</a> and thus escalate their <a href=\"https://wikipedia.org/wiki/Persecution_of_Falun_Gong\" title=\"Persecution of Falun Gong\">persecution</a>.", "no_year_html": "Five people attempt to <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_self-immolation_incident\" class=\"mw-redirect\" title=\"Tiananmen Square self-immolation incident\">set themselves on fire</a> in Beijing's <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a>, an act that many people later claim is staged by the <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> to frame <a href=\"https://wikipedia.org/wiki/Falun_Gong\" title=\"Falun Gong\">Falun Gong</a> and thus escalate their <a href=\"https://wikipedia.org/wiki/Persecution_of_Falun_Gong\" title=\"Persecution of Falun Gong\">persecution</a>.", "links": [{"title": "Tiananmen Square self-immolation incident", "link": "https://wikipedia.org/wiki/Tiananmen_Square_self-immolation_incident"}, {"title": "Tiananmen Square", "link": "https://wikipedia.org/wiki/Tiananmen_Square"}, {"title": "Chinese Communist Party", "link": "https://wikipedia.org/wiki/Chinese_Communist_Party"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>al<PERSON>_<PERSON>"}, {"title": "Persecution of Falun Gong", "link": "https://wikipedia.org/wiki/Persecution_of_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "U.S. journalist <PERSON> is kidnapped in Karachi, Pakistan and subsequently murdered.", "html": "2002 - U.S. journalist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is kidnapped in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>, Pakistan and subsequently murdered.", "no_year_html": "U.S. journalist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is kidnapped in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>, Pakistan and subsequently murdered.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Karachi", "link": "https://wikipedia.org/wiki/Karachi"}]}, {"year": "2003", "text": "A very weak signal from Pioneer 10 is detected for the last time, but no usable data can be extracted.", "html": "2003 - A very weak signal from <a href=\"https://wikipedia.org/wiki/Pioneer_10\" title=\"Pioneer 10\">Pioneer 10</a> is detected for the last time, but no usable data can be extracted.", "no_year_html": "A very weak signal from <a href=\"https://wikipedia.org/wiki/Pioneer_10\" title=\"Pioneer 10\">Pioneer 10</a> is detected for the last time, but no usable data can be extracted.", "links": [{"title": "Pioneer 10", "link": "https://wikipedia.org/wiki/Pioneer_10"}]}, {"year": "2018", "text": "A 7.9 Mw  earthquake occurs in the Gulf of Alaska. It is tied as the sixth-largest earthquake ever recorded in the United States, but there are no reports of significant damage or fatalities.", "html": "2018 - A 7.9 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2018_Gulf_of_Alaska_earthquake\" title=\"2018 Gulf of Alaska earthquake\">earthquake</a> occurs in the <a href=\"https://wikipedia.org/wiki/Gulf_of_Alaska\" title=\"Gulf of Alaska\">Gulf of Alaska</a>. It is tied as the <a href=\"https://wikipedia.org/wiki/List_of_earthquakes_in_the_United_States\" title=\"List of earthquakes in the United States\">sixth-largest earthquake</a> ever recorded in the United States, but there are no reports of significant damage or fatalities.", "no_year_html": "A 7.9 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2018_Gulf_of_Alaska_earthquake\" title=\"2018 Gulf of Alaska earthquake\">earthquake</a> occurs in the <a href=\"https://wikipedia.org/wiki/Gulf_of_Alaska\" title=\"Gulf of Alaska\">Gulf of Alaska</a>. It is tied as the <a href=\"https://wikipedia.org/wiki/List_of_earthquakes_in_the_United_States\" title=\"List of earthquakes in the United States\">sixth-largest earthquake</a> ever recorded in the United States, but there are no reports of significant damage or fatalities.", "links": [{"title": "2018 Gulf of Alaska earthquake", "link": "https://wikipedia.org/wiki/2018_Gulf_of_Alaska_earthquake"}, {"title": "Gulf of Alaska", "link": "https://wikipedia.org/wiki/Gulf_of_Alaska"}, {"title": "List of earthquakes in the United States", "link": "https://wikipedia.org/wiki/List_of_earthquakes_in_the_United_States"}]}, {"year": "2018", "text": "A double car bombing in Benghazi, Libya, kills at least 33 people and wounds \"dozens\" of others. The victims include both military personnel and civilians, according to local officials.", "html": "2018 - A double car bombing in <a href=\"https://wikipedia.org/wiki/Benghazi\" title=\"Benghazi\">Benghazi</a>, Libya, kills at least 33 people and wounds \"dozens\" of others. The victims include both military personnel and civilians, according to local officials.", "no_year_html": "A double car bombing in <a href=\"https://wikipedia.org/wiki/Benghazi\" title=\"Benghazi\">Benghazi</a>, Libya, kills at least 33 people and wounds \"dozens\" of others. The victims include both military personnel and civilians, according to local officials.", "links": [{"title": "Benghazi", "link": "https://wikipedia.org/wiki/Benghazi"}]}, {"year": "2018", "text": "The China-United States trade war begins when President <PERSON> places tariffs on Chinese solar panels and washing machines.", "html": "2018 - The <a href=\"https://wikipedia.org/wiki/China%E2%80%93United_States_trade_war\" title=\"China-United States trade war\">China-United States trade war</a> begins when President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> places <a href=\"https://wikipedia.org/wiki/First_Trump_tariffs\" title=\"First Trump tariffs\">tariffs</a> on Chinese solar panels and washing machines.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/China%E2%80%93United_States_trade_war\" title=\"China-United States trade war\">China-United States trade war</a> begins when President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> places <a href=\"https://wikipedia.org/wiki/First_Trump_tariffs\" title=\"First Trump tariffs\">tariffs</a> on Chinese solar panels and washing machines.", "links": [{"title": "China-United States trade war", "link": "https://wikipedia.org/wiki/China%E2%80%93United_States_trade_war"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "First Trump tariffs", "link": "https://wikipedia.org/wiki/First_Trump_tariffs"}]}, {"year": "2022", "text": "Mutinying Burkinabè soldiers led by <PERSON><PERSON><PERSON> depose and detain President <PERSON><PERSON> amid widespread anti-government protests.", "html": "2022 - Mutinying Burkinabè soldiers led by <a href=\"https://wikipedia.org/wiki/<PERSON>_Damiba\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/January_2022_Burkina_Faso_coup_d%27%C3%A9tat\" title=\"January 2022 Burkina Faso coup d'état\">depose and detain</a> President <a href=\"https://wikipedia.org/wiki/Roch_<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"Roch <PERSON>\">Roch <PERSON></a> amid widespread anti-government protests.", "no_year_html": "Mutinying Burkinabè soldiers led by <a href=\"https://wikipedia.org/wiki/<PERSON>_Damiba\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/January_2022_Burkina_Faso_coup_d%27%C3%A9tat\" title=\"January 2022 Burkina Faso coup d'état\">depose and detain</a> President <a href=\"https://wikipedia.org/wiki/Roch_<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"Roch <PERSON>\">Roch <PERSON></a> amid widespread anti-government protests.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>iba"}, {"title": "January 2022 Burkina Faso coup d'état", "link": "https://wikipedia.org/wiki/January_2022_Burkina_Faso_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "2024", "text": "Northwestern Air Flight 738 crashes after takeoff from Fort Smith Airport, Northwest Territories, Canada, killing six people.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Northwestern_Air_Flight_738\" title=\"Northwestern Air Flight 738\">Northwestern Air Flight 738</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Fort_Smith_Airport\" title=\"Fort Smith Airport\">Fort Smith Airport</a>, Northwest Territories, Canada, killing six people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northwestern_Air_Flight_738\" title=\"Northwestern Air Flight 738\">Northwestern Air Flight 738</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Fort_Smith_Airport\" title=\"Fort Smith Airport\">Fort Smith Airport</a>, Northwest Territories, Canada, killing six people.", "links": [{"title": "Northwestern Air Flight 738", "link": "https://wikipedia.org/wiki/Northwestern_Air_Flight_738"}, {"title": "Fort Smith Airport", "link": "https://wikipedia.org/wiki/Fort_Smith_Airport"}]}], "Births": [{"year": "1350", "text": "<PERSON>, Spanish missionary and saint (d. 1419)", "html": "1350 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish missionary and saint (d. 1419)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish missionary and saint (d. 1419)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1378", "text": "<PERSON>, Elector <PERSON> (d. 1436)", "html": "1378 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a> (d. 1436)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a> (d. 1436)", "links": [{"title": "<PERSON>, <PERSON>ector <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1514", "text": "<PERSON>, Chinese politician (d. 1587)", "html": "1514 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician (d. 1587)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician (d. 1587)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ui"}]}, {"year": "1585", "text": "<PERSON>, English Catholic Religious Sister (d. 1645)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(nun)\" title=\"<PERSON> (nun)\"><PERSON></a>, English Catholic Religious Sister (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(nun)\" title=\"<PERSON> (nun)\"><PERSON></a>, English Catholic Religious Sister (d. 1645)", "links": [{"title": "<PERSON> (nun)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(nun)"}]}, {"year": "1622", "text": "<PERSON>, Dutch painter (d. 1670)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1719", "text": "<PERSON>, English mathematician and theorist (d. 1790)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and theorist (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and theorist (d. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1737", "text": "<PERSON>, American general and politician, first Governor of Massachusetts (d. 1793)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, first <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, first <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1745", "text": "<PERSON>, English engineer, built the Cromford Canal (d. 1814)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, built the <a href=\"https://wikipedia.org/wiki/Cromford_Canal\" title=\"Cromford Canal\">Cromford Canal</a> (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, built the <a href=\"https://wikipedia.org/wiki/Cromford_Canal\" title=\"Cromford Canal\">Cromford Canal</a> (d. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Cromford Canal", "link": "https://wikipedia.org/wiki/Cromford_Canal"}]}, {"year": "1752", "text": "<PERSON><PERSON>, Italian pianist, composer, and conductor (d. 1832)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist, composer, and conductor (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist, composer, and conductor (d. 1832)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1780", "text": "<PERSON><PERSON>, Greek general (d. 1827)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (d. 1827)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georg<PERSON>_Karaiskakis"}]}, {"year": "1783", "text": "<PERSON><PERSON><PERSON>, French novelist (d. 1842)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/Stendhal\" title=\"Stendhal\"><PERSON><PERSON><PERSON></a>, French novelist (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stendhal\" title=\"Stendhal\"><PERSON><PERSON><PERSON></a>, French novelist (d. 1842)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stendhal"}]}, {"year": "1786", "text": "<PERSON>, French-Russian architect, designed Saint Isaac's Cathedral and <PERSON> (d. 1858)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Russian architect, designed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Cathedral\" title=\"Saint Isaac's Cathedral\">Saint <PERSON>'s Cathedral</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Russian architect, designed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Cathedral\" title=\"Saint Isaac's Cathedral\">Saint <PERSON>'s Cathedral</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Saint <PERSON>'s Cathedral", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Cathedral"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexander_<PERSON>n"}]}, {"year": "1799", "text": "<PERSON><PERSON>, Tyrolean engineer and railroad pioneer active in the Austrian Empire (d. 1858)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tyrolean engineer and railroad pioneer active in the Austrian Empire (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tyrolean engineer and railroad pioneer active in the Austrian Empire (d. 1858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON><PERSON>, Indian activist (d. 1884)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sai\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Sai\"><PERSON><PERSON></a>, Indian activist (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sai\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Sai\"><PERSON><PERSON></a>, Indian activist (d. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Sai"}]}, {"year": "1813", "text": "<PERSON><PERSON>, Norwegian novelist and activist (d. 1895)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian novelist and activist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian novelist and activist (d. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lett"}]}, {"year": "1828", "text": "<PERSON><PERSON><PERSON>, Japanese samurai (d. 1877)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/Saig%C5%8D_<PERSON><PERSON>mori\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese samurai (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saig%C5%8D_<PERSON><PERSON>mori\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese samurai (d. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saig%C5%8D_<PERSON><PERSON><PERSON>i"}]}, {"year": "1832", "text": "<PERSON><PERSON><PERSON>, French painter (d. 1883)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (d. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON><PERSON><PERSON>, Sri Lankan lawyer and politician (d. 1879)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician (d. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1838", "text": "<PERSON>, German-American nun and saint (d. 1918)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American nun and saint (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American nun and saint (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, German physicist and engineer (d. 1905)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and engineer (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and engineer (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON><PERSON>, Russian physicist and mathematician (d. 1915)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and mathematician (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and mathematician (d. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, American politician (d. 1902)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, American weapons designer, founded the Browning Arms Company (d. 1926)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weapons designer, founded the <a href=\"https://wikipedia.org/wiki/Browning_Arms_Company\" title=\"Browning Arms Company\">Browning Arms Company</a> (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Browning\"><PERSON></a>, American weapons designer, founded the <a href=\"https://wikipedia.org/wiki/Browning_Arms_Company\" title=\"Browning Arms Company\">Browning Arms Company</a> (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Browning Arms Company", "link": "https://wikipedia.org/wiki/Browning_Arms_Company"}]}, {"year": "1857", "text": "<PERSON><PERSON><PERSON>, Croatian meteorologist and seismologist (d. 1936)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ovi%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian meteorologist and seismologist (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian meteorologist and seismologist (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mohorovi%C4%8Di%C4%87"}]}, {"year": "1862", "text": "<PERSON>, German mathematician and academic (d. 1943)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American inventor and engineer (d. 1918)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and engineer (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and engineer (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, French physicist and academic (d. 1946)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON>, Slovenian architect, designed Plečnik Parliament (d. 1957)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Jo%C5%BEe_Ple%C4%8Dnik\" title=\"<PERSON><PERSON><PERSON> Plečnik\"><PERSON><PERSON><PERSON></a>, Slovenian architect, designed <a href=\"https://wikipedia.org/wiki/Ple%C4%8Dnik_Parliament\" title=\"Plečnik Parliament\">Plečnik Parliament</a> (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C5%BEe_Ple%C4%8Dnik\" title=\"<PERSON><PERSON><PERSON> Plečnik\"><PERSON><PERSON><PERSON></a>, Slovenian architect, designed <a href=\"https://wikipedia.org/wiki/Ple%C4%8Dnik_Parliament\" title=\"Plečnik Parliament\">Plečnik Parliament</a> (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo%C5%BEe_Ple%C4%8Dnik"}, {"title": "Plečnik Parliament", "link": "https://wikipedia.org/wiki/Ple%C4%8Dnik_Parliament"}]}, {"year": "1876", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 1954)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, English composer (d. 1960)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Rutland_Boughton\" title=\"Rutland Boughton\">Rut<PERSON></a>, English composer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rutland_Boughton\" title=\"Rutland Boughton\"><PERSON><PERSON><PERSON></a>, English composer (d. 1960)", "links": [{"title": "Rutland Boughton", "link": "https://wikipedia.org/wiki/Rutland_Boughton"}]}, {"year": "1880", "text": "<PERSON>, Mexican politician (d. 1967)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Antonio_D%C3%ADaz_Soto_y_Gama\" title=\"<PERSON>\"><PERSON></a>, Mexican politician (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antonio_D%C3%ADaz_Soto_y_Gama\" title=\"<PERSON>\"><PERSON></a>, Mexican politician (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_D%C3%ADaz_Soto_y_Gama"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, American mathematician (d. 1965)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Clar<PERSON> Kendall\"><PERSON><PERSON><PERSON></a>, American mathematician (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Clar<PERSON> Kendall\"><PERSON><PERSON><PERSON></a>, American mathematician (d. 1965)", "links": [{"title": "Clar<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kendall"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian author (d. 1988)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian author (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian author (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Devi"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Australian rugby league player and coach (d. 1944)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player and coach (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player and coach (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, English-South African cricketer (d. 1964)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Alf_Hall\" title=\"Alf Hall\"><PERSON><PERSON></a>, English-South African cricketer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alf_Hall\" title=\"Alf Hall\"><PERSON><PERSON></a>, English-South African cricketer (d. 1964)", "links": [{"title": "<PERSON><PERSON> Hall", "link": "https://wikipedia.org/wiki/Alf_Hall"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Indian freedom fighter and politician (d. 1945)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian freedom fighter and politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian freedom fighter and politician (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Austrian architect (d. 2000)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%BCtte-Lihotzky\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian architect (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%BCtte-Lihotzky\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian architect (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%BCtte-Lihotzky"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Lithuanian author (d. 1978)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/I<PERSON>_<PERSON>%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian author (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian author (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ieva_Simonaityt%C4%97"}]}, {"year": "1897", "text": "<PERSON>, Canadian captain and spy (d. 1989)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain and spy (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain and spy (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, German violinist (d. 1948)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American actor (d. 1987)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, English scholar and author (d. 1978)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English scholar and author (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English scholar and author (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, English racing driver and pilot (d. 1931)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ton\" title=\"Glen Kidston\"><PERSON></a>, English racing driver and pilot (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glen_<PERSON>ton\" title=\"Glen Kidston\"><PERSON></a>, English racing driver and pilot (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glen_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Welsh organist and conductor (d. 1988)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh organist and conductor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh organist and conductor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American businessman (d. 1983)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}]}, {"year": "1903", "text": "<PERSON>, Colombian lawyer and politician, 16th Minister of National Education of Colombia (d. 1948)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9cer_<PERSON>ait%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Education_of_Colombia\" class=\"mw-redirect\" title=\"Minister of National Education of Colombia\">Minister of National Education of Colombia</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9cer_<PERSON>ait%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Education_of_Colombia\" class=\"mw-redirect\" title=\"Minister of National Education of Colombia\">Minister of National Education of Colombia</a> (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jorge_Eli%C3%A9cer_Gait%C3%A1n"}, {"title": "Minister of National Education of Colombia", "link": "https://wikipedia.org/wiki/Minister_of_National_Education_of_Colombia"}]}, {"year": "1905", "text": "<PERSON>, German sprinter (d. 2000)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American actor and singer (d. 1968)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Japanese physicist and academic, Nobel Prize laureate (d. 1981)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Belgian guitarist and composer (d. 1953)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Re<PERSON>hardt\"><PERSON><PERSON><PERSON></a>, Belgian guitarist and composer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Re<PERSON>hardt\"><PERSON><PERSON><PERSON></a>, Belgian guitarist and composer (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Russian director and manager (d. 2009)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and manager (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and manager (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Algerian-French painter (d. 1960)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian-French painter (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian-French painter (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American businessman, founded the National Hot Rod Association (d. 2007)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/National_Hot_Rod_Association\" title=\"National Hot Rod Association\">National Hot Rod Association</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/National_Hot_Rod_Association\" title=\"National Hot Rod Association\">National Hot Rod Association</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Hot Rod Association", "link": "https://wikipedia.org/wiki/National_Hot_Rod_Association"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Austrian javelin thrower and handball player (d. 2003)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian javelin thrower and handball player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian javelin thrower and handball player (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON> <PERSON>, Saint <PERSON>-Barbadian economist and academic, Nobel Prize laureate (d. 1991)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\">W<PERSON> <PERSON></a>, Saint <PERSON>-Barbadian economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\">W<PERSON> <PERSON></a>, Saint <PERSON>-Barbadian economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1915", "text": "<PERSON>, American lawyer and judge (d. 1985)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American photographer and journalist (d. 2018)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, English colonel, lawyer, and politician, Shadow Secretary of State for Northern Ireland (d. 1979)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Neave\" title=\"<PERSON><PERSON>eave\"><PERSON><PERSON></a>, English colonel, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Northern_Ireland\" title=\"Shadow Secretary of State for Northern Ireland\">Shadow Secretary of State for Northern Ireland</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>eave\" title=\"<PERSON><PERSON>eave\"><PERSON><PERSON></a>, English colonel, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Northern_Ireland\" title=\"Shadow Secretary of State for Northern Ireland\">Shadow Secretary of State for Northern Ireland</a> (d. 1979)", "links": [{"title": "<PERSON><PERSON>ve", "link": "https://wikipedia.org/wiki/Airey_Neave"}, {"title": "Shadow Secretary of State for Northern Ireland", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Northern_Ireland"}]}, {"year": "1918", "text": "<PERSON>, American biochemist and pharmacologist, Nobel Prize laureate (d. 1999)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1918", "text": "<PERSON>, Executed Irish Republican (d. 1944)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}]}, {"year": "1918", "text": "<PERSON>, American social worker and theorist (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Florence_Rush\" title=\"Florence Rush\"><PERSON></a>, American social worker and theorist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_Rush\" title=\"Florence Rush\"><PERSON></a>, American social worker and theorist (d. 2008)", "links": [{"title": "Florence Rush", "link": "https://wikipedia.org/wiki/Florence_Rush"}]}, {"year": "1919", "text": "<PERSON>, Canadian-American actress (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Frances_Bay\" title=\"Frances Bay\"><PERSON></a>, Canadian-American actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frances_Bay\" title=\"Frances Bay\"><PERSON></a>, Canadian-American actress (d. 2011)", "links": [{"title": "Frances Bay", "link": "https://wikipedia.org/wiki/Frances_Bay"}]}, {"year": "1919", "text": "<PERSON>, Austrian biologist and diver (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian biologist and diver (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian biologist and diver (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American actor and game show host (d. 1962)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English footballer and manager (d. 1996)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, German architect (d. 2021)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_B%C3%B6hm\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German architect (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_B%C3%B6hm\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German architect (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gottfried_B%C3%B6hm"}]}, {"year": "1920", "text": "<PERSON>, Swedish runner (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish runner (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish runner (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American businessman, invented the Frisbee (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, invented the <a href=\"https://wikipedia.org/wiki/Fr<PERSON>bee\" title=\"<PERSON><PERSON><PERSON>\">Fr<PERSON>bee</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, invented the <a href=\"https://wikipedia.org/wiki/Fr<PERSON>bee\" title=\"<PERSON><PERSON><PERSON>\">Fr<PERSON>bee</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Frisbee", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American painter and academic (d. 2004)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Go<PERSON>\"><PERSON></a>, American painter and academic (d. 2004)", "links": [{"title": "Leon <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Australian politician, 33rd Premier of New South Wales (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 2016)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1923", "text": "<PERSON>, American runner (d. 2018)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Jr., American soldier and author (d. 1996)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American soldier and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American soldier and author (d. 1996)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1924", "text": "<PERSON>, American soldier, businessman, and politician (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, businessman, and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, businessman, and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American pianist, composer, producer, and conductor (d. 1995)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, producer, and conductor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, producer, and conductor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Indian journalist, cartoonist, and politician (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Bal_Thackeray\" title=\"Bal Thackeray\"><PERSON><PERSON></a>, Indian journalist, cartoonist, and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bal_Thackeray\" title=\"Bal Thackeray\"><PERSON><PERSON></a>, Indian journalist, cartoonist, and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bal_Thackeray"}]}, {"year": "1927", "text": "Lars<PERSON><PERSON>, Swedish-American businessman and explorer (d. 1994)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American businessman and explorer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American businessman and explorer (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Australian painter (d. 1982)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Australian painter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Australian painter (d. 1982)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1928", "text": "<PERSON>, French actress (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American journalist and sportscaster (d. 2008)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and sportscaster (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and sportscaster (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Ukrainian religious leader, Patriarch of the Ukrainian Orthodox Church - Kyiv Patriarchate", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian religious leader, Patriarch of the <a href=\"https://wikipedia.org/wiki/Ukrainian_Orthodox_Church_%E2%80%93_Kyiv_Patriarchate\" title=\"Ukrainian Orthodox Church - Kyiv Patriarchate\">Ukrainian Orthodox Church - Kyiv Patriarchate</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian religious leader, Patriarch of the <a href=\"https://wikipedia.org/wiki/Ukrainian_Orthodox_Church_%E2%80%93_Kyiv_Patriarchate\" title=\"Ukrainian Orthodox Church - Kyiv Patriarchate\">Ukrainian Orthodox Church - Kyiv Patriarchate</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Ukrainian Orthodox Church - Kyiv Patriarchate", "link": "https://wikipedia.org/wiki/Ukrainian_Orthodox_Church_%E2%80%93_Kyiv_Patriarchate"}]}, {"year": "1929", "text": "<PERSON>, Australian journalist, author, and critic (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, author, and critic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, author, and critic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German-Canadian chemist and academic, Nobel Prize laureate", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1930", "text": "<PERSON>, Russian child diarist (d. 1944)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian child diarist (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian child diarist (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Australian tennis player (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian tennis player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian tennis player (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Saint Lucian poet and playwright, Nobel Prize laureate (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saint Lucian poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saint Lucian poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Polish operatic soprano (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%<PERSON><PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish operatic soprano (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%B<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish operatic soprano (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Teresa_%C5%<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English footballer (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1932)\" title=\"<PERSON> (footballer, born 1932)\"><PERSON></a>, English footballer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1932)\" title=\"<PERSON> (footballer, born 1932)\"><PERSON></a>, English footballer (d. 2016)", "links": [{"title": "<PERSON> (footballer, born 1932)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1932)"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American actress and dancer (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Australian politician, 21st Governor General of Australia (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor General of Australia", "link": "https://wikipedia.org/wiki/Governor_General_of_Australia"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American actress, singer, and dancer (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, and dancer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, and dancer (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actor and director", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian journalist and politician (d. 2003)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Trinidadian sprinter (d. 2016)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian sprinter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian sprinter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American author (d. 1977)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American football player and sportscaster", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, French pianist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/C%C3%A9ci<PERSON>_Ousset\" title=\"Cé<PERSON><PERSON> Ousset\"><PERSON><PERSON><PERSON><PERSON></a>, French pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9ci<PERSON>_Ousset\" title=\"Cé<PERSON><PERSON> Ousset\"><PERSON><PERSON><PERSON><PERSON></a>, French pianist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9cile_O<PERSON>et"}]}, {"year": "1938", "text": "<PERSON>, Japanese wrestler and promoter, founded All Japan Pro Wrestling (d. 1999)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Baba\" title=\"Giant Baba\"><PERSON> Baba</a>, Japanese wrestler and promoter, founded <a href=\"https://wikipedia.org/wiki/All_Japan_Pro_Wrestling\" title=\"All Japan Pro Wrestling\">All Japan Pro Wrestling</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giant_Baba\" title=\"Giant Baba\"><PERSON> Baba</a>, Japanese wrestler and promoter, founded <a href=\"https://wikipedia.org/wiki/All_Japan_Pro_Wrestling\" title=\"All Japan Pro Wrestling\">All Japan Pro Wrestling</a> (d. 1999)", "links": [{"title": "Giant Baba", "link": "https://wikipedia.org/wiki/<PERSON>_Baba"}, {"title": "All Japan Pro Wrestling", "link": "https://wikipedia.org/wiki/All_Japan_Pro_Wrestling"}]}, {"year": "1938", "text": "<PERSON>, German painter and sculptor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American activist (d. 1995)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, American activist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, American activist (d. 1995)", "links": [{"title": "<PERSON> (activist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)"}]}, {"year": "1940", "text": "<PERSON>, American writer and critic (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and critic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and critic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer (d. 2016)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Australian economist and academic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian economist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Brazilian journalist, author, and academic (d. 2014)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian journalist, author, and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian journalist, author, and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON><PERSON><PERSON>_<PERSON>ibeiro"}]}, {"year": "1942", "text": "<PERSON>, Australian cricketer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mongolian politician, 1st President of Mongolia (d. 2025)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>at\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Mongolia\" title=\"President of Mongolia\">President of Mongolia</a> (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Punsal<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Mongolia\" title=\"President of Mongolia\">President of Mongolia</a> (d. 2025)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Punsalmaagiin_<PERSON>at"}, {"title": "President of Mongolia", "link": "https://wikipedia.org/wiki/President_of_Mongolia"}]}, {"year": "1942", "text": "<PERSON>, Dutch judge and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch judge and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch judge and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American musician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Turkish basketball player and businessman (d. 2010)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/%C3%96zhan_Canayd%C4%B1n\" title=\"<PERSON><PERSON><PERSON>aydın\"><PERSON><PERSON><PERSON></a>, Turkish basketball player and businessman (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96zhan_Canayd%C4%B1n\" title=\"<PERSON><PERSON><PERSON>aydın\"><PERSON><PERSON><PERSON></a>, Turkish basketball player and businessman (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96zhan_Canayd%C4%B1n"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Dutch actor, director, and producer (d. 2019)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch actor, director, and producer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch actor, director, and producer (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian politician, 22nd Premier of Ontario", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Ontario", "link": "https://wikipedia.org/wiki/Premier_of_Ontario"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Nicaraguan lawyer and politician, President of Nicaragua", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nicaraguan lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nicaraguan lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arnoldo_Alem%C3%A1n"}, {"title": "President of Nicaragua", "link": "https://wikipedia.org/wiki/President_of_Nicaragua"}]}, {"year": "1946", "text": "<PERSON>, Russian-English businessman and mathematician (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Russian-English businessman and mathematician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Russian-English businessman and mathematician (d. 2013)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_(businessman)"}]}, {"year": "1946", "text": "<PERSON>, Central African politician and diplomat (d. 2021)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Central African politician and diplomat (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Central African politician and diplomat (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American captain and politician, 71st Governor of Delaware", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 71st <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 71st <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Delaware", "link": "https://wikipedia.org/wiki/Governor_of_Delaware"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Indonesian politician, President of Indonesia", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian politician, <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian politician, <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>kar<PERSON>putri"}, {"title": "President of Indonesia", "link": "https://wikipedia.org/wiki/President_of_Indonesia"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter (d. 2022)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1949", "text": "<PERSON>, American nuclear engineer, brewer and author.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nuclear engineer, brewer and author.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nuclear engineer, brewer and author.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor, producer, and composer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Portuguese actress (d. 2018)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese actress (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Maria"}]}, {"year": "1950", "text": "<PERSON>, American economist and academic (d. 2014)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Argentinian singer-songwriter, guitarist, and poet (d. 2012)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian singer-songwriter, guitarist, and poet (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian singer-songwriter, guitarist, and poet (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, American airline pilot and safety expert", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Ch<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American airline pilot and safety expert", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Ch<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American airline pilot and safety expert", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, South African cricketer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American composer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Irish priest, historian, and theologian", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish priest, historian, and theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish priest, historian, and theologian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American politician, 41st Mayor of Los Angeles", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 41st <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 41st <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mayor of Los Angeles", "link": "https://wikipedia.org/wiki/Mayor_of_Los_Angeles"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian cricketer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Princess of Hanover", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_of_Hanover\" class=\"mw-redirect\" title=\"<PERSON>, Princess of Hanover\"><PERSON>, Princess of Hanover</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_of_Hanover\" class=\"mw-redirect\" title=\"<PERSON>, Princess of Hanover\"><PERSON>, Princess of Hanover</a>", "links": [{"title": "<PERSON>, Princess of Hanover", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_of_Hanover"}]}, {"year": "1958", "text": "<PERSON>, Russian hammer thrower (d. 2018)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete,_born_1958)\" title=\"<PERSON> (athlete, born 1958)\"><PERSON></a>, Russian hammer thrower (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete,_born_1958)\" title=\"<PERSON> (athlete, born 1958)\"><PERSON></a>, Russian hammer thrower (d. 2018)", "links": [{"title": "<PERSON> (athlete, born 1958)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete,_born_1958)"}]}, {"year": "1959", "text": "<PERSON>, English radio host", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Clive Bull\"><PERSON></a>, English radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian cricketer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Russian long jumper", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian long jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English composer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Estonian footballer and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Spanish journalist and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Grady\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Grady\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Grady"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American actress and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hargita<PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hargita<PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mariska_Hargitay"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Guyanese economist and politician, 7th President of Guyana", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guyanese economist and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guyanese economist and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}, {"title": "President of Guyana", "link": "https://wikipedia.org/wiki/President_of_Guyana"}]}, {"year": "1965", "text": "<PERSON>, American drummer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian surfer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American basketball player and referee", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Workman\" title=\"<PERSON>wood<PERSON> Workman\"><PERSON><PERSON><PERSON></a>, American basketball player and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Workman\" title=\"<PERSON>wood<PERSON> Workman\"><PERSON><PERSON><PERSON></a>, American basketball player and referee", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Workman"}]}, {"year": "1967", "text": "<PERSON>, Australian rugby league player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Japanese violinist and composer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese violinist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese violinist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Czech-Monégasque tennis player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-Monégasque tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-Monégasque tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Ukrainian-Russian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian ice hockey player and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, German long jumper", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tiedtke\" title=\"<PERSON><PERSON> Tiedtke\"><PERSON><PERSON></a>, German long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tiedtke\" title=\"<PERSON><PERSON> Tiedtke\"><PERSON><PERSON>ied<PERSON></a>, German long jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>ke"}]}, {"year": "1970", "text": "<PERSON>, Czech ice hockey player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Richard_%C5%A0mehl%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0mehl%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_%C5%A0mehl%C3%ADk"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Greek long jumper", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek long jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rid<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American football player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, New Zealand cricketer and mountaineer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and mountaineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Scottish actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Swedish ice hockey player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tomas_<PERSON>r%C3%B6m"}]}, {"year": "1974", "text": "<PERSON>, English cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>pp<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>pp<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>le"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Australian actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English painter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>en"}]}, {"year": "1975", "text": "<PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American mixed martial artist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player and scout", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Norwegian orienteering competitor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian orienteering competitor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian orienteering competitor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American skier", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(alpine_skier)\" class=\"mw-redirect\" title=\"<PERSON> (alpine skier)\"><PERSON></a>, American skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(alpine_skier)\" class=\"mw-redirect\" title=\"<PERSON> (alpine skier)\"><PERSON></a>, American skier", "links": [{"title": "<PERSON> (alpine skier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(alpine_skier)"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Scottish-English fashion designer and journalist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Porter\" title=\"<PERSON>\"><PERSON></a>, Scottish-English fashion designer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Porter\" title=\"<PERSON>\"><PERSON></a>, Scottish-English fashion designer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dawn_O%27Porter"}]}, {"year": "1979", "text": "<PERSON>, Venezuelan baseball player and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, Venezuelan baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Rinc%C3%B3n"}]}, {"year": "1979", "text": "<PERSON>, Russian basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian soccer player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Friend\" title=\"<PERSON> Friend\"><PERSON></a>, Canadian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Friend\" title=\"<PERSON> Friend\"><PERSON></a>, Canadian soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Friend"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, German singer and songwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Wily_<PERSON>_Pe%C3%B1a\" title=\"Wily <PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wily_<PERSON>_Pe%C3%B1a\" title=\"Wily <PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "W<PERSON>", "link": "https://wikipedia.org/wiki/Wily_Mo_Pe%C3%B1a"}]}, {"year": "1982", "text": "<PERSON>, American sprinter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Andrew Rock\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Andrew Rock\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Panamanian long jumper", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, South Korean rapper", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_E\" title=\"San E\"><PERSON></a>, South Korean rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_E\" title=\"San E\"><PERSON></a>, South Korean rapper", "links": [{"title": "San E", "link": "https://wikipedia.org/wiki/San_E"}]}, {"year": "1985", "text": "<PERSON>, Chinese footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Dutch model and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch model and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Russian pole vaulter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pole vaulter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ch_Mergia\" title=\"Aselefech Mergia\"><PERSON><PERSON><PERSON><PERSON> Mergia</a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Mergia\" title=\"Aselefech Mergia\"><PERSON><PERSON><PERSON><PERSON> Mergia</a>, Ethiopian runner", "links": [{"title": "Aselefech Mergia", "link": "https://wikipedia.org/wiki/As<PERSON><PERSON>ch_Mergia"}]}, {"year": "1985", "text": "<PERSON>, American baseball and football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball and football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Burka\" title=\"<PERSON><PERSON><PERSON> Burka\"><PERSON><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_B<PERSON>ka\" title=\"<PERSON><PERSON><PERSON> Burka\"><PERSON><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Burka"}]}, {"year": "1986", "text": "<PERSON>, Scottish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Spanish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Swiss skier", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Sand<PERSON>_V<PERSON>tta\" title=\"<PERSON><PERSON> V<PERSON>tta\"><PERSON><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sand<PERSON>_Viletta\" title=\"<PERSON>ro Viletta\"><PERSON><PERSON></a>, Swiss skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_V<PERSON>tta"}]}, {"year": "1987", "text": "<PERSON>, Estonian-Finnish ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Finnish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian wrestler", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)"}]}, {"year": "1991", "text": "<PERSON>, American soccer player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Japanese model and actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>endl"}]}, {"year": "1994", "text": "<PERSON>, American baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, English footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>us-Cheek\" title=\"<PERSON><PERSON> Lo<PERSON>-Cheek\"><PERSON><PERSON>-Cheek</a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Cheek\" title=\"<PERSON><PERSON>-Cheek\"><PERSON><PERSON>-Cheek</a>, English footballer", "links": [{"title": "<PERSON><PERSON>-<PERSON>eek", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Cheek"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, American rapper (d. 2018)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/XXXTentacion\" title=\"XXXTentacion\">XXXTentac<PERSON></a>, American rapper (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/XXXTentacion\" title=\"XXXTentacion\">XXXTentac<PERSON></a>, American rapper (d. 2018)", "links": [{"title": "XXXTentacion", "link": "https://wikipedia.org/wiki/XXXTentacion"}]}, {"year": "1999", "text": "<PERSON><PERSON>, French footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Alban_La<PERSON>nt\" title=\"Alban La<PERSON>nt\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alban_La<PERSON>nt\" title=\"Alban La<PERSON>nt\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alban_Lafont"}]}, {"year": "2001", "text": "<PERSON>, Serbian tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Olga_Danilovi%C4%87"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Croatian footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Jo%C5%A1ko_Gvardiol\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C5%A1ko_Gvardiol\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo%C5%A1ko_Gvardiol"}]}, {"year": "2002", "text": "<PERSON>, Polish footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Paraguayan footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_2004)\" title=\"<PERSON> (footballer, born 2004)\"><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_2004)\" title=\"<PERSON> (footballer, born 2004)\"><PERSON></a>, Paraguayan footballer", "links": [{"title": "<PERSON> (footballer, born 2004)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_2004)"}]}], "Deaths": [{"year": "667", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bishop of Toledo", "html": "667 - <a href=\"https://wikipedia.org/wiki/Ildefonsus\" title=\"Ildefonsus\">Ildefonsus</a>, <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Toledo\" title=\"Roman Catholic Archdiocese of Toledo\">bishop of Toledo</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ildefonsus\" title=\"Ildefonsus\">Ildefonsus</a>, <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Toledo\" title=\"Roman Catholic Archdiocese of Toledo\">bishop of Toledo</a>", "links": [{"title": "Ildefonsus", "link": "https://wikipedia.org/wiki/Ildefonsus"}, {"title": "Roman Catholic Archdiocese of Toledo", "link": "https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Toledo"}]}, {"year": "989", "text": "<PERSON><PERSON><PERSON><PERSON>, archbishop of Reims", "html": "989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(archbishop_of_Reims)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (archbishop of Reims)\"><PERSON><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Reims\" title=\"Roman Catholic Archdiocese of Reims\">Reims</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(archbishop_of_Reims)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (archbishop of Reims)\"><PERSON><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Reims\" title=\"Roman Catholic Archdiocese of Reims\">Reims</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (archbishop of Reims)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(archbishop_of_Reims)"}, {"title": "Roman Catholic Archdiocese of Reims", "link": "https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Reims"}]}, {"year": "1002", "text": "<PERSON>, Holy Roman Emperor (b. 980)", "html": "1002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 980)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1199", "text": "<PERSON>, Moroccan caliph (b. 1160)", "html": "1199 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Moroccan caliph (b. 1160)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Moroccan caliph (b. 1160)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1252", "text": "<PERSON>, Queen of Armenia", "html": "1252 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Armenia\" title=\"<PERSON>, Queen of Armenia\"><PERSON>, Queen of Armenia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Armenia\" title=\"<PERSON>, Queen of Armenia\"><PERSON>, Queen of Armenia</a>", "links": [{"title": "<PERSON>, Queen of Armenia", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Armenia"}]}, {"year": "1297", "text": "Florent of Hai<PERSON>ut, Prince of Achaea (b. c. 1255)", "html": "1297 - <a href=\"https://wikipedia.org/wiki/Florent_of_Hainaut\" title=\"Florent of Hainaut\">Florent of Hainaut</a>, Prince of Achaea (b. c. 1255)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florent_of_Hainaut\" title=\"Florent of Hainaut\">Florent of Hainaut</a>, Prince of Achaea (b. c. 1255)", "links": [{"title": "Florent of Hainaut", "link": "https://wikipedia.org/wiki/Florent_of_Hainaut"}]}, {"year": "1423", "text": "<PERSON> of Bavaria, Burgundian regent (b. 1363)", "html": "1423 - <a href=\"https://wikipedia.org/wiki/Margaret_<PERSON>_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, Burgundian regent (b. 1363)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, Burgundian regent (b. 1363)", "links": [{"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/Margaret_<PERSON>_Bavaria"}]}, {"year": "1516", "text": "<PERSON> Aragon (b. 1452)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Aragon\" title=\"<PERSON> II of Aragon\"><PERSON> Aragon</a> (b. 1452)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> II of Aragon\"><PERSON> of Aragon</a> (b. 1452)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon"}]}, {"year": "1548", "text": "<PERSON>, Italian priest, scholar, and composer (b. 1490)", "html": "1548 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, scholar, and composer (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, scholar, and composer (b. 1490)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1549", "text": "<PERSON>, Romanian-Hungarian cartographer and theologian (b. 1498)", "html": "1549 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Hungarian cartographer and theologian (b. 1498)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Hungarian cartographer and theologian (b. 1498)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1567", "text": "<PERSON><PERSON><PERSON> Emperor of China (b. 1507)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Jiajing Emperor\"><PERSON><PERSON><PERSON> Emperor</a> of China (b. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Jiajing Emperor\">Jiajing Emperor</a> of China (b. 1507)", "links": [{"title": "<PERSON><PERSON><PERSON> Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}]}, {"year": "1570", "text": "<PERSON>, 1st Earl of Moray, Scottish politician (b. 1531)", "html": "1570 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Moray\" title=\"<PERSON>, 1st Earl of Moray\"><PERSON>, 1st Earl of Moray</a>, Scottish politician (b. 1531)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Moray\" title=\"<PERSON>, 1st Earl of Moray\"><PERSON>, 1st Earl of Moray</a>, Scottish politician (b. 1531)", "links": [{"title": "<PERSON>, 1st Earl of Moray", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Moray"}]}, {"year": "1620", "text": "<PERSON>, English politician and judge (b. 1553)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and judge (b. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and judge (b. 1553)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1622", "text": "<PERSON>, English explorer and navigator (b. 1584)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer and navigator (b. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer and navigator (b. 1584)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1650", "text": "<PERSON>, 4th Earl of Pembroke (b. 1584)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Pembroke\" title=\"<PERSON>, 4th Earl of Pembroke\"><PERSON>, 4th Earl of Pembroke</a> (b. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Pembroke\" title=\"<PERSON>, 4th Earl of Pembroke\"><PERSON>, 4th Earl of Pembroke</a> (b. 1584)", "links": [{"title": "<PERSON>, 4th Earl of Pembroke", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Pembroke"}]}, {"year": "1744", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian historian and philosopher (b. 1668)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian historian and philosopher (b. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian historian and philosopher (b. 1668)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, Scottish mathematician and academic (b. 1717)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Scottish mathematician and academic (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Scottish mathematician and academic (b. 1717)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>(mathematician)"}]}, {"year": "1789", "text": "<PERSON>, English author and playwright (b. 1724)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1724)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, English author (b. 1709)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1709)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, American captain and politician, 39th Governor of South Carolina (b. 1749)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Carolina\" title=\"Governor of South Carolina\">Governor of South Carolina</a> (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Carolina\" title=\"Governor of South Carolina\">Governor of South Carolina</a> (b. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of South Carolina", "link": "https://wikipedia.org/wiki/Governor_of_South_Carolina"}]}, {"year": "1803", "text": "<PERSON>, Irish brewer, founded <PERSON> (b. 1725)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arthur Guinness\"><PERSON></a>, Irish brewer, founded <a href=\"https://wikipedia.org/wiki/Guinness\" title=\"Guinness\">Guinness</a> (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Guinness\" title=\"Arthur Guinness\"><PERSON></a>, Irish brewer, founded <a href=\"https://wikipedia.org/wiki/Guinness\" title=\"Guinness\">Guinness</a> (b. 1725)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Guinness", "link": "https://wikipedia.org/wiki/Guinness"}]}, {"year": "1805", "text": "<PERSON>, French engineer (b. 1763)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON> the Younger, English politician, Prime Minister of the United Kingdom (b. 1759)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1759)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1810", "text": "<PERSON>, German chemist and physicist (b. 1776)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and physicist (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and physicist (b. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, Scottish general and politician (b. 1764)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish general and politician (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish general and politician (b. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, Duke of Kent and Strathearn (b. 1767)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent_and_Strathearn\" title=\"Prince <PERSON>, Duke of Kent and Strathearn\">Prince <PERSON>, Duke of Kent and Strathearn</a> (b. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent_and_Strathearn\" title=\"Prince <PERSON>, Duke of Kent and Strathearn\">Prince <PERSON>, Duke of Kent and Strathearn</a> (b. 1767)", "links": [{"title": "<PERSON>, Duke of Kent and Strathearn", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent_and_<PERSON>rat<PERSON><PERSON>"}]}, {"year": "1833", "text": "<PERSON>, 1st Viscount Ex<PERSON>, English admiral and politician (b. 1757)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Exmouth\" title=\"<PERSON>, 1st Viscount Exmouth\"><PERSON>, 1st Viscount Ex<PERSON></a>, English admiral and politician (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Exmouth\" title=\"<PERSON>, 1st Viscount Exmouth\"><PERSON>, 1st Viscount Ex<PERSON></a>, English admiral and politician (b. 1757)", "links": [{"title": "<PERSON>, 1st Viscount Ex<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Exmouth"}]}, {"year": "1837", "text": "<PERSON>, Irish pianist and composer (b. 1782)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Irish pianist and composer (b. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Irish pianist and composer (b. 1782)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1866", "text": "<PERSON>, English author and poet (b. 1785)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (b. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, English priest and author (b. 1819)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, French engraver and illustrator (b. 1832)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French engraver and illustrator (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French engraver and illustrator (b. 1832)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Dor%C3%A9"}]}, {"year": "1893", "text": "<PERSON>, American lawyer and politician, 16th United States Secretary of the Interior (b. 1825)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>tus_Cincinnatus_Lamar_II\" class=\"mw-redirect\" title=\"Lucius Quintus Cincinnatus Lamar II\"><PERSON>us <PERSON></a>, American lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>tus_Cincinnatus_Lamar_II\" class=\"mw-redirect\" title=\"<PERSON> Quintus Cincinnatus Lamar II\"><PERSON>us <PERSON></a>, American lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_II"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1893", "text": "<PERSON>, Welsh physician, Chartist, and neo-Druid (b. 1800)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Welsh physician, Chartist, and neo-Druid (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Welsh physician, Chartist, and neo-Druid (b. 1800)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}]}, {"year": "1893", "text": "<PERSON>, Spanish poet and playwright (b. 1817)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Zor<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Zorrilla\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright (b. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>rilla"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Ukrainian composer and conductor (b. 1877)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian composer and conductor (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian composer and conductor (b. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Alsatian painter and draughtsman (b. 1886)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Alsatian painter and draughtsman (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Alsatian painter and draughtsman (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Beeh"}]}, {"year": "1922", "text": "<PERSON>, Hungarian conductor and academic (b. 1855)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian conductor and academic (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian conductor and academic (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Austrian physician and author (b. 1849)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and author (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and author (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>au"}]}, {"year": "1931", "text": "<PERSON>, Russian-English ballerina (b. 1881)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English ballerina (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English ballerina (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Italian physicist and politician (b. 1876)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian physicist and politician (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian physicist and politician (b. 1876)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Austrian footballer and manager (b. 1903)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor, playwright, and critic (b. 1887)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, playwright, and critic (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, playwright, and critic (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Norwegian painter and illustrator (b. 1863)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian painter and illustrator (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian painter and illustrator (b. 1863)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, French painter (b. 1867)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Hungarian-English director and producer (b. 1893)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English director and producer (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English director and producer (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Polish sculptor (b. 1908)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>s%C5%82<PERSON><PERSON>_(sculptor)\" title=\"<PERSON><PERSON><PERSON> (sculptor)\"><PERSON><PERSON><PERSON></a>, Polish sculptor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON><PERSON>%C5%82<PERSON><PERSON>_(sculptor)\" title=\"<PERSON><PERSON><PERSON> (sculptor)\"><PERSON><PERSON><PERSON></a>, Polish sculptor (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON><PERSON>%C5%82<PERSON><PERSON>_(sculptor)"}]}, {"year": "1966", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan lawyer and politician (b. 1895)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T. <PERSON>. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T. M. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician (b. 1895)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>nam"}]}, {"year": "1971", "text": "<PERSON>, Austrian-Brazilian chemist and academic (b. 1871)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Brazilian chemist and academic (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Brazilian chemist and academic (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American-Greek businessman (b. 1948)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Greek businessman (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Greek businessman (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American trombonist, composer, and bandleader (b. 1886)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, composer, and bandleader (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, composer, and bandleader (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor, singer, and activist (b. 1898)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and activist (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and activist (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American businessman, founded <PERSON>ts Shor's Restaurant (b. 1903)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Toots_Shor\" title=\"Toots Shor\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Toots_Shor%27s_Restaurant\" title=\"Toots Shor's Restaurant\">Toots Shor's Restaurant</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toots_Shor\" title=\"Toots Shor\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Toots_Shor%27s_Restaurant\" title=\"Toots Shor's Restaurant\">Toots Shor's Restaurant</a> (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toots_Shor"}, {"title": "Toots Shor's Restaurant", "link": "https://wikipedia.org/wiki/Toots_Shor%27s_Restaurant"}]}, {"year": "1978", "text": "<PERSON>, American guitarist and songwriter (b. 1946)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor (b. 1903)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Italian engineer (b. 1921)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American pianist and composer (b. 1910)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Barber\"><PERSON></a>, American pianist and composer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English cricketer and coach (b. 1908)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Palestinian-Egyptian poet and critic (b. 1926)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian-Egyptian poet and critic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian-Egyptian poet and critic (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American chef and cookbook author for whom the James Beard Foundation Awards are named (b. 1905)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and cookbook author for whom the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Foundation_Award\" title=\"James Beard Foundation Award\">James <PERSON> Foundation Awards</a> are named (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and cookbook author for whom the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Foundation_Award\" title=\"James Beard Foundation Award\">James Beard Foundation Awards</a> are named (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "James Beard Foundation Award", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Foundation_Award"}]}, {"year": "1986", "text": "<PERSON>, German sculptor and painter (b. 1921)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and painter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and painter (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American biochemist and academic (b. 1896)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, American biochemist and academic (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, American biochemist and academic (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Spanish painter and sculptor (b. 1904)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Salvador_Dal%C3%AD\" title=\"Salvador Dalí\"><PERSON></a>, Spanish painter and sculptor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Dal%C3%AD\" title=\"Salvador Dalí\"><PERSON></a>, Spanish painter and sculptor (b. 1904)", "links": [{"title": "Salvador Dalí", "link": "https://wikipedia.org/wiki/Salvador_Dal%C3%AD"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Swedish race car driver (b. 1961)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish race car driver (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish race car driver (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American guitarist and songwriter (b. 1952)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Canadian author and critic (b. 1912)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Northrop_Frye\" title=\"Northrop Frye\"><PERSON><PERSON> Frye</a>, Canadian author and critic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northrop_Frye\" title=\"Northrop Frye\"><PERSON><PERSON> Frye</a>, Canadian author and critic (b. 1912)", "links": [{"title": "Northrop Frye", "link": "https://wikipedia.org/wiki/Northrop_Frye"}]}, {"year": "1992", "text": "<PERSON>, American actor (b. 1924)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American soldier, author, and diplomat (b. 1925)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, author, and diplomat (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, author, and diplomat (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Russian field marshal (b. 1917)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian field marshal (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian field marshal (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English journalist and author (b. 1929)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Italian director and cinematographer (b. 1936)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_D%27Amato\" title=\"<PERSON>\"><PERSON></a>, Italian director and cinematographer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_D%27Amato\" title=\"<PERSON>\"><PERSON></a>, Italian director and cinematographer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joe_D%27Amato"}]}, {"year": "1999", "text": "<PERSON>, American businessman, co-founded the Hyatt Corporation (b. 1922)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Hyatt\" title=\"Hyatt\">Hyatt Corporation</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Hyatt\" title=\"Hyatt\">Hyatt Corporation</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hyatt", "link": "https://wikipedia.org/wiki/Hyatt"}]}, {"year": "2002", "text": "<PERSON>, American race car driver (b. 1934)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, French sociologist, anthropologist, and philosopher (b. 1930)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist, anthropologist, and philosopher (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist, anthropologist, and philosopher (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American philosopher, author, and academic (b. 1938)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, author, and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, author, and academic (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actress and singer (b. 1948)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American television personality and producer (b. 1927)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality and producer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality and producer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, German-Australian photographer (b. 1920)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian photographer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian photographer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, 4th Baron <PERSON>, English lieutenant and politician (b. 1921)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 4th Baron <PERSON>\"><PERSON><PERSON><PERSON>, 4th Baron <PERSON></a>, English lieutenant and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 4th Baron <PERSON>\"><PERSON><PERSON><PERSON>, 4th Baron <PERSON></a>, English lieutenant and politician (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>, 4th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_4th_Baron_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American talk show host, television personality, and producer (b. 1925)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host, television personality, and producer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host, television personality, and producer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Malaysian sociologist and politician (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian sociologist and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian sociologist and politician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American CIA officer (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "CIA", "link": "https://wikipedia.org/wiki/CIA"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Polish journalist and author (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%9Bci%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%9Bci%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and author (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON><PERSON>_<PERSON><PERSON>%C5%9Bci%C5%84ski"}]}, {"year": "2009", "text": "<PERSON>, American farmer and politician, 67th Governor of North Carolina (b. 1929)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician, 67th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician, 67th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of North Carolina", "link": "https://wikipedia.org/wiki/Governor_of_North_Carolina"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, American colonel and pilot (b. 1913)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel and pilot (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel and pilot (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rm<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American pianist and composer (b. 1915)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American fitness instructor, author, and television host (b. 1914)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fitness instructor, author, and television host (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fitness instructor, author, and television host (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and jurist (b. 1907)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American historian, author, and academic (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American businessman, co-founded October Films (b. 1954)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bingham Ray\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/October_Films\" title=\"October Films\">October Films</a> (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bingham Ray\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/October_Films\" title=\"October Films\">October Films</a> (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "October Films", "link": "https://wikipedia.org/wiki/October_Films"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Polish cardinal (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cardinal (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cardinal (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>p"}]}, {"year": "2013", "text": "<PERSON>, South African cricketer and referee (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer and referee (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer and referee (b. 1937)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French bishop (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Jean-<PERSON>%C3%A9<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French bishop (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jean-<PERSON>%C3%A9<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French bishop (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jean-<PERSON>%C3%A9<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Russian meteorologist and journalist (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian meteorologist and journalist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian meteorologist and journalist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Italian composer and conductor (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and conductor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and conductor (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American baseball player and coach (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Dutch activist, founded the Oud-Strijders Legioen (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Prosper_<PERSON>\" title=\"<PERSON>sper <PERSON>\"><PERSON><PERSON></a>, Dutch activist, founded the <a href=\"https://wikipedia.org/wiki/Oud-Strijders_Legioen\" title=\"Oud-Strijders Legioen\"><PERSON><PERSON>-Strijders Legioen</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sper_<PERSON>\" title=\"<PERSON>sper <PERSON>\"><PERSON><PERSON></a>, Dutch activist, founded the <a href=\"https://wikipedia.org/wiki/Oud-Strijders_Legioen\" title=\"Oud-Strijders Legioen\"><PERSON><PERSON>-Strijders Legioen</a> (b. 1927)", "links": [{"title": "Prosper <PERSON>", "link": "https://wikipedia.org/wiki/Prosper_<PERSON>go"}, {"title": "Oud-St<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oud-Strij<PERSON>_Legioen"}]}, {"year": "2015", "text": "<PERSON> of Saudi Arabia (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saudi_Arabia\" title=\"<PERSON> of Saudi Arabia\"><PERSON> of Saudi Arabia</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saudi_Arabia\" title=\"<PERSON> of Saudi Arabia\"><PERSON> of Saudi Arabia</a> (b. 1924)", "links": [{"title": "<PERSON> of Saudi Arabia", "link": "https://wikipedia.org/wiki/<PERSON>_of_Saudi_Arabia"}]}, {"year": "2016", "text": "<PERSON>, Scottish bassist (b. 1947)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bassist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bassist (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American basketball player and coach (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American singer, songwriter and record producer (b. 1940)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and record producer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and record producer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, English actor (b. 1941)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor (b. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, South African trumpeter, composer and singer (b. 1939)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African trumpeter, composer and singer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African trumpeter, composer and singer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, Chilean poet (b. 1914)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Nicanor_<PERSON>rra\" title=\"<PERSON><PERSON><PERSON> Parra\"><PERSON><PERSON><PERSON></a>, Chilean poet (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean poet (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>r_Parra"}]}, {"year": "2018", "text": "<PERSON>, American civil rights activist and pastor (b. 1928)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist and pastor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist and pastor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, Singaporean actor (b. 1990)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Singaporean actor (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Singaporean actor (b. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Zimbabwean Afro Jazz musician (b. 1952)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean Afro Jazz musician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean Afro Jazz musician (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actor and director (b. 1925)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American journalist and talk show host (b. 1933)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, South Korean actress and model (b. 1994)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-jung\" title=\"<PERSON> Yoo-jung\"><PERSON></a>, South Korean actress and model (b. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-jung\" title=\"<PERSON> Yoo-jung\"><PERSON></a>, South Korean actress and model (b. 1994)", "links": [{"title": "<PERSON>jung", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-jung"}]}, {"year": "2024", "text": "<PERSON>, American radio and television commentator, writer and musician (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television commentator, writer and musician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television commentator, writer and musician (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American Emmy winning singer-songwriter (b. 1947)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American Emmy winning singer-songwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American Emmy winning singer-songwriter (b. 1947)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}]}}