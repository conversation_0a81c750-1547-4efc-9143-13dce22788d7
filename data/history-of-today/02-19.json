{"date": "February 19", "url": "https://wikipedia.org/wiki/February_19", "data": {"Events": [{"year": "197", "text": "Emperor <PERSON><PERSON><PERSON> defeats usurper <PERSON><PERSON><PERSON> in the Battle of Lugdunum, the bloodiest battle between Roman armies.", "html": "197 - Emperor <a href=\"https://wikipedia.org/wiki/Septimius_Severus\" title=\"Septimius Severus\">Septimius Severus</a> defeats <a href=\"https://wikipedia.org/wiki/Roman_usurper\" title=\"Roman usurper\">usurper</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>binus\" title=\"<PERSON><PERSON><PERSON> Albinus\"><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Lugdunum\" title=\"Battle of Lugdunum\">Battle of Lugdunum</a>, the bloodiest battle between Roman armies.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/Septimius_Severus\" title=\"Septimius Severus\">Septimius Severus</a> defeats <a href=\"https://wikipedia.org/wiki/Roman_usurper\" title=\"Roman usurper\">usurper</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Albinus\" title=\"<PERSON><PERSON><PERSON> Albinus\"><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Lugdunum\" title=\"Battle of Lugdunum\">Battle of Lugdunum</a>, the bloodiest battle between Roman armies.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Septimius_Severus"}, {"title": "Roman usurper", "link": "https://wikipedia.org/wiki/Roman_usurper"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Battle of Lugdunum", "link": "https://wikipedia.org/wiki/Battle_of_Lugdunum"}]}, {"year": "356", "text": "The anti-paganism policy of <PERSON><PERSON><PERSON> forbids the worship of pagan idols in the Roman Empire.", "html": "356 - The <a href=\"https://wikipedia.org/wiki/Anti-paganism_policy_of_Constantius_II\" class=\"mw-redirect\" title=\"Anti-paganism policy of <PERSON><PERSON><PERSON> II\">anti-paganism policy of <PERSON><PERSON><PERSON> II</a> forbids the worship of pagan idols in the Roman Empire.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Anti-paganism_policy_of_Constantius_II\" class=\"mw-redirect\" title=\"Anti-paganism policy of <PERSON><PERSON><PERSON> II\">anti-paganism policy of <PERSON><PERSON><PERSON> II</a> forbids the worship of pagan idols in the Roman Empire.", "links": [{"title": "Anti-paganism policy of <PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/Anti-paganism_policy_of_<PERSON><PERSON><PERSON>_II"}]}, {"year": "1594", "text": "Having already been elected to the throne of the Polish-Lithuanian Commonwealth in 1587, <PERSON><PERSON><PERSON> of the House of Vasa is crowned King of Sweden, having succeeded his father <PERSON> III of Sweden in 1592.", "html": "1594 - Having already been elected to the throne of the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a> in 1587, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_Vasa\" title=\"<PERSON><PERSON><PERSON> III Vasa\"><PERSON><PERSON><PERSON> III</a> of the <a href=\"https://wikipedia.org/wiki/House_of_Vasa\" title=\"House of Vasa\"><PERSON> of Vasa</a> is crowned King of Sweden, having succeeded his father <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> III of Sweden\"><PERSON> of Sweden</a> in 1592.", "no_year_html": "Having already been elected to the throne of the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a> in 1587, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_Vasa\" title=\"<PERSON><PERSON><PERSON> III Vasa\"><PERSON><PERSON><PERSON> III</a> of the <a href=\"https://wikipedia.org/wiki/House_of_Vasa\" title=\"House of Vasa\"><PERSON> of Vasa</a> is crowned King of Sweden, having succeeded his father <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> III of Sweden\"><PERSON> of Sweden</a> in 1592.", "links": [{"title": "Polish-Lithuanian Commonwealth", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "House of Vasa", "link": "https://wikipedia.org/wiki/House_of_Vasa"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1600", "text": "The Peruvian stratovolcano Huaynaputina explodes in the most violent eruption in the recorded history of South America.", "html": "1600 - The Peruvian <a href=\"https://wikipedia.org/wiki/Stratovolcano\" title=\"Stratovolcano\">stratovolcano</a> <a href=\"https://wikipedia.org/wiki/Huaynaputina\" title=\"Huaynaputina\">Huaynaputina</a> explodes in the most violent eruption in the recorded history of South America.", "no_year_html": "The Peruvian <a href=\"https://wikipedia.org/wiki/Stratovolcano\" title=\"Stratovolcano\">stratovolcano</a> <a href=\"https://wikipedia.org/wiki/Huaynaputina\" title=\"Huaynaputina\">Huaynaputina</a> explodes in the most violent eruption in the recorded history of South America.", "links": [{"title": "Stratovolcano", "link": "https://wikipedia.org/wiki/Stratovolcano"}, {"title": "Huaynaputi<PERSON>", "link": "https://wikipedia.org/wiki/Huaynaputina"}]}, {"year": "1649", "text": "The Second Battle of Guararapes takes place, effectively ending Dutch colonization efforts in Brazil.", "html": "1649 - The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Guararapes\" title=\"Second Battle of Guararapes\">Second Battle of Guararapes</a> takes place, effectively ending Dutch colonization efforts in Brazil.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Guararapes\" title=\"Second Battle of Guararapes\">Second Battle of Guararapes</a> takes place, effectively ending Dutch colonization efforts in Brazil.", "links": [{"title": "Second Battle of Guararapes", "link": "https://wikipedia.org/wiki/Second_Battle_of_Guararapes"}]}, {"year": "1674", "text": "England and the Netherlands sign the Treaty of Westminster, ending the Third Anglo-Dutch War. A provision of the agreement transfers the Dutch colony of New Amsterdam to England.", "html": "1674 - England and the Netherlands sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Westminster_(1674)\" title=\"Treaty of Westminster (1674)\">Treaty of Westminster</a>, ending the <a href=\"https://wikipedia.org/wiki/Third_Anglo-Dutch_War\" title=\"Third Anglo-Dutch War\">Third Anglo-Dutch War</a>. A provision of the agreement transfers the Dutch colony of <a href=\"https://wikipedia.org/wiki/New_Amsterdam\" title=\"New Amsterdam\">New Amsterdam</a> to England.", "no_year_html": "England and the Netherlands sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Westminster_(1674)\" title=\"Treaty of Westminster (1674)\">Treaty of Westminster</a>, ending the <a href=\"https://wikipedia.org/wiki/Third_Anglo-Dutch_War\" title=\"Third Anglo-Dutch War\">Third Anglo-Dutch War</a>. A provision of the agreement transfers the Dutch colony of <a href=\"https://wikipedia.org/wiki/New_Amsterdam\" title=\"New Amsterdam\">New Amsterdam</a> to England.", "links": [{"title": "Treaty of Westminster (1674)", "link": "https://wikipedia.org/wiki/Treaty_of_Westminster_(1674)"}, {"title": "Third Anglo-Dutch War", "link": "https://wikipedia.org/wiki/Third_Anglo-Dutch_War"}, {"title": "New Amsterdam", "link": "https://wikipedia.org/wiki/New_Amsterdam"}]}, {"year": "1714", "text": "Great Northern War: The battle of Napue between Sweden and Russia is fought in Isokyrö, Ostrobothnia.", "html": "1714 - <a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Napue\" title=\"Battle of Napue\">battle of Napue</a> between <a href=\"https://wikipedia.org/wiki/Swedish_Empire\" title=\"Swedish Empire\">Sweden</a> and <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Russia</a> is fought in <a href=\"https://wikipedia.org/wiki/Isokyr%C3%B6\" title=\"Isokyrö\">Isokyrö</a>, <a href=\"https://wikipedia.org/wiki/Ostrobothnia_(historical_province)\" title=\"Ostrobothnia (historical province)\">Ostrobothnia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Napue\" title=\"Battle of Napue\">battle of Napue</a> between <a href=\"https://wikipedia.org/wiki/Swedish_Empire\" title=\"Swedish Empire\">Sweden</a> and <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Russia</a> is fought in <a href=\"https://wikipedia.org/wiki/Isokyr%C3%B6\" title=\"Isokyrö\">Isokyrö</a>, <a href=\"https://wikipedia.org/wiki/Ostrobothnia_(historical_province)\" title=\"Ostrobothnia (historical province)\">Ostrobothnia</a>.", "links": [{"title": "Great Northern War", "link": "https://wikipedia.org/wiki/Great_Northern_War"}, {"title": "Battle of Napue", "link": "https://wikipedia.org/wiki/Battle_of_Napue"}, {"title": "Swedish Empire", "link": "https://wikipedia.org/wiki/Swedish_Empire"}, {"title": "Tsardom of Russia", "link": "https://wikipedia.org/wiki/Tsardom_of_Russia"}, {"title": "Isokyrö", "link": "https://wikipedia.org/wiki/Isokyr%C3%B6"}, {"title": "Ostrobothnia (historical province)", "link": "https://wikipedia.org/wiki/Ostrobothnia_(historical_province)"}]}, {"year": "1726", "text": "The Supreme Privy Council is established in Russia.", "html": "1726 - The <a href=\"https://wikipedia.org/wiki/Supreme_Privy_Council\" title=\"Supreme Privy Council\">Supreme Privy Council</a> is established in Russia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Privy_Council\" title=\"Supreme Privy Council\">Supreme Privy Council</a> is established in Russia.", "links": [{"title": "Supreme Privy Council", "link": "https://wikipedia.org/wiki/Supreme_Privy_Council"}]}, {"year": "1807", "text": "Former Vice President of the United States <PERSON> is arrested for treason in Wakefield, Alabama, and confined to Fort Stoddert.", "html": "1807 - Former <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested for treason in <a href=\"https://wikipedia.org/wiki/Wakefield,_Alabama\" title=\"Wakefield, Alabama\">Wakefield, Alabama</a>, and confined to <a href=\"https://wikipedia.org/wiki/Fort_Stoddert\" title=\"Fort Stoddert\">Fort Stoddert</a>.", "no_year_html": "Former <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested for treason in <a href=\"https://wikipedia.org/wiki/Wakefield,_Alabama\" title=\"Wakefield, Alabama\">Wakefield, Alabama</a>, and confined to <a href=\"https://wikipedia.org/wiki/Fort_Stoddert\" title=\"Fort Stoddert\">Fort Stoddert</a>.", "links": [{"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wakefield, Alabama", "link": "https://wikipedia.org/wiki/Wakefield,_Alabama"}, {"title": "Fort Stoddert", "link": "https://wikipedia.org/wiki/Fort_Stoddert"}]}, {"year": "1819", "text": "British explorer <PERSON> discovers the South Shetland Islands.", "html": "1819 - <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> explorer <a href=\"https://wikipedia.org/wiki/<PERSON>(mariner)\" title=\"<PERSON> (mariner)\"><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/South_Shetland_Islands\" title=\"South Shetland Islands\">South Shetland Islands</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> explorer <a href=\"https://wikipedia.org/wiki/<PERSON>(mariner)\" title=\"<PERSON> (mariner)\"><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/South_Shetland_Islands\" title=\"South Shetland Islands\">South Shetland Islands</a>.", "links": [{"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}, {"title": "<PERSON> (mariner)", "link": "https://wikipedia.org/wiki/<PERSON>_(mariner)"}, {"title": "South Shetland Islands", "link": "https://wikipedia.org/wiki/South_Shetland_Islands"}]}, {"year": "1836", "text": "King <PERSON> signs Letters Patent establishing the province of South Australia.", "html": "1836 - King <a href=\"https://wikipedia.org/wiki/William_IV_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"William IV of the United Kingdom\"><PERSON> IV</a> signs Letters Patent establishing the province of <a href=\"https://wikipedia.org/wiki/South_Australia\" title=\"South Australia\">South Australia</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"William IV of the United Kingdom\"><PERSON> IV</a> signs Letters Patent establishing the province of <a href=\"https://wikipedia.org/wiki/South_Australia\" title=\"South Australia\">South Australia</a>.", "links": [{"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom"}, {"title": "South Australia", "link": "https://wikipedia.org/wiki/South_Australia"}]}, {"year": "1846", "text": "In Austin, Texas, the newly formed Texas state government is officially installed. The Republic of Texas government officially transfers power to the State of Texas government following the annexation of Texas by the United States.", "html": "1846 - In <a href=\"https://wikipedia.org/wiki/Austin,_Texas\" title=\"Austin, Texas\">Austin, Texas</a>, the newly formed Texas state government is officially installed. The <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a> government officially transfers power to the <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">State of Texas</a> government following the <a href=\"https://wikipedia.org/wiki/Annexation\" title=\"Annexation\">annexation</a> of Texas by the United States.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Austin,_Texas\" title=\"Austin, Texas\">Austin, Texas</a>, the newly formed Texas state government is officially installed. The <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a> government officially transfers power to the <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">State of Texas</a> government following the <a href=\"https://wikipedia.org/wiki/Annexation\" title=\"Annexation\">annexation</a> of Texas by the United States.", "links": [{"title": "Austin, Texas", "link": "https://wikipedia.org/wiki/Austin,_Texas"}, {"title": "Republic of Texas", "link": "https://wikipedia.org/wiki/Republic_of_Texas"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "Annexation", "link": "https://wikipedia.org/wiki/Annexation"}]}, {"year": "1847", "text": "The first group of rescuers reaches the Donner Party.", "html": "1847 - The first group of rescuers reaches the <a href=\"https://wikipedia.org/wiki/Donner_Party\" title=\"Donner Party\">Donner Party</a>.", "no_year_html": "The first group of rescuers reaches the <a href=\"https://wikipedia.org/wiki/Donner_Party\" title=\"Donner Party\">Donner Party</a>.", "links": [{"title": "Donner Party", "link": "https://wikipedia.org/wiki/<PERSON>ner_Party"}]}, {"year": "1878", "text": "Thomas Edison patents the phonograph.", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Edison\"><PERSON></a> patents the <a href=\"https://wikipedia.org/wiki/Phonograph\" title=\"Phonograph\">phonograph</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_Edison\" title=\"Thomas Edison\"><PERSON></a> patents the <a href=\"https://wikipedia.org/wiki/Phonograph\" title=\"Phonograph\">phonograph</a>.", "links": [{"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Phonograph", "link": "https://wikipedia.org/wiki/Phonograph"}]}, {"year": "1884", "text": "More than sixty tornadoes strike the Southern United States, one of the largest tornado outbreaks in U.S. history.", "html": "1884 - More than <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_February_19%E2%80%9320,_1884\" class=\"mw-redirect\" title=\"Tornado outbreak of February 19-20, 1884\">sixty tornadoes</a> strike the <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">Southern United States</a>, one of the largest <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> outbreaks in U.S. history.", "no_year_html": "More than <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_February_19%E2%80%9320,_1884\" class=\"mw-redirect\" title=\"Tornado outbreak of February 19-20, 1884\">sixty tornadoes</a> strike the <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">Southern United States</a>, one of the largest <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> outbreaks in U.S. history.", "links": [{"title": "Tornado outbreak of February 19-20, 1884", "link": "https://wikipedia.org/wiki/Tornado_outbreak_of_February_19%E2%80%9320,_1884"}, {"title": "Southern United States", "link": "https://wikipedia.org/wiki/Southern_United_States"}, {"title": "Tornado", "link": "https://wikipedia.org/wiki/Tornado"}]}, {"year": "1913", "text": "<PERSON> becomes President of Mexico for 45 minutes; this is the shortest term to date of any person as president of any country.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1in\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> for 45 minutes; this is the shortest term to date of any person as president of any country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A1in\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> for 45 minutes; this is the shortest term to date of any person as president of any country.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Lascur%C3%A1in"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1915", "text": "World War I: The first naval attack on the Dardanelles begins when a strong Anglo-French task force bombards Ottoman artillery along the coast of Gallipoli.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Naval_operations_in_the_Dardanelles_Campaign\" class=\"mw-redirect\" title=\"Naval operations in the Dardanelles Campaign\">first naval attack</a> on the <a href=\"https://wikipedia.org/wiki/Dardanelles\" title=\"Dardanelles\">Dardanelles</a> begins when a strong Anglo-French task force bombards Ottoman artillery along the coast of <a href=\"https://wikipedia.org/wiki/Gallipoli\" title=\"Gallipoli\">Gallipoli</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Naval_operations_in_the_Dardanelles_Campaign\" class=\"mw-redirect\" title=\"Naval operations in the Dardanelles Campaign\">first naval attack</a> on the <a href=\"https://wikipedia.org/wiki/Dardanelles\" title=\"Dardanelles\">Dardanelles</a> begins when a strong Anglo-French task force bombards Ottoman artillery along the coast of <a href=\"https://wikipedia.org/wiki/Gallipoli\" title=\"Gallipoli\">Gallipoli</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Naval operations in the Dardanelles Campaign", "link": "https://wikipedia.org/wiki/Naval_operations_in_the_Dardanelles_Campaign"}, {"title": "Dardanelles", "link": "https://wikipedia.org/wiki/Dardanelles"}, {"title": "Gallipoli", "link": "https://wikipedia.org/wiki/Gallipoli"}]}, {"year": "1937", "text": "Yekatit 12: During a public ceremony at the Viceregal Palace (the former Imperial residence) in Addis Ababa, Ethiopia, two Ethiopian nationalists of Eritrean origin attempt to kill viceroy <PERSON><PERSON><PERSON> with a number of grenades.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Yekatit_12\" title=\"Yekatit 12\">Yekatit 12</a>: During a public ceremony at the Viceregal Palace (the former Imperial residence) in <a href=\"https://wikipedia.org/wiki/Addis_Ababa\" title=\"Addis Ababa\">Addis Ababa</a>, Ethiopia, two Ethiopian nationalists of <a href=\"https://wikipedia.org/wiki/Eritrea\" title=\"Eritrea\">Eritrean</a> origin attempt to kill viceroy <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> with a number of grenades.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yekatit_12\" title=\"Yekatit 12\">Yekatit 12</a>: During a public ceremony at the Viceregal Palace (the former Imperial residence) in <a href=\"https://wikipedia.org/wiki/Addis_Ababa\" title=\"Addis Ababa\">Addis Ababa</a>, Ethiopia, two Ethiopian nationalists of <a href=\"https://wikipedia.org/wiki/Eritrea\" title=\"Eritrea\">Eritrean</a> origin attempt to kill viceroy <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> with a number of grenades.", "links": [{"title": "Yekatit 12", "link": "https://wikipedia.org/wiki/Yekatit_12"}, {"title": "Addis A<PERSON>ba", "link": "https://wikipedia.org/wiki/Addis_Ababa"}, {"title": "Eritrea", "link": "https://wikipedia.org/wiki/Eritrea"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "World War II: Nearly 250 Japanese warplanes attack the northern Australian city of Darwin, killing 243 people.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Nearly 250 Japanese warplanes <a href=\"https://wikipedia.org/wiki/Bombing_of_Darwin\" title=\"Bombing of Darwin\">attack</a> the northern Australian city of <a href=\"https://wikipedia.org/wiki/Darwin,_Northern_Territory\" title=\"Darwin, Northern Territory\">Darwin</a>, killing 243 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Nearly 250 Japanese warplanes <a href=\"https://wikipedia.org/wiki/Bombing_of_Darwin\" title=\"Bombing of Darwin\">attack</a> the northern Australian city of <a href=\"https://wikipedia.org/wiki/Darwin,_Northern_Territory\" title=\"Darwin, Northern Territory\">Darwin</a>, killing 243 people.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Bombing of Darwin", "link": "https://wikipedia.org/wiki/Bombing_of_Darwin"}, {"title": "Darwin, Northern Territory", "link": "https://wikipedia.org/wiki/Darwin,_Northern_Territory"}]}, {"year": "1942", "text": "World War II: United States President <PERSON> signs executive order 9066, allowing the United States military to relocate Japanese Americans to internment camps.", "html": "1942 - World War II: United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs <a href=\"https://wikipedia.org/wiki/Executive_Order_9066\" title=\"Executive Order 9066\">executive order 9066</a>, allowing the United States military to relocate <a href=\"https://wikipedia.org/wiki/Japanese_Americans\" title=\"Japanese Americans\">Japanese Americans</a> to <a href=\"https://wikipedia.org/wiki/Internment_of_Japanese_Americans\" title=\"Internment of Japanese Americans\">internment camps</a>.", "no_year_html": "World War II: United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs <a href=\"https://wikipedia.org/wiki/Executive_Order_9066\" title=\"Executive Order 9066\">executive order 9066</a>, allowing the United States military to relocate <a href=\"https://wikipedia.org/wiki/Japanese_Americans\" title=\"Japanese Americans\">Japanese Americans</a> to <a href=\"https://wikipedia.org/wiki/Internment_of_Japanese_Americans\" title=\"Internment of Japanese Americans\">internment camps</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Executive Order 9066", "link": "https://wikipedia.org/wiki/Executive_Order_9066"}, {"title": "Japanese Americans", "link": "https://wikipedia.org/wiki/Japanese_Americans"}, {"title": "Internment of Japanese Americans", "link": "https://wikipedia.org/wiki/Internment_of_Japanese_Americans"}]}, {"year": "1943", "text": "World War II: Battle of Kasserine Pass in Tunisia begins.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Kasserine_Pass\" title=\"Battle of Kasserine Pass\">Battle of Kasserine Pass</a> in <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a> begins.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Kasserine_Pass\" title=\"Battle of Kasserine Pass\">Battle of Kasserine Pass</a> in <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a> begins.", "links": [{"title": "Battle of Kasserine Pass", "link": "https://wikipedia.org/wiki/Battle_of_Kasserine_Pass"}, {"title": "Tunisia", "link": "https://wikipedia.org/wiki/Tunisia"}]}, {"year": "1945", "text": "World War II: Battle of Iwo Jima: About 30,000 United States Marines land on the island of Iwo Jima.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a>: About 30,000 <a href=\"https://wikipedia.org/wiki/United_States_Marine\" class=\"mw-redirect\" title=\"United States Marine\">United States Marines</a> land on the island of <a href=\"https://wikipedia.org/wiki/Iwo_Jima\" title=\"Iwo Jima\">Iwo Jima</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a>: About 30,000 <a href=\"https://wikipedia.org/wiki/United_States_Marine\" class=\"mw-redirect\" title=\"United States Marine\">United States Marines</a> land on the island of <a href=\"https://wikipedia.org/wiki/Iwo_Jima\" title=\"Iwo Jima\">Iwo Jima</a>.", "links": [{"title": "Battle of Iwo Jima", "link": "https://wikipedia.org/wiki/Battle_of_Iwo_Jima"}, {"title": "United States Marine", "link": "https://wikipedia.org/wiki/United_States_Marine"}, {"title": "Iwo Jima", "link": "https://wikipedia.org/wiki/Iwo_<PERSON>a"}]}, {"year": "1948", "text": "The Conference of Youth and Students of Southeast Asia Fighting for Freedom and Independence convenes in Calcutta.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Conference_of_Youth_and_Students_of_Southeast_Asia_Fighting_for_Freedom_and_Independence\" title=\"Conference of Youth and Students of Southeast Asia Fighting for Freedom and Independence\">Conference of Youth and Students of Southeast Asia Fighting for Freedom and Independence</a> convenes in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Conference_of_Youth_and_Students_of_Southeast_Asia_Fighting_for_Freedom_and_Independence\" title=\"Conference of Youth and Students of Southeast Asia Fighting for Freedom and Independence\">Conference of Youth and Students of Southeast Asia Fighting for Freedom and Independence</a> convenes in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>.", "links": [{"title": "Conference of Youth and Students of Southeast Asia Fighting for Freedom and Independence", "link": "https://wikipedia.org/wiki/Conference_of_Youth_and_Students_of_Southeast_Asia_Fighting_for_Freedom_and_Independence"}, {"title": "Kolkata", "link": "https://wikipedia.org/wiki/Kolkata"}]}, {"year": "1949", "text": "<PERSON> is awarded the first Bo<PERSON>en Prize in poetry by the Bollingen Foundation and Yale University.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ezra Pound\"><PERSON></a> is awarded the first <a href=\"https://wikipedia.org/wiki/Bollingen_Prize\" title=\"Bollingen Prize\">Bollingen Prize in poetry</a> by the <a href=\"https://wikipedia.org/wiki/Bollingen_Foundation\" title=\"Bollingen Foundation\">Bollingen Foundation</a> and <a href=\"https://wikipedia.org/wiki/Yale_University\" title=\"Yale University\">Yale University</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ezra Pound\"><PERSON></a> is awarded the first <a href=\"https://wikipedia.org/wiki/Bollingen_Prize\" title=\"Bollingen Prize\">Bollingen Prize in poetry</a> by the <a href=\"https://wikipedia.org/wiki/Bollingen_Foundation\" title=\"Bollingen Foundation\">Bollingen Foundation</a> and <a href=\"https://wikipedia.org/wiki/Yale_University\" title=\"Yale University\">Yale University</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bollingen Prize", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Prize"}, {"title": "Bollingen Foundation", "link": "https://wikipedia.org/wiki/Bollingen_Foundation"}, {"title": "Yale University", "link": "https://wikipedia.org/wiki/Yale_University"}]}, {"year": "1953", "text": "Book censorship in the United States: The Georgia Literature Commission is established.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Book_censorship_in_the_United_States\" title=\"Book censorship in the United States\">Book censorship in the United States</a>: The Georgia Literature Commission is established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Book_censorship_in_the_United_States\" title=\"Book censorship in the United States\">Book censorship in the United States</a>: The Georgia Literature Commission is established.", "links": [{"title": "Book censorship in the United States", "link": "https://wikipedia.org/wiki/Book_censorship_in_the_United_States"}]}, {"year": "1954", "text": "Transfer of Crimea: The Soviet Politburo of the Soviet Union orders the transfer of the Crimean Oblast from the Russian SFSR to the Ukrainian SSR.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/1954_transfer_of_Crimea\" class=\"mw-redirect\" title=\"1954 transfer of Crimea\">Transfer of Crimea</a>: The <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Politburo of the Communist Party of the Soviet Union\">Soviet Politburo</a> of the Soviet Union orders the transfer of the <a href=\"https://wikipedia.org/wiki/Crimean_Oblast\" class=\"mw-redirect\" title=\"Crimean Oblast\">Crimean Oblast</a> from the <a href=\"https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic\" title=\"Russian Soviet Federative Socialist Republic\">Russian SFSR</a> to the <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian SSR</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1954_transfer_of_Crimea\" class=\"mw-redirect\" title=\"1954 transfer of Crimea\">Transfer of Crimea</a>: The <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Politburo of the Communist Party of the Soviet Union\">Soviet Politburo</a> of the Soviet Union orders the transfer of the <a href=\"https://wikipedia.org/wiki/Crimean_Oblast\" class=\"mw-redirect\" title=\"Crimean Oblast\">Crimean Oblast</a> from the <a href=\"https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic\" title=\"Russian Soviet Federative Socialist Republic\">Russian SFSR</a> to the <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian SSR</a>.", "links": [{"title": "1954 transfer of Crimea", "link": "https://wikipedia.org/wiki/1954_transfer_of_Crimea"}, {"title": "Politburo of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union"}, {"title": "Crimean Oblast", "link": "https://wikipedia.org/wiki/Crimean_Oblast"}, {"title": "Russian Soviet Federative Socialist Republic", "link": "https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic"}, {"title": "Ukrainian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic"}]}, {"year": "1959", "text": "The United Kingdom grants Cyprus independence, which is formally proclaimed on August 16, 1960.", "html": "1959 - The United Kingdom grants <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> independence, which is formally proclaimed on August 16, 1960.", "no_year_html": "The United Kingdom grants <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> independence, which is formally proclaimed on August 16, 1960.", "links": [{"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}]}, {"year": "1960", "text": "China successfully launches the T-7, its first sounding rocket.", "html": "1960 - China successfully launches the <a href=\"https://wikipedia.org/wiki/T-7_(rocket)\" title=\"T-7 (rocket)\">T-7</a>, its first <a href=\"https://wikipedia.org/wiki/Sounding_rocket\" title=\"Sounding rocket\">sounding rocket</a>.", "no_year_html": "China successfully launches the <a href=\"https://wikipedia.org/wiki/T-7_(rocket)\" title=\"T-7 (rocket)\">T-7</a>, its first <a href=\"https://wikipedia.org/wiki/Sounding_rocket\" title=\"Sounding rocket\">sounding rocket</a>.", "links": [{"title": "T-7 (rocket)", "link": "https://wikipedia.org/wiki/T-7_(rocket)"}, {"title": "Sounding rocket", "link": "https://wikipedia.org/wiki/Sounding_rocket"}]}, {"year": "1963", "text": "The publication of <PERSON>'s The Feminine Mystique reawakens the feminist movement in the United States as women's organizations and consciousness raising groups spread.", "html": "1963 - The publication of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/The_Feminine_Mystique\" title=\"The Feminine Mystique\">The Feminine Mystique</a></i> reawakens the <a href=\"https://wikipedia.org/wiki/Feminist_movement\" title=\"Feminist movement\">feminist movement</a> in the United States as women's organizations and <a href=\"https://wikipedia.org/wiki/Consciousness_raising\" title=\"Consciousness raising\">consciousness raising</a> groups spread.", "no_year_html": "The publication of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/The_Feminine_Mystique\" title=\"The Feminine Mystique\">The Feminine Mystique</a></i> reawakens the <a href=\"https://wikipedia.org/wiki/Feminist_movement\" title=\"Feminist movement\">feminist movement</a> in the United States as women's organizations and <a href=\"https://wikipedia.org/wiki/Consciousness_raising\" title=\"Consciousness raising\">consciousness raising</a> groups spread.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Feminine Mystique", "link": "https://wikipedia.org/wiki/The_Feminine_Mystique"}, {"title": "Feminist movement", "link": "https://wikipedia.org/wiki/Feminist_movement"}, {"title": "Consciousness raising", "link": "https://wikipedia.org/wiki/Consciousness_raising"}]}, {"year": "1965", "text": "Colonel <PERSON><PERSON><PERSON> of the Army of the Republic of Vietnam, and a communist spy of the North Vietnamese Viet Minh, along with Generals <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, all Catholics, attempt a coup against the military junta of the Buddhist <PERSON><PERSON>.", "html": "1965 - Colonel <a href=\"https://wikipedia.org/wiki/Ph%E1%BA%A1m_Ng%E1%BB%8Dc_Th%E1%BA%A3o\" title=\"<PERSON>ạ<PERSON> Ngọc <PERSON>hảo\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">Army of the Republic of Vietnam</a>, and a communist spy of the <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\">Viet Minh</a>, along with Generals <a href=\"https://wikipedia.org/wiki/L%C3%A2m_V%C4%83n_Ph%C3%A1t\" title=\"Lâm Văn Phát\"><PERSON>â<PERSON></a> and <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Thi%E1%BB%87n_Khi%C3%AAm\" title=\"Trần Thiện Khiêm\">Trần Thiện Khiêm</a>, all Catholics, <a href=\"https://wikipedia.org/wiki/1965_South_Vietnamese_coup\" title=\"1965 South Vietnamese coup\">attempt a coup</a> against the military <a href=\"https://wikipedia.org/wiki/Military_dictatorship\" title=\"Military dictatorship\">junta</a> of the Buddhist <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"Nguyễn Khánh\">Nguyễn Khánh</a>.", "no_year_html": "Colonel <a href=\"https://wikipedia.org/wiki/Ph%E1%BA%A1m_Ng%E1%BB%8Dc_Th%E1%BA%A3o\" title=\"<PERSON>ạ<PERSON> Ngọc Thảo\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">Army of the Republic of Vietnam</a>, and a communist spy of the <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\">V<PERSON> Minh</a>, along with Generals <a href=\"https://wikipedia.org/wiki/L%C3%A2m_V%C4%83n_Ph%C3%A1t\" title=\"Lâm Văn Phát\">Lâ<PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Thi%E1%BB%87n_Khi%C3%AAm\" title=\"Trần Thiện Khiêm\">Trần <PERSON>n Khiêm</a>, all Catholics, <a href=\"https://wikipedia.org/wiki/1965_South_Vietnamese_coup\" title=\"1965 South Vietnamese coup\">attempt a coup</a> against the military <a href=\"https://wikipedia.org/wiki/Military_dictatorship\" title=\"Military dictatorship\">junta</a> of the Buddhist <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"Nguyễn Khánh\">Nguyễn Khánh</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ph%E1%BA%A1m_Ng%E1%BB%8Dc_Th%E1%BA%A3o"}, {"title": "Army of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viet_Minh"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A2m_V%C4%83n_Ph%C3%A1t"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_Thi%E1%BB%87n_Khi%C3%AAm"}, {"title": "1965 South Vietnamese coup", "link": "https://wikipedia.org/wiki/1965_South_Vietnamese_coup"}, {"title": "Military dictatorship", "link": "https://wikipedia.org/wiki/Military_dictatorship"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh"}]}, {"year": "1976", "text": "Executive Order 9066, which led to the relocation of Japanese Americans to internment camps, is rescinded by President <PERSON>'s Proclamation 4417.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Executive_Order_9066\" title=\"Executive Order 9066\">Executive Order 9066</a>, which led to the relocation of Japanese Americans to internment camps, is rescinded by President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s Proclamation 4417.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Executive_Order_9066\" title=\"Executive Order 9066\">Executive Order 9066</a>, which led to the relocation of Japanese Americans to internment camps, is rescinded by President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s Proclamation 4417.", "links": [{"title": "Executive Order 9066", "link": "https://wikipedia.org/wiki/Executive_Order_9066"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "Egyptian forces raid Larnaca International Airport in an attempt to intervene in a hijacking, without authorisation from the Republic of Cyprus authorities. The Cypriot National Guard and Police forces kill 15 Egyptian commandos and destroy the Egyptian C-130 transport plane in open combat.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Egyptian_raid_on_Larnaca_International_Airport\" title=\"Egyptian raid on Larnaca International Airport\">Egyptian forces raid Larnaca International Airport</a> in an attempt to intervene in a hijacking, without authorisation from the <a href=\"https://wikipedia.org/wiki/Republic_of_Cyprus\" class=\"mw-redirect\" title=\"Republic of Cyprus\">Republic of Cyprus</a> authorities. The <a href=\"https://wikipedia.org/wiki/Cypriot_National_Guard\" title=\"Cypriot National Guard\">Cypriot National Guard</a> and Police forces kill 15 Egyptian commandos and destroy the Egyptian <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">C-130</a> transport plane in open combat.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egyptian_raid_on_Larnaca_International_Airport\" title=\"Egyptian raid on Larnaca International Airport\">Egyptian forces raid Larnaca International Airport</a> in an attempt to intervene in a hijacking, without authorisation from the <a href=\"https://wikipedia.org/wiki/Republic_of_Cyprus\" class=\"mw-redirect\" title=\"Republic of Cyprus\">Republic of Cyprus</a> authorities. The <a href=\"https://wikipedia.org/wiki/Cypriot_National_Guard\" title=\"Cypriot National Guard\">Cypriot National Guard</a> and Police forces kill 15 Egyptian commandos and destroy the Egyptian <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">C-130</a> transport plane in open combat.", "links": [{"title": "Egyptian raid on Larnaca International Airport", "link": "https://wikipedia.org/wiki/Egyptian_raid_on_Larnaca_International_Airport"}, {"title": "Republic of Cyprus", "link": "https://wikipedia.org/wiki/Republic_of_Cyprus"}, {"title": "Cypriot National Guard", "link": "https://wikipedia.org/wiki/Cypriot_National_Guard"}, {"title": "Lockheed C-130 Hercules", "link": "https://wikipedia.org/wiki/Lockheed_C-130_Hercules"}]}, {"year": "1985", "text": "<PERSON> becomes the first recipient of an artificial heart to leave the hospital.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first recipient of an <a href=\"https://wikipedia.org/wiki/Artificial_heart\" title=\"Artificial heart\">artificial heart</a> to leave the hospital.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first recipient of an <a href=\"https://wikipedia.org/wiki/Artificial_heart\" title=\"Artificial heart\">artificial heart</a> to leave the hospital.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Artificial heart", "link": "https://wikipedia.org/wiki/Artificial_heart"}]}, {"year": "1985", "text": "A Boeing 727 operating as Iberia Flight 610 crashes Mount Oiz in Spain, killing 148; it is the deadliest accident to occur in Iberia's history and the deadliest in Basque County to occur.", "html": "1985 - A <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a> operating as <a href=\"https://wikipedia.org/wiki/Iberia_Flight_610\" title=\"Iberia Flight 610\">Iberia Flight 610</a> crashes <a href=\"https://wikipedia.org/wiki/Mount_Oiz\" class=\"mw-redirect\" title=\"Mount Oiz\">Mount Oiz</a> in Spain, killing 148; it is the deadliest accident to occur in <a href=\"https://wikipedia.org/wiki/Iberia_(airline)\" title=\"Iberia (airline)\">Iberia</a>'s history and the deadliest in <a href=\"https://wikipedia.org/wiki/Basque_Country_(autonomous_community)\" title=\"Basque Country (autonomous community)\">Basque County</a> to occur.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a> operating as <a href=\"https://wikipedia.org/wiki/Iberia_Flight_610\" title=\"Iberia Flight 610\">Iberia Flight 610</a> crashes <a href=\"https://wikipedia.org/wiki/Mount_Oiz\" class=\"mw-redirect\" title=\"Mount Oiz\">Mount Oiz</a> in Spain, killing 148; it is the deadliest accident to occur in <a href=\"https://wikipedia.org/wiki/Iberia_(airline)\" title=\"Iberia (airline)\">Iberia</a>'s history and the deadliest in <a href=\"https://wikipedia.org/wiki/Basque_Country_(autonomous_community)\" title=\"Basque Country (autonomous community)\">Basque County</a> to occur.", "links": [{"title": "Boeing 727", "link": "https://wikipedia.org/wiki/Boeing_727"}, {"title": "Iberia Flight 610", "link": "https://wikipedia.org/wiki/Iberia_Flight_610"}, {"title": "Mount Oiz", "link": "https://wikipedia.org/wiki/Mount_Oiz"}, {"title": "Iberia (airline)", "link": "https://wikipedia.org/wiki/Iberia_(airline)"}, {"title": "Basque Country (autonomous community)", "link": "https://wikipedia.org/wiki/Basque_Country_(autonomous_community)"}]}, {"year": "1986", "text": "Akkaraipattu massacre: the Sri Lankan Army massacres 80 Tamil farm workers in eastern Sri Lanka.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Akkaraipattu_massacre\" title=\"Akkaraipattu massacre\">Akkaraipattu massacre</a>: the <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Army\" class=\"mw-redirect\" title=\"Sri Lankan Army\">Sri Lankan Army</a> massacres 80 <a href=\"https://wikipedia.org/wiki/Tamils\" title=\"Tamils\">Tamil</a> farm workers in eastern <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Akkaraipattu_massacre\" title=\"Akkaraipattu massacre\">Akkaraipattu massacre</a>: the <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Army\" class=\"mw-redirect\" title=\"Sri Lankan Army\">Sri Lankan Army</a> massacres 80 <a href=\"https://wikipedia.org/wiki/Tamils\" title=\"Tamils\">Tamil</a> farm workers in eastern <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>.", "links": [{"title": "Akkaraipattu massacre", "link": "https://wikipedia.org/wiki/Akkaraipattu_massacre"}, {"title": "Sri Lankan Army", "link": "https://wikipedia.org/wiki/Sri_Lankan_Army"}, {"title": "Tamils", "link": "https://wikipedia.org/wiki/Tamils"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}]}, {"year": "1988", "text": "A Fairchild Swearingen Metroliner operating as AVAir Flight 3378 crashes in Cary, North Carolina, killing 12.", "html": "1988 - A <a href=\"https://wikipedia.org/wiki/Fairchild_Swearingen_Metroliner\" title=\"Fairchild Swearingen Metroliner\">Fairchild Swearingen Metroliner</a> operating as <a href=\"https://wikipedia.org/wiki/AVAir_Flight_3378\" title=\"AVAir Flight 3378\">AVAir Flight 3378</a> crashes in <a href=\"https://wikipedia.org/wiki/Cary,_North_Carolina\" title=\"Cary, North Carolina\">Cary, North Carolina</a>, killing 12.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Fairchild_Swearingen_Metroliner\" title=\"Fairchild Swearingen Metroliner\">Fairchild Swearingen Metroliner</a> operating as <a href=\"https://wikipedia.org/wiki/AVAir_Flight_3378\" title=\"AVAir Flight 3378\">AVAir Flight 3378</a> crashes in <a href=\"https://wikipedia.org/wiki/Cary,_North_Carolina\" title=\"Cary, North Carolina\">Cary, North Carolina</a>, killing 12.", "links": [{"title": "Fairchild Swearingen Metroliner", "link": "https://wikipedia.org/wiki/Fairchild_Swearingen_Metroliner"}, {"title": "AVAir Flight 3378", "link": "https://wikipedia.org/wiki/AVAir_Flight_3378"}, {"title": "Cary, North Carolina", "link": "https://wikipedia.org/wiki/Cary,_North_Carolina"}]}, {"year": "1989", "text": "Flying Tiger Line flight 66 crashes into a hill near Sultan Abdul <PERSON> Airport in Malaysia, killing four.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Flying_Tiger_Line_Flight_66\" title=\"Flying Tiger Line Flight 66\">Flying Tiger Line flight 66</a> crashes into a hill near <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Airport\" title=\"Sultan <PERSON> Airport\">Sultan <PERSON> Airport</a> in <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>, killing four.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flying_Tiger_Line_Flight_66\" title=\"Flying Tiger Line Flight 66\">Flying Tiger Line flight 66</a> crashes into a hill near <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Airport\" title=\"Sultan <PERSON> Airport\">Sultan <PERSON> Airport</a> in <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>, killing four.", "links": [{"title": "Flying Tiger Line Flight 66", "link": "https://wikipedia.org/wiki/Flying_Tiger_Line_Flight_66"}, {"title": "<PERSON> Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Shah_Airport"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}]}, {"year": "2002", "text": "NASA's Mars Odyssey space probe begins to map the surface of Mars using its thermal emission imaging system.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mars_Odyssey\" class=\"mw-redirect\" title=\"Mars Odyssey\">Mars Odyssey</a> <a href=\"https://wikipedia.org/wiki/Uncrewed_spacecraft\" title=\"Uncrewed spacecraft\">space probe</a> begins to map the surface of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a> using its thermal emission imaging system.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mars_Odyssey\" class=\"mw-redirect\" title=\"Mars Odyssey\">Mars Odyssey</a> <a href=\"https://wikipedia.org/wiki/Uncrewed_spacecraft\" title=\"Uncrewed spacecraft\">space probe</a> begins to map the surface of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a> using its thermal emission imaging system.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mars Odyssey", "link": "https://wikipedia.org/wiki/Mars_Odyssey"}, {"title": "Uncrewed spacecraft", "link": "https://wikipedia.org/wiki/Uncrewed_spacecraft"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "2003", "text": "An Ilyushin Il-76 military aircraft crashes near Kerman, Iran, killing 275.", "html": "2003 - An <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> military aircraft <a href=\"https://wikipedia.org/wiki/2003_Iran_Ilyushin_Il-76_crash\" title=\"2003 Iran Ilyushin Il-76 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Kerman\" title=\"Kerman\">Kerman</a>, Iran, killing 275.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> military aircraft <a href=\"https://wikipedia.org/wiki/2003_Iran_Ilyushin_Il-76_crash\" title=\"2003 Iran Ilyushin Il-76 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Kerman\" title=\"Kerman\">Kerman</a>, Iran, killing 275.", "links": [{"title": "Ilyushin Il-76", "link": "https://wikipedia.org/wiki/Ilyushin_Il-76"}, {"title": "2003 Iran Ilyushin Il-76 crash", "link": "https://wikipedia.org/wiki/2003_Iran_Ilyushin_Il-76_crash"}, {"title": "Kerman", "link": "https://wikipedia.org/wiki/Kerman"}]}, {"year": "2006", "text": "A methane explosion in a coal mine near Nueva Rosita, Mexico, kills 65 miners.", "html": "2006 - A <a href=\"https://wikipedia.org/wiki/Methane\" title=\"Methane\">methane</a> explosion in a <a href=\"https://wikipedia.org/wiki/Coal_mine\" class=\"mw-redirect\" title=\"Coal mine\">coal mine</a> near <a href=\"https://wikipedia.org/wiki/Nueva_Rosita\" title=\"Nueva Rosita\">Nueva Rosita</a>, Mexico, <a href=\"https://wikipedia.org/wiki/Pasta_de_Conchos_mine_disaster\" title=\"Pasta de Conchos mine disaster\">kills 65 miners</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Methane\" title=\"Methane\">methane</a> explosion in a <a href=\"https://wikipedia.org/wiki/Coal_mine\" class=\"mw-redirect\" title=\"Coal mine\">coal mine</a> near <a href=\"https://wikipedia.org/wiki/Nueva_Rosita\" title=\"Nueva Rosita\">Nueva Rosita</a>, Mexico, <a href=\"https://wikipedia.org/wiki/Pasta_de_Conchos_mine_disaster\" title=\"Pasta de Conchos mine disaster\">kills 65 miners</a>.", "links": [{"title": "Methane", "link": "https://wikipedia.org/wiki/Methane"}, {"title": "Coal mine", "link": "https://wikipedia.org/wiki/Coal_mine"}, {"title": "Nueva Rosita", "link": "https://wikipedia.org/wiki/Nueva_Rosita"}, {"title": "Pasta de Conchos mine disaster", "link": "https://wikipedia.org/wiki/Pasta_de_<PERSON><PERSON>s_mine_disaster"}]}, {"year": "2011", "text": "The debut exhibition of the Belitung shipwreck, containing the largest collection of Tang dynasty artifacts found in one location, begins in Singapore.", "html": "2011 - The debut exhibition of the <a href=\"https://wikipedia.org/wiki/Belitung_shipwreck\" title=\"Belitung shipwreck\">Belitung shipwreck</a>, containing the largest collection of <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> artifacts found in one location, begins in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>.", "no_year_html": "The debut exhibition of the <a href=\"https://wikipedia.org/wiki/Belitung_shipwreck\" title=\"Belitung shipwreck\">Belitung shipwreck</a>, containing the largest collection of <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> artifacts found in one location, begins in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>.", "links": [{"title": "<PERSON><PERSON>ung shipwreck", "link": "https://wikipedia.org/wiki/Belitung_shipwreck"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}]}, {"year": "2012", "text": "Forty-four people are killed in a prison brawl in Apodaca, Nuevo León, Mexico.", "html": "2012 - Forty-four people are killed in a prison <a href=\"https://wikipedia.org/wiki/Apodaca_prison_riot\" title=\"Apodaca prison riot\">brawl</a> in <a href=\"https://wikipedia.org/wiki/Apodaca\" title=\"Apodaca\">Apodaca, Nuevo León</a>, Mexico.", "no_year_html": "Forty-four people are killed in a prison <a href=\"https://wikipedia.org/wiki/Apodaca_prison_riot\" title=\"Apodaca prison riot\">brawl</a> in <a href=\"https://wikipedia.org/wiki/Apodaca\" title=\"Apodaca\">Apodaca, Nuevo León</a>, Mexico.", "links": [{"title": "Apodaca prison riot", "link": "https://wikipedia.org/wiki/Apodaca_prison_riot"}, {"title": "Apodaca", "link": "https://wikipedia.org/wiki/Apodaca"}]}, {"year": "2020", "text": "Nine people are killed in two domestic terrorist shootings in Hanau, Hesse, Germany.", "html": "2020 - Nine people are killed in <a href=\"https://wikipedia.org/wiki/Hanau_shootings\" title=\"Hanau shootings\">two domestic terrorist shootings</a> in <a href=\"https://wikipedia.org/wiki/Hanau\" title=\"Hanau\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Hesse\" title=\"Hesse\">Hesse</a>, Germany.", "no_year_html": "Nine people are killed in <a href=\"https://wikipedia.org/wiki/Hanau_shootings\" title=\"Hanau shootings\">two domestic terrorist shootings</a> in <a href=\"https://wikipedia.org/wiki/Hanau\" title=\"Hanau\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Hesse\" title=\"Hesse\">Hesse</a>, Germany.", "links": [{"title": "Hanau shootings", "link": "https://wikipedia.org/wiki/<PERSON>au_shootings"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>au"}, {"title": "Hesse", "link": "https://wikipedia.org/wiki/Hesse"}]}, {"year": "2021", "text": "<PERSON><PERSON> <PERSON><PERSON>, a 19-year-old protester, becomes the first known casualty of anti-coup protests that formed in response to the 2021 Myanmar coup d'état.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>hwe_Thwe_<PERSON><PERSON>\" title=\"Death of <PERSON><PERSON> <PERSON>hwe <PERSON>hwe <PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, a 19-year-old protester, becomes the first known casualty of <a href=\"https://wikipedia.org/wiki/Myanmar_protests_(2021%E2%80%93present)\" title=\"Myanmar protests (2021-present)\">anti-coup protests</a> that formed in response to the <a href=\"https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat\" title=\"2021 Myanmar coup d'état\">2021 Myanmar coup d'état</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>hwe_Thwe_K<PERSON>\" title=\"Death of My<PERSON> Thwe Thwe <PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, a 19-year-old protester, becomes the first known casualty of <a href=\"https://wikipedia.org/wiki/Myanmar_protests_(2021%E2%80%93present)\" title=\"Myanmar protests (2021-present)\">anti-coup protests</a> that formed in response to the <a href=\"https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat\" title=\"2021 Myanmar coup d'état\">2021 Myanmar coup d'état</a>.", "links": [{"title": "Death of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>hwe_Thwe_<PERSON>hine"}, {"title": "Myanmar protests (2021-present)", "link": "https://wikipedia.org/wiki/Myanmar_protests_(2021%E2%80%93present)"}, {"title": "2021 Myanmar coup d'état", "link": "https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat"}]}], "Births": [{"year": "1461", "text": "<PERSON>, Italian cardinal (d. 1523)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1523)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1523)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1473", "text": "<PERSON><PERSON>, Prussian mathematician and astronomer (d. 1543)", "html": "1473 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Prussian mathematician and astronomer (d. 1543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Prussian mathematician and astronomer (d. 1543)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Copernicus"}]}, {"year": "1497", "text": "<PERSON><PERSON><PERSON><PERSON>, German fashion writer (d. 1574)", "html": "1497 - <a href=\"https://wikipedia.org/wiki/Matth%C3%A4<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German fashion writer (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matth%C3%A4<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German fashion writer (d. 1574)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matth%C3%A4us_<PERSON>"}]}, {"year": "1519", "text": "<PERSON><PERSON><PERSON> of Zimmern, German author of the Zimmern Chronicle (d. 1566)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Zimmern\" title=\"<PERSON><PERSON><PERSON> of Zimmern\"><PERSON><PERSON><PERSON> of Zimmern</a>, German author of the Zimmern Chronicle (d. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Zimmern\" title=\"<PERSON><PERSON><PERSON> of Zimmern\"><PERSON><PERSON><PERSON> of Zimmern</a>, German author of the Zimmern Chronicle (d. 1566)", "links": [{"title": "<PERSON><PERSON><PERSON> of Zimmern", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1526", "text": "<PERSON><PERSON>, Flemish botanist and academic (d. 1609)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish botanist and academic (d. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish botanist and academic (d. 1609)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1532", "text": "<PERSON><PERSON><PERSON>, French poet (d. 1589)", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%AFf\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet (d. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%AFf\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet (d. 1589)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%AFf"}]}, {"year": "1552", "text": "<PERSON><PERSON><PERSON>, Austrian cardinal (d. 1630)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian cardinal (d. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian cardinal (d. 1630)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "1594", "text": "<PERSON>, Prince of Wales (d. 1612)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON>, Prince of Wales</a> (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON>, Prince of Wales</a> (d. 1612)", "links": [{"title": "<PERSON>, Prince of Wales", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Wales"}]}, {"year": "1611", "text": "<PERSON><PERSON>, Dutch politician (d. 1678)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician (d. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician (d. 1678)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1630", "text": "<PERSON><PERSON>, Indian warrior-king and the founder of Maratha Empire (d. 1680)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/Shivaji\" title=\"Shivaji\"><PERSON><PERSON></a>, Indian warrior-king and the founder of <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shivaji\" title=\"Shivaji\"><PERSON><PERSON></a>, Indian warrior-king and the founder of <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> (d. 1680)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}]}, {"year": "1660", "text": "<PERSON>, German physician and chemist (d. 1742)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and chemist (d. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and chemist (d. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON>, English actor, playwright, and producer (d. 1779)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, playwright, and producer (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, playwright, and producer (d. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1743", "text": "<PERSON>, Italian cellist and composer (d. 1805)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (d. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, Canadian soldier, lawyer, and politician, Premier of Canada West (d. 1862)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Joint_Premiers_of_the_Province_of_Canada\" class=\"mw-redirect\" title=\"Joint Premiers of the Province of Canada\">Premier of Canada West</a> (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Joint_Premiers_of_the_Province_of_Canada\" class=\"mw-redirect\" title=\"Joint Premiers of the Province of Canada\">Premier of Canada West</a> (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Joint Premiers of the Province of Canada", "link": "https://wikipedia.org/wiki/Joint_Premiers_of_the_Province_of_Canada"}]}, {"year": "1800", "text": "<PERSON><PERSON><PERSON>, Canadian nun and social worker, founded the Sisters of Providence (d. 1851)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/%C3%89milie_<PERSON>lin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian nun and social worker, founded the <a href=\"https://wikipedia.org/wiki/Sisters_of_Providence_(Montreal)\" title=\"Sisters of Providence (Montreal)\">Sisters of Providence</a> (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian nun and social worker, founded the <a href=\"https://wikipedia.org/wiki/Sisters_of_Providence_(Montreal)\" title=\"Sisters of Providence (Montreal)\">Sisters of Providence</a> (d. 1851)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89milie_<PERSON>lin"}, {"title": "Sisters of Providence (Montreal)", "link": "https://wikipedia.org/wiki/<PERSON>_of_Providence_(Montreal)"}]}, {"year": "1804", "text": "<PERSON>, German physician, pathologist, and philosopher  (d. 1878)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, pathologist, and philosopher (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, pathologist, and philosopher (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, German linguist and academic (d. 1868)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German linguist and academic (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German linguist and academic (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1833", "text": "<PERSON><PERSON>, Swiss journalist and activist, Nobel Prize laureate (d. 1906)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/%C3%89lie_Ducommun\" title=\"<PERSON><PERSON> Ducommu<PERSON>\"><PERSON><PERSON></a>, Swiss journalist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_Ducommun\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss journalist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lie_Ducommun"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1838", "text": "<PERSON>, British burlesque performer (d. 1908)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British burlesque performer (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British burlesque performer (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON><PERSON><PERSON>, Swedish organist, composer, and conductor (d. 1929)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Andr%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish organist, composer, and conductor (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Andr%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish organist, composer, and conductor (d. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elfrida_Andr%C3%A9e"}]}, {"year": "1855", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 16th <PERSON><PERSON><PERSON><PERSON> (d. 1908)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/Ni<PERSON><PERSON><PERSON>_Kajir%C5%8D_I\" title=\"Nishinou<PERSON> Kajirō I\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 16th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Kajir%C5%8D_I\" title=\"Nishinou<PERSON> Kajirō I\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 16th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Kajir%C5%8D_I"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON>, Swedish physicist and chemist, Nobel Prize laureate (d. 1927)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hen<PERSON>\" title=\"<PERSON><PERSON><PERSON> A<PERSON>hen<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1865", "text": "<PERSON>, Swedish geographer and explorer (d. 1952)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish geographer and explorer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish geographer and explorer (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON><PERSON>, Armenian-Russian poet and author (d. 1923)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian-Russian poet and author (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian-Russian poet and author (d. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Estonian admiral (d. 1944)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian admiral (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian admiral (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "Con<PERSON><PERSON>, Romanian-French sculptor, painter, and photographer (d. 1957)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Constantin_Br%C3%A2ncu%C8%99i\" title=\"Constantin <PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-French sculptor, painter, and photographer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantin_Br%C3%A2ncu%C8%99i\" title=\"Constantin <PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-French sculptor, painter, and photographer (d. 1957)", "links": [{"title": "Constant<PERSON>", "link": "https://wikipedia.org/wiki/Constantin_Br%C3%A2ncu%C8%99i"}]}, {"year": "1877", "text": "<PERSON><PERSON>, German painter (d. 1962)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Gabriel<PERSON>_M%C3%BCnter\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_M%C3%BCnter\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gabriele_M%C3%BCnter"}]}, {"year": "1878", "text": "<PERSON>, Swedish-Norwegian actress (d. 1961)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Norwegian actress (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Norwegian actress (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, Mexican general and politician, 39th President of Mexico (d. 1928)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican general and politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican general and politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>breg%C3%B3n"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1886", "text": "<PERSON>, Filipino lawyer and jurist, 5th Chief Justice of the Supreme Court of the Philippines (d. 1942)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and jurist, 5th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines\" class=\"mw-redirect\" title=\"Chief Justice of the Supreme Court of the Philippines\">Chief Justice of the Supreme Court of the Philippines</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and jurist, 5th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines\" class=\"mw-redirect\" title=\"Chief Justice of the Supreme Court of the Philippines\">Chief Justice of the Supreme Court of the Philippines</a> (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Justice of the Supreme Court of the Philippines", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines"}]}, {"year": "1888", "text": "<PERSON>, Colombian lawyer and poet (d. 1928)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and poet (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and poet (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, English actor and director (d. 1964)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and director (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and director (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American actor (d. 1956)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>n"}]}, {"year": "1896", "text": "<PERSON>, French poet and author (d. 1966)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Breton\" title=\"André Breton\"><PERSON></a>, French poet and author (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Breton\" title=\"André Breton\"><PERSON></a>, French poet and author (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Breton"}]}, {"year": "1897", "text": "<PERSON>, American actress (d. 1931)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Argentinian-Italian painter and sculptor (d. 1968)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian-Italian painter and sculptor (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian-Italian painter and sculptor (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American novelist, short story writer, and educator (d. 1992)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and educator (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and educator (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Dutch journalist and author (d. 1964)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>vank\" title=\"<PERSON>van<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch journalist and author (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>vank\" title=\"<PERSON>van<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch journalist and author (d. 1964)", "links": [{"title": "Havank", "link": "https://wikipedia.org/wiki/Havank"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Indian-American actress (d. 1979)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Me<PERSON>_<PERSON>\" title=\"Merle <PERSON>\"><PERSON><PERSON></a>, Indian-American actress (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Merle O<PERSON>on\"><PERSON><PERSON></a>, Indian-American actress (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Me<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American composer (d. 1997)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American actress (d. 2010)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON> of Orléans-Braganza (d. 2007)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>%C3%A3o_of_Orl%C3%A9ans-Braganza\" class=\"mw-redirect\" title=\"Prince <PERSON> of Orléans-Braganza\">Prince <PERSON> of Orléans-Braganza</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>%C3%A3o_of_Orl%C3%A9ans-Braganza\" class=\"mw-redirect\" title=\"Prince <PERSON> of Orléans-Braganza\">Prince <PERSON> of Orléans-Braganza</a> (d. 2007)", "links": [{"title": "Prince <PERSON> of Orléans-Braganza", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A3o_of_Orl%C3%A9ans-Braganza"}]}, {"year": "1913", "text": "<PERSON>, American animator and screenwriter (d. 1972)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and screenwriter (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and screenwriter (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, New Zealand Olympic sprinter (d. 1985)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand Olympic sprinter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand Olympic sprinter (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English actor and comedian (d. 1983)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English lawyer, politician, and diplomat, British Ambassador to the United States (d. 2014)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_the_United_States\" title=\"List of ambassadors of the United Kingdom to the United States\">British Ambassador to the United States</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_the_United_States\" title=\"List of ambassadors of the United Kingdom to the United States\">British Ambassador to the United States</a> (d. 2014)", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)"}, {"title": "List of ambassadors of the United Kingdom to the United States", "link": "https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_the_United_States"}]}, {"year": "1916", "text": "<PERSON>, American jockey and sportscaster (d. 1997)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and sportscaster (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and sportscaster (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American novelist, short story writer, playwright, and essayist (d. 1967)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, playwright, and essayist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, playwright, and essayist (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actress (d. 2019)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actress, fashion designer, and author (d. 2003)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Guest\" title=\"<PERSON><PERSON> <PERSON><PERSON> Guest\"><PERSON><PERSON> <PERSON><PERSON></a>, American actress, fashion designer, and author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Guest\" title=\"<PERSON><PERSON> <PERSON><PERSON> Guest\"><PERSON><PERSON> <PERSON><PERSON></a>, American actress, fashion designer, and author (d. 2003)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Guest"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Estonian author and poet (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and poet (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and poet (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English actor and singer (d. 1988)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and singer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and singer (d. 1988)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish journalist and politician, Polish Minister of Foreign Affairs (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)\" title=\"Ministry of Foreign Affairs (Poland)\">Polish Minister of Foreign Affairs</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)\" title=\"Ministry of Foreign Affairs (Poland)\">Polish Minister of Foreign Affairs</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON>"}, {"title": "Ministry of Foreign Affairs (Poland)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)"}]}, {"year": "1924", "text": "<PERSON>, Ukrainian chess player and theoretician (d. 2006)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian chess player and theoretician (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian chess player and theoretician (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor (d. 1987)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian composer and academic", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6<PERSON>_<PERSON>%C3%A1g\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian composer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_<PERSON>%C3%A1g\" title=\"<PERSON><PERSON>ö<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian composer and academic", "links": [{"title": "<PERSON><PERSON>ö<PERSON>", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_Kurt%C3%A1g"}]}, {"year": "1927", "text": "<PERSON>, French journalist (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, French director and screenwriter (d. 2003)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American director and producer (d. 2002)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Indian actor, director, and screenwriter (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wana<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wana<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and screenwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American captain, physician, and astronaut", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, physician, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, physician, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Wood scientist (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Wood scientist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Wood scientist (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American sportscaster (d. 2010)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American MLB catcher and coach (d. 2016)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American MLB catcher and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American MLB catcher and coach (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter (d. 2006)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American poet", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American author and educator (d. 1987)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Australian cricketer and sportscaster (d. 2008)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Norm_O%27Neill\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer and sportscaster (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norm_O%27Neill\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer and sportscaster (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norm_O%27Neill"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, 10th <PERSON><PERSON> (d. 1989)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_<PERSON><PERSON>_Lama\" title=\"<PERSON><PERSON><PERSON>, 10th Panchen <PERSON>\"><PERSON><PERSON><PERSON>, 10th <PERSON>chen <PERSON></a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_<PERSON><PERSON>_Lama\" title=\"<PERSON><PERSON><PERSON>, 10th Panchen <PERSON>\"><PERSON><PERSON><PERSON>, 10th Panchen <PERSON></a> (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>, 10th <PERSON>chen <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English activist and author, founded Refuge", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist and author, founded <a href=\"https://wikipedia.org/wiki/Refuge_(United_Kingdom_charity)\" title=\"Refuge (United Kingdom charity)\">Refuge</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist and author, founded <a href=\"https://wikipedia.org/wiki/Refuge_(United_Kingdom_charity)\" title=\"Refuge (United Kingdom charity)\">Refuge</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ey"}, {"title": "Refuge (United Kingdom charity)", "link": "https://wikipedia.org/wiki/Refuge_(United_Kingdom_charity)"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkmen engineer and politician, 1st President of Turkmenistan (d. 2006)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Saparmurat_Niyazov\" title=\"Saparmurat Niyazov\"><PERSON><PERSON><PERSON><PERSON></a>, Turkmen engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Turkmenistan\" title=\"President of Turkmenistan\">President of Turkmenistan</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saparmurat_Niyazov\" title=\"Saparmurat Niyazov\"><PERSON><PERSON><PERSON><PERSON></a>, Turkmen engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Turkmenistan\" title=\"President of Turkmenistan\">President of Turkmenistan</a> (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saparmurat_Niyazov"}, {"title": "President of Turkmenistan", "link": "https://wikipedia.org/wiki/President_of_Turkmenistan"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1941", "text": "<PERSON>, <PERSON>, English politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English biochemist and emeritus scientist at the Laboratory of Molecular Biology (d. 2019)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and emeritus scientist at the <a href=\"https://wikipedia.org/wiki/Laboratory_of_Molecular_Biology\" class=\"mw-redirect\" title=\"Laboratory of Molecular Biology\">Laboratory of Molecular Biology</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and emeritus scientist at the <a href=\"https://wikipedia.org/wiki/Laboratory_of_Molecular_Biology\" class=\"mw-redirect\" title=\"Laboratory of Molecular Biology\">Laboratory of Molecular Biology</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Laboratory of Molecular Biology", "link": "https://wikipedia.org/wiki/Laboratory_of_Molecular_Biology"}]}, {"year": "1942", "text": "<PERSON>, American football player and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American biologist, historian, and academic (d. 2015)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American biologist, historian, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American biologist, historian, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>vine"}]}, {"year": "1942", "text": "<PERSON>, Welsh businessman", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American author and engineer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Hi<PERSON>\" title=\"<PERSON> Hi<PERSON>\"><PERSON></a>, American author and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Hi<PERSON>\" title=\"<PERSON> Hickam\"><PERSON></a>, American author and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>am"}]}, {"year": "1943", "text": "<PERSON>, English biochemist and academic, Nobel laureate", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel laureate</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hunt\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel laureate</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1944", "text": "<PERSON>, English-American journalist and businessman", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Uzbek-Russian singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Uzbek-Russian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Uzbek-Russian singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1946", "text": "<PERSON>, Canadian guitarist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, Canadian guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, Canadian guitarist", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)"}]}, {"year": "1946", "text": "<PERSON>, Australian footballer and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American technician and activist (d. 1974)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American technician and activist (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American technician and activist (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actress and playwright (d. 1985)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, New Zealand businessman and politician, 42nd Mayor of Invercargill", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman and politician, 42nd <a href=\"https://wikipedia.org/wiki/Mayor_of_Invercargill\" title=\"Mayor of Invercargill\">Mayor of Invercargill</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman and politician, 42nd <a href=\"https://wikipedia.org/wiki/Mayor_of_Invercargill\" title=\"Mayor of Invercargill\">Mayor of Invercargill</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Invercargill", "link": "https://wikipedia.org/wiki/Mayor_of_Invercargill"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mark <PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mark <PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Dutch sociologist, academic, and politician (d. 2002)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Pim_Fortuyn\" title=\"Pim Fortuyn\"><PERSON><PERSON></a>, Dutch sociologist, academic, and politician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pim_Fortuyn\" title=\"Pim Fortuyn\"><PERSON><PERSON></a>, Dutch sociologist, academic, and politician (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pim_Fortuyn"}]}, {"year": "1948", "text": "<PERSON>, English guitarist and songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American game designer and programmer (d. 1998)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer and programmer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer and programmer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter and pianist (d. 2015)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and pianist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and pianist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English footballer and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, American author and illustrator", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Finnish singer-songwriter (d. 2006)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer-songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer-songwriter (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ju<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Pakistani scholar and politician, founder of <PERSON>aj-ul-Quran", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani scholar and politician, founder of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-ul-<PERSON>\" title=\"<PERSON><PERSON><PERSON>ul-Quran\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani scholar and politician, founder of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-ul-<PERSON>\" title=\"<PERSON><PERSON><PERSON>ul-Quran\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Japanese novelist and filmmaker", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Ry%C5%AB_Murakami\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese novelist and filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ry%C5%AB_Murakami\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese novelist and filmmaker", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ry%C5%AB_Murakami"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Mexican engineer and astronaut", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican engineer and astronaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American novelist, essayist, and short story writer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Slovene academic and politician, 3rd President of Slovenia", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Danilo_T%C3%BCrk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovene academic and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Slovenia\" title=\"President of Slovenia\">President of Slovenia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lo_T%C3%BCrk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovene academic and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Slovenia\" title=\"President of Slovenia\">President of Slovenia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Danilo_T%C3%BCrk"}, {"title": "President of Slovenia", "link": "https://wikipedia.org/wiki/President_of_Slovenia"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Italian tennis player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Argentine lawyer and politician, President of Argentina and Vice President of Argentina", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> and <a href=\"https://wikipedia.org/wiki/Vice_President_of_Argentina\" title=\"Vice President of Argentina\">Vice President of Argentina</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> and <a href=\"https://wikipedia.org/wiki/Vice_President_of_Argentina\" title=\"Vice President of Argentina\">Vice President of Argentina</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cristina_Fern%C3%A1<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}, {"title": "Vice President of Argentina", "link": "https://wikipedia.org/wiki/Vice_President_of_Argentina"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Italian actor, director, and screenwriter (d. 1994)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor, director, and screenwriter (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor, director, and screenwriter (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Massimo_Troisi"}]}, {"year": "1954", "text": "<PERSON>, German bass player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and manager (d. 2011)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/S%C3%B3crates\" title=\"Sócrates\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%B3crates\" title=\"Sócrates\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%B3crates"}]}, {"year": "1955", "text": "<PERSON>, American actor and playwright", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American biologist and academic, Nobel Prize laureate", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1956", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Austrian singer-songwriter, rapper, and musician (d. 1998)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Austrian singer-songwriter, rapper, and musician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Austrian singer-songwriter, rapper, and musician (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)"}]}, {"year": "1957", "text": "<PERSON>, American baseball player, coach, and executive", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and executive", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1957", "text": "<PERSON>, English actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English author and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English keyboard player and composer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American businessman, 6th National Football League Commissioner", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, 6th <a href=\"https://wikipedia.org/wiki/National_Football_League_Commissioner\" class=\"mw-redirect\" title=\"National Football League Commissioner\">National Football League Commissioner</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, 6th <a href=\"https://wikipedia.org/wiki/National_Football_League_Commissioner\" class=\"mw-redirect\" title=\"National Football League Commissioner\">National Football League Commissioner</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "National Football League Commissioner", "link": "https://wikipedia.org/wiki/National_Football_League_Commissioner"}]}, {"year": "1960", "text": "<PERSON> <PERSON>, Duke of York", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_York\" title=\"Prince <PERSON>, Duke of York\">Prince <PERSON>, Duke of York</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_York\" title=\"Prince <PERSON>, Duke of York\">Prince <PERSON>, Duke of York</a>", "links": [{"title": "<PERSON> <PERSON>, Duke of York", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_York"}]}, {"year": "1960", "text": "<PERSON>, American race car driver (d. 2020)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON>.</a>, American race car driver (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON>.</a>, American race car driver (d. 2020)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1961", "text": "<PERSON>, English footballer (d. 1998)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American golfer (d. 2020)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Czech-Australian tennis player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dl%C3%ADkov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-Australian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADkov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-Australian tennis player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hana_Mandl%C3%ADkov%C3%A1"}]}, {"year": "1963", "text": "<PERSON>, English singer-songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1963", "text": "<PERSON>, American actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American biochemist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American novelist, essayist, and short story writer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American drummer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American businessman", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hunt\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Clark Hunt\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American actress and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Dutch tennis player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American designer and author (d. 2024)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American designer and author (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American designer and author (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Puerto Rican actor, director, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Ben<PERSON>o_del_Toro\" title=\"<PERSON>ici<PERSON> del Toro\"><PERSON><PERSON><PERSON></a>, Puerto Rican actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_del_Toro\" title=\"<PERSON><PERSON><PERSON> del Toro\"><PERSON><PERSON><PERSON></a>, Puerto Rican actor, director, and producer", "links": [{"title": "<PERSON><PERSON>o <PERSON> Toro", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_del_Toro"}]}, {"year": "1968", "text": "<PERSON> <PERSON><PERSON>, American rapper and actor (d. 2021)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\">Prince <PERSON><PERSON></a>, American rapper and actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\">Prince <PERSON><PERSON></a>, American rapper and actor (d. 2021)", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American bass player (d. 2015)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player (d. 2015)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Burton_<PERSON>_Bell\" title=\"Burton C. Bell\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Burton_C._Bell\" title=\"Burton C. Bell\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "Burton C. Bell", "link": "https://wikipedia.org/wiki/Burton_C._Bell"}]}, {"year": "1969", "text": "<PERSON>, Canadian businesswoman and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Swedish singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Austrian politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Young\" title=\"<PERSON><PERSON> Young\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Young\" title=\"<PERSON><PERSON> Young\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Young"}]}, {"year": "1971", "text": "<PERSON>, Dominican baseball player and poet", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian golfer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1971", "text": "<PERSON>, American author and illustrator", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" class=\"mw-redirect\" title=\"<PERSON> (author)\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" class=\"mw-redirect\" title=\"<PERSON> (author)\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, American wrestler and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American pornographic actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Thomas\" title=\"Sunset Thomas\"><PERSON></a>, American pornographic actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Thomas\" title=\"Sunset Thomas\"><PERSON></a>, American pornographic actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Thomas"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian drummer and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, South Korean-American skateboarder, co-founded Almost Skateboards", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Song\" title=\"Daewon Song\"><PERSON><PERSON><PERSON></a>, South Korean-American skateboarder, co-founded <a href=\"https://wikipedia.org/wiki/Almost_Skateboards\" title=\"Almost Skateboards\">Almost Skateboards</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Song\" title=\"Da<PERSON><PERSON> Song\"><PERSON><PERSON><PERSON></a>, South Korean-American skateboarder, co-founded <a href=\"https://wikipedia.org/wiki/Almost_Skateboards\" title=\"Almost Skateboards\">Almost Skateboards</a>", "links": [{"title": "<PERSON><PERSON><PERSON> Song", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Almost Skateboards", "link": "https://wikipedia.org/wiki/Almost_Skateboards"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Swedish singer-songwriter and keyboard player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sal<PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sal<PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter and keyboard player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>la_<PERSON>o"}]}, {"year": "1977", "text": "<PERSON>, American journalist and author", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English scholar and politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Peruvian-American rapper", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Immortal_Technique\" title=\"Immortal Technique\">Immortal Technique</a>, Peruvian-American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Immortal_Technique\" title=\"Immortal Technique\">Immortal Technique</a>, Peruvian-American rapper", "links": [{"title": "Immortal Technique", "link": "https://wikipedia.org/wiki/Immortal_Technique"}]}, {"year": "1979", "text": "<PERSON>, American soccer player and manager", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Chinese table tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(table_tennis)\" title=\"<PERSON> (table tennis)\"><PERSON></a>, Chinese table tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(table_tennis)\" title=\"<PERSON> (table tennis)\"><PERSON></a>, Chinese table tennis player", "links": [{"title": "<PERSON> (table tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(table_tennis)"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1980)\" title=\"<PERSON> (basketball, born 1980)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1980)\" title=\"<PERSON> (basketball, born 1980)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1980)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1980)"}]}, {"year": "1981", "text": "<PERSON>, American singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Bulgarian sumo wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Koto%C5%8Dsh%C5%AB_Katsunori\" title=\"Kotoōshū Katsunori\">Kotoō<PERSON> Katsunori</a>, Bulgarian sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Koto%C5%8Dsh%C5%AB_Katsunori\" title=\"Kotoōshū Katsunori\"><PERSON>toō<PERSON> Katsunori</a>, Bulgarian sumo wrestler", "links": [{"title": "Kotoō<PERSON> Katsunori", "link": "https://wikipedia.org/wiki/Koto%C5%8Dsh%C5%AB_Kat<PERSON>ori"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Japanese singer and actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Indonesian sex offender", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian sex offender", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian sex offender", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American actress and singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American actress and model", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Serbian basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kosta_Perovi%C4%87"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Brazilian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1986", "text": "<PERSON>, Norwegian singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}]}, {"year": "1987", "text": "<PERSON>, Italian ice dancer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian ice dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American guitarist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1989", "text": "<PERSON><PERSON>, English-Nigerian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Aluko\"><PERSON><PERSON></a>, English-Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Aluko\"><PERSON><PERSON></a>, English-Nigerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>uko"}]}, {"year": "1991", "text": "<PERSON>, American race car driver", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, German footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American basketball player (d. 2022)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American model", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Argentine footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American actress and singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Victoria_Justice\" title=\"Victoria Justice\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Justice\" title=\"Victoria Justice\"><PERSON></a>, American actress and singer", "links": [{"title": "Victoria Justice", "link": "https://wikipedia.org/wiki/Victoria_Justice"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Tiina_Trutsi\" title=\"Tiina Trutsi\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tiina_Trutsi\" title=\"Tiina Trutsi\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tiina_Trutsi"}]}, {"year": "1995", "text": "<PERSON>, Serbian basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikola_Joki%C4%87"}]}, {"year": "1996", "text": "<PERSON>, British-Swedish singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British-Swedish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British-Swedish singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1996", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, German tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, American singer and songwriter", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Chappel<PERSON> Roan\"><PERSON><PERSON><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Chappel<PERSON> Roan\"><PERSON><PERSON><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, South Korean singer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON>, South Korean footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-in\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-in\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-in"}]}, {"year": "2001", "text": "<PERSON>, American actor", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_O<PERSON>rzy%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_O<PERSON>rzy%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jakub_Ojrzy%C5%84ski"}]}, {"year": "2004", "text": "<PERSON>, English actress, model and producer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, model and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, model and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "197", "text": "<PERSON><PERSON><PERSON>, Roman usurper (b. 150)", "html": "197 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Albinus\" title=\"<PERSON><PERSON><PERSON> Albinus\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Roman_usurper\" title=\"Roman usurper\">Roman usurper</a> (b. 150)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>binus\" title=\"<PERSON><PERSON><PERSON> Albinus\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Roman_usurper\" title=\"Roman usurper\">Roman usurper</a> (b. 150)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Roman usurper", "link": "https://wikipedia.org/wiki/Roman_usurper"}]}, {"year": "446", "text": "<PERSON><PERSON> Trier, Bishop of Trier", "html": "446 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Trier\" title=\"<PERSON><PERSON> of Trier\"><PERSON><PERSON> of Trier</a>, Bishop of <a href=\"https://wikipedia.org/wiki/Trier\" title=\"Trier\"><PERSON>er</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Trier\" title=\"<PERSON><PERSON> of Trier\"><PERSON><PERSON> of Trier</a>, Bishop of <a href=\"https://wikipedia.org/wiki/Trier\" title=\"Trier\">Trier</a>", "links": [{"title": "<PERSON><PERSON> of Trier", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Tri<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trier"}]}, {"year": "1133", "text": "<PERSON>, Byzantine wife of <PERSON><PERSON> (b. 1066)", "html": "1133 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Komnenos\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1066)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Komnenos\" title=\"<PERSON><PERSON>nen<PERSON>\"><PERSON><PERSON></a> (b. 1066)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Alexios I Komnenos", "link": "https://wikipedia.org/wiki/Alexios_I_Komnenos"}]}, {"year": "1275", "text": "<PERSON>, Sufi philosopher and poet (b. 1177)", "html": "1275 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Qalandar\" title=\"Lal Shahbaz Qalandar\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sufi\" class=\"mw-redirect\" title=\"Sufi\">Sufi</a> philosopher and poet (b. 1177)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Qalandar\" title=\"Lal Shahbaz Qalandar\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sufi\" class=\"mw-redirect\" title=\"Sufi\">Sufi</a> philosopher and poet (b. 1177)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lal_Shahbaz_Qalandar"}, {"title": "Sufi", "link": "https://wikipedia.org/wiki/Sufi"}]}, {"year": "1300", "text": "<PERSON><PERSON> Zamora, General of the Dominican Order", "html": "1300 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Zamora\" title=\"<PERSON><PERSON> of Zamora\"><PERSON><PERSON> of Zamora</a>, General of the Dominican Order", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Zamora\" title=\"<PERSON><PERSON> of Zamora\"><PERSON><PERSON> of Zamora</a>, General of the Dominican Order", "links": [{"title": "<PERSON><PERSON> of Zamora", "link": "https://wikipedia.org/wiki/Munio_of_Zamora"}]}, {"year": "1408", "text": "<PERSON>, 5th Baron <PERSON>, English rebel", "html": "1408 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Baron_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, English rebel", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Baron_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, English rebel", "links": [{"title": "<PERSON>, 5th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Baron_<PERSON>"}]}, {"year": "1414", "text": "<PERSON>, Archbishop of Canterbury (b. 1353)", "html": "1414 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a> (b. 1353)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a> (b. 1353)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}]}, {"year": "1445", "text": "<PERSON> of Aragon, queen of Portugal (b. 1402)", "html": "1445 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Portugal\" title=\"<PERSON> of <PERSON>, Queen of Portugal\"><PERSON> of Aragon</a>, queen of <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> (b. 1402)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Portugal\" title=\"<PERSON> Aragon, Queen of Portugal\"><PERSON> of Aragon</a>, queen of <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> (b. 1402)", "links": [{"title": "<PERSON> Aragon, Queen of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Portugal"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}]}, {"year": "1491", "text": "<PERSON><PERSON>, Count of East Frisia, German noble (b. 1460)", "html": "1491 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_East_Frisia\" title=\"<PERSON><PERSON>, Count of East Frisia\"><PERSON><PERSON>, Count of East Frisia</a>, German noble (b. 1460)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_East_Frisia\" title=\"<PERSON><PERSON> <PERSON>, Count of East Frisia\"><PERSON><PERSON>, Count of East Frisia</a>, German noble (b. 1460)", "links": [{"title": "<PERSON><PERSON>, Count of East Frisia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_East_Frisia"}]}, {"year": "1553", "text": "<PERSON><PERSON>, German astronomer and mathematician (b. 1511)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Reinhold\" title=\"Era<PERSON> Reinhold\"><PERSON><PERSON></a>, German astronomer and mathematician (b. 1511)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Re<PERSON>hold\" title=\"Erasmus Reinhold\"><PERSON><PERSON></a>, German astronomer and mathematician (b. 1511)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>inhold"}]}, {"year": "1602", "text": "<PERSON>, Duke of Mercœur (b. 1558)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Merc%C5%93ur\" title=\"<PERSON>, Duke of Mercœur\"><PERSON>, Duke of Mercœur</a> (b. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Merc%C5%93ur\" title=\"<PERSON>, Duke of Mercœur\"><PERSON>, Duke of Mercœur</a> (b. 1558)", "links": [{"title": "<PERSON>, Duke of Mercœur", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Merc%C5%93ur"}]}, {"year": "1605", "text": "<PERSON><PERSON><PERSON>, Italian composer (b. 1550)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (b. 1550)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1622", "text": "<PERSON>, English scholar and politician (b. 1549)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bible_translator)\" title=\"<PERSON> (Bible translator)\"><PERSON></a>, English scholar and politician (b. 1549)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bible_translator)\" title=\"<PERSON> (Bible translator)\"><PERSON></a>, English scholar and politician (b. 1549)", "links": [{"title": "<PERSON> (Bible translator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bible_translator)"}]}, {"year": "1672", "text": "<PERSON>, English-American minister, theologian, and academic (b. 1592)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American minister, theologian, and academic (b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American minister, theologian, and academic (b. 1592)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1709", "text": "<PERSON>, Japanese shōgun (b. 1646)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_T<PERSON>oshi\" title=\"Tokugawa Tsunayoshi\"><PERSON></a>, Japanese shōgun (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>oshi\" title=\"Tokugawa Tsunayoshi\"><PERSON></a>, Japanese shō<PERSON> (b. 1646)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1716", "text": "<PERSON><PERSON><PERSON>, Norwegian author and poet (b. 1634)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian author and poet (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian author and poet (b. 1634)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, Countess of Harold, English aristocrat and philanthropist (b. 1701)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Harold\" title=\"<PERSON>, Countess of Harold\"><PERSON>, Countess of Harold</a>, English aristocrat and philanthropist (b. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Harold\" title=\"<PERSON>, Countess of Harold\"><PERSON>, Countess of Harold</a>, English aristocrat and philanthropist (b. 1701)", "links": [{"title": "<PERSON>, Countess of Harold", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, American lawyer and politician, 7th Governor of Delaware (b. 1738)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)\" class=\"mw-redirect\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (b. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(governor)\" class=\"mw-redirect\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (b. 1738)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}, {"title": "Governor of Delaware", "link": "https://wikipedia.org/wiki/Governor_of_Delaware"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, French mathematician, physicist, and sailor (b. 1733)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician, physicist, and sailor (b. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician, physicist, and sailor (b. 1733)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, English poet and translator (b. 1717)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and translator (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and translator (b. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, German-Swiss poet and playwright (b. 1813)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Georg_B%C3%<PERSON><PERSON>ner\" title=\"<PERSON>\"><PERSON></a>, German-Swiss poet and playwright (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georg_B%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss poet and playwright (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_B%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1837", "text": "<PERSON>, English bishop and philosopher (b. 1756)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop,_born_1756)\" class=\"mw-redirect\" title=\"<PERSON> (bishop, born 1756)\"><PERSON></a>, English bishop and philosopher (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop,_born_1756)\" class=\"mw-redirect\" title=\"<PERSON> (bishop, born 1756)\"><PERSON></a>, English bishop and philosopher (b. 1756)", "links": [{"title": "<PERSON> (bishop, born 1756)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop,_born_1756)"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch-German author and civil servant (b. 1820)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Multatu<PERSON>\" title=\"<PERSON>lta<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch-German author and civil servant (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Multatu<PERSON>\" title=\"<PERSON>lta<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch-German author and civil servant (b. 1820)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Multatuli"}]}, {"year": "1897", "text": "<PERSON>, German mathematician and academic (b. 1815)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Indian philosopher and politician (b. 1866)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Gopal_<PERSON>_Gokhale\" title=\"Gopal Krishna Gokhale\"><PERSON><PERSON></a>, Indian philosopher and politician (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gopal_<PERSON>_Gokhale\" title=\"Gopal Krishna Gokhale\"><PERSON><PERSON></a>, Indian philosopher and politician (b. 1866)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Krishna_Gokhale"}]}, {"year": "1916", "text": "<PERSON>, Austrian-Czech physicist and philosopher (b. 1838)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Czech physicist and philosopher (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Czech physicist and philosopher (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Austrian composer and educator (b. 1847)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Austrian composer and educator (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Austrian composer and educator (b. 1847)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1928", "text": "<PERSON>, American lawyer and businessman (b. 1856)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and businessman (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and businessman (b. 1856)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1936", "text": "<PERSON>, American general and pilot (b. 1879)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American sergeant, Medal of Honor recipient (b. 1916)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1951", "text": "<PERSON>, French novelist, essayist, and dramatist, Nobel Prize laureate  (b. 1869)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Gide\" title=\"<PERSON>\"><PERSON></a>, French novelist, essayist, and dramatist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Gide\" title=\"<PERSON>\"><PERSON></a>, French novelist, essayist, and dramatist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Gide"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Norwegian novelist, poet, and playwright, Nobel Prize laureate (b. 1859)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian novelist, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian novelist, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1859)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1953", "text": "<PERSON>, British businessman (b. 1864)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British businessman (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British businessman (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Italian-French cyclist (b. 1871)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French cyclist (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French cyclist (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American sailor, Medal of Honor recipient (b. 1877)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Greek-American pathologist, invented the Pap smear (b. 1883)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American pathologist, invented the <a href=\"https://wikipedia.org/wiki/Pap_smear\" class=\"mw-redirect\" title=\"Pap smear\">Pap smear</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American pathologist, invented the <a href=\"https://wikipedia.org/wiki/Pap_smear\" class=\"mw-redirect\" title=\"Pap smear\">Pap smear</a> (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georg<PERSON>_<PERSON>"}, {"title": "Pap smear", "link": "https://wikipedia.org/wiki/Pap_smear"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American actress (b. 1899)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, US Senator from Vermont (b. 1890)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, US Senator from Vermont (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, US Senator from Vermont (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Scottish director and producer (b. 1898)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish director and producer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish director and producer (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American trumpet player and composer (b. 1938)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Hungarian violinist (b. 1892)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English author and politician, Secretary of State for Foreign and Commonwealth Affairs (b. 1918)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Foreign and Commonwealth Affairs", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs"}]}, {"year": "1977", "text": "<PERSON>, Cuban baseball player, coach, and manager (b. 1890)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, Cuban baseball player, coach, and manager (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, Cuban baseball player, coach, and manager (b. 1890)", "links": [{"title": "<PERSON> (catcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(catcher)"}]}, {"year": "1980", "text": "<PERSON>, Scottish-Australian singer-songwriter (b. 1946)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer-songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Scott\"><PERSON></a>, Scottish-Australian singer-songwriter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actress (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, French-American physician and physiologist, Nobel Prize laureate (b. 1895)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Fr%C3%A9d%C3%A9ric_Cournand\" title=\"<PERSON>\"><PERSON></a>, French-American physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Fr%C3%A9d%C3%A9ric_Cournand\" title=\"<PERSON>\"><PERSON></a>, French-American physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Fr%C3%A9d%C3%A9ric_Cournand"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American wrestler and manager (b. 1927)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and manager (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and manager (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English director and set designer (b. 1942)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and set designer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and set designer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American businessman (b. 1918)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Polish-American author and academic (b. 1908)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author and academic (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_R<PERSON>en"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Chinese politician, 1st Vice Premier of the People's Republic of China (b. 1904)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Den<PERSON>_<PERSON>"}, {"title": "Vice Premier of the People's Republic of China", "link": "https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China"}]}, {"year": "1998", "text": "<PERSON>, American singer-songwriter and banjo player (b. 1913)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Grandpa_<PERSON>\" title=\"Grandpa <PERSON>\">Grandpa <PERSON></a>, American singer-songwriter and banjo player (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grandpa_<PERSON>\" title=\"Grandpa <PERSON>\">Grandpa <PERSON></a>, American singer-songwriter and banjo player (b. 1913)", "links": [{"title": "Grandpa <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Iraqi cleric (b. 1943)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Iraqi cleric (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Iraqi cleric (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Austrian-New Zealand painter and illustrator (b. 1928)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>ied<PERSON><PERSON><PERSON>_<PERSON>wasser\" title=\"Friedens<PERSON><PERSON> Hundertwasser\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Austrian-New Zealand painter and illustrator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ied<PERSON><PERSON><PERSON>_<PERSON>wasser\" title=\"Friedens<PERSON><PERSON> Hundertwasser\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Austrian-New Zealand painter and illustrator (b. 1928)", "links": [{"title": "Friedensreich <PERSON>was<PERSON>", "link": "https://wikipedia.org/wiki/Friedensreich_<PERSON>wasser"}]}, {"year": "2001", "text": "<PERSON> <PERSON><PERSON><PERSON>, Belgian, transgender, hardcore DJ (b. 1958)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%27N%27_<PERSON><PERSON>\" title=\"<PERSON> 'N' Eliaz\"><PERSON> <PERSON><PERSON><PERSON></a>, Belgian, transgender, hardcore DJ (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%27N%27_<PERSON><PERSON>\" title=\"<PERSON> 'N' Eliaz\"><PERSON> <PERSON><PERSON><PERSON></a>, Belgian, transgender, hardcore DJ (b. 1958)", "links": [{"title": "<PERSON> <PERSON>N' Eliaz", "link": "https://wikipedia.org/wiki/Liza_%27N%27_<PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American director and producer (b. 1913)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, French singer-songwriter (b. 1913)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American transgender LGBT activist (b. 1951)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American transgender LGBT activist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American transgender LGBT activist (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1938)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American actress and singer (b. 1921)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English-Canadian dancer and director, founded the National Ballet of Canada (b. 1921)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian dancer and director, founded the <a href=\"https://wikipedia.org/wiki/National_Ballet_of_Canada\" title=\"National Ballet of Canada\">National Ballet of Canada</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian dancer and director, founded the <a href=\"https://wikipedia.org/wiki/National_Ballet_of_Canada\" title=\"National Ballet of Canada\">National Ballet of Canada</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Ballet of Canada", "link": "https://wikipedia.org/wiki/National_Ballet_of_Canada"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Russian singer-songwriter (b. 1964)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian singer-songwriter (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian singer-songwriter (b. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v"}]}, {"year": "2008", "text": "<PERSON>, Chinese-Hong Kong actress and singer (b. 1945)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Hong Kong actress and singer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Hong Kong actress and singer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, English singer and bass player  (b. 1945)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and bass player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and bass player (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American sprinter and football player (b. 1930)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter and football player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter and football player (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American philosopher and logician (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and logician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and logician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Czech author and songwriter (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech author and songwriter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech author and songwriter (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Velinsk%C3%BD"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Russian politician, 27th Prime Minister of Russia (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian politician, 27th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia\" title=\"List of heads of government of Russia\">Prime Minister of Russia</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian politician, 27th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia\" title=\"List of heads of government of Russia\">Prime Minister of Russia</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of government of Russia", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American economist and academic (b. 1914)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Armen_Alchian\" title=\"Armen Alchian\"><PERSON><PERSON></a>, American economist and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armen_Alchian\" title=\"Armen Alchian\"><PERSON><PERSON></a>, American economist and academic (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Armen_Alchian"}]}, {"year": "2013", "text": "<PERSON>, South Korean director, producer, and screenwriter (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-soo\" title=\"<PERSON> Chu<PERSON>-soo\"><PERSON></a>, South Korean director, producer, and screenwriter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-soo\" title=\"<PERSON>-soo\"><PERSON></a>, South Korean director, producer, and screenwriter (b. 1948)", "links": [{"title": "<PERSON>o", "link": "https://wikipedia.org/wiki/Park_Chul-soo"}]}, {"year": "2013", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2013", "text": "<PERSON>, American-Japanese author and critic (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Japanese author and critic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Japanese author and critic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian farmer and politician, 22nd Canadian Minister of Agriculture (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian farmer and politician, 22nd <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)\" class=\"mw-redirect\" title=\"Minister of Agriculture (Canada)\">Canadian Minister of Agriculture</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian farmer and politician, 22nd <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)\" class=\"mw-redirect\" title=\"Minister of Agriculture (Canada)\">Canadian Minister of Agriculture</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Agriculture (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Danish footballer and manager (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Kresten_Bjerre\" title=\"<PERSON><PERSON><PERSON> Bjer<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish footballer and manager (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Krest<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish footballer and manager (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American captain and astronaut (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and astronaut (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and astronaut (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Russian engineer and astronaut (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American boxer (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (b. 1928)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_(boxer)"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian director, producer, and screenwriter (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actor, producer, and screenwriter (b. 1984)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>itte<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ittels"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Italian novelist, literary critic, and philosopher (b. 1932)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Umberto_Eco\" title=\"Umberto Eco\"><PERSON><PERSON></a>, Italian novelist, literary critic, and philosopher (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umberto_Eco\" title=\"<PERSON><PERSON> Eco\"><PERSON><PERSON></a>, Italian novelist, literary critic, and philosopher (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Umberto_Eco"}]}, {"year": "2016", "text": "<PERSON>, American author (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harper Lee\"><PERSON></a>, American author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harper Lee\"><PERSON></a>, American author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Japanese anime screenwriter (b. 1959)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese anime screenwriter (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese anime screenwriter (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Polish-Israeli sculptor and painter (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli sculptor and painter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli sculptor and painter (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American jazz guitarist (b. 1943)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz guitarist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz guitarist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, German fashion designer (b. 1933)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German fashion designer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German fashion designer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Brazilian filmmaker, actor, composer, screenwriter, and television horror host (b. 1936)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mojica_Marins\" title=\"<PERSON>\"><PERSON></a>, Brazilian filmmaker, actor, composer, screenwriter, and television horror host (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mojica_Marins\" title=\"<PERSON>\"><PERSON></a>, Brazilian filmmaker, actor, composer, screenwriter, and television horror host (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mojica_Marins"}]}, {"year": "2020", "text": "<PERSON>, American rapper (b. 1999)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Pop_Smoke\" title=\"Pop Smoke\"><PERSON> Smoke</a>, American rapper (b. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pop_Smoke\" title=\"Pop Smoke\"><PERSON> Smoke</a>, American rapper (b. 1999)", "links": [{"title": "Pop Smoke", "link": "https://wikipedia.org/wiki/Pop_Smoke"}]}]}}