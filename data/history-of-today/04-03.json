{"date": "April 3", "url": "https://wikipedia.org/wiki/April_3", "data": {"Events": [{"year": "686", "text": "Maya king <PERSON><PERSON><PERSON><PERSON>' assumes the crown of Calakmul.", "html": "686 - <a href=\"https://wikipedia.org/wiki/Maya_civilization\" title=\"Maya civilization\">Maya</a> king <a href=\"https://wikipedia.org/wiki/Yuknoom_Yich%27aak_K%27ahk%27\" class=\"mw-redirect\" title=\"Yuknoom Yi<PERSON>'aak <PERSON>ah<PERSON>'\">Yuknoom <PERSON>'aak <PERSON>ah<PERSON>'</a> assumes the crown of <a href=\"https://wikipedia.org/wiki/Calakmul\" title=\"Calakmul\">Calakmul</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maya_civilization\" title=\"Maya civilization\">Maya</a> king <a href=\"https://wikipedia.org/wiki/Yuknoom_Yich%27aak_K%27ahk%27\" class=\"mw-redirect\" title=\"Yuknoom Yi<PERSON>'aak <PERSON>ahk'\">Yuknoom <PERSON>'aak <PERSON>ahk'</a> assumes the crown of <a href=\"https://wikipedia.org/wiki/Calakmul\" title=\"Calakmul\">Calakmul</a>.", "links": [{"title": "Maya civilization", "link": "https://wikipedia.org/wiki/Maya_civilization"}, {"title": "Yuknoom Yich'aak <PERSON>ah<PERSON>'", "link": "https://wikipedia.org/wiki/Yuknoom_Yich%27aak_K%27ahk%27"}, {"title": "Calakmul", "link": "https://wikipedia.org/wiki/Calakmul"}]}, {"year": "1043", "text": "<PERSON> the Confessor is crowned King of England.", "html": "1043 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Confessor\" title=\"<PERSON> the Confessor\"><PERSON> the Confessor</a> is crowned <a href=\"https://wikipedia.org/wiki/King_of_England\" class=\"mw-redirect\" title=\"King of England\">King of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Confessor\" title=\"<PERSON> the Confessor\"><PERSON> the Confessor</a> is crowned <a href=\"https://wikipedia.org/wiki/King_of_England\" class=\"mw-redirect\" title=\"King of England\">King of England</a>.", "links": [{"title": "<PERSON> the Confessor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Confessor"}, {"title": "King of England", "link": "https://wikipedia.org/wiki/King_of_England"}]}, {"year": "1077", "text": "The Patriarchate of Friûl, the first Friulian state, is created.", "html": "1077 - The Patriarchate of Friûl, the first <a href=\"https://wikipedia.org/wiki/Friuli\" title=\"Fr<PERSON>li\">Friulian</a> state, is created.", "no_year_html": "The Patriarchate of Friûl, the first <a href=\"https://wikipedia.org/wiki/Friuli\" title=\"Friuli\">Friulian</a> state, is created.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>li"}]}, {"year": "1559", "text": "The second of two treaties making up the Peace of Cateau-Cambrésis is signed, ending the Italian Wars.", "html": "1559 - The second of two treaties making up the <a href=\"https://wikipedia.org/wiki/Peace_of_Cateau-Cambr%C3%A9sis\" class=\"mw-redirect\" title=\"Peace of Cateau-Cambrésis\">Peace of Cateau-Cambrésis</a> is signed, ending the <a href=\"https://wikipedia.org/wiki/Italian_Wars\" title=\"Italian Wars\">Italian Wars</a>.", "no_year_html": "The second of two treaties making up the <a href=\"https://wikipedia.org/wiki/Peace_of_Cateau-Cambr%C3%A9sis\" class=\"mw-redirect\" title=\"Peace of Cateau-Cambrésis\">Peace of Cateau-Cambrésis</a> is signed, ending the <a href=\"https://wikipedia.org/wiki/Italian_Wars\" title=\"Italian Wars\">Italian Wars</a>.", "links": [{"title": "Peace of Cateau-Cambrésis", "link": "https://wikipedia.org/wiki/Peace_of_Cateau-Cambr%C3%A9sis"}, {"title": "Italian Wars", "link": "https://wikipedia.org/wiki/Italian_Wars"}]}, {"year": "1589", "text": "The janissaries revolt in response to the debasement of coins.", "html": "1589 - The <a href=\"https://wikipedia.org/wiki/Janissaries\" class=\"mw-redirect\" title=\"Janissaries\">janissaries</a> <a href=\"https://wikipedia.org/wiki/Beylerbeyi_event\" title=\"Beylerbeyi event\">revolt</a> in response to the debasement of coins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Janissaries\" class=\"mw-redirect\" title=\"Janissaries\">janissaries</a> <a href=\"https://wikipedia.org/wiki/Beylerbeyi_event\" title=\"Beylerbeyi event\">revolt</a> in response to the debasement of coins.", "links": [{"title": "Janissaries", "link": "https://wikipedia.org/wiki/Janissaries"}, {"title": "Beylerbeyi event", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>i_event"}]}, {"year": "1721", "text": "<PERSON> becomes, in effect, the first Prime Minister of Great Britain, though he himself denied that title.", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes, in effect, the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a>, though he himself denied that title.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes, in effect, the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a>, though he himself denied that title.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1851", "text": "<PERSON> is crowned King of Thailand after the death of his half-brother, <PERSON>.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Mongkut\" title=\"Mongkut\">Rama IV</a> is crowned <a href=\"https://wikipedia.org/wiki/Monarchy_of_Thailand\" title=\"Monarchy of Thailand\">King</a> of <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a> after the death of his half-brother, <a href=\"https://wikipedia.org/wiki/Rama_III\" title=\"Rama III\">Rama III</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mon<PERSON>ku<PERSON>\" title=\"Mongku<PERSON>\">Rama IV</a> is crowned <a href=\"https://wikipedia.org/wiki/Monarchy_of_Thailand\" title=\"Monarchy of Thailand\">King</a> of <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a> after the death of his half-brother, <a href=\"https://wikipedia.org/wiki/Rama_III\" title=\"Rama III\">Rama III</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mongkut"}, {"title": "Monarchy of Thailand", "link": "https://wikipedia.org/wiki/Monarchy_of_Thailand"}, {"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rama_III"}]}, {"year": "1860", "text": "The first successful United States Pony Express run from St. Joseph, Missouri, to Sacramento, California, begins.", "html": "1860 - The first successful United States <a href=\"https://wikipedia.org/wiki/Pony_Express\" title=\"Pony Express\">Pony Express</a> run from <a href=\"https://wikipedia.org/wiki/St._Joseph,_Missouri\" title=\"St. Joseph, Missouri\">St. Joseph, Missouri</a>, to <a href=\"https://wikipedia.org/wiki/Sacramento,_California\" title=\"Sacramento, California\">Sacramento, California</a>, begins.", "no_year_html": "The first successful United States <a href=\"https://wikipedia.org/wiki/Pony_Express\" title=\"Pony Express\">Pony Express</a> run from <a href=\"https://wikipedia.org/wiki/St._Joseph,_Missouri\" title=\"St. Joseph, Missouri\">St. Joseph, Missouri</a>, to <a href=\"https://wikipedia.org/wiki/Sacramento,_California\" title=\"Sacramento, California\">Sacramento, California</a>, begins.", "links": [{"title": "Pony Express", "link": "https://wikipedia.org/wiki/Pony_Express"}, {"title": "St. Joseph, Missouri", "link": "https://wikipedia.org/wiki/St._Joseph,_Missouri"}, {"title": "Sacramento, California", "link": "https://wikipedia.org/wiki/Sacramento,_California"}]}, {"year": "1865", "text": "American Civil War: Union forces capture Richmond, Virginia, the capital of the Confederate States of America.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces capture <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate States of America</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces capture <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate States of America</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Richmond, Virginia", "link": "https://wikipedia.org/wiki/Richmond,_Virginia"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1882", "text": "American Old West: <PERSON> kills <PERSON>.", "html": "1882 - <a href=\"https://wikipedia.org/wiki/American_Old_West\" class=\"mw-redirect\" title=\"American Old West\">American Old West</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_(outlaw)\" title=\"<PERSON> (outlaw)\"><PERSON></a> kills <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Old_West\" class=\"mw-redirect\" title=\"American Old West\">American Old West</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_(outlaw)\" title=\"<PERSON> (outlaw)\"><PERSON></a> kills <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "American Old West", "link": "https://wikipedia.org/wiki/American_Old_West"}, {"title": "<PERSON> (outlaw)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outlaw)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON><PERSON> is granted a German patent for a light, high-speed, four-stroke engine, which he uses seven months later to create the world's first motorcycle, the Daimler Reitwagen.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Daimler\"><PERSON><PERSON><PERSON><PERSON></a> is granted a German <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for a light, high-speed, four-stroke <a href=\"https://wikipedia.org/wiki/Engine\" title=\"Engine\">engine</a>, which he uses seven months later to create the world's first motorcycle, the <a href=\"https://wikipedia.org/wiki/Daimler_Reitwagen\" title=\"Daimler Reitwagen\">Daimler Reitwagen</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is granted a German <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for a light, high-speed, four-stroke <a href=\"https://wikipedia.org/wiki/Engine\" title=\"Engine\">engine</a>, which he uses seven months later to create the world's first motorcycle, the <a href=\"https://wikipedia.org/wiki/Daimler_Reitwagen\" title=\"Daimler Reitwagen\">Daimler Reitwagen</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Engine", "link": "https://wikipedia.org/wiki/Engine"}, {"title": "Daimler Reitwagen", "link": "https://wikipedia.org/wiki/Daimler_Reitwagen"}]}, {"year": "1888", "text": "<PERSON> the <PERSON>per: The first of 11 unsolved brutal murders of women committed in or near the impoverished Whitechapel district in the East End of London, occurs.", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Ripper\" title=\"Jack the Ripper\"><PERSON> the Ripper</a>: The first of 11 <a href=\"https://wikipedia.org/wiki/Whitechapel_murders\" title=\"Whitechapel murders\">unsolved brutal murders</a> of women committed in or near the impoverished <a href=\"https://wikipedia.org/wiki/Whitechapel\" title=\"Whitechapel\">Whitechapel</a> district in the <a href=\"https://wikipedia.org/wiki/East_End_of_London\" title=\"East End of London\">East End of London</a>, occurs.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Ripper\" title=\"Jack the Ripper\"><PERSON> the Ripper</a>: The first of 11 <a href=\"https://wikipedia.org/wiki/Whitechapel_murders\" title=\"Whitechapel murders\">unsolved brutal murders</a> of women committed in or near the impoverished <a href=\"https://wikipedia.org/wiki/Whitechapel\" title=\"Whitechapel\">Whitechapel</a> district in the <a href=\"https://wikipedia.org/wiki/East_End_of_London\" title=\"East End of London\">East End of London</a>, occurs.", "links": [{"title": "<PERSON> the Rip<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}, {"title": "Whitechapel murders", "link": "https://wikipedia.org/wiki/Whitechapel_murders"}, {"title": "Whitechapel", "link": "https://wikipedia.org/wiki/Whitechapel"}, {"title": "East End of London", "link": "https://wikipedia.org/wiki/East_End_of_London"}]}, {"year": "1895", "text": "The trial in the libel case brought by <PERSON> begins, eventually resulting in his imprisonment on charges of homosexuality.", "html": "1895 - The trial in the <a href=\"https://wikipedia.org/wiki/Libel\" class=\"mw-redirect\" title=\"Libel\">libel</a> case brought by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins, eventually resulting in his imprisonment on charges of <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexuality</a>.", "no_year_html": "The trial in the <a href=\"https://wikipedia.org/wiki/Libel\" class=\"mw-redirect\" title=\"Libel\">libel</a> case brought by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> begins, eventually resulting in his imprisonment on charges of <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexuality</a>.", "links": [{"title": "Libel", "link": "https://wikipedia.org/wiki/Libel"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Homosexuality", "link": "https://wikipedia.org/wiki/Homosexuality"}]}, {"year": "1920", "text": "Attempts are made to carry out the failed assassination attempt on General <PERSON><PERSON><PERSON>, led by <PERSON><PERSON><PERSON><PERSON> by order of <PERSON><PERSON>, during the White Guard parade in Tampere, Finland.", "html": "1920 - Attempts are made to carry out the failed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#The_1920_assassination_attempt\" title=\"<PERSON>\">assassination attempt on General <PERSON></a>, led by <PERSON><PERSON><PERSON><PERSON> by order of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, during the <a href=\"https://wikipedia.org/wiki/White_Guard_(Finland)\" title=\"White Guard (Finland)\">White Guard</a> parade in <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\">Tampere</a>, Finland.", "no_year_html": "Attempts are made to carry out the failed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#The_1920_assassination_attempt\" title=\"<PERSON>\">assassination attempt on General <PERSON></a>, led by <PERSON><PERSON><PERSON><PERSON> by order of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, during the <a href=\"https://wikipedia.org/wiki/White_Guard_(Finland)\" title=\"White Guard (Finland)\">White Guard</a> parade in <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\">Tampere</a>, Finland.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>#The_1920_assassination_attempt"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "White Guard (Finland)", "link": "https://wikipedia.org/wiki/White_Guard_(Finland)"}, {"title": "Tampere", "link": "https://wikipedia.org/wiki/Tampere"}]}, {"year": "1922", "text": "<PERSON> becomes the first General Secretary of the Communist Party of the Soviet Union.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">General Secretary of the Communist Party of the Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">General Secretary of the Communist Party of the Soviet Union</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "General Secretary of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union"}]}, {"year": "1933", "text": "First flight over Mount Everest, the British Houston-Mount Everest Flight Expedition, led by the <PERSON> of Clydesdale and funded by <PERSON>, <PERSON>.", "html": "1933 - First flight over <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a>, the British <a href=\"https://wikipedia.org/wiki/Houston-Mount_Everest_Flight_Expedition\" class=\"mw-redirect\" title=\"Houston-Mount Everest Flight Expedition\">Houston-Mount Everest Flight Expedition</a>, led by the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>, 14th <PERSON> Hamilton\">Marquis of Clydesdale</a> and funded by <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>.", "no_year_html": "First flight over <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a>, the British <a href=\"https://wikipedia.org/wiki/Houston-Mount_Everest_Flight_Expedition\" class=\"mw-redirect\" title=\"Houston-Mount Everest Flight Expedition\">Houston-Mount Everest Flight Expedition</a>, led by the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_<PERSON>_<PERSON>_Hamilton\" title=\"<PERSON>, 14th <PERSON> Hamilton\">Marquis of Clydesdale</a> and funded by <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>.", "links": [{"title": "Mount Everest", "link": "https://wikipedia.org/wiki/Mount_Everest"}, {"title": "Houston-Mount Everest Flight Expedition", "link": "https://wikipedia.org/wiki/Houston-Mount_Everest_Flight_Expedition"}, {"title": "<PERSON>, 14th Duke of Hamilton", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON> is executed for the kidnapping and death of <PERSON>., the infant son of pilot <PERSON>.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is executed for the kidnapping and death of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_kidnapping\" title=\"<PERSON><PERSON><PERSON><PERSON> kidnapping\"><PERSON>.</a>, the infant son of pilot <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is executed for the kidnapping and death of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_kidnapping\" title=\"<PERSON><PERSON><PERSON><PERSON> kidnapping\"><PERSON>.</a>, the infant son of pilot <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> kidnapping", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_kidnapping"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "World War II: Japanese forces begin an assault on the United States and Filipino troops on the Bataan Peninsula.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces begin an assault on the United States and <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">Filipino</a> troops on the <a href=\"https://wikipedia.org/wiki/Battle_of_Bataan\" title=\"Battle of Bataan\">Bataan Peninsula</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces begin an assault on the United States and <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">Filipino</a> troops on the <a href=\"https://wikipedia.org/wiki/Battle_of_Bataan\" title=\"Battle of Bataan\">Bataan Peninsula</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Commonwealth of the Philippines", "link": "https://wikipedia.org/wiki/Commonwealth_of_the_Philippines"}, {"title": "Battle of Bataan", "link": "https://wikipedia.org/wiki/Battle_of_Bataan"}]}, {"year": "1946", "text": "Japanese Lt. General <PERSON><PERSON><PERSON><PERSON> is executed in the Philippines for leading the Bataan Death March.", "html": "1946 - Japanese <a href=\"https://wikipedia.org/wiki/Lt._General\" class=\"mw-redirect\" title=\"Lt. General\">Lt. General</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is executed in the Philippines for leading the <a href=\"https://wikipedia.org/wiki/Bataan_Death_March\" title=\"Bataan Death March\">Bataan Death March</a>.", "no_year_html": "Japanese <a href=\"https://wikipedia.org/wiki/Lt._General\" class=\"mw-redirect\" title=\"Lt. General\">Lt. General</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is executed in the Philippines for leading the <a href=\"https://wikipedia.org/wiki/Bataan_Death_March\" title=\"Bataan Death March\">Bataan Death March</a>.", "links": [{"title": "Lt. General", "link": "https://wikipedia.org/wiki/Lt._General"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Bataan Death March", "link": "https://wikipedia.org/wiki/Bataan_Death_March"}]}, {"year": "1948", "text": "Cold War: U.S. President <PERSON> signs the Marshall Plan, authorizing $5 billion in aid for 16 countries.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Marshall_Plan\" title=\"Marshall Plan\">Marshall Plan</a>, authorizing $5 billion in aid for 16 countries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Marshall_Plan\" title=\"Marshall Plan\">Marshall Plan</a>, authorizing $5 billion in aid for 16 countries.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Marshall Plan", "link": "https://wikipedia.org/wiki/Marshall_Plan"}]}, {"year": "1948", "text": "In Jeju Province, South Korea, a civil-war-like period of violence and human rights abuses known as the Jeju uprising begins.", "html": "1948 - In <a href=\"https://wikipedia.org/wiki/Jeju_Province\" title=\"Jeju Province\">Jeju Province</a>, South Korea, a civil-war-like period of violence and <a href=\"https://wikipedia.org/wiki/Human_rights_abuses\" class=\"mw-redirect\" title=\"Human rights abuses\">human rights abuses</a> known as the <a href=\"https://wikipedia.org/wiki/Jeju_uprising\" title=\"Jeju uprising\">Jeju uprising</a> begins.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Jeju_Province\" title=\"Jeju Province\">Jeju Province</a>, South Korea, a civil-war-like period of violence and <a href=\"https://wikipedia.org/wiki/Human_rights_abuses\" class=\"mw-redirect\" title=\"Human rights abuses\">human rights abuses</a> known as the <a href=\"https://wikipedia.org/wiki/Jeju_uprising\" title=\"Jeju uprising\">Jeju uprising</a> begins.", "links": [{"title": "Jeju Province", "link": "https://wikipedia.org/wiki/Jeju_Province"}, {"title": "Human rights abuses", "link": "https://wikipedia.org/wiki/Human_rights_abuses"}, {"title": "Jeju uprising", "link": "https://wikipedia.org/wiki/Jeju_uprising"}]}, {"year": "1955", "text": "The American Civil Liberties Union announces it will defend <PERSON>'s book Howl against obscenity charges.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/American_Civil_Liberties_Union\" title=\"American Civil Liberties Union\">American Civil Liberties Union</a> announces it will defend <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s book <i><a href=\"https://wikipedia.org/wiki/Howl_(poem)\" title=\"Howl (poem)\">Howl</a></i> against <a href=\"https://wikipedia.org/wiki/Obscenity\" title=\"Obscenity\">obscenity</a> charges.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/American_Civil_Liberties_Union\" title=\"American Civil Liberties Union\">American Civil Liberties Union</a> announces it will defend <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s book <i><a href=\"https://wikipedia.org/wiki/Howl_(poem)\" title=\"Howl (poem)\">Howl</a></i> against <a href=\"https://wikipedia.org/wiki/Obscenity\" title=\"Obscenity\">obscenity</a> charges.", "links": [{"title": "American Civil Liberties Union", "link": "https://wikipedia.org/wiki/American_Civil_Liberties_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Howl (poem)", "link": "https://wikipedia.org/wiki/Howl_(poem)"}, {"title": "Obscenity", "link": "https://wikipedia.org/wiki/Obscenity"}]}, {"year": "1956", "text": "Hudsonville-Standale tornado: The western half of the Lower Peninsula of Michigan is struck by a deadly F5 tornado.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Hudsonville%E2%80%93Standale_tornado_of_April_1956\" class=\"mw-redirect\" title=\"Hudsonville-Standale tornado of April 1956\">Hudsonville-Standale tornado</a>: The western half of the <a href=\"https://wikipedia.org/wiki/Lower_Peninsula_of_Michigan\" title=\"Lower Peninsula of Michigan\">Lower Peninsula of Michigan</a> is struck by a deadly <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">F5</a> tornado.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hudsonville%E2%80%93Standale_tornado_of_April_1956\" class=\"mw-redirect\" title=\"Hudsonville-Standale tornado of April 1956\">Hudsonville-Standale tornado</a>: The western half of the <a href=\"https://wikipedia.org/wiki/Lower_Peninsula_of_Michigan\" title=\"Lower Peninsula of Michigan\">Lower Peninsula of Michigan</a> is struck by a deadly <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">F5</a> tornado.", "links": [{"title": "Hudsonville-Standale tornado of April 1956", "link": "https://wikipedia.org/wiki/Hudsonville%E2%80%93Standale_tornado_of_April_1956"}, {"title": "Lower Peninsula of Michigan", "link": "https://wikipedia.org/wiki/Lower_Peninsula_of_Michigan"}, {"title": "Fujita scale", "link": "https://wikipedia.org/wiki/Fujita_scale"}]}, {"year": "1968", "text": "<PERSON> delivers his \"I've Been to the Mountaintop\" speech; he was assassinated the next day.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> delivers his \"<a href=\"https://wikipedia.org/wiki/I%27ve_Been_to_the_Mountaintop\" title=\"I've Been to the Mountaintop\">I've Been to the Mountaintop</a>\" speech; he was assassinated the next day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a> delivers his \"<a href=\"https://wikipedia.org/wiki/I%27ve_Been_to_the_Mountaintop\" title=\"I've Been to the Mountaintop\">I've Been to the Mountaintop</a>\" speech; he was assassinated the next day.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "I've Been to the Mountaintop", "link": "https://wikipedia.org/wiki/I%27ve_Been_to_the_Mountaintop"}]}, {"year": "1969", "text": "Vietnam War: United States Secretary of Defense <PERSON> announces that the United States will start to \"Vietnamize\" the war effort.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that the United States will start to \"<a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamize</a>\" the war effort.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that the United States will start to \"<a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamize</a>\" the war effort.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vietnamization", "link": "https://wikipedia.org/wiki/Vietnamization"}]}, {"year": "1973", "text": "<PERSON> of Motorola makes the first handheld mobile phone call to <PERSON> of Bell Labs.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Motorola\" title=\"Motorola\">Motorola</a> makes the <a href=\"https://wikipedia.org/wiki/History_of_mobile_phones\" title=\"History of mobile phones\">first handheld mobile phone call</a> to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gel\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Bell_Labs\" title=\"Bell Labs\">Bell Labs</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Motorola\" title=\"Motorola\">Motorola</a> makes the <a href=\"https://wikipedia.org/wiki/History_of_mobile_phones\" title=\"History of mobile phones\">first handheld mobile phone call</a> to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gel\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Bell_Labs\" title=\"Bell Labs\">Bell Labs</a>.", "links": [{"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(inventor)"}, {"title": "Motorola", "link": "https://wikipedia.org/wiki/Motorola"}, {"title": "History of mobile phones", "link": "https://wikipedia.org/wiki/History_of_mobile_phones"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gel"}, {"title": "Bell Labs", "link": "https://wikipedia.org/wiki/Bell_Labs"}]}, {"year": "1974", "text": "The 1974 Super Outbreak occurs, the second largest tornado outbreak in recorded history (after the 2011 Super Outbreak). The death toll is 315, with nearly 5,500 injured.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/1974_Super_Outbreak\" title=\"1974 Super Outbreak\">The 1974 Super Outbreak</a> occurs, the second largest tornado outbreak in recorded history (after the <a href=\"https://wikipedia.org/wiki/2011_Super_Outbreak\" title=\"2011 Super Outbreak\">2011 Super Outbreak</a>). The death toll is 315, with nearly 5,500 injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1974_Super_Outbreak\" title=\"1974 Super Outbreak\">The 1974 Super Outbreak</a> occurs, the second largest tornado outbreak in recorded history (after the <a href=\"https://wikipedia.org/wiki/2011_Super_Outbreak\" title=\"2011 Super Outbreak\">2011 Super Outbreak</a>). The death toll is 315, with nearly 5,500 injured.", "links": [{"title": "1974 Super Outbreak", "link": "https://wikipedia.org/wiki/1974_Super_Outbreak"}, {"title": "2011 Super Outbreak", "link": "https://wikipedia.org/wiki/2011_Super_Outbreak"}]}, {"year": "1975", "text": "Vietnam War: Operation Babylift, a mass evacuation of children in the closing stages of the war begins.", "html": "1975 - Vietnam War: <a href=\"https://wikipedia.org/wiki/Operation_Babylift\" title=\"Operation Babylift\">Operation Babylift</a>, a mass evacuation of children in the closing stages of the war begins.", "no_year_html": "Vietnam War: <a href=\"https://wikipedia.org/wiki/Operation_Babylift\" title=\"Operation Babylift\">Operation Babylift</a>, a mass evacuation of children in the closing stages of the war begins.", "links": [{"title": "Operation Babylift", "link": "https://wikipedia.org/wiki/Operation_Babylift"}]}, {"year": "1975", "text": "<PERSON> refuses to play in a chess match against <PERSON><PERSON><PERSON>, giving <PERSON><PERSON><PERSON> the title of World Champion by default.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/World_Chess_Championship_1975\" title=\"World Chess Championship 1975\">refuses to play</a> in a <a href=\"https://wikipedia.org/wiki/Chess\" title=\"Chess\">chess</a> match against <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, giving <PERSON><PERSON><PERSON> the title of World Champion by default.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/World_Chess_Championship_1975\" title=\"World Chess Championship 1975\">refuses to play</a> in a <a href=\"https://wikipedia.org/wiki/Chess\" title=\"Chess\">chess</a> match against <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, giving <PERSON><PERSON><PERSON> the title of World Champion by default.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "World Chess Championship 1975", "link": "https://wikipedia.org/wiki/World_Chess_Championship_1975"}, {"title": "Chess", "link": "https://wikipedia.org/wiki/Chess"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "US Congress restores a federal trust relationship with the 501 members of the Shivwits, Kanosh, Koosharem, and the Indian Peaks and Cedar City bands of the Paiute people of Utah.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Us_congress\" class=\"mw-redirect\" title=\"Us congress\">US Congress</a> restores a federal trust relationship with the 501 members of the <a href=\"https://wikipedia.org/wiki/Shivwits_Band_of_Paiutes\" title=\"Shivwits Band of Paiutes\">Shivwits</a>, Kanosh, Koosharem, and the Indian Peaks and Cedar City bands of the <a href=\"https://wikipedia.org/wiki/Paiute\" title=\"Paiute\">Paiute</a> people of Utah.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Us_congress\" class=\"mw-redirect\" title=\"Us congress\">US Congress</a> restores a federal trust relationship with the 501 members of the <a href=\"https://wikipedia.org/wiki/Shivwits_Band_of_Paiutes\" title=\"Shivwits Band of Paiutes\">Shivwits</a>, Kanosh, Koosharem, and the Indian Peaks and Cedar City bands of the <a href=\"https://wikipedia.org/wiki/Paiute\" title=\"Paiute\">Paiute</a> people of Utah.", "links": [{"title": "Us congress", "link": "https://wikipedia.org/wiki/Us_congress"}, {"title": "Shivwits Band of Paiutes", "link": "https://wikipedia.org/wiki/Shivwits_Band_of_Paiutes"}, {"title": "Paiute", "link": "https://wikipedia.org/wiki/Paiute"}]}, {"year": "1981", "text": "The Osborne 1, the first successful portable computer, is unveiled at the West Coast Computer Faire in San Francisco.", "html": "1981 - The <a href=\"https://wikipedia.org/wiki/Osborne_1\" title=\"Osborne 1\">Osborne 1</a>, the first successful <a href=\"https://wikipedia.org/wiki/Portable_computer\" title=\"Portable computer\">portable computer</a>, is unveiled at the <a href=\"https://wikipedia.org/wiki/West_Coast_Computer_Faire\" title=\"West Coast Computer Faire\">West Coast Computer Faire</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Osborne_1\" title=\"Osborne 1\">Osborne 1</a>, the first successful <a href=\"https://wikipedia.org/wiki/Portable_computer\" title=\"Portable computer\">portable computer</a>, is unveiled at the <a href=\"https://wikipedia.org/wiki/West_Coast_Computer_Faire\" title=\"West Coast Computer Faire\">West Coast Computer Faire</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "links": [{"title": "Osborne 1", "link": "https://wikipedia.org/wiki/Osborne_1"}, {"title": "Portable computer", "link": "https://wikipedia.org/wiki/Portable_computer"}, {"title": "West Coast Computer Faire", "link": "https://wikipedia.org/wiki/West_Coast_Computer_Faire"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}]}, {"year": "1989", "text": "The US Supreme Court upholds the jurisdictional rights of tribal courts under the Indian Child Welfare Act of 1978 in Mississippi Choctaw Band v. <PERSON>field.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/US_supreme_court\" class=\"mw-redirect\" title=\"US supreme court\">US Supreme Court</a> upholds the jurisdictional rights of <a href=\"https://wikipedia.org/wiki/Tribal_court\" title=\"Tribal court\">tribal courts</a> under the <a href=\"https://wikipedia.org/wiki/Indian_Child_Welfare_Act\" title=\"Indian Child Welfare Act\">Indian Child Welfare Act of 1978</a> in <a href=\"https://wikipedia.org/wiki/Mississippi_Band_of_Choctaw_Indians_v._<PERSON>field\" title=\"Mississippi Band of Choctaw Indians v. <PERSON>field\">Mississippi Choctaw Band v. <PERSON>field</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/US_supreme_court\" class=\"mw-redirect\" title=\"US supreme court\">US Supreme Court</a> upholds the jurisdictional rights of <a href=\"https://wikipedia.org/wiki/Tribal_court\" title=\"Tribal court\">tribal courts</a> under the <a href=\"https://wikipedia.org/wiki/Indian_Child_Welfare_Act\" title=\"Indian Child Welfare Act\">Indian Child Welfare Act of 1978</a> in <a href=\"https://wikipedia.org/wiki/Mississippi_Band_of_Choctaw_Indians_v._<PERSON>field\" title=\"Mississippi Band of Choctaw Indians v. <PERSON>field\">Mississippi Choctaw Band v. <PERSON>field</a>.", "links": [{"title": "US supreme court", "link": "https://wikipedia.org/wiki/US_supreme_court"}, {"title": "Tribal court", "link": "https://wikipedia.org/wiki/Tribal_court"}, {"title": "Indian Child Welfare Act", "link": "https://wikipedia.org/wiki/Indian_Child_Welfare_Act"}, {"title": "Mississippi Band of Choctaw Indians v. <PERSON>field", "link": "https://wikipedia.org/wiki/Mississippi_Band_of_Choctaw_Indians_v._<PERSON>field"}]}, {"year": "1993", "text": "The outcome of the Grand National horse race is declared void for the first (and only) time", "html": "1993 - The outcome of the <a href=\"https://wikipedia.org/wiki/Grand_National\" title=\"Grand National\">Grand National</a> horse race is declared void for the first (and only) time", "no_year_html": "The outcome of the <a href=\"https://wikipedia.org/wiki/Grand_National\" title=\"Grand National\">Grand National</a> horse race is declared void for the first (and only) time", "links": [{"title": "Grand National", "link": "https://wikipedia.org/wiki/Grand_National"}]}, {"year": "1996", "text": "Suspected \"Unabomber\" <PERSON> is captured at his Montana cabin in the United States.", "html": "1996 - Suspected \"Unabomber\" <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is captured at his <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a> cabin in the United States.", "no_year_html": "Suspected \"Unabomber\" <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is captured at his <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a> cabin in the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Montana", "link": "https://wikipedia.org/wiki/Montana"}]}, {"year": "1996", "text": "A United States Air Force Boeing T-43 crashes near Dubrovnik Airport in Croatia, killing 35, including Secretary of Commerce <PERSON>.", "html": "1996 - A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Boeing_T-43\" title=\"Boeing T-43\">Boeing T-43</a> <a href=\"https://wikipedia.org/wiki/1996_Croatia_USAF_CT-43_crash\" title=\"1996 Croatia USAF CT-43 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Dubrovnik_Airport\" title=\"Dubrovnik Airport\">Dubrovnik Airport</a> in <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a>, killing 35, including <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">Secretary of Commerce</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Boeing_T-43\" title=\"Boeing T-43\">Boeing T-43</a> <a href=\"https://wikipedia.org/wiki/1996_Croatia_USAF_CT-43_crash\" title=\"1996 Croatia USAF CT-43 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Dubrovnik_Airport\" title=\"Dubrovnik Airport\">Dubrovnik Airport</a> in <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a>, killing 35, including <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">Secretary of Commerce</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Boeing T-43", "link": "https://wikipedia.org/wiki/Boeing_T-43"}, {"title": "1996 Croatia USAF CT-43 crash", "link": "https://wikipedia.org/wiki/1996_Croatia_USAF_CT-43_crash"}, {"title": "Dubrovnik Airport", "link": "https://wikipedia.org/wiki/Dubrovnik_Airport"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "United States Secretary of Commerce", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Commerce"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "The Thalit massacre begins in Algeria; all but one of the 53 inhabitants of Thalit are killed by guerrillas.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Thalit_massacre\" title=\"Thalit massacre\">Thalit massacre</a> begins in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>; all but one of the 53 inhabitants of <a href=\"https://wikipedia.org/wiki/Thalit\" title=\"Thalit\">Thali<PERSON></a> are killed by guerrillas.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Thalit_massacre\" title=\"Thalit massacre\">Thalit massacre</a> begins in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>; all but one of the 53 inhabitants of <a href=\"https://wikipedia.org/wiki/Thalit\" title=\"Thalit\">Thali<PERSON></a> are killed by guerrillas.", "links": [{"title": "<PERSON><PERSON><PERSON> massacre", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_massacre"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thalit"}]}, {"year": "2000", "text": "United States v. Microsoft Corp.: Microsoft is ruled to have violated United States antitrust law by keeping \"an oppressive thumb\" on its competitors.", "html": "2000 - <i><a href=\"https://wikipedia.org/wiki/United_States_v._Microsoft_Corp._(2001)\" class=\"mw-redirect\" title=\"United States v. Microsoft Corp. (2001)\">United States v. Microsoft Corp.</a></i>: <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> is ruled to have violated <a href=\"https://wikipedia.org/wiki/United_States_antitrust_law\" title=\"United States antitrust law\">United States antitrust law</a> by keeping \"an oppressive thumb\" on its competitors.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/United_States_v._Microsoft_Corp._(2001)\" class=\"mw-redirect\" title=\"United States v. Microsoft Corp. (2001)\">United States v. Microsoft Corp.</a></i>: <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> is ruled to have violated <a href=\"https://wikipedia.org/wiki/United_States_antitrust_law\" title=\"United States antitrust law\">United States antitrust law</a> by keeping \"an oppressive thumb\" on its competitors.", "links": [{"title": "United States v. Microsoft Corp. (2001)", "link": "https://wikipedia.org/wiki/United_States_v._Microsoft_Corp._(2001)"}, {"title": "Microsoft", "link": "https://wikipedia.org/wiki/Microsoft"}, {"title": "United States antitrust law", "link": "https://wikipedia.org/wiki/United_States_antitrust_law"}]}, {"year": "2004", "text": "Islamic terrorists involved in the 2004 Madrid train bombings are trapped by the police in their apartment and kill themselves.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Islamic_terrorists\" class=\"mw-redirect\" title=\"Islamic terrorists\">Islamic terrorists</a> involved in the <a href=\"https://wikipedia.org/wiki/2004_Madrid_train_bombings\" title=\"2004 Madrid train bombings\">2004 Madrid train bombings</a> are trapped by the police in their apartment and kill themselves.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Islamic_terrorists\" class=\"mw-redirect\" title=\"Islamic terrorists\">Islamic terrorists</a> involved in the <a href=\"https://wikipedia.org/wiki/2004_Madrid_train_bombings\" title=\"2004 Madrid train bombings\">2004 Madrid train bombings</a> are trapped by the police in their apartment and kill themselves.", "links": [{"title": "Islamic terrorists", "link": "https://wikipedia.org/wiki/Islamic_terrorists"}, {"title": "2004 Madrid train bombings", "link": "https://wikipedia.org/wiki/2004_Madrid_train_bombings"}]}, {"year": "2007", "text": "Conventional-Train World Speed Record: A French TGV train on the LGV Est high speed line sets an official new world speed record of 574.8 km/h (159.6 m/s, 357.2 mph).", "html": "2007 - <a href=\"https://wikipedia.org/wiki/TGV_world_speed_record#Record_of_2007\" title=\"TGV world speed record\">Conventional-Train World Speed Record</a>: A French <a href=\"https://wikipedia.org/wiki/TGV\" title=\"TGV\">TGV</a> train on the <a href=\"https://wikipedia.org/wiki/LGV_Est\" title=\"LGV Est\">LGV Est</a> high speed line sets an official new world speed record of 574.8 km/h (159.6 m/s, 357.2 mph).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TGV_world_speed_record#Record_of_2007\" title=\"TGV world speed record\">Conventional-Train World Speed Record</a>: A French <a href=\"https://wikipedia.org/wiki/TGV\" title=\"TGV\">TGV</a> train on the <a href=\"https://wikipedia.org/wiki/LGV_Est\" title=\"LGV Est\">LGV Est</a> high speed line sets an official new world speed record of 574.8 km/h (159.6 m/s, 357.2 mph).", "links": [{"title": "TGV world speed record", "link": "https://wikipedia.org/wiki/TGV_world_speed_record#Record_of_2007"}, {"title": "TGV", "link": "https://wikipedia.org/wiki/TGV"}, {"title": "LGV Est", "link": "https://wikipedia.org/wiki/LGV_Est"}]}, {"year": "2008", "text": "ATA Airlines, once one of the ten largest U.S. passenger airlines and largest charter airline, files for bankruptcy for the second time in five years and ceases all operations.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/ATA_Airlines\" title=\"ATA Airlines\">ATA Airlines</a>, once one of the ten largest U.S. passenger airlines and largest charter airline, files for bankruptcy for the second time in five years and ceases all operations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/ATA_Airlines\" title=\"ATA Airlines\">ATA Airlines</a>, once one of the ten largest U.S. passenger airlines and largest charter airline, files for bankruptcy for the second time in five years and ceases all operations.", "links": [{"title": "ATA Airlines", "link": "https://wikipedia.org/wiki/ATA_Airlines"}]}, {"year": "2008", "text": "Texas law enforcement cordons off the FLDS's YFZ Ranch. Eventually 533 women and children will be taken into state custody.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> law enforcement cordons off the <a href=\"https://wikipedia.org/wiki/Fundamentalist_Church_of_Jesus_Christ_of_Latter-Day_Saints\" title=\"Fundamentalist Church of Jesus Christ of Latter-Day Saints\">FLDS</a>'s <a href=\"https://wikipedia.org/wiki/YFZ_Ranch\" title=\"YFZ Ranch\">YFZ Ranch</a>. Eventually 533 women and children will be taken into state custody.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> law enforcement cordons off the <a href=\"https://wikipedia.org/wiki/Fundamentalist_Church_of_Jesus_Christ_of_Latter-Day_Saints\" title=\"Fundamentalist Church of Jesus Christ of Latter-Day Saints\">FLDS</a>'s <a href=\"https://wikipedia.org/wiki/YFZ_Ranch\" title=\"YFZ Ranch\">YFZ Ranch</a>. Eventually 533 women and children will be taken into state custody.", "links": [{"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "Fundamentalist Church of Jesus Christ of Latter-Day Saints", "link": "https://wikipedia.org/wiki/Fundamentalist_Church_of_Jesus_Christ_of_Latter-Day_Saints"}, {"title": "YFZ Ranch", "link": "https://wikipedia.org/wiki/YFZ_Ranch"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> opens fire at the American Civic Association immigration center in Binghamton, New York, killing thirteen and wounding four before committing suicide.", "html": "2009 - <PERSON><PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/2009_Binghamton_shooting\" title=\"2009 Binghamton shooting\">opens fire</a> at the <a href=\"https://wikipedia.org/wiki/American_Civic_Association_(Binghamton)\" title=\"American Civic Association (Binghamton)\">American Civic Association</a> immigration center in <a href=\"https://wikipedia.org/wiki/Binghamton,_New_York\" title=\"Binghamton, New York\">Binghamton, New York</a>, killing thirteen and wounding four before committing suicide.", "no_year_html": "<PERSON><PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/2009_Binghamton_shooting\" title=\"2009 Binghamton shooting\">opens fire</a> at the <a href=\"https://wikipedia.org/wiki/American_Civic_Association_(Binghamton)\" title=\"American Civic Association (Binghamton)\">American Civic Association</a> immigration center in <a href=\"https://wikipedia.org/wiki/Binghamton,_New_York\" title=\"Binghamton, New York\">Binghamton, New York</a>, killing thirteen and wounding four before committing suicide.", "links": [{"title": "2009 Binghamton shooting", "link": "https://wikipedia.org/wiki/2009_Binghamton_shooting"}, {"title": "American Civic Association (Binghamton)", "link": "https://wikipedia.org/wiki/American_Civic_Association_(Binghamton)"}, {"title": "Binghamton, New York", "link": "https://wikipedia.org/wiki/Binghamton,_New_York"}]}, {"year": "2010", "text": "Apple Inc. released the first generation iPad, a tablet computer.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple Inc.</a> released the <a href=\"https://wikipedia.org/wiki/IPad_(1st_generation)\" title=\"IPad (1st generation)\">first generation iPad</a>, a <a href=\"https://wikipedia.org/wiki/Tablet_computer\" title=\"Tablet computer\">tablet computer</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple Inc.</a> released the <a href=\"https://wikipedia.org/wiki/IPad_(1st_generation)\" title=\"IPad (1st generation)\">first generation iPad</a>, a <a href=\"https://wikipedia.org/wiki/Tablet_computer\" title=\"Tablet computer\">tablet computer</a>.", "links": [{"title": "Apple Inc.", "link": "https://wikipedia.org/wiki/Apple_Inc."}, {"title": "IPad (1st generation)", "link": "https://wikipedia.org/wiki/IPad_(1st_generation)"}, {"title": "Tablet computer", "link": "https://wikipedia.org/wiki/Tablet_computer"}]}, {"year": "2013", "text": "More than 50 people die in floods resulting from record-breaking rainfall in La Plata and Buenos Aires, Argentina.", "html": "2013 - More than 50 people die in <a href=\"https://wikipedia.org/wiki/2013_Argentina_floods\" title=\"2013 Argentina floods\">floods</a> resulting from record-breaking rainfall in <a href=\"https://wikipedia.org/wiki/La_Plata\" title=\"La Plata\">La Plata</a> and <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, Argentina.", "no_year_html": "More than 50 people die in <a href=\"https://wikipedia.org/wiki/2013_Argentina_floods\" title=\"2013 Argentina floods\">floods</a> resulting from record-breaking rainfall in <a href=\"https://wikipedia.org/wiki/La_Plata\" title=\"La Plata\">La Plata</a> and <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>, Argentina.", "links": [{"title": "2013 Argentina floods", "link": "https://wikipedia.org/wiki/2013_Argentina_floods"}, {"title": "La Plata", "link": "https://wikipedia.org/wiki/La_Plata"}, {"title": "Buenos Aires", "link": "https://wikipedia.org/wiki/Buenos_Aires"}]}, {"year": "2016", "text": "The Panama Papers, a leak of legal documents, reveals information on 214,488 offshore companies.", "html": "2016 - The <a href=\"https://wikipedia.org/wiki/Panama_Papers\" title=\"Panama Papers\">Panama Papers</a>, a <a href=\"https://wikipedia.org/wiki/News_leak\" title=\"News leak\">leak</a> of legal documents, reveals information on 214,488 <a href=\"https://wikipedia.org/wiki/Offshore_financial_centre\" title=\"Offshore financial centre\">offshore companies</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Panama_Papers\" title=\"Panama Papers\">Panama Papers</a>, a <a href=\"https://wikipedia.org/wiki/News_leak\" title=\"News leak\">leak</a> of legal documents, reveals information on 214,488 <a href=\"https://wikipedia.org/wiki/Offshore_financial_centre\" title=\"Offshore financial centre\">offshore companies</a>.", "links": [{"title": "Panama Papers", "link": "https://wikipedia.org/wiki/Panama_Papers"}, {"title": "News leak", "link": "https://wikipedia.org/wiki/News_leak"}, {"title": "Offshore financial centre", "link": "https://wikipedia.org/wiki/Offshore_financial_centre"}]}, {"year": "2017", "text": "A bomb explodes in the St Petersburg metro system, killing 14 and injuring several more people.", "html": "2017 - A <a href=\"https://wikipedia.org/wiki/2017_St._Petersburg_Metro_bombing\" class=\"mw-redirect\" title=\"2017 St. Petersburg Metro bombing\">bomb explodes</a> in the <a href=\"https://wikipedia.org/wiki/St_Petersburg\" class=\"mw-redirect\" title=\"St Petersburg\">St Petersburg</a> metro system, killing 14 and injuring several more people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2017_St._Petersburg_Metro_bombing\" class=\"mw-redirect\" title=\"2017 St. Petersburg Metro bombing\">bomb explodes</a> in the <a href=\"https://wikipedia.org/wiki/St_Petersburg\" class=\"mw-redirect\" title=\"St Petersburg\">St Petersburg</a> metro system, killing 14 and injuring several more people.", "links": [{"title": "2017 St. Petersburg Metro bombing", "link": "https://wikipedia.org/wiki/2017_St._Petersburg_Metro_bombing"}, {"title": "St Petersburg", "link": "https://wikipedia.org/wiki/St_Petersburg"}]}, {"year": "2018", "text": "YouTube headquarters shooting: A 38-year-old gunwoman opens fire at YouTube Headquarters in San Bruno, California, injuring three people before committing suicide.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/YouTube_headquarters_shooting\" title=\"YouTube headquarters shooting\">YouTube headquarters shooting</a>: A 38-year-old gunwoman opens fire at YouTube Headquarters in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_California\" title=\"San Bruno, California\">San Bruno, California</a>, injuring three people before committing suicide.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/YouTube_headquarters_shooting\" title=\"YouTube headquarters shooting\">YouTube headquarters shooting</a>: A 38-year-old gunwoman opens fire at YouTube Headquarters in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_California\" title=\"San Bruno, California\">San Bruno, California</a>, injuring three people before committing suicide.", "links": [{"title": "YouTube headquarters shooting", "link": "https://wikipedia.org/wiki/YouTube_headquarters_shooting"}, {"title": "San Bruno, California", "link": "https://wikipedia.org/wiki/San_Bruno,_California"}]}], "Births": [{"year": "1016", "text": "<PERSON><PERSON>, Chinese emperor (d. 1055)", "html": "1016 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON><PERSON> of Liao\"><PERSON><PERSON></a>, Chinese emperor (d. 1055)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON><PERSON> of Liao\"><PERSON><PERSON></a>, Chinese emperor (d. 1055)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON> of Liao", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao"}]}, {"year": "1151", "text": "<PERSON>, Russian prince (d. 1202)", "html": "1151 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian prince (d. 1202)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian prince (d. 1202)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1395", "text": "<PERSON> of Trebizond, Greek philosopher, scholar and humanist (d. 1486)", "html": "1395 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Trebizond\" title=\"<PERSON> of Trebizond\"><PERSON> of Trebizond</a>, Greek philosopher, scholar and humanist (d. 1486)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Trebizond\" title=\"<PERSON> of Trebizond\"><PERSON> of Trebizond</a>, Greek philosopher, scholar and humanist (d. 1486)", "links": [{"title": "George of Trebizond", "link": "https://wikipedia.org/wiki/George_of_Trebizond"}]}, {"year": "1438", "text": "<PERSON> of Egmont, Dutch nobleman (d. 1516)", "html": "1438 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Egmont\" title=\"<PERSON> of Egmont\"><PERSON> of Egmont</a>, Dutch nobleman (d. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Egmont\" title=\"<PERSON> of Egmont\"><PERSON> of Egmont</a>, Dutch nobleman (d. 1516)", "links": [{"title": "<PERSON> of Egmont", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Egmont"}]}, {"year": "1529", "text": "<PERSON>, German mathematician and astronomer (d. 1581)", "html": "1529 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and astronomer (d. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and astronomer (d. 1581)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1540", "text": "<PERSON>, Italian noblewoman, the eldest daughter of <PERSON><PERSON><PERSON>, Grand Duke of Tuscany and <PERSON><PERSON><PERSON>. (d. 1557)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>_(1540%E2%80%931557)\" title=\"<PERSON> (1540-1557)\"><PERSON></a>, Italian noblewoman, the eldest daughter of <PERSON><PERSON><PERSON>, Grand Duke of Tuscany and <PERSON><PERSON><PERSON>. (d. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>_(1540%E2%80%931557)\" title=\"<PERSON> (1540-1557)\"><PERSON></a>, Italian noblewoman, the eldest daughter of <PERSON><PERSON><PERSON>, Grand Duke of Tuscany and <PERSON><PERSON><PERSON>. (d. 1557)", "links": [{"title": "<PERSON> (1540-1557)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>_(1540%E2%80%931557)"}]}, {"year": "1593", "text": "<PERSON>, English poet (d. 1633)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (d. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (d. 1633)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON>, duke of Lorraine (d. 1690)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON></a>, duke of Lorraine (d. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON></a>, duke of Lorraine (d. 1690)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine"}]}, {"year": "1682", "text": "<PERSON><PERSON>, German organist and composer (d. 1750)", "html": "1682 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ber\"><PERSON><PERSON></a>, German organist and composer (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German organist and composer (d. 1750)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ber"}]}, {"year": "1693", "text": "<PERSON>, English ornithologist and entomologist (d. 1773)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(naturalist)\" title=\"<PERSON> (naturalist)\"><PERSON></a>, English ornithologist and entomologist (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(naturalist)\" title=\"<PERSON> (naturalist)\"><PERSON></a>, English ornithologist and entomologist (d. 1773)", "links": [{"title": "<PERSON> (naturalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(naturalist)"}]}, {"year": "1715", "text": "<PERSON>, English physician, physicist, and botanist (d. 1787)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English physician, physicist, and botanist (d. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English physician, physicist, and botanist (d. 1787)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_(scientist)"}]}, {"year": "1764", "text": "<PERSON>, English surgeon and anatomist (d. 1831)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, English surgeon and anatomist (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, English surgeon and anatomist (d. 1831)", "links": [{"title": "<PERSON> (surgeon)", "link": "https://wikipedia.org/wiki/<PERSON>(surgeon)"}]}, {"year": "1769", "text": "<PERSON>, Danish-Prussian politician and diplomat (d. 1835)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/Christian_G%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Prussian politician and diplomat (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_G%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Prussian politician and diplomat (d. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_G%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON><PERSON>, Greek general (d. 1843)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (d. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (d. 1843)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, French doctor who performed the first successful tracheotomy (d. 1862)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French doctor who performed the first successful tracheotomy (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French doctor who performed the first successful tracheotomy (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian religious leader (d. 1830)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/Swami<PERSON>ayan\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian religious leader (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swami<PERSON>ayan\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian religious leader (d. 1830)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1782", "text": "<PERSON>, American general (d. 1841)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (d. 1841)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}]}, {"year": "1783", "text": "<PERSON>, American short story writer, essayist, biographer, historian (d. 1859)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/Washington_Irving\" title=\"Washington Irving\"><PERSON></a>, American short story writer, essayist, biographer, historian (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Washington_Irving\" title=\"Washington Irving\"><PERSON></a>, American short story writer, essayist, biographer, historian (d. 1859)", "links": [{"title": "<PERSON> Irving", "link": "https://wikipedia.org/wiki/Washington_Irving"}]}, {"year": "1791", "text": "<PERSON>, English diarist, mountaineer, and traveller (d. 1840)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diarist, mountaineer, and traveller (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diarist, mountaineer, and traveller (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, American admiral, geographer, and explorer (d. 1877)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral, geographer, and explorer (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral, geographer, and explorer (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, English educational and social reformer (d. 1877)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educational and <a href=\"https://wikipedia.org/wiki/Social_reformer\" class=\"mw-redirect\" title=\"Social reformer\">social reformer</a> (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educational and <a href=\"https://wikipedia.org/wiki/Social_reformer\" class=\"mw-redirect\" title=\"Social reformer\">social reformer</a> (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Social reformer", "link": "https://wikipedia.org/wiki/Social_reformer"}]}, {"year": "1814", "text": "<PERSON>, American religious leader, 5th President of The Church of Jesus Christ of Latter-day Saints (d. 1901)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 5th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 5th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1822", "text": "<PERSON>, American minister, historian, and author (d. 1909)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, historian, and author (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, historian, and author (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, American lieutenant and journalist (d. 1861)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Derby\"><PERSON></a>, American lieutenant and journalist (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_Derby\" title=\"George Derby\"><PERSON></a>, American lieutenant and journalist (d. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Derby"}]}, {"year": "1823", "text": "<PERSON>, American politician (d. 1878)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>weed\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>weed"}]}, {"year": "1826", "text": "<PERSON>, American businessman (d. 1900)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, American botanist and author (d. 1921)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and author (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and author (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1842", "text": "<PERSON><PERSON><PERSON>, American colonel (d. 1864)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel (d. 1864)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Chilean lawyer and captain (d. 1879)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and captain (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and captain (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, English author (d. 1893)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Talbot <PERSON>\"><PERSON></a>, English author (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Reed"}]}, {"year": "1858", "text": "<PERSON>, Canadian rower (d. 1937)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian rower (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian rower (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, Dutch psychiatrist and author (d. 1932)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch psychiatrist and author (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch psychiatrist and author (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, Swiss target shooter (d. 1943)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss target shooter (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss target shooter (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress and singer (d. 1956)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Misting<PERSON><PERSON>\" title=\"<PERSON>sting<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress and singer (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sting<PERSON><PERSON>\" title=\"<PERSON>sting<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress and singer (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Canadian actress, director, and producer (d. 1958)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress, director, and producer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress, director, and producer (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, Czech businessman, founded Bata Shoes (d. 1932)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Ba%C5%A5a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech businessman, founded <a href=\"https://wikipedia.org/wiki/Bata_Shoes\" class=\"mw-redirect\" title=\"Bata Shoes\">Bata Shoes</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Ba%C5%A5a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>a_Shoes\" class=\"mw-redirect\" title=\"Bata Shoes\">Bata Shoes</a> (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Ba%C5%A5a"}, {"title": "<PERSON>a Shoes", "link": "https://wikipedia.org/wiki/<PERSON>a_Shoes"}]}, {"year": "1880", "text": "<PERSON>, Jewish-Austrian philosopher and author (d. 1903)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish-Austrian philosopher and author (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish-Austrian philosopher and author (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Italian journalist and politician, 30th Prime Minister of Italy (d. 1954)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alcide_<PERSON>_<PERSON>i"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1882", "text": "<PERSON>, Canadian archbishop (d. 1952)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, Japanese philosopher and author (d. 1937)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese philosopher and author (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese philosopher and author (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1885", "text": "<PERSON>, Canadian-American director, producer, and screenwriter (d. 1981)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director, producer, and screenwriter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director, producer, and screenwriter (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American cartoonist (d. 1954)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Fisher"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian botanist and academic (d. 1944)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian botanist and academic (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian botanist and academic (d. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, English colonial and explorer (d. 1960)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_John_<PERSON>\" title=\"St John Philby\"><PERSON> <PERSON></a>, English colonial and explorer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"St John Philby\"><PERSON></a>, English colonial and explorer (d. 1960)", "links": [{"title": "St John Philby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON>, American actor and singer (d. 1953)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer (d. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 24th <PERSON><PERSON><PERSON><PERSON> (d. 1956)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/%C5%8Ctori_Tanigor%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 24th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8Ctori_Tanigor%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 24th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%8Ctori_Tanigor%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese admiral (d. 1966)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Nishiz%C5%8D_Tsu<PERSON>hara\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nishiz%C5%8D_T<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nishiz%C5%8D_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American admiral (d. 1972)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON><PERSON>, Romanian violinist and composer (d. 1949)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Grigora%C8%99_Dinicu\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian violinist and composer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grigora%C8%99_Din<PERSON>u\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian violinist and composer (d. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Grigora%C8%99_Dinicu"}]}, {"year": "1893", "text": "<PERSON>, English actor (d. 1943)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 1943)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1895", "text": "<PERSON>-<PERSON><PERSON><PERSON>, Italian-American composer and educator (d. 1968)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>esco\" title=\"<PERSON>\"><PERSON></a>, Italian-American composer and educator (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>co\" title=\"<PERSON>\"><PERSON></a>, Italian-American composer and educator (d. 1968)", "links": [{"title": "<PERSON>-Tedesco", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Tedesco"}]}, {"year": "1895", "text": "<PERSON><PERSON>, American pianist and composer (d. 1971)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Australian golfer (d. 1970)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Australian golfer (d. 1970)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Greek general (d. 1989)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON><PERSON>_Tsakalotos\" title=\"Thrasy<PERSON><PERSON> Tsakalotos\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek general (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON><PERSON>_Tsakalotos\" title=\"Thrasy<PERSON><PERSON> Tsakalotos\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek general (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>ulos_T<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, English footballer and manager (d. 1958)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (d. 1958)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1898", "text": "<PERSON>, American actor, singer, and producer (d. 1981)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and producer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and producer (d. 1981)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1898", "text": "<PERSON>, American publisher, co-founded Time magazine (d. 1967)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Luce\"><PERSON></a>, American publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/Time_(magazine)\" title=\"Time (magazine)\">Time</a></i> magazine (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Luce\"><PERSON></a>, American publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/Time_(magazine)\" title=\"Time (magazine)\">Time</a></i> magazine (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Time (magazine)", "link": "https://wikipedia.org/wiki/Time_(magazine)"}]}, {"year": "1900", "text": "<PERSON>, Lebanese lawyer and politician, 7th President of Lebanon (d. 1987)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Lebanon\" title=\"President of Lebanon\">President of Lebanon</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Lebanon\" title=\"President of Lebanon\">President of Lebanon</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Lebanon", "link": "https://wikipedia.org/wiki/President_of_Lebanon"}]}, {"year": "1900", "text": "<PERSON>, Canadian lawyer and politician, 1st Lieutenant Governor of Newfoundland (d. 1958)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Newfoundland_and_Labrador\" title=\"Lieutenant Governor of Newfoundland and Labrador\">Lieutenant Governor of Newfoundland</a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Newfoundland_and_Labrador\" title=\"Lieutenant Governor of Newfoundland and Labrador\">Lieutenant Governor of Newfoundland</a> (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Lieutenant Governor of Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Newfoundland_and_Labrador"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Indian social reformer and freedom fighter (d. 1988)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hyay\" title=\"<PERSON><PERSON><PERSON>hyay\"><PERSON><PERSON><PERSON></a>, Indian social reformer and freedom fighter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hya<PERSON>\" title=\"<PERSON><PERSON><PERSON>hyay\"><PERSON><PERSON><PERSON></a>, Indian social reformer and freedom fighter (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Chattopadhyay"}]}, {"year": "1904", "text": "<PERSON> Eyes <PERSON>, American actor and stuntman (d. 1999)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Iron_Eyes_Cody\" title=\"Iron Eyes Cody\"><PERSON> Eyes <PERSON></a>, American actor and stuntman (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iron_Eyes_Cody\" title=\"Iron Eyes Cody\"><PERSON> Eyes <PERSON></a>, American actor and stuntman (d. 1999)", "links": [{"title": "Iron Eyes Cody", "link": "https://wikipedia.org/wiki/Iron_Eyes_Cody"}]}, {"year": "1904", "text": "<PERSON>, American dancer (d. 1979)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, American furniture designer (d. 1976)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American furniture designer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American furniture designer (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American general (d. 1965)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Australian public servant (d. 1990)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hook\"><PERSON></a>, Australian public servant (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Canadian-American actress (d. 1956)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, English-Scottish surgeon and academic (d. 2001)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish surgeon and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish surgeon and academic (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Polish-American runner (d. 1980)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American runner (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American runner (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82awa_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1912", "text": "<PERSON>, New Zealand-English author (d. 1982)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English author (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English author (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Greek physician and politician (d. 1963)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek physician and politician (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek physician and politician (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Norwegian politician, 18th Prime Minister of Norway (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian politician, 18th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian politician, 18th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Norway", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Norway"}]}, {"year": "1914", "text": "<PERSON>, Canadian ice hockey player (d. 2008)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Indian field marshal (d. 2008)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian field marshal (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian field marshal (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Dutch politician and naval officer, Prime Minister of the Netherlands (d. 2016)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician and naval officer, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician and naval officer, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Turkish physician and academic (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/%C4%B0hsan_Do%C4%9Framac%C4%B1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish physician and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0hsan_Do%C4%9Framac%C4%B1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish physician and academic (d. 2010)", "links": [{"title": "İhsan <PERSON>ğrama<PERSON>ı", "link": "https://wikipedia.org/wiki/%C4%B0hsan_Do%C4%9Framac%C4%B1"}]}, {"year": "1916", "text": "<PERSON>, American journalist and author (d. 1997)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English cricketer (d. 1988)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Catalan composer (d. 1991)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Catalan composer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Catalan composer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actress (d. 2014)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1918)\" title=\"<PERSON> (actress, born 1918)\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1918)\" title=\"<PERSON> (actress, born 1918)\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON> (actress, born 1918)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1918)"}]}, {"year": "1918", "text": "<PERSON>, Canadian composer and conductor (d. 2000)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and conductor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and conductor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American songwriter and composer (d. 2015)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American songwriter and composer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American songwriter and composer (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, French-Canadian actress and singer (d. 2008)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Canadian actress and singer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Canadian actress and singer (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>era"}]}, {"year": "1920", "text": "<PERSON>, American composer and conductor (d. 2001)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 43rd <PERSON><PERSON><PERSON><PERSON> (d. 1977)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Jun<PERSON>uke\" title=\"<PERSON><PERSON><PERSON><PERSON> Junnosuke\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 43rd <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>uke\" title=\"<PERSON><PERSON><PERSON><PERSON> Jun<PERSON>uke\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 43rd <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1921", "text": "<PERSON>, American actor (d. 1991)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actress (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Ukrainian hurdler (d. 1996)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian hurdler (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian hurdler (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American singer and actress (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Day\"><PERSON></a>, American singer and actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Doris Day\"><PERSON></a>, American singer and actress (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American poet and academic (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American actor and director (d. 2004)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brand<PERSON>\"><PERSON><PERSON></a>, American actor and director (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and director (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Russian sergeant and sniper (d. 1945)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian sergeant and sniper (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian sergeant and sniper (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English pilot and politician, Secretary of State for Industry (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Industry</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Industry</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Business, Innovation and Skills", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills"}]}, {"year": "1926", "text": "<PERSON>, American baseball player, manager, and coach (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and coach (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and coach (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American colonel, pilot, and astronaut (d. 1967)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American general and engineer (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and engineer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and engineer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2003)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Canadian priest, founded Dans la Rue (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian priest, founded <a href=\"https://wikipedia.org/wiki/Dan<PERSON>_<PERSON>_Rue\" title=\"Dans la Rue\">Dans <PERSON> Rue</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian priest, founded <a href=\"https://wikipedia.org/wiki/Dan<PERSON>_<PERSON>_Rue\" title=\"Dans la Rue\">Dans la Rue</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON>"}, {"title": "Dans la Rue", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Rue"}]}, {"year": "1928", "text": "<PERSON>, American basketball player and coach (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English chef and television personality (d. 1999)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and television personality (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and television personality (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi engineer and architect, co-designed the Willis Tower and John Hancock Center (d. 1982)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi engineer and architect, co-designed the <a href=\"https://wikipedia.org/wiki/Willis_Tower\" title=\"Willis Tower\">Willis Tower</a> and <a href=\"https://wikipedia.org/wiki/John_<PERSON>_Center\" title=\"John Hancock Center\">John Hancock Center</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi engineer and architect, co-designed the <a href=\"https://wikipedia.org/wiki/Willis_Tower\" title=\"Willis Tower\">Willis Tower</a> and <a href=\"https://wikipedia.org/wiki/John_<PERSON>_Center\" title=\"John Hancock Center\">John Hancock Center</a> (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Willis Tower", "link": "https://wikipedia.org/wiki/Willis_Tower"}, {"title": "John <PERSON> Center", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Danish lawyer and politician, 37th Prime Minister of Denmark (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hl%C3%BCter\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hl%C3%BCter\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a> (d. 2021)", "links": [{"title": "Poul Schlüter", "link": "https://wikipedia.org/wiki/Poul_Schl%C3%BCter"}, {"title": "Prime Minister of Denmark", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Denmark"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American soldier, lawyer, and politician, 41st Governor of Florida (d. 1998)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Lawton_Chiles\" title=\"Lawton Chiles\"><PERSON><PERSON></a>, American soldier, lawyer, and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lawton_Chiles\" title=\"Lawton Chiles\"><PERSON><PERSON></a>, American soldier, lawyer, and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a> (d. 1998)", "links": [{"title": "<PERSON>ton <PERSON>s", "link": "https://wikipedia.org/wiki/Lawton_Chiles"}, {"title": "Governor of Florida", "link": "https://wikipedia.org/wiki/Governor_of_Florida"}]}, {"year": "1930", "text": "<PERSON>, German politician, Chancellor of Germany (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic)\">Chancellor of Germany</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic)\">Chancellor of Germany</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chancellor of Germany (Federal Republic)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic)"}]}, {"year": "1930", "text": "<PERSON>, Argentinian general and politician (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Men%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Men%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mario_Benjam%C3%ADn_Men%C3%A9ndez"}]}, {"year": "1930", "text": "<PERSON>, American baseball player and coach (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American screenwriter and author (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American politician", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American golfer (d. 1985)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, New Zealand children's writer and illustrator", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand children's writer and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand children's writer and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English primatologist and anthropologist", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English primatologist and anthropologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English primatologist and anthropologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American football player (d. 2005)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2005)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1935", "text": "<PERSON>, American rabbi and author (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rabbi and author (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rabbi and author (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American organist and bandleader (d. 2008)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and bandleader (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and bandleader (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American saxophonist and flute player (d. 1987)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and flute player (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and flute player (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter, and producer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American golfer (d. 2018)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, French composer (d. 1975)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_de_Roubaix\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_de_Roubaix\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_R<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American baseball player and coach (d. 2012)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American economist and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter (d. 2004)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American singer-songwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American singer-songwriter (d. 2004)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American soul singer (d. 1984)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Philipp%C3%A9_Wynne\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soul singer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philipp%C3%A9_Wynne\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soul singer (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Philipp%C3%A9_Wynne"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2015)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Billy <PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Billy <PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Mexican composer (d. 2021)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican composer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican composer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English actor, director, and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian singer-songwriter and pianist (d. 1986)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and pianist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and pianist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Japanese admiral, the first female star officer of the Japan Self-Defense Forces", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Hikaru_Saeki\" title=\"Hikaru Saek<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese admiral, the first female <a href=\"https://wikipedia.org/wiki/Flag_officer\" title=\"Flag officer\">star officer</a> of the <a href=\"https://wikipedia.org/wiki/Japan_Self-Defense_Forces\" title=\"Japan Self-Defense Forces\">Japan Self-Defense Forces</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hikaru_Saeki\" title=\"Hikaru Saeki\"><PERSON><PERSON><PERSON></a>, Japanese admiral, the first female <a href=\"https://wikipedia.org/wiki/Flag_officer\" title=\"Flag officer\">star officer</a> of the <a href=\"https://wikipedia.org/wiki/Japan_Self-Defense_Forces\" title=\"Japan Self-Defense Forces\">Japan Self-Defense Forces</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hikar<PERSON>_Sa<PERSON>i"}, {"title": "Flag officer", "link": "https://wikipedia.org/wiki/Flag_officer"}, {"title": "Japan Self-Defense Forces", "link": "https://wikipedia.org/wiki/Japan_Self-Defense_Forces"}]}, {"year": "1944", "text": "<PERSON>, Australian biologist and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Orlando\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American author and journalist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Doon_Arbus\" title=\"Doon Arbus\"><PERSON><PERSON></a>, American author and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Doon_Arbus\" title=\"Doon Arbus\"><PERSON><PERSON></a>, American author and journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>n_<PERSON><PERSON>us"}]}, {"year": "1945", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rent"}]}, {"year": "1945", "text": "<PERSON>, French actress (d. 2022)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1946", "text": "<PERSON>, English bass player (d. 1992)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Spanish film actress (d. 2024)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish film actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish film actress (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Polish politician, Prime Minister of Poland", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "1947", "text": "<PERSON>, Swedish composer (d. 2013)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Canadian author and screenwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cousture\" title=\"<PERSON><PERSON><PERSON> Cousture\"><PERSON><PERSON><PERSON></a>, Canadian author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Coust<PERSON>\" title=\"<PERSON><PERSON><PERSON> Cousture\"><PERSON><PERSON><PERSON></a>, Canadian author and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cousture"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Dutch academic, politician, and diplomat, 11th Secretary General of NATO", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Dutch academic, politician, and diplomat, 11th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Dutch academic, politician, and diplomat, 11th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a>", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aa<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Secretary General of NATO", "link": "https://wikipedia.org/wiki/Secretary_General_of_NATO"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Mexican economist and politician, 53rd President of Mexico", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican economist and politician, 53rd <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican economist and politician, 53rd <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1949", "text": "<PERSON>, American football player and actor (d. 1992)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON> <PERSON><PERSON>, English philosopher and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and academic", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Sri Lankan cricketer and economist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer and economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer and economist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English trade union leader", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trade union leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trade union leader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, British academician and educator", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academician and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Annette Dolphin\"><PERSON></a>, British academician and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American lawyer and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(U.S._politician)\" class=\"mw-redirect\" title=\"<PERSON> (U.S. politician)\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(U.S._politician)\" class=\"mw-redirect\" title=\"<PERSON> (U.S. politician)\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON> (U.S. politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(U.S._politician)"}]}, {"year": "1953", "text": "<PERSON>, American author and illustrator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 56th Yo<PERSON><PERSON>na (d. 2022)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Wakanohana_Kanji_II\" title=\"Wakanohana Kanji II\">W<PERSON><PERSON>hana Kanji II</a>, Japanese sumo wrestler, the 56th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wakanohana_Kanji_II\" title=\"Wakanohana Kanji II\"><PERSON><PERSON><PERSON><PERSON> Kanji II</a>, Japanese sumo wrestler, the 56th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2022)", "links": [{"title": "Wakanohana Kanji II", "link": "https://wikipedia.org/wiki/Wakanohana_Kanji_II"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1953", "text": "<PERSON>, American boxer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_(boxer)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Brus<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Indian physician and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian physician and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian physician and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Estonian politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Serbian director and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i%C4%87"}]}, {"year": "1956", "text": "<PERSON>, Spanish musician and actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Spanish musician and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Spanish musician and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bo<PERSON>%C3%A9"}]}, {"year": "1956", "text": "<PERSON>, American game show host (d. 1996)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor, comedian, producer and television host", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, producer and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, producer and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American scholar, musician, and memoirist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, musician, and memoirist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, musician, and memoirist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American photographer (d. 1981)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor and activist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch singer-songwriter, guitarist, and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American baseball player (d. 1993)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>s\"><PERSON></a>, American baseball player (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tim_Crews"}]}, {"year": "1961", "text": "<PERSON>, American actor and comedian", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American baseball player and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Indian actress and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Prada\"><PERSON></a>, Indian actress and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Prada\"><PERSON></a>, Indian actress and politician", "links": [{"title": "<PERSON> P<PERSON>", "link": "https://wikipedia.org/wiki/Jaya_Prada"}]}, {"year": "1963", "text": "<PERSON>, Australian rugby league player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, American guitarist and songwriter (d. 1993)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American guitarist and songwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American guitarist and songwriter (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C<PERSON>s_<PERSON>liva"}]}, {"year": "1964", "text": "<PERSON>, Italian footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English banker and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English banker and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English banker and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Danish cyclist and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Rii<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish cyclist and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Rii<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish cyclist and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>s"}]}, {"year": "1964", "text": "<PERSON>, English rugby player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian politician, 45th Premier of South Australia", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 45th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 45th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Pakistani pop singer-songwriter, lawyer and social activist (d. 2000)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani pop singer-songwriter, lawyer and social activist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani pop singer-songwriter, lawyer and social activist (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian race car driver", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1967", "text": "<PERSON>, American chef and author", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Cora\" title=\"<PERSON> Cora\"><PERSON></a>, American chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Cora\" title=\"Cat Cora\"><PERSON></a>, American chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Cora"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American basketball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Per<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brent_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Romanian director and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian race car driver and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Bahamian-Canadian singer-songwriter and actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bach\"><PERSON></a>, Bahamian-Canadian singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bach\"><PERSON></a>, Bahamian-Canadian singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English actress (d. 2001)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English director and performer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and performer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and performer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American football player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian footballer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian wrestler and trainer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler and trainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Latvian footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Vit%C4%81lijs_Astafjevs\" title=\"Vitālij<PERSON> Astafjevs\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vit%C4%81lijs_Astafjevs\" title=\"Vitāli<PERSON><PERSON> Astafjevs\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vit%C4%81lijs_Astafjevs"}]}, {"year": "1971", "text": "<PERSON>, French race car driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American skier", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Picabo_Street\" title=\"Picabo Street\">Picabo Street</a>, American skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Picabo_Street\" title=\"Picabo Street\">Picabo Street</a>, American skier", "links": [{"title": "Picabo Street", "link": "https://wikipedia.org/wiki/Picabo_Street"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress and director", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, French tennis player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ud"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1974", "text": "<PERSON>, American basketball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Welsh model and actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Welsh model and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Welsh model and actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1975", "text": "<PERSON>, American ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Nigerian-American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American comedian and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Spears\"><PERSON><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Spears\"><PERSON><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Japanese baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, French tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_E<PERSON>cud%C3%A9"}]}, {"year": "1978", "text": "<PERSON>, English actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, German-American tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, South African rugby player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian footballer and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Belarusian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American pastor and transgender activist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and transgender activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and transgender activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American trumpet player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Canadian actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lders\" title=\"<PERSON><PERSON> Smulders\"><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lder<PERSON>\" title=\"<PERSON><PERSON>mulder<PERSON>\"><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mulders"}]}, {"year": "1983", "text": "<PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Belgian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Argentinian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Maxi_L%C3%B3pez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maxi_L%C3%B3pez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maxi_L%C3%B3pez"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Finnish race car driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1985", "text": "<PERSON><PERSON>, English singer-songwriter and producer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American soccer player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Italian cyclist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Spanish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Sergio_S%C3%A1nchez_Ortega\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sergio_S%C3%A1nchez_Ortega\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sergio_S%C3%<PERSON><PERSON>ez_Or<PERSON>ga"}]}, {"year": "1987", "text": "<PERSON>, American actress, writer, and producer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, writer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, writer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, English sprinter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ng<PERSON>\" title=\"<PERSON><PERSON>ng<PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ng<PERSON>\" title=\"<PERSON><PERSON>ng<PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>pungin"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Chancellor\" title=\"<PERSON><PERSON> Chancellor\"><PERSON><PERSON> Chancellor</a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Chancellor\" title=\"<PERSON><PERSON> Chancellor\"><PERSON><PERSON> Chancellor</a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1988", "text": "<PERSON>, Dutch footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, French footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian rugby player and footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Israel_Folau\" title=\"Israel Folau\">Israel <PERSON></a>, Australian rugby player and footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Folau\" title=\"Israel Folau\">Israel <PERSON></a>, Australian rugby player and footballer", "links": [{"title": "Israel Folau", "link": "https://wikipedia.org/wiki/Israel_Folau"}]}, {"year": "1989", "text": "<PERSON>, Australian rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Sri Lankan cricketer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Thisara Perera\"><PERSON><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Perera\"><PERSON><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thisara_Perera"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Iranian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>gle\" title=\"<PERSON> Brengle\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>\" title=\"<PERSON> Brengle\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Brengle"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian actress and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American actress and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Italian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Russian swimmer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Senegalese footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Konat%C3%A9\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>nat%C3%A9\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>%C3%A9"}]}, {"year": "1994", "text": "<PERSON><PERSON>, New Zealand rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American mass murderer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>oof\"><PERSON><PERSON></a>, American mass murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>oof\"><PERSON><PERSON></a>, American mass murderer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Japanese tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hibi\"><PERSON></a>, Japanese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>bi\" title=\"<PERSON> Hibi\"><PERSON></a>, Japanese tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mayo_Hibi"}]}, {"year": "1997", "text": "<PERSON>, Brazilian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actress, model and singer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Jackson\" title=\"<PERSON> Jackson\"><PERSON></a>, American actress, model and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Jackson\" title=\"<PERSON> Jackson\"><PERSON></a>, American actress, model and singer", "links": [{"title": "Paris Jackson", "link": "https://wikipedia.org/wiki/Paris_Jackson"}]}, {"year": "1999", "text": "<PERSON><PERSON>-<PERSON><PERSON>, New Zealand-Samoan rugby league player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-Samoan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-Samoan rugby league player", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "33", "text": "Jesus of Nazareth", "html": "33 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Jesus\"><PERSON> of Nazareth</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Jesus\"><PERSON> of Nazareth</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "963", "text": "<PERSON>, Duke of Aquitaine (b. 915)", "html": "963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aquitaine\" class=\"mw-redirect\" title=\"<PERSON> of Aquitaine\"><PERSON></a>, Duke of Aquitaine (b. 915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aquitaine\" class=\"mw-redirect\" title=\"<PERSON> of Aquitaine\"><PERSON></a>, Duke of Aquitaine (b. 915)", "links": [{"title": "<PERSON> of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aquitaine"}]}, {"year": "1153", "text": "<PERSON><PERSON><PERSON><PERSON> ibn <PERSON>, vizier of the Fatimid Caliphate", "html": "1153 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_ibn_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> ibn <PERSON>Sal<PERSON>\"><PERSON><PERSON><PERSON><PERSON> ibn <PERSON></a>, vizier of the Fatimid Caliphate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_ibn_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> ibn <PERSON>Sallar\"><PERSON><PERSON><PERSON><PERSON> ibn <PERSON></a>, vizier of the Fatimid Caliphate", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1171", "text": "<PERSON> Milly, seventh Grand Master of the Knights Templar (b. c. 1120)", "html": "1171 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mill<PERSON>\" title=\"<PERSON> of Milly\"><PERSON> Milly</a>, seventh <a href=\"https://wikipedia.org/wiki/Grand_Master_of_the_Knights_Templar\" class=\"mw-redirect\" title=\"Grand Master of the Knights Templar\">Grand Master of the Knights Templar</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1120</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mill<PERSON>\" title=\"<PERSON> of Milly\"><PERSON> Milly</a>, seventh <a href=\"https://wikipedia.org/wiki/Grand_Master_of_the_Knights_Templar\" class=\"mw-redirect\" title=\"Grand Master of the Knights Templar\">Grand Master of the Knights Templar</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1120</span>)", "links": [{"title": "<PERSON> of Milly", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Grand Master of the Knights Templar", "link": "https://wikipedia.org/wiki/Grand_Master_of_the_Knights_Templar"}]}, {"year": "1203", "text": "<PERSON>, Duke of Brittany (b. 1187)", "html": "1203 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1187)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1187)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1253", "text": "<PERSON> of Chichester", "html": "1253 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Chichester\" title=\"<PERSON> of Chichester\"><PERSON> of Chichester</a>", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Chichester\" title=\"<PERSON> of Chichester\"><PERSON> of Chichester</a>", "links": [{"title": "<PERSON> of Chichester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1287", "text": "<PERSON> <PERSON><PERSON> (b. 1210)", "html": "1287 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Honorius_IV\" title=\"<PERSON> Honorius IV\"><PERSON> <PERSON><PERSON> IV</a> (b. 1210)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Honorius_IV\" title=\"Pope Honorius IV\"><PERSON> <PERSON><PERSON> IV</a> (b. 1210)", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_IV"}]}, {"year": "1325", "text": "<PERSON><PERSON><PERSON>, Sufi saint (b. 1238)", "html": "1325 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sufi saint (b. 1238)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sufi saint (b. 1238)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1350", "text": "<PERSON><PERSON> <PERSON>, Duke of Burgundy (b. 1295)", "html": "1350 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_IV,_Duke_of_Burgundy\" title=\"<PERSON><PERSON> <PERSON>, Duke of Burgundy\"><PERSON><PERSON> <PERSON>, Duke of Burgundy</a> (b. 1295)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_IV,_Duke_of_Burgundy\" title=\"<PERSON><PERSON> <PERSON>, Duke of Burgundy\"><PERSON><PERSON> <PERSON>, Duke of Burgundy</a> (b. 1295)", "links": [{"title": "<PERSON><PERSON> <PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/O<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1538", "text": "<PERSON>, Countess of Wiltshire (b. 1480)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Wiltshire\" title=\"<PERSON>, Countess of Wiltshire\"><PERSON>, Countess of Wiltshire</a> (b. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Wiltshire\" title=\"<PERSON>, Countess of Wiltshire\"><PERSON>, Countess of Wiltshire</a> (b. 1480)", "links": [{"title": "<PERSON>, Countess of Wiltshire", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Wiltshire"}]}, {"year": "1545", "text": "<PERSON>, Spanish chronicler and moralist (b. 1481)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish chronicler and moralist (b. 1481)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish chronicler and moralist (b. 1481)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1606", "text": "<PERSON>, 8th Baron <PERSON>, English general and politician, Lord Lieutenant of Ireland (b. 1563)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Baron_<PERSON>\" title=\"<PERSON>, 8th Baron <PERSON>\"><PERSON>, 8th Baron <PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Baron_<PERSON>\" title=\"<PERSON>, 8th Baron <PERSON>\"><PERSON>, 8th Baron <PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1563)", "links": [{"title": "<PERSON>, 8th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_8th_Baron_<PERSON>"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1630", "text": "<PERSON>, 1st Earl of Anglesey, English noble (b. c.  1593)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Anglesey\" title=\"<PERSON>, 1st Earl of Anglesey\"><PERSON>, 1st Earl of Anglesey</a>, English noble (b. c.  1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Anglesey\" title=\"<PERSON>, 1st Earl of Anglesey\"><PERSON>, 1st Earl of Anglesey</a>, English noble (b. c.  1593)", "links": [{"title": "<PERSON>, 1st Earl of Anglesey", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Ang<PERSON>ey"}]}, {"year": "1637", "text": "<PERSON>, German rabbi", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rabbi", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rabbi", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_N%C3%B6rd<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian emperor, founded the Maratha Empire (b. 1630)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/Shivaji\" title=\"Shivaji\"><PERSON><PERSON><PERSON><PERSON></a>, Indian emperor, founded the <a href=\"https://wikipedia.org/wiki/Maratha_Empire\" class=\"mw-redirect\" title=\"Maratha Empire\">Maratha Empire</a> (b. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shivaji\" title=\"Shivaji\"><PERSON><PERSON><PERSON><PERSON></a>, Indian emperor, founded the <a href=\"https://wikipedia.org/wiki/Maratha_Empire\" class=\"mw-redirect\" title=\"Maratha Empire\">Maratha Empire</a> (b. 1630)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Maratha Empire", "link": "https://wikipedia.org/wiki/Maratha_Empire"}]}, {"year": "1682", "text": "<PERSON><PERSON><PERSON>, Spanish painter and educator (b. 1618)", "html": "1682 - <a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_Esteban_Murillo\" title=\"Bart<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish painter and educator (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_Esteban_Murillo\" title=\"Bart<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish painter and educator (b. 1618)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartolom%C3%A9_<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1691", "text": "<PERSON>, French-Swiss painter (b. 1608)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss painter (b. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss painter (b. 1608)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1695", "text": "<PERSON><PERSON><PERSON>, Dutch painter (b. 1636)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/Melchior_d%27Hondecoeter\" title=\"<PERSON><PERSON><PERSON>decoe<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mel<PERSON>or_d%27Hondecoeter\" title=\"<PERSON><PERSON><PERSON>decoe<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1636)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Melchior_d%27Hondecoeter"}]}, {"year": "1717", "text": "<PERSON>, French mathematician and academic (b. 1640)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1640)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1728", "text": "<PERSON>, Scottish lawyer and historian (b. 1662)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Scottish lawyer and historian (b. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Scottish lawyer and historian (b. 1662)", "links": [{"title": "<PERSON> (lawyer)", "link": "https://wikipedia.org/wiki/<PERSON>_(lawyer)"}]}, {"year": "1792", "text": "<PERSON>, English admiral (b. 1706)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (b. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (b. 1706)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish priest, historian, and author (b. 1727)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/J%C4%99dr<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish priest, historian, and author (b. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C4%99dr<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish priest, historian, and author (b. 1727)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C4%99dr<PERSON><PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, English priest (b. 1783)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest (b. 1783)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, German physicist and academic (b. 1756)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1756)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, French physician and author (b. 1780)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and author (b. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and author (b. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, English cleric, 1st Archdeacon of Lindisfarne (b. 1807)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric, 1st <a href=\"https://wikipedia.org/wiki/Archdeacon_of_Lindisfarne\" title=\"Archdeacon of Lindisfarne\">Archdeacon of Lindisfarne</a> (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric, 1st <a href=\"https://wikipedia.org/wiki/Archdeacon_of_Lindisfarne\" title=\"Archdeacon of Lindisfarne\">Archdeacon of Lindisfarne</a> (b. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Archdeacon of Lindisfarne", "link": "https://wikipedia.org/wiki/Archdeacon_of_Lindisfarne"}]}, {"year": "1846", "text": "<PERSON>, English soldier and explorer (b. 1814)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and explorer (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and explorer (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON><PERSON>, Polish-French poet and playwright (b. 1809)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/Juliusz_S%C5%82owacki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-French poet and playwright (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juliusz_S%C5%82owacki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-French poet and playwright (b. 1809)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juliusz_S%C5%82owacki"}]}, {"year": "1868", "text": "<PERSON>, Swedish composer and surgeon (b. 1796)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish composer and surgeon (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish composer and surgeon (b. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, German actress and opera singer (b. 1831)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Felicit<PERSON>_<PERSON>estvali\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actress and opera singer (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Felicit<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actress and opera singer (b. 1831)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Felicita_Vestvali"}]}, {"year": "1882", "text": "<PERSON>, American criminal and outlaw (b. 1847)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal and outlaw (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal and outlaw (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, German pianist and composer (b. 1833)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, English composer and talent agent (b. 1844)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27O<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and talent agent (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27O<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and talent agent (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_D%27Oyl<PERSON>_<PERSON><PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American lawyer and judge (b. 1814)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Canadian-English operatic soprano (b. 1847)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English operatic soprano (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English operatic soprano (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, German-American murderer (b. 1899)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American murderer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American murderer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 22nd <PERSON><PERSON><PERSON><PERSON> (b. 1877)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mineemon\" title=\"Tachi<PERSON> Mineemon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 22nd <a href=\"https://wikipedia.org/wiki/Yo<PERSON><PERSON>na\" class=\"mw-redirect\" title=\"Yo<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mineemon\" title=\"Ta<PERSON><PERSON> Mineemon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 22nd <a href=\"https://wikipedia.org/wiki/Yo<PERSON><PERSON>na\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mineemon"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yokozuna"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Hungarian academic and politician, 22nd Prime Minister of Hungary (b. 1879)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/P%C3%A1l_Teleki\" title=\"Pál Teleki\"><PERSON><PERSON><PERSON></a>, Hungarian academic and politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A1l_Teleki\" title=\"Pál Teleki\"><PERSON><PERSON><PERSON></a>, Hungarian academic and politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1879)", "links": [{"title": "<PERSON><PERSON>l Teleki", "link": "https://wikipedia.org/wiki/P%C3%A1l_Teleki"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1943", "text": "<PERSON>, German actor, director, and producer (b. 1893)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor, director, and producer (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor, director, and producer (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese general (b. 1887)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, German-American composer and pianist (b. 1900)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American composer and pianist (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American composer and pianist (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American historian, author, and journalist, founded Black History Month (b. 1875)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and journalist, founded <a href=\"https://wikipedia.org/wiki/Black_History_Month\" title=\"Black History Month\">Black History Month</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and journalist, founded <a href=\"https://wikipedia.org/wiki/Black_History_Month\" title=\"Black History Month\">Black History Month</a> (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Black History Month", "link": "https://wikipedia.org/wiki/Black_History_Month"}]}, {"year": "1951", "text": "<PERSON>, Estonian poet and playwright (b. 1890)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet and playwright (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet and playwright (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Finnish minister and politician (b. 1866)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>llanp%C3%A4%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish minister and politician (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>llan<PERSON>%C3%A4%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish minister and politician (b. 1866)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ina_Sillanp%C3%A4%C3%A4"}]}, {"year": "1957", "text": "<PERSON>, Canadian-American actor (b. 1883)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sparks\"><PERSON></a>, Canadian-American actor (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ned Sparks\"><PERSON></a>, Canadian-American actor (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sparks"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Estonian poet and author (b. 1891)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4rner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and author (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4rner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and author (b. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaan_K%C3%A4rner"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Greek composer and educator (b. 1883)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Man<PERSON>_<PERSON>is\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek composer and educator (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Man<PERSON>_<PERSON>is\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek composer and educator (b. 1883)", "links": [{"title": "Man<PERSON>", "link": "https://wikipedia.org/wiki/Man<PERSON>_Ka<PERSON>miris"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli author (b. 1890)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli author (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli author (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Avig<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American gangster (b. 1904)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American pianist and composer (b. 1892)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Ferde_Grof%C3%A9\" title=\"<PERSON>rde Grofé\"><PERSON><PERSON></a>, American pianist and composer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferde_Grof%C3%A9\" title=\"<PERSON><PERSON> Grofé\"><PERSON><PERSON></a>, American pianist and composer (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferde_Grof%C3%A9"}]}, {"year": "1975", "text": "<PERSON>, Scottish-English actress (b. 1933)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English actress (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English actress (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American physicist and academic (b. 1900)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Canadian journalist and politician (b. 1894)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English bandleader, composer, and actor (b. 1903)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bandleader, composer, and actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bandleader, composer, and actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American composer (b. 1909)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American businessman, founded Pan American World Airways (b. 1899)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Pan_American_World_Airways\" class=\"mw-redirect\" title=\"Pan American World Airways\">Pan American World Airways</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Pan_American_World_Airways\" class=\"mw-redirect\" title=\"Pan American World Airways\">Pan American World Airways</a> (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pan American World Airways", "link": "https://wikipedia.org/wiki/Pan_American_World_Airways"}]}, {"year": "1982", "text": "<PERSON>, American actor (b. 1928)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English footballer and manager (b. 1934)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English tenor and educator (b. 1910)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tenor and educator (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tenor and educator (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player (b. 1936)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American cartoonist (b. 1907)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Can<PERSON>\"><PERSON></a>, American cartoonist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Milton Caniff\"><PERSON></a>, American cartoonist (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>iff"}]}, {"year": "1990", "text": "<PERSON>, American singer (b. 1924)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American bridge player and author (b. 1901)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bridge player and author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bridge player and author (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English novelist, playwright, and critic (b. 1904)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, playwright, and critic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, playwright, and critic (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American television host (b. 1907)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television host (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television host (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American businessman (b. 1932)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Canadian businessman, co-founded Canadian Tire (b. 1902)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, co-founded <a href=\"https://wikipedia.org/wiki/Canadian_Tire\" title=\"Canadian Tire\">Canadian Tire</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, co-founded <a href=\"https://wikipedia.org/wiki/Canadian_Tire\" title=\"Canadian Tire\">Canadian Tire</a> (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Canadian Tire", "link": "https://wikipedia.org/wiki/Canadian_Tire"}]}, {"year": "1996", "text": "<PERSON>, American captain and politician, 30th United States Secretary of Commerce (b. 1941)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 30th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 30th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Commerce", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Commerce"}]}, {"year": "1997", "text": "<PERSON>, Norwegian chemical engineer and inventor (b. 1921)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian chemical engineer and inventor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian chemical engineer and inventor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English mathematician and academic (b. 1900)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English composer (b. 1930)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bart\"><PERSON></a>, English composer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bart\"><PERSON></a>, English composer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian general (b. 1909)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American botanist and philosopher (b. 1946)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and philosopher (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and philosopher (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Librarian and YIVO and Yiddish language expert (b. 1909)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Librarian and YIVO and Yiddish language expert (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Librarian and YIVO and Yiddish language expert (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian lawyer and politician (b. 1944)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_G%C3%A9rin\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_G%C3%A9rin\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_G%C3%A9rin"}]}, {"year": "2007", "text": "<PERSON>, Chinese businesswoman (b. 1937)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese businesswoman (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese businesswoman (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian footballer (b. 1983)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Hrvoje_%C4%86usti%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian footballer (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hrvoje_%C4%86usti%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian footballer (b. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hrvoje_%C4%86usti%C4%87"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Spanish cartoonist and journalist (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish cartoonist and journalist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish cartoonist and journalist (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French civil servant (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French civil servant (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French civil servant (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Indian politician, 8th Governor of Karnataka (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>vin<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Karnataka\" class=\"mw-redirect\" title=\"List of Governors of Karnataka\">Governor of Karnataka</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Karnataka\" class=\"mw-redirect\" title=\"List of Governors of Karnataka\">Governor of Karnataka</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON>d_<PERSON>rain"}, {"title": "List of Governors of Karnataka", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Karnataka"}]}, {"year": "2012", "text": "Chief <PERSON>, American wrestler (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Chief_<PERSON>_<PERSON>\" title=\"Chief <PERSON>\">Chief <PERSON></a>, American wrestler (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chief_<PERSON>_<PERSON>\" title=\"Chief <PERSON>\">Chief <PERSON></a>, American wrestler (b. 1928)", "links": [{"title": "Chief <PERSON>", "link": "https://wikipedia.org/wiki/Chief_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Spanish footballer and manager (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Z%C3%A1rraga\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Z%C3%A1rraga\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Z%C3%A1rraga"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Spanish actress (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Mariv%C3%AD_Bilbao\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mariv%C3%AD_Bilbao\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish actress (b. 1930)", "links": [{"title": "Mariví Bilbao", "link": "https://wikipedia.org/wiki/Mariv%C3%AD_Bilbao"}]}, {"year": "2013", "text": "<PERSON>, German-American author and screenwriter (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American author and screenwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American author and screenwriter (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, French author, playwright, and director (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/R%C3%A9gine_Deforges\" title=\"<PERSON><PERSON><PERSON><PERSON> Deforges\"><PERSON><PERSON><PERSON><PERSON></a>, French author, playwright, and director (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9gine_Deforges\" title=\"<PERSON><PERSON><PERSON><PERSON> Defo<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author, playwright, and director (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9gine_Deforges"}]}, {"year": "2014", "text": "<PERSON>, American illustrator (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON> Prussia (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Prussia\" class=\"mw-redirect\" title=\"Prince <PERSON> of Prussia\">Prince <PERSON> of Prussia</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Prussia\" class=\"mw-redirect\" title=\"Prince <PERSON> of Prussia\">Prince <PERSON> of Prussia</a> (b. 1940)", "links": [{"title": "Prince <PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Prussia"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Serbian metropolitan (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian metropolitan (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian metropolitan (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Pavlovi%C4%87"}]}, {"year": "2014", "text": "<PERSON> \"<PERSON> Boogie\" <PERSON>, American guitarist, fiddler, and composer (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Guitar_Boogie%22_<PERSON>\" title='<PERSON> \"Guitar Boogie\" Smith'><PERSON> \"Guitar Boogie\" <PERSON></a>, American guitarist, fiddler, and composer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Guitar_<PERSON>%22_<PERSON>\" title='<PERSON> \"Guitar Boogie\" Smith'><PERSON> \"Guitar Boogie\" <PERSON></a>, American guitarist, fiddler, and composer (b. 1921)", "links": [{"title": "<PERSON> \"<PERSON> Boogie\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22G<PERSON>ar_<PERSON>%22_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American activist and author (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American drummer and songwriter (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer and songwriter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer and songwriter (b. 1950)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(drummer)"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Austrian-Israeli rabbi and author (b. 1913)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-Israeli rabbi and author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-Israeli rabbi and author (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Italian footballer and manager (b. 1932)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American anthropologist, historian, and author (b. 1913)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Joe_<PERSON>_<PERSON>\" title=\"Joe <PERSON> Crow\"><PERSON></a>, American anthropologist, historian, and author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joe_<PERSON>_<PERSON>\" title=\"Joe <PERSON> Crow\"><PERSON></a>, American anthropologist, historian, and author (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joe_Medicine_Crow"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Japanese singer and songwriter (b. 1974)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ada\" class=\"mw-redirect\" title=\"<PERSON><PERSON>ada\"><PERSON><PERSON></a>, Japanese singer and songwriter (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ada\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Wada\"><PERSON><PERSON></a>, Japanese singer and songwriter (b. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ada"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Indian classical vocalist (b. 1931)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian classical vocalist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian classical vocalist (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Canadian-American politician, 20th Governor of Montana (b. 1929)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_Montana\" class=\"mw-redirect\" title=\"Governor of Montana\">Governor of Montana</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_Montana\" class=\"mw-redirect\" title=\"Governor of Montana\">Governor of Montana</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Montana", "link": "https://wikipedia.org/wiki/Governor_of_Montana"}]}, {"year": "2022", "text": "<PERSON>, English actress (b. 1927)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/June_<PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, English actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, English actress (b. 1927)", "links": [{"title": "June Brown", "link": "https://wikipedia.org/wiki/June_Brown"}]}, {"year": "2024", "text": "<PERSON>, Australian rugby league player (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Italian architect and designer (b. 1939)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Gae<PERSON>_<PERSON>\" title=\"Gae<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian architect and designer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gae<PERSON>_<PERSON>\" title=\"Gae<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian architect and designer (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gaetano_Pesce"}]}]}}