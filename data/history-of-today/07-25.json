{"date": "July 25", "url": "https://wikipedia.org/wiki/July_25", "data": {"Events": [{"year": "306", "text": "<PERSON> is proclaimed Roman emperor by his troops.", "html": "306 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> I</a> is proclaimed <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman</a> emperor by his troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> I</a> is proclaimed <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman</a> emperor by his troops.", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}]}, {"year": "315", "text": "The Arch of Constantine is completed near the Colosseum in Rome to commemorate <PERSON> I's victory over <PERSON><PERSON><PERSON> at the Milvian Bridge.", "html": "315 - The <a href=\"https://wikipedia.org/wiki/Arch_of_Constantine\" title=\"Arch of Constantine\">Arch of Constantine</a> is completed near the <a href=\"https://wikipedia.org/wiki/Colosseum\" title=\"Colosseum\">Colosseum</a> in Rome to commemorate <PERSON> I's victory over <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Ponte_Milvio\" title=\"Ponte Milvio\">Milvian Bridge</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arch_of_Constantine\" title=\"Arch of Constantine\">Arch of Constantine</a> is completed near the <a href=\"https://wikipedia.org/wiki/Colosseum\" title=\"Colosseum\">Colosseum</a> in Rome to commemorate <PERSON> I's victory over <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Ponte_Milvio\" title=\"Ponte Milvio\">Milvian Bridge</a>.", "links": [{"title": "Arch of Constantine", "link": "https://wikipedia.org/wiki/Arch_of_Constantine"}, {"title": "Colosseum", "link": "https://wikipedia.org/wiki/Colosseum"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ent<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "677", "text": "Climax of the Siege of Thessalonica by the Slavs in a three-day assault on the city walls.", "html": "677 - Climax of the <a href=\"https://wikipedia.org/wiki/Siege_of_Thessalonica_(676%E2%80%93678)\" title=\"Siege of Thessalonica (676-678)\">Siege of Thessalonica</a> by the Slavs in a three-day assault on the city walls.", "no_year_html": "Climax of the <a href=\"https://wikipedia.org/wiki/Siege_of_Thessalonica_(676%E2%80%93678)\" title=\"Siege of Thessalonica (676-678)\">Siege of Thessalonica</a> by the Slavs in a three-day assault on the city walls.", "links": [{"title": "Siege of Thessalonica (676-678)", "link": "https://wikipedia.org/wiki/Siege_of_Thessalonica_(676%E2%80%93678)"}]}, {"year": "864", "text": "The Edict of Pistres of Charles the Bald orders defensive measures against the Vikings.", "html": "864 - The <a href=\"https://wikipedia.org/wiki/Edict_of_Pistres\" class=\"mw-redirect\" title=\"Edict of Pistres\">Edict of Pistres</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the <PERSON>ld</a> orders defensive measures against the <a href=\"https://wikipedia.org/wiki/Vikings\" title=\"Vikings\">Vikings</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Edict_of_Pistres\" class=\"mw-redirect\" title=\"Edict of Pistres\">Edict of Pistres</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a> orders defensive measures against the <a href=\"https://wikipedia.org/wiki/Vikings\" title=\"Vikings\">Vikings</a>.", "links": [{"title": "Edict of Pistres", "link": "https://wikipedia.org/wiki/Edict_of_Pistres"}, {"title": "<PERSON> the <PERSON>ld", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vikings", "link": "https://wikipedia.org/wiki/Vikings"}]}, {"year": "918", "text": "<PERSON> becomes King of Goryeo after overthrowing <PERSON><PERSON> in a coup the previous day", "html": "918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Geon\"><PERSON></a> becomes King of <a href=\"https://wikipedia.org/wiki/Goryeo\" title=\"Goryeo\">Goryeo</a> after overthrowing <a href=\"https://wikipedia.org/wiki/<PERSON>g_Ye\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in a coup the previous day", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Geon\"><PERSON></a> becomes King of <a href=\"https://wikipedia.org/wiki/Goryeo\" title=\"Goryeo\">Goryeo</a> after overthrowing <a href=\"https://wikipedia.org/wiki/Gung_Ye\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in a coup the previous day", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Goryeo", "link": "https://wikipedia.org/wiki/Goryeo"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1137", "text": "<PERSON> of Aquitaine marries <PERSON>, later King <PERSON> of France, at the Cathedral of Saint-André in Bordeaux.", "html": "1137 - <a href=\"https://wikipedia.org/wiki/Eleanor_<PERSON>_Aquitaine\" title=\"<PERSON> of Aquitaine\"><PERSON> of Aquitaine</a> marries Prince <PERSON>, later King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a>, at the <a href=\"https://wikipedia.org/wiki/Bordeaux_Cathedral\" title=\"Bordeaux Cathedral\">Cathedral of Saint-André in Bordeaux</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eleanor_<PERSON>_Aquitaine\" title=\"<PERSON> of Aquitaine\"><PERSON> of Aquitaine</a> marries Prince <PERSON>, later King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a>, at the <a href=\"https://wikipedia.org/wiki/Bordeaux_Cathedral\" title=\"Bordeaux Cathedral\">Cathedral of Saint-André in Bordeaux</a>.", "links": [{"title": "<PERSON> of Aquitaine", "link": "https://wikipedia.org/wiki/Eleanor_of_Aquitaine"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VII_of_France"}, {"title": "Bordeaux Cathedral", "link": "https://wikipedia.org/wiki/Bordeaux_Cathedral"}]}, {"year": "1139", "text": "Battle of Ourique: The Almoravids, led by <PERSON>, are defeated by Prince <PERSON><PERSON><PERSON> who is proclaimed King of Portugal.", "html": "1139 - <a href=\"https://wikipedia.org/wiki/Battle_of_Ourique\" title=\"Battle of Ourique\">Battle of Ourique</a>: The <a href=\"https://wikipedia.org/wiki/Almoravid_dynasty\" title=\"Almoravid dynasty\">Almoravids</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON></a>, are defeated by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON><PERSON><PERSON> of Portugal\"><PERSON><PERSON><PERSON></a> who is proclaimed King of Portugal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Ourique\" title=\"Battle of Ourique\">Battle of Ourique</a>: The <a href=\"https://wikipedia.org/wiki/Almoravid_dynasty\" title=\"Almoravid dynasty\">Almoravids</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON></a>, are defeated by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON><PERSON><PERSON> of Portugal\"><PERSON><PERSON><PERSON></a> who is proclaimed King of Portugal.", "links": [{"title": "Battle of Ourique", "link": "https://wikipedia.org/wiki/Battle_of_Ourique"}, {"title": "Almoravid dynasty", "link": "https://wikipedia.org/wiki/Almoravid_dynasty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1261", "text": "The city of Constantinople is recaptured by Nicaean forces under the command of <PERSON><PERSON>, re-establishing the Byzantine Empire.", "html": "1261 - The city of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> is <a href=\"https://wikipedia.org/wiki/Reconquest_of_Constantinople\" title=\"Reconquest of Constantinople\">recaptured</a> by <a href=\"https://wikipedia.org/wiki/Empire_of_Nicaea\" title=\"Empire of Nicaea\">Nicaean</a> forces under the command of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Strategopoulos\" title=\"<PERSON><PERSON> Strategopoulos\"><PERSON><PERSON> Strategopoulos</a>, re-establishing the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> is <a href=\"https://wikipedia.org/wiki/Reconquest_of_Constantinople\" title=\"Reconquest of Constantinople\">recaptured</a> by <a href=\"https://wikipedia.org/wiki/Empire_of_Nicaea\" title=\"Empire of Nicaea\">Nicaean</a> forces under the command of <a href=\"https://wikipedia.org/wiki/Alex<PERSON>_Strategopoulos\" title=\"<PERSON><PERSON> Strategopoulos\"><PERSON><PERSON> Strategopo<PERSON></a>, re-establishing the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "links": [{"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "Reconquest of Constantinople", "link": "https://wikipedia.org/wiki/Reconquest_of_Constantinople"}, {"title": "Empire of Nicaea", "link": "https://wikipedia.org/wiki/Empire_of_Nicaea"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alexios_Strategopoulos"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "1278", "text": "The naval Battle of Algeciras takes place in the context of the Spanish Reconquista resulting in a victory for the Emirate of Granada and the Maranid Dynasty over the Kingdom of Castile.", "html": "1278 - The naval <a href=\"https://wikipedia.org/wiki/Battle_of_Algeciras_(1278)\" title=\"Battle of Algeciras (1278)\">Battle of Algeciras</a> takes place in the context of the <a href=\"https://wikipedia.org/wiki/Spanish_Reconquista\" class=\"mw-redirect\" title=\"Spanish Reconquista\">Spanish Reconquista</a> resulting in a victory for the <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Emirate of Granada</a> and the Maranid Dynasty over the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a>.", "no_year_html": "The naval <a href=\"https://wikipedia.org/wiki/Battle_of_Algeciras_(1278)\" title=\"Battle of Algeciras (1278)\">Battle of Algeciras</a> takes place in the context of the <a href=\"https://wikipedia.org/wiki/Spanish_Reconquista\" class=\"mw-redirect\" title=\"Spanish Reconquista\">Spanish Reconquista</a> resulting in a victory for the <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Emirate of Granada</a> and the Maranid Dynasty over the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a>.", "links": [{"title": "Battle of Algeciras (1278)", "link": "https://wikipedia.org/wiki/Battle_of_Algeciras_(1278)"}, {"title": "Spanish Reconquista", "link": "https://wikipedia.org/wiki/Spanish_Reconquista"}, {"title": "Emirate of Granada", "link": "https://wikipedia.org/wiki/Emirate_of_Granada"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}]}, {"year": "1467", "text": "The Battle of Molinella: The first battle in Italy in which firearms are used extensively.", "html": "1467 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Molinella\" title=\"Battle of Molinella\">Battle of Molinella</a>: The first battle in Italy in which firearms are used extensively.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Molinella\" title=\"Battle of Molinella\">Battle of Molinella</a>: The first battle in Italy in which firearms are used extensively.", "links": [{"title": "Battle of Molinella", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON>lla"}]}, {"year": "1536", "text": "<PERSON><PERSON><PERSON><PERSON> on his search of El Dorado founds the city of Santiago de Cali.", "html": "1536 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_de_Belalc%C3%A1zar\" title=\"<PERSON><PERSON><PERSON><PERSON> de Belalcázar\"><PERSON><PERSON><PERSON><PERSON> Belalcázar</a> on his search of <a href=\"https://wikipedia.org/wiki/El_Dorado\" title=\"El Dorado\">El Dorado</a> founds the city of <a href=\"https://wikipedia.org/wiki/Cali\" title=\"Cali\">Santiago de Cali</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_de_Belalc%C3%A1zar\" title=\"<PERSON><PERSON><PERSON><PERSON> de Belalcázar\"><PERSON><PERSON><PERSON><PERSON> Belalcázar</a> on his search of <a href=\"https://wikipedia.org/wiki/El_Dorado\" title=\"El Dorado\">El Dorado</a> founds the city of <a href=\"https://wikipedia.org/wiki/Cali\" title=\"Cali\">Santiago de Cali</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> de Belalcázar", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1n_de_Belalc%C3%A1zar"}, {"title": "El Dorado", "link": "https://wikipedia.org/wiki/El_Dorado"}, {"title": "Cali", "link": "https://wikipedia.org/wiki/Cali"}]}, {"year": "1538", "text": "The city of Guayaquil is founded by the Spanish Conquistador <PERSON>ellana and given the name <PERSON><PERSON> y Muy Leal Ciudad de Santiago de Guayaquil.", "html": "1538 - The city of <a href=\"https://wikipedia.org/wiki/Guayaquil\" title=\"Guayaquil\">Guayaquil</a> is founded by the Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">Conquistador</a> <a href=\"https://wikipedia.org/wiki/Francisco_de_Orellana\" title=\"Francisco de Orellana\"><PERSON> de Orellana</a> and given the name <PERSON><PERSON> y <PERSON> de Santiago de Guayaquil.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Guayaquil\" title=\"Guayaquil\">Guayaquil</a> is founded by the Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">Conquistador</a> <a href=\"https://wikipedia.org/wiki/Francisco_de_Orellana\" title=\"Francisco de Orellana\"><PERSON> de Orellana</a> and given the name <PERSON><PERSON> y <PERSON>l Ciudad de Santiago de Guayaquil.", "links": [{"title": "Guayaquil", "link": "https://wikipedia.org/wiki/Guayaquil"}, {"title": "Conquistador", "link": "https://wikipedia.org/wiki/Conquistador"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>_<PERSON>"}]}, {"year": "1547", "text": "<PERSON> of France is crowned.", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> is <a href=\"https://wikipedia.org/wiki/Coronation\" title=\"Coronation\">crowned</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Henry II of France\"><PERSON> of France</a> is <a href=\"https://wikipedia.org/wiki/Coronation\" title=\"Coronation\">crowned</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "Coronation", "link": "https://wikipedia.org/wiki/Coronation"}]}, {"year": "1554", "text": "The royal wedding of <PERSON> and <PERSON> of Spain celebrated at Winchester Cathedral.", "html": "1554 - <a href=\"https://wikipedia.org/wiki/Wedding_of_<PERSON>_I_of_England_and_<PERSON>_of_Spain\" title=\"Wedding of <PERSON> I of England and Philip of Spain\">The royal wedding</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> I</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> of Spain</a> celebrated at <a href=\"https://wikipedia.org/wiki/Winchester_Cathedral\" title=\"Winchester Cathedral\">Winchester Cathedral</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wedding_of_<PERSON>_I_of_England_and_<PERSON>_of_Spain\" title=\"Wedding of <PERSON> I of England and Philip of Spain\">The royal wedding</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> I</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> of Spain</a> celebrated at <a href=\"https://wikipedia.org/wiki/Winchester_Cathedral\" title=\"Winchester Cathedral\">Winchester Cathedral</a>.", "links": [{"title": "Wedding of <PERSON> of England and <PERSON> of Spain", "link": "https://wikipedia.org/wiki/Wedding_of_<PERSON>_<PERSON>_of_England_and_<PERSON>_of_Spain"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}, {"title": "Winchester Cathedral", "link": "https://wikipedia.org/wiki/Winchester_Cathedral"}]}, {"year": "1567", "text": "Don Diego de Losada founds the city of Santiago de Leon de Caracas, modern-day Caracas, the capital city of Venezuela.", "html": "1567 - <PERSON> founds the city of Santiago de Leon de Caracas, modern-day <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas</a>, the capital city of <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>.", "no_year_html": "<PERSON> founds the city of Santiago de Leon de Caracas, modern-day <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas</a>, the capital city of <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>.", "links": [{"title": "Caracas", "link": "https://wikipedia.org/wiki/Caracas"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}]}, {"year": "1591", "text": "The <PERSON> of Parma is defeated near the Dutch city of Nijmegen by an Anglo-Dutch force led by <PERSON>.", "html": "1591 - The <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\">Duke of Parma</a> is <a href=\"https://wikipedia.org/wiki/Siege_of_Knodsenburg\" title=\"Siege of Knodsenburg\">defeated</a> near the Dutch city of <a href=\"https://wikipedia.org/wiki/Nijmegen\" title=\"Nijmegen\">Nijmegen</a> by an Anglo-Dutch force led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Orange\" class=\"mw-redirect\" title=\"<PERSON> of Orange\"><PERSON> Orange</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\">Duke of Parma</a> is <a href=\"https://wikipedia.org/wiki/Siege_of_Knodsenburg\" title=\"Siege of Knodsenburg\">defeated</a> near the Dutch city of <a href=\"https://wikipedia.org/wiki/Nijmegen\" title=\"Nijmegen\">Nijmegen</a> by an Anglo-Dutch force led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> of Orange\"><PERSON> Orange</a>.", "links": [{"title": "<PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma"}, {"title": "Siege of Knodsenburg", "link": "https://wikipedia.org/wiki/Siege_of_Knodsenburg"}, {"title": "Nijmegen", "link": "https://wikipedia.org/wiki/Nijmegen"}, {"title": "<PERSON> of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1593", "text": "<PERSON> of France publicly converts from Protestantism to Roman Catholicism.", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> publicly converts from <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestantism</a> to <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholicism</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> publicly converts from <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestantism</a> to <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholicism</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}, {"title": "Protestantism", "link": "https://wikipedia.org/wiki/Protestantism"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "1603", "text": "<PERSON> and <PERSON> and <PERSON> of <PERSON> are crowned in Westminster Abbey.", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_<PERSON>\" title=\"<PERSON> VI and I\"><PERSON> VI and I</a> and <a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> are <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Coronation of <PERSON> I\">crowned</a> in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_<PERSON>\" title=\"<PERSON> VI and I\"><PERSON> VI and I</a> and <a href=\"https://wikipedia.org/wiki/Anne_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> are <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Coronation of <PERSON> I\">crowned</a> in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "links": [{"title": "James <PERSON> and I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_I"}, {"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Denmark"}, {"title": "Coronation of <PERSON>", "link": "https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1609", "text": "The English ship Sea Venture, en route to Virginia, is deliberately driven ashore during a storm at Bermuda to prevent its sinking; the survivors go on to found a new colony there.", "html": "1609 - The English ship <i><a href=\"https://wikipedia.org/wiki/Sea_Venture\" title=\"Sea Venture\">Sea Venture</a></i>, en route to <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Virginia</a>, is deliberately driven ashore during a storm at <a href=\"https://wikipedia.org/wiki/Bermuda\" title=\"Bermuda\">Bermuda</a> to prevent its sinking; the survivors go on to found a new colony there.", "no_year_html": "The English ship <i><a href=\"https://wikipedia.org/wiki/Sea_Venture\" title=\"Sea Venture\">Sea Venture</a></i>, en route to <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Virginia</a>, is deliberately driven ashore during a storm at <a href=\"https://wikipedia.org/wiki/Bermuda\" title=\"Bermuda\">Bermuda</a> to prevent its sinking; the survivors go on to found a new colony there.", "links": [{"title": "Sea Venture", "link": "https://wikipedia.org/wiki/Sea_Venture"}, {"title": "Colony of Virginia", "link": "https://wikipedia.org/wiki/Colony_of_Virginia"}, {"title": "Bermuda", "link": "https://wikipedia.org/wiki/Bermuda"}]}, {"year": "1668", "text": "A magnitude 8.5 earthquake strikes eastern China, killing over 43,000 people.", "html": "1668 - A <a href=\"https://wikipedia.org/wiki/1668_Shandong_earthquake\" title=\"1668 Shandong earthquake\">magnitude 8.5 earthquake</a> strikes eastern China, killing over 43,000 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1668_Shandong_earthquake\" title=\"1668 Shandong earthquake\">magnitude 8.5 earthquake</a> strikes eastern China, killing over 43,000 people.", "links": [{"title": "1668 Shandong earthquake", "link": "https://wikipedia.org/wiki/1668_Shandong_earthquake"}]}, {"year": "1693", "text": "<PERSON> founds the Real Santiago de las Sabinas, now known as Sabinas Hidalgo, Nuevo León, Mexico.", "html": "1693 - <PERSON> founds the Real Santiago de las Sabinas, now known as <a href=\"https://wikipedia.org/wiki/Sabinas_Hidalgo,_Nuevo_Le%C3%B3n\" class=\"mw-redirect\" title=\"Sabinas Hidalgo, Nuevo León\">Sabinas Hidalgo, Nuevo León</a>, Mexico.", "no_year_html": "<PERSON> founds the Real Santiago de las Sabinas, now known as <a href=\"https://wikipedia.org/wiki/Sabinas_Hidalgo,_Nuevo_Le%C3%B3n\" class=\"mw-redirect\" title=\"Sabinas Hidalgo, Nuevo León\">Sabinas Hidalgo, Nuevo León</a>, Mexico.", "links": [{"title": "Sabinas Hidalgo, Nuevo León", "link": "https://wikipedia.org/wiki/Sabinas_Hidalgo,_Nuevo_Le%C3%B3n"}]}, {"year": "1718", "text": "At the behest of Tsar <PERSON>, the construction of Kadriorg Palace, dedicated to his wife <PERSON>, begins in Tallinn.", "html": "1718 - At the behest of Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the <PERSON></a>, the construction of <a href=\"https://wikipedia.org/wiki/Kadriorg_Palace\" title=\"Kadriorg Palace\">Kadriorg Palace</a>, dedicated to his wife <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\">Catherine</a>, begins in <a href=\"https://wikipedia.org/wiki/Tallinn\" title=\"Tallinn\">Tallinn</a>.", "no_year_html": "At the behest of Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, the construction of <a href=\"https://wikipedia.org/wiki/Kadriorg_Palace\" title=\"Kadriorg Palace\">Kadriorg Palace</a>, dedicated to his wife <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\">Catherine</a>, begins in <a href=\"https://wikipedia.org/wiki/Tallinn\" title=\"Tallinn\">Tallinn</a>.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}, {"title": "Kadriorg Palace", "link": "https://wikipedia.org/wiki/Kadriorg_Palace"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}, {"title": "Tallinn", "link": "https://wikipedia.org/wiki/Tallinn"}]}, {"year": "1722", "text": "<PERSON><PERSON>'s War begins along the Maine-Massachusetts border.", "html": "1722 - <a href=\"https://wikipedia.org/wiki/Du<PERSON>%27s_War\" title=\"<PERSON>mmer's War\"><PERSON>mmer's War</a> begins along the <a href=\"https://wikipedia.org/wiki/Maine\" title=\"Maine\">Maine</a>-<a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> border.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du<PERSON>%27s_War\" title=\"Dummer's War\">Dummer's War</a> begins along the <a href=\"https://wikipedia.org/wiki/Maine\" title=\"Maine\">Maine</a>-<a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> border.", "links": [{"title": "<PERSON><PERSON>'s War", "link": "https://wikipedia.org/wiki/Dummer%27s_War"}, {"title": "Maine", "link": "https://wikipedia.org/wiki/Maine"}, {"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}]}, {"year": "1755", "text": "British governor <PERSON> and the Nova Scotia Council order the deportation of the Acadians.", "html": "1755 - <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> governor <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a> Council order the <a href=\"https://wikipedia.org/wiki/Expulsion_of_the_Acadians\" title=\"Expulsion of the Acadians\">deportation</a> of the <a href=\"https://wikipedia.org/wiki/Acadians\" title=\"Acadians\">Acadians</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British</a> governor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a> Council order the <a href=\"https://wikipedia.org/wiki/Expulsion_of_the_Acadians\" title=\"Expulsion of the Acadians\">deportation</a> of the <a href=\"https://wikipedia.org/wiki/Acadians\" title=\"Acadians\">Acadians</a>.", "links": [{"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}, {"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}, {"title": "Nova Scotia", "link": "https://wikipedia.org/wiki/Nova_Scotia"}, {"title": "Expulsion of the Acadians", "link": "https://wikipedia.org/wiki/Expulsion_of_the_Acadians"}, {"title": "Acadians", "link": "https://wikipedia.org/wiki/Acadians"}]}, {"year": "1759", "text": "French and Indian War: In Western New York, British forces capture Fort Niagara from the French, who subsequently abandon Fort Rouillé.", "html": "1759 - <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: In <a href=\"https://wikipedia.org/wiki/Western_New_York\" title=\"Western New York\">Western New York</a>, British forces capture <a href=\"https://wikipedia.org/wiki/Fort_Niagara\" title=\"Fort Niagara\">Fort Niagara</a> from the French, who subsequently abandon <a href=\"https://wikipedia.org/wiki/Fort_Rouill%C3%A9\" title=\"Fort Rouillé\">Fort Rouillé</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: In <a href=\"https://wikipedia.org/wiki/Western_New_York\" title=\"Western New York\">Western New York</a>, British forces capture <a href=\"https://wikipedia.org/wiki/Fort_Niagara\" title=\"Fort Niagara\">Fort Niagara</a> from the French, who subsequently abandon <a href=\"https://wikipedia.org/wiki/Fort_Rouill%C3%A9\" title=\"Fort Rouillé\">Fort Rouillé</a>.", "links": [{"title": "French and Indian War", "link": "https://wikipedia.org/wiki/French_and_Indian_War"}, {"title": "Western New York", "link": "https://wikipedia.org/wiki/Western_New_York"}, {"title": "Fort Niagara", "link": "https://wikipedia.org/wiki/Fort_Niagara"}, {"title": "Fort Rouillé", "link": "https://wikipedia.org/wiki/Fort_Rouill%C3%A9"}]}, {"year": "1783", "text": "American Revolutionary War: The war's last action, the Siege of Cuddalore, is ended by a preliminary peace agreement.", "html": "1783 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The war's last action, the <a href=\"https://wikipedia.org/wiki/Siege_of_Cuddalore\" title=\"Siege of Cuddalore\">Siege of Cuddalore</a>, is ended by a preliminary peace agreement.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The war's last action, the <a href=\"https://wikipedia.org/wiki/Siege_of_Cuddalore\" title=\"Siege of Cuddalore\">Siege of Cuddalore</a>, is ended by a preliminary peace agreement.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Siege of Cuddalore", "link": "https://wikipedia.org/wiki/Siege_of_Cuddalore"}]}, {"year": "1788", "text": "<PERSON> completes his Symphony No. 40 in G minor (K550).", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ade<PERSON>_<PERSON>\" title=\"<PERSON> Amadeus Mozart\"><PERSON></a> completes his <a href=\"https://wikipedia.org/wiki/Symphony_No._40_(Mozart)\" title=\"Symphony No. 40 (<PERSON>)\">Symphony No. 40 in G minor</a> (K550).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Amade<PERSON> Mozart\"><PERSON></a> completes his <a href=\"https://wikipedia.org/wiki/Symphony_No._40_(<PERSON>)\" title=\"Symphony No. 40 (<PERSON>)\">Symphony No. 40 in G minor</a> (K550).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Symphony No. 40 (<PERSON>)", "link": "https://wikipedia.org/wiki/Symphony_No._40_(<PERSON>)"}]}, {"year": "1792", "text": "The Brunswick Manifesto is issued to the population of Paris promising vengeance if the French royal family is harmed.", "html": "1792 - The <a href=\"https://wikipedia.org/wiki/Brunswick_Manifesto\" title=\"Brunswick Manifesto\">Brunswick Manifesto</a> is issued to the population of Paris promising vengeance if the French royal family is harmed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Brunswick_Manifesto\" title=\"Brunswick Manifesto\">Brunswick Manifesto</a> is issued to the population of Paris promising vengeance if the French royal family is harmed.", "links": [{"title": "Brunswick Manifesto", "link": "https://wikipedia.org/wiki/Brunswick_Manifesto"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON> loses more than 300 men and his right arm during the failed conquest attempt of Tenerife (Spain).", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON><PERSON></a> loses more than 300 men and his right arm during the failed conquest attempt of <a href=\"https://wikipedia.org/wiki/Tenerife\" title=\"Tenerife\">Tenerife</a> (Spain).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON><PERSON></a> loses more than 300 men and his right arm during the failed conquest attempt of <a href=\"https://wikipedia.org/wiki/Tenerife\" title=\"Tenerife\">Tenerife</a> (Spain).", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rat<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Tenerife", "link": "https://wikipedia.org/wiki/Tenerife"}]}, {"year": "1799", "text": "<PERSON> defeats a numerically superior Ottoman army under <PERSON> at the Battle of Abukir.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats a numerically superior Ottoman army under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Egypt)\" title=\"<PERSON> (Egypt)\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Abukir_(1799)\" title=\"Battle of Abukir (1799)\">Battle of Abukir.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats a numerically superior Ottoman army under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Egypt)\" title=\"<PERSON> (Egypt)\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Abukir_(1799)\" title=\"Battle of Abukir (1799)\">Battle of Abukir.</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "<PERSON> (Egypt)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Egypt)"}, {"title": "Battle of Abukir (1799)", "link": "https://wikipedia.org/wiki/Battle_of_Abukir_(1799)"}]}, {"year": "1814", "text": "War of 1812: An American attack on Canada is repulsed.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: An <a href=\"https://wikipedia.org/wiki/Battle_of_Lundy%27s_Lane\" title=\"Battle of Lundy's Lane\">American attack on Canada</a> is repulsed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: An <a href=\"https://wikipedia.org/wiki/Battle_of_Lundy%27s_Lane\" title=\"Battle of Lundy's Lane\">American attack on Canada</a> is repulsed.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "Battle of Lundy's Lane", "link": "https://wikipedia.org/wiki/Battle_of_Lundy%27s_Lane"}]}, {"year": "1824", "text": "Costa Rica annexes Guanacaste from Nicaragua.", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Costa_Rica\" title=\"Costa Rica\">Costa Rica</a> annexes <a href=\"https://wikipedia.org/wiki/Guanacaste_Province\" title=\"Guanacaste Province\">Guanacaste</a> from <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Costa_Rica\" title=\"Costa Rica\">Costa Rica</a> annexes <a href=\"https://wikipedia.org/wiki/Guanacaste_Province\" title=\"Guanacaste Province\">Guanacaste</a> from <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "links": [{"title": "Costa Rica", "link": "https://wikipedia.org/wiki/Costa_Rica"}, {"title": "Guanacaste Province", "link": "https://wikipedia.org/wiki/Guanacaste_Province"}, {"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}]}, {"year": "1835", "text": "<PERSON> demonstrates a constant electric light at a public meeting in Dundee, Scotland.", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> demonstrates a constant <a href=\"https://wikipedia.org/wiki/Incandescent_light_bulb\" title=\"Incandescent light bulb\">electric light</a> at a public meeting in <a href=\"https://wikipedia.org/wiki/Dundee\" title=\"Dundee\">Dundee, Scotland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> demonstrates a constant <a href=\"https://wikipedia.org/wiki/Incandescent_light_bulb\" title=\"Incandescent light bulb\">electric light</a> at a public meeting in <a href=\"https://wikipedia.org/wiki/Dundee\" title=\"Dundee\">Dundee, Scotland</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Incandescent light bulb", "link": "https://wikipedia.org/wiki/Incandescent_light_bulb"}, {"title": "Dundee", "link": "https://wikipedia.org/wiki/Dundee"}]}, {"year": "1837", "text": "The first commercial use of an electrical telegraph is successfully demonstrated in London by <PERSON> and <PERSON>.", "html": "1837 - The first commercial use of an <a href=\"https://wikipedia.org/wiki/Electrical_telegraph\" title=\"Electrical telegraph\">electrical telegraph</a> is successfully demonstrated in London by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The first commercial use of an <a href=\"https://wikipedia.org/wiki/Electrical_telegraph\" title=\"Electrical telegraph\">electrical telegraph</a> is successfully demonstrated in London by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Charles_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Electrical telegraph", "link": "https://wikipedia.org/wiki/Electrical_telegraph"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_<PERSON>stone"}]}, {"year": "1853", "text": "<PERSON>,  the famous Californio bandit known as the \"Robin Hood of El Dorado\", is killed.", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the famous <a href=\"https://wikipedia.org/wiki/Californio\" class=\"mw-redirect\" title=\"Californio\">Californio</a> bandit known as the \"Robin Hood of El Dorado\", is killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the famous <a href=\"https://wikipedia.org/wiki/Californio\" class=\"mw-redirect\" title=\"Californio\">Californio</a> bandit known as the \"Robin Hood of El Dorado\", is killed.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Californio", "link": "https://wikipedia.org/wiki/Californio"}]}, {"year": "1861", "text": "American Civil War: The United States Congress passes the Crittenden-Johnson Resolution, stating that the war is being fought to preserve the Union and not to end slavery, in the wake of the defeat at the First Battle of Bull Run.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Crittenden%E2%80%93Johnson_Resolution\" title=\"Crittenden-Johnson Resolution\">Crittenden-Johnson Resolution</a>, stating that the war is being fought to preserve the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> and not to end <a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">slavery</a>, in the wake of the defeat at the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Bull_Run\" title=\"First Battle of Bull Run\">First Battle of Bull Run</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Crittenden%E2%80%93Johnson_Resolution\" title=\"Crittenden-Johnson Resolution\">Crittenden-Johnson Resolution</a>, stating that the war is being fought to preserve the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> and not to end <a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">slavery</a>, in the wake of the defeat at the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Bull_Run\" title=\"First Battle of Bull Run\">First Battle of Bull Run</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "<PERSON><PERSON><PERSON><PERSON>-Johnson Resolution", "link": "https://wikipedia.org/wiki/Crittenden%E2%80%93<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Slavery in the United States", "link": "https://wikipedia.org/wiki/Slavery_in_the_United_States"}, {"title": "First Battle of Bull Run", "link": "https://wikipedia.org/wiki/First_Battle_of_Bull_Run"}]}, {"year": "1866", "text": "The United States Congress passes legislation authorizing the rank of General of the Army. Lieutenant General <PERSON> becomes the first to be promoted to this rank.", "html": "1866 - The United States Congress passes legislation authorizing the <a href=\"https://wikipedia.org/wiki/Military_rank\" title=\"Military rank\">rank</a> of <a href=\"https://wikipedia.org/wiki/General_of_the_Army_(United_States)\" title=\"General of the Army (United States)\">General of the Army</a>. <a href=\"https://wikipedia.org/wiki/Lieutenant_general_(United_States)\" title=\"Lieutenant general (United States)\">Lieutenant General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first to be promoted to this rank.", "no_year_html": "The United States Congress passes legislation authorizing the <a href=\"https://wikipedia.org/wiki/Military_rank\" title=\"Military rank\">rank</a> of <a href=\"https://wikipedia.org/wiki/General_of_the_Army_(United_States)\" title=\"General of the Army (United States)\">General of the Army</a>. <a href=\"https://wikipedia.org/wiki/Lieutenant_general_(United_States)\" title=\"Lieutenant general (United States)\">Lieutenant General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first to be promoted to this rank.", "links": [{"title": "Military rank", "link": "https://wikipedia.org/wiki/Military_rank"}, {"title": "General of the Army (United States)", "link": "https://wikipedia.org/wiki/General_of_the_Army_(United_States)"}, {"title": "Lieutenant general (United States)", "link": "https://wikipedia.org/wiki/Lieutenant_general_(United_States)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1868", "text": "The Wyoming Territory is established.", "html": "1868 - The <a href=\"https://wikipedia.org/wiki/Wyoming_Territory\" title=\"Wyoming Territory\">Wyoming Territory</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wyoming_Territory\" title=\"Wyoming Territory\">Wyoming Territory</a> is established.", "links": [{"title": "Wyoming Territory", "link": "https://wikipedia.org/wiki/Wyoming_Territory"}]}, {"year": "1869", "text": "The Japanese daimyōs begin returning their land holdings to the emperor as part of the Meiji Restoration reforms. (Traditional Japanese Date: June 17, 1869).", "html": "1869 - The Japanese <i><a href=\"https://wikipedia.org/wiki/Daimy%C5%8D\" class=\"mw-redirect\" title=\"Daimyō\">daimyōs</a></i> begin returning their land holdings to the <a href=\"https://wikipedia.org/wiki/Emperor_of_Japan\" title=\"Emperor of Japan\">emperor</a> as part of the <a href=\"https://wikipedia.org/wiki/Meiji_Restoration\" title=\"Meiji Restoration\">Meiji Restoration</a> reforms. (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese Date</a>: June 17, 1869).", "no_year_html": "The Japanese <i><a href=\"https://wikipedia.org/wiki/Daimy%C5%8D\" class=\"mw-redirect\" title=\"Daimyō\">daimyōs</a></i> begin returning their land holdings to the <a href=\"https://wikipedia.org/wiki/Emperor_of_Japan\" title=\"Emperor of Japan\">emperor</a> as part of the <a href=\"https://wikipedia.org/wiki/Meiji_Restoration\" title=\"Meiji Restoration\">Meiji Restoration</a> reforms. (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese Date</a>: June 17, 1869).", "links": [{"title": "Daimy<PERSON>", "link": "https://wikipedia.org/wiki/Daimy%C5%8D"}, {"title": "Emperor of Japan", "link": "https://wikipedia.org/wiki/Emperor_of_Japan"}, {"title": "Meiji Restoration", "link": "https://wikipedia.org/wiki/Meiji_Restoration"}, {"title": "Japanese calendar", "link": "https://wikipedia.org/wiki/Japanese_calendar"}]}, {"year": "1894", "text": "The First Sino-Japanese War begins when the Japanese fire upon a Chinese warship.", "html": "1894 - The <a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a> begins when the Japanese fire upon a Chinese warship.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a> begins when the Japanese fire upon a Chinese warship.", "links": [{"title": "First Sino-Japanese War", "link": "https://wikipedia.org/wiki/First_Sino-Japanese_War"}]}, {"year": "1897", "text": "American author <PERSON> embarks on a sailing trip to take part in the Klondike's gold rush, from which he wrote his first successful stories.", "html": "1897 - American author <a href=\"https://wikipedia.org/wiki/Jack_London\" title=\"Jack London\"><PERSON></a> embarks on a sailing trip to take part in the <a href=\"https://wikipedia.org/wiki/Klondike,_Yukon\" title=\"Klondike, Yukon\">Klondike</a>'s <a href=\"https://wikipedia.org/wiki/Gold_rush\" title=\"Gold rush\">gold rush</a>, from which he wrote his first successful stories.", "no_year_html": "American author <a href=\"https://wikipedia.org/wiki/Jack_<PERSON>\" title=\"Jack London\"><PERSON></a> embarks on a sailing trip to take part in the <a href=\"https://wikipedia.org/wiki/Klondike,_Yukon\" title=\"Klondike, Yukon\">Klondike</a>'s <a href=\"https://wikipedia.org/wiki/Gold_rush\" title=\"Gold rush\">gold rush</a>, from which he wrote his first successful stories.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jack_<PERSON>"}, {"title": "Klondike, Yukon", "link": "https://wikipedia.org/wiki/Klondike,_Yukon"}, {"title": "Gold rush", "link": "https://wikipedia.org/wiki/Gold_rush"}]}, {"year": "1898", "text": "Spanish-American War: The American invasion of Spanish-held Puerto Rico begins, as United States Army troops under General <PERSON> land and secure the port at Guánica.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: The American invasion of Spanish-held <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a> begins, as <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> troops under General <a href=\"https://wikipedia.org/wiki/Nelson_<PERSON>._Miles\" title=\"Nelson A. Miles\"><PERSON></a> land and secure the port at <a href=\"https://wikipedia.org/wiki/Gu%C3%A1nica,_Puerto_Rico\" title=\"Guánica, Puerto Rico\">Guánica</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: The American invasion of Spanish-held <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a> begins, as <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> troops under General <a href=\"https://wikipedia.org/wiki/Nelson_A._Miles\" title=\"Nelson A. Miles\">Nelson <PERSON> Miles</a> land and secure the port at <a href=\"https://wikipedia.org/wiki/Gu%C3%A1nica,_Puerto_Rico\" title=\"Guánica, Puerto Rico\">Guánica</a>.", "links": [{"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}, {"title": "Puerto Rico", "link": "https://wikipedia.org/wiki/Puerto_Rico"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Guánica, Puerto Rico", "link": "https://wikipedia.org/wiki/Gu%C3%A1nica,_Puerto_Rico"}]}, {"year": "1908", "text": "Ajinomoto is founded. <PERSON><PERSON><PERSON> of the Tokyo Imperial University discovers that a key ingredient in kombu soup stock is monosodium glutamate (MSG), and patents a process for manufacturing it.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Ajinomoto\" title=\"Ajinomoto\">Ajinomoto</a> is founded. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/University_of_Tokyo\" title=\"University of Tokyo\">Tokyo Imperial University</a> discovers that a key ingredient in <a href=\"https://wikipedia.org/wiki/Kombu\" title=\"Kombu\">kombu</a> soup stock is <a href=\"https://wikipedia.org/wiki/Monosodium_glutamate\" title=\"Monosodium glutamate\">monosodium glutamate</a> (MSG), and patents a process for manufacturing it.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ajinomoto\" title=\"Ajinomoto\">Ajinomoto</a> is founded. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/University_of_Tokyo\" title=\"University of Tokyo\">Tokyo Imperial University</a> discovers that a key ingredient in <a href=\"https://wikipedia.org/wiki/Kombu\" title=\"Kombu\">kombu</a> soup stock is <a href=\"https://wikipedia.org/wiki/Monosodium_glutamate\" title=\"Monosodium glutamate\">monosodium glutamate</a> (MSG), and patents a process for manufacturing it.", "links": [{"title": "Ajinomoto", "link": "https://wikipedia.org/wiki/Ajinomoto"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}, {"title": "University of Tokyo", "link": "https://wikipedia.org/wiki/University_of_Tokyo"}, {"title": "Kombu", "link": "https://wikipedia.org/wiki/Kombu"}, {"title": "Monosodium glutamate", "link": "https://wikipedia.org/wiki/Monosodium_glutamate"}]}, {"year": "1909", "text": "<PERSON> makes the first flight across the English Channel in a heavier-than-air machine from Calais to Dover, England, United Kingdom in 37 minutes.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Louis_Bl%C3%A9riot\" title=\"Louis Blériot\"><PERSON></a> makes the first flight across the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> in a <a href=\"https://wikipedia.org/wiki/Bl%C3%A9riot_XI\" title=\"Blériot XI\">heavier-than-air machine</a> from <a href=\"https://wikipedia.org/wiki/Calais\" title=\"Calais\">Calais</a> to <a href=\"https://wikipedia.org/wiki/Dover\" title=\"Dover\">Dover, England, United Kingdom</a> in 37 minutes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_Bl%C3%A9riot\" title=\"Louis Blériot\"><PERSON></a> makes the first flight across the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> in a <a href=\"https://wikipedia.org/wiki/Bl%C3%A9riot_XI\" title=\"Blériot XI\">heavier-than-air machine</a> from <a href=\"https://wikipedia.org/wiki/Calais\" title=\"Calais\">Calais</a> to <a href=\"https://wikipedia.org/wiki/Dover\" title=\"Dover\">Dover, England, United Kingdom</a> in 37 minutes.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Bl%C3%A9riot"}, {"title": "English Channel", "link": "https://wikipedia.org/wiki/English_Channel"}, {"title": "Blériot XI", "link": "https://wikipedia.org/wiki/Bl%C3%A9riot_XI"}, {"title": "Calais", "link": "https://wikipedia.org/wiki/Calais"}, {"title": "Dover", "link": "https://wikipedia.org/wiki/Dover"}]}, {"year": "1915", "text": "RFC Captain <PERSON><PERSON><PERSON> becomes the first British pursuit aviator to earn the Victoria Cross.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Royal_Flying_Corps\" title=\"Royal Flying Corps\">RFC</a> Captain <a href=\"https://wikipedia.org/wiki/La<PERSON><PERSON>_Hawker\" title=\"<PERSON><PERSON><PERSON> Hawker\"><PERSON><PERSON><PERSON></a> becomes the first British <a href=\"https://wikipedia.org/wiki/Fighter_aircraft\" title=\"Fighter aircraft\">pursuit</a> aviator to earn the <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Royal_Flying_Corps\" title=\"Royal Flying Corps\">RFC</a> Captain <a href=\"https://wikipedia.org/wiki/La<PERSON><PERSON>_Hawker\" title=\"<PERSON><PERSON><PERSON> Hawker\"><PERSON><PERSON><PERSON></a> becomes the first British <a href=\"https://wikipedia.org/wiki/Fighter_aircraft\" title=\"Fighter aircraft\">pursuit</a> aviator to earn the <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a>.", "links": [{"title": "Royal Flying Corps", "link": "https://wikipedia.org/wiki/Royal_Flying_Corps"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>r"}, {"title": "Fighter aircraft", "link": "https://wikipedia.org/wiki/Fighter_aircraft"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1917", "text": "Sir <PERSON> introduces the first income tax in Canada as a \"temporary\" measure (lowest bracket is 4% and highest is 25%).", "html": "1917 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> introduces the first <a href=\"https://wikipedia.org/wiki/Income_taxes_in_Canada\" class=\"mw-redirect\" title=\"Income taxes in Canada\">income tax in Canada</a> as a \"temporary\" measure (lowest bracket is 4% and highest is 25%).", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> introduces the first <a href=\"https://wikipedia.org/wiki/Income_taxes_in_Canada\" class=\"mw-redirect\" title=\"Income taxes in Canada\">income tax in Canada</a> as a \"temporary\" measure (lowest bracket is 4% and highest is 25%).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Income taxes in Canada", "link": "https://wikipedia.org/wiki/Income_taxes_in_Canada"}]}, {"year": "1925", "text": "Telegraph Agency of the Soviet Union (TASS) is established.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Telegraph_Agency_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Telegraph Agency of the Soviet Union\">Telegraph Agency of the Soviet Union</a> (TASS) is established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Telegraph_Agency_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Telegraph Agency of the Soviet Union\">Telegraph Agency of the Soviet Union</a> (TASS) is established.", "links": [{"title": "Telegraph Agency of the Soviet Union", "link": "https://wikipedia.org/wiki/Telegraph_Agency_of_the_Soviet_Union"}]}, {"year": "1934", "text": "The Nazis assassinate Austrian Chancellor <PERSON><PERSON><PERSON> in a failed coup attempt.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazis</a> assassinate Austrian Chancellor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in a failed coup attempt.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazis</a> assassinate Austrian Chancellor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in a failed coup attempt.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "General <PERSON> orders the Swiss Army to resist German invasion and makes surrender illegal.", "html": "1940 - General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/Swiss_Army\" class=\"mw-redirect\" title=\"Swiss Army\">Swiss Army</a> to resist German invasion and makes surrender illegal.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/Swiss_Army\" class=\"mw-redirect\" title=\"Swiss Army\">Swiss Army</a> to resist German invasion and makes surrender illegal.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Swiss Army", "link": "https://wikipedia.org/wiki/Swiss_Army"}]}, {"year": "1942", "text": "The Norwegian Manifesto calls for nonviolent resistance to the German occupation.", "html": "1942 - The <a href=\"https://wikipedia.org/wiki/Norwegian_resistance_movement\" title=\"Norwegian resistance movement\">Norwegian Manifesto</a> calls for nonviolent resistance to the German occupation.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Norwegian_resistance_movement\" title=\"Norwegian resistance movement\">Norwegian Manifesto</a> calls for nonviolent resistance to the German occupation.", "links": [{"title": "Norwegian resistance movement", "link": "https://wikipedia.org/wiki/Norwegian_resistance_movement"}]}, {"year": "1943", "text": "World War II: <PERSON> is forced out of office by the King (encouraged by the Grand Council of Fascism) and is replaced by <PERSON>.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/25_Luglio\" class=\"mw-redirect\" title=\"25 Luglio\">is forced out of office</a> by the King (encouraged by the <a href=\"https://wikipedia.org/wiki/Grand_Council_of_Fascism\" title=\"Grand Council of Fascism\">Grand Council of Fascism</a>) and is replaced by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/25_Luglio\" class=\"mw-redirect\" title=\"25 Luglio\">is forced out of office</a> by the King (encouraged by the <a href=\"https://wikipedia.org/wiki/Grand_Council_of_Fascism\" title=\"Grand Council of Fascism\">Grand Council of Fascism</a>) and is replaced by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "25 Luglio", "link": "https://wikipedia.org/wiki/25_Lu<PERSON>o"}, {"title": "Grand Council of Fascism", "link": "https://wikipedia.org/wiki/Grand_Council_of_Fascism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "World War II: Operation Spring is one of the bloodiest days for the First Canadian Army during the war.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Operation_Spring\" title=\"Operation Spring\">Operation Spring</a> is one of the bloodiest days for the <a href=\"https://wikipedia.org/wiki/First_Canadian_Army\" title=\"First Canadian Army\">First Canadian Army</a> during the war.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Operation_Spring\" title=\"Operation Spring\">Operation Spring</a> is one of the bloodiest days for the <a href=\"https://wikipedia.org/wiki/First_Canadian_Army\" title=\"First Canadian Army\">First Canadian Army</a> during the war.", "links": [{"title": "Operation Spring", "link": "https://wikipedia.org/wiki/Operation_Spring"}, {"title": "First Canadian Army", "link": "https://wikipedia.org/wiki/First_Canadian_Army"}]}, {"year": "1946", "text": "The Crossroads Baker device is the first underwater nuclear weapon test.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Operation_Crossroads#Test_Baker\" title=\"Operation Crossroads\">Crossroads Baker</a> device is the first underwater nuclear weapon test.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Operation_Crossroads#Test_Baker\" title=\"Operation Crossroads\">Crossroads Baker</a> device is the first underwater nuclear weapon test.", "links": [{"title": "Operation Crossroads", "link": "https://wikipedia.org/wiki/Operation_Crossroads#Test_Baker"}]}, {"year": "1956", "text": "Forty-five miles south of Nantucket Island, the Italian ocean liner SS <PERSON> collides with the MS Stockholm in heavy fog and sinks the next day, killing 51.", "html": "1956 - Forty-five miles south of <a href=\"https://wikipedia.org/wiki/Nantucket\" title=\"Nantucket\">Nantucket Island</a>, the Italian <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">ocean liner</a> <a href=\"https://wikipedia.org/wiki/SS_Andrea_<PERSON>\" title=\"SS <PERSON>\">SS <i><PERSON></i></a> collides with the <a href=\"https://wikipedia.org/wiki/MV_Astoria\" title=\"MV Astoria\">MS <i>Stockholm</i></a> in heavy fog and sinks the next day, killing 51.", "no_year_html": "Forty-five miles south of <a href=\"https://wikipedia.org/wiki/Nantucket\" title=\"Nantucket\">Nantucket Island</a>, the Italian <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">ocean liner</a> <a href=\"https://wikipedia.org/wiki/SS_Andrea_<PERSON>\" title=\"SS <PERSON>\">SS <i><PERSON></i></a> collides with the <a href=\"https://wikipedia.org/wiki/MV_Astoria\" title=\"MV Astoria\">MS <i>Stockholm</i></a> in heavy fog and sinks the next day, killing 51.", "links": [{"title": "Nantucket", "link": "https://wikipedia.org/wiki/Nantucket"}, {"title": "Ocean liner", "link": "https://wikipedia.org/wiki/Ocean_liner"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "MV Astoria", "link": "https://wikipedia.org/wiki/MV_Astoria"}]}, {"year": "1957", "text": "The Tunisian King <PERSON> is replaced by President <PERSON><PERSON><PERSON>.", "html": "1957 - The Tunisian King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_<PERSON>-<PERSON>\" title=\"<PERSON> VIII al-Amin\"><PERSON></a> is replaced by President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>b_Bourguiba\" title=\"Habi<PERSON> Bourguiba\"><PERSON><PERSON><PERSON> Bourg<PERSON></a>.", "no_year_html": "The Tunisian King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_<PERSON>-<PERSON>\" title=\"<PERSON> VIII al-Am<PERSON>\"><PERSON></a> is replaced by President <a href=\"https://wikipedia.org/wiki/Habib_Bourguiba\" title=\"Habi<PERSON> Bourguiba\"><PERSON><PERSON><PERSON> Bourguiba</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ha<PERSON>b_Bourguiba"}]}, {"year": "1958", "text": "The African Regroupment Party holds its first congress in Cotonou.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/African_Regroupment_Party\" title=\"African Regroupment Party\">African Regroupment Party</a> holds its first congress in <a href=\"https://wikipedia.org/wiki/Cotonou\" title=\"Cotonou\">Cotonou</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/African_Regroupment_Party\" title=\"African Regroupment Party\">African Regroupment Party</a> holds its first congress in <a href=\"https://wikipedia.org/wiki/Cotonou\" title=\"Cotonou\">Cotonou</a>.", "links": [{"title": "African Regroupment Party", "link": "https://wikipedia.org/wiki/African_Regroupment_Party"}, {"title": "Cotonou", "link": "https://wikipedia.org/wiki/Cotonou"}]}, {"year": "1961", "text": "Cold War: In a speech <PERSON> emphasizes that any attack on Berlin is an attack on NATO.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: In a speech <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> emphasizes that any attack on Berlin is an attack on <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: In a speech <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> emphasizes that any attack on Berlin is an attack on <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}]}, {"year": "1965", "text": "<PERSON> goes electric at the Newport Folk Festival, signaling a major change in folk and rock music.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Dylan\" title=\"Bob Dylan\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Electric_Dylan_controversy\" title=\"Electric Dylan controversy\">goes electric</a> at the <a href=\"https://wikipedia.org/wiki/Newport_Folk_Festival\" title=\"Newport Folk Festival\">Newport Folk Festival</a>, signaling a major change in folk and rock music.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Dylan\" title=\"Bob Dylan\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Electric_Dylan_controversy\" title=\"Electric Dylan controversy\">goes electric</a> at the <a href=\"https://wikipedia.org/wiki/Newport_Folk_Festival\" title=\"Newport Folk Festival\">Newport Folk Festival</a>, signaling a major change in folk and rock music.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Electric Dylan controversy", "link": "https://wikipedia.org/wiki/Electric_Dylan_controversy"}, {"title": "Newport Folk Festival", "link": "https://wikipedia.org/wiki/Newport_Folk_Festival"}]}, {"year": "1969", "text": "Vietnam War: U.S. President <PERSON> declares the Nixon Doctrine, stating that the United States now expects its Asian allies to take care of their own military defense. This is the start of the \"Vietnamization\" of the war.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the <a href=\"https://wikipedia.org/wiki/Nixon_Doctrine\" title=\"Nixon Doctrine\">Nixon Doctrine</a>, stating that the United States now expects its Asian allies to take care of their own military defense. This is the start of the \"<a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a>\" of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the <a href=\"https://wikipedia.org/wiki/<PERSON>_Doctrine\" title=\"Nixon Doctrine\">Nixon Doctrine</a>, stating that the United States now expects its Asian allies to take care of their own military defense. This is the start of the \"<a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a>\" of the war.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Doc<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Doctrine"}, {"title": "Vietnamization", "link": "https://wikipedia.org/wiki/Vietnamization"}]}, {"year": "1971", "text": "The Sohagpur massacre is perpetrated by the Pakistan Army.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Sohagpur_massacre\" title=\"Sohagpur massacre\">Sohagpur massacre</a> is perpetrated by the Pakistan Army.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sohagpur_massacre\" title=\"Sohagpur massacre\">Sohagpur massacre</a> is perpetrated by the Pakistan Army.", "links": [{"title": "Sohagpur massacre", "link": "https://wikipedia.org/wiki/Sohagpur_massacre"}]}, {"year": "1973", "text": "Soviet Mars 5 space probe is launched.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <i><a href=\"https://wikipedia.org/wiki/Mars_program\" title=\"Mars program\">Mars 5</a></i> space probe is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <i><a href=\"https://wikipedia.org/wiki/Mars_program\" title=\"Mars program\">Mars 5</a></i> space probe is launched.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Mars program", "link": "https://wikipedia.org/wiki/Mars_program"}]}, {"year": "1976", "text": "Viking program: Viking 1 takes the famous Face on Mars photo.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Viking_program\" title=\"Viking program\">Viking program</a>: <i><a href=\"https://wikipedia.org/wiki/Viking_1\" title=\"Viking 1\">Viking 1</a></i> takes the famous <a href=\"https://wikipedia.org/wiki/Cydonia_(region_of_Mars)\" class=\"mw-redirect\" title=\"Cydonia (region of Mars)\">Face on Mars</a> photo.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viking_program\" title=\"Viking program\">Viking program</a>: <i><a href=\"https://wikipedia.org/wiki/Viking_1\" title=\"Viking 1\">Viking 1</a></i> takes the famous <a href=\"https://wikipedia.org/wiki/Cydonia_(region_of_Mars)\" class=\"mw-redirect\" title=\"Cydonia (region of Mars)\">Face on Mars</a> photo.", "links": [{"title": "Viking program", "link": "https://wikipedia.org/wiki/Viking_program"}, {"title": "Viking 1", "link": "https://wikipedia.org/wiki/Viking_1"}, {"title": "Cydonia (region of Mars)", "link": "https://wikipedia.org/wiki/Cydonia_(region_of_Mars)"}]}, {"year": "1978", "text": "Puerto Rican police shoot two nationalists in the Cerro Maravilla murders.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rican</a> police shoot two nationalists in the <a href=\"https://wikipedia.org/wiki/Ce<PERSON>_Maravilla_murders\" title=\"Cerro <PERSON> murders\"><PERSON><PERSON> murders</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rican</a> police shoot two nationalists in the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Maravilla_murders\" title=\"Cerro <PERSON> murders\"><PERSON><PERSON> murders</a>.", "links": [{"title": "Puerto Rico", "link": "https://wikipedia.org/wiki/Puerto_Rico"}, {"title": "<PERSON><PERSON> murders", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Maravilla_murders"}]}, {"year": "1978", "text": "Birth of <PERSON>, the first human to have been born after conception by in vitro fertilisation, or IVF.", "html": "1978 - Birth of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first human to have been born after conception by <a href=\"https://wikipedia.org/wiki/In_vitro_fertilisation\" title=\"In vitro fertilisation\">in vitro fertilisation</a>, or IVF.", "no_year_html": "Birth of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first human to have been born after conception by <a href=\"https://wikipedia.org/wiki/In_vitro_fertilisation\" title=\"In vitro fertilisation\">in vitro fertilisation</a>, or IVF.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "In vitro fertilisation", "link": "https://wikipedia.org/wiki/In_vitro_fertilisation"}]}, {"year": "1979", "text": "In accord with the Egypt-Israel peace treaty, Israel begins its withdrawal from the Sinai Peninsula.", "html": "1979 - In accord with the <a href=\"https://wikipedia.org/wiki/Egypt%E2%80%93Israel_peace_treaty\" title=\"Egypt-Israel peace treaty\">Egypt-Israel peace treaty</a>, Israel begins its withdrawal from the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a>.", "no_year_html": "In accord with the <a href=\"https://wikipedia.org/wiki/Egypt%E2%80%93Israel_peace_treaty\" title=\"Egypt-Israel peace treaty\">Egypt-Israel peace treaty</a>, Israel begins its withdrawal from the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a>.", "links": [{"title": "Egypt-Israel peace treaty", "link": "https://wikipedia.org/wiki/Egypt%E2%80%93Israel_peace_treaty"}, {"title": "Sinai Peninsula", "link": "https://wikipedia.org/wiki/Sinai_Peninsula"}]}, {"year": "1983", "text": "Black July: Thirty-seven Tamil political prisoners at the Welikada high security prison in Colombo are massacred by the fellow Sinhalese prisoners.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Black_July\" title=\"Black July\">Black July</a>: Thirty-seven <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Tamils\" title=\"Sri Lankan Tamils\">Tamil</a> political prisoners at the Welikada high security prison in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a> are <a href=\"https://wikipedia.org/wiki/Welikada_prison_massacre\" title=\"Welikada prison massacre\">massacred</a> by the fellow <a href=\"https://wikipedia.org/wiki/Sinhalese_people\" title=\"Sinhalese people\">Sinhalese</a> prisoners.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_July\" title=\"Black July\">Black July</a>: Thirty-seven <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Tamils\" title=\"Sri Lankan Tamils\">Tamil</a> political prisoners at the Welikada high security prison in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a> are <a href=\"https://wikipedia.org/wiki/Welikada_prison_massacre\" title=\"Welikada prison massacre\">massacred</a> by the fellow <a href=\"https://wikipedia.org/wiki/Sinhalese_people\" title=\"Sinhalese people\">Sinhalese</a> prisoners.", "links": [{"title": "Black July", "link": "https://wikipedia.org/wiki/Black_July"}, {"title": "Sri Lankan Tamils", "link": "https://wikipedia.org/wiki/Sri_Lankan_Tamils"}, {"title": "Colombo", "link": "https://wikipedia.org/wiki/Colombo"}, {"title": "Welikada prison massacre", "link": "https://wikipedia.org/wiki/Welikada_prison_massacre"}, {"title": "Sinhalese people", "link": "https://wikipedia.org/wiki/Sinhalese_people"}]}, {"year": "1984", "text": "Salyut 7 cosmonaut <PERSON><PERSON><PERSON> becomes the first woman to perform a space walk.", "html": "1984 - <i><a href=\"https://wikipedia.org/wiki/Salyut_7\" title=\"Salyut 7\">Salyut 7</a></i> <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>vet<PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first woman to perform a <a href=\"https://wikipedia.org/wiki/Extravehicular_activity\" title=\"Extravehicular activity\">space walk</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Salyut_7\" title=\"Salyut 7\">Salyut 7</a></i> <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first woman to perform a <a href=\"https://wikipedia.org/wiki/Extravehicular_activity\" title=\"Extravehicular activity\">space walk</a>.", "links": [{"title": "Salyut 7", "link": "https://wikipedia.org/wiki/Salyut_7"}, {"title": "Astronaut", "link": "https://wikipedia.org/wiki/Astronaut"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Extravehicular activity", "link": "https://wikipedia.org/wiki/Extravehicular_activity"}]}, {"year": "1993", "text": "Israel launches a massive attack against Lebanon in what the Israelis call Operation Accountability, and the Lebanese call the Seven-Day War.", "html": "1993 - Israel launches a massive attack against <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a> in what the Israelis call <a href=\"https://wikipedia.org/wiki/Operation_Accountability\" title=\"Operation Accountability\">Operation Accountability</a>, and the Lebanese call the Seven-Day War.", "no_year_html": "Israel launches a massive attack against <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a> in what the Israelis call <a href=\"https://wikipedia.org/wiki/Operation_Accountability\" title=\"Operation Accountability\">Operation Accountability</a>, and the Lebanese call the Seven-Day War.", "links": [{"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}, {"title": "Operation Accountability", "link": "https://wikipedia.org/wiki/Operation_Accountability"}]}, {"year": "1993", "text": "The Saint James Church massacre occurs in Kenilworth, Cape Town, South Africa.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Saint_James_Church_massacre\" title=\"Saint James Church massacre\">Saint James Church massacre</a> occurs in Kenilworth, Cape Town, South Africa.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Saint_James_Church_massacre\" title=\"Saint James Church massacre\">Saint James Church massacre</a> occurs in Kenilworth, Cape Town, South Africa.", "links": [{"title": "Saint James Church massacre", "link": "https://wikipedia.org/wiki/Saint_James_Church_massacre"}]}, {"year": "1994", "text": "Israel and Jordan sign the Washington Declaration, that formally ends the state of war that had existed between the nations since 1948.", "html": "1994 - Israel and <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a> sign the <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Jordan_peace_treaty\" title=\"Israel-Jordan peace treaty\">Washington Declaration</a>, that formally ends the state of war that had existed between the nations since 1948.", "no_year_html": "Israel and <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a> sign the <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Jordan_peace_treaty\" title=\"Israel-Jordan peace treaty\">Washington Declaration</a>, that formally ends the state of war that had existed between the nations since 1948.", "links": [{"title": "Jordan", "link": "https://wikipedia.org/wiki/Jordan"}, {"title": "Israel-Jordan peace treaty", "link": "https://wikipedia.org/wiki/Israel%E2%80%93Jordan_peace_treaty"}]}, {"year": "1995", "text": "A gas bottle explodes in Saint Michel station of line B of the RER (Paris regional train network). Eight are killed and 80 wounded.", "html": "1995 - A gas bottle <a href=\"https://wikipedia.org/wiki/1995_Paris_M%C3%A9tro_and_RER_bombings\" class=\"mw-redirect\" title=\"1995 Paris Métro and RER bombings\">explodes</a> in <i>Saint Michel</i> station of line B of the <a href=\"https://wikipedia.org/wiki/R%C3%A9seau_Express_R%C3%A9gional\" title=\"Réseau Express Régional\">RER</a> (Paris regional train network). Eight are killed and 80 wounded.", "no_year_html": "A gas bottle <a href=\"https://wikipedia.org/wiki/1995_Paris_M%C3%A9tro_and_RER_bombings\" class=\"mw-redirect\" title=\"1995 Paris Métro and RER bombings\">explodes</a> in <i>Saint Michel</i> station of line B of the <a href=\"https://wikipedia.org/wiki/R%C3%A9seau_Express_R%C3%A9gional\" title=\"Réseau Express Régional\">RER</a> (Paris regional train network). Eight are killed and 80 wounded.", "links": [{"title": "1995 Paris Métro and RER bombings", "link": "https://wikipedia.org/wiki/1995_Paris_M%C3%A9tro_and_RER_bombings"}, {"title": "Réseau Express Régional", "link": "https://wikipedia.org/wiki/R%C3%A9seau_Express_R%C3%A9gional"}]}, {"year": "1996", "text": "In a military coup in Burundi, <PERSON> deposes <PERSON><PERSON><PERSON><PERSON>.", "html": "1996 - In a <a href=\"https://wikipedia.org/wiki/1996_Burundian_coup_d%27%C3%A9tat\" title=\"1996 Burundian coup d'état\">military coup in Burundi</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> deposes <a href=\"https://wikipedia.org/wiki/Sylvestre_Ntibantunganya\" title=\"<PERSON>ylves<PERSON> Ntiban<PERSON>ganya\"><PERSON>ylvestre Ntibantunganya</a>.", "no_year_html": "In a <a href=\"https://wikipedia.org/wiki/1996_Burundian_coup_d%27%C3%A9tat\" title=\"1996 Burundian coup d'état\">military coup in Burundi</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> deposes <a href=\"https://wikipedia.org/wiki/Sylvestre_Ntibantunganya\" title=\"<PERSON>ylves<PERSON> Nti<PERSON>ganya\"><PERSON>yl<PERSON><PERSON> Ntibantunganya</a>.", "links": [{"title": "1996 Burundian coup d'état", "link": "https://wikipedia.org/wiki/1996_Burundian_coup_d%27%C3%A9tat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sylvestre_Ntibantunganya"}]}, {"year": "2000", "text": "Concorde Air France Flight 4590 crashes outside of Paris shortly after taking off at Charles de Gaulle Airport, killing 113 people.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> <a href=\"https://wikipedia.org/wiki/Air_France_Flight_4590\" title=\"Air France Flight 4590\">Air France Flight 4590</a> crashes outside of Paris shortly after taking off at <a href=\"https://wikipedia.org/wiki/Charles_de_Gaulle_Airport\" title=\"Charles de Gaulle Airport\">Charles <PERSON>le Airport</a>, killing 113 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> <a href=\"https://wikipedia.org/wiki/Air_France_Flight_4590\" title=\"Air France Flight 4590\">Air France Flight 4590</a> crashes outside of Paris shortly after taking off at <a href=\"https://wikipedia.org/wiki/Charles_de_Gaulle_Airport\" title=\"Charles de Gaulle Airport\">Charles de <PERSON>aulle Airport</a>, killing 113 people.", "links": [{"title": "Concorde", "link": "https://wikipedia.org/wiki/Concorde"}, {"title": "Air France Flight 4590", "link": "https://wikipedia.org/wiki/Air_France_Flight_4590"}, {"title": "Charles <PERSON> Airport", "link": "https://wikipedia.org/wiki/Charles_de_<PERSON>_Airport"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON> is sworn in as India's first female president.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is sworn in as India's first female president.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is sworn in as India's first female president.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>il"}]}, {"year": "2010", "text": "WikiLeaks publishes classified documents about the War in Afghanistan, one of the largest leaks in U.S. military history.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a> publishes <a href=\"https://wikipedia.org/wiki/Afghan_War_documents_leak\" class=\"mw-redirect\" title=\"Afghan War documents leak\">classified documents about the War in Afghanistan</a>, one of the largest leaks in U.S. military history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a> publishes <a href=\"https://wikipedia.org/wiki/Afghan_War_documents_leak\" class=\"mw-redirect\" title=\"Afghan War documents leak\">classified documents about the War in Afghanistan</a>, one of the largest leaks in U.S. military history.", "links": [{"title": "WikiLeaks", "link": "https://wikipedia.org/wiki/WikiLeaks"}, {"title": "Afghan War documents leak", "link": "https://wikipedia.org/wiki/Afghan_War_documents_leak"}]}, {"year": "2018", "text": "As-Suwayda attacks: Coordinated attacks occur in Syria.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/2018_As-Suwayda_attacks\" title=\"2018 As-Suwayda attacks\">As-Suwayda attacks</a>: Coordinated attacks occur in Syria.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2018_As-Suwayda_attacks\" title=\"2018 As-Suwayda attacks\">As-Suwayda attacks</a>: Coordinated attacks occur in Syria.", "links": [{"title": "2018 As-Suwayda attacks", "link": "https://wikipedia.org/wiki/2018_As-Suwayda_attacks"}]}, {"year": "2019", "text": "National extreme heat records set this day in the UK, Belgium, The Netherlands and Germany during the July 2019 European heat wave.", "html": "2019 - National extreme heat records set this day in the UK, Belgium, The Netherlands and Germany during the <a href=\"https://wikipedia.org/wiki/July_2019_European_heat_wave\" class=\"mw-redirect\" title=\"July 2019 European heat wave\">July 2019 European heat wave</a>.", "no_year_html": "National extreme heat records set this day in the UK, Belgium, The Netherlands and Germany during the <a href=\"https://wikipedia.org/wiki/July_2019_European_heat_wave\" class=\"mw-redirect\" title=\"July 2019 European heat wave\">July 2019 European heat wave</a>.", "links": [{"title": "July 2019 European heat wave", "link": "https://wikipedia.org/wiki/July_2019_European_heat_wave"}]}], "Births": [{"year": "975", "text": "<PERSON><PERSON><PERSON><PERSON>, bishop of Merseburg (d. 1018)", "html": "975 - <a href=\"https://wikipedia.org/wiki/Thiet<PERSON>_of_Merseburg\" title=\"Thiet<PERSON> of Merseburg\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of Merseburg (d. 1018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thiet<PERSON>_of_Merseburg\" title=\"Thiet<PERSON> of Merseburg\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of Merseburg (d. 1018)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Merseburg", "link": "https://wikipedia.org/wiki/Thietmar_of_Merseburg"}]}, {"year": "1016", "text": "<PERSON>, duke of Poland (d. 1058)", "html": "1016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Restorer\" title=\"<PERSON> the Restorer\"><PERSON> the Restorer</a>, duke of Poland (d. 1058)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Restorer\" title=\"<PERSON> the Restorer\"><PERSON> the Restorer</a>, duke of Poland (d. 1058)", "links": [{"title": "<PERSON> the Restorer", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Restorer"}]}, {"year": "1109", "text": "<PERSON><PERSON><PERSON>, king of Portugal (d. 1185)", "html": "1109 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON><PERSON><PERSON> <PERSON> of Portugal\"><PERSON><PERSON><PERSON> I</a>, king of Portugal (d. 1185)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON><PERSON><PERSON> of Portugal\"><PERSON><PERSON><PERSON> I</a>, king of Portugal (d. 1185)", "links": [{"title": "<PERSON><PERSON><PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1165", "text": "<PERSON>, Andalusian Sufi mystic, poet, and philosopher (d. 1240)", "html": "1165 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Andalusian Sufi mystic, poet, and philosopher (d. 1240)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Andalusian Sufi mystic, poet, and philosopher (d. 1240)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1261", "text": "<PERSON>, Duke of Brittany (d. 1312)", "html": "1261 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (d. 1312)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (d. 1312)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1291", "text": "<PERSON><PERSON><PERSON>, Welsh noblewoman (d. 1353)", "html": "1291 - <a href=\"https://wikipedia.org/wiki/Hawys_Gadarn\" title=\"Hawys Gadarn\"><PERSON><PERSON><PERSON> Gada<PERSON></a>, Welsh noblewoman (d. 1353)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hawys_Gadarn\" title=\"Hawys Gadarn\"><PERSON><PERSON><PERSON> Gada<PERSON></a>, Welsh noblewoman (d. 1353)", "links": [{"title": "Hawys Gadarn", "link": "https://wikipedia.org/wiki/Hawys_Gadarn"}]}, {"year": "1336", "text": "<PERSON>, Duke of Bavaria (d. 1404)", "html": "1336 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1404)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1404)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1394", "text": "<PERSON>, king of Scotland (d. 1437)", "html": "1394 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> I of Scotland\"><PERSON></a>, king of Scotland (d. 1437)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> I of Scotland\"><PERSON></a>, king of Scotland (d. 1437)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1404", "text": "<PERSON>, Duke of Brabant (d. 1430)", "html": "1404 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (d. 1430)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (d. 1430)", "links": [{"title": "<PERSON>, Duke of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1421", "text": "<PERSON>, 3rd Earl of Northumberland, English politician (d. 1461)", "html": "1421 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Northumberland\" title=\"<PERSON>, 3rd Earl of Northumberland\"><PERSON>, 3rd Earl of Northumberland</a>, English politician (d. 1461)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Northumberland\" title=\"<PERSON>, 3rd Earl of Northumberland\"><PERSON>, 3rd Earl of Northumberland</a>, English politician (d. 1461)", "links": [{"title": "<PERSON>, 3rd Earl of Northumberland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Northumberland"}]}, {"year": "1450", "text": "<PERSON>, Renaissance humanist (d. 1528)", "html": "1450 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>elin<PERSON>\" title=\"<PERSON>elin<PERSON>\"><PERSON></a>, Renaissance humanist (d. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>elin<PERSON>\" title=\"<PERSON>elin<PERSON>\"><PERSON></a>, Renaissance humanist (d. 1528)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>eling"}]}, {"year": "1486", "text": "<PERSON><PERSON>, Duke of Mecklenburg (d. 1547)", "html": "1486 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON><PERSON>, Duke of Mecklenburg\"><PERSON><PERSON>, Duke of Mecklenburg</a> (d. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON><PERSON>, Duke of Mecklenburg\"><PERSON><PERSON>, Duke of Mecklenburg</a> (d. 1547)", "links": [{"title": "<PERSON><PERSON>, Duke of Mecklenburg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Mecklenburg"}]}, {"year": "1498", "text": "<PERSON><PERSON><PERSON>, Archbishop of Zaragoza (d. 1575)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/Hernan<PERSON>_<PERSON>_<PERSON>g%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Zaragoza\" class=\"mw-redirect\" title=\"Archbishop of Zaragoza\">Archbishop of Zaragoza</a> (d. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>g%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Zaragoza\" class=\"mw-redirect\" title=\"Archbishop of Zaragoza\">Archbishop of Zaragoza</a> (d. 1575)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hernan<PERSON>_de_Arag%C3%B3n"}, {"title": "Archbishop of Zaragoza", "link": "https://wikipedia.org/wiki/Archbishop_of_Zaragoza"}]}, {"year": "1532", "text": "<PERSON><PERSON><PERSON>, Jesuit lay brother and saint (d. 1617)", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jesuit lay brother and saint (d. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jesuit lay brother and saint (d. 1617)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1556", "text": "<PERSON>, English translator, poet, and dramatist (d. 1596)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English translator, poet, and dramatist (d. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English translator, poet, and dramatist (d. 1596)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1562", "text": "<PERSON><PERSON>, Japanese warlord (d. 1611)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/Kat%C5%8D_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese warlord (d. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kat%C5%8D_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese warlord (d. 1611)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kat%C5%8D_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1573", "text": "<PERSON>, German astronomer and Jesuit (d. 1650)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and Jesuit (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and Jesuit (d. 1650)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1581", "text": "<PERSON>, English archivist (d. 1644)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archivist (d. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archivist (d. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON>, German scholar (d. 1690)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar (d. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar (d. 1690)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1633", "text": "<PERSON>, English politician (d. 1701)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_politician)\" title=\"<PERSON> (English politician)\"><PERSON></a>, English politician (d. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(English_politician)\" title=\"<PERSON> (English politician)\"><PERSON></a>, English politician (d. 1701)", "links": [{"title": "<PERSON> (English politician)", "link": "https://wikipedia.org/wiki/<PERSON>(English_politician)"}]}, {"year": "1654", "text": "<PERSON><PERSON><PERSON>, Italian composer and diplomat (d. 1728)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>effani\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and diplomat (d. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and diplomat (d. 1728)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agos<PERSON>_Steffani"}]}, {"year": "1657", "text": "<PERSON>, German composer (d. 1714)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1658", "text": "<PERSON>, 1st Duke of Argyll, Scottish general (d. 1703)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Argyll\" title=\"<PERSON>, 1st Duke of Argyll\"><PERSON>, 1st Duke of Argyll</a>, Scottish general (d. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Argyll\" title=\"<PERSON>, 1st Duke of Argyll\"><PERSON>, 1st Duke of Argyll</a>, Scottish general (d. 1703)", "links": [{"title": "<PERSON>, 1st Duke of Argyll", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Argyll"}]}, {"year": "1683", "text": "<PERSON>, Dutch playwright and poet (d. 1756)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch playwright and poet (d. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch playwright and poet (d. 1756)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1750", "text": "<PERSON>, American general and politician, 1st United States Secretary of War (d. 1806)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of War", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_War"}]}, {"year": "1753", "text": "<PERSON>, 1st Count of Buenos Aires, French-Spanish captain and politician, 10th Viceroy of the Viceroyalty of the Río de la Plata (d. 1810)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/Santiago_de_Liniers,_1st_Count_of_Buenos_Aires\" title=\"Santiago de Lin<PERSON>, 1st Count of Buenos Aires\"><PERSON>, 1st Count of Buenos Aires</a>, French-Spanish captain and politician, 10th <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata\" title=\"Viceroyalty of the Río de la Plata\">Viceroy of the Viceroyalty of the Río de la Plata</a> (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_de_Liniers,_1st_Count_of_Buenos_Aires\" title=\"Santiago de Liniers, 1st Count of Buenos Aires\"><PERSON>, 1st Count of Buenos Aires</a>, French-Spanish captain and politician, 10th <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata\" title=\"Viceroyalty of the Río de la Plata\">Viceroy of the Viceroyalty of the Río de la Plata</a> (d. 1810)", "links": [{"title": "<PERSON>, 1st Count of Buenos Aires", "link": "https://wikipedia.org/wiki/Santiago_de_Lin<PERSON>,_1st_Count_of_Buenos_Aires"}, {"title": "Viceroyalty of the Río de la Plata", "link": "https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata"}]}, {"year": "1797", "text": "Princess <PERSON> of Hesse-Kassel (d. 1889)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse-Kassel\" title=\"Princess <PERSON> of Hesse-Kassel\">Princess <PERSON> of Hesse-Kassel</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Augusta_of_Hesse-Kassel\" title=\"Princess <PERSON> of Hesse-Kassel\">Princess <PERSON> of Hesse-Kassel</a> (d. 1889)", "links": [{"title": "Princess <PERSON> of Hesse-Kassel", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse-Kassel"}]}, {"year": "1806", "text": "<PERSON>, American abolitionist (d. 1885)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American abolitionist (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American abolitionist (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, French captain and explorer (d. 1873)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French captain and explorer (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French captain and explorer (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, American painter, sculptor, and photographer (d. 1916)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, sculptor, and photographer (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, sculptor, and photographer (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>,  German pathologist, physiologist and biologist (d. 1888)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pathologist, physiologist and biologist (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pathologist, physiologist and biologist (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Scottish-English lieutenant and politician, 33rd Prime Minister of the United Kingdom (d. 1930)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English lieutenant and politician, 33rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English lieutenant and politician, 33rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1857", "text": "<PERSON>, American naval officer and inventor (d. 1934)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American naval officer and inventor (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American naval officer and inventor (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, Dutch botanist and conservationist (d. 1945)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Jac._P._Thijsse\" class=\"mw-redirect\" title=\"Jac. P. Thijsse\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Dutch botanist and conservationist (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON>._<PERSON>._Thijsse\" class=\"mw-redirect\" title=\"Jac. P. Thijsse\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Dutch botanist and conservationist (d. 1945)", "links": [{"title": "Jac. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON>._<PERSON><PERSON>_Thijsse"}]}, {"year": "1866", "text": "<PERSON>, English physiologist and academic (d. 1947)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and academic (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and academic (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, German author and painter (d. 1918)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and painter (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and painter (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, American painter (d. 1959)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON>, Estonian bishop and saint (d. 1919)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Platon_(Ku<PERSON><PERSON>ch)\" class=\"mw-redirect\" title=\"Platon (Ku<PERSON>busch)\"><PERSON><PERSON></a>, Estonian bishop and saint (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Platon_(<PERSON><PERSON><PERSON><PERSON>)\" class=\"mw-redirect\" title=\"<PERSON>n (Ku<PERSON>busch)\"><PERSON><PERSON></a>, Estonian bishop and saint (d. 1919)", "links": [{"title": "Platon (Kulbusch)", "link": "https://wikipedia.org/wiki/<PERSON>n_(<PERSON><PERSON><PERSON><PERSON>)"}]}, {"year": "1870", "text": "<PERSON><PERSON>, American painter and illustrator (d. 1966)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Maxfield_Parrish\" title=\"Maxfield Parrish\"><PERSON><PERSON></a>, American painter and illustrator (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maxfield_Parrish\" title=\"Maxfield Parrish\"><PERSON><PERSON></a>, American painter and illustrator (d. 1966)", "links": [{"title": "Maxfield Parrish", "link": "https://wikipedia.org/wiki/Maxfield_<PERSON>sh"}]}, {"year": "1875", "text": "<PERSON>, Indian hunter, environmentalist, and author (d. 1955)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian hunter, environmentalist, and author (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian hunter, environmentalist, and author (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese philosopher and scholar (d. 1949)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese philosopher and scholar (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese philosopher and scholar (d. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American commander (d. 1942)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Italian pianist, composer, and conductor (d. 1947)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist, composer, and conductor (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist, composer, and conductor (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American golfer (d. 1926)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American actor (d. 1974)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON>, Bosnian Serb revolutionary (d. 1918)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Gavrilo_Princip\" title=\"Gavrilo Princip\"><PERSON><PERSON><PERSON><PERSON> Prin<PERSON></a>, Bosnian Serb revolutionary (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gavrilo_Princip\" title=\"Gavrilo Princip\"><PERSON><PERSON><PERSON><PERSON> Princi<PERSON></a>, Bosnian Serb revolutionary (d. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>rin<PERSON>", "link": "https://wikipedia.org/wiki/Gavrilo_Princip"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Danish actress (d. 1968)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Ingeborg_Spangsfeldt\" title=\"Ingeborg Spangsfeldt\">Ingeborg Spangsfeldt</a>, Danish actress (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ingeborg_Spangsfeldt\" title=\"Ingeborg Spangsfeldt\">Ingeborg Spangsfeldt</a>, Danish actress (d. 1968)", "links": [{"title": "Ingeborg Spangsfeldt", "link": "https://wikipedia.org/wiki/Ingeborg_Spangsfeldt"}]}, {"year": "1896", "text": "<PERSON>, American actor and stuntman (d. 1967)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and stuntman (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and stuntman (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Scottish author and playwright (d. 1952)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and playwright (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and playwright (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American author and poet (d. 1993)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Egyptian physician and Righteous Among the Nations (d. 1982)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian physician and <a href=\"https://wikipedia.org/wiki/Righteous_Among_the_Nations\" title=\"Righteous Among the Nations\">Righteous Among the Nations</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian physician and <a href=\"https://wikipedia.org/wiki/Righteous_Among_the_Nations\" title=\"Righteous Among the Nations\">Righteous Among the Nations</a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Righteous Among the Nations", "link": "https://wikipedia.org/wiki/Righteous_Among_the_Nations"}]}, {"year": "1901", "text": "<PERSON>, American actress and singer (d. 1973)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American philosopher and author (d. 1983)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Bulgarian-Swiss novelist, playwright, and memoirist, Nobel Prize laureate (d. 1994)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-Swiss novelist, playwright, and memoirist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-Swiss novelist, playwright, and memoirist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1905", "text": "<PERSON>, French race car driver (d. 1977)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English author and illustrator (d. 1990)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and illustrator (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and illustrator (d. 1990)", "links": [{"title": "<PERSON><PERSON>-Pitchford", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>ford"}]}, {"year": "1906", "text": "<PERSON>, American saxophonist and clarinet player (d. 1970)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, English cricketer (d. 1987)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French priest and author (d. 2004)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French priest and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Am<PERSON><PERSON>-<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French priest and author (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1908", "text": "<PERSON>, American actor (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American centenarian (d. 2024)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American centenarian (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American centenarian (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American football player and actor (d. 1994)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "1915", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan businessman and politician", "html": "1915 - <a href=\"https://wikipedia.org/wiki/S._U._<PERSON><PERSON>\" title=\"S. U. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._U._<PERSON><PERSON>\" title=\"S. U. E<PERSON>\"><PERSON>. <PERSON><PERSON></a>, Sri Lankan businessman and politician", "links": [{"title": "S. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American lieutenant and pilot (d. 1944)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON> Jr.</a>, American lieutenant and pilot (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American lieutenant and pilot (d. 1944)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1916", "text": "<PERSON>, Canadian lawyer and politician (d. 1989)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Swiss lawyer and politician (d. 1999)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American painter and sculptor (d. 1986)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, English biophysicist, chemist, and academic (d. 1958)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English biophysicist, chemist, and academic (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English biophysicist, chemist, and academic (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, American soldier and trumpet player  (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soldier and trumpet player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soldier and trumpet player (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French mountaineer (d. 1965)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American materials scientist, physicist, Nobel Prize laureate (d. 2023)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American materials scientist, physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American materials scientist, physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, American actress (d. 2008)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ty"}]}, {"year": "1923", "text": "<PERSON>, American mathematician and theorist (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Swedish journalist and author (d. 2007)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "1924", "text": "<PERSON>, American lawyer and politician (d. 1984)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank <PERSON>\"><PERSON></a>, American lawyer and politician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank Church\"><PERSON></a>, American lawyer and politician (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, South African cricketer and hockey player (d. 2004)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Scotch_Taylor\" title=\"Scotch Taylor\"><PERSON></a>, South African cricketer and hockey player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scotch_Taylor\" title=\"Scotch Taylor\"><PERSON></a>, South African cricketer and hockey player (d. 2004)", "links": [{"title": "Scotch Taylor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American R&B drummer (d. 1969)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Benjamin\"><PERSON></a>, American R&amp;B drummer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Benjamin\" title=\"Benny Benjamin\"><PERSON></a>, American R&amp;B drummer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor and director (d. 1986)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jerry_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American race car driver (d. 2020)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Finnish journalist and politician", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Zilliacus"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American baseball player, coach, and manager (d. 2009)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lockman\"><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lockman\"><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lockman"}]}, {"year": "1926", "text": "<PERSON>, British television producer and director (d. 1998)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, British television producer and director (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, British television producer and director (d. 1998)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Brazilian actress (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian actress (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, French actor, director, and screenwriter (d. 2003)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American journalist and author (d. 2022)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Midge_Decter\" title=\"Midge Decter\">Mid<PERSON></a>, American journalist and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Midge_Decter\" title=\"Midge Decter\">Midge Decter</a>, American journalist and author (d. 2022)", "links": [{"title": "Midge Decter", "link": "https://wikipedia.org/wiki/Midge_Decter"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Pakistani politician, 10th Governor of Punjab (d. 2000)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani politician, 10th <a href=\"https://wikipedia.org/wiki/Governor_of_Punjab,_Pakistan\" title=\"Governor of Punjab, Pakistan\">Governor of Punjab</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani politician, 10th <a href=\"https://wikipedia.org/wiki/Governor_of_Punjab,_Pakistan\" title=\"Governor of Punjab, Pakistan\">Governor of Punjab</a> (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Punjab, Pakistan", "link": "https://wikipedia.org/wiki/Governor_of_Punjab,_Pakistan"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Kenyan activist and politician (d. 1982)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan activist and politician (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan activist and politician (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Filipino actor, singer, and producer (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor, singer, and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor, singer, and producer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Filipino actor (d. 1988)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Estonian-English businessman (d. 2008)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-English businessman (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-English businessman (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>be"}]}, {"year": "1929", "text": "<PERSON>, Canadian businessman and politician, 36th Canadian Minister of Public Works", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 36th <a href=\"https://wikipedia.org/wiki/Minister_of_Public_Works_(Canada)\" title=\"Minister of Public Works (Canada)\">Canadian Minister of Public Works</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 36th <a href=\"https://wikipedia.org/wiki/Minister_of_Public_Works_(Canada)\" title=\"Minister of Public Works (Canada)\">Canadian Minister of Public Works</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Public Works (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Public_Works_(Canada)"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician, 14th Speaker of the Lok Sabha (d. 2018)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Speaker of the Lok Sabha", "link": "https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha"}]}, {"year": "1929", "text": "<PERSON>, Canadian ice hockey player (d. 1995)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, New Zealand cricketer and manager (d. 1985)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>pple\"><PERSON></a>, New Zealand cricketer and manager (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>pp<PERSON>\"><PERSON></a>, New Zealand cricketer and manager (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Canadian actress and singer (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Polish-Canadian journalist and criminologist (d. 1990)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian journalist and criminologist (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian journalist and criminologist (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American economist and academic (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>f"}]}, {"year": "1930", "text": "<PERSON>, Scottish-American singer and actress (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American singer and actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American singer and actress (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English sculptor and educator (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English sculptor and educator (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English sculptor and educator (d. 2022)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1932", "text": "<PERSON>, American astronaut (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American trumpet player and composer (d. 1978)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, French director and screenwriter", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American actress and singer (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress and singer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress and singer (d. 2018)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Saudi Arabian businessman (d. 2017)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Saudi Arabian businessman (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Saudi Arabian businessman (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Canadian educator and politician, 33rd Speaker of the House of Commons of Canada (d. 2009)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 33rd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_of_Canada\" class=\"mw-redirect\" title=\"Speaker of the House of Commons of Canada\">Speaker of the House of Commons of Canada</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 33rd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_of_Canada\" class=\"mw-redirect\" title=\"Speaker of the House of Commons of Canada\">Speaker of the House of Commons of Canada</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rent"}, {"title": "Speaker of the House of Commons of Canada", "link": "https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_of_Canada"}]}, {"year": "1935", "text": "<PERSON>, American football player and coach (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" class=\"mw-redirect\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" class=\"mw-redirect\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (d. 2024)", "links": [{"title": "<PERSON> (American football coach)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)"}]}, {"year": "1935", "text": "<PERSON>, American baseball player and coach (d. 2006)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Swedish lawyer and politician (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English race car driver (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English-Australian architect and academic", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian architect and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian architect and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Baron <PERSON> of Kaimsthorn, English archaeologist and academic", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON><PERSON>_of_Kaimsthorn\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Kaimsthorn\"><PERSON>, Baron <PERSON> of Kaimsthorn</a>, English archaeologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Kaimsthorn\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Kaimsthorn\"><PERSON>, Baron <PERSON> of Kaimsthorn</a>, English archaeologist and academic", "links": [{"title": "<PERSON>, Baron <PERSON> of Kaimsthorn", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Kaimsthorn"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Indian politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/S._Ramadoss\" title=\"S. Ramadoss\"><PERSON><PERSON></a>, Indian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._Ramadoss\" title=\"S. Ramadoss\"><PERSON><PERSON></a>, Indian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._<PERSON>s"}]}, {"year": "1940", "text": "<PERSON>, American-English journalist and author (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English journalist and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Spanish-born Scottish rock musician and songwriter (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-born Scottish rock musician and songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-born Scottish rock musician and songwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American basketball player (d. 2016)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, African-American lynching and kidnapping victim (d. 1955)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American lynching and kidnapping victim (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American lynching and kidnapping victim (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English singer and drummer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Polish-German politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English journalist and author (d. 2016)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Nicaraguan drummer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Areas\" title=\"José Areas\">José <PERSON></a>, Nicaraguan drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Areas\" title=\"José Areas\"><PERSON></a>, Nicaraguan drummer", "links": [{"title": "José <PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Areas"}]}, {"year": "1946", "text": "<PERSON>, French fashion designer and sculptor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American radio host", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(political_commentator)\" title=\"<PERSON> (political commentator)\"><PERSON></a>, American radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(political_commentator)\" title=\"<PERSON> (political commentator)\"><PERSON></a>, American radio host", "links": [{"title": "<PERSON> (political commentator)", "link": "https://wikipedia.org/wiki/<PERSON>_(political_commentator)"}]}, {"year": "1946", "text": "<PERSON>, Cuban-Jamaican singer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-Jamaican singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Marley\"><PERSON></a>, Cuban-Jamaican singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Sri Lankan politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Macedonian-Croatian pop singer (d. 2016)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Macedonian-Croatian pop singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Macedonian-Croatian pop singer (d. 2016)", "links": [{"title": "Ljupka Dimitrovska", "link": "https://wikipedia.org/wiki/Ljup<PERSON>_Dimitrovska"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1984)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English singer-songwriter and bass player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American lawyer and activist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, American lawyer and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, American lawyer and activist", "links": [{"title": "<PERSON> (activist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American bass player and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Portuguese architect, designed the Estádio Municipal de Braga", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese architect, designed the <a href=\"https://wikipedia.org/wiki/Est%C3%A1dio_Municipal_de_Braga\" title=\"Estádio Municipal de Braga\">Estádio Municipal de Braga</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese architect, designed the <a href=\"https://wikipedia.org/wiki/Est%C3%A1dio_Municipal_de_Braga\" title=\"Estádio Municipal de Braga\">Estádio Municipal de Braga</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Estádio Municipal de Braga", "link": "https://wikipedia.org/wiki/Est%C3%A1dio_Municipal_de_Braga"}]}, {"year": "1953", "text": "<PERSON>, American football player and race car driver (d. 1999)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and race car driver (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and race car driver (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Chicago based author, foremost expert on Elvis Presley", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a> based author, foremost expert on <a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\">Elvis Presley</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a> based author, foremost expert on <a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\">Elvis Presley</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American banker and politician, 14th United States Deputy Secretary of State", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 14th <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_State\" title=\"United States Deputy Secretary of State\">United States Deputy Secretary of State</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 14th <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_State\" title=\"United States Deputy Secretary of State\">United States Deputy Secretary of State</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Deputy Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_State"}]}, {"year": "1954", "text": "<PERSON>, Canadian guitarist, keyboard player, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist, keyboard player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Scottish journalist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON><PERSON> Z<PERSON>t\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Z<PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jochem_<PERSON>t"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Somalian-English model and actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(model)\" title=\"<PERSON><PERSON> (model)\"><PERSON><PERSON></a>, Somalian-English model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(model)\" title=\"<PERSON><PERSON> (model)\"><PERSON><PERSON></a>, Somalian-English model and actress", "links": [{"title": "<PERSON><PERSON> (model)", "link": "https://wikipedia.org/wiki/I<PERSON>_(model)"}]}, {"year": "1955", "text": "<PERSON>, American guitarist and songwriter (d. 2009)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American scientist and engineer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1957", "text": "<PERSON>, Canadian skier", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American astrophysicist and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, guitarist, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Russian footballer and manager (d. 2014)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer and manager (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American chef and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian snooker player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American photographer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "links": [{"title": "Justice <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Latvian film director, producer, screenwriter, and editor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/M%C4%<PERSON><PERSON>_<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (director)\"><PERSON><PERSON><PERSON></a>, Latvian film director, producer, screenwriter, and editor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C4%<PERSON><PERSON>_<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (director)\"><PERSON><PERSON><PERSON></a>, Latvian film director, producer, screenwriter, and editor", "links": [{"title": "<PERSON><PERSON><PERSON> (director)", "link": "https://wikipedia.org/wiki/M%C4%81<PERSON>_<PERSON><PERSON>_(director)"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Dutch tennis player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ku<PERSON>\" title=\"Carin Bakkum\"><PERSON><PERSON></a>, Dutch tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ku<PERSON>\" title=\"Carin Bakkum\"><PERSON><PERSON></a>, Dutch tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Carin_<PERSON>kum"}]}, {"year": "1962", "text": "<PERSON>, American baseball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian politician, 44th Mayor of Montreal", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 44th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 44th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rre"}, {"title": "Mayor of Montreal", "link": "https://wikipedia.org/wiki/Mayor_of_Montreal"}]}, {"year": "1963", "text": "<PERSON>, Welsh chess player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American journalist and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American ice hockey player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American designer and journalist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Breuk_Iversen\" title=\"Breuk Iversen\"><PERSON><PERSON><PERSON></a>, American designer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Breuk_Iversen\" title=\"Breuk Iversen\"><PERSON><PERSON><PERSON></a>, American designer and journalist", "links": [{"title": "Breuk Iversen", "link": "https://wikipedia.org/wiki/<PERSON>re<PERSON>_<PERSON>n"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, American actress, director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American baseball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian rugby league player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, New Zealand rugby player and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American bass player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Dutch minister and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch minister and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch minister and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Norwegian footballer and referee", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, South African cricketer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Chinese journalist and poet", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Chinese journalist and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Chinese journalist and poet", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1969", "text": "<PERSON>, American basketball player and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Australian politician, 39th Premier of Queensland", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Annastacia <PERSON>uk\"><PERSON><PERSON><PERSON></a>, Australian politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Annastacia <PERSON>czuk\"><PERSON><PERSON><PERSON></a>, Australian politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a>", "links": [{"title": "Annastacia Palaszczuk", "link": "https://wikipedia.org/wiki/Annastacia_Palas<PERSON>uk"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1969", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/D._<PERSON><PERSON>_Woodside\" title=\"D<PERSON> <PERSON>. Woodside\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D._<PERSON><PERSON>_Woodside\" title=\"D. <PERSON>. Woodside\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "links": [{"title": "D. B<PERSON> Woodside", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Woodside"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American basketball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American baseball player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian rugby league player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English singer-songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lt<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lt<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dani_Filth"}]}, {"year": "1973", "text": "<PERSON>, English footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Albanian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Igli_<PERSON>\" title=\"Igli Ta<PERSON>\"><PERSON><PERSON></a>, Albanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Igli_<PERSON>\" title=\"Igli Tare\"><PERSON><PERSON></a>, Albanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Igli_Tare"}]}, {"year": "1974", "text": "<PERSON>, American animator, producer, and screenwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Faust\"><PERSON></a>, American animator, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Faust\"><PERSON></a>, American animator, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Estonian lawyer and judge", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julia_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Japanese rugby player and wrestler", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese rugby player and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese rugby player and wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, English footballer and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Guianan-French footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guianan-French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guianan-French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Mexican wrestler", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1975", "text": "<PERSON>, American bass player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Ev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Brazilian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>sun%C3%A7%C3%A3o"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Macedonian poet and critic", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Macedonian poet and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Macedonian poet and critic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Puerto Rican-American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican-American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/Javier_V%C3%<PERSON><PERSON><PERSON>_(baseball)"}]}, {"year": "1977", "text": "<PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English snooker player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English cricketer and umpire", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Egyptian politician and journalist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian politician and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian politician and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1980", "text": "<PERSON>, Finnish race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, New Zealand rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Irish wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Finn_B%C3%A1lor\" title=\"Finn B<PERSON>\"><PERSON></a>, Irish wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finn_B%C3%A1lor\" title=\"Finn <PERSON>\"><PERSON></a>, Irish wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Finn_B%C3%A1lor"}]}, {"year": "1981", "text": "<PERSON>, American soccer player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Cypriot footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constant<PERSON>s_Charalambidis"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Y%C5%ABichi_Komano\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>mano\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y%C5%ABichi_Komano"}]}, {"year": "1981", "text": "<PERSON>, American rapper and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian TV host", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian TV host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian TV host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor and musician (d. 2008)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Serbian basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Nenad_Krsti%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nenad_Krsti%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nenad_Krsti%C4%87"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>roke<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>roke<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lid<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor and athlete", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Brazilian race car driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, Brazilian race car driver", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1985", "text": "<PERSON>, Colombian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Ivorian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1986", "text": "<PERSON>, Brazilian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1987", "text": "<PERSON>, American ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1987", "text": "<PERSON>, Dutch footballer and rapper", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, Dutch footballer and rapper", "links": [{"title": "Mitchell <PERSON>", "link": "https://wikipedia.org/wiki/Mitchell_Burgzorg"}]}, {"year": "1987", "text": "<PERSON>, Brazilian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born 1987)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1987)"}]}, {"year": "1987", "text": "<PERSON>, English DJ, singer and songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English DJ, singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English DJ, singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Filipino singer and actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Dutch footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Dutch footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English skater", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_July_1988)\" title=\"<PERSON><PERSON><PERSON> (footballer, born July 1988)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_July_1988)\" title=\"<PERSON><PERSON><PERSON> (footballer, born July 1988)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born July 1988)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_July_1988)"}]}, {"year": "1988", "text": "<PERSON>, Irish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Russian basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thodor<PERSON>_<PERSON>s"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American Twitch streamer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Twitch_(service)\" title=\"Twitch (service)\">Twitch</a> streamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Twitch_(service)\" title=\"Twitch (service)\">Twitch</a> streamer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Twitch (service)", "link": "https://wikipedia.org/wiki/Twitch_(service)"}]}, {"year": "1992", "text": "<PERSON>, Russian ice hockey player (d. 2016)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Russian ice hockey player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Russian ice hockey player (d. 2016)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Serbian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>evanovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Natalija_Stevanovi%C4%87"}]}, {"year": "1994", "text": "<PERSON>, Russian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Greek tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/1995\" title=\"1995\">1995</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1995\" title=\"1995\">1995</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek tennis player", "links": [{"title": "1995", "link": "https://wikipedia.org/wiki/1995"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Australian rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actress", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Chinese singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Chinese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Chinese singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "2001", "text": "<PERSON>, American football player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Turkish basketball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Alperen_%C5%9Eeng%C3%BCn\" title=\"Alperen Şengün\"><PERSON><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alperen_%C5%9Eeng%C3%BCn\" title=\"Alperen Şengün\"><PERSON><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "Alper<PERSON>", "link": "https://wikipedia.org/wiki/Alperen_%C5%9Eeng%C3%BCn"}]}, {"year": "2002", "text": "<PERSON>, Czech footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%BEek\" title=\"<PERSON>\"><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%BEek\" title=\"<PERSON>\"><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adam_Hlo%C5%BEek"}]}], "Deaths": [{"year": "306", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 250)", "html": "306 - <a href=\"https://wikipedia.org/wiki/Con<PERSON>ius_Chlorus\" title=\"Constantius Chlorus\"><PERSON><PERSON><PERSON>lor<PERSON></a>, Roman emperor (b. 250)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ius_Chlorus\" title=\"Constantius Chlorus\"><PERSON><PERSON><PERSON>lor<PERSON></a>, Roman emperor (b. 250)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantius_Chlorus"}]}, {"year": "885", "text": "<PERSON><PERSON><PERSON>, margrave of Neustria", "html": "885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Neustria\" title=\"<PERSON><PERSON><PERSON> of Neustria\"><PERSON><PERSON><PERSON></a>, margrave of Neustria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Neustria\" title=\"<PERSON><PERSON><PERSON> of Neustria\"><PERSON><PERSON><PERSON></a>, margrave of Neustria", "links": [{"title": "<PERSON><PERSON><PERSON> of Neustria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Neustria"}]}, {"year": "1011", "text": "<PERSON><PERSON><PERSON>, emperor of Japan (b. 980)", "html": "1011 - <a href=\"https://wikipedia.org/wiki/Emperor_I<PERSON>j%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>j%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 980)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>%C5%8D"}]}, {"year": "1190", "text": "<PERSON><PERSON><PERSON>, queen of Jerusalem", "html": "1190 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Queen_of_Jerusalem\" title=\"<PERSON><PERSON><PERSON>, Queen of Jerusalem\"><PERSON><PERSON><PERSON></a>, queen of Jerusalem", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Queen_of_Jerusalem\" title=\"<PERSON><PERSON><PERSON>, Queen of Jerusalem\"><PERSON><PERSON><PERSON></a>, queen of Jerusalem", "links": [{"title": "<PERSON><PERSON><PERSON>, Queen of Jerusalem", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Queen_of_Jerusalem"}]}, {"year": "1195", "text": "<PERSON><PERSON> of Landsberg, abbess, author, and illustrator (b. c. 1130)", "html": "1195 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Landsberg\" title=\"<PERSON><PERSON> of Landsberg\"><PERSON><PERSON> of Landsberg</a>, abbess, author, and illustrator (b. c. 1130)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Landsberg\" title=\"<PERSON><PERSON> of Landsberg\"><PERSON><PERSON> of Landsberg</a>, abbess, author, and illustrator (b. c. 1130)", "links": [{"title": "Herrad of Landsberg", "link": "https://wikipedia.org/wiki/Herrad_of_Landsberg"}]}, {"year": "1409", "text": "<PERSON>, king of Sicily (b. 1376)", "html": "1409 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON></a>, king of Sicily (b. 1376)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON></a>, king of Sicily (b. 1376)", "links": [{"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sicily"}]}, {"year": "1471", "text": "<PERSON>, German priest and mystic", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%A0_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and mystic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and mystic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%A0_<PERSON>is"}]}, {"year": "1472", "text": "<PERSON> Artois, French nobleman (b. 1394)", "html": "1472 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Eu\" title=\"<PERSON> Artois, Count of Eu\"><PERSON> Artois</a>, French nobleman (b. 1394)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Eu\" title=\"<PERSON> Artois, Count of Eu\"><PERSON> of Artois</a>, French nobleman (b. 1394)", "links": [{"title": "<PERSON> Artois, Count of Eu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_<PERSON>"}]}, {"year": "1492", "text": "<PERSON>, pope of the Catholic Church (b. 1432)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_VIII\" title=\"Pope Innocent VIII\"><PERSON> VIII</a>, pope of the Catholic Church (b. 1432)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Innocent_VIII\" title=\"Pope Innocent VIII\"><PERSON></a>, pope of the Catholic Church (b. 1432)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_VIII"}]}, {"year": "1564", "text": "<PERSON>, Holy Roman Emperor (b. 1503)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (b. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (b. 1503)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1572", "text": "<PERSON>, Ottoman rabbi and mystic (b. 1534)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman rabbi and mystic (b. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman rabbi and mystic (b. 1534)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1608", "text": "<PERSON><PERSON><PERSON>, Italian composer (b. 1556)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/Pomponio_Nenna\" title=\"Pomponio Nenna\">Pompo<PERSON></a>, Italian composer (b. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pomponio_Nenna\" title=\"Pomponio Nenna\"><PERSON><PERSON><PERSON></a>, Italian composer (b. 1556)", "links": [{"title": "Pomponio Nenna", "link": "https://wikipedia.org/wiki/Pomponio_Nenna"}]}, {"year": "1616", "text": "<PERSON>, German physician and chemist (b. 1550)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and chemist (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and chemist (b. 1550)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON>, 1st Earl of Kingston-upon-Hull, English general and politician (b. 1584)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Kingston-upon-Hull\" title=\"<PERSON>, 1st Earl of Kingston-upon-Hull\"><PERSON>, 1st Earl of Kingston-upon-Hull</a>, English general and politician (b. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Kingston-upon-Hull\" title=\"<PERSON>, 1st Earl of Kingston-upon-Hull\"><PERSON>, 1st Earl of Kingston-upon-Hull</a>, English general and politician (b. 1584)", "links": [{"title": "<PERSON>, 1st Earl of Kingston-upon-Hull", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Kingston-upon-Hull"}]}, {"year": "1681", "text": "<PERSON><PERSON>, English-American minister and educator (b. 1631)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American minister and educator (b. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American minister and educator (b. 1631)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, German educator and reformer (b. 1723)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and reformer (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and reformer (b. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, American soldier and politician, 1st Governor of New Jersey (b. 1723)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (b. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1791", "text": "<PERSON>, American merchant and politician (b. 1735)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician (b. 1735)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, Greek-French poet and author (b. 1762)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Ch%C3%A9nier\" title=\"<PERSON>\"><PERSON></a>, Greek-French poet and author (b. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Ch%C3%A9nier\" title=\"<PERSON>\"><PERSON></a>, Greek-French poet and author (b. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Ch%C3%A9nier"}]}, {"year": "1794", "text": "<PERSON><PERSON><PERSON>, French poet and author (b. 1745)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and author (b. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and author (b. 1745)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1794", "text": "<PERSON>, Prussian adventurer and author (b. 1726)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian adventurer and author (b. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian adventurer and author (b. 1726)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON><PERSON><PERSON>, Russian poet and publisher (b. 1795)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and publisher (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and publisher (b. 1795)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, Polish composer and pianist (b. 1789)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish composer and pianist (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish composer and pianist (b. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_<PERSON>zy<PERSON>owska"}]}, {"year": "1834", "text": "<PERSON>, English philosopher, poet, and critic (b. 1772)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, poet, and critic (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, poet, and critic (b. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, French physician and surgeon (b. 1766)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and surgeon (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and surgeon (b. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, Scottish chemist and inventor of waterproof fabric (b. 1766)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and inventor of waterproof fabric (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and inventor of waterproof fabric (b. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, Swiss lawyer and politician, President of the Swiss Confederation (b. 1805)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Swiss Confederation", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_Confederation"}]}, {"year": "1865", "text": "<PERSON>, English soldier and surgeon (b. 1799)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, English soldier and surgeon (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, English soldier and surgeon (b. 1799)", "links": [{"title": "<PERSON> (surgeon)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(surgeon)"}]}, {"year": "1887", "text": "<PERSON>, American religious leader, 3rd President of The Church of Jesus Christ of Latter-day Saints (b. 1808)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Mormon)\" title=\"<PERSON> (Mormon)\"><PERSON></a>, American religious leader, 3rd <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Mormon)\" title=\"<PERSON> (Mormon)\"><PERSON></a>, American religious leader, 3rd <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1808)", "links": [{"title": "<PERSON> (Mormon)", "link": "https://wikipedia.org/wiki/<PERSON>_(Mormon)"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1934", "text": "<PERSON>, French businessman, founded <PERSON><PERSON> (b. 1874)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Co<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman, founded <a href=\"https://wikipedia.org/wiki/Coty\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Co<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman, founded <a href=\"https://wikipedia.org/wiki/Co<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Coty"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Coty"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Austrian politician, 14th Chancellor of Austria (b. 1892)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician, 14th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician, 14th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a> (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chancellor of Austria", "link": "https://wikipedia.org/wiki/Chancellor_of_Austria"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Ukrainian anarchist revolutionary (b. 1888)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>\" title=\"Nest<PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian anarchist revolutionary (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>\" title=\"Nest<PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian anarchist revolutionary (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nestor_<PERSON><PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American triple jumper (b. 1879)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American triple jumper (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American triple jumper (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English sculptor (b. 1878)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English organist and composer (b. 1909)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Finnish wrestler (b. 1891)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish wrestler (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish wrestler (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Polish-born Irish rabbi and author (b. 1888)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>zog\" title=\"<PERSON><PERSON><PERSON>zo<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-born Irish rabbi and author (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>zog\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-born Irish rabbi and author (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>zog"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and jurist, 9th Chief Justice of Canada (b. 1879)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Thi<PERSON>udeau_Rinfret\" title=\"Thibaudeau Rinfret\"><PERSON><PERSON><PERSON><PERSON><PERSON> Rinfret</a>, Canadian lawyer and jurist, 9th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thi<PERSON>udeau_Rinfret\" title=\"Thibaudeau Rinfret\"><PERSON><PERSON><PERSON><PERSON><PERSON> Rinfret</a>, Canadian lawyer and jurist, 9th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (b. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Rinfret", "link": "https://wikipedia.org/wiki/Thi<PERSON><PERSON><PERSON>_Rinfret"}, {"title": "Chief Justice of Canada", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Canada"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Italian neurologist and academic (b. 1877)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian neurologist and academic (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian neurologist and academic (b. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American poet and critic (b. 1926)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_O%27Hara"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Egyptian-Greek painter (b. 1878)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-Greek painter (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-Greek painter (b. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hen<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American swimmer and water polo player (b. 1880)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer and water polo player (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer and water polo player (b. 1880)", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_(swimmer)"}]}, {"year": "1971", "text": "<PERSON>, American composer and educator (b. 1896)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Jamaican-American journalist and activist (b. 1895)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-American journalist and activist (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-American journalist and activist (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian lawyer and politician, 12th Prime Minister of Canada (b. 1882)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Louis_St._Laurent\" title=\"Louis St. Laurent\"><PERSON> St. Laurent</a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_St._Laurent\" title=\"Louis St. Laurent\"><PERSON> St. Laurent</a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1882)", "links": [{"title": "Louis St. Laurent", "link": "https://wikipedia.org/wiki/Louis_St._Laurent"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian educationist (b. 1900)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Shivrampant_<PERSON>le\" title=\"Shivrampant Damle\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian educationist (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shivrampant_<PERSON>le\" title=\"Shivrampant Damle\">Shiv<PERSON><PERSON><PERSON></a>, Indian educationist (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shivrampant_Damle"}]}, {"year": "1980", "text": "<PERSON>, Russian singer-songwriter, guitarist, and actor (b. 1938)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter, guitarist, and actor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter, guitarist, and actor (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>ky"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Puerto Rican nurse, author, feminist, and activist (b. 1889)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Gonz%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican nurse, author, feminist, and activist (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Gonz%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican nurse, author, feminist, and activist (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rosa_<PERSON><PERSON>_Gonz%C3%A1lez"}]}, {"year": "1982", "text": "<PERSON>, Canadian-American author and illustrator (b. 1892)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and illustrator (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and illustrator (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player (b. 1913)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1984", "text": "<PERSON> <PERSON>, American singer-songwriter (b. 1926)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Big_Mama_Thornton\" title=\"Big Mama Thornton\">Big Mama <PERSON></a>, American singer-songwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_Mama_Thornton\" title=\"Big Mama Thornton\">Big Mama <PERSON></a>, American singer-songwriter (b. 1926)", "links": [{"title": "Big Mama Thornton", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American director and screenwriter (b. 1903)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and screenwriter (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and screenwriter (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American child actress (b. 1978)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American child actress (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American child actress (b. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American businessman, co-owner of Studio 54 (b. 1943)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-owner of <a href=\"https://wikipedia.org/wiki/Studio_54\" title=\"Studio 54\">Studio 54</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-owner of <a href=\"https://wikipedia.org/wiki/Studio_54\" title=\"Studio 54\">Studio 54</a> (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Studio 54", "link": "https://wikipedia.org/wiki/Studio_54"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Soviet politician (b. 1893)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet politician (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet politician (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor and singer (b. 1914)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American singer-songwriter (b. 1932)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rich\"><PERSON></a>, American singer-songwriter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rich\"><PERSON></a>, American singer-songwriter (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American golfer (b. 1912)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Greek businessman, co-founded <PERSON><PERSON><PERSON> (b. 1910)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek businessman, co-founded <a href=\"https://wikipedia.org/wiki/Papastratos\" title=\"<PERSON>stra<PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek businessman, co-founded <a href=\"https://wikipedia.org/wiki/Papa<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Papastratos"}]}, {"year": "2000", "text": "<PERSON><PERSON>, German footballer, coach, and manager (b. 1934)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Rudi_Fa%C3%9Fnacht\" title=\"Rudi Faßnacht\"><PERSON><PERSON></a>, German footballer, coach, and manager (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rudi_Fa%C3%9Fnacht\" title=\"Rudi Faßnacht\"><PERSON><PERSON></a>, German footballer, coach, and manager (b. 1934)", "links": [{"title": "R<PERSON>", "link": "https://wikipedia.org/wiki/Rudi_Fa%C3%9Fnacht"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Egyptian philosopher and poet (b. 1917)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian philosopher and poet (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian philosopher and poet (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, German engineer (b. 1912)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Ludwig_B%C3%B6lkow\" title=\"<PERSON>\"><PERSON></a>, German engineer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludwig_B%C3%B6lkow\" title=\"<PERSON>\"><PERSON></a>, German engineer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ludwig_B%C3%B6lkow"}]}, {"year": "2003", "text": "<PERSON>, English actor, director, producer, and screenwriter (b. 1926)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Australian philosopher and academic (b. 1914)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philosopher and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philosopher and academic (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, German trombonist (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German trombonist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German trombonist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Romanian-Israeli poet and philologist (b. 1928)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Israeli poet and philologist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Israeli poet and philologist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, German footballer and manager (b. 1952)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Australian footballer (b. 1955)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American chemist and academic (b. 1919)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Tracy_Hall\" title=\"Tracy Hall\"><PERSON></a>, American chemist and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tracy_Hall\" title=\"Tracy Hall\"><PERSON></a>, American chemist and academic (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tracy_Hall"}]}, {"year": "2008", "text": "<PERSON>, American computer scientist and educator (b. 1960)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and educator (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and educator (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American boxer (b. 1971)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, English author (b. 1919)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, English soldier (b. 1898)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Malaysian film director (b. 1958)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian film director (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian film director (b. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Cypriot-Greek director, producer, and screenwriter (b. 1922)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot-Greek director, producer, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot-Greek director, producer, and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian director and screenwriter (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian director and screenwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian director and screenwriter (b. 1934)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English director and producer (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American-Canadian football player and coach (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Austrian painter and sculptor (b. 1947)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Franz_West\" title=\"Franz West\"><PERSON></a>, Austrian painter and sculptor (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franz_West\" title=\"Franz West\"><PERSON></a>, Austrian painter and sculptor (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_West"}]}, {"year": "2013", "text": "<PERSON>, American sculptor, illustrator, and composer (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor, illustrator, and composer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor, illustrator, and composer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English-American biologist and academic (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American biologist and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American biologist and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, German-American author and academic (b. 1911)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American author and academic (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American author and academic (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Kaufman"}]}, {"year": "2014", "text": "<PERSON>, Australian painter and illustrator (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and illustrator (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and illustrator (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, French diplomat, French ambassador to the United States (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French diplomat, <a href=\"https://wikipedia.org/wiki/French_ambassador_to_the_United_States\" class=\"mw-redirect\" title=\"French ambassador to the United States\">French ambassador to the United States</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French diplomat, <a href=\"https://wikipedia.org/wiki/French_ambassador_to_the_United_States\" class=\"mw-redirect\" title=\"French ambassador to the United States\">French ambassador to the United States</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "French ambassador to the United States", "link": "https://wikipedia.org/wiki/French_ambassador_to_the_United_States"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, 18th Governor of Kerala (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/R._S._Gavai\" title=\"R. S. Gavai\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_Kerala\" class=\"mw-redirect\" title=\"Governor of Kerala\">Governor of Kerala</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R._S._Gavai\" title=\"R. S. Gavai\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_Kerala\" class=\"mw-redirect\" title=\"Governor of Kerala\">Governor of Kerala</a> (b. 1929)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R._<PERSON><PERSON>_G<PERSON>i"}, {"title": "Governor of Kerala", "link": "https://wikipedia.org/wiki/Governor_of_Kerala"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and coach (b. 1946)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American Christian minister and author (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Christian minister and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Christian minister and author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American television personality (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1944)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1944)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "2018", "text": "<PERSON>, Italian-Canadian businessman (b. 1952)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Canadian businessman (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Canadian businessman (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, 4th President and 9th Prime Minister of Tunisia (b. 1926)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Beji_Caid_Essebsi\" title=\"Beji Caid Essebsi\"><PERSON>ji Caid Essebsi</a>, 4th <a href=\"https://wikipedia.org/wiki/President_of_Tunisia\" title=\"President of Tunisia\">President</a> and 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Tunisia\" class=\"mw-redirect\" title=\"Prime Minister of the Republic of Tunisia\">Prime Minister</a> of <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beji_Caid_Essebsi\" title=\"Beji Caid Essebsi\"><PERSON>ji Caid Essebsi</a>, 4th <a href=\"https://wikipedia.org/wiki/President_of_Tunisia\" title=\"President of Tunisia\">President</a> and 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Tunisia\" class=\"mw-redirect\" title=\"Prime Minister of the Republic of Tunisia\">Prime Minister</a> of <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beji_Caid_Essebsi"}, {"title": "President of Tunisia", "link": "https://wikipedia.org/wiki/President_of_Tunisia"}, {"title": "Prime Minister of the Republic of Tunisia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Tunisia"}, {"title": "Tunisia", "link": "https://wikipedia.org/wiki/Tunisia"}]}, {"year": "2020", "text": "<PERSON>, English blues rock guitarist, singer-songwriter and founder of <PERSON> Mac (b. 1946)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English blues rock guitarist, singer-songwriter and founder of <a href=\"https://wikipedia.org/wiki/<PERSON>_Mac\" title=\"Fleetwood Mac\"><PERSON></a> (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English blues rock guitarist, singer-songwriter and founder of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fleetwood Mac\"><PERSON></a> (b. 1946)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}, {"title": "Fleetwood Mac", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American college basketball coach (b. 1932)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/College_basketball\" title=\"College basketball\">college basketball</a> coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/College_basketball\" title=\"College basketball\">college basketball</a> coach (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "College basketball", "link": "https://wikipedia.org/wiki/College_basketball"}]}, {"year": "2022", "text": "<PERSON>, American actor (b. 1939)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Bangladeshi bassist and singer-songwriter (b. 1961)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi bassist and singer-songwriter (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi bassist and singer-songwriter (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American diplomat (b. 1951)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}