{"date": "October 6", "url": "https://wikipedia.org/wiki/October_6", "data": {"Events": [{"year": "105 BC", "text": "Cimbrian War: Defeat at the Battle of Arausio of the Roman army of the mid-Republic", "html": "105 BC - 105 BC - <a href=\"https://wikipedia.org/wiki/Cimbrian_War\" title=\"Cimbrian War\">Cimbrian War</a>: Defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_Arausio\" title=\"Battle of Arausio\">Battle of Arausio</a> of the <a href=\"https://wikipedia.org/wiki/Roman_army_of_the_mid-Republic\" title=\"Roman army of the mid-Republic\">Roman army of the mid-Republic</a>", "no_year_html": "105 BC - <a href=\"https://wikipedia.org/wiki/Cimbrian_War\" title=\"Cimbrian War\">Cimbrian War</a>: Defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_Arausio\" title=\"Battle of Arausio\">Battle of Arausio</a> of the <a href=\"https://wikipedia.org/wiki/Roman_army_of_the_mid-Republic\" title=\"Roman army of the mid-Republic\">Roman army of the mid-Republic</a>", "links": [{"title": "Cimbrian War", "link": "https://wikipedia.org/wiki/Cimbrian_War"}, {"title": "Battle of Arausio", "link": "https://wikipedia.org/wiki/Battle_of_Arausio"}, {"title": "Roman army of the mid-Republic", "link": "https://wikipedia.org/wiki/Roman_army_of_the_mid-Republic"}]}, {"year": "69 BC", "text": "Third Mithridatic War: The military of the Roman Republic subdue Armenia.", "html": "69 BC - 69 BC - <a href=\"https://wikipedia.org/wiki/Third_Mithridatic_War\" title=\"Third Mithridatic War\">Third Mithridatic War</a>: The <a href=\"https://wikipedia.org/wiki/Roman_army_of_the_late_Republic\" title=\"Roman army of the late Republic\">military</a> of the <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Roman Republic</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Tigranocerta\" title=\"Battle of Tigranocerta\">subdue Armenia</a>.", "no_year_html": "69 BC - <a href=\"https://wikipedia.org/wiki/Third_Mithridatic_War\" title=\"Third Mithridatic War\">Third Mithridatic War</a>: The <a href=\"https://wikipedia.org/wiki/Roman_army_of_the_late_Republic\" title=\"Roman army of the late Republic\">military</a> of the <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Roman Republic</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Tigranocerta\" title=\"Battle of Tigranocerta\">subdue Armenia</a>.", "links": [{"title": "Third Mithridatic War", "link": "https://wikipedia.org/wiki/Third_Mithridatic_War"}, {"title": "Roman army of the late Republic", "link": "https://wikipedia.org/wiki/Roman_army_of_the_late_Republic"}, {"title": "Roman Republic", "link": "https://wikipedia.org/wiki/Roman_Republic"}, {"title": "Battle of Tigranocerta", "link": "https://wikipedia.org/wiki/Battle_of_Tigranocerta"}]}, {"year": "23", "text": "Rebels decapitate <PERSON> two days after his capital was sacked during a peasant rebellion.", "html": "23 - AD 23 - Rebels decapitate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Man<PERSON>\"><PERSON></a> two days after his capital was sacked during a <a href=\"https://wikipedia.org/wiki/Lulin\" title=\"Lulin\">peasant rebellion</a>.", "no_year_html": "AD 23 - Rebels decapitate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Man<PERSON>\"><PERSON></a> two days after his capital was sacked during a <a href=\"https://wikipedia.org/wiki/Lulin\" title=\"Lulin\">peasant rebellion</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lin"}]}, {"year": "404", "text": "Byzantine Empress <PERSON><PERSON><PERSON> dies from the miscarriage of her seventh pregnancy.", "html": "404 - <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> Empress <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> dies from the miscarriage of her seventh pregnancy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> Empress <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>xia\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> dies from the miscarriage of her seventh pregnancy.", "links": [{"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "618", "text": "Transition from Sui to Tang: <PERSON> decisively defeats <PERSON> at the Battle of Yanshi.", "html": "618 - <a href=\"https://wikipedia.org/wiki/Transition_from_Sui_to_Tang\" title=\"Transition from Sui to Tang\">Transition from Sui to Tang</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> decisively defeats <a href=\"https://wikipedia.org/wiki/Li_Mi_(Sui_dynasty)\" title=\"Li Mi (Sui dynasty)\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Yanshi\" title=\"Battle of Yanshi\">Battle of Yanshi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Transition_from_Sui_to_Tang\" title=\"Transition from Sui to Tang\">Transition from Sui to Tang</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> decisively defeats <a href=\"https://wikipedia.org/wiki/Li_Mi_(Sui_dynasty)\" title=\"Li Mi (Sui dynasty)\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Yanshi\" title=\"Battle of Yanshi\">Battle of Yanshi</a>.", "links": [{"title": "Transition from Sui to Tang", "link": "https://wikipedia.org/wiki/Transition_from_Sui_to_Tang"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (Sui dynasty)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Sui_dynasty)"}, {"title": "Battle of Yanshi", "link": "https://wikipedia.org/wiki/Battle_of_Yanshi"}]}, {"year": "1539", "text": "Spain's <PERSON><PERSON><PERSON> expedition takes over the Apalachee capital of Anhaica for their winter quarters.", "html": "1539 - Spain's <a href=\"https://wikipedia.org/wiki/De_Soto_Expedition\" class=\"mw-redirect\" title=\"De Soto Expedition\">De<PERSON>oto expedition</a> takes over the Apalachee capital of <a href=\"https://wikipedia.org/wiki/Anhaica\" title=\"Anhaica\">Anhaica</a> for their winter quarters.", "no_year_html": "Spain's <a href=\"https://wikipedia.org/wiki/De_Soto_Expedition\" class=\"mw-redirect\" title=\"De Soto Expedition\">DeSoto expedition</a> takes over the Apalachee capital of <a href=\"https://wikipedia.org/wiki/Anhaica\" title=\"Anhaica\">Anhaica</a> for their winter quarters.", "links": [{"title": "De Soto Expedition", "link": "https://wikipedia.org/wiki/De_Soto_Expedition"}, {"title": "Anhaica", "link": "https://wikipedia.org/wiki/Anhaica"}]}, {"year": "1600", "text": "<PERSON><PERSON><PERSON>, the earliest surviving opera, receives its première performance, beginning the Baroque period.", "html": "1600 - <i><a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_(Peri)\" title=\"<PERSON>uri<PERSON> (Peri)\">Euridice</a></i>, the earliest surviving opera, receives its première performance, beginning the <a href=\"https://wikipedia.org/wiki/Baroque\" title=\"Baroque\">Baroque period</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Peri)\" title=\"<PERSON>uri<PERSON> (Peri)\">Euri<PERSON></a></i>, the earliest surviving opera, receives its première performance, beginning the <a href=\"https://wikipedia.org/wiki/Baroque\" title=\"Baroque\">Baroque period</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (Peri)", "link": "https://wikipedia.org/wiki/Euri<PERSON>_(Peri)"}, {"title": "Baroque", "link": "https://wikipedia.org/wiki/Baroque"}]}, {"year": "1683", "text": "Immigrant families found Germantown, Pennsylvania in the first major immigration of German people to America.", "html": "1683 - Immigrant families found <a href=\"https://wikipedia.org/wiki/Germantown,_Philadelphia\" title=\"Germantown, Philadelphia\">Germantown, Pennsylvania</a> in the first major immigration of <a href=\"https://wikipedia.org/wiki/German_Americans\" title=\"German Americans\">German people to America</a>.", "no_year_html": "Immigrant families found <a href=\"https://wikipedia.org/wiki/Germantown,_Philadelphia\" title=\"Germantown, Philadelphia\">Germantown, Pennsylvania</a> in the first major immigration of <a href=\"https://wikipedia.org/wiki/German_Americans\" title=\"German Americans\">German people to America</a>.", "links": [{"title": "Germantown, Philadelphia", "link": "https://wikipedia.org/wiki/Germantown,_Philadelphia"}, {"title": "German Americans", "link": "https://wikipedia.org/wiki/German_Americans"}]}, {"year": "1762", "text": "Seven Years' War: The British capture Manila from Spain and occupy it.", "html": "1762 - <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: The British <a href=\"https://wikipedia.org/wiki/Battle_of_Manila_(1762)\" title=\"Battle of Manila (1762)\">capture Manila</a> from Spain and occupy it.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: The British <a href=\"https://wikipedia.org/wiki/Battle_of_Manila_(1762)\" title=\"Battle of Manila (1762)\">capture Manila</a> from Spain and occupy it.", "links": [{"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "Battle of Manila (1762)", "link": "https://wikipedia.org/wiki/Battle_of_Manila_(1762)"}]}, {"year": "1777", "text": "American Revolutionary War: British forces capture Forts Clinton and Montgomery on the Hudson River.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British forces <a href=\"https://wikipedia.org/wiki/Battle_of_Forts_Clinton_and_Montgomery\" title=\"Battle of Forts Clinton and Montgomery\">capture Forts Clinton and Montgomery</a> on the Hudson River.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British forces <a href=\"https://wikipedia.org/wiki/Battle_of_Forts_Clinton_and_Montgomery\" title=\"Battle of Forts Clinton and Montgomery\">capture Forts Clinton and Montgomery</a> on the Hudson River.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Forts Clinton and Montgomery", "link": "https://wikipedia.org/wiki/Battle_of_Forts_<PERSON>_and_<PERSON>"}]}, {"year": "1789", "text": "French Revolution: King <PERSON> is forced to change his residence from Versailles to the Tuileries Palace.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_XVI_of_France\" class=\"mw-redirect\" title=\"<PERSON> XVI of France\"><PERSON></a> is forced to change his residence from <a href=\"https://wikipedia.org/wiki/Versailles\" class=\"mw-redirect\" title=\"Versailles\">Versailles</a> to the <a href=\"https://wikipedia.org/wiki/Tuileries_Palace\" title=\"Tuileries Palace\">Tuileries Palace</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_XVI_of_France\" class=\"mw-redirect\" title=\"<PERSON> XVI of France\"><PERSON></a> is forced to change his residence from <a href=\"https://wikipedia.org/wiki/Versailles\" class=\"mw-redirect\" title=\"Versailles\">Versailles</a> to the <a href=\"https://wikipedia.org/wiki/Tuileries_Palace\" title=\"Tuileries Palace\">Tuileries Palace</a>.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}, {"title": "Versailles", "link": "https://wikipedia.org/wiki/Versailles"}, {"title": "Tuileries Palace", "link": "https://wikipedia.org/wiki/Tuileries_Palace"}]}, {"year": "1810", "text": "A large fire destroys a third of all the buildings in the town of Raahe in the Grand Duchy of Finland.", "html": "1810 - A large fire destroys a third of all the buildings in the town of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>ah<PERSON>\"><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>.", "no_year_html": "A large fire destroys a third of all the buildings in the town of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>ah<PERSON>\"><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e"}, {"title": "Grand Duchy of Finland", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Finland"}]}, {"year": "1849", "text": "The execution of the 13 Martyrs of Arad after the Hungarian war of independence.", "html": "1849 - The execution of <a href=\"https://wikipedia.org/wiki/The_13_Martyrs_of_Arad\" title=\"The 13 Martyrs of Arad\">the 13 Martyrs of Arad</a> after the Hungarian war of independence.", "no_year_html": "The execution of <a href=\"https://wikipedia.org/wiki/The_13_Martyrs_of_Arad\" title=\"The 13 Martyrs of Arad\">the 13 Martyrs of Arad</a> after the Hungarian war of independence.", "links": [{"title": "The 13 Martyrs of Arad", "link": "https://wikipedia.org/wiki/The_13_Martyrs_of_Arad"}]}, {"year": "1854", "text": "In England the Great fire of Newcastle and Gateshead leads to 53 deaths and hundreds injured.", "html": "1854 - In England the <a href=\"https://wikipedia.org/wiki/Great_fire_of_Newcastle_and_Gateshead\" title=\"Great fire of Newcastle and Gateshead\">Great fire of Newcastle and Gateshead</a> leads to 53 deaths and hundreds injured.", "no_year_html": "In England the <a href=\"https://wikipedia.org/wiki/Great_fire_of_Newcastle_and_Gateshead\" title=\"Great fire of Newcastle and Gateshead\">Great fire of Newcastle and Gateshead</a> leads to 53 deaths and hundreds injured.", "links": [{"title": "Great fire of Newcastle and Gateshead", "link": "https://wikipedia.org/wiki/Great_fire_of_Newcastle_and_Gateshead"}]}, {"year": "1884", "text": "The Naval War College of the United States is founded in Rhode Island.", "html": "1884 - The <a href=\"https://wikipedia.org/wiki/Naval_War_College\" title=\"Naval War College\">Naval War College</a> of the United States is founded in <a href=\"https://wikipedia.org/wiki/Rhode_Island\" title=\"Rhode Island\">Rhode Island</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Naval_War_College\" title=\"Naval War College\">Naval War College</a> of the United States is founded in <a href=\"https://wikipedia.org/wiki/Rhode_Island\" title=\"Rhode Island\">Rhode Island</a>.", "links": [{"title": "Naval War College", "link": "https://wikipedia.org/wiki/Naval_War_College"}, {"title": "Rhode Island", "link": "https://wikipedia.org/wiki/Rhode_Island"}]}, {"year": "1898", "text": "Phi Mu Alpha Sinfonia, the largest American music fraternity, is founded at the New England Conservatory of Music.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Phi_Mu_Alpha_Sinfonia\" title=\"Phi Mu Alpha Sinfonia\">Phi Mu Alpha Sinfonia</a>, the largest American music fraternity, is founded at the <a href=\"https://wikipedia.org/wiki/New_England_Conservatory_of_Music\" title=\"New England Conservatory of Music\">New England Conservatory of Music</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Phi_Mu_Alpha_Sinfonia\" title=\"Phi Mu Alpha Sinfonia\">Phi Mu Alpha Sinfonia</a>, the largest American music fraternity, is founded at the <a href=\"https://wikipedia.org/wiki/New_England_Conservatory_of_Music\" title=\"New England Conservatory of Music\">New England Conservatory of Music</a>.", "links": [{"title": "Phi Mu Alpha Sinfonia", "link": "https://wikipedia.org/wiki/Phi_Mu_Alpha_Sinfonia"}, {"title": "New England Conservatory of Music", "link": "https://wikipedia.org/wiki/New_England_Conservatory_of_Music"}]}, {"year": "1903", "text": "The High Court of Australia sits for the first time.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/High_Court_of_Australia\" title=\"High Court of Australia\">High Court of Australia</a> sits for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/High_Court_of_Australia\" title=\"High Court of Australia\">High Court of Australia</a> sits for the first time.", "links": [{"title": "High Court of Australia", "link": "https://wikipedia.org/wiki/High_Court_of_Australia"}]}, {"year": "1908", "text": "The Bosnian crisis erupts when Austria-Hungary formally annexes Bosnia and Herzegovina.", "html": "1908 - The <a href=\"https://wikipedia.org/wiki/Bosnian_crisis\" class=\"mw-redirect\" title=\"Bosnian crisis\">Bosnian crisis</a> erupts when <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> formally annexes <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_rule_in_Bosnia_and_Herzegovina\" title=\"Austro-Hungarian rule in Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bosnian_crisis\" class=\"mw-redirect\" title=\"Bosnian crisis\">Bosnian crisis</a> erupts when <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> formally annexes <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_rule_in_Bosnia_and_Herzegovina\" title=\"Austro-Hungarian rule in Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "links": [{"title": "Bosnian crisis", "link": "https://wikipedia.org/wiki/Bosnian_crisis"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}, {"title": "Austro-Hungarian rule in Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Austro-Hungarian_rule_in_Bosnia_and_Herzegovina"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON> is elected Prime Minister of Greece for the first of seven times.", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Eleftherios_Venizelos\" title=\"<PERSON>eftherios Venizelos\"><PERSON><PERSON><PERSON><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> for the first of seven times.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eleftherios_Venizelos\" title=\"Eleftherios Venizelos\"><PERSON><PERSON><PERSON><PERSON>eni<PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> for the first of seven times.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eleftherios_Venizelos"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1915", "text": "World War I: Combined Austro-Hungarian and German Central Powers, reinforced by the recently joined Bulgaria launched a new offensive against Serbia under command of <PERSON> .", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Combined Austro-Hungarian and German <a href=\"https://wikipedia.org/wiki/Central_Powers\" title=\"Central Powers\">Central Powers</a>, reinforced by the recently joined <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bulgaria\" class=\"mw-redirect\" title=\"Kingdom of Bulgaria\">Bulgaria</a> launched a new offensive against <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia\" title=\"Kingdom of Serbia\">Serbia</a> under command of <a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> .", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Combined Austro-Hungarian and German <a href=\"https://wikipedia.org/wiki/Central_Powers\" title=\"Central Powers\">Central Powers</a>, reinforced by the recently joined <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bulgaria\" class=\"mw-redirect\" title=\"Kingdom of Bulgaria\">Bulgaria</a> launched a new offensive against <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia\" title=\"Kingdom of Serbia\">Serbia</a> under command of <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"<PERSON>\"><PERSON></a> .", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Central Powers", "link": "https://wikipedia.org/wiki/Central_Powers"}, {"title": "Kingdom of Bulgaria", "link": "https://wikipedia.org/wiki/Kingdom_of_Bulgaria"}, {"title": "Kingdom of Serbia", "link": "https://wikipedia.org/wiki/Kingdom_of_Serbia"}, {"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "World War I: Entente forces land in Thessaloniki, to open the Macedonian front against the Central Powers.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Entente</a> forces land in <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a>, to open the <a href=\"https://wikipedia.org/wiki/Macedonian_front\" title=\"Macedonian front\">Macedonian front</a> against the Central Powers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Entente</a> forces land in <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a>, to open the <a href=\"https://wikipedia.org/wiki/Macedonian_front\" title=\"Macedonian front\">Macedonian front</a> against the Central Powers.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}, {"title": "Thessaloniki", "link": "https://wikipedia.org/wiki/Thessaloniki"}, {"title": "Macedonian front", "link": "https://wikipedia.org/wiki/Macedonian_front"}]}, {"year": "1920", "text": "Ukrainian War of Independence: The Starobilsk agreement is signed by representatives of the Ukrainian Soviet Socialist Republic and the Makhnovshchina.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Starobilsk_agreement\" title=\"Starobilsk agreement\">Starobilsk agreement</a> is signed by representatives of the <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian Soviet Socialist Republic</a> and the <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Starobilsk_agreement\" title=\"Starobilsk agreement\">Starobilsk agreement</a> is signed by representatives of the <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian Soviet Socialist Republic</a> and the <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a>.", "links": [{"title": "Ukrainian War of Independence", "link": "https://wikipedia.org/wiki/Ukrainian_War_of_Independence"}, {"title": "Starobilsk agreement", "link": "https://wikipedia.org/wiki/Starobilsk_agreement"}, {"title": "Ukrainian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic"}, {"title": "Makhnovshchina", "link": "https://wikipedia.org/wiki/Makhnovshchina"}]}, {"year": "1923", "text": "The Turkish National Movement enters Constantinople.", "html": "1923 - The <a href=\"https://wikipedia.org/wiki/Turkish_National_Movement\" title=\"Turkish National Movement\">Turkish National Movement</a> enters <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Turkish_National_Movement\" title=\"Turkish National Movement\">Turkish National Movement</a> enters <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>.", "links": [{"title": "Turkish National Movement", "link": "https://wikipedia.org/wiki/Turkish_National_Movement"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}]}, {"year": "1927", "text": "Opening of The Jazz Singer, the first prominent \"talkie\" movie.", "html": "1927 - Opening of <i><a href=\"https://wikipedia.org/wiki/The_Jazz_Singer\" title=\"The Jazz Singer\">The Jazz Singer</a></i>, the first prominent \"talkie\" movie.", "no_year_html": "Opening of <i><a href=\"https://wikipedia.org/wiki/The_Jazz_Singer\" title=\"The Jazz Singer\">The Jazz Singer</a></i>, the first prominent \"talkie\" movie.", "links": [{"title": "The Jazz Singer", "link": "https://wikipedia.org/wiki/The_Jazz_Singer"}]}, {"year": "1934", "text": "Revolution of 1934: The President of the autonomous government of Catalonia, <PERSON><PERSON>ís <PERSON>s, proclaims the Catalan State with the support of the Worker's Alliance.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Events_of_6_October\" title=\"Events of 6 October\">Revolution of 1934</a>: The <a href=\"https://wikipedia.org/wiki/President_of_the_Government_of_Catalonia\" title=\"President of the Government of Catalonia\">President</a> of the autonomous government of <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>, <a href=\"https://wikipedia.org/wiki/Llu%C3%ADs_Companys\" title=\"Lluís Companys\">Lluís Companys</a>, proclaims the <a href=\"https://wikipedia.org/wiki/Catalan_State_(1934)\" title=\"Catalan State (1934)\">Catalan State</a> with the support of the <a href=\"https://wikipedia.org/w/index.php?title=Worker%27s_Alliance&amp;action=edit&amp;redlink=1\" class=\"new\" title=\"Worker's Alliance (page does not exist)\">Worker's Alliance</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Events_of_6_October\" title=\"Events of 6 October\">Revolution of 1934</a>: The <a href=\"https://wikipedia.org/wiki/President_of_the_Government_of_Catalonia\" title=\"President of the Government of Catalonia\">President</a> of the autonomous government of <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>, <a href=\"https://wikipedia.org/wiki/Llu%C3%ADs_Companys\" title=\"Lluís Companys\">Lluís Companys</a>, proclaims the <a href=\"https://wikipedia.org/wiki/Catalan_State_(1934)\" title=\"Catalan State (1934)\">Catalan State</a> with the support of the <a href=\"https://wikipedia.org/w/index.php?title=Worker%27s_Alliance&amp;action=edit&amp;redlink=1\" class=\"new\" title=\"Worker's Alliance (page does not exist)\">Worker's Alliance</a>.", "links": [{"title": "Events of 6 October", "link": "https://wikipedia.org/wiki/Events_of_6_October"}, {"title": "President of the Government of Catalonia", "link": "https://wikipedia.org/wiki/President_of_the_Government_of_Catalonia"}, {"title": "Catalonia", "link": "https://wikipedia.org/wiki/Catalonia"}, {"title": "Lluís Companys", "link": "https://wikipedia.org/wiki/Llu%C3%ADs_Companys"}, {"title": "Catalan State (1934)", "link": "https://wikipedia.org/wiki/Catalan_State_(1934)"}, {"title": "Worker's Alliance (page does not exist)", "link": "https://wikipedia.org/w/index.php?title=Worker%27s_Alliance&action=edit&redlink=1"}]}, {"year": "1939", "text": "World War II: The Battle of Kock is the final combat of the September Campaign in Poland.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Kock_(1939)\" title=\"Battle of Kock (1939)\">Battle of Kock</a> is the final combat of the <a href=\"https://wikipedia.org/wiki/Invasion_of_Poland\" title=\"Invasion of Poland\">September Campaign</a> in Poland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Kock_(1939)\" title=\"Battle of Kock (1939)\">Battle of Kock</a> is the final combat of the <a href=\"https://wikipedia.org/wiki/Invasion_of_Poland\" title=\"Invasion of Poland\">September Campaign</a> in Poland.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Kock (1939)", "link": "https://wikipedia.org/wiki/Battle_of_Kock_(1939)"}, {"title": "Invasion of Poland", "link": "https://wikipedia.org/wiki/Invasion_of_Poland"}]}, {"year": "1942", "text": "World War II: American troops force the Japanese from their positions east of the Matanikau River during the Battle of Guadalcanal.", "html": "1942 - World War II: American troops <a href=\"https://wikipedia.org/wiki/Actions_along_the_Matanikau\" title=\"Actions along the Matanikau\">force the Japanese from their positions</a> east of the Matanikau River during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_campaign\" title=\"Guadalcanal campaign\">Battle of Guadalcanal</a>.", "no_year_html": "World War II: American troops <a href=\"https://wikipedia.org/wiki/Actions_along_the_Matanikau\" title=\"Actions along the Matanikau\">force the Japanese from their positions</a> east of the Matanikau River during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_campaign\" title=\"Guadalcanal campaign\">Battle of Guadalcanal</a>.", "links": [{"title": "Actions along the Matanikau", "link": "https://wikipedia.org/wiki/Actions_along_the_Matanikau"}, {"title": "Guadalcanal campaign", "link": "https://wikipedia.org/wiki/Guadalcanal_campaign"}]}, {"year": "1943", "text": "World War II: Thirteen civilians are burnt alive by a paramilitary group in Crete during the Nazi occupation of Greece.", "html": "1943 - World War II: Thirteen civilians are <a href=\"https://wikipedia.org/wiki/Burnings_of_Kali_Sykia\" title=\"Burnings of Kali Sykia\">burnt alive</a> by a paramilitary group in Crete during the <a href=\"https://wikipedia.org/wiki/Axis_occupation_of_Greece\" title=\"Axis occupation of Greece\">Nazi occupation of Greece</a>.", "no_year_html": "World War II: Thirteen civilians are <a href=\"https://wikipedia.org/wiki/Burnings_of_Kali_Sykia\" title=\"Burnings of Kali Sykia\">burnt alive</a> by a paramilitary group in Crete during the <a href=\"https://wikipedia.org/wiki/Axis_occupation_of_Greece\" title=\"Axis occupation of Greece\">Nazi occupation of Greece</a>.", "links": [{"title": "Burnings of Kali Sykia", "link": "https://wikipedia.org/wiki/Burnings_of_Kali_Sykia"}, {"title": "Axis occupation of Greece", "link": "https://wikipedia.org/wiki/Axis_occupation_of_Greece"}]}, {"year": "1944", "text": "World War II: Units of the 1st Czechoslovak Army Corps enter Czechoslovakia during the Battle of the Dukla Pass.", "html": "1944 - World War II: Units of the <a href=\"https://wikipedia.org/wiki/1st_Czechoslovak_Army_Corps_in_the_Soviet_Union\" title=\"1st Czechoslovak Army Corps in the Soviet Union\">1st Czechoslovak Army Corps</a> enter <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> during the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Dukla_Pass\" title=\"Battle of the Dukla Pass\">Battle of the Dukla Pass</a>.", "no_year_html": "World War II: Units of the <a href=\"https://wikipedia.org/wiki/1st_Czechoslovak_Army_Corps_in_the_Soviet_Union\" title=\"1st Czechoslovak Army Corps in the Soviet Union\">1st Czechoslovak Army Corps</a> enter <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> during the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Dukla_Pass\" title=\"Battle of the Dukla Pass\">Battle of the Dukla Pass</a>.", "links": [{"title": "1st Czechoslovak Army Corps in the Soviet Union", "link": "https://wikipedia.org/wiki/1st_Czechoslovak_Army_Corps_in_the_Soviet_Union"}, {"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "Battle of the Dukla Pass", "link": "https://wikipedia.org/wiki/Battle_of_the_Dukla_Pass"}]}, {"year": "1973", "text": "Egypt and Syria launch coordinated attacks against Israel, beginning the Yom Kippur War.", "html": "1973 - Egypt and Syria launch coordinated <a href=\"https://wikipedia.org/wiki/Operation_Badr_(1973)\" title=\"Operation Badr (1973)\">attacks</a> against Israel, beginning the <a href=\"https://wikipedia.org/wiki/Yom_Kippur_War\" title=\"Yom Kippur War\">Yom Kippur War</a>.", "no_year_html": "Egypt and Syria launch coordinated <a href=\"https://wikipedia.org/wiki/Operation_Badr_(1973)\" title=\"Operation Badr (1973)\">attacks</a> against Israel, beginning the <a href=\"https://wikipedia.org/wiki/Yom_Kippur_War\" title=\"Yom Kippur War\">Yom Kippur War</a>.", "links": [{"title": "Operation Badr (1973)", "link": "https://wikipedia.org/wiki/Operation_Badr_(1973)"}, {"title": "Yom Kippur War", "link": "https://wikipedia.org/wiki/Yom_Kippur_War"}]}, {"year": "1976", "text": "Cubana de Aviación Flight 455 is destroyed by two bombs, placed on board by an anti-Castro militant group.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_455\" title=\"Cubana de Aviación Flight 455\">Cubana de Aviación Flight 455</a> is destroyed by two bombs, placed on board by an anti-Castro militant group.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_455\" title=\"Cubana de Aviación Flight 455\">Cubana de Aviación Flight 455</a> is destroyed by two bombs, placed on board by an anti-Castro militant group.", "links": [{"title": "Cubana de Aviación Flight 455", "link": "https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_455"}]}, {"year": "1976", "text": "Premier <PERSON><PERSON> arrests the Gang of Four, ending the Cultural Revolution in China.", "html": "1976 - Premier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> arrests the <a href=\"https://wikipedia.org/wiki/Gang_of_Four\" title=\"Gang of Four\">Gang of Four</a>, ending the <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a> in China.", "no_year_html": "Premier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> arrests the <a href=\"https://wikipedia.org/wiki/Gang_of_Four\" title=\"Gang of Four\">Gang of Four</a>, ending the <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a> in China.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Gang of Four", "link": "https://wikipedia.org/wiki/Gang_of_Four"}, {"title": "Cultural Revolution", "link": "https://wikipedia.org/wiki/Cultural_Revolution"}]}, {"year": "1976", "text": "Dozens are killed by Thai police and right-wing paramilitaries in the Thammasat University massacre; afterwards, the <PERSON>i Pramoj government is toppled in a military coup led by <PERSON><PERSON>.", "html": "1976 - Dozens are killed by Thai police and right-wing paramilitaries in the <a href=\"https://wikipedia.org/wiki/6_October_1976_massacre\" title=\"6 October 1976 massacre\">Thammasat University massacre</a>; afterwards, the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ramoj\" title=\"<PERSON><PERSON> Pramoj\"><PERSON><PERSON>j</a> government is toppled in a military coup led by <a href=\"https://wikipedia.org/wiki/Sang<PERSON>_<PERSON>loryu\" title=\"<PERSON><PERSON>loryu\"><PERSON><PERSON></a>.", "no_year_html": "Dozens are killed by Thai police and right-wing paramilitaries in the <a href=\"https://wikipedia.org/wiki/6_October_1976_massacre\" title=\"6 October 1976 massacre\">Thammasat University massacre</a>; afterwards, the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oj\" title=\"<PERSON><PERSON>oj\"><PERSON><PERSON></a> government is toppled in a military coup led by <a href=\"https://wikipedia.org/wiki/Sang<PERSON>_<PERSON>loryu\" title=\"<PERSON><PERSON>loryu\"><PERSON><PERSON></a>.", "links": [{"title": "6 October 1976 massacre", "link": "https://wikipedia.org/wiki/6_October_1976_massacre"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Seni_Pramoj"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>yu"}]}, {"year": "1977", "text": "The first prototype of the Mikoyan MiG-29, designated 9-01, makes its maiden flight.", "html": "1977 - The first prototype of the <a href=\"https://wikipedia.org/wiki/Mikoyan_MiG-29\" title=\"Mikoyan MiG-29\">Mikoyan MiG-29</a>, designated 9-01, makes its maiden flight.", "no_year_html": "The first prototype of the <a href=\"https://wikipedia.org/wiki/Mikoyan_MiG-29\" title=\"Mikoyan MiG-29\">Mikoyan MiG-29</a>, designated 9-01, makes its maiden flight.", "links": [{"title": "Mikoyan MiG-29", "link": "https://wikipedia.org/wiki/Mikoyan_MiG-29"}]}, {"year": "1979", "text": "<PERSON> <PERSON> becomes the first pontiff to visit the White House.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> becomes the first pontiff to visit the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> becomes the first pontiff to visit the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}]}, {"year": "1981", "text": "Egyptian President <PERSON><PERSON> is murdered by Islamic extremists.", "html": "1981 - Egyptian President <a href=\"https://wikipedia.org/wiki/Anwar_Sadat\" title=\"Anwar Sadat\"><PERSON><PERSON> Sadat</a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_Anwar_Sadat\" title=\"Assassination of Anwar Sadat\">murdered</a> by Islamic extremists.", "no_year_html": "Egyptian President <a href=\"https://wikipedia.org/wiki/Anwar_Sadat\" title=\"Anwar Sadat\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_Anwar_Sadat\" title=\"Assassination of Anwar Sadat\">murdered</a> by Islamic extremists.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>at"}, {"title": "Assassination of Anwar Sadat", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_Sadat"}]}, {"year": "1981", "text": "NLM CityHopper Flight 431 crashes in Moerdijk after taking off from Rotterdam The Hague Airport in the Netherlands, killing all 17 people on board.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/NLM_CityHopper_Flight_431\" title=\"NLM CityHopper Flight 431\">NLM CityHopper Flight 431</a> crashes in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> after taking off from <a href=\"https://wikipedia.org/wiki/Rotterdam_The_Hague_Airport\" title=\"Rotterdam The Hague Airport\">Rotterdam The Hague Airport</a> in the Netherlands, killing all 17 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NLM_CityHopper_Flight_431\" title=\"NLM CityHopper Flight 431\">NLM CityHopper Flight 431</a> crashes in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>jk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> after taking off from <a href=\"https://wikipedia.org/wiki/Rotterdam_The_Hague_Airport\" title=\"Rotterdam The Hague Airport\">Rotterdam The Hague Airport</a> in the Netherlands, killing all 17 people on board.", "links": [{"title": "NLM CityHopper Flight 431", "link": "https://wikipedia.org/wiki/NLM_CityHopper_Flight_431"}, {"title": "Moerdijk", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Rotterdam The Hague Airport", "link": "https://wikipedia.org/wiki/Rotterdam_The_Hague_Airport"}]}, {"year": "1985", "text": "Police constable <PERSON> is murdered as riots erupt in the Broadwater Farm suburb of London.", "html": "1985 - Police constable <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>\" title=\"Murder of <PERSON>\"><PERSON> is murdered</a> as riots erupt in the Broadwater Farm suburb of London.", "no_year_html": "Police constable <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>\" title=\"Murder of <PERSON>\"><PERSON> is murdered</a> as riots erupt in the Broadwater Farm suburb of London.", "links": [{"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "Fiji becomes a republic.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Fiji\" title=\"Fiji\">Fiji</a> becomes a republic.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fiji\" title=\"Fiji\">Fiji</a> becomes a republic.", "links": [{"title": "Fiji", "link": "https://wikipedia.org/wiki/Fiji"}]}, {"year": "1990", "text": "Space Shuttle Discovery is launched on STS-41, and deploys the Ulysses space probe to study the Sun's polar regions.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-41\" title=\"STS-41\">STS-41</a>, and deploys the <a href=\"https://wikipedia.org/wiki/Ulysses_(spacecraft)\" title=\"Ulysses (spacecraft)\"><i>Ulysses</i></a> space probe to study the <a href=\"https://wikipedia.org/wiki/Sun\" title=\"Sun\">Sun</a>'s polar regions.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-41\" title=\"STS-41\">STS-41</a>, and deploys the <a href=\"https://wikipedia.org/wiki/Ulysses_(spacecraft)\" title=\"Ulysses (spacecraft)\"><i>Ulysses</i></a> space probe to study the <a href=\"https://wikipedia.org/wiki/Sun\" title=\"Sun\">Sun</a>'s polar regions.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-41", "link": "https://wikipedia.org/wiki/STS-41"}, {"title": "<PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON>_(spacecraft)"}, {"title": "Sun", "link": "https://wikipedia.org/wiki/Sun"}]}, {"year": "1995", "text": "The first planet orbiting another sun, 51 <PERSON>egasi b, is discovered.", "html": "1995 - The first planet orbiting another sun, <a href=\"https://wikipedia.org/wiki/51_Pegasi_b\" title=\"51 Pegasi b\">51 Pegasi b</a>, is discovered.", "no_year_html": "The first planet orbiting another sun, <a href=\"https://wikipedia.org/wiki/51_Pegasi_b\" title=\"51 Pegasi b\">51 Pegasi b</a>, is discovered.", "links": [{"title": "51 <PERSON><PERSON><PERSON> b", "link": "https://wikipedia.org/wiki/51_Pegasi_b"}]}, {"year": "2007", "text": "<PERSON> completes the first human-powered circumnavigation of the Earth.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(adventurer)\" title=\"<PERSON> (adventurer)\"><PERSON></a> completes the first human-powered circumnavigation of the Earth.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(adventurer)\" title=\"<PERSON> (adventurer)\"><PERSON></a> completes the first human-powered circumnavigation of the Earth.", "links": [{"title": "<PERSON> (adventurer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(adventurer)"}]}, {"year": "2010", "text": "Instagram, a mainstream photo-sharing application, is founded.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Instagram\" title=\"Instagram\">Instagram</a>, a mainstream photo-sharing application, is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Instagram\" title=\"Instagram\">Instagram</a>, a mainstream photo-sharing application, is founded.", "links": [{"title": "Instagram", "link": "https://wikipedia.org/wiki/Instagram"}]}, {"year": "2018", "text": "The United States Senate confirms <PERSON> as a Supreme Court Associate Justice, ending a contentious confirmation process.", "html": "2018 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> confirms <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as a <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Supreme Court Associate Justice</a>, ending a <a href=\"https://wikipedia.org/wiki/<PERSON>_Supreme_Court_nomination\" title=\"<PERSON> Supreme Court nomination\">contentious confirmation process</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> confirms <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as a <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Supreme Court Associate Justice</a>, ending a <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Supreme_Court_nomination\" title=\"<PERSON> Supreme Court nomination\">contentious confirmation process</a>.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Associate Justice of the Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States"}, {"title": "<PERSON> Supreme Court nomination", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Supreme_Court_nomination"}]}, {"year": "2022", "text": "<PERSON> is awarded the Nobel Prize in Literature. ", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/2022_Nobel_Prize_in_Literature\" title=\"2022 Nobel Prize in Literature\">awarded</a> the <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize in Literature</a>. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/2022_Nobel_Prize_in_Literature\" title=\"2022 Nobel Prize in Literature\">awarded</a> the <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize in Literature</a>. ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2022 Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/2022_Nobel_Prize_in_Literature"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}], "Births": [{"year": "649", "text": "Yuk<PERSON><PERSON> Yichʼ<PERSON> (d. around 696)", "html": "649 - <a href=\"https://wikipedia.org/wiki/Yuknoom_Yich%CA%BCaak_K%CA%BCahk%CA%BC\" title=\"Yuknoom Yichʼaak Kʼahkʼ\">Yuknoom Yichʼaak Kʼahkʼ</a> (d. around 696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yuknoom_Yich%CA%BCaak_K%CA%BCahk%CA%BC\" title=\"Yuknoom Yichʼaak Kʼahkʼ\">Yuknoom Yichʼaak Kʼahkʼ</a> (d. around 696)", "links": [{"title": "Yuknoom Yichʼaak Kʼahkʼ", "link": "https://wikipedia.org/wiki/Yuknoom_Yich%CA%BCaak_K%CA%BCahk%CA%BC"}]}, {"year": "1289", "text": "<PERSON><PERSON><PERSON> of Bohemia (d. 1306)", "html": "1289 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Bohemia\" title=\"<PERSON><PERSON><PERSON> III of Bohemia\"><PERSON><PERSON><PERSON> of Bohemia</a> (d. 1306)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Bohemia\" title=\"<PERSON><PERSON><PERSON> III of Bohemia\"><PERSON><PERSON><PERSON> of Bohemia</a> (d. 1306)", "links": [{"title": "<PERSON><PERSON><PERSON> III of Bohemia", "link": "https://wikipedia.org/wiki/Wen<PERSON>laus_III_of_Bohemia"}]}, {"year": "1459", "text": "<PERSON>, German navigator and geographer (d. 1507)", "html": "1459 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German navigator and geographer (d. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German navigator and geographer (d. 1507)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1510", "text": "<PERSON>, English physician and academic, co-founded the Gonville and Caius College (d. 1573)", "html": "1510 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic, co-founded the <a href=\"https://wikipedia.org/wiki/Gonville_and_Caius_College,_Cambridge\" title=\"Gonville and Caius College, Cambridge\">Gonville and Caius College</a> (d. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic, co-founded the <a href=\"https://wikipedia.org/wiki/Gonville_and_Caius_College,_Cambridge\" title=\"Gonville and Caius College, Cambridge\">Gonville and Caius College</a> (d. 1573)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gonville and Caius College, Cambridge", "link": "https://wikipedia.org/wiki/Gonville_and_Caius_College,_Cambridge"}]}, {"year": "1510", "text": "<PERSON>, English priest and martyr (d. 1555)", "html": "1510 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and martyr (d. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and martyr (d. 1555)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1552", "text": "<PERSON>, Italian priest and missionary (d. 1610)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and missionary (d. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and missionary (d. 1610)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1555", "text": "<PERSON><PERSON><PERSON>, Hungarian noble (d. 1604)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/Ferenc_N%C3%A1dasdy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian noble (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferenc_N%C3%A1dasdy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian noble (d. 1604)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_N%C3%A1dasdy"}]}, {"year": "1565", "text": "<PERSON>, French writer (d. 1645)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French writer (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French writer (d. 1645)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1573", "text": "<PERSON>, 3rd Earl of Southampton, English politician, Lord Lieutenant of Hampshire (d. 1624)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Southampton\" title=\"<PERSON>, 3rd Earl of Southampton\"><PERSON>, 3rd Earl of Southampton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Hampshire\" title=\"Lord Lieutenant of Hampshire\">Lord Lieutenant of Hampshire</a> (d. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Southampton\" title=\"<PERSON>, 3rd Earl of Southampton\"><PERSON>, 3rd Earl of Southampton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Hampshire\" title=\"Lord Lieutenant of Hampshire\">Lord Lieutenant of Hampshire</a> (d. 1624)", "links": [{"title": "<PERSON>, 3rd Earl of Southampton", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Southampton"}, {"title": "Lord Lieutenant of Hampshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Hampshire"}]}, {"year": "1576", "text": "<PERSON>, 5th Earl of Rutland (d. 1612)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Rutland\" title=\"<PERSON>, 5th Earl of Rutland\"><PERSON>, 5th Earl of Rutland</a> (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Rutland\" title=\"<PERSON>, 5th Earl of Rutland\"><PERSON>, 5th Earl of Rutland</a> (d. 1612)", "links": [{"title": "<PERSON>, 5th Earl of Rutland", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Rutland"}]}, {"year": "1591", "text": "<PERSON><PERSON><PERSON>, Italian singer-songwriter (d. 1638)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Caccini\" title=\"<PERSON><PERSON><PERSON> Caccini\"><PERSON><PERSON><PERSON></a>, Italian singer-songwriter (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Caccini\" title=\"<PERSON>ti<PERSON> Caccini\"><PERSON><PERSON><PERSON></a>, Italian singer-songwriter (d. 1638)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ti<PERSON>_Caccini"}]}, {"year": "1610", "text": "<PERSON>, duc <PERSON>, French general (d. 1690)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc de <PERSON></a>, French general (d. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc <PERSON></a>, French general (d. 1690)", "links": [{"title": "<PERSON>, duc de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>"}]}, {"year": "1626", "text": "<PERSON><PERSON><PERSON>, French historian, philosopher and lawyer (d. 1684)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>_Cordemoy\" title=\"<PERSON><PERSON><PERSON> Cordemoy\"><PERSON><PERSON><PERSON></a>, French historian, philosopher and lawyer (d. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>_Cordemoy\" title=\"<PERSON><PERSON><PERSON> Cordemoy\"><PERSON><PERSON><PERSON></a>, French historian, philosopher and lawyer (d. 1684)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9ra<PERSON>_<PERSON>_<PERSON>rdemoy"}]}, {"year": "1716", "text": "<PERSON>, 2nd Earl of Halifax, English general and politician, Lord Lieutenant of Ireland (d. 1771)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Halifax\" title=\"<PERSON>, 2nd Earl of Halifax\"><PERSON>, 2nd Earl of Halifax</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Halifax\" title=\"<PERSON>, 2nd Earl of Halifax\"><PERSON>, 2nd Earl of Halifax</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1771)", "links": [{"title": "<PERSON>, 2nd Earl of Halifax", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Halifax"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1729", "text": "<PERSON>, English preacher, the first female Methodist preacher (d. 1804)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English preacher, the first female <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist</a> preacher (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English preacher, the first female <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist</a> preacher (d. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Methodism", "link": "https://wikipedia.org/wiki/Methodism"}]}, {"year": "1732", "text": "<PERSON>, Scottish businessman, co-founded John <PERSON> and Sons (d. 1812)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_Sons\" class=\"mw-redirect\" title=\"<PERSON> and Sons\"><PERSON> and Sons</a> (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_Sons\" class=\"mw-redirect\" title=\"<PERSON> and Sons\"><PERSON> and Sons</a> (d. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON> and Sons", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_Sons"}]}, {"year": "1738", "text": "Archduchess <PERSON> of Austria (d. 1789)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria_(1738%E2%80%931789)\" class=\"mw-redirect\" title=\"Archduchess <PERSON> of Austria (1738-1789)\">Archduchess <PERSON> of Austria</a> (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria_(1738%E2%80%931789)\" class=\"mw-redirect\" title=\"Archduchess <PERSON> of Austria (1738-1789)\">Archduchess <PERSON> of Austria</a> (d. 1789)", "links": [{"title": "Archduchess <PERSON> of Austria (1738-1789)", "link": "https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria_(1738%E2%80%931789)"}]}, {"year": "1742", "text": "<PERSON>, Norwegian-Danish poet and playwright (d. 1785)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Danish poet and playwright (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Danish poet and playwright (d. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON>, Scottish-Canadian businessman and philanthropist, founded McGill University (d. 1813)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/McGill_University\" title=\"McGill University\">McGill University</a> (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/McGill_University\" title=\"McGill University\">McGill University</a> (d. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McGill University", "link": "https://wikipedia.org/wiki/McGill_University"}]}, {"year": "1767", "text": "<PERSON>, Grenadian-Haitian king (d. 1820)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grenadian-Haitian king (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grenadian-Haitian king (d. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1769", "text": "<PERSON>, English general and politician, Lieutenant Governor of Upper Canada (d. 1812)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Upper_Canada\" class=\"mw-redirect\" title=\"Lieutenant Governor of Upper Canada\">Lieutenant Governor of Upper Canada</a> (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Upper_Canada\" class=\"mw-redirect\" title=\"Lieutenant Governor of Upper Canada\">Lieutenant Governor of Upper Canada</a> (d. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Lieutenant Governor of Upper Canada", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Upper_Canada"}]}, {"year": "1773", "text": "<PERSON>, Scottish geologist and academic (d. 1835)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish geologist and academic (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish geologist and academic (d. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1773", "text": "<PERSON> France (d. 1850)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of France (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of France (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON><PERSON><PERSON>, French politician (d. 1888)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/Hippolyte_Carnot\" title=\"Hippolyte Carnot\"><PERSON><PERSON><PERSON></a>, French politician (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hippolyte_Carnot\" title=\"Hippolyte Carnot\"><PERSON><PERSON><PERSON></a>, French politician (d. 1888)", "links": [{"title": "Hippol<PERSON>", "link": "https://wikipedia.org/wiki/Hippolyte_Carnot"}]}, {"year": "1803", "text": "<PERSON>, Polish-German physicist and meteorologist (d. 1879)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German physicist and meteorologist (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German physicist and meteorologist (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, 3rd Earl of Charlemont, Irish politician, Lord Lieutenant of Armagh (d. 1892)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Charlemont\" title=\"<PERSON>, 3rd Earl of Charlemont\"><PERSON>, 3rd Earl of Charlemont</a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Armagh\" title=\"Lord Lieutenant of Armagh\">Lord Lieutenant of Armagh</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Charlemont\" title=\"<PERSON>, 3rd Earl of Charlemont\"><PERSON>, 3rd Earl of Charlemont</a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Armagh\" title=\"Lord Lieutenant of Armagh\">Lord Lieutenant of Armagh</a> (d. 1892)", "links": [{"title": "<PERSON>, 3rd Earl of Charlemont", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Charlemont"}, {"title": "Lord Lieutenant of Armagh", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Armagh"}]}, {"year": "1820", "text": "<PERSON>, Swedish soprano and actress (d. 1887)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish soprano and actress (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish soprano and actress (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, German mathematician and philosopher (d. 1916)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and philosopher (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and philosopher (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, Italian soldier, poet, and author (d. 1910)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier, poet, and author (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier, poet, and author (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, American engineer and businessman, founded the Westinghouse Air Brake Company (d. 1914)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Westinghouse_Air_Brake_Company\" title=\"Westinghouse Air Brake Company\">Westinghouse Air Brake Company</a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Westinghouse_Air_Brake_Company\" title=\"Westinghouse Air Brake Company\">Westinghouse Air Brake Company</a> (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}, {"title": "Westinghouse Air Brake Company", "link": "https://wikipedia.org/wiki/Westinghouse_Air_Brake_Company"}]}, {"year": "1862", "text": "<PERSON>, American historian and politician (d. 1927)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Canadian engineer and academic, invented radiotelephony (d. 1932)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and academic, invented <a href=\"https://wikipedia.org/wiki/Radiotelephony\" class=\"mw-redirect\" title=\"Radiotelephony\">radiotelephony</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and academic, invented <a href=\"https://wikipedia.org/wiki/Radiotelephony\" class=\"mw-redirect\" title=\"Radiotelephony\">radiotelephony</a> (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Radiotelephony", "link": "https://wikipedia.org/wiki/Radiotelephony"}]}, {"year": "1874", "text": "<PERSON>, American merchant and politician, 51st Governor of Massachusetts (d. 1950)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 51st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 51st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1876", "text": "<PERSON>, Canadian lawyer and politician, 18th Canadian Minister of Justice (d. 1941)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Justice (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Canada)"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Polish pianist and composer (d. 1937)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish pianist and composer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish pianist and composer (d. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Swiss pianist and conductor (d. 1960)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pianist and conductor (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pianist and conductor (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Swiss-French architect and painter, designed the Philips Pavilion and Saint-Pierre, Firminy (d. 1965)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Le_Corbusier\" title=\"Le Corbusier\"><PERSON></a>, Swiss-French architect and painter, designed the <a href=\"https://wikipedia.org/wiki/Philips_Pavilion\" title=\"Philips Pavilion\">Philips Pavilion</a> and <a href=\"https://wikipedia.org/wiki/Saint-Pierre,_Firminy\" title=\"Saint-Pierre, Firminy\">Saint-Pierre, Firminy</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le_Corbusier\" title=\"Le Corbusier\"><PERSON></a>, Swiss-French architect and painter, designed the <a href=\"https://wikipedia.org/wiki/Philips_Pavilion\" title=\"Philips Pavilion\">Philips Pavilion</a> and <a href=\"https://wikipedia.org/wiki/Saint-Pierre,_Firminy\" title=\"Saint-Pierre, Firminy\">Saint-Pierre, Firminy</a> (d. 1965)", "links": [{"title": "Le Corbusier", "link": "https://wikipedia.org/wiki/Le_Corbusier"}, {"title": "Philips Pavilion", "link": "https://wikipedia.org/wiki/Philips_Pavilion"}, {"title": "Saint-Pierre, Firminy", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>,_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1888", "text": "<PERSON>, French soldier and pilot (d. 1918)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aviator)\" title=\"<PERSON> (aviator)\"><PERSON></a>, French soldier and pilot (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aviator)\" title=\"<PERSON> (aviator)\"><PERSON></a>, French soldier and pilot (d. 1918)", "links": [{"title": "<PERSON> (aviator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aviator)"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Estonian poet and educator (d. 1946)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet and educator (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet and educator (d. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Indian astrophysicist, astronomer, and academic (d. 1956)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian astrophysicist, astronomer, and academic (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian astrophysicist, astronomer, and academic (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American author and critic (d. 1981)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American film director (d. 1941)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American film director (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American film director (d. 1941)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>(director)"}]}, {"year": "1897", "text": "<PERSON> <PERSON>, American biochemist and academic (d. 1991)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Florence_B<PERSON>_<PERSON>\" title=\"Florence B. Seibert\">Florence <PERSON><PERSON></a>, American biochemist and academic (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_B._<PERSON>\" title=\"Florence B. Seibert\">Florence <PERSON><PERSON></a>, American biochemist and academic (d. 1991)", "links": [{"title": "Florence B<PERSON>", "link": "https://wikipedia.org/wiki/Florence_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, American activist and desegregationist (d. 1991)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>iv<PERSON>_<PERSON>\" title=\"<PERSON>iv<PERSON>\"><PERSON><PERSON><PERSON></a>, American activist and desegregationist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>iv<PERSON>_<PERSON>\" title=\"<PERSON>iv<PERSON>\"><PERSON><PERSON><PERSON></a>, American activist and desegregationist (d. 1991)", "links": [{"title": "Viv<PERSON>", "link": "https://wikipedia.org/wiki/Vivion_Brewer"}]}, {"year": "1900", "text": "<PERSON>, German mountaineer (d. 1934)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mountaineer (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mountaineer (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, English cricketer (d. 1961)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>-<PERSON><PERSON>, German-Brazilian zoologist and academic (d. 1990)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Brazilian zoologist and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Brazilian zoologist and academic (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Irish physicist and academic, Nobel Prize laureate (d. 1995)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1905", "text": "<PERSON>, American tennis player and painter (d. 1998)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and painter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and painter (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American actress (d. 1984)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Welsh footballer and coach (d. 1946)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27C<PERSON>agh<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh footballer and coach (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27C<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh footballer and coach (d. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ta<PERSON>_O%27<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American actress (d. 1942)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Russian mathematician and academic (d. 1989)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English journalist and politician, First Secretary of State (d. 2002)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Barbara_Castle\" title=\"Barbara Castle\">Barbara Castle</a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/First_Secretary_of_State\" title=\"First Secretary of State\">First Secretary of State</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barbara_Castle\" title=\"Barbara Castle\">Barbara Castle</a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/First_Secretary_of_State\" title=\"First Secretary of State\">First Secretary of State</a> (d. 2002)", "links": [{"title": "Barbara Castle", "link": "https://wikipedia.org/wiki/Barbara_Castle"}, {"title": "First Secretary of State", "link": "https://wikipedia.org/wiki/First_Secretary_of_State"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Italian automobile designer (d. 1974)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Puliga\" title=\"<PERSON><PERSON><PERSON> Puli<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian automobile designer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Puliga\" title=\"<PERSON><PERSON>o <PERSON> Puliga\"><PERSON><PERSON><PERSON></a>, Italian automobile designer (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>uliga"}]}, {"year": "1912", "text": "<PERSON>, American lawyer and politician (d. 2011)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bass\"><PERSON></a>, American lawyer and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bass\"><PERSON></a>, American lawyer and politician (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, German-Swiss painter and photographer (d. 1985)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/M%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Swiss painter and photographer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Swiss painter and photographer (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A9ret_Oppenheim"}]}, {"year": "1914", "text": "<PERSON>, Norwegian ethnographer and explorer (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian ethnographer and explorer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian ethnographer and explorer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English director and playwright (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and playwright (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and playwright (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American psychologist and activist (d. 2007)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, American psychologist and activist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, American psychologist and activist (d. 2007)", "links": [{"title": "<PERSON> (psychologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychologist)"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Portuguese-American cardinal (d. 1983)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sousa_Medeiros\" title=\"<PERSON><PERSON><PERSON> Sousa Medeiros\"><PERSON><PERSON><PERSON></a>, Portuguese-American cardinal (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sousa_Medeiros\" title=\"<PERSON><PERSON><PERSON> Sousa Medeiros\"><PERSON><PERSON><PERSON> Medeiro<PERSON></a>, Portuguese-American cardinal (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON> Medeiro<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sousa_Medeiros"}]}, {"year": "1915", "text": "<PERSON>, Swedish dentist and actress (d. 2007)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish dentist and actress (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish dentist and actress (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Japanese-Chinese general (d. 1997)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-Chinese general (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-Chinese general (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American activist and philanthropist (d. 1977)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist and philanthropist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist and philanthropist (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Singaporean soldier and politician, 2nd Deputy Prime Minister of Singapore (d. 2010)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean soldier and politician, 2nd <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Singapore\" title=\"Deputy Prime Minister of Singapore\">Deputy Prime Minister of Singapore</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean soldier and politician, 2nd <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Singapore\" title=\"Deputy Prime Minister of Singapore\">Deputy Prime Minister of Singapore</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>wee"}, {"title": "Deputy Prime Minister of Singapore", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Singapore"}]}, {"year": "1918", "text": "<PERSON>, French-Belgian race car driver (d. 1993)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Belgian race car driver (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Belgian race car driver (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Pilette"}]}, {"year": "1919", "text": "<PERSON>, English footballer and coach (d. 1996)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, <PERSON> of Lymington, English lawyer and judge (d. 2005)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Lymington\" title=\"<PERSON>, <PERSON> of Lymington\"><PERSON>, Baron <PERSON> of Lymington</a>, English lawyer and judge (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Lymington\" title=\"<PERSON>, Baron <PERSON> of Lymington\"><PERSON>, Baron <PERSON> of Lymington</a>, English lawyer and judge (d. 2005)", "links": [{"title": "<PERSON>, <PERSON> of Lymington", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Lymington"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian-Russian mathematician and theorist (d. 1997)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>\" title=\"<PERSON>vgen<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian-Russian mathematician and theorist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>\" title=\"<PERSON>vgen<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian-Russian mathematician and theorist (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evgenii_Landis"}]}, {"year": "1921", "text": "<PERSON>, American minister and activist (d. 2020)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Italian automotive designer (d. 1980)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian automotive designer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian automotive designer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American baseball player and manager (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 2011)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1922", "text": "<PERSON><PERSON>, American actress (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Malaysian Chinese business magnate and investor", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian Chinese business magnate and investor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian Chinese business magnate and investor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and author (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Ya%C5%9Far_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya%C5%9Far_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and author (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya%C5%9Far_<PERSON>mal"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American journalist and author (d. 2005)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American sportscaster (d. 2005)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Scottish Gaelic singer (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish Gaelic singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish Gaelic singer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actress and singer (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American pole vaulter (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Syrian general and politician, 20th President of Syria (d. 2000)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Syrian general and politician, 20th <a href=\"https://wikipedia.org/wiki/President_of_Syria\" title=\"President of Syria\">President of Syria</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Syrian general and politician, 20th <a href=\"https://wikipedia.org/wiki/President_of_Syria\" title=\"President of Syria\">President of Syria</a> (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "President of Syria", "link": "https://wikipedia.org/wiki/President_of_Syria"}]}, {"year": "1930", "text": "<PERSON>, Australian cricketer and sportscaster (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Russian astronomer (d. 2004)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian astronomer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian astronomer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikolai_<PERSON>ern<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English actress", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian-American astrophysicist and astronomer, Nobel Prize laureate (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-American astrophysicist and astronomer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-American astrophysicist and astronomer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1933", "text": "Prince <PERSON><PERSON>, 8th Nizam of Hyderabad (d. 2023)", "html": "1933 - Prince <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Jah\"><PERSON><PERSON><PERSON></a>, 8th <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hyderabad\" title=\"Nizam of Hyderabad\"><PERSON><PERSON> of Hyderabad</a> (d. 2023)", "no_year_html": "Prince <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Jah\"><PERSON><PERSON><PERSON></a>, 8th <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hyderabad\" title=\"<PERSON>zam of Hyderabad\"><PERSON><PERSON> of Hyderabad</a> (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ah"}, {"title": "<PERSON><PERSON> of Hyderabad", "link": "https://wikipedia.org/wiki/Nizam_of_Hyderabad"}]}, {"year": "1934", "text": "<PERSON>, American psychologist and author (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Italian-American wrestler and trainer (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American wrestler and trainer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American wrestler and trainer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American lawyer, educator, and activist (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, educator, and activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, educator, and activist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Caribbean-French bodybuilder and actor (d. 2011)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean-French bodybuilder and actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean-French bodybuilder and actor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, English journalist, author, and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist, author, and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American baseball player", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American lawyer and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American computer scientist and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American captain, lawyer, and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Dutch footballer and referee", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(referee)\" title=\"<PERSON> (referee)\"><PERSON></a>, Dutch footballer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(referee)\" title=\"<PERSON> (referee)\"><PERSON></a>, Dutch footballer and referee", "links": [{"title": "<PERSON> (referee)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(referee)"}]}, {"year": "1941", "text": "<PERSON>, American soldier and activist, co-founded Gay Men's Health Crisis (d. 1987)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and activist, co-founded <a href=\"https://wikipedia.org/wiki/Gay_Men%27s_Health_Crisis\" class=\"mw-redirect\" title=\"Gay Men's Health Crisis\">Gay Men's Health Crisis</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and activist, co-founded <a href=\"https://wikipedia.org/wiki/Gay_Men%27s_Health_Crisis\" class=\"mw-redirect\" title=\"Gay Men's Health Crisis\">Gay Men's Health Crisis</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gay Men's Health Crisis", "link": "https://wikipedia.org/wiki/Gay_Men%27s_Health_Crisis"}]}, {"year": "1942", "text": "<PERSON>, American painter (d. 2007)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Swedish actress and singer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rit<PERSON> E<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nd"}]}, {"year": "1942", "text": "<PERSON>, American comedian and actor (d. 2009)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English engineer and politician, Minister for Sport and the Olympics", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Sport and the Olympics", "link": "https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics"}]}, {"year": "1943", "text": "<PERSON>, Australian politician, 24th Premier of Western Australia", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1943", "text": "<PERSON>, Russian painter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Dutch singer-songwriter and guitarist (d. 2014)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Dutch singer-songwriter and guitarist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Dutch singer-songwriter and guitarist (d. 2014)", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Algerian director and screenwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English general", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Russian ice hockey player and coach", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Russian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Russian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_(ice_hockey)"}]}, {"year": "1944", "text": "<PERSON>, Brazilian race car driver (d. 1977)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Italian singer-songwriter and guitarist (d. 1997)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and guitarist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and guitarist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ivan_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American lawyer and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, South African-English cricketer and sportscaster (d. 2012)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer and sportscaster (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer and sportscaster (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian rugby league player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Filipino evangelist and politician, founded the ZOE Broadcasting Network", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino evangelist and politician, founded the <a href=\"https://wikipedia.org/wiki/ZOE_Broadcasting_Network\" title=\"ZOE Broadcasting Network\">ZOE Broadcasting Network</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino evangelist and politician, founded the <a href=\"https://wikipedia.org/wiki/ZOE_Broadcasting_Network\" title=\"ZOE Broadcasting Network\">ZOE Broadcasting Network</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "ZOE Broadcasting Network", "link": "https://wikipedia.org/wiki/ZOE_Broadcasting_Network"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Indian actor, producer and politician (d. 2017)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, producer and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, producer and politician (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>na"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Spanish singer-songwriter and actor (d. 2019)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Patxi_Andi%C3%B3n\" title=\"Patxi Andión\"><PERSON><PERSON></a>, Spanish singer-songwriter and actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patxi_Andi%C3%B3n\" title=\"Patxi Andión\"><PERSON><PERSON></a>, Spanish singer-songwriter and actor (d. 2019)", "links": [{"title": "Patxi Andión", "link": "https://wikipedia.org/wiki/Patxi_Andi%C3%B3n"}]}, {"year": "1947", "text": "<PERSON>, Italian diver", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian diver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Jamaican singer-songwriter (d. 2020)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Irish republican politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish republican politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish republican politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American guitarist and composer (d. 2018)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glenn_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, American inventor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(inventor)\" title=\"<PERSON><PERSON><PERSON> (inventor)\"><PERSON><PERSON><PERSON></a>, American inventor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(inventor)\" title=\"<PERSON><PERSON><PERSON> (inventor)\"><PERSON><PERSON><PERSON></a>, American inventor", "links": [{"title": "<PERSON><PERSON><PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(inventor)"}]}, {"year": "1949", "text": "<PERSON>, English journalist and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American R&B singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American R&amp;B singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American R&amp;B singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1949", "text": "<PERSON>, American businessman", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, French singer-songwriter and photographer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American physicist and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Singaporean-Welsh rugby player and educator", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean-Welsh rugby player and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean-Welsh rugby player and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Scottish singer-songwriter and bass player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Sutherland_Brothers\" class=\"mw-redirect\" title=\"Sutherland Brothers\"><PERSON></a>, Scottish singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sutherland_Brothers\" class=\"mw-redirect\" title=\"Sutherland Brothers\"><PERSON></a>, Scottish singer-songwriter and bass player", "links": [{"title": "Sutherland Brothers", "link": "https://wikipedia.org/wiki/Sutherland_Brothers"}]}, {"year": "1951", "text": "<PERSON>, German race car driver (d. 1985)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Turkish poet and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tlu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tlu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ayten_Mutlu"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Estonian pianist and composer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nnap"}]}, {"year": "1954", "text": "<PERSON>, American author and journalist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American football player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Yemeni politician (d. 2023)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yemeni politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yemeni politician (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American author and illustrator", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Zimbabwean footballer and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Russian ice hockey player and coach (d. 2017)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Saudi Arabian politician (d. 2012)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, Saudi Arabian politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, Saudi Arabian politician (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American baseball player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Oil_Can_Boyd\" title=\"Oil Can Boyd\">Oil Can Boyd</a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oil_Can_Boyd\" title=\"Oil Can Boyd\">Oil Can Boyd</a>, American baseball player", "links": [{"title": "Oil Can Boyd", "link": "https://wikipedia.org/wiki/Oil_Can_Boyd"}]}, {"year": "1959", "text": "<PERSON>, American politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Jr., American bowler", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American bowler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American bowler", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1960", "text": "<PERSON>, Scottish singer, songwriter director and producer.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_presenter)\" title=\"<PERSON> (television presenter)\"><PERSON></a>, Scottish singer, songwriter director and producer.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_presenter)\" title=\"<PERSON> (television presenter)\"><PERSON></a>, Scottish singer, songwriter director and producer.", "links": [{"title": "<PERSON> (television presenter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_presenter)"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Japanese actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English footballer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English businessman and journalist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American biologist and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biochemist)\" title=\"<PERSON> (biochemist)\"><PERSON></a>, American biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(biochemist)\" title=\"<PERSON> (biochemist)\"><PERSON></a>, American biologist and academic", "links": [{"title": "<PERSON> (biochemist)", "link": "https://wikipedia.org/wiki/<PERSON>(biochemist)"}]}, {"year": "1962", "text": "<PERSON>, American baseball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Yett"}]}, {"year": "1963", "text": "<PERSON>, Swedish footballer and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1963)\" title=\"<PERSON> (footballer, born 1963)\"><PERSON></a>, Swedish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1963)\" title=\"<PERSON> (footballer, born 1963)\"><PERSON></a>, Swedish footballer and coach", "links": [{"title": "<PERSON> (footballer, born 1963)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1963)"}]}, {"year": "1963", "text": "<PERSON>, American actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American basketball player (d. 1989)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German-English lawyer and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mark Field\"><PERSON></a>, German-English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mark Field\"><PERSON></a>, German-English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mark_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American swimmer and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Greek painter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>lt<PERSON>_Manetas\" title=\"<PERSON>lt<PERSON> Manetas\"><PERSON><PERSON><PERSON></a>, Greek painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lt<PERSON> Manetas\"><PERSON><PERSON><PERSON></a>, Greek painter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miltos_Manetas"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Norwegian lawyer and politician, Norwegian Minister of Justice", "html": "1964 - <a href=\"https://wikipedia.org/wiki/K<PERSON>_St<PERSON>berget\" title=\"Knut Storberget\"><PERSON><PERSON></a>, Norwegian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_and_Public_Security\" title=\"Minister of Justice and Public Security\">Norwegian Minister of Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON>_St<PERSON>berget\" title=\"Knut Storberget\"><PERSON><PERSON></a>, Norwegian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_and_Public_Security\" title=\"Minister of Justice and Public Security\">Norwegian Minister of Justice</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K<PERSON>_Storberget"}, {"title": "Minister of Justice and Public Security", "link": "https://wikipedia.org/wiki/Minister_of_Justice_and_Public_Security"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>,  German footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American philosopher and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Peg_O%27Connor\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peg_O%27Connor\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American philosopher and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Peg_O%27Connor"}]}, {"year": "1965", "text": "<PERSON>, American lawyer and politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Puerto Rican-American baseball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Sierra\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Sierra\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Sierra"}]}, {"year": "1965", "text": "<PERSON>, American academic and linguist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and linguist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and linguist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Italian author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Melania_Mazzucco\" title=\"Melania Mazzucco\"><PERSON><PERSON></a>, Italian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Melania_Mazzucco\" title=\"Melania Mazzucco\"><PERSON><PERSON></a>, Italian author", "links": [{"title": "Melania Mazzucco", "link": "https://wikipedia.org/wiki/Melania_Mazzucco"}]}, {"year": "1966", "text": "<PERSON>, American actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Irish footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Swedish footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Norwegian strongman and bodybuilder", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian strongman and bodybuilder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian strongman and bodybuilder", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English barrister and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English barrister and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English barrister and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Danish footballer and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/B<PERSON>ne_Goldb%C3%A6k\" title=\"<PERSON><PERSON><PERSON>æk\"><PERSON><PERSON><PERSON></a>, Danish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Goldb%C3%A6k\" title=\"<PERSON><PERSON><PERSON>æk\"><PERSON><PERSON><PERSON></a>, Danish footballer and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bjarne_Goldb%C3%A6k"}]}, {"year": "1968", "text": "<PERSON>, American golfer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_May_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_May_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1969", "text": "<PERSON>, Zimbabwean golfer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Black\"><PERSON></a>, Zimbabwean golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Black\"><PERSON></a>, Zimbabwean golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON> of Kelantan, <PERSON> of Malaysia", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_V_of_Kelantan\" title=\"<PERSON> V of Kelantan\"><PERSON> of Kelantan</a>, <PERSON> of Malaysia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_V_of_Kelantan\" title=\"Muhammad V of Kelantan\"><PERSON> of Kelantan</a>, <PERSON> of Malaysia", "links": [{"title": "<PERSON> of Kelantan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Kelant<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Danish-Norwegian pianist and composer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Norwegian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Norwegian pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Canadian actress and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Canadian_actress)\" title=\"<PERSON><PERSON> (Canadian actress)\"><PERSON><PERSON></a>, Canadian actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Canadian_actress)\" title=\"<PERSON><PERSON> (Canadian actress)\"><PERSON><PERSON></a>, Canadian actress and producer", "links": [{"title": "<PERSON><PERSON> (Canadian actress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Canadian_actress)"}]}, {"year": "1970", "text": "<PERSON>, American baseball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English race car driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Greek footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gonia<PERSON>\" title=\"<PERSON><PERSON> Gonia<PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gonia<PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1971", "text": "<PERSON>, English actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English footballer, coach, and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Swedish bass player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Australian golfer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, South Korean actor and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-won\" title=\"Ryu Si-won\"><PERSON><PERSON></a>, South Korean actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-won\" title=\"Ryu Si-won\"><PERSON><PERSON></a>, South Korean actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-won"}]}, {"year": "1972", "text": "<PERSON>, South Korean model and actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, South Korean model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American comedian, actor, and singer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Welsh actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American basketball player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rebecca Lobo\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Costa Rican footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Swedish ice hockey player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, British politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Vietnamese shooter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ho%C3%A0ng_Xu%C3%A2n_Vinh\" title=\"Hoàng Xu<PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ho%C3%A0ng_Xu%C3%A2n_Vinh\" title=\"<PERSON>àng Xu<PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese shooter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ho%C3%A0ng_Xu%C3%A2n_Vinh"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Guyanese cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"<PERSON><PERSON> King\"><PERSON><PERSON></a>, Guyanese cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"<PERSON>on King\"><PERSON><PERSON></a>, Guyanese cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_King"}]}, {"year": "1976", "text": "<PERSON>, Venezuelan baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>arc%C3%ADa"}]}, {"year": "1976", "text": "<PERSON>, American actor and comedian", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Czech-German tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Magdalena_Ku%C4%8Derov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech-German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magdalena_Ku%C4%8Derov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech-German tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Magdalena_Ku%C4%8Derov%C3%A1"}]}, {"year": "1976", "text": "<PERSON>, Dutch footballer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ma"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ri%C3%A8re"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Doolittle\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Doolittle\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Israeli footballer and singer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hon\"><PERSON><PERSON></a>, Israeli footballer and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hon\"><PERSON><PERSON></a>, Israeli footballer and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hon"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Bulgarian footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Swedish model, actress, and singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Carolina_Gynning\" title=\"<PERSON> Gynning\"><PERSON></a>, Swedish model, actress, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carolina_Gynning\" title=\"<PERSON> Gynning\"><PERSON></a>, Swedish model, actress, and singer", "links": [{"title": "Carolina Gynning", "link": "https://wikipedia.org/wiki/Carolina_Gynning"}]}, {"year": "1978", "text": "<PERSON>, English boxer and promoter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer and promoter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer and promoter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Chinese astronaut", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)\" class=\"mw-redirect\" title=\"<PERSON> (astronaut)\"><PERSON></a>, Chinese astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(astronaut)\" class=\"mw-redirect\" title=\"<PERSON> (astronaut)\"><PERSON></a>, Chinese astronaut", "links": [{"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)"}]}, {"year": "1979", "text": "<PERSON>, French footballer (d. 2005)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Sierra Leonean footballer and manager", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sierra Leonean footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sierra Leonean footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Dutch sprinter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, French cyclist (d. 2013)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Abdoulaye_M%C3%A9%C3%AFt%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abdoulaye_M%C3%A9%C3%AFt%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abdoulaye_M%C3%A9%C3%AFt%C3%A9"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Georgian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Ecuadorian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Armenian chess grandmaster", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Levon_Aronian\" title=\"Levon Aronian\"><PERSON><PERSON></a>, Armenian chess grandmaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Levon_Aronian\" title=\"Levon Aronian\"><PERSON><PERSON></a>, Armenian chess grandmaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Levon_Aronian"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Barbadian netball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Latonia_Blackman\" title=\"Latonia Blackman\"><PERSON><PERSON><PERSON></a>, Barbadian netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Latonia_Blackman\" title=\"Latonia Blackman\"><PERSON><PERSON><PERSON></a>, Barbadian netball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Latonia_Blackman"}]}, {"year": "1982", "text": "<PERSON>, American musician and composer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/F%C3%A1bio_J%C3%BAnior_dos_Santos\" class=\"mw-redirect\" title=\"Fábio Júnior dos Santos\"><PERSON>ábio <PERSON>ún<PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A1bio_J%C3%BAnior_dos_Santos\" class=\"mw-redirect\" title=\"Fábio Júnior dos Santos\">Fábio <PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A1bio_J%C3%BAnior_dos_Santos"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>h"}]}, {"year": "1982", "text": "<PERSON>, English boxer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, English boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, English boxer", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_(boxer)"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Czech tennis player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Renata_Vor%C3%A1%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Renata_Vor%C3%A1%C4%8Dov%C3%A1\" title=\"Renata <PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Renata_Vor%C3%A1%C4%8Dov%C3%A1"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, South African cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Morn%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morn%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Morn%C3%A9_<PERSON><PERSON>el"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1985", "text": "<PERSON>, English footballer (d. 2012)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Mexican ten-pin bowler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3ngora\" title=\"<PERSON>\"><PERSON></a>, Mexican ten-pin bowler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3ngora\" title=\"<PERSON>\"><PERSON></a>, Mexican ten-pin bowler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_G%C3%B3ngora"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ta<PERSON><PERSON>_<PERSON>nk\" title=\"Ta<PERSON><PERSON>nk\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nk\" title=\"<PERSON><PERSON><PERSON>nk\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nk"}]}, {"year": "1986", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1987)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Fijian-Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ate\" title=\"<PERSON><PERSON><PERSON>ate\"><PERSON><PERSON><PERSON></a>, Fijian-Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Uate\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Fijian-Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ate"}]}, {"year": "1988", "text": "<PERSON>, American film director", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Cameroonian footballer (d. 2014)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Bodjongo\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Bodjongo\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Albert_E<PERSON>s%C3%A9_<PERSON><PERSON>o"}]}, {"year": "1989", "text": "<PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Portuguese_footballer)\" title=\"<PERSON><PERSON> (Portuguese footballer)\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Portuguese_footballer)\" title=\"<PERSON><PERSON> (Portuguese footballer)\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON> (Portuguese footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(Portuguese_footballer)"}]}, {"year": "1990", "text": "<PERSON>, American basketball player and coach", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Acy\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Acy\" title=\"Quincy Acy\"><PERSON></a>, American basketball player and coach", "links": [{"title": "Quincy Acy", "link": "https://wikipedia.org/wiki/Quincy_Acy"}]}, {"year": "1990", "text": "<PERSON>, English actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1990", "text": "<PERSON>, Swedish ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1990)\" title=\"<PERSON> (ice hockey, born 1990)\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1990)\" title=\"<PERSON> (ice hockey, born 1990)\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1990)", "link": "https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1990)"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1990", "text": "<PERSON>, South Korean singer and actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hwa\" title=\"Han Sun-hwa\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hwa\" title=\"Han Sun-hwa\"><PERSON>-<PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON>wa", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hwa"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American actor, rapper, and dancer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, rapper, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, rapper, and dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gan"}]}, {"year": "1992", "text": "<PERSON>, Canadian-American ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian rugby player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Taylor_<PERSON>\" title=\"Taylor Paris\"><PERSON></a>, Canadian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taylor_<PERSON>\" title=\"Taylor Paris\"><PERSON></a>, Canadian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Taylor_Paris"}]}, {"year": "1993", "text": "<PERSON>, English sprinter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, American fashion model", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American fashion model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American fashion model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English-Irish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Nail_<PERSON>pov\" title=\"Na<PERSON> Yakupov\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>pov\" title=\"Na<PERSON>pov\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>_<PERSON><PERSON>v"}]}, {"year": "1994", "text": "<PERSON>, American ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South Korean rapper and songwriter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-he<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>he<PERSON>\"><PERSON></a>, South Korean rapper and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-he<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>he<PERSON>\"><PERSON></a>, South Korean rapper and songwriter", "links": [{"title": "<PERSON>heon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-heon"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Danish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Finnish race car driver", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ri"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American internet personality", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Jennings\" title=\"<PERSON> Jennings\"><PERSON></a>, American internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Jennings\" title=\"<PERSON> Jennings\"><PERSON></a>, American internet personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Jennings"}]}, {"year": "2000", "text": "<PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American social media personality, dancer, and singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social media personality, dancer, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social media personality, dancer, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Congolese basketball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Australian singer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Australian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Australian singer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}], "Deaths": [{"year": "23", "text": "<PERSON>, emperor of the Xin Dynasty", "html": "23 - AD 23 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, emperor of the Xin Dynasty", "no_year_html": "AD 23 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, emperor of the Xin Dynasty", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "404", "text": "<PERSON><PERSON><PERSON>, Byzantine empress", "html": "404 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Eudoxia\"><PERSON><PERSON><PERSON></a>, Byzantine empress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Eudoxia\"><PERSON><PERSON><PERSON></a>, Byzantine empress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "836", "text": "<PERSON><PERSON> the Patrician, Byzantine general", "html": "836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Patrician\" title=\"<PERSON><PERSON> the Patrician\"><PERSON><PERSON> the Patrician</a>, Byzantine general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Patrician\" title=\"<PERSON><PERSON> the Patrician\"><PERSON><PERSON> the Patrician</a>, Byzantine general", "links": [{"title": "<PERSON><PERSON> the Patrician", "link": "https://wikipedia.org/wiki/Nicetas_the_Patrician"}]}, {"year": "869", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans, Frankish queen (b. 823)", "html": "869 - <a href=\"https://wikipedia.org/wiki/Erment<PERSON>e_of_Orl%C3%A9ans\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans</a>, Frankish queen (b. 823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erment<PERSON><PERSON>_of_Orl%C3%A9ans\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans</a>, Frankish queen (b. 823)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans", "link": "https://wikipedia.org/wiki/Ermentrude_of_Orl%C3%A9ans"}]}, {"year": "877", "text": "<PERSON> the <PERSON>, Holy Roman Emperor (b. 823)", "html": "877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bald\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a>, Holy Roman Emperor (b. 823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ba<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the <PERSON>ld</a>, Holy Roman Emperor (b. 823)", "links": [{"title": "<PERSON> the <PERSON>ld", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "997", "text": "<PERSON><PERSON>, Japanese samurai (b. 912)", "html": "997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Mitsunaka\" title=\"Minamoto no Mitsunaka\"><PERSON><PERSON> no <PERSON></a>, Japanese samurai (b. 912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Mitsunaka\" title=\"Minamoto no Mitsunaka\"><PERSON><PERSON> no <PERSON></a>, Japanese samurai (b. 912)", "links": [{"title": "<PERSON><PERSON> no <PERSON>tsuna<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1014", "text": "<PERSON>, tsar of the Bulgarian Empire", "html": "1014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON></a>, tsar of the Bulgarian Empire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON></a>, tsar of the Bulgarian Empire", "links": [{"title": "Samuel of Bulgaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bulgaria"}]}, {"year": "1019", "text": "<PERSON> of Luxembourg, count of Moselgau (b. 965)", "html": "1019 - <a href=\"https://wikipedia.org/wiki/Frederick_of_Luxembourg\" title=\"<PERSON> of Luxembourg\"><PERSON> of Luxembourg</a>, count of Moselgau (b. 965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frederick_of_Luxembourg\" title=\"<PERSON> of Luxembourg\"><PERSON> of Luxembourg</a>, count of Moselgau (b. 965)", "links": [{"title": "<PERSON> of Luxembourg", "link": "https://wikipedia.org/wiki/Frederick_of_Luxembourg"}]}, {"year": "1145", "text": "<PERSON>, archbishop of Pisa", "html": "1145 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Pisa)\" title=\"<PERSON> (archbishop of Pisa)\"><PERSON></a>, archbishop of Pisa", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Pisa)\" title=\"<PERSON> (archbishop of Pisa)\"><PERSON></a>, archbishop of Pisa", "links": [{"title": "<PERSON> (archbishop of Pisa)", "link": "https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Pisa)"}]}, {"year": "1090", "text": "<PERSON><PERSON><PERSON><PERSON>, bishop of Würzburg", "html": "1090 - <a href=\"https://wikipedia.org/wiki/Adalbero_of_W%C3%BCrzburg\" title=\"<PERSON><PERSON><PERSON><PERSON> of Würzburg\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of Würzburg", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adalbero_of_W%C3%BCrzburg\" title=\"<PERSON><PERSON><PERSON><PERSON> of Würzburg\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of Würzburg", "links": [{"title": "Adalbero of Würzburg", "link": "https://wikipedia.org/wiki/Adalbero_of_W%C3%BCrzburg"}]}, {"year": "1101", "text": "<PERSON> of Cologne, German monk, founded the Carthusian Order", "html": "1101 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cologne\" title=\"<PERSON> of Cologne\"><PERSON> of Cologne</a>, German monk, founded the <a href=\"https://wikipedia.org/wiki/Carthusian\" class=\"mw-redirect\" title=\"Carthusian\">Carthusian Order</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cologne\" title=\"<PERSON> of Cologne\"><PERSON> of Cologne</a>, German monk, founded the <a href=\"https://wikipedia.org/wiki/Carthusian\" class=\"mw-redirect\" title=\"Carthusian\">Carthusian Order</a>", "links": [{"title": "<PERSON> of Cologne", "link": "https://wikipedia.org/wiki/Bruno_of_Cologne"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Carthusian"}]}, {"year": "1173", "text": "<PERSON><PERSON><PERSON>, margrave of Istria", "html": "1173 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Istria\" title=\"<PERSON><PERSON><PERSON>, Margrave of Istria\"><PERSON><PERSON><PERSON></a>, margrave of Istria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Istria\" title=\"<PERSON><PERSON><PERSON> III, Mar<PERSON> of Istria\"><PERSON><PERSON><PERSON> III</a>, margrave of Istria", "links": [{"title": "<PERSON><PERSON><PERSON>, Margrave of Istria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_<PERSON><PERSON>"}]}, {"year": "1349", "text": "<PERSON> of Navarre, daughter of <PERSON> of France (b. 1312)", "html": "1349 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a>, daughter of <a href=\"https://wikipedia.org/wiki/Louis_X_of_France\" title=\"Louis X of France\"><PERSON> of France</a> (b. 1312)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a>, daughter of <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_of_France\" title=\"Louis <PERSON> of France\"><PERSON> of France</a> (b. 1312)", "links": [{"title": "<PERSON> of Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_X_of_France"}]}, {"year": "1398", "text": "<PERSON><PERSON>, Korean prime minister (b. 1342)", "html": "1398 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Korean prime minister (b. 1342)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Korean prime minister (b. 1342)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1413", "text": "<PERSON><PERSON><PERSON>, ruler (Emperor) of Ethiopia (b. 1382)", "html": "1413 - <a href=\"https://wikipedia.org/wiki/<PERSON>wit_<PERSON>\" title=\"<PERSON>wit I\"><PERSON><PERSON><PERSON> <PERSON></a>, ruler (Emperor) of Ethiopia (b. 1382)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>t_<PERSON>\" title=\"<PERSON>wit I\"><PERSON><PERSON><PERSON> <PERSON></a>, ruler (Emperor) of Ethiopia (b. 1382)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>wit_I"}]}, {"year": "1536", "text": "<PERSON>, English Protestant Bible translator (b. c. 1494)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Protestant Bible translator (b. c. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Protestant Bible translator (b. c. 1494)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1553", "text": "<PERSON><PERSON><PERSON><PERSON>, Ottoman prince (b. 1515)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/%C5%9<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ottoman prince (b. 1515)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%9<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ottoman prince (b. 1515)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%9<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1559", "text": "<PERSON>, Count of Nassau-Siegen, German count (b. 1487)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count (b. 1487)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count (b. 1487)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-<PERSON>n"}]}, {"year": "1640", "text": "<PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg (b. 1588)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV,_Count_of_Waldeck-Eisenberg\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg\"><PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg</a> (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Waldeck-Eisenberg\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg\"><PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg</a> (b. 1588)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV,_Count_<PERSON>_Waldeck-<PERSON>ise<PERSON>"}]}, {"year": "1641", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch explorer", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON>hij<PERSON>_Quast\" title=\"<PERSON><PERSON><PERSON><PERSON> Quast\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matthij<PERSON>_Quast\" title=\"<PERSON><PERSON><PERSON><PERSON> Quast\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch explorer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hi<PERSON><PERSON>_<PERSON>uast"}]}, {"year": "1644", "text": "<PERSON> of France, queen of Spain and Portugal (b. 1602)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_France_(1602%E2%80%931644)\" class=\"mw-redirect\" title=\"<PERSON> of France (1602-1644)\"><PERSON> of France</a>, queen of Spain and Portugal (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_France_(1602%E2%80%931644)\" class=\"mw-redirect\" title=\"<PERSON> of France (1602-1644)\"><PERSON> of France</a>, queen of Spain and Portugal (b. 1602)", "links": [{"title": "<PERSON> of France (1602-1644)", "link": "https://wikipedia.org/wiki/Elisabeth_of_France_(1602%E2%80%931644)"}]}, {"year": "1660", "text": "<PERSON>, French poet and author (b. 1610)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1610)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1661", "text": "<PERSON>, Indian 7th Sikh guru (b. 1630)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>, Indian 7th <a href=\"https://wikipedia.org/wiki/Sikh_gurus\" title=\"Sikh gurus\">Sikh guru</a> (b. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>, Indian 7th <a href=\"https://wikipedia.org/wiki/Sikh_gurus\" title=\"Sikh gurus\">Sikh guru</a> (b. 1630)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Sikh gurus", "link": "https://wikipedia.org/wiki/Sikh_gurus"}]}, {"year": "1688", "text": "<PERSON>, 2nd Duke of Albemarle, English soldier and politician, Lieutenant Governor of Jamaica (b. 1652)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Albemarle\" title=\"<PERSON>, 2nd Duke of Albemarle\"><PERSON>, 2nd Duke of Albemarle</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Jamaica\" class=\"mw-redirect\" title=\"Lieutenant Governor of Jamaica\">Lieutenant Governor of Jamaica</a> (b. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Albemarle\" title=\"<PERSON>, 2nd Duke of Albemarle\"><PERSON>, 2nd Duke of Albemarle</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Jamaica\" class=\"mw-redirect\" title=\"Lieutenant Governor of Jamaica\">Lieutenant Governor of Jamaica</a> (b. 1652)", "links": [{"title": "<PERSON>, 2nd Duke of Albemarle", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Albemarle"}, {"title": "Lieutenant Governor of Jamaica", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Jamaica"}]}, {"year": "1762", "text": "<PERSON>, Italian violinist and composer (b. 1684)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, king of Sardinia (b. 1751)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON></a>, king of Sardinia (b. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON></a>, king of Sardinia (b. 1751)", "links": [{"title": "<PERSON> of Sardinia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia"}]}, {"year": "1829", "text": "<PERSON>, French-American politician, 6th Governor of Louisiana (b. 1769)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Louisiana", "link": "https://wikipedia.org/wiki/Governor_of_Louisiana"}]}, {"year": "1836", "text": "<PERSON>, Dutch painter and actor (b. 1770)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and actor (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and actor (b. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Polish-English geologist and explorer (b. 1797)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Pawe%C5%82_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-English geologist and explorer (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pawe%C5%82_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-English geologist and explorer (b. 1797)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pawe%C5%82_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, Vietnamese emperor (b. 1852)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/D%E1%BB%A5c_%C4%90%E1%BB%A9c\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese emperor (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%E1%BB%A5c_%C4%90%E1%BB%A9c\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese emperor (b. 1852)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%E1%BB%A5c_%C4%90%E1%BB%A9c"}]}, {"year": "1891", "text": "<PERSON>, Irish politician (b. 1846)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Lord <PERSON>, English poet (b. 1809)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, English poet (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, English poet (b. 1809)", "links": [{"title": "<PERSON>, Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Lord_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Belgian politician, 14th Prime Minister of Belgium, Nobel Prize laureate (b. 1829)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Ottoman politician, 285th Grand Vizier of the Ottoman Empire (b. 1853)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ottoman politician, 285th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ottoman politician, 285th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1853)", "links": [{"title": "Damat Ferid Pasha", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, German politician (b. 1884)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>egmund_Gl%C3%<PERSON><PERSON>mann"}]}, {"year": "1945", "text": "<PERSON>, German SS officer (b. 1900)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Finnish composer and critic (b. 1887)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish composer and critic (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish composer and critic (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American businessman, founded the Kellogg Company (b. 1860)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Kellogg_Company\" class=\"mw-redirect\" title=\"Kellogg Company\">Kellogg Company</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Kellogg_Company\" class=\"mw-redirect\" title=\"Kellogg Company\">Kellogg Company</a> (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kellogg Company", "link": "https://wikipedia.org/wiki/Ke<PERSON>gg_Company"}]}, {"year": "1951", "text": "<PERSON>, German-American physician and biochemist, Nobel Prize laureate (b. 1884)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1959", "text": "<PERSON>, American historian and author (b. 1865)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actor, director, screenwriter (b. 1880)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"To<PERSON> Browning\"><PERSON><PERSON></a>, American actor, director, screenwriter (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"To<PERSON> Browning\"><PERSON><PERSON></a>, American actor, director, screenwriter (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English mathematician and physicist (b. 1917)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and physicist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and physicist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American golfer (b. 1892)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Austrian zoologist (b. 1893)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ck\" title=\"<PERSON>\"><PERSON></a>, Austrian zoologist (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ck\" title=\"<PERSON>\"><PERSON></a>, Austrian zoologist (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ck"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Brazilian actress and film director (born c. 1909)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A9o_de_Verberena\" title=\"<PERSON><PERSON>o de Verberena\"><PERSON><PERSON><PERSON></a>, Brazilian actress and film director (born c. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A9o_de_Verberena\" title=\"<PERSON><PERSON><PERSON> de Verberena\"><PERSON><PERSON><PERSON></a>, Brazilian actress and film director (born c. 1909)", "links": [{"title": "Cléo de Verberena", "link": "https://wikipedia.org/wiki/Cl%C3%A9o_de_Verberena"}]}, {"year": "1973", "text": "<PERSON>, American actor (b. 1895)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, French race car driver (b. 1944)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Cevert"}]}, {"year": "1973", "text": "<PERSON>, Dutch actor, screenwriter, and author (b. 1894)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch actor, screenwriter, and author (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch actor, screenwriter, and author (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English actor (b. 1915)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American missionary and author (b. 1882)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American missionary and author (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American missionary and author (b. 1882)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_(novelist)"}]}, {"year": "1974", "text": "<PERSON>, Austrian race car driver (b. 1948)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English philosopher and author (b. 1900)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian singer-songwriter (b. 1935)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe"}]}, {"year": "1979", "text": "<PERSON>, American poet and short-story writer (b. 1911)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and short-story writer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and short-story writer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, English actress and producer (b. 1922)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress and producer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress and producer (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, French cyclist (b. 1921)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Egyptian colonel and politician, 3rd President of Egypt, Nobel Prize laureate (b. 1918)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>war <PERSON>\"><PERSON><PERSON></a>, Egyptian colonel and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian colonel and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>at"}, {"title": "President of Egypt", "link": "https://wikipedia.org/wiki/President_of_Egypt"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1983", "text": "<PERSON>, American cardinal (b. 1921)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American composer, conductor, and bandleader (b. 1921)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, conductor, and bandleader (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, conductor, and bandleader (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Russian mathematician and computer scientist (b. 1921)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and computer scientist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and computer scientist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American actress (b. 1908)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish sociologist and politician (b. 1919)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Bahriye_%C3%9C%C3%A7ok\" title=\"Bahriye Üçok\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish sociologist and politician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bahriye_%C3%9C%C3%A7ok\" title=\"Bahriye Üçok\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish sociologist and politician (b. 1919)", "links": [{"title": "Bahriye Üçok", "link": "https://wikipedia.org/wiki/Bahriye_%C3%9C%C3%A7ok"}]}, {"year": "1991", "text": "<PERSON>, Russian singer-songwriter (b. 1956)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, English actor (b. 1922)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Elliott\"><PERSON><PERSON></a>, English actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Elliott\"><PERSON><PERSON></a>, English actor (b. 1922)", "links": [{"title": "<PERSON><PERSON> Elliott", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian cricketer and sportscaster (b. 1905)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and sportscaster (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and sportscaster (b. 1905)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(cricketer)"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Turkish chemist, businessman, and philanthropist, founded E<PERSON>ac<PERSON>başı (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Nejat_Eczac%C4%B1ba%C5%9F%C4%B1\" title=\"Nejat Eczacıbaşı\"><PERSON><PERSON><PERSON>acı<PERSON>şı</a>, Turkish chemist, businessman, and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Eczac%C4%B1ba%C5%9F%C4%B1\" title=\"Eczacıbaşı\"><PERSON><PERSON><PERSON><PERSON><PERSON>ş<PERSON></a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nejat_Eczac%C4%B1ba%C5%9F%C4%B1\" title=\"Nejat Eczacıbaşı\"><PERSON><PERSON><PERSON> Eczacıbaşı</a>, Turkish chemist, businessman, and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Eczac%C4%B1ba%C5%9F%C4%B1\" title=\"Eczacıbaşı\">Eczacıbaş<PERSON></a> (b. 1913)", "links": [{"title": "<PERSON><PERSON>at <PERSON>", "link": "https://wikipedia.org/wiki/Nejat_Eczac%C4%B1ba%C5%9F%C4%B1"}, {"title": "Eczacıbaşı", "link": "https://wikipedia.org/wiki/Eczac%C4%B1ba%C5%9F%C4%B1"}]}, {"year": "1993", "text": "<PERSON>, American truck driver and pilot (b. 1949)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American truck driver and pilot (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American truck driver and pilot (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, French mountaineer (b. 1961)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French mountaineer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beno%C3%AE<PERSON>_<PERSON>ux\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French mountaineer (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beno%C3%AEt_Cha<PERSON>ux"}]}, {"year": "1997", "text": "<PERSON>, American baseball player and manager (b. 1914)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American baseball player (b. 1944)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Portuguese singer and actress (b. 1920)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Am%C3%<PERSON><PERSON>_Rod<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese singer and actress (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Am%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese singer and actress (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Am%C3%A1lia_Rod<PERSON>ues"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American wrestler and sportscaster (b. 1937)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Gorilla_Monsoon\" title=\"Gorilla Monsoon\"><PERSON><PERSON></a>, American wrestler and sportscaster (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gorilla_Monsoon\" title=\"Gorilla Monsoon\"><PERSON><PERSON></a>, American wrestler and sportscaster (b. 1937)", "links": [{"title": "Gorilla Monsoon", "link": "https://wikipedia.org/wiki/Gorilla_Monsoon"}]}, {"year": "2000", "text": "<PERSON>, American actor and stuntman (b. 1920)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and stuntman (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and stuntman (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American director and producer (b. 1934)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and producer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and producer (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON> of the Netherlands (b. 1926)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_the_Netherlands\" title=\"Prince <PERSON> of the Netherlands\">Prince <PERSON> of the Netherlands</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_the_Netherlands\" title=\"Prince <PERSON> of the Netherlands\">Prince <PERSON> of the Netherlands</a> (b. 1926)", "links": [{"title": "<PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_of_the_Netherlands"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Dutch sprinter (b. 1930)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sprinter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sprinter (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Argentinian director and screenwriter (b. 1940)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and screenwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and screenwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American baseball player and manager (b. 1911)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neil\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neil\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Neil"}]}, {"year": "2006", "text": "<PERSON>, American author and critic (b. 1914)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and critic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and critic (b. 1914)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, 8th Chief Minister of Maharashtra (b. 1921)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a> (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of Maharashtra", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Indian scholar, jurist, and politician (b. 1931)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>x<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian scholar, jurist, and politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>x<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian scholar, jurist, and politician (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laxmi_Mall_Singhvi"}]}, {"year": "2008", "text": "<PERSON>, Australian public servant and politician (b. 1925)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian public servant and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian public servant and politician (b. 1925)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "2009", "text": "<PERSON>, Scottish-Canadian actor and screenwriter (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish-Canadian actor and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish-Canadian actor and screenwriter (b. 1922)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2010", "text": "<PERSON>, South-African-Australian historian and author (b. 1937)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South-African-Australian historian and author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South-African-Australian historian and author (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Dutch television and film actor, and musician (b. 1966)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch television and film actor, and musician (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch television and film actor, and musician (b. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Australian actress and author (b. 1932)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and author (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Algerian colonel and politician, 3rd President of Algeria (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian colonel and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chad<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian colonel and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Algeria", "link": "https://wikipedia.org/wiki/President_of_Algeria"}]}, {"year": "2012", "text": "<PERSON>, English organist and composer (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1977)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Mar<PERSON> of Meissen (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Meissen_(1934%E2%80%932012)\" class=\"mw-redirect\" title=\"<PERSON>, Mar<PERSON> of Meissen (1934-2012)\"><PERSON>, <PERSON><PERSON> of Meissen</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Meissen_(1934%E2%80%932012)\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON> of Meissen (1934-2012)\"><PERSON>, <PERSON><PERSON> of Meissen</a> (b. 1943)", "links": [{"title": "<PERSON>, Mar<PERSON> of Meissen (1934-2012)", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_(1934%E2%80%932012)"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and politician, 19th Secretary of State of Wyoming (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Wyoming_politician)\" title=\"<PERSON> (Wyoming politician)\"><PERSON></a>, American lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_Wyoming\" title=\"Secretary of State of Wyoming\">Secretary of State of Wyoming</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Wyoming_politician)\" title=\"<PERSON> (Wyoming politician)\"><PERSON></a>, American lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_Wyoming\" title=\"Secretary of State of Wyoming\">Secretary of State of Wyoming</a> (b. 1941)", "links": [{"title": "<PERSON> (Wyoming politician)", "link": "https://wikipedia.org/wiki/<PERSON>(Wyoming_politician)"}, {"title": "Secretary of State of Wyoming", "link": "https://wikipedia.org/wiki/Secretary_of_State_of_Wyoming"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, Indian lawyer and politician, 19th Governor of West Bengal (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Bengal\" class=\"mw-redirect\" title=\"Governor of West Bengal\">Governor of West Bengal</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Bengal\" class=\"mw-redirect\" title=\"Governor of West Bengal\">Governor of West Bengal</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of West Bengal", "link": "https://wikipedia.org/wiki/Governor_of_West_Bengal"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, English-Australian philosopher and academic (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English-Australian philosopher and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. J. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English-Australian philosopher and academic (b. 1920)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON>._<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American-Canadian football player and coach (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American screenwriter and producer (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/R<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>nier\"><PERSON><PERSON></a>, American screenwriter and producer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter and producer (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English actor (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1917)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "2013", "text": "<PERSON>, Dutch physicist and academic (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and academic (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American tennis player and coach (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vic_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, German-Polish sculptor (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Polish sculptor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Polish sculptor (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian actress, director and choreographer (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress, director and choreographer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress, director and choreographer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actress (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer and manager (b. 1976)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (b. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Turkish football player (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Feridun_Bu%C4%9Feker\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish football player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ridun_Bu%C4%9Feker\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish football player (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Feridun_Bu%C4%9Feker"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian author, playwright, and politician, 1st President of Hungary (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1d_G%C3%B6ncz\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author, playwright, and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Hungary\" title=\"List of heads of state of Hungary\">President of Hungary</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1d_G%C3%B6ncz\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author, playwright, and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Hungary\" title=\"List of heads of state of Hungary\">President of Hungary</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81rp%C3%A1d_G%C3%B6ncz"}, {"title": "List of heads of state of Hungary", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Hungary"}]}, {"year": "2015", "text": "<PERSON>, Ukrainian-American sociologist, historian, political scientist, and academic (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Vladimir_<PERSON>h\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American sociologist, historian, political scientist, and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladimir_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American sociologist, historian, political scientist, and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_Shlapentokh"}]}, {"year": "2015", "text": "<PERSON>, Peruvian historian, lawyer, and jurist (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>no\" title=\"<PERSON>no\"><PERSON></a>, Peruvian historian, lawyer, and jurist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian historian, lawyer, and jurist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, American stand-up comedian and actor (b. 1972)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American stand-up comedian and actor (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American stand-up comedian and actor (b. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_May"}]}, {"year": "2017", "text": "<PERSON>, British architect, designer of the London Eye (b. 1952)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, British architect, designer of the <a href=\"https://wikipedia.org/wiki/London_Eye\" title=\"London Eye\">London Eye</a> (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, British architect, designer of the <a href=\"https://wikipedia.org/wiki/London_Eye\" title=\"London Eye\">London Eye</a> (b. 1952)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_(architect)"}, {"title": "London Eye", "link": "https://wikipedia.org/wiki/London_Eye"}]}, {"year": "2018", "text": "<PERSON>, American actor (b. 1942)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1942)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, Spanish soprano (b. 1933)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Montserrat_Caball%C3%A9\" title=\"Montserrat Caballé\"><PERSON><PERSON><PERSON>ll<PERSON></a>, Spanish soprano (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Montserrat_Caball%C3%A9\" title=\"Montserrat Caballé\"><PERSON><PERSON><PERSON></a>, Spanish soprano (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Montserrat_Caball%C3%A9"}]}, {"year": "2019", "text": "<PERSON>, English drummer (b. 1939)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Australian rugby league player (b. 1936)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, American actor and comedian (b. 1931)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor and comedian (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor and comedian (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rip_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Dutch-American guitarist, songwriter, and producer (b. 1955)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American guitarist, songwriter, and producer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American guitarist, songwriter, and producer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American singer-songwriter (b. 1940)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nash\"><PERSON></a>, American singer-songwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American politician (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Dutch footballer and manager (b. 1951)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}]}}