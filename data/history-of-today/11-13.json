{"date": "November 13", "url": "https://wikipedia.org/wiki/November_13", "data": {"Events": [{"year": "1002", "text": "English king <PERSON><PERSON><PERSON><PERSON> <PERSON> orders the killing of all Danes in England, known today as the St. Brice's Day massacre.", "html": "1002 - English king <a href=\"https://wikipedia.org/wiki/%C3%86thelred_II\" class=\"mw-redirect\" title=\"Æthelred II\"><PERSON><PERSON><PERSON><PERSON> II</a> orders the killing of all <a href=\"https://wikipedia.org/wiki/Danes_(ancient_people)\" class=\"mw-redirect\" title=\"Danes (ancient people)\">Danes</a> in England, known today as the <a href=\"https://wikipedia.org/wiki/St._Brice%27s_Day_massacre\" class=\"mw-redirect\" title=\"St. Brice's Day massacre\">St. Brice's Day massacre</a>.", "no_year_html": "English king <a href=\"https://wikipedia.org/wiki/%C3%86thelred_II\" class=\"mw-redirect\" title=\"Æthelred II\"><PERSON><PERSON><PERSON><PERSON> II</a> orders the killing of all <a href=\"https://wikipedia.org/wiki/Danes_(ancient_people)\" class=\"mw-redirect\" title=\"Danes (ancient people)\">Danes</a> in England, known today as the <a href=\"https://wikipedia.org/wiki/St._Brice%27s_Day_massacre\" class=\"mw-redirect\" title=\"St. Brice's Day massacre\">St. Brice's Day massacre</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%86thelred_II"}, {"title": "Danes (ancient people)", "link": "https://wikipedia.org/wiki/Danes_(ancient_people)"}, {"title": "St. Brice's Day massacre", "link": "https://wikipedia.org/wiki/St._Brice%27s_Day_massacre"}]}, {"year": "1093", "text": "Battle of Alnwick: in an English victory over the Scots, <PERSON> of Scotland, and his son <PERSON>, are killed.", "html": "1093 - <a href=\"https://wikipedia.org/wiki/Battle_of_Alnwick_(1093)\" title=\"Battle of Alnwick (1093)\">Battle of Alnwick</a>: in an English victory over the Scots, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a>, and his son <PERSON>, are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Alnwick_(1093)\" title=\"Battle of Alnwick (1093)\">Battle of Alnwick</a>: in an English victory over the Scots, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a>, and his son <PERSON>, are killed.", "links": [{"title": "Battle of Alnwick (1093)", "link": "https://wikipedia.org/wiki/Battle_of_Alnwick_(1093)"}, {"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1160", "text": "<PERSON> of France marries <PERSON><PERSON> of Champagne.", "html": "1160 - <a href=\"https://wikipedia.org/wiki/Louis_VII_of_France\" title=\"Louis VII of France\"><PERSON> of France</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Champagne\" title=\"<PERSON><PERSON> of Champagne\"><PERSON><PERSON> of Champagne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_VII_of_France\" title=\"Louis VII of France\"><PERSON> of France</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Champagne\" title=\"<PERSON><PERSON> of Champagne\"><PERSON><PERSON> of Champagne</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VII_of_France"}, {"title": "<PERSON><PERSON> of Champagne", "link": "https://wikipedia.org/wiki/<PERSON>ela_of_Champagne"}]}, {"year": "1642", "text": "First English Civil War: Battle of Turnham Green: The Royalist forces withdraw in the face of the Parliamentarian army and fail to take London.", "html": "1642 - <a href=\"https://wikipedia.org/wiki/First_English_Civil_War\" title=\"First English Civil War\">First English Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Turnham_Green\" title=\"Battle of Turnham Green\">Battle of Turnham Green</a>: The <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Royalist</a> forces withdraw in the face of the <a href=\"https://wikipedia.org/wiki/Roundhead\" title=\"Roundhead\">Parliamentarian</a> army and fail to take <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_English_Civil_War\" title=\"First English Civil War\">First English Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Turnham_Green\" title=\"Battle of Turnham Green\">Battle of Turnham Green</a>: The <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Royalist</a> forces withdraw in the face of the <a href=\"https://wikipedia.org/wiki/Roundhead\" title=\"Roundhead\">Parliamentarian</a> army and fail to take <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "links": [{"title": "First English Civil War", "link": "https://wikipedia.org/wiki/First_English_Civil_War"}, {"title": "Battle of Turnham Green", "link": "https://wikipedia.org/wiki/Battle_of_Turnham_Green"}, {"title": "Cavalier", "link": "https://wikipedia.org/wiki/Cavalier"}, {"title": "Roundhead", "link": "https://wikipedia.org/wiki/Roundhead"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1715", "text": "Jacobite rising in Scotland: Battle of Sheriffmuir: The forces of the Kingdom of Great Britain halt the Jacobite advance, although the action is inconclusive.", "html": "1715 - <a href=\"https://wikipedia.org/wiki/Jacobite_rising_of_1715\" title=\"Jacobite rising of 1715\">Jacobite rising</a> in Scotland: <a href=\"https://wikipedia.org/wiki/Battle_of_Sheriffmuir\" title=\"Battle of Sheriffmuir\">Battle of Sheriffmuir</a>: The forces of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> halt the Jacobite advance, although the action is inconclusive.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jacobite_rising_of_1715\" title=\"Jacobite rising of 1715\">Jacobite rising</a> in Scotland: <a href=\"https://wikipedia.org/wiki/Battle_of_Sheriffmuir\" title=\"Battle of Sheriffmuir\">Battle of Sheriffmuir</a>: The forces of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> halt the Jacobite advance, although the action is inconclusive.", "links": [{"title": "Jacobite rising of 1715", "link": "https://wikipedia.org/wiki/Jacobite_rising_of_1715"}, {"title": "Battle of Sheriffmuir", "link": "https://wikipedia.org/wiki/Battle_of_Sheriffmuir"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}]}, {"year": "1775", "text": "American Revolutionary War: Patriot revolutionary forces under <PERSON><PERSON> <PERSON> occupy Montreal.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: Patriot revolutionary forces under <PERSON>. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> occupy <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: Patriot revolutionary forces under <PERSON>. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> occupy <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}]}, {"year": "1833", "text": "Great Meteor Storm of 1833.", "html": "1833 - <a href=\"https://wikipedia.org/wiki/Great_Meteor_Storm_of_1833\" class=\"mw-redirect\" title=\"Great Meteor Storm of 1833\">Great Meteor Storm of 1833</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Meteor_Storm_of_1833\" class=\"mw-redirect\" title=\"Great Meteor Storm of 1833\">Great Meteor Storm of 1833</a>.", "links": [{"title": "Great Meteor Storm of 1833", "link": "https://wikipedia.org/wiki/Great_Meteor_Storm_of_1833"}]}, {"year": "1841", "text": "<PERSON> first sees a demonstration of animal magnetism by <PERSON>, which leads to his study of the subject he eventually calls hypnotism.", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a> first sees a demonstration of <i><a href=\"https://wikipedia.org/wiki/Animal_magnetism\" title=\"Animal magnetism\">animal magnetism</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, which leads to his study of the subject he eventually calls <i><a href=\"https://wikipedia.org/wiki/Hypnotism\" class=\"mw-redirect\" title=\"Hypnotism\">hypnotism</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a> first sees a demonstration of <i><a href=\"https://wikipedia.org/wiki/Animal_magnetism\" title=\"Animal magnetism\">animal magnetism</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, which leads to his study of the subject he eventually calls <i><a href=\"https://wikipedia.org/wiki/Hypnotism\" class=\"mw-redirect\" title=\"Hypnotism\">hypnotism</a></i>.", "links": [{"title": "<PERSON> (surgeon)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surgeon)"}, {"title": "Animal magnetism", "link": "https://wikipedia.org/wiki/Animal_magnetism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hypnotism", "link": "https://wikipedia.org/wiki/Hypnotism"}]}, {"year": "1851", "text": "The Denny Party lands at Alki Point, before moving to the other side of Elliott Bay to what would become Seattle.", "html": "1851 - The <a href=\"https://wikipedia.org/wiki/Denny_Party\" title=\"Denny Party\">Denny Party</a> lands at <a href=\"https://wikipedia.org/wiki/Alki_Point,_Seattle\" title=\"Alki Point, Seattle\">Alki Point</a>, before moving to the other side of Elliott Bay to what would become <a href=\"https://wikipedia.org/wiki/Seattle\" title=\"Seattle\">Seattle</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Denny_Party\" title=\"Denny Party\">Denny Party</a> lands at <a href=\"https://wikipedia.org/wiki/Alki_Point,_Seattle\" title=\"Alki Point, Seattle\">Alki Point</a>, before moving to the other side of Elliott Bay to what would become <a href=\"https://wikipedia.org/wiki/Seattle\" title=\"Seattle\">Seattle</a>.", "links": [{"title": "Denny Party", "link": "https://wikipedia.org/wiki/Denny_Party"}, {"title": "Alki Point, Seattle", "link": "https://wikipedia.org/wiki/Alki_Point,_Seattle"}, {"title": "Seattle", "link": "https://wikipedia.org/wiki/Seattle"}]}, {"year": "1864", "text": "American Civil War: The three-day Battle of Bull's Gap ends in a Union rout as Confederates under Major General <PERSON> pursue them to Strawberry Plains, Tennessee.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The three-day <a href=\"https://wikipedia.org/wiki/Battle_of_Bull%27s_Gap\" title=\"Battle of Bull's Gap\">Battle of Bull's Gap</a> ends in a <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> rout as <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederates</a> under Major General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ridge\" title=\"<PERSON>\"><PERSON></a> pursue them to <a href=\"https://wikipedia.org/wiki/Strawberry_Plains,_Tennessee\" title=\"Strawberry Plains, Tennessee\">Strawberry Plains, Tennessee</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The three-day <a href=\"https://wikipedia.org/wiki/Battle_of_Bull%27s_Gap\" title=\"Battle of Bull's Gap\">Battle of Bull's Gap</a> ends in a <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> rout as <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederates</a> under Major General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ridge\" title=\"John <PERSON>ridge\"><PERSON></a> pursue them to <a href=\"https://wikipedia.org/wiki/Strawberry_Plains,_Tennessee\" title=\"Strawberry Plains, Tennessee\">Strawberry Plains, Tennessee</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Bull's Gap", "link": "https://wikipedia.org/wiki/Battle_of_Bull%27s_Gap"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Strawberry Plains, Tennessee", "link": "https://wikipedia.org/wiki/Strawberry_Plains,_Tennessee"}]}, {"year": "1887", "text": "Bloody Sunday clashes in central London.", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Bloody_Sunday_(1887)\" title=\"Bloody Sunday (1887)\">Bloody Sunday</a> clashes in <a href=\"https://wikipedia.org/wiki/Central_London\" title=\"Central London\">central London</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bloody_Sunday_(1887)\" title=\"Bloody Sunday (1887)\">Bloody Sunday</a> clashes in <a href=\"https://wikipedia.org/wiki/Central_London\" title=\"Central London\">central London</a>.", "links": [{"title": "Bloody Sunday (1887)", "link": "https://wikipedia.org/wiki/Bloody_Sunday_(1887)"}, {"title": "Central London", "link": "https://wikipedia.org/wiki/Central_London"}]}, {"year": "1901", "text": "The 1901 Caister lifeboat disaster.", "html": "1901 - The <a href=\"https://wikipedia.org/wiki/1901_Caister_lifeboat_disaster\" title=\"1901 Caister lifeboat disaster\">1901 Caister lifeboat disaster</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1901_Caister_lifeboat_disaster\" title=\"1901 Caister lifeboat disaster\">1901 Caister lifeboat disaster</a>.", "links": [{"title": "1901 Caister lifeboat disaster", "link": "https://wikipedia.org/wiki/1901_Caister_lifeboat_disaster"}]}, {"year": "1914", "text": "Zaian War: Berber tribesmen inflict the heaviest defeat of French forces in Morocco at the Battle of El Herri.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Zaian_War\" title=\"Zaian War\">Zaian War</a>: Berber tribesmen inflict the heaviest defeat of French forces in Morocco at the <a href=\"https://wikipedia.org/wiki/Battle_of_El_Herri\" title=\"Battle of El Herri\">Battle of El Herri</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zaian_War\" title=\"Zaian War\">Zaian War</a>: Berber tribesmen inflict the heaviest defeat of French forces in Morocco at the <a href=\"https://wikipedia.org/wiki/Battle_of_El_Herri\" title=\"Battle of El Herri\">Battle of El Herri</a>.", "links": [{"title": "Zaian War", "link": "https://wikipedia.org/wiki/Zaian_War"}, {"title": "Battle of El Herri", "link": "https://wikipedia.org/wiki/Battle_of_El_Herri"}]}, {"year": "1916", "text": "World War I: Prime Minister of Australia <PERSON> is expelled from the Labor Party over his support for conscription.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is expelled from the <a href=\"https://wikipedia.org/wiki/Australian_Labor_Party\" title=\"Australian Labor Party\">Labor Party</a> over his support for <a href=\"https://wikipedia.org/wiki/Conscription_in_Australia\" title=\"Conscription in Australia\">conscription</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is expelled from the <a href=\"https://wikipedia.org/wiki/Australian_Labor_Party\" title=\"Australian Labor Party\">Labor Party</a> over his support for <a href=\"https://wikipedia.org/wiki/Conscription_in_Australia\" title=\"Conscription in Australia\">conscription</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Australian Labor Party", "link": "https://wikipedia.org/wiki/Australian_Labor_Party"}, {"title": "Conscription in Australia", "link": "https://wikipedia.org/wiki/Conscription_in_Australia"}]}, {"year": "1917", "text": "World War I: beginning of the First Battle of Monte Grappa (in Italy known as the \"First Battle of the Piave\"). The Austro-Hungarian Armed Forces, despite help from the German Alpenkorps and numerical superiority, will fail their offensive against the Italian Army now led by its new chief of staff <PERSON><PERSON>.", "html": "1917 - World War I: beginning of the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Monte_Grappa\" class=\"mw-redirect\" title=\"First Battle of Monte Grappa\">First Battle of Monte Grappa</a> (in Italy known as the \"First Battle of the Piave\"). The <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Armed_Forces\" title=\"Austro-Hungarian Armed Forces\">Austro-Hungarian Armed Forces</a>, despite help from the German <a href=\"https://wikipedia.org/wiki/Alpenkorps\" class=\"mw-redirect\" title=\"Alpenkorps\">Alpenkorps</a> and numerical superiority, will fail their offensive against the <a href=\"https://wikipedia.org/wiki/Italian_Army\" title=\"Italian Army\">Italian Army</a> now led by its new chief of staff <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "World War I: beginning of the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Monte_Grappa\" class=\"mw-redirect\" title=\"First Battle of Monte Grappa\">First Battle of Monte Grappa</a> (in Italy known as the \"First Battle of the Piave\"). The <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Armed_Forces\" title=\"Austro-Hungarian Armed Forces\">Austro-Hungarian Armed Forces</a>, despite help from the German <a href=\"https://wikipedia.org/wiki/Alpenkorps\" class=\"mw-redirect\" title=\"Alpenkorps\">Alpenkorps</a> and numerical superiority, will fail their offensive against the <a href=\"https://wikipedia.org/wiki/Italian_Army\" title=\"Italian Army\">Italian Army</a> now led by its new chief of staff <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "First Battle of Monte Grappa", "link": "https://wikipedia.org/wiki/First_Battle_of_Monte_Grappa"}, {"title": "Austro-Hungarian Armed Forces", "link": "https://wikipedia.org/wiki/Austro-Hungarian_Armed_Forces"}, {"title": "Alpenkorps", "link": "https://wikipedia.org/wiki/Alpenkorps"}, {"title": "Italian Army", "link": "https://wikipedia.org/wiki/Italian_Army"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "World War I: Allied troops occupy Constantinople, the capital of the Ottoman Empire.", "html": "1918 - World War I: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied troops</a> <a href=\"https://wikipedia.org/wiki/Occupation_of_Constantinople\" class=\"mw-redirect\" title=\"Occupation of Constantinople\">occupy Constantinople</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied troops</a> <a href=\"https://wikipedia.org/wiki/Occupation_of_Constantinople\" class=\"mw-redirect\" title=\"Occupation of Constantinople\">occupy Constantinople</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}, {"title": "Occupation of Constantinople", "link": "https://wikipedia.org/wiki/Occupation_of_Constantinople"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1922", "text": "The United States Supreme Court upholds mandatory vaccinations for public school students in <PERSON><PERSON><PERSON> v. King.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> upholds <a href=\"https://wikipedia.org/wiki/Mandatory_vaccination\" class=\"mw-redirect\" title=\"Mandatory vaccination\">mandatory vaccinations</a> for public school students in <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_v._King\" title=\"<PERSON><PERSON><PERSON> v. King\"><PERSON><PERSON><PERSON> v. King</a></i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> upholds <a href=\"https://wikipedia.org/wiki/Mandatory_vaccination\" class=\"mw-redirect\" title=\"Mandatory vaccination\">mandatory vaccinations</a> for public school students in <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_v._King\" title=\"<PERSON><PERSON><PERSON> v. King\"><PERSON><PERSON><PERSON> v. King</a></i>.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Mandatory vaccination", "link": "https://wikipedia.org/wiki/Mandatory_vaccination"}, {"title": "<PERSON><PERSON><PERSON> v. King", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_v._King"}]}, {"year": "1927", "text": "The Holland Tunnel opens to traffic as the first Hudson River vehicle tunnel linking New Jersey to New York City.", "html": "1927 - The <a href=\"https://wikipedia.org/wiki/Holland_Tunnel\" title=\"Holland Tunnel\">Holland Tunnel</a> opens to traffic as the first <a href=\"https://wikipedia.org/wiki/Hudson_River\" title=\"Hudson River\">Hudson River</a> vehicle tunnel linking <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a> to New York City.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Holland_Tunnel\" title=\"Holland Tunnel\">Holland Tunnel</a> opens to traffic as the first <a href=\"https://wikipedia.org/wiki/Hudson_River\" title=\"Hudson River\">Hudson River</a> vehicle tunnel linking <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a> to New York City.", "links": [{"title": "Holland Tunnel", "link": "https://wikipedia.org/wiki/Holland_Tunnel"}, {"title": "Hudson River", "link": "https://wikipedia.org/wiki/Hudson_River"}, {"title": "New Jersey", "link": "https://wikipedia.org/wiki/New_Jersey"}]}, {"year": "1940", "text": "<PERSON>'s animated musical film <PERSON><PERSON><PERSON> is first released at New York's Broadway Theatre, on the first night of a roadshow.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON> Disney</a>'s animated musical film <i><a href=\"https://wikipedia.org/wiki/Fantasia_(1940_film)\" title=\"Fantasia (1940 film)\">Fantasia</a></i> is first released at New York's <a href=\"https://wikipedia.org/wiki/Broadway_Theatre_(53rd_Street)\" title=\"Broadway Theatre (53rd Street)\">Broadway Theatre</a>, on the first night of a roadshow.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\">Walt Disney</a>'s animated musical film <i><a href=\"https://wikipedia.org/wiki/Fantasia_(1940_film)\" title=\"Fantasia (1940 film)\">Fantasia</a></i> is first released at New York's <a href=\"https://wikipedia.org/wiki/Broadway_Theatre_(53rd_Street)\" title=\"Broadway Theatre (53rd Street)\">Broadway Theatre</a>, on the first night of a roadshow.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fantasia (1940 film)", "link": "https://wikipedia.org/wiki/Fantasia_(1940_film)"}, {"title": "Broadway Theatre (53rd Street)", "link": "https://wikipedia.org/wiki/Broadway_Theatre_(53rd_Street)"}]}, {"year": "1941", "text": "World War II: The aircraft carrier HMS Ark Royal is torpedoed by U-81, sinking the following day.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/HMS_Ark_Royal_(91)\" title=\"HMS Ark Royal (91)\">HMS <i>Ark Royal</i></a> is torpedoed by <a href=\"https://wikipedia.org/wiki/German_submarine_U-81_(1941)\" title=\"German submarine U-81 (1941)\"><i>U-81</i></a>, sinking the <a href=\"https://wikipedia.org/wiki/November_14\" title=\"November 14\">following day</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/HMS_Ark_Royal_(91)\" title=\"HMS Ark Royal (91)\">HMS <i>Ark Royal</i></a> is torpedoed by <a href=\"https://wikipedia.org/wiki/German_submarine_U-81_(1941)\" title=\"German submarine U-81 (1941)\"><i>U-81</i></a>, sinking the <a href=\"https://wikipedia.org/wiki/November_14\" title=\"November 14\">following day</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Aircraft carrier", "link": "https://wikipedia.org/wiki/Aircraft_carrier"}, {"title": "HMS Ark Royal (91)", "link": "https://wikipedia.org/wiki/HMS_Ark_Royal_(91)"}, {"title": "German submarine U-81 (1941)", "link": "https://wikipedia.org/wiki/German_submarine_U-81_(1941)"}, {"title": "November 14", "link": "https://wikipedia.org/wiki/November_14"}]}, {"year": "1942", "text": "World War II: Naval Battle of Guadalcanal: U.S. and Japanese ships engage in an intense, close-quarters surface naval engagement during the Guadalcanal Campaign.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Naval_Battle_of_Guadalcanal\" title=\"Naval Battle of Guadalcanal\">Naval Battle of Guadalcanal</a>: U.S. and Japanese ships engage in an intense, close-quarters surface naval engagement during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Naval_Battle_of_Guadalcanal\" title=\"Naval Battle of Guadalcanal\">Naval Battle of Guadalcanal</a>: U.S. and Japanese ships engage in an intense, close-quarters surface naval engagement during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a>.", "links": [{"title": "Naval Battle of Guadalcanal", "link": "https://wikipedia.org/wiki/Naval_Battle_of_Guadalcanal"}, {"title": "Guadalcanal Campaign", "link": "https://wikipedia.org/wiki/Guadalcanal_Campaign"}]}, {"year": "1947", "text": "The Soviet Union completes development of the AK-47, one of the first proper assault rifles.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> completes development of the <a href=\"https://wikipedia.org/wiki/AK-47\" title=\"AK-47\">AK-47</a>, one of the first proper <a href=\"https://wikipedia.org/wiki/Assault_rifle\" title=\"Assault rifle\">assault rifles</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> completes development of the <a href=\"https://wikipedia.org/wiki/AK-47\" title=\"AK-47\">AK-47</a>, one of the first proper <a href=\"https://wikipedia.org/wiki/Assault_rifle\" title=\"Assault rifle\">assault rifles</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "AK-47", "link": "https://wikipedia.org/wiki/AK-47"}, {"title": "Assault rifle", "link": "https://wikipedia.org/wiki/Assault_rifle"}]}, {"year": "1950", "text": "General <PERSON>, President of Venezuela, is assassinated in Caracas.", "html": "1950 - General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a>, is assassinated in <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas</a>.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a>, is assassinated in <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}, {"title": "Caracas", "link": "https://wikipedia.org/wiki/Caracas"}]}, {"year": "1954", "text": "Great Britain defeats France to capture the first ever Rugby League World Cup in Paris in front of around 30,000 spectators.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Great_Britain_national_rugby_league_team\" title=\"Great Britain national rugby league team\">Great Britain</a> <a href=\"https://wikipedia.org/wiki/1954_Rugby_League_World_Cup\" title=\"1954 Rugby League World Cup\">defeats</a> <a href=\"https://wikipedia.org/wiki/France_national_rugby_league_team\" title=\"France national rugby league team\">France</a> to capture the first ever <a href=\"https://wikipedia.org/wiki/Rugby_League_World_Cup\" title=\"Rugby League World Cup\">Rugby League World Cup</a> in Paris in front of around 30,000 spectators.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Britain_national_rugby_league_team\" title=\"Great Britain national rugby league team\">Great Britain</a> <a href=\"https://wikipedia.org/wiki/1954_Rugby_League_World_Cup\" title=\"1954 Rugby League World Cup\">defeats</a> <a href=\"https://wikipedia.org/wiki/France_national_rugby_league_team\" title=\"France national rugby league team\">France</a> to capture the first ever <a href=\"https://wikipedia.org/wiki/Rugby_League_World_Cup\" title=\"Rugby League World Cup\">Rugby League World Cup</a> in Paris in front of around 30,000 spectators.", "links": [{"title": "Great Britain national rugby league team", "link": "https://wikipedia.org/wiki/Great_Britain_national_rugby_league_team"}, {"title": "1954 Rugby League World Cup", "link": "https://wikipedia.org/wiki/1954_Rugby_League_World_Cup"}, {"title": "France national rugby league team", "link": "https://wikipedia.org/wiki/France_national_rugby_league_team"}, {"title": "Rugby League World Cup", "link": "https://wikipedia.org/wiki/Rugby_League_World_Cup"}]}, {"year": "1956", "text": "The Supreme Court of the United States affirmed a lower court ruling that invalidated Alabama laws requiring segregated buses, thus ending the Montgomery bus boycott.", "html": "1956 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> affirmed a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_v<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> v<PERSON>\">lower court ruling</a> that invalidated <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> laws requiring segregated buses, thus ending the <a href=\"https://wikipedia.org/wiki/Montgomery_bus_boycott\" title=\"Montgomery bus boycott\">Montgomery bus boycott</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> affirmed a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_v<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> v<PERSON>\">lower court ruling</a> that invalidated <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> laws requiring segregated buses, thus ending the <a href=\"https://wikipedia.org/wiki/Montgomery_bus_boycott\" title=\"Montgomery bus boycott\">Montgomery bus boycott</a>.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "<PERSON><PERSON><PERSON> v. <PERSON>le", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_v._<PERSON><PERSON>"}, {"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "Montgomery bus boycott", "link": "https://wikipedia.org/wiki/Montgomery_bus_boycott"}]}, {"year": "1965", "text": "Fire and sinking of SS Yarmouth Castle, 87 dead. ", "html": "1965 - Fire and sinking of <a href=\"https://wikipedia.org/wiki/SS_Yarmouth_Castle\" title=\"SS Yarmouth Castle\">SS Yarmouth Castle</a>, 87 dead. ", "no_year_html": "Fire and sinking of <a href=\"https://wikipedia.org/wiki/SS_Yarmouth_Castle\" title=\"SS Yarmouth Castle\">SS Yarmouth Castle</a>, 87 dead. ", "links": [{"title": "SS Yarmouth Castle", "link": "https://wikipedia.org/wiki/SS_Yarmouth_Castle"}]}, {"year": "1966", "text": "In response to Fatah raids against Israelis near the West Bank border, Israel launches an attack on the village of As-Samu.", "html": "1966 - In response to <a href=\"https://wikipedia.org/wiki/Fatah\" title=\"Fatah\"><PERSON><PERSON></a> raids against Israelis near the <a href=\"https://wikipedia.org/wiki/West_Bank\" title=\"West Bank\">West Bank</a> border, Israel <a href=\"https://wikipedia.org/wiki/Samu_Incident\" class=\"mw-redirect\" title=\"Samu Incident\">launches an attack</a> on the village of <a href=\"https://wikipedia.org/wiki/As-Samu\" title=\"As-Samu\">As-Samu</a>.", "no_year_html": "In response to <a href=\"https://wikipedia.org/wiki/Fatah\" title=\"Fatah\">Fat<PERSON></a> raids against Israelis near the <a href=\"https://wikipedia.org/wiki/West_Bank\" title=\"West Bank\">West Bank</a> border, Israel <a href=\"https://wikipedia.org/wiki/Samu_Incident\" class=\"mw-redirect\" title=\"Samu Incident\">launches an attack</a> on the village of <a href=\"https://wikipedia.org/wiki/As-Samu\" title=\"As-Samu\">As-Samu</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ah"}, {"title": "West Bank", "link": "https://wikipedia.org/wiki/West_Bank"}, {"title": "Samu Incident", "link": "https://wikipedia.org/wiki/Samu_Incident"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/As-<PERSON>u"}]}, {"year": "1966", "text": "All Nippon Airways Flight 533 crashes into the Seto Inland Sea near Matsuyama Airport in Japan, killing 50 people.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/All_Nippon_Airways_Flight_533\" title=\"All Nippon Airways Flight 533\">All Nippon Airways Flight 533</a> crashes into the <a href=\"https://wikipedia.org/wiki/Seto_Inland_Sea\" title=\"Seto Inland Sea\">Seto Inland Sea</a> near <a href=\"https://wikipedia.org/wiki/Matsuyama_Airport\" title=\"Matsuyama Airport\">Matsuyama Airport</a> in Japan, killing 50 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/All_Nippon_Airways_Flight_533\" title=\"All Nippon Airways Flight 533\">All Nippon Airways Flight 533</a> crashes into the <a href=\"https://wikipedia.org/wiki/Seto_Inland_Sea\" title=\"Seto Inland Sea\">Seto Inland Sea</a> near <a href=\"https://wikipedia.org/wiki/Matsuyama_Airport\" title=\"Matsuyama Airport\">Matsuyama Airport</a> in Japan, killing 50 people.", "links": [{"title": "All Nippon Airways Flight 533", "link": "https://wikipedia.org/wiki/All_Nippon_Airways_Flight_533"}, {"title": "Seto Inland Sea", "link": "https://wikipedia.org/wiki/Seto_Inland_Sea"}, {"title": "Matsuyama Airport", "link": "https://wikipedia.org/wiki/Matsuyama_Airport"}]}, {"year": "1969", "text": "Vietnam War: Anti-war protesters in Washington, D.C. stage a symbolic March Against Death.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Opposition_to_United_States_involvement_in_the_Vietnam_War\" title=\"Opposition to United States involvement in the Vietnam War\">Anti-war protesters</a> in Washington, D.C. stage a symbolic <i>March Against Death</i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Opposition_to_United_States_involvement_in_the_Vietnam_War\" title=\"Opposition to United States involvement in the Vietnam War\">Anti-war protesters</a> in Washington, D.C. stage a symbolic <i>March Against Death</i>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Opposition to United States involvement in the Vietnam War", "link": "https://wikipedia.org/wiki/Opposition_to_United_States_involvement_in_the_Vietnam_War"}]}, {"year": "1970", "text": "Bhola cyclone: A 240 km/h (150 mph) tropical cyclone hits the densely populated Ganges Delta region of East Pakistan (now Bangladesh), killing an estimated 500,000 people in one night.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/1970_Bhola_cyclone\" title=\"1970 Bhola cyclone\">Bhola cyclone</a>: A 240 km/h (150 mph) <a href=\"https://wikipedia.org/wiki/Tropical_cyclone\" title=\"Tropical cyclone\">tropical cyclone</a> hits the densely populated <a href=\"https://wikipedia.org/wiki/Ganges_Delta\" title=\"Ganges Delta\">Ganges Delta</a> region of <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a> (now <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>), killing an estimated 500,000 people in one night.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1970_Bhola_cyclone\" title=\"1970 Bhola cyclone\">Bhola cyclone</a>: A 240 km/h (150 mph) <a href=\"https://wikipedia.org/wiki/Tropical_cyclone\" title=\"Tropical cyclone\">tropical cyclone</a> hits the densely populated <a href=\"https://wikipedia.org/wiki/Ganges_Delta\" title=\"Ganges Delta\">Ganges Delta</a> region of <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a> (now <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>), killing an estimated 500,000 people in one night.", "links": [{"title": "1970 Bhola cyclone", "link": "https://wikipedia.org/wiki/1970_Bhola_cyclone"}, {"title": "Tropical cyclone", "link": "https://wikipedia.org/wiki/Tropical_cyclone"}, {"title": "Ganges Delta", "link": "https://wikipedia.org/wiki/Ganges_Delta"}, {"title": "East Pakistan", "link": "https://wikipedia.org/wiki/East_Pakistan"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}]}, {"year": "1982", "text": "<PERSON> defeats <PERSON><PERSON> in a boxing match held in Las Vegas. <PERSON>'s subsequent death (on November 18) leads to significant changes in the sport.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a> in a <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a> match held in <a href=\"https://wikipedia.org/wiki/Las_Vegas\" title=\"Las Vegas\">Las Vegas</a>. <PERSON>'s subsequent death (on November 18) leads to significant changes in the sport.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in a <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a> match held in <a href=\"https://wikipedia.org/wiki/Las_Vegas\" title=\"Las Vegas\">Las Vegas</a>. <PERSON>'s subsequent death (on November 18) leads to significant changes in the sport.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Boxing", "link": "https://wikipedia.org/wiki/Boxing"}, {"title": "Las Vegas", "link": "https://wikipedia.org/wiki/Las_Vegas"}]}, {"year": "1982", "text": "The Vietnam Veterans Memorial is dedicated in Washington, D.C. after a march to its site by thousands of Vietnam War veterans.", "html": "1982 - The <a href=\"https://wikipedia.org/wiki/Vietnam_Veterans_Memorial\" title=\"Vietnam Veterans Memorial\">Vietnam Veterans Memorial</a> is dedicated in Washington, D.C. after a march to its site by thousands of <a href=\"https://wikipedia.org/wiki/Vietnam_veteran\" title=\"Vietnam veteran\">Vietnam War veterans</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Vietnam_Veterans_Memorial\" title=\"Vietnam Veterans Memorial\">Vietnam Veterans Memorial</a> is dedicated in Washington, D.C. after a march to its site by thousands of <a href=\"https://wikipedia.org/wiki/Vietnam_veteran\" title=\"Vietnam veteran\">Vietnam War veterans</a>.", "links": [{"title": "Vietnam Veterans Memorial", "link": "https://wikipedia.org/wiki/Vietnam_Veterans_Memorial"}, {"title": "Vietnam veteran", "link": "https://wikipedia.org/wiki/Vietnam_veteran"}]}, {"year": "1985", "text": "The volcano Nevado del Ruiz erupts and melts a glacier, causing a lahar (volcanic mudslide) that buries Armero, Colombia, killing approximately 23,000 people.", "html": "1985 - The volcano <a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Nevado del <PERSON>\">N<PERSON><PERSON></a> erupts and melts a <a href=\"https://wikipedia.org/wiki/Glacier\" title=\"Glacier\">glacier</a>, causing a <a href=\"https://wikipedia.org/wiki/Lahar\" title=\"Lahar\">lahar</a> (volcanic mudslide) that <a href=\"https://wikipedia.org/wiki/Armero_tragedy\" title=\"Armero tragedy\">buries Armero, Colombia</a>, killing approximately 23,000 people.", "no_year_html": "The volcano <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>eva<PERSON>\"><PERSON><PERSON><PERSON></a> erupts and melts a <a href=\"https://wikipedia.org/wiki/Glacier\" title=\"Glacier\">glacier</a>, causing a <a href=\"https://wikipedia.org/wiki/Lahar\" title=\"Lahar\">lahar</a> (volcanic mudslide) that <a href=\"https://wikipedia.org/wiki/Armero_tragedy\" title=\"Armero tragedy\">buries Armero, Colombia</a>, killing approximately 23,000 people.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Glacier", "link": "https://wikipedia.org/wiki/Glacier"}, {"title": "Lahar", "link": "https://wikipedia.org/wiki/Lahar"}, {"title": "Armero tragedy", "link": "https://wikipedia.org/wiki/Armero_tragedy"}]}, {"year": "1985", "text": "<PERSON> is sworn in as Miami's first Cuban-born mayor.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Xavier_Su%C3%A1rez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is sworn in as <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>'s first Cuban-born mayor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xavier_Su%C3%A1rez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is sworn in as <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>'s first Cuban-born mayor.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Xavier_Su%C3%A1rez"}, {"title": "Miami", "link": "https://wikipedia.org/wiki/Miami"}]}, {"year": "1986", "text": "The Late, Late Breakfast Show incident leads to death of 24 year old <PERSON> and show cancellation.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/1986\" title=\"1986\">1986</a> - <a href=\"https://wikipedia.org/wiki/The_Late,_Late_Breakfast_Show\" title=\"The Late, Late Breakfast Show\">The Late, Late Breakfast Show</a> incident leads to death of 24 year old <PERSON> and show cancellation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1986\" title=\"1986\">1986</a> - <a href=\"https://wikipedia.org/wiki/The_Late,_Late_Breakfast_Show\" title=\"The Late, Late Breakfast Show\">The Late, Late Breakfast Show</a> incident leads to death of 24 year old <PERSON> and show cancellation.", "links": [{"title": "1986", "link": "https://wikipedia.org/wiki/1986"}, {"title": "The Late, Late Breakfast Show", "link": "https://wikipedia.org/wiki/The_Late,_Late_Breakfast_Show"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, the present Prince of Liechtenstein, begins his reign on the death of his father.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Prince_of_Liechtenstein\" title=\"<PERSON><PERSON><PERSON>, Prince of Liechtenstein\"><PERSON><PERSON><PERSON> II</a>, the present <a href=\"https://wikipedia.org/wiki/Prince_of_Liechtenstein\" class=\"mw-redirect\" title=\"Prince of Liechtenstein\">Prince of Liechtenstein</a>, begins his reign on the death of his father.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Prince_of_Liechtenstein\" title=\"<PERSON><PERSON><PERSON>, Prince of Liechtenstein\"><PERSON><PERSON><PERSON> II</a>, the present <a href=\"https://wikipedia.org/wiki/Prince_of_Liechtenstein\" class=\"mw-redirect\" title=\"Prince of Liechtenstein\">Prince of Liechtenstein</a>, begins his reign on the death of his father.", "links": [{"title": "<PERSON><PERSON><PERSON>, Prince of Liechtenstein", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Prince_of_Liechtenstein"}, {"title": "Prince of Liechtenstein", "link": "https://wikipedia.org/wiki/Prince_of_Liechtenstein"}]}, {"year": "1990", "text": "In Aramoana, New Zealand, <PERSON> shoots dead 13 people in a massacre before being tracked down and killed by police the next day.", "html": "1990 - In <a href=\"https://wikipedia.org/wiki/Aramoana\" title=\"Aramoana\">Aramoana</a>, New Zealand, <PERSON> shoots dead 13 people in <a href=\"https://wikipedia.org/wiki/Aramoana_massacre\" title=\"Aramoana massacre\">a massacre</a> before being tracked down and killed by police the next day.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Aramoana\" title=\"Aramoana\">Aramoana</a>, New Zealand, <PERSON> shoots dead 13 people in <a href=\"https://wikipedia.org/wiki/Aramoana_massacre\" title=\"Aramoana massacre\">a massacre</a> before being tracked down and killed by police the next day.", "links": [{"title": "Ara<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aramoana"}, {"title": "Aramoana massacre", "link": "https://wikipedia.org/wiki/Aramoana_massacre"}]}, {"year": "1991", "text": "The Republic of Karelia, an autonomous republic of Russia, is formed from the former Karelian ASSR.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/Republic_of_Karelia\" title=\"Republic of Karelia\">Republic of Karelia</a>, an autonomous <a href=\"https://wikipedia.org/wiki/Republics_of_Russia\" title=\"Republics of Russia\">republic of Russia</a>, is formed from the former <a href=\"https://wikipedia.org/wiki/Karelian_Autonomous_Soviet_Socialist_Republic\" title=\"Karelian Autonomous Soviet Socialist Republic\">Karelian ASSR</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_Karelia\" title=\"Republic of Karelia\">Republic of Karelia</a>, an autonomous <a href=\"https://wikipedia.org/wiki/Republics_of_Russia\" title=\"Republics of Russia\">republic of Russia</a>, is formed from the former <a href=\"https://wikipedia.org/wiki/Karelian_Autonomous_Soviet_Socialist_Republic\" title=\"Karelian Autonomous Soviet Socialist Republic\">Karelian ASSR</a>.", "links": [{"title": "Republic of Karelia", "link": "https://wikipedia.org/wiki/Republic_of_Karelia"}, {"title": "Republics of Russia", "link": "https://wikipedia.org/wiki/Republics_of_Russia"}, {"title": "Karelian Autonomous Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Karelian_Autonomous_Soviet_Socialist_Republic"}]}, {"year": "1992", "text": "The High Court of Australia rules in <PERSON> v The Queen that although there is no absolute right to have publicly funded counsel, in most circumstances a judge should grant any request for an adjournment or stay when an accused is unrepresented.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/High_Court_of_Australia\" title=\"High Court of Australia\">High Court of Australia</a> rules in <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v_The_Queen\" title=\"<PERSON> v <PERSON> Queen\"><PERSON> v The Queen</a></i> that although there is no absolute right to have publicly funded counsel, in most circumstances a judge should grant any request for an adjournment or stay when an accused is unrepresented.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/High_Court_of_Australia\" title=\"High Court of Australia\">High Court of Australia</a> rules in <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v_The_Queen\" title=\"<PERSON> v <PERSON> Queen\"><PERSON> v <PERSON> Queen</a></i> that although there is no absolute right to have publicly funded counsel, in most circumstances a judge should grant any request for an adjournment or stay when an accused is unrepresented.", "links": [{"title": "High Court of Australia", "link": "https://wikipedia.org/wiki/High_Court_of_Australia"}, {"title": "<PERSON> v The Queen", "link": "https://wikipedia.org/wiki/<PERSON>_v_The_Queen"}]}, {"year": "1993", "text": "China Northern Airlines Flight 6901 crashes on approach to Ürümqi Diwopu International Airport in Ürümqi, China, killing 12 people.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/China_Northern_Airlines_Flight_6901\" title=\"China Northern Airlines Flight 6901\">China Northern Airlines Flight 6901</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi_Diwopu_International_Airport\" title=\"Ürümqi Diwopu International Airport\">Ürümqi Diwopu International Airport</a> in <a href=\"https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi\" title=\"Ürümqi\">Ürümqi</a>, China, killing 12 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Northern_Airlines_Flight_6901\" title=\"China Northern Airlines Flight 6901\">China Northern Airlines Flight 6901</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi_Diwopu_International_Airport\" title=\"Ürümqi Diwopu International Airport\">Ürümqi Diwopu International Airport</a> in <a href=\"https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi\" title=\"Ürümqi\">Ürümqi</a>, China, killing 12 people.", "links": [{"title": "China Northern Airlines Flight 6901", "link": "https://wikipedia.org/wiki/China_Northern_Airlines_Flight_6901"}, {"title": "Ürümqi Diwopu International Airport", "link": "https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi_Diwopu_International_Airport"}, {"title": "Ürümqi", "link": "https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi"}]}, {"year": "1994", "text": "In a referendum, voters in Sweden decide to join the European Union.", "html": "1994 - In a <a href=\"https://wikipedia.org/wiki/1994_Swedish_European_Union_membership_referendum\" title=\"1994 Swedish European Union membership referendum\">referendum</a>, voters in Sweden decide to join the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "no_year_html": "In a <a href=\"https://wikipedia.org/wiki/1994_Swedish_European_Union_membership_referendum\" title=\"1994 Swedish European Union membership referendum\">referendum</a>, voters in Sweden decide to join the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "links": [{"title": "1994 Swedish European Union membership referendum", "link": "https://wikipedia.org/wiki/1994_Swedish_European_Union_membership_referendum"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "1995", "text": "Mozambique becomes the first state to join the Commonwealth of Nations without having been part of the former British Empire.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a> becomes the first state to join the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a> without having been part of the former <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a> becomes the first state to join the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a> without having been part of the former <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "links": [{"title": "Mozambique", "link": "https://wikipedia.org/wiki/Mozambique"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}]}, {"year": "1995", "text": "Nigeria Airways Flight 357 crashes at Kaduna International Airport in Kaduna, Nigeria, killing 11 people and injuring 66.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Nigeria_Airways_Flight_357\" title=\"Nigeria Airways Flight 357\">Nigeria Airways Flight 357</a> crashes at <a href=\"https://wikipedia.org/wiki/Kaduna_International_Airport\" title=\"Kaduna International Airport\">Kaduna International Airport</a> in <a href=\"https://wikipedia.org/wiki/Kaduna_(city)\" class=\"mw-redirect\" title=\"Kaduna (city)\">Kaduna</a>, Nigeria, killing 11 people and injuring 66.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nigeria_Airways_Flight_357\" title=\"Nigeria Airways Flight 357\">Nigeria Airways Flight 357</a> crashes at <a href=\"https://wikipedia.org/wiki/Kaduna_International_Airport\" title=\"Kaduna International Airport\">Kaduna International Airport</a> in <a href=\"https://wikipedia.org/wiki/Kaduna_(city)\" class=\"mw-redirect\" title=\"Kaduna (city)\">Kaduna</a>, Nigeria, killing 11 people and injuring 66.", "links": [{"title": "Nigeria Airways Flight 357", "link": "https://wikipedia.org/wiki/Nigeria_Airways_Flight_357"}, {"title": "Kaduna International Airport", "link": "https://wikipedia.org/wiki/Kaduna_International_Airport"}, {"title": "Kaduna (city)", "link": "https://wikipedia.org/wiki/Kaduna_(city)"}]}, {"year": "1996", "text": "As part of the Great Internet Mersenne Prime Search (GIMPS) project, <PERSON> discovers the project's first Mersenne prime number, \n  \n    \n      \n        \n          2\n          \n            1398269\n          \n        \n        −\n        1\n      \n    \n    {\\displaystyle 2^{1398269}-1}\n  \n, a number with 420,921 digits.", "html": "1996 - As part of the <a href=\"https://wikipedia.org/wiki/Great_Internet_Mersenne_Prime_Search\" title=\"Great Internet Mersenne Prime Search\">Great Internet Mersenne Prime Search</a> (GIMPS) project, <PERSON> discovers the project's first <a href=\"https://wikipedia.org/wiki/Mersenne_prime\" title=\"Mersenne prime\">Mersenne prime</a> number, <span class=\"mwe-math-element\"><span class=\"mwe-math-mathml-inline mwe-math-mathml-a11y\" style=\"display: none;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\" alttext=\"{\\displaystyle 2^{1398269}-1}\">\n <semantics>\n <mrow class=\"MJX-TeXAtom-ORD\">\n <mstyle displaystyle=\"true\" scriptlevel=\"0\">\n <msup>\n <mn>2</mn>\n <mrow class=\"MJX-TeXAtom-ORD\">\n <mn>1398269</mn>\n </mrow>\n </msup>\n <mo>−<!-- − --></mo>\n <mn>1</mn>\n </mstyle>\n </mrow>\n <annotation encoding=\"application/x-tex\">{\\displaystyle 2^{1398269}-1}</annotation>\n </semantics>\n</math></span><img src=\"https://wikimedia.org/api/rest_v1/media/math/render/svg/f53379a69fd142675709f2e96f208e9f303ffb60\" class=\"mwe-math-fallback-image-inline mw-invert skin-invert\" aria-hidden=\"true\" style=\"vertical-align: -0.505ex; width:11.151ex; height:2.843ex;\" alt=\"{\\displaystyle 2^{1398269}-1}\"></span>, a number with 420,921 digits.", "no_year_html": "As part of the <a href=\"https://wikipedia.org/wiki/Great_Internet_Mersenne_Prime_Search\" title=\"Great Internet Mersenne Prime Search\">Great Internet Mersenne Prime Search</a> (GIMPS) project, <PERSON> discovers the project's first <a href=\"https://wikipedia.org/wiki/Mersenne_prime\" title=\"Mersenne prime\">Mersenne prime</a> number, <span class=\"mwe-math-element\"><span class=\"mwe-math-mathml-inline mwe-math-mathml-a11y\" style=\"display: none;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\" alttext=\"{\\displaystyle 2^{1398269}-1}\">\n <semantics>\n <mrow class=\"MJX-TeXAtom-ORD\">\n <mstyle displaystyle=\"true\" scriptlevel=\"0\">\n <msup>\n <mn>2</mn>\n <mrow class=\"MJX-TeXAtom-ORD\">\n <mn>1398269</mn>\n </mrow>\n </msup>\n <mo>−<!-- − --></mo>\n <mn>1</mn>\n </mstyle>\n </mrow>\n <annotation encoding=\"application/x-tex\">{\\displaystyle 2^{1398269}-1}</annotation>\n </semantics>\n</math></span><img src=\"https://wikimedia.org/api/rest_v1/media/math/render/svg/f53379a69fd142675709f2e96f208e9f303ffb60\" class=\"mwe-math-fallback-image-inline mw-invert skin-invert\" aria-hidden=\"true\" style=\"vertical-align: -0.505ex; width:11.151ex; height:2.843ex;\" alt=\"{\\displaystyle 2^{1398269}-1}\"></span>, a number with 420,921 digits.", "links": [{"title": "Great Internet Mersenne Prime Search", "link": "https://wikipedia.org/wiki/Great_Internet_Mersenne_Prime_Search"}, {"title": "<PERSON><PERSON><PERSON> prime", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_prime"}]}, {"year": "2000", "text": "Philippine House Speaker <PERSON> passes the articles of impeachment against Philippine President <PERSON>.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippine</a> House Speaker <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> passes the articles of impeachment against <a href=\"https://wikipedia.org/wiki/Philippine_President\" class=\"mw-redirect\" title=\"Philippine President\">Philippine President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippine</a> House Speaker <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> passes the articles of impeachment against <a href=\"https://wikipedia.org/wiki/Philippine_President\" class=\"mw-redirect\" title=\"Philippine President\">Philippine President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Philippine President", "link": "https://wikipedia.org/wiki/Philippine_President"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "War on Terror: In the first such act since World War II, US President <PERSON> signs an executive order allowing military tribunals against foreigners suspected of connections to terrorist acts or planned acts on the United States.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/War_on_Terror\" class=\"mw-redirect\" title=\"War on Terror\">War on Terror</a>: In the first such act since World War II, US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs an <a href=\"https://wikipedia.org/wiki/Executive_order\" title=\"Executive order\">executive order</a> allowing <a href=\"https://wikipedia.org/wiki/Military_tribunal\" class=\"mw-redirect\" title=\"Military tribunal\">military tribunals</a> against foreigners suspected of connections to terrorist acts or planned acts on the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_on_Terror\" class=\"mw-redirect\" title=\"War on Terror\">War on Terror</a>: In the first such act since World War II, US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs an <a href=\"https://wikipedia.org/wiki/Executive_order\" title=\"Executive order\">executive order</a> allowing <a href=\"https://wikipedia.org/wiki/Military_tribunal\" class=\"mw-redirect\" title=\"Military tribunal\">military tribunals</a> against foreigners suspected of connections to terrorist acts or planned acts on the United States.", "links": [{"title": "War on Terror", "link": "https://wikipedia.org/wiki/War_on_Terror"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Executive order", "link": "https://wikipedia.org/wiki/Executive_order"}, {"title": "Military tribunal", "link": "https://wikipedia.org/wiki/Military_tribunal"}]}, {"year": "2002", "text": "Iraq disarmament crisis: Iraq agrees to the terms of the UN Security Council Resolution 1441.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Iraq_disarmament_crisis\" title=\"Iraq disarmament crisis\">Iraq disarmament crisis</a>: <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> agrees to the terms of the <a href=\"https://wikipedia.org/wiki/UN_Security_Council_Resolution_1441\" class=\"mw-redirect\" title=\"UN Security Council Resolution 1441\">UN Security Council Resolution 1441</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_disarmament_crisis\" title=\"Iraq disarmament crisis\">Iraq disarmament crisis</a>: <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> agrees to the terms of the <a href=\"https://wikipedia.org/wiki/UN_Security_Council_Resolution_1441\" class=\"mw-redirect\" title=\"UN Security Council Resolution 1441\">UN Security Council Resolution 1441</a>.", "links": [{"title": "Iraq disarmament crisis", "link": "https://wikipedia.org/wiki/Iraq_disarmament_crisis"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "UN Security Council Resolution 1441", "link": "https://wikipedia.org/wiki/UN_Security_Council_Resolution_1441"}]}, {"year": "2002", "text": "During the Prestige oil spill, a storm bursts a tank of the oil tanker MV Prestige, which was not allowed to dock and sank on November 19, 2002, off the coast of Galicia, spilling 63,000 metric tons of heavy fuel oil, more than the Exxon Valdez oil spill.", "html": "2002 - During the <a href=\"https://wikipedia.org/wiki/Prestige_oil_spill\" title=\"Prestige oil spill\"><i>Prestige</i> oil spill</a>, a storm bursts a tank of the <a href=\"https://wikipedia.org/wiki/Oil_tanker\" title=\"Oil tanker\">oil tanker</a> <a href=\"https://wikipedia.org/wiki/MV_Prestige\" title=\"MV Prestige\">MV <i>Prestige</i></a>, which was not allowed to dock and sank on November 19, 2002, off the coast of <a href=\"https://wikipedia.org/wiki/Galicia_(Spain)\" title=\"Galicia (Spain)\">Galicia</a>, spilling 63,000 metric tons of heavy fuel oil, more than the <a href=\"https://wikipedia.org/wiki/Exxon_Valdez_oil_spill\" title=\"Exxon Valdez oil spill\"><i>Exxon Valdez</i> oil spill</a>.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Prestige_oil_spill\" title=\"Prestige oil spill\"><i>Prestige</i> oil spill</a>, a storm bursts a tank of the <a href=\"https://wikipedia.org/wiki/Oil_tanker\" title=\"Oil tanker\">oil tanker</a> <a href=\"https://wikipedia.org/wiki/MV_Prestige\" title=\"MV Prestige\">MV <i>Prestige</i></a>, which was not allowed to dock and sank on November 19, 2002, off the coast of <a href=\"https://wikipedia.org/wiki/Galicia_(Spain)\" title=\"Galicia (Spain)\">Galicia</a>, spilling 63,000 metric tons of heavy fuel oil, more than the <a href=\"https://wikipedia.org/wiki/Exxon_Valdez_oil_spill\" title=\"Exxon Valdez oil spill\"><i>Exxon Valdez</i> oil spill</a>.", "links": [{"title": "Prestige oil spill", "link": "https://wikipedia.org/wiki/Prestige_oil_spill"}, {"title": "Oil tanker", "link": "https://wikipedia.org/wiki/Oil_tanker"}, {"title": "MV Prestige", "link": "https://wikipedia.org/wiki/MV_Prestige"}, {"title": "Galicia (Spain)", "link": "https://wikipedia.org/wiki/Galicia_(Spain)"}, {"title": "Exxon Valdez oil spill", "link": "https://wikipedia.org/wiki/Exxon_Valdez_oil_spill"}]}, {"year": "2012", "text": "A total solar eclipse occurs in parts of Australia and the South Pacific.", "html": "2012 - A total <a href=\"https://wikipedia.org/wiki/Solar_eclipse\" title=\"Solar eclipse\">solar eclipse</a> <a href=\"https://wikipedia.org/wiki/Solar_eclipse_of_November_13,_2012\" title=\"Solar eclipse of November 13, 2012\">occurs</a> in parts of Australia and the South Pacific.", "no_year_html": "A total <a href=\"https://wikipedia.org/wiki/Solar_eclipse\" title=\"Solar eclipse\">solar eclipse</a> <a href=\"https://wikipedia.org/wiki/Solar_eclipse_of_November_13,_2012\" title=\"Solar eclipse of November 13, 2012\">occurs</a> in parts of Australia and the South Pacific.", "links": [{"title": "Solar eclipse", "link": "https://wikipedia.org/wiki/Solar_eclipse"}, {"title": "Solar eclipse of November 13, 2012", "link": "https://wikipedia.org/wiki/Solar_eclipse_of_November_13,_2012"}]}, {"year": "2013", "text": "Hawaii legalizes same-sex marriage.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Hawaii\" title=\"Hawaii\">Hawaii</a> legalizes <a href=\"https://wikipedia.org/wiki/Hawaii_Marriage_Equality_Act\" title=\"Hawaii Marriage Equality Act\">same-sex marriage</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hawaii\" title=\"Hawaii\">Hawaii</a> legalizes <a href=\"https://wikipedia.org/wiki/Hawaii_Marriage_Equality_Act\" title=\"Hawaii Marriage Equality Act\">same-sex marriage</a>.", "links": [{"title": "Hawaii", "link": "https://wikipedia.org/wiki/Hawaii"}, {"title": "Hawaii Marriage Equality Act", "link": "https://wikipedia.org/wiki/Hawaii_Marriage_Equality_Act"}]}, {"year": "2013", "text": "4 World Trade Center officially opens.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/4_World_Trade_Center\" title=\"4 World Trade Center\">4 World Trade Center</a> officially opens.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/4_World_Trade_Center\" title=\"4 World Trade Center\">4 World Trade Center</a> officially opens.", "links": [{"title": "4 World Trade Center", "link": "https://wikipedia.org/wiki/4_World_Trade_Center"}]}, {"year": "2015", "text": "Islamic State operatives carry out a series of coordinated terrorist attacks in Paris, including suicide bombings, mass shootings and a hostage crisis. The terrorists kill 130 people, making it the deadliest attack in France since the Second World War.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State</a> operatives carry out a series of <a href=\"https://wikipedia.org/wiki/November_2015_Paris_attacks\" title=\"November 2015 Paris attacks\">coordinated terrorist attacks</a> in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>, including suicide bombings, mass shootings and a hostage crisis. The terrorists kill 130 people, making it the deadliest attack in France since the Second World War.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State</a> operatives carry out a series of <a href=\"https://wikipedia.org/wiki/November_2015_Paris_attacks\" title=\"November 2015 Paris attacks\">coordinated terrorist attacks</a> in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>, including suicide bombings, mass shootings and a hostage crisis. The terrorists kill 130 people, making it the deadliest attack in France since the Second World War.", "links": [{"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}, {"title": "November 2015 Paris attacks", "link": "https://wikipedia.org/wiki/November_2015_Paris_attacks"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}], "Births": [{"year": "354", "text": "<PERSON> of Hippo, Roman bishop and theologian (d. 430)", "html": "354 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hippo\" title=\"<PERSON> of Hippo\"><PERSON> of Hippo</a>, Roman bishop and theologian (d. 430)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hippo\" title=\"<PERSON> of Hippo\"><PERSON> of Hippo</a>, Roman bishop and theologian (d. 430)", "links": [{"title": "<PERSON> of Hippo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hip<PERSON>"}]}, {"year": "1312", "text": "<PERSON> of England (d. 1377)", "html": "1312 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> (d. 1377)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> (d. 1377)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1453", "text": "<PERSON>, Margrave of Baden-Baden (1475-1515) (d. 1527)", "html": "1453 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Baden\" class=\"mw-redirect\" title=\"<PERSON>, Margrave of Baden-Baden\"><PERSON>, Margrave of Baden-Baden</a> (1475-1515) (d. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Baden\" class=\"mw-redirect\" title=\"<PERSON>, Margrave of Baden-Baden\"><PERSON>, Margrave of Baden-Baden</a> (1475-1515) (d. 1527)", "links": [{"title": "<PERSON>, Margrave of Baden-Baden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Baden"}]}, {"year": "1486", "text": "<PERSON>, German theologian and academic (d. 1543)", "html": "1486 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and academic (d. 1543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and academic (d. 1543)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1493", "text": "<PERSON>, Duke of Bavaria (d. 1550)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1550)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1504", "text": "<PERSON>, Landgrave of Hesse (d. 1567)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse\" title=\"<PERSON>, Landgrave of Hesse\"><PERSON>, Landgrave of Hesse</a> (d. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse\" title=\"<PERSON>, Landgrave of Hesse\"><PERSON>, Landgrave of Hesse</a> (d. 1567)", "links": [{"title": "<PERSON>, Landgrave of Hesse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse"}]}, {"year": "1572", "text": "<PERSON>, Greek patriarch and theologian (d. 1638)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek patriarch and theologian (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek patriarch and theologian (d. 1638)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1559", "text": "<PERSON>, Archduke of Austria, Governor of the Low Countries (d. 1621)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON>, Archduke of Austria</a>, Governor of the Low Countries (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON>, Archduke of Austria</a>, Governor of the Low Countries (d. 1621)", "links": [{"title": "<PERSON>, Archduke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria"}]}, {"year": "1699", "text": "<PERSON>, Czech violinist, organist, and composer (d. 1773)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech violinist, organist, and composer (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech violinist, organist, and composer (d. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, French director and playwright (d. 1792)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and playwright (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and playwright (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, German first female medical doctor (d. 1762)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German first female medical doctor (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German first female medical doctor (d. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1732", "text": "<PERSON>, American lawyer and politician, 5th Governor of Pennsylvania (d. 1808)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Pennsylvania_and_Delaware)\" class=\"mw-redirect\" title=\"<PERSON> (Pennsylvania and Delaware)\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Pennsylvania_and_Delaware)\" class=\"mw-redirect\" title=\"<PERSON> (Pennsylvania and Delaware)\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (d. 1808)", "links": [{"title": "<PERSON> (Pennsylvania and Delaware)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Pennsylvania_and_Delaware)"}, {"title": "Governor of Pennsylvania", "link": "https://wikipedia.org/wiki/Governor_of_Pennsylvania"}]}, {"year": "1760", "text": "<PERSON><PERSON><PERSON> Emperor of China (d. 1820)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Jiaqing Emperor\"><PERSON><PERSON><PERSON> Emperor</a> of China (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Jiaqing Emperor\"><PERSON><PERSON><PERSON> Emperor</a> of China (d. 1820)", "links": [{"title": "<PERSON><PERSON>ing Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}]}, {"year": "1761", "text": "<PERSON>, Scottish general and politician (d. 1809)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Scottish general and politician (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Scottish general and politician (d. 1809)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)"}]}, {"year": "1780", "text": "<PERSON><PERSON><PERSON>, Sikh emperor (d. 1839)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sikh emperor (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sikh emperor (d. 1839)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON><PERSON><PERSON>, Swedish bishop and educator (d. 1846)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/Esaias_Tegn%C3%A9r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish bishop and educator (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esaias_Tegn%C3%A9r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish bishop and educator (d. 1846)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Esaias_Tegn%C3%A9r"}]}, {"year": "1804", "text": "<PERSON><PERSON><PERSON>, American general (d. 1880)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>phi<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Theophi<PERSON>\"><PERSON><PERSON><PERSON></a>, American general (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>phi<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Theophi<PERSON>\"><PERSON>phi<PERSON></a>, American general (d. 1880)", "links": [{"title": "Theophi<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>phi<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, American admiral (d. 1870)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON><PERSON> <PERSON>-<PERSON><PERSON><PERSON><PERSON>, Montenegrin metropolitan, philosopher, and poet (d. 1851)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Petar_II_Petrovi%C4%87-Njego%C5%A1\" title=\"<PERSON><PERSON> <PERSON>-<PERSON>jegoš\"><PERSON><PERSON> <PERSON>-<PERSON></a>, Montenegrin metropolitan, philosopher, and poet (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II_Petrovi%C4%87-Njego%C5%A1\" title=\"<PERSON><PERSON> <PERSON>-<PERSON>jegoš\"><PERSON><PERSON> <PERSON>-<PERSON></a>, Montenegrin metropolitan, philosopher, and poet (d. 1851)", "links": [{"title": "Petar II Petrović-Njegoš", "link": "https://wikipedia.org/wiki/Petar_II_Petrovi%C4%87-Njego%C5%A1"}]}, {"year": "1814", "text": "<PERSON>, American general (d. 1879)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, American actor and manager (d. 1893)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and manager (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and manager (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, American lawyer and politician (d. 1883)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, American religious leader, 6th President of The Church of Jesus Christ of Latter-day Saints (d. 1918)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 6th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 6th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1841", "text": "<PERSON>, Jr., American general and diplomat, United States Ambassador to Spain (d. 1913)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Spain\" class=\"mw-redirect\" title=\"United States Ambassador to Spain\">United States Ambassador to Spain</a> (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Spain\" class=\"mw-redirect\" title=\"United States Ambassador to Spain\">United States Ambassador to Spain</a> (d. 1913)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}, {"title": "United States Ambassador to Spain", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Spain"}]}, {"year": "1847", "text": "<PERSON>, famous novelist of Bengali literature (d. 1912)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, famous novelist of <a href=\"https://wikipedia.org/wiki/Bengali_literature\" title=\"Bengali literature\">Bengali literature</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, famous novelist of <a href=\"https://wikipedia.org/wiki/Bengali_literature\" title=\"Bengali literature\">Bengali literature</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Bengali literature", "link": "https://wikipedia.org/wiki/Bengali_literature"}]}, {"year": "1848", "text": "<PERSON>, Prince of Monaco (d. 1922)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a> (d. 1922)", "links": [{"title": "<PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco"}]}, {"year": "1850", "text": "<PERSON>, Scottish novelist, poet, and essayist (d. 1894)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish novelist, poet, and essayist (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish novelist, poet, and essayist (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, American actor (d. 1927)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American actor (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American actor (d. 1927)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}, {"year": "1854", "text": "<PERSON>, American composer and educator (d. 1931)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>wick"}]}, {"year": "1856", "text": "<PERSON>, American lawyer and jurist (d. 1941)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Brandeis"}]}, {"year": "1864", "text": "<PERSON>, American Bishop of the Methodist Episcopal Church, South (d. 1944),", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American Bishop of the <a href=\"https://wikipedia.org/wiki/Methodist_Episcopal_Church,_South\" title=\"Methodist Episcopal Church, South\">Methodist Episcopal Church, South</a> (d. 1944),", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American Bishop of the <a href=\"https://wikipedia.org/wiki/Methodist_Episcopal_Church,_South\" title=\"Methodist Episcopal Church, South\">Methodist Episcopal Church, South</a> (d. 1944),", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "Methodist Episcopal Church, South", "link": "https://wikipedia.org/wiki/Methodist_Episcopal_Church,_South"}]}, {"year": "1866", "text": "<PERSON>, American educator, founded the Institute for Advanced Study (d. 1959)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, founded the <a href=\"https://wikipedia.org/wiki/Institute_for_Advanced_Study\" title=\"Institute for Advanced Study\">Institute for Advanced Study</a> (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, founded the <a href=\"https://wikipedia.org/wiki/Institute_for_Advanced_Study\" title=\"Institute for Advanced Study\">Institute for Advanced Study</a> (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Institute for Advanced Study", "link": "https://wikipedia.org/wiki/Institute_for_Advanced_Study"}]}, {"year": "1869", "text": "<PERSON><PERSON>, German author and activist (d. 1943)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6cker\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and activist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6cker\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and activist (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Helene_St%C3%B6cker"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>-<PERSON>, Russian-American activist, journalist, and politician (d. 1962)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, Russian-American activist, journalist, and politician (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, Russian-American activist, journalist, and politician (d. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Irish-Canadian architect and educator, designed the Royal Alexandra Theatre (d. 1945)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian architect and educator, designed the <a href=\"https://wikipedia.org/wiki/Royal_Alexandra_Theatre\" title=\"Royal Alexandra Theatre\">Royal Alexandra Theatre</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian architect and educator, designed the <a href=\"https://wikipedia.org/wiki/Royal_Alexandra_Theatre\" title=\"Royal Alexandra Theatre\">Royal Alexandra Theatre</a> (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Royal Alexandra Theatre", "link": "https://wikipedia.org/wiki/Royal_Alexandra_Theatre"}]}, {"year": "1878", "text": "<PERSON>, German-American mathematician and academic (d. 1952)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American mathematician and academic (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American mathematician and academic (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, American gymnast and triathlete (d. 1939)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and triathlete (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and triathlete (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Mexican railroad brakeman (d. 1907)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Garc%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican railroad brakeman (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Garc%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican railroad brakeman (d. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Garc%C3%ADa"}]}, {"year": "1883", "text": "<PERSON>, American swimmer, diver, and water polo player (d. 1957)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer, diver, and water polo player (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer, diver, and water polo player (d. 1957)", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1886", "text": "<PERSON>, German dancer and choreographer (d. 1973)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German dancer and choreographer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German dancer and choreographer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (d. 1986)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1894", "text": "<PERSON><PERSON>, American pianist and bandleader (d. 1935)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and bandleader (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and bandleader (d. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, German SS officer (d. 1945)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1897", "text": "<PERSON>, American actress (d. 1975)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, Pakistani general and politician, 1st President of Pakistan (d. 1969)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1900", "text": "<PERSON>, American convicted murderer and firearms designer (d. 1975)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American convicted murderer and firearms designer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American convicted murderer and firearms designer (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American actor, director, and screenwriter (d. 1985)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON> <PERSON><PERSON>, American director and producer (d. 1977)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Potter\"><PERSON><PERSON> <PERSON><PERSON></a>, American director and producer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"H. C<PERSON> Potter\"><PERSON><PERSON> <PERSON><PERSON></a>, American director and producer (d. 1977)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, English actress (d. 1986)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Her<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Her<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan physicist and academic (d. 1987)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/A._<PERSON><PERSON>_Mailvaganam\" title=\"<PERSON><PERSON> <PERSON><PERSON>gan<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan physicist and academic (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A._W<PERSON>_Mailvaganam\" title=\"<PERSON><PERSON> <PERSON><PERSON>vaganam\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan physicist and academic (d. 1987)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A._<PERSON><PERSON>_Mailvaganam"}]}, {"year": "1906", "text": "<PERSON>, Hungarian-American potter and designer (d. 2011)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American potter and designer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American potter and designer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American historian, author, and academic (d. 1999)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian, author, and academic (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian, author, and academic (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Maltese sculptor (d. 2003)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese sculptor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese sculptor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American journalist and author (d. 1986)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>e"}]}, {"year": "1910", "text": "<PERSON>, Indian-English soldier and author (d. 1990)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English soldier and author (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English soldier and author (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American baseball player and manager (d. 2006)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neil\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neil\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Neil"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Sri Lankan physicist and academic (d. 2001)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan physicist and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan physicist and academic (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Greek novelist and journalist (d. 1981)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek novelist and journalist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek novelist and journalist (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Cambodian general and politician, 37th Prime Minister of Cambodia (d. 1985)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cambodian general and politician, 37th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Cambodia\" title=\"Prime Minister of Cambodia\">Prime Minister of Cambodia</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cambodian general and politician, 37th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Cambodia\" title=\"Prime Minister of Cambodia\">Prime Minister of Cambodia</a> (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lon_<PERSON>l"}, {"title": "Prime Minister of Cambodia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Cambodia"}]}, {"year": "1914", "text": "<PERSON>, Argentinian actress (d. 2016)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Italian actor, director, and screenwriter (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian farmer and politician, 9th Governor of Rajasthan (d. 1989)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Vasantdada_Patil\" title=\"Vasantdada Patil\">V<PERSON><PERSON><PERSON><PERSON></a>, Indian farmer and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Rajasthan\" class=\"mw-redirect\" title=\"List of Governors of Rajasthan\">Governor of Rajasthan</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasantdada_Patil\" title=\"Vasantdada Patil\">Vasa<PERSON><PERSON><PERSON></a>, Indian farmer and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Rajasthan\" class=\"mw-redirect\" title=\"List of Governors of Rajasthan\">Governor of Rajasthan</a> (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vasantdada_Patil"}, {"title": "List of Governors of Rajasthan", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Rajasthan"}]}, {"year": "1917", "text": "<PERSON>, American actor (d. 2006)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Canadian philosopher and academic (d. 1988)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, Canadian philosopher and academic (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, Canadian philosopher and academic (d. 1988)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(philosopher)"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican dancer, choreographer, and director (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_Bravo\" title=\"Guillermina Bravo\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican dancer, choreographer, and director (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_Bravo\" title=\"Guillermina Bravo\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican dancer, choreographer, and director (d. 2013)", "links": [{"title": "Guillermina Bravo", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_Bravo"}]}, {"year": "1920", "text": "<PERSON>, American actor (d. 2003)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Finnish pianist and composer (d. 1996)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish pianist and composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish pianist and composer (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American game show host and announcer (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and announcer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and announcer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Austrian-German actor (d. 1984)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-German actor (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-German actor (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Irish and Canadian medievalist and palaeographer (d. 1999)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish and Canadian medievalist and palaeographer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish and Canadian medievalist and palaeographer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Mexican-American actress (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actress (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Japanese biologist and geneticist (d. 1994)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese biologist and geneticist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese biologist and geneticist (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American lawyer and politician, 57th Governor of Maryland (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 57th <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 57th <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Maryland", "link": "https://wikipedia.org/wiki/Governor_of_Maryland"}]}, {"year": "1927", "text": "<PERSON>-<PERSON>, American mathematician and theorist (d. 1985)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (d. 1985)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>uch<PERSON>-Reid"}]}, {"year": "1928", "text": "<PERSON>, Scottish-American actress (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American basketball player (d. 1989)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American pianist and author (d. 1977)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Hampton_Hawes\" title=\"<PERSON> Hawes\"><PERSON></a>, American pianist and author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hampton_Hawes\" title=\"Hampton Hawes\"><PERSON></a>, American pianist and author (d. 1977)", "links": [{"title": "<PERSON> Hawes", "link": "https://wikipedia.org/wiki/Hampton_<PERSON>wes"}]}, {"year": "1929", "text": "<PERSON>, French historian and academic (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American lawyer, pastor, and activist, founded the Westboro Baptist Church (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, pastor, and activist, founded the <a href=\"https://wikipedia.org/wiki/Westboro_Baptist_Church\" title=\"Westboro Baptist Church\">Westboro Baptist Church</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, pastor, and activist, founded the <a href=\"https://wikipedia.org/wiki/Westboro_Baptist_Church\" title=\"Westboro Baptist Church\">Westboro Baptist Church</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Westboro Baptist Church", "link": "https://wikipedia.org/wiki/Westboro_Baptist_Church"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 46th <PERSON><PERSON><PERSON>na (d. 1988)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Asashio_Tar%C5%8D_III\" title=\"<PERSON><PERSON><PERSON> Tarō III\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 46th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asashio_Tar%C5%8D_III\" title=\"<PERSON><PERSON><PERSON> Tarō III\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 46th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asashio_Tar%C5%8D_III"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1930", "text": "<PERSON>, American painter and academic (d. 2006)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American politician (d. 2024)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Scottish actress (d. 2016)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish actress (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American record producer and music publisher (d. 2006)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer and music publisher (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Killen\"><PERSON></a>, American record producer and music publisher (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actor (d. 2000)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American-Australian actor, singer, and television host (d. 2009)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Don Lane\"><PERSON></a>, American-Australian actor, singer, and television host (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Don_Lane\" title=\"Don Lane\"><PERSON></a>, American-Australian actor, singer, and television host (d. 2009)", "links": [{"title": "Don Lane", "link": "https://wikipedia.org/wiki/Don_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian author and poet (d. 1983)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Oj%C4%81rs_V%C4%81cietis\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian author and poet (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oj%C4%81rs_V%C4%81cietis\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian author and poet (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oj%C4%81rs_V%C4%81cietis"}]}, {"year": "1934", "text": "<PERSON>, New Zealand-American journalist and academic", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-American journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-American journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Italian singer-songwriter and actor (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Malaysian-Australian singer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>mahl\" title=\"<PERSON>mah<PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian-Australian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>mah<PERSON>\" title=\"<PERSON>mah<PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian-Australian singer", "links": [{"title": "Ka<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>l"}]}, {"year": "1934", "text": "<PERSON>, American actor, director, and producer (d. 2016)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English archbishop and theologian", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and theologian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Syrian actor and politician (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian actor and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian actor and politician (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Canadian journalist, poet, and politician (d. 1994)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist, poet, and politician (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist, poet, and politician (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9ral<PERSON>_<PERSON>in"}]}, {"year": "1938", "text": "<PERSON>, Jr., American golfer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American golfer", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1938", "text": "<PERSON>, American-French actress and singer (d. 1979)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French actress and singer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French actress and singer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Czech footballer and manager", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Br%C3%BCckner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Br%C3%BCckner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rel_Br%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, American drummer and composer (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American drummer and composer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American drummer and composer (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American philosopher and academic (d. 2022)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American jazz singer and educator (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz singer and educator (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz singer and educator (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American soul singer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Baby_Washington\" title=\"Baby Washington\"><PERSON></a>, American soul singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baby_Washington\" title=\"Baby Washington\"><PERSON></a>, American soul singer", "links": [{"title": "Baby Washington", "link": "https://wikipedia.org/wiki/<PERSON>_Washington"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, German lawyer and politician, 10th Mayor of Berlin", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Berlin\" title=\"List of mayors of Berlin\">Mayor of Berlin</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Berlin\" title=\"List of mayors of Berlin\">Mayor of Berlin</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of mayors of Berlin", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Berlin"}]}, {"year": "1941", "text": "<PERSON>, American businessman and philanthropist, founded Hobby Lobby", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(entrepreneur)\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Ho<PERSON>_Lobby\" title=\"Hobby Lobby\"><PERSON><PERSON>bby</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(entrepreneur)\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Lobby\" title=\"Hobby Lobby\"><PERSON><PERSON>bby</a>", "links": [{"title": "<PERSON> (entrepreneur)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(entrepreneur)"}, {"title": "Hobby Lobby", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American actor (d. 1994)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American baseball player and coach (d. 2019)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mel_<PERSON>ottlemyre"}]}, {"year": "1941", "text": "<PERSON>, American political scientist and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Italian footballer and manager", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American golfer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English footballer and manager", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American singer-songwriter, keyboard player, and producer (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, keyboard player, and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, keyboard player, and producer (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American guitarist and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Norwegian saxophonist and composer (d. 2023)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Knut_Riisn%C3%A6s\" title=\"<PERSON>nut Riisnæ<PERSON>\"><PERSON><PERSON></a>, Norwegian saxophonist and composer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Knut_Riisn%C3%A6s\" title=\"Knut <PERSON>ii<PERSON>næ<PERSON>\"><PERSON><PERSON></a>, Norwegian saxophonist and composer (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Knut_Riisn%C3%A6s"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Polish-American poet, critic, and scholar (d. 2014)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Bara%C5%84czak\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American poet, critic, and scholar (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Bara%C5%84czak\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American poet, critic, and scholar (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_Bara%C5%84czak"}]}, {"year": "1946", "text": "<PERSON>, American country singer-songwriter and guitarist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American guitarist and songwriter (d. 1993)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Toy_Caldwell\" title=\"Toy Caldwell\"><PERSON></a>, American guitarist and songwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toy_Caldwell\" title=\"Toy Caldwell\"><PERSON></a>, American guitarist and songwriter (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Caldwell"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American physicist and environmentalist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and environmentalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and environmentalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor and voice artist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Bengali popular writer, dramatist, novelist, screenwriter, lyricist and filmmaker (d. 2012)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bengali popular writer, dramatist, novelist, screenwriter, lyricist and filmmaker (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bengali popular writer, dramatist, novelist, screenwriter, lyricist and filmmaker (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Israeli basketball player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hon"}]}, {"year": "1951", "text": "<PERSON>, American author and journalist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American author and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American author and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American jurist, 86th United States Attorney General", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jurist, 86th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jurist, 86th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1952", "text": "<PERSON>, American golfer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Pakistani-English actor and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani-English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani-English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, former President of Mexico", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_<PERSON>_<PERSON>%C3%B3pez_O<PERSON>dor\" title=\"<PERSON>\"><PERSON></a>, former President of Mexico", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_<PERSON>_<PERSON>%C3%B3pez_O<PERSON>dor\" title=\"<PERSON>\"><PERSON></a>, former President of Mexico", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_<PERSON>_L%C3%B3pez_Obrador"}]}, {"year": "1953", "text": "<PERSON>, American actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American businessman, co-founded Sun Microsystems", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sun_Microsystems\" title=\"Sun Microsystems\">Sun Microsystems</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sun_Microsystems\" title=\"Sun Microsystems\">Sun Microsystems</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sun Microsystems", "link": "https://wikipedia.org/wiki/Sun_Microsystems"}]}, {"year": "1954", "text": "<PERSON>, American actor and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Canadian jazz musician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian jazz musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian jazz musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American golfer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, American actress, comedian, and talk show host", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, comedian, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, comedian, and talk show host", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Canadian singer-songwriter and musician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Aldo_Nova\" title=\"Aldo Nova\"><PERSON><PERSON></a>, Canadian singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aldo_Nova\" title=\"Aldo Nova\"><PERSON><PERSON></a>, Canadian singer-songwriter and musician", "links": [{"title": "Aldo Nova", "link": "https://wikipedia.org/wiki/Aldo_Nova"}]}, {"year": "1957", "text": "<PERSON>, American politician, 48th Governor of Texas", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 48th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 48th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Texas", "link": "https://wikipedia.org/wiki/Governor_of_Texas"}]}, {"year": "1959", "text": "<PERSON>, English actress and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Romanian gymnast and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian gymnast and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian gymnast and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American entrepreneur and technology executive", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur and technology executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur and technology executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Angolan basketball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3\" title=\"<PERSON>\"><PERSON></a>, Angolan basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A3\" title=\"<PERSON>\"><PERSON></a>, Angolan basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American football player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>averde\" title=\"<PERSON><PERSON> Testaverde\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>averde\" title=\"<PERSON><PERSON> Testaverde\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ny_Testaverde"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Finnish race car driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(co-driver)\" title=\"<PERSON><PERSON> (co-driver)\"><PERSON><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(co-driver)\" title=\"<PERSON><PERSON> (co-driver)\"><PERSON><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON><PERSON> (co-driver)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(co-driver)"}]}, {"year": "1964", "text": "<PERSON>, American politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(U.S._Senator)\" class=\"mw-redirect\" title=\"<PERSON> (U.S. Senator)\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(U.S._Senator)\" class=\"mw-redirect\" title=\"<PERSON> (U.S. Senator)\"><PERSON></a>, American politician", "links": [{"title": "<PERSON> (U.S. Senator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(U.S._Senator)"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Finnish politician (d. 2009)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Indian actress, singer, and producer, Miss India 1984", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress, singer, and producer, <a href=\"https://wikipedia.org/wiki/Femina_Miss_India\" title=\"Femina Miss India\">Miss India 1984</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress, singer, and producer, <a href=\"https://wikipedia.org/wiki/Femina_Miss_India\" title=\"Femina Miss India\">Miss India 1984</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>a"}, {"title": "Femina Miss India", "link": "https://wikipedia.org/wiki/Femina_Miss_India"}]}, {"year": "1967", "text": "<PERSON>, American comedian, actor, and talk show host", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor and singer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American baseball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Somalian-American activist and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Somalian-American activist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Somalian-American activist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>rik_<PERSON>\" title=\"Patrik <PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rik_<PERSON>\" title=\"Patrik Augusta\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patrik_Augusta"}]}, {"year": "1969", "text": "<PERSON>, American activist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Scottish actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, German runner", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Japanese singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian swimmer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, French rugby player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bridges\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bridges\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Bridges"}]}, {"year": "1973", "text": "<PERSON>, American drummer and composer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}]}, {"year": "1974", "text": "<PERSON>, New Zealand rugby player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Estonian footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ski\"><PERSON><PERSON><PERSON></a>, Estonian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"In<PERSON><PERSON>ski\"><PERSON><PERSON><PERSON></a>, Estonian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Belgian runner (d. 2008)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, French basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Serbian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_Dr<PERSON><PERSON>vi%C4%87"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hinds\" title=\"<PERSON><PERSON> Hinds\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Hinds\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hinds"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>ui<PERSON>_(footballer,_born_1975)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1975)\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ui<PERSON>_(footballer,_born_1975)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1975)\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1975)", "link": "https://wikipedia.org/wiki/<PERSON>ui<PERSON>_(footballer,_born_1975)"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Estonian ice hockey player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Su<PERSON>oo\" title=\"Toivo Suursoo\"><PERSON><PERSON></a>, Estonian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Suursoo\" title=\"Toivo Suursoo\"><PERSON><PERSON></a>, Estonian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toivo_Suursoo"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Venezuelan nutritionist, television presenter and model.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan nutritionist, television presenter and model.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan nutritionist, television presenter and model.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English sprinter and long jumper", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter and long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter and long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ashi"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi journalist and lyricist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi journalist and lyricist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi journalist and lyricist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Chinese actor and singer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American bass player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Japanese comedian and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Kick_(comedian)\" title=\"Kick (comedian)\"><PERSON></a>, Japanese comedian and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Japanese comedian and screenwriter", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_(comedian)"}]}, {"year": "1979", "text": "<PERSON><PERSON>-<PERSON><PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ord-<PERSON>st\" title=\"<PERSON><PERSON>-Artest\"><PERSON><PERSON>-<PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sandiford-<PERSON>st\" title=\"<PERSON><PERSON>ord-Artest\"><PERSON><PERSON>-<PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>-Artest", "link": "https://wikipedia.org/wiki/Met<PERSON>_Sandiford-Artest"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Israeli rapper and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Subliminal_(rapper)\" title=\"Sublim<PERSON> (rapper)\"><PERSON>lim<PERSON></a>, Israeli rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Subliminal_(rapper)\" title=\"Sublim<PERSON> (rapper)\"><PERSON>lim<PERSON></a>, Israeli rapper and producer", "links": [{"title": "<PERSON><PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/Subliminal_(rapper)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American actress, singer, and dancer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American wrestler and trainer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Sara_Del_Rey\" title=\"Sara Del Rey\"><PERSON></a>, American wrestler and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sara_Del_Rey\" title=\"Sara Del Rey\"><PERSON></a>, American wrestler and trainer", "links": [{"title": "Sara Del Rey", "link": "https://wikipedia.org/wiki/Sara_Del_Rey"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Slovak ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>raj_Koln%C3%ADk"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Canadian speed skater", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American wrestler and coach", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, American author and illustrator", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(artist)\" title=\"<PERSON><PERSON><PERSON><PERSON> (artist)\"><PERSON><PERSON><PERSON><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(artist)\" title=\"<PERSON><PERSON><PERSON><PERSON> (artist)\"><PERSON><PERSON><PERSON><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(artist)"}]}, {"year": "1982", "text": "<PERSON>, American actor, singer, and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Nigerian-American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Samkon_Gado\" title=\"Samkon Gado\"><PERSON><PERSON></a>, Nigerian-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Samkon_Gado\" title=\"Samkon Gado\"><PERSON><PERSON></a>, Nigerian-American football player", "links": [{"title": "Samkon Gado", "link": "https://wikipedia.org/wiki/Samkon_Gado"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Japanese singer-songwriter and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>da"}]}, {"year": "1982", "text": "<PERSON>, English cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Estonian cyclist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Fijian rugby player (d. 2012)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>li_Ku<PERSON>vore\" title=\"Maleli Kunavore\"><PERSON><PERSON></a>, Fijian rugby player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>vore\" title=\"Maleli Kunavore\"><PERSON><PERSON></a>, Fijian rugby player (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Kunavore"}]}, {"year": "1984", "text": "<PERSON>, Paraguayan footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Tongan rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tongan rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tongan rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Venezuelan baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Asdr%C3%BAbal_Cabrera\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asdr%C3%BAbal_Cabrera\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asdr%C3%BAbal_Cabrera"}]}, {"year": "1986", "text": "<PERSON>, Scottish comedian and actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Japanese model and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hima\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese model and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hatsune_Matsushima"}]}, {"year": "1987", "text": "<PERSON>, American swimmer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lmer"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Devon_Bostick\" title=\"Devon Bostick\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Devon_Bostick\" title=\"Devon Bostick\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Devon_Bostick"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Gr%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gr%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gr%C3%A9<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian-Cook Islands rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Cook Islands rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Cook Islands rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American singer and songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Dutch field hockey midfielder", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch field hockey midfielder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch field hockey midfielder", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Singaporean race car driver", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Singaporean race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Singaporean race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1995", "text": "<PERSON>, Austrian model", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ll"}]}, {"year": "1999", "text": "<PERSON>, American baseball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, British-Belgian race car driver", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British-Belgian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British-Belgian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, British tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emma_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American soccer player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "867", "text": "<PERSON> (b. 800)", "html": "867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Nicholas I\">Pope <PERSON> I</a> (b. 800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Nicholas <PERSON>\">Pope <PERSON> I</a> (b. 800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1002", "text": "<PERSON><PERSON><PERSON>, Danish chieftain, <PERSON><PERSON><PERSON> of Devonshire", "html": "1002 - <a href=\"https://wikipedia.org/wiki/Pallig\" title=\"Pallig\"><PERSON>lli<PERSON></a>, Danish chieftain, <PERSON><PERSON><PERSON> of Devonshire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pallig\" title=\"Pallig\">Palli<PERSON></a>, Danish chieftain, <PERSON><PERSON><PERSON> of Devonshire", "links": [{"title": "<PERSON>llig", "link": "https://wikipedia.org/wiki/Pallig"}]}, {"year": "1002", "text": "<PERSON><PERSON><PERSON>, wife of <PERSON><PERSON><PERSON>, Danish chieftain", "html": "1002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/Pallig\" title=\"Pallig\"><PERSON><PERSON><PERSON></a>, Danish chieftain", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/Pallig\" title=\"Pallig\"><PERSON><PERSON><PERSON></a>, Danish chieftain", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gunhilde"}, {"title": "<PERSON>llig", "link": "https://wikipedia.org/wiki/Pallig"}]}, {"year": "1004", "text": "<PERSON><PERSON><PERSON> of Fleury, French monk and saint (b. 945)", "html": "1004 - <a href=\"https://wikipedia.org/wiki/Abb<PERSON>_of_Fleury\" title=\"Abbo of Fleury\">A<PERSON><PERSON> of Fleury</a>, French monk and saint (b. 945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abb<PERSON>_of_Fleury\" title=\"A<PERSON><PERSON> of Fleury\">A<PERSON><PERSON> of Fleury</a>, French monk and saint (b. 945)", "links": [{"title": "<PERSON><PERSON><PERSON> of Fleury", "link": "https://wikipedia.org/wiki/Abbo_of_<PERSON><PERSON><PERSON>"}]}, {"year": "1072", "text": "<PERSON><PERSON><PERSON><PERSON> of Luxembourg (b. c. 1010)", "html": "1072 - <a href=\"https://wikipedia.org/wiki/Adalbero_III_of_Luxembourg\" title=\"Adalbero III of Luxembourg\">Adalbero III of Luxembourg</a> (b. c. 1010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adalbero_III_of_Luxembourg\" title=\"Adalbero III of Luxembourg\">Adalbero III of Luxembourg</a> (b. c. 1010)", "links": [{"title": "Adalbero III of Luxembourg", "link": "https://wikipedia.org/wiki/Adalbero_III_of_Luxembourg"}]}, {"year": "1093", "text": "<PERSON> of Scotland (b. 1031)", "html": "1093 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> (b. 1031)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> (b. 1031)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1143", "text": "<PERSON><PERSON>, King of Jerusalem (b. 1089)", "html": "1143 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Jerusalem\" title=\"<PERSON><PERSON>, King of Jerusalem\"><PERSON><PERSON>, King of Jerusalem</a> (b. 1089)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Jerusalem\" title=\"<PERSON><PERSON>, King of Jerusalem\"><PERSON><PERSON>, King of Jerusalem</a> (b. 1089)", "links": [{"title": "<PERSON><PERSON>, King of Jerusalem", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Jerusalem"}]}, {"year": "1154", "text": "<PERSON><PERSON><PERSON> of Kiev, Prince of Vladimir and Volyn, (b. c. 1097)", "html": "1154 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Kiev\" title=\"<PERSON><PERSON><PERSON> II of Kiev\"><PERSON><PERSON><PERSON> of Kiev</a>, Prince of <a href=\"https://wikipedia.org/wiki/Vladimir,_Russia\" title=\"Vladimir, Russia\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Volhynia\" title=\"Volhynia\"><PERSON><PERSON></a>, (b. c. 1097)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Kiev\" title=\"<PERSON><PERSON><PERSON> II of Kiev\"><PERSON><PERSON><PERSON> of Kiev</a>, Prince of <a href=\"https://wikipedia.org/wiki/Vladimir,_Russia\" title=\"Vladimir, Russia\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Volhynia\" title=\"Volhynia\"><PERSON><PERSON></a>, (b. c. 1097)", "links": [{"title": "<PERSON><PERSON><PERSON> of Kiev", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Kiev"}, {"title": "Vladimir, Russia", "link": "https://wikipedia.org/wiki/Vladimir,_Russia"}, {"title": "Volhynia", "link": "https://wikipedia.org/wiki/Volhynia"}]}, {"year": "1175", "text": "<PERSON> of France, Archbishop of Reims (b. c.1121)", "html": "1175 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_France,_Archbishop_of_Reims\" class=\"mw-redirect\" title=\"<PERSON> of France, Archbishop of Reims\"><PERSON> of France, Archbishop of Reims</a> (b. c.1121)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_France,_Archbishop_of_Reims\" class=\"mw-redirect\" title=\"<PERSON> of France, Archbishop of Reims\"><PERSON> of France, Archbishop of Reims</a> (b. c.1121)", "links": [{"title": "<PERSON> of France, Archbishop of Reims", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Archbishop_of_Reims"}]}, {"year": "1299", "text": "<PERSON>, Bishop of Lincoln", "html": "1299 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Bishop of Lincoln", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Bishop of Lincoln", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1319", "text": "<PERSON> of Denmark (b. 1274)", "html": "1319 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1274)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1274)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1345", "text": "<PERSON> of Peñafiel, queen of Pedro I of Portugal (b. 1323)", "html": "1345 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Pe%C3%B1afiel\" class=\"mw-redirect\" title=\"<PERSON> of Peñafiel\"><PERSON> of Peñafiel</a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" class=\"mw-redirect\" title=\"Pedro I of Portugal\"><PERSON> of Portugal</a> (b. 1323)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Pe%C3%B1afiel\" class=\"mw-redirect\" title=\"<PERSON> of Peñafiel\"><PERSON> of Peñafiel</a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Portugal\" class=\"mw-redirect\" title=\"Pedro I of Portugal\"><PERSON> of Portugal</a> (b. 1323)", "links": [{"title": "Constance of Peñafiel", "link": "https://wikipedia.org/wiki/Constance_of_Pe%C3%B1afiel"}, {"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>_of_Portugal"}]}, {"year": "1359", "text": "<PERSON> of Moscow (b. 1326)", "html": "1359 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Moscow\" title=\"<PERSON> II of Moscow\"><PERSON> of Moscow</a> (b. 1326)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Moscow\" title=\"<PERSON> II of Moscow\"><PERSON> of Moscow</a> (b. 1326)", "links": [{"title": "<PERSON> of Moscow", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Moscow"}]}, {"year": "1369", "text": "<PERSON>, 11th Earl of Warwick", "html": "1369 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Earl_of_Warwick\" class=\"mw-redirect\" title=\"<PERSON>, 11th Earl <PERSON>\"><PERSON>, 11th Earl of Warwick</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Earl_of_Warwick\" class=\"mw-redirect\" title=\"<PERSON>, 11th Earl <PERSON>\"><PERSON>, 11th Earl of Warwick</a>", "links": [{"title": "<PERSON>, 11th Earl of Warwick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Earl_of_Warwick"}]}, {"year": "1432", "text": "<PERSON> of Burgundy, duchess of Bedford (b. 1404)", "html": "1432 - <a href=\"https://wikipedia.org/wiki/Anne_of_Burgundy\" title=\"<PERSON> of Burgundy\"><PERSON> of Burgundy</a>, duchess of Bedford (b. 1404)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_Burgundy\" title=\"<PERSON> of Burgundy\"><PERSON> of Burgundy</a>, duchess of Bedford (b. 1404)", "links": [{"title": "<PERSON> of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Burgundy"}]}, {"year": "1440", "text": "<PERSON>, Countess of Westmoreland", "html": "1440 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Westmoreland\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Westmoreland\"><PERSON>, Countess of Westmoreland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Westmoreland\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Westmoreland\"><PERSON>, Countess of Westmoreland</a>", "links": [{"title": "<PERSON>, Countess of Westmoreland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Westmoreland"}]}, {"year": "1460", "text": "Prince <PERSON> the Navigator, Portuguese patron of exploration (b. 1394)", "html": "1460 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_the_Navigator\" title=\"Prince <PERSON> the Navigator\">Prince <PERSON> the Navigator</a>, Portuguese patron of exploration (b. 1394)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_the_Navigator\" title=\"Prince <PERSON> the Navigator\">Prince <PERSON> the Navigator</a>, Portuguese patron of exploration (b. 1394)", "links": [{"title": "Prince <PERSON> the Navigator", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Navigator"}]}, {"year": "1502", "text": "<PERSON><PERSON>, Italian friar, historian, and scholar (b. 1432)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_da_Viterbo\" class=\"mw-redirect\" title=\"<PERSON><PERSON> da Viterbo\"><PERSON><PERSON> Viterbo</a>, Italian friar, historian, and scholar (b. 1432)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_da_Viterbo\" class=\"mw-redirect\" title=\"<PERSON><PERSON> da Viterbo\"><PERSON><PERSON> Viterbo</a>, Italian friar, historian, and scholar (b. 1432)", "links": [{"title": "<PERSON><PERSON> da Viterbo", "link": "https://wikipedia.org/wiki/Ann<PERSON>_<PERSON>_<PERSON>iterbo"}]}, {"year": "1606", "text": "<PERSON><PERSON><PERSON>, Italian physician and philologist (b. 1530)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/Girolamo_Mercuriale\" title=\"Girolamo Mercuriale\"><PERSON><PERSON><PERSON></a>, Italian physician and philologist (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Girolamo_Mercuriale\" title=\"Girolamo Mercuriale\"><PERSON><PERSON><PERSON></a>, Italian physician and philologist (b. 1530)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Girolamo_Mercuriale"}]}, {"year": "1619", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian painter and illustrator (b. 1555)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter and illustrator (b. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter and illustrator (b. 1555)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1650", "text": "<PERSON>, English poet and historian (b. 1595)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and historian (b. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and historian (b. 1595)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "1726", "text": "<PERSON> Celle (b. 1666)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Celle\" title=\"<PERSON> of Celle\"><PERSON> of Celle</a> (b. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Celle\" title=\"<PERSON> of Celle\"><PERSON> of Celle</a> (b. 1666)", "links": [{"title": "<PERSON> of Celle", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, English lawyer and politician, Prime Minister of Great Britain (b. 1712)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1771", "text": "<PERSON>, German actor (b. 1712)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (b. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, English printer and author (b. 1699)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>, English printer and author (b. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>, English printer and author (b. 1699)", "links": [{"title": "<PERSON> (printer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(printer)"}]}, {"year": "1862", "text": "<PERSON>, German poet, philologist, and historian (b. 1787)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, philologist, and historian (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, philologist, and historian (b. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, Mexican soldier and politician. President 1855-1858 (b. 1812)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican soldier and politician. President 1855-1858 (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican soldier and politician. President 1855-1858 (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, French archaeologist and historian (b. 1806)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French archaeologist and historian (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French archaeologist and historian (b. 1806)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9on_<PERSON>ron"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian pianist and composer (b. 1792)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian pianist and composer (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian pianist and composer (b. 1792)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>achi<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, English painter (b. 1793)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, American physician and gynecologist (b. 1813)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and gynecologist (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and gynecologist (b. 1813)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Virgin Islander-French painter (b. 1830)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Virgin Islander-French painter (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Virgin Islander-French painter (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Norwegian women's rights pioneer (b. 1858)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian women's rights pioneer (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian women's rights pioneer (b. 1858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian scholar of Islam (b. 1850)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Ign%C3%A1c_Gold<PERSON>her\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian scholar of Islam (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ign%C3%A1c_<PERSON>her\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian scholar of Islam (b. 1850)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ign%C3%A1c_<PERSON>"}]}, {"year": "1929", "text": "Princess <PERSON><PERSON> of Prussia (b. 1866)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Princess_Viktoria_of_Prussia\" title=\"Princess <PERSON><PERSON> of Prussia\">Princess <PERSON><PERSON> of Prussia</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Viktoria_of_Prussia\" title=\"Princess <PERSON><PERSON> of Prussia\">Princess <PERSON><PERSON> of Prussia</a> (b. 1866)", "links": [{"title": "Princess <PERSON><PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Princess_Viktoria_of_Prussia"}]}, {"year": "1932", "text": "Francisco <PERSON>ázar<PERSON>, acting president of Mexico (1915) (b. 1878)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Francisco_Lagos_Ch%C3%A1zaro\" title=\"Francisco Lagos Cházaro\">Francisco Lagos Cházaro</a>, acting president of Mexico (1915) (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Lagos_Ch%C3%A1zaro\" title=\"Francisco Lagos Cházaro\">Francisco Lagos Cházaro</a>, acting president of Mexico (1915) (b. 1878)", "links": [{"title": "Francisco Lagos Cházaro", "link": "https://wikipedia.org/wiki/Francisco_Lagos_Ch%C3%A1zaro"}]}, {"year": "1937", "text": "Mrs. <PERSON>, American actress (b. 1857)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Mrs._<PERSON>_<PERSON>\" title=\"Mrs. <PERSON>\">Mrs. <PERSON></a>, American actress (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mrs._<PERSON>_<PERSON>\" title=\"Mrs. <PERSON>\">Mrs. <PERSON></a>, American actress (b. 1857)", "links": [{"title": "Mrs. <PERSON>", "link": "https://wikipedia.org/wiki/Mrs._<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American admiral (b. 1890)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American author (b. 1910)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, German field marshal (b. 1881)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American historian and author (b. 1897)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Greek rabbi (b. 1869)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek rabbi (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek rabbi (b. 1869)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Jr., American general and diplomat, United States Ambassador to Czechoslovakia (b. 1897)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Czechoslovakia\" class=\"mw-redirect\" title=\"United States Ambassador to Czechoslovakia\">United States Ambassador to Czechoslovakia</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Czechoslovakia\" class=\"mw-redirect\" title=\"United States Ambassador to Czechoslovakia\">United States Ambassador to Czechoslovakia</a> (b. 1897)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_<PERSON>."}, {"title": "United States Ambassador to Czechoslovakia", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Czechoslovakia"}]}, {"year": "1963", "text": "<PERSON>, Indian-English anthropologist and author (b. 1863)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English anthropologist and author (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English anthropologist and author (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Indian-Pakistani general and politician, 1st President of Pakistan (b. 1899)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1970", "text": "<PERSON><PERSON>, British politician (b. 1899)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British politician (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British politician (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actress (b. 1901)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Italian-German conductor and composer (b. 1920)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-German conductor and composer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-German conductor and composer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Italian-French actor, director, and screenwriter (b. 1901)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>\" title=\"<PERSON>itt<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French actor, director, and screenwriter (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>itt<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French actor, director, and screenwriter (b. 1901)", "links": [{"title": "Vittorio <PERSON>", "link": "https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American technician and activist (b. 1946)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American technician and activist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American technician and activist (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Russian poet and playwright (b. 1910)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and playwright (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and playwright (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Greek playwright and academic (b. 1907)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek playwright and academic (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek playwright and academic (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Canadian lawyer and politician, 15th Solicitor General of Canada (b. 1911)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Canada\" title=\"Solicitor General of Canada\">Solicitor General of Canada</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Canada\" title=\"Solicitor General of Canada\">Solicitor General of Canada</a> (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Solicitor General of Canada", "link": "https://wikipedia.org/wiki/Solicitor_General_of_Canada"}]}, {"year": "1983", "text": "<PERSON>, American swimmer and water polo player (b. 1886)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Handy\"><PERSON></a>, American swimmer and water polo player (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Handy\"><PERSON></a>, American swimmer and water polo player (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American comedian and actor (b. 1926)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Junior_<PERSON>\" title=\"Junior Sam<PERSON>\"><PERSON></a>, American comedian and actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Junior_<PERSON>\" title=\"Junior Samples\"><PERSON></a>, American comedian and actor (b. 1926)", "links": [{"title": "Junior Samples", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Italian race car driver (b. 1903)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_<PERSON>se"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Hungarian-American conductor and composer (b. 1906)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Antal_Dor%C3%<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-American conductor and composer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An<PERSON>_<PERSON>r%C3%<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-American conductor and composer (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antal_Dor%C3%A1ti"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Czech composer (b. 1902)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Jarom%C3%ADr_Vejvoda\" title=\"Jaromír Vejvoda\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech composer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jarom%C3%ADr_Vejvoda\" title=\"Jaromír Vejvoda\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech composer (b. 1902)", "links": [{"title": "Jaromír <PERSON>", "link": "https://wikipedia.org/wiki/Jarom%C3%ADr_Vejvoda"}]}, {"year": "1989", "text": "<PERSON>, Canadian swimmer (b. 1964)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Prince of Liechtenstein (b. 1906)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Prince_of_Liechtenstein\" title=\"<PERSON>, Prince of Liechtenstein\"><PERSON>, Prince of Liechtenstein</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Prince_of_Liechtenstein\" title=\"<PERSON>, Prince of Liechtenstein\"><PERSON>, Prince of Liechtenstein</a> (b. 1906)", "links": [{"title": "<PERSON>, Prince of Liechtenstein", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Liechtenstein"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Sri Lankan rebel and politician (b. 1943)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan rebel and politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan rebel and politician (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Latvian-South African author, translator and scholar (b. 1920)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-South African author, translator and scholar (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-South African author, translator and scholar (b. 1920)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American golfer (b. 1914)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian cardinal (b. 1904)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89mile_L%C3%A9ger\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89mile_L%C3%A9ger\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-%C3%89mile_L%C3%A9ger"}]}, {"year": "1993", "text": "<PERSON>, American wrestler (b. 1933)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor and screenwriter (b. 1947)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (b. 1947)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Japanese biologist and geneticist (b. 1924)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese biologist and geneticist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese biologist and geneticist (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American pianist and composer (b. 1916)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Australian astrophysicist and academic (b. 1959)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ail<PERSON>\" title=\"<PERSON><PERSON> Vaile\"><PERSON><PERSON></a>, Australian astrophysicist and academic (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Vaile\"><PERSON><PERSON></a>, Australian astrophysicist and academic (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1997", "text": "<PERSON>, Bulgarian-French pianist and composer (b. 1925)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>v\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-French pianist and composer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>v\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-French pianist and composer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>liev"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, French actress (b. 1907)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Ed<PERSON><PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edwige_Fe<PERSON>l%C3%A8re"}]}, {"year": "1998", "text": "<PERSON>, Irish-born English actress (b. 1917)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born English actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born English actress (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American basketball player and coach (b. 1920)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American pole vaulter (b. 1915)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Uruguayan footballer and manager (b. 1925)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer and manager (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer and manager (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON>, Nepalese academic and politician (b. 1925)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nepalese academic and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nepalese academic and politician (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rishi<PERSON><PERSON>_Shaha"}]}, {"year": "2004", "text": "<PERSON>, English singer-songwriter (b. 1962)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON> <PERSON>stard, American rapper and producer (b. 1968)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Ol%27_Dirty_Bastard\" title=\"Ol' Dirty Bastard\">O<PERSON>' <PERSON> Bastard</a>, American rapper and producer (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ol%27_Dirty_Bastard\" title=\"Ol' Dirty Bastard\">Ol' Dirty Bastard</a>, American rapper and producer (b. 1968)", "links": [{"title": "Ol' <PERSON> Bastard", "link": "https://wikipedia.org/wiki/Ol%27_Dirty_Bastard"}]}, {"year": "2004", "text": "<PERSON>, American lawyer and politician, United States Ambassador to Italy (b. 1928)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Italy\" class=\"mw-redirect\" title=\"United States Ambassador to Italy\">United States Ambassador to Italy</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Italy\" class=\"mw-redirect\" title=\"United States Ambassador to Italy\">United States Ambassador to Italy</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Italy", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Italy"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Jr., American historian, theologian, and author (b. 1933)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American historian, theologian, and author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American historian, theologian, and author (b. 1933)", "links": [{"title": "<PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "2005", "text": "<PERSON>, American wrestler (b. 1967)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Filipino lawyer and politician (b. 1960)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English footballer and manager (b. 1935)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer and manager (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer and manager (b. 1935)", "links": [{"title": "<PERSON> (English footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(English_footballer)"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese baseball player and manager (b. 1937)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Spanish director and screenwriter (b. 1921)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Berlanga\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Berlanga\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Garc%C3%ADa_Berlanga"}]}, {"year": "2010", "text": "<PERSON>, American astronomer and cosmologist (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and cosmologist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and cosmologist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Polish photographer and author (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Erazm_Cio%C5%82<PERSON>_(photographer)\" title=\"<PERSON><PERSON><PERSON> (photographer)\"><PERSON><PERSON><PERSON></a>, Polish photographer and author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erazm_Cio%C5%82<PERSON>_(photographer)\" title=\"<PERSON><PERSON><PERSON> (photographer)\"><PERSON><PERSON><PERSON></a>, Polish photographer and author (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON> (photographer)", "link": "https://wikipedia.org/wiki/Erazm_Cio%C5%82ek_(photographer)"}]}, {"year": "2012", "text": "<PERSON>, Spanish footballer (b. 1965)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a_Escontrela\" class=\"mw-redirect\" title=\"<PERSON>ntrel<PERSON>\"><PERSON></a>, Spanish footballer (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a_Escontrela\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manuel_Pe%C3%B1a_Escontrela"}]}, {"year": "2012", "text": "<PERSON>, English rugby player and coach (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, English rugby player and coach (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, English rugby player and coach (b. 1933)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, German author and poet (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German author and poet (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hans-<PERSON>%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German author and poet (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hans-J%C3%<PERSON><PERSON>_<PERSON>ise"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Japanese actress and voice actress (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and voice actress (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress and voice actress (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eko_<PERSON>oi"}]}, {"year": "2014", "text": "<PERSON>, Hon<PERSON>ran model, Señorita Honduras 2014 (b. 1995)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Jos%C3%A9_<PERSON>ado\" title=\"<PERSON>\"><PERSON></a>, Honduran model, <a href=\"https://wikipedia.org/wiki/Se%C3%B1orita_Honduras\" class=\"mw-redirect\" title=\"Señorita Honduras\">Señorita Honduras 2014</a> (b. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Jos%C3%A9_<PERSON>ado\" title=\"<PERSON>\"><PERSON></a>, Honduran model, <a href=\"https://wikipedia.org/wiki/Se%C3%B1orita_Honduras\" class=\"mw-redirect\" title=\"Señorita Honduras\">Señorita Honduras 2014</a> (b. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_Jos%C3%A9_<PERSON><PERSON><PERSON>"}, {"title": "Señorita Honduras", "link": "https://wikipedia.org/wiki/Se%C3%B1orita_Honduras"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Georgian economist and politician, Georgian Minister of Economy (b. 1956)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Economy_and_Sustainable_Development_(Georgia)\" title=\"Ministry of Economy and Sustainable Development (Georgia)\">Georgian Minister of Economy</a> (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Economy_and_Sustainable_Development_(Georgia)\" title=\"Ministry of Economy and Sustainable Development (Georgia)\">Georgian Minister of Economy</a> (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Economy and Sustainable Development (Georgia)", "link": "https://wikipedia.org/wiki/Ministry_of_Economy_and_Sustainable_Development_(Georgia)"}]}, {"year": "2014", "text": "<PERSON>, American baseball player and manager (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alvin Dark\"><PERSON></a>, American baseball player and manager (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alvin Dark\"><PERSON></a>, American baseball player and manager (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, German-French mathematician and theorist (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French mathematician and theorist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French mathematician and theorist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American singer-songwriter (b. 1942)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American baseball player and manager (b. 1918)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, English serial killer (b. 1946)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English serial killer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English serial killer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American lawyer (b. 1940)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American record producer, songwriter and arranger (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>lm<PERSON>\"><PERSON><PERSON></a>, American record producer, songwriter and arranger (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American record producer, songwriter and arranger (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/She<PERSON>_<PERSON>y"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Japanese poet and translator (b. 1931)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Shuntar%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet and translator (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shuntar%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet and translator (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shuntar%C5%8D_<PERSON><PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Malaysian politician (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian politician (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}