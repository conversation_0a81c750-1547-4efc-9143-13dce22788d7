{"date": "March 26", "url": "https://wikipedia.org/wiki/March_26", "data": {"Events": [{"year": "590", "text": "Emperor <PERSON> proclaims his son <PERSON><PERSON><PERSON> as co-emperor of the Byzantine Empire.", "html": "590 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> proclaims his son <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(son_of_<PERSON>)\" title=\"<PERSON><PERSON><PERSON> (son of <PERSON>)\"><PERSON><PERSON><PERSON></a> as co-emperor of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> proclaims his son <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(son_of_<PERSON>)\" title=\"<PERSON><PERSON><PERSON> (son of <PERSON>)\"><PERSON><PERSON><PERSON></a> as co-emperor of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(emperor)"}, {"title": "<PERSON><PERSON><PERSON> (son of <PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(son_of_<PERSON>)"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "624", "text": "First Eid al-Fitr celebration.", "html": "624 - First <a href=\"https://wikipedia.org/wiki/E<PERSON>_al-<PERSON>tr\" title=\"<PERSON><PERSON> al-<PERSON>tr\"><PERSON><PERSON> al-<PERSON>tr</a> celebration.", "no_year_html": "First <a href=\"https://wikipedia.org/wiki/E<PERSON>_al-<PERSON>tr\" title=\"<PERSON><PERSON> al-Fitr\"><PERSON><PERSON> al-<PERSON>tr</a> celebration.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_al-<PERSON>tr"}]}, {"year": "1021", "text": "The death of the Fatimid caliph <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kept secret for six weeks, is announced, along with the succession of his son, <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "html": "1021 - The death of the <a href=\"https://wikipedia.org/wiki/Fatimid\" class=\"mw-redirect\" title=\"Fatim<PERSON>\"><PERSON><PERSON><PERSON></a> caliph <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_bi-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> bi-Am<PERSON>\"><PERSON><PERSON><PERSON><PERSON> bi-<PERSON><PERSON></a>, kept secret for six weeks, is announced, along with the succession of his son, <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_li-i%27za<PERSON>_<PERSON>_Allah\" class=\"mw-redirect\" title=\"<PERSON>-<PERSON><PERSON><PERSON> li-i'z<PERSON>\">al-<PERSON><PERSON><PERSON> li-<PERSON>'<PERSON><PERSON> <PERSON></a>.", "no_year_html": "The death of the <a href=\"https://wikipedia.org/wiki/Fatimid\" class=\"mw-redirect\" title=\"Fatim<PERSON>\"><PERSON><PERSON><PERSON></a> caliph <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_bi-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> bi-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> bi-<PERSON><PERSON></a>, kept secret for six weeks, is announced, along with the succession of his son, <a href=\"https://wikipedia.org/wiki/<PERSON>-Z<PERSON><PERSON>_li-i%27za<PERSON>_Din_Allah\" class=\"mw-redirect\" title=\"<PERSON>-<PERSON><PERSON><PERSON> li-i'zaz <PERSON>\">al-<PERSON><PERSON><PERSON> li-<PERSON>'<PERSON><PERSON> <PERSON></a>.", "links": [{"title": "Fatimid", "link": "https://wikipedia.org/wiki/Fatimid"}, {"title": "<PERSON><PERSON><PERSON><PERSON> bi<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>-<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_li-i%27<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1027", "text": "<PERSON> crowns <PERSON> as Holy Roman Emperor.", "html": "1027 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John <PERSON>\">Pope John XI<PERSON></a> crowns <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> as <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John <PERSON>\">Pope John XI<PERSON></a> crowns <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> as <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}]}, {"year": "1169", "text": "<PERSON><PERSON><PERSON> becomes the emir of Egypt.", "html": "1169 - <a href=\"https://wikipedia.org/wiki/Saladin\" title=\"Saladi<PERSON>\"><PERSON><PERSON><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\">emir</a> of <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saladin\" title=\"Saladi<PERSON>\"><PERSON><PERSON><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\">emir</a> of <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>n"}, {"title": "<PERSON>ir", "link": "https://wikipedia.org/wiki/Emir"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1244", "text": "The crown of Aragon and the crown of Castile agree in the Treaty of Almizra on the limits of their respective expansion into al-Andalus.", "html": "1244 - The <a href=\"https://wikipedia.org/wiki/Crown_of_Aragon\" title=\"Crown of Aragon\">crown of Aragon</a> and the <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">crown of Castile</a> agree in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Almizra\" title=\"Treaty of Almizra\">Treaty of Almizra</a> on the limits of their respective expansion into <a href=\"https://wikipedia.org/wiki/Al-Andalus\" title=\"Al-Andalus\">al-Andalus</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Crown_of_Aragon\" title=\"Crown of Aragon\">crown of Aragon</a> and the <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">crown of Castile</a> agree in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Almizra\" title=\"Treaty of Almizra\">Treaty of Almizra</a> on the limits of their respective expansion into <a href=\"https://wikipedia.org/wiki/Al-Andalus\" title=\"Al-Andalus\">al-Andalus</a>.", "links": [{"title": "Crown of Aragon", "link": "https://wikipedia.org/wiki/Crown_of_Aragon"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}, {"title": "Treaty of Almizra", "link": "https://wikipedia.org/wiki/Treaty_of_Almizra"}, {"title": "Al-Andalus", "link": "https://wikipedia.org/wiki/Al-Andalus"}]}, {"year": "1344", "text": "The Siege of Algeciras, one of the first European military engagements where gunpowder was used, comes to an end.", "html": "1344 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Algeciras_(1342%E2%80%9344)\" class=\"mw-redirect\" title=\"Siege of Algeciras (1342-44)\">Siege of Algeciras</a>, one of the first European military engagements where gunpowder was used, comes to an end.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Algeciras_(1342%E2%80%9344)\" class=\"mw-redirect\" title=\"Siege of Algeciras (1342-44)\">Siege of Algeciras</a>, one of the first European military engagements where gunpowder was used, comes to an end.", "links": [{"title": "Siege of Algeciras (1342-44)", "link": "https://wikipedia.org/wiki/Siege_of_Algeciras_(1342%E2%80%9344)"}]}, {"year": "1351", "text": "Combat of the Thirty: Thirty Breton knights call out and defeat thirty English knights.", "html": "1351 - <a href=\"https://wikipedia.org/wiki/Combat_of_the_Thirty\" title=\"Combat of the Thirty\">Combat of the Thirty</a>: Thirty <a href=\"https://wikipedia.org/wiki/Duchy_of_Brittany\" title=\"Duchy of Brittany\">Breton</a> knights call out and defeat thirty English knights.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Combat_of_the_Thirty\" title=\"Combat of the Thirty\">Combat of the Thirty</a>: Thirty <a href=\"https://wikipedia.org/wiki/Duchy_of_Brittany\" title=\"Duchy of Brittany\">Breton</a> knights call out and defeat thirty English knights.", "links": [{"title": "Combat of the Thirty", "link": "https://wikipedia.org/wiki/Combat_of_the_Thirty"}, {"title": "Duchy of Brittany", "link": "https://wikipedia.org/wiki/Duchy_of_Brittany"}]}, {"year": "1484", "text": "<PERSON> prints his translation of <PERSON><PERSON><PERSON>'s Fables.", "html": "1484 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> prints his translation of <i><a href=\"https://wikipedia.org/wiki/Aesop%27s_Fables\" title=\"<PERSON><PERSON><PERSON>'s Fables\">Aesop's Fables</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> prints his translation of <i><a href=\"https://wikipedia.org/wiki/Aesop%27s_Fables\" title=\"<PERSON><PERSON><PERSON>'s Fables\"><PERSON>es<PERSON>'s Fables</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>'s Fables", "link": "https://wikipedia.org/wiki/Aesop%27s_Fables"}]}, {"year": "1552", "text": "Guru <PERSON> becomes the Third Sikh guru.", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a> becomes the Third <a href=\"https://wikipedia.org/wiki/Sikh_guru\" class=\"mw-redirect\" title=\"Sikh guru\">Sikh guru</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a> becomes the Third <a href=\"https://wikipedia.org/wiki/Sikh_guru\" class=\"mw-redirect\" title=\"Sikh guru\">Sikh guru</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sikh guru", "link": "https://wikipedia.org/wiki/Sikh_guru"}]}, {"year": "1636", "text": "Utrecht University is founded in the Netherlands.", "html": "1636 - <a href=\"https://wikipedia.org/wiki/Utrecht_University\" title=\"Utrecht University\">Utrecht University</a> is founded in the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Utrecht_University\" title=\"Utrecht University\">Utrecht University</a> is founded in the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a>.", "links": [{"title": "Utrecht University", "link": "https://wikipedia.org/wiki/Utrecht_University"}, {"title": "Netherlands", "link": "https://wikipedia.org/wiki/Netherlands"}]}, {"year": "1640", "text": "The Royal Academy of Turku, the first university of Finland, is founded in the city of Turku by Queen <PERSON> of Sweden at the proposal of Count <PERSON>.", "html": "1640 - The <a href=\"https://wikipedia.org/wiki/Royal_Academy_of_Turku\" title=\"Royal Academy of Turku\">Royal Academy of Turku</a>, the first university of <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, is founded in the city of <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a> by Queen <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden\" title=\"<PERSON>, Queen of Sweden\"><PERSON> of Sweden</a> at the proposal of Count <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_the_Younger\" title=\"<PERSON><PERSON> the Younger\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Academy_of_Turku\" title=\"Royal Academy of Turku\">Royal Academy of Turku</a>, the first university of <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, is founded in the city of <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a> by Queen <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden\" title=\"<PERSON>, Queen of Sweden\"><PERSON> of Sweden</a> at the proposal of Count <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_the_Younger\" title=\"<PERSON> the <PERSON>\"><PERSON></a>.", "links": [{"title": "Royal Academy of Turku", "link": "https://wikipedia.org/wiki/Royal_Academy_of_Turku"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Turku", "link": "https://wikipedia.org/wiki/Turku"}, {"title": "<PERSON>, Queen of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden"}, {"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_the_<PERSON>"}]}, {"year": "1651", "text": "Silver-loaded Spanish ship San José is pushed south by strong winds, subsequently it wrecks in the coast of southern Chile and its surviving crew is killed by indigenous Cuncos.", "html": "1651 - <a href=\"https://wikipedia.org/wiki/Real_Situado\" title=\"Real Situado\">Silver-loaded</a> Spanish ship <i>San José</i> is pushed south by strong winds, subsequently <a href=\"https://wikipedia.org/wiki/Wreckage_of_San_Jos%C3%A9\" title=\"Wreckage of San José\">it wrecks in the coast of southern Chile</a> and its surviving crew is killed by indigenous <a href=\"https://wikipedia.org/wiki/Cunco_people\" title=\"Cunco people\">Cuncos</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Real_Situado\" title=\"Real Situado\">Silver-loaded</a> Spanish ship <i>San José</i> is pushed south by strong winds, subsequently <a href=\"https://wikipedia.org/wiki/Wreckage_of_San_Jos%C3%A9\" title=\"Wreckage of San José\">it wrecks in the coast of southern Chile</a> and its surviving crew is killed by indigenous <a href=\"https://wikipedia.org/wiki/Cunco_people\" title=\"Cunco people\">Cuncos</a>.", "links": [{"title": "Real Situado", "link": "https://wikipedia.org/wiki/Real_Situado"}, {"title": "Wreckage of San José", "link": "https://wikipedia.org/wiki/Wreckage_of_San_Jos%C3%A9"}, {"title": "Cunco people", "link": "https://wikipedia.org/wiki/Cunco_people"}]}, {"year": "1697", "text": "Safavid government troops take control of Basra.", "html": "1697 - <a href=\"https://wikipedia.org/wiki/Safavid_Iran\" title=\"Safavid Iran\">Safavid</a> government troops <a href=\"https://wikipedia.org/wiki/Safavid_occupation_of_Basra_(1697%E2%80%931701)\" class=\"mw-redirect\" title=\"Safavid occupation of Basra (1697-1701)\">take control of Basra</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Safavid_Iran\" title=\"Safavid Iran\">Safavid</a> government troops <a href=\"https://wikipedia.org/wiki/Safavid_occupation_of_Basra_(1697%E2%80%931701)\" class=\"mw-redirect\" title=\"Safavid occupation of Basra (1697-1701)\">take control of Basra</a>.", "links": [{"title": "Safavid Iran", "link": "https://wikipedia.org/wiki/Safavid_Iran"}, {"title": "Safavid occupation of Basra (1697-1701)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_occupation_of_Basra_(1697%E2%80%931701)"}]}, {"year": "1700", "text": "<PERSON> is the first European to circumnavigate New Britain, discovering it is an island (which he names Nova Britannia) rather than part of New Guinea.", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first European to circumnavigate <a href=\"https://wikipedia.org/wiki/New_Britain\" title=\"New Britain\">New Britain</a>, discovering it is an island (which he names Nova Britannia) rather than part of <a href=\"https://wikipedia.org/wiki/New_Guinea\" title=\"New Guinea\">New Guinea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first European to circumnavigate <a href=\"https://wikipedia.org/wiki/New_Britain\" title=\"New Britain\">New Britain</a>, discovering it is an island (which he names Nova Britannia) rather than part of <a href=\"https://wikipedia.org/wiki/New_Guinea\" title=\"New Guinea\">New Guinea</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New Britain", "link": "https://wikipedia.org/wiki/New_Britain"}, {"title": "New Guinea", "link": "https://wikipedia.org/wiki/New_Guinea"}]}, {"year": "1812", "text": "An earthquake devastates Caracas, Venezuela.", "html": "1812 - An <a href=\"https://wikipedia.org/wiki/1812_Caracas_earthquake\" title=\"1812 Caracas earthquake\">earthquake</a> devastates <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas</a>, Venezuela.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1812_Caracas_earthquake\" title=\"1812 Caracas earthquake\">earthquake</a> devastates <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas</a>, Venezuela.", "links": [{"title": "1812 Caracas earthquake", "link": "https://wikipedia.org/wiki/1812_Caracas_earthquake"}, {"title": "Caracas", "link": "https://wikipedia.org/wiki/Caracas"}]}, {"year": "1812", "text": "A political cartoon in the Boston-Gazette coins the term \"gerrymander\" to describe oddly shaped electoral districts designed to help incumbents win reelection.", "html": "1812 - A political cartoon in the <i>Boston-Gazette</i> coins the term \"<a href=\"https://wikipedia.org/wiki/Gerrymander\" class=\"mw-redirect\" title=\"Gerrymander\">gerrymander</a>\" to describe oddly shaped <a href=\"https://wikipedia.org/wiki/Electoral_district\" title=\"Electoral district\">electoral districts</a> designed to help incumbents win reelection.", "no_year_html": "A political cartoon in the <i>Boston-Gazette</i> coins the term \"<a href=\"https://wikipedia.org/wiki/Gerrymander\" class=\"mw-redirect\" title=\"Gerrymander\">gerrymander</a>\" to describe oddly shaped <a href=\"https://wikipedia.org/wiki/Electoral_district\" title=\"Electoral district\">electoral districts</a> designed to help incumbents win reelection.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Electoral district", "link": "https://wikipedia.org/wiki/Electoral_district"}]}, {"year": "1830", "text": "The Book of Mormon is published in Palmyra, New York.", "html": "1830 - The <a href=\"https://wikipedia.org/wiki/Book_of_Mormon\" title=\"Book of Mormon\">Book of Mormon</a> is published in <a href=\"https://wikipedia.org/wiki/Palmyra_(town),_New_York\" class=\"mw-redirect\" title=\"Palmyra (town), New York\">Palmyra, New York</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Book_of_Mormon\" title=\"Book of Mormon\">Book of Mormon</a> is published in <a href=\"https://wikipedia.org/wiki/Palmyra_(town),_New_York\" class=\"mw-redirect\" title=\"Palmyra (town), New York\">Palmyra, New York</a>.", "links": [{"title": "Book of Mormon", "link": "https://wikipedia.org/wiki/Book_of_Mormon"}, {"title": "Palmyra (town), New York", "link": "https://wikipedia.org/wiki/Palmyra_(town),_New_York"}]}, {"year": "1839", "text": "The first Henley Royal Regatta is held.", "html": "1839 - The first <a href=\"https://wikipedia.org/wiki/Henley_Royal_Regatta\" title=\"Henley Royal Regatta\">Henley Royal Regatta</a> is held.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Henley_Royal_Regatta\" title=\"Henley Royal Regatta\">Henley Royal Regatta</a> is held.", "links": [{"title": "Henley Royal Regatta", "link": "https://wikipedia.org/wiki/Henley_Royal_Regatta"}]}, {"year": "1871", "text": "The elections of Commune council of the Paris Commune are held.", "html": "1871 - The elections of Commune council of the <a href=\"https://wikipedia.org/wiki/Paris_Commune\" title=\"Paris Commune\">Paris Commune</a> are held.", "no_year_html": "The elections of Commune council of the <a href=\"https://wikipedia.org/wiki/Paris_Commune\" title=\"Paris Commune\">Paris Commune</a> are held.", "links": [{"title": "Paris Commune", "link": "https://wikipedia.org/wiki/Paris_Commune"}]}, {"year": "1885", "text": "The Métis people of the District of Saskatchewan under <PERSON> begin the North-West Rebellion against Canada.", "html": "1885 - The <a href=\"https://wikipedia.org/wiki/M%C3%A9tis_people_(Canada)\" class=\"mw-redirect\" title=\"Métis people (Canada)\"><PERSON><PERSON><PERSON></a> people of the <a href=\"https://wikipedia.org/wiki/District_of_Saskatchewan\" title=\"District of Saskatchewan\">District of Saskatchewan</a> under <a href=\"https://wikipedia.org/wiki/Louis_Riel\" title=\"Louis Riel\"><PERSON></a> begin the <a href=\"https://wikipedia.org/wiki/North-West_Rebellion\" title=\"North-West Rebellion\">North-West Rebellion</a> against <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/M%C3%A9tis_people_(Canada)\" class=\"mw-redirect\" title=\"Métis people (Canada)\"><PERSON><PERSON><PERSON></a> people of the <a href=\"https://wikipedia.org/wiki/District_of_Saskatchewan\" title=\"District of Saskatchewan\">District of Saskatchewan</a> under <a href=\"https://wikipedia.org/wiki/Louis_Riel\" title=\"Louis Riel\"><PERSON></a> begin the <a href=\"https://wikipedia.org/wiki/North-West_Rebellion\" title=\"North-West Rebellion\">North-West Rebellion</a> against <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> people (Canada)", "link": "https://wikipedia.org/wiki/M%C3%A9tis_people_(Canada)"}, {"title": "District of Saskatchewan", "link": "https://wikipedia.org/wiki/District_of_Saskatchewan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>"}, {"title": "North-West Rebellion", "link": "https://wikipedia.org/wiki/North-West_Rebellion"}, {"title": "Canada", "link": "https://wikipedia.org/wiki/Canada"}]}, {"year": "1896", "text": "An explosion at the Brunner Mine near Greymouth, New Zealand, kills 65 coal miners in the country's worst industrial accident.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Brunner_Mine_disaster\" title=\"Brunner Mine disaster\">An explosion</a> at the <a href=\"https://wikipedia.org/wiki/Brunner_Mine\" title=\"Brunner Mine\">Brunner Mine</a> near <a href=\"https://wikipedia.org/wiki/Greymouth\" title=\"Greymouth\">Greymouth</a>, New Zealand, kills 65 coal miners in the country's worst industrial accident.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brunner_Mine_disaster\" title=\"Brunner Mine disaster\">An explosion</a> at the <a href=\"https://wikipedia.org/wiki/Brunner_Mine\" title=\"Brunner Mine\">Brunner Mine</a> near <a href=\"https://wikipedia.org/wiki/Greymouth\" title=\"Greymouth\">Greymouth</a>, New Zealand, kills 65 coal miners in the country's worst industrial accident.", "links": [{"title": "Brunner Mine disaster", "link": "https://wikipedia.org/wiki/Brunner_Mine_disaster"}, {"title": "Brunner Mine", "link": "https://wikipedia.org/wiki/Brunner_Mine"}, {"title": "Greymouth", "link": "https://wikipedia.org/wiki/Greymouth"}]}, {"year": "1913", "text": "First Balkan War: Bulgarian forces capture Adrianople.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgarian</a> forces <a href=\"https://wikipedia.org/wiki/Siege_of_Adrianople_(1912%E2%80%9313)\" class=\"mw-redirect\" title=\"Siege of Adrianople (1912-13)\">capture</a> <a href=\"https://wikipedia.org/wiki/Adrianople\" class=\"mw-redirect\" title=\"Adrianople\">Adrianople</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgarian</a> forces <a href=\"https://wikipedia.org/wiki/Siege_of_Adrianople_(1912%E2%80%9313)\" class=\"mw-redirect\" title=\"Siege of Adrianople (1912-13)\">capture</a> <a href=\"https://wikipedia.org/wiki/Adrianople\" class=\"mw-redirect\" title=\"Adrianople\">Adrianople</a>.", "links": [{"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "Siege of Adrianople (1912-13)", "link": "https://wikipedia.org/wiki/Siege_of_Adrian<PERSON>le_(1912%E2%80%9313)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1915", "text": "The Vancouver Millionaires win the 1915 Stanley Cup Finals, the first championship played between the Pacific Coast Hockey Association and the National Hockey Association.", "html": "1915 - The <a href=\"https://wikipedia.org/wiki/Vancouver_Millionaires\" title=\"Vancouver Millionaires\">Vancouver Millionaires</a> win the <a href=\"https://wikipedia.org/wiki/1915_Stanley_Cup_Finals\" title=\"1915 Stanley Cup Finals\">1915 Stanley Cup Finals</a>, the first championship played between the <a href=\"https://wikipedia.org/wiki/Pacific_Coast_Hockey_Association\" title=\"Pacific Coast Hockey Association\">Pacific Coast Hockey Association</a> and the <a href=\"https://wikipedia.org/wiki/National_Hockey_Association\" title=\"National Hockey Association\">National Hockey Association</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Vancouver_Millionaires\" title=\"Vancouver Millionaires\">Vancouver Millionaires</a> win the <a href=\"https://wikipedia.org/wiki/1915_Stanley_Cup_Finals\" title=\"1915 Stanley Cup Finals\">1915 Stanley Cup Finals</a>, the first championship played between the <a href=\"https://wikipedia.org/wiki/Pacific_Coast_Hockey_Association\" title=\"Pacific Coast Hockey Association\">Pacific Coast Hockey Association</a> and the <a href=\"https://wikipedia.org/wiki/National_Hockey_Association\" title=\"National Hockey Association\">National Hockey Association</a>.", "links": [{"title": "Vancouver Millionaires", "link": "https://wikipedia.org/wiki/Vancouver_Millionaires"}, {"title": "1915 Stanley Cup Finals", "link": "https://wikipedia.org/wiki/1915_Stanley_Cup_Finals"}, {"title": "Pacific Coast Hockey Association", "link": "https://wikipedia.org/wiki/Pacific_Coast_Hockey_Association"}, {"title": "National Hockey Association", "link": "https://wikipedia.org/wiki/National_Hockey_Association"}]}, {"year": "1917", "text": "World War I: First Battle of Gaza: British troops are halted after 17,000 Turks block their advance.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/First_Battle_of_Gaza\" title=\"First Battle of Gaza\">First Battle of Gaza</a>: British troops are halted after 17,000 <a href=\"https://wikipedia.org/wiki/Turkish_people\" title=\"Turkish people\">Turks</a> block their advance.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/First_Battle_of_Gaza\" title=\"First Battle of Gaza\">First Battle of Gaza</a>: British troops are halted after 17,000 <a href=\"https://wikipedia.org/wiki/Turkish_people\" title=\"Turkish people\">Turks</a> block their advance.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "First Battle of Gaza", "link": "https://wikipedia.org/wiki/First_Battle_of_Gaza"}, {"title": "Turkish people", "link": "https://wikipedia.org/wiki/Turkish_people"}]}, {"year": "1922", "text": "The German Social Democratic Party is founded in Poland.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/German_Social_Democratic_Party_of_Poland\" class=\"mw-redirect\" title=\"German Social Democratic Party of Poland\">German Social Democratic Party</a> is founded in Poland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/German_Social_Democratic_Party_of_Poland\" class=\"mw-redirect\" title=\"German Social Democratic Party of Poland\">German Social Democratic Party</a> is founded in Poland.", "links": [{"title": "German Social Democratic Party of Poland", "link": "https://wikipedia.org/wiki/German_Social_Democratic_Party_of_Poland"}]}, {"year": "1931", "text": "Swissair is founded as the national airline of Switzerland.", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Swissair\" title=\"Swissair\">Swissair</a> is founded as the <a href=\"https://wikipedia.org/wiki/National_airline\" class=\"mw-redirect\" title=\"National airline\">national airline</a> of <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Switzerland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swissair\" title=\"Swissair\">Swissair</a> is founded as the <a href=\"https://wikipedia.org/wiki/National_airline\" class=\"mw-redirect\" title=\"National airline\">national airline</a> of <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Switzerland</a>.", "links": [{"title": "Swissair", "link": "https://wikipedia.org/wiki/Swissair"}, {"title": "National airline", "link": "https://wikipedia.org/wiki/National_airline"}, {"title": "Switzerland", "link": "https://wikipedia.org/wiki/Switzerland"}]}, {"year": "1931", "text": "Ho Chi Minh Communist Youth Union is founded in Vietnam.", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Communist_Youth_Union\" title=\"Ho Chi Minh Communist Youth Union\">Ho <PERSON> Minh Communist Youth Union</a> is founded in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Communist_Youth_Union\" title=\"Ho Chi Minh Communist Youth Union\">Ho Chi Minh Communist Youth Union</a> is founded in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "links": [{"title": "Ho <PERSON> Minh Communist Youth Union", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Minh_Communist_Youth_Union"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1934", "text": "The United Kingdom driving test is introduced.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/United_Kingdom_driving_test\" title=\"United Kingdom driving test\">United Kingdom driving test</a> is introduced.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Kingdom_driving_test\" title=\"United Kingdom driving test\">United Kingdom driving test</a> is introduced.", "links": [{"title": "United Kingdom driving test", "link": "https://wikipedia.org/wiki/United_Kingdom_driving_test"}]}, {"year": "1939", "text": "Spanish Civil War: Nationalists begin their final offensive of the war.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Francoist_Spain\" title=\"Francoist Spain\">Nationalists</a> begin their <a href=\"https://wikipedia.org/wiki/Final_offensive_of_the_Spanish_Civil_War\" title=\"Final offensive of the Spanish Civil War\">final offensive</a> of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Francoist_Spain\" title=\"Francoist Spain\">Nationalists</a> begin their <a href=\"https://wikipedia.org/wiki/Final_offensive_of_the_Spanish_Civil_War\" title=\"Final offensive of the Spanish Civil War\">final offensive</a> of the war.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Francoist Spain", "link": "https://wikipedia.org/wiki/Francoist_Spain"}, {"title": "Final offensive of the Spanish Civil War", "link": "https://wikipedia.org/wiki/Final_offensive_of_the_Spanish_Civil_War"}]}, {"year": "1942", "text": "World War II: The first female prisoners arrive at Auschwitz concentration camp in German-occupied Poland.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The first female prisoners arrive at <a href=\"https://wikipedia.org/wiki/Auschwitz\" class=\"mw-redirect\" title=\"Auschwitz\">Auschwitz</a> <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camp</a> in <a href=\"https://wikipedia.org/wiki/Polish_areas_annexed_by_Nazi_Germany\" title=\"Polish areas annexed by Nazi Germany\">German-occupied Poland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The first female prisoners arrive at <a href=\"https://wikipedia.org/wiki/Auschwitz\" class=\"mw-redirect\" title=\"Auschwitz\">Auschwitz</a> <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camp</a> in <a href=\"https://wikipedia.org/wiki/Polish_areas_annexed_by_Nazi_Germany\" title=\"Polish areas annexed by Nazi Germany\">German-occupied Poland</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Auschwitz", "link": "https://wikipedia.org/wiki/Auschwitz"}, {"title": "Nazi concentration camps", "link": "https://wikipedia.org/wiki/Nazi_concentration_camps"}, {"title": "Polish areas annexed by Nazi Germany", "link": "https://wikipedia.org/wiki/Polish_areas_annexed_by_Nazi_Germany"}]}, {"year": "1945", "text": "World War II: The Battle of Iwo Jima ends as the island is officially secured by American forces.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a> ends as the island is officially secured by American forces.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a> ends as the island is officially secured by American forces.", "links": [{"title": "Battle of Iwo Jima", "link": "https://wikipedia.org/wiki/Battle_of_Iwo_Jima"}]}, {"year": "1954", "text": "Nuclear weapons testing: The Romeo shot of Operation Castle is detonated at Bikini Atoll. Yield: 11 megatons.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: The <a href=\"https://wikipedia.org/wiki/Castle_Romeo\" title=\"Castle Romeo\">Romeo shot</a> of <a href=\"https://wikipedia.org/wiki/Operation_Castle\" title=\"Operation Castle\">Operation Castle</a> is detonated at <a href=\"https://wikipedia.org/wiki/Bikini_Atoll\" title=\"Bikini Atoll\">Bikini Atoll</a>. Yield: 11 megatons.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: The <a href=\"https://wikipedia.org/wiki/Castle_Romeo\" title=\"Castle Romeo\">Romeo shot</a> of <a href=\"https://wikipedia.org/wiki/Operation_Castle\" title=\"Operation Castle\">Operation Castle</a> is detonated at <a href=\"https://wikipedia.org/wiki/Bikini_Atoll\" title=\"Bikini Atoll\">Bikini Atoll</a>. Yield: 11 megatons.", "links": [{"title": "Nuclear weapons testing", "link": "https://wikipedia.org/wiki/Nuclear_weapons_testing"}, {"title": "Castle Romeo", "link": "https://wikipedia.org/wiki/Castle_Romeo"}, {"title": "Operation Castle", "link": "https://wikipedia.org/wiki/Operation_Castle"}, {"title": "Bikini Atoll", "link": "https://wikipedia.org/wiki/Bikini_Atoll"}]}, {"year": "1955", "text": "Pan Am Flight 845/26 ditches in the Pacific Ocean off the coast of Oregon, killing four.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_845/26\" title=\"Pan Am Flight 845/26\">Pan Am Flight 845/26</a> ditches in the <a href=\"https://wikipedia.org/wiki/Pacific_Ocean\" title=\"Pacific Ocean\">Pacific Ocean</a> off the coast of <a href=\"https://wikipedia.org/wiki/Oregon\" title=\"Oregon\">Oregon</a>, killing four.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_845/26\" title=\"Pan Am Flight 845/26\">Pan Am Flight 845/26</a> ditches in the <a href=\"https://wikipedia.org/wiki/Pacific_Ocean\" title=\"Pacific Ocean\">Pacific Ocean</a> off the coast of <a href=\"https://wikipedia.org/wiki/Oregon\" title=\"Oregon\">Oregon</a>, killing four.", "links": [{"title": "Pan Am Flight 845/26", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_845/26"}, {"title": "Pacific Ocean", "link": "https://wikipedia.org/wiki/Pacific_Ocean"}, {"title": "Oregon", "link": "https://wikipedia.org/wiki/Oregon"}]}, {"year": "1958", "text": "The United States Army launches Explorer 3.", "html": "1958 - The United States Army launches <a href=\"https://wikipedia.org/wiki/Explorer_3\" title=\"Explorer 3\">Explorer 3</a>.", "no_year_html": "The United States Army launches <a href=\"https://wikipedia.org/wiki/Explorer_3\" title=\"Explorer 3\">Explorer 3</a>.", "links": [{"title": "Explorer 3", "link": "https://wikipedia.org/wiki/Explorer_3"}]}, {"year": "1958", "text": "The African Regroupment Party is launched at a meeting in Paris.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/African_Regroupment_Party\" title=\"African Regroupment Party\">African Regroupment Party</a> is launched at a meeting in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/African_Regroupment_Party\" title=\"African Regroupment Party\">African Regroupment Party</a> is launched at a meeting in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "links": [{"title": "African Regroupment Party", "link": "https://wikipedia.org/wiki/African_Regroupment_Party"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "1967", "text": "Ten thousand people gather for one of many Central Park be-ins in New York City.", "html": "1967 - Ten thousand people gather for one of many <a href=\"https://wikipedia.org/wiki/Central_Park_be-ins\" title=\"Central Park be-ins\">Central Park be-ins</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "no_year_html": "Ten thousand people gather for one of many <a href=\"https://wikipedia.org/wiki/Central_Park_be-ins\" title=\"Central Park be-ins\">Central Park be-ins</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "links": [{"title": "Central Park be-ins", "link": "https://wikipedia.org/wiki/Central_Park_be-ins"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1970", "text": "South Vietnamese President <PERSON><PERSON><PERSON><PERSON> implements a land reform program to solve the problem of land tenancy.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> President <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87u\" title=\"<PERSON>uy<PERSON><PERSON>\"><PERSON>uy<PERSON><PERSON></a> implements a <a href=\"https://wikipedia.org/wiki/Land_to_the_Tiller_(South_Vietnam)\" class=\"mw-redirect\" title=\"Land to the Tiller (South Vietnam)\">land reform program</a> to solve the problem of <a href=\"https://wikipedia.org/wiki/Tenant_farmer\" title=\"Tenant farmer\">land tenancy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> President <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87u\" title=\"<PERSON>uy<PERSON><PERSON>\"><PERSON>uy<PERSON><PERSON></a> implements a <a href=\"https://wikipedia.org/wiki/Land_to_the_Tiller_(South_Vietnam)\" class=\"mw-redirect\" title=\"Land to the Tiller (South Vietnam)\">land reform program</a> to solve the problem of <a href=\"https://wikipedia.org/wiki/Tenant_farmer\" title=\"Tenant farmer\">land tenancy</a>.", "links": [{"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87u"}, {"title": "Land to the Tiller (South Vietnam)", "link": "https://wikipedia.org/wiki/Land_to_the_<PERSON>er_(South_Vietnam)"}, {"title": "Tenant farmer", "link": "https://wikipedia.org/wiki/Tenant_farmer"}]}, {"year": "1971", "text": "East Pakistan, then province of Pakistan, declares its independence from Pakistan to form Bangladesh; the Bangladesh's War of Independence begins.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a>, then province of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, <a href=\"https://wikipedia.org/wiki/Proclamation_of_Bangladeshi_Independence\" title=\"Proclamation of Bangladeshi Independence\">declares its independence</a> from Pakistan to form <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>; the <a href=\"https://wikipedia.org/wiki/Bangladesh_Liberation_War\" title=\"Bangladesh Liberation War\">Bangladesh's War of Independence</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a>, then province of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, <a href=\"https://wikipedia.org/wiki/Proclamation_of_Bangladeshi_Independence\" title=\"Proclamation of Bangladeshi Independence\">declares its independence</a> from Pakistan to form <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>; the <a href=\"https://wikipedia.org/wiki/Bangladesh_Liberation_War\" title=\"Bangladesh Liberation War\">Bangladesh's War of Independence</a> begins.", "links": [{"title": "East Pakistan", "link": "https://wikipedia.org/wiki/East_Pakistan"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "Proclamation of Bangladeshi Independence", "link": "https://wikipedia.org/wiki/Proclamation_of_Bangladeshi_Independence"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}, {"title": "Bangladesh Liberation War", "link": "https://wikipedia.org/wiki/Bangladesh_Liberation_War"}]}, {"year": "1975", "text": "The Biological Weapons Convention comes into force.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Biological_Weapons_Convention\" title=\"Biological Weapons Convention\">Biological Weapons Convention</a> comes into force.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Biological_Weapons_Convention\" title=\"Biological Weapons Convention\">Biological Weapons Convention</a> comes into force.", "links": [{"title": "Biological Weapons Convention", "link": "https://wikipedia.org/wiki/Biological_Weapons_Convention"}]}, {"year": "1979", "text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON> sign the Egypt-Israel peace treaty in Washington, D.C.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>at\" class=\"mw-redirect\" title=\"<PERSON>war al-Sadat\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gin\" title=\"<PERSON><PERSON><PERSON>gin\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Egypt%E2%80%93Israel_peace_treaty\" title=\"Egypt-Israel peace treaty\">Egypt-Israel peace treaty</a> in Washington, D.C.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>war al-Sadat\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gin\" title=\"<PERSON><PERSON><PERSON>gin\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Egypt%E2%80%93Israel_peace_treaty\" title=\"Egypt-Israel peace treaty\">Egypt-Israel peace treaty</a> in Washington, D.C.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gin"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Egypt-Israel peace treaty", "link": "https://wikipedia.org/wiki/Egypt%E2%80%93Israel_peace_treaty"}]}, {"year": "1979", "text": "An Interflug Ilyushin Il-18 crashes at Quatro de Fevereiro Airport during a rejected takeoff, killing 10.", "html": "1979 - An <a href=\"https://wikipedia.org/wiki/Interflug\" title=\"Interflug\">Interflug</a> <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-18\" title=\"Ilyushin Il-18\">Ilyushin Il-18</a> <a href=\"https://wikipedia.org/wiki/1979_Interflug_Ilyushin_Il-18_crash\" title=\"1979 Interflug Ilyushin Il-18 crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Quatro_de_Fevereiro_Airport\" title=\"Quatro de Fevereiro Airport\">Quatro de Fevereiro Airport</a> during a <a href=\"https://wikipedia.org/wiki/Rejected_takeoff\" title=\"Rejected takeoff\">rejected takeoff</a>, killing 10.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Interflug\" title=\"Interflug\">Interflug</a> <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-18\" title=\"Ilyushin Il-18\">Ilyushin Il-18</a> <a href=\"https://wikipedia.org/wiki/1979_Interflug_Ilyushin_Il-18_crash\" title=\"1979 Interflug Ilyushin Il-18 crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Quatro_de_Fevereiro_Airport\" title=\"Quatro de Fevereiro Airport\">Quatro de Fevereiro Airport</a> during a <a href=\"https://wikipedia.org/wiki/Rejected_takeoff\" title=\"Rejected takeoff\">rejected takeoff</a>, killing 10.", "links": [{"title": "Interflug", "link": "https://wikipedia.org/wiki/Interflug"}, {"title": "Ilyushin Il-18", "link": "https://wikipedia.org/wiki/Ilyushin_Il-18"}, {"title": "1979 Interflug Ilyushin Il-18 crash", "link": "https://wikipedia.org/wiki/1979_Interflug_Ilyushin_Il-18_crash"}, {"title": "Quatro de Fevereiro Airport", "link": "https://wikipedia.org/wiki/Quatro_de_Fevereiro_Airport"}, {"title": "Rejected takeoff", "link": "https://wikipedia.org/wiki/Rejected_takeoff"}]}, {"year": "1981", "text": "Social Democratic Party (UK) is founded as a party.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_(UK)\" title=\"Social Democratic Party (UK)\">Social Democratic Party (UK)</a> is founded as a party.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_(UK)\" title=\"Social Democratic Party (UK)\">Social Democratic Party (UK)</a> is founded as a party.", "links": [{"title": "Social Democratic Party (UK)", "link": "https://wikipedia.org/wiki/Social_Democratic_Party_(UK)"}]}, {"year": "1982", "text": "A groundbreaking ceremony for the Vietnam Veterans Memorial is held in Washington, D.C.", "html": "1982 - A groundbreaking ceremony for the <a href=\"https://wikipedia.org/wiki/Vietnam_Veterans_Memorial\" title=\"Vietnam Veterans Memorial\">Vietnam Veterans Memorial</a> is held in Washington, D.C.", "no_year_html": "A groundbreaking ceremony for the <a href=\"https://wikipedia.org/wiki/Vietnam_Veterans_Memorial\" title=\"Vietnam Veterans Memorial\">Vietnam Veterans Memorial</a> is held in Washington, D.C.", "links": [{"title": "Vietnam Veterans Memorial", "link": "https://wikipedia.org/wiki/Vietnam_Veterans_Memorial"}]}, {"year": "1991", "text": "Argentina, Brazil, Uruguay and Paraguay sign the Treaty of Asunción, establishing Mercosur, the South Common Market.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a>, <a href=\"https://wikipedia.org/wiki/Uruguay\" title=\"Uruguay\">Uruguay</a> and <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Asunci%C3%B3n\" title=\"Treaty of Asunción\">Treaty of Asunción</a>, establishing <a href=\"https://wikipedia.org/wiki/Mercosur\" title=\"Mercosur\">Mercosur</a>, the South Common Market.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a>, <a href=\"https://wikipedia.org/wiki/Uruguay\" title=\"Uruguay\">Uruguay</a> and <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Asunci%C3%B3n\" title=\"Treaty of Asunción\">Treaty of Asunción</a>, establishing <a href=\"https://wikipedia.org/wiki/Mercosur\" title=\"Mercosur\">Mercosur</a>, the South Common Market.", "links": [{"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "Uruguay", "link": "https://wikipedia.org/wiki/Uruguay"}, {"title": "Paraguay", "link": "https://wikipedia.org/wiki/Paraguay"}, {"title": "Treaty of Asunción", "link": "https://wikipedia.org/wiki/Treaty_of_Asunci%C3%B3n"}, {"title": "Mercosur", "link": "https://wikipedia.org/wiki/Mercosur"}]}, {"year": "1991", "text": "Singapore Airlines Flight 117 is hijacked by four Pakistani terrorists and diverted to Changi Airport.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Singapore_Airlines_Flight_117\" title=\"Singapore Airlines Flight 117\">Singapore Airlines Flight 117</a> is hijacked by four Pakistani terrorists and diverted to <a href=\"https://wikipedia.org/wiki/Changi_Airport\" title=\"Changi Airport\">Changi Airport</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Singapore_Airlines_Flight_117\" title=\"Singapore Airlines Flight 117\">Singapore Airlines Flight 117</a> is hijacked by four Pakistani terrorists and diverted to <a href=\"https://wikipedia.org/wiki/Changi_Airport\" title=\"Changi Airport\">Changi Airport</a>.", "links": [{"title": "Singapore Airlines Flight 117", "link": "https://wikipedia.org/wiki/Singapore_Airlines_Flight_117"}, {"title": "Changi Airport", "link": "https://wikipedia.org/wiki/Changi_Airport"}]}, {"year": "1997", "text": "Thirty-nine bodies are found in the Heaven's Gate mass suicides.", "html": "1997 - Thirty-nine bodies are found in the <a href=\"https://wikipedia.org/wiki/Heaven%27s_Gate_(cult)\" class=\"mw-redirect\" title=\"Heaven's Gate (cult)\">Heaven's Gate</a> <a href=\"https://wikipedia.org/wiki/Mass_suicide\" title=\"Mass suicide\">mass suicides</a>.", "no_year_html": "Thirty-nine bodies are found in the <a href=\"https://wikipedia.org/wiki/Heaven%27s_Gate_(cult)\" class=\"mw-redirect\" title=\"Heaven's Gate (cult)\">Heaven's Gate</a> <a href=\"https://wikipedia.org/wiki/Mass_suicide\" title=\"Mass suicide\">mass suicides</a>.", "links": [{"title": "Heaven's Gate (cult)", "link": "https://wikipedia.org/wiki/Heaven%27s_Gate_(cult)"}, {"title": "Mass suicide", "link": "https://wikipedia.org/wiki/Mass_suicide"}]}, {"year": "1998", "text": "During the Algerian Civil War, the Oued Bouaicha massacre sees fifty-two people, mostly infants, killed with axes and knives.", "html": "1998 - During the <a href=\"https://wikipedia.org/wiki/Algerian_Civil_War\" title=\"Algerian Civil War\">Algerian Civil War</a>, the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bouaicha_massacre\" title=\"Oued Bouaicha massacre\">Oued Bouaicha massacre</a> sees fifty-two people, mostly infants, killed with axes and knives.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Algerian_Civil_War\" title=\"Algerian Civil War\">Algerian Civil War</a>, the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bouaicha_massacre\" title=\"Oued Bouaicha massacre\">Oued Bouaicha massacre</a> sees fifty-two people, mostly infants, killed with axes and knives.", "links": [{"title": "Algerian Civil War", "link": "https://wikipedia.org/wiki/Algerian_Civil_War"}, {"title": "<PERSON><PERSON> massacre", "link": "https://wikipedia.org/wiki/O<PERSON>_<PERSON>icha_massacre"}]}, {"year": "2005", "text": "Around 200,000 to 300,000 Taiwanese demonstrate in Taipei in opposition to the Anti-Secession Law of  China.", "html": "2005 - Around 200,000 to 300,000 <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwanese</a> demonstrate in <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a> in opposition to the <a href=\"https://wikipedia.org/wiki/Anti-Secession_Law\" title=\"Anti-Secession Law\">Anti-Secession Law</a> of <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>.", "no_year_html": "Around 200,000 to 300,000 <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwanese</a> demonstrate in <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a> in opposition to the <a href=\"https://wikipedia.org/wiki/Anti-Secession_Law\" title=\"Anti-Secession Law\">Anti-Secession Law</a> of <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>.", "links": [{"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}, {"title": "Taipei", "link": "https://wikipedia.org/wiki/Taipei"}, {"title": "Anti-Secession Law", "link": "https://wikipedia.org/wiki/Anti-Secession_Law"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}]}, {"year": "2010", "text": "The South Korean Navy corvette Cheonan is torpedoed, killing 46 sailors. After an international investigation, the President of the United Nations Security Council blames North Korea.", "html": "2010 - The South Korean Navy corvette <i><a href=\"https://wikipedia.org/wiki/ROK<PERSON>_Cheonan\" title=\"ROK<PERSON> Cheonan\"><PERSON><PERSON><PERSON></a></i> is <a href=\"https://wikipedia.org/wiki/ROK<PERSON>_Cheon<PERSON>_sinking\" title=\"<PERSON><PERSON><PERSON> Cheonan sinking\">torpedoed</a>, killing 46 sailors. After an international investigation, the <a href=\"https://wikipedia.org/wiki/President_of_the_United_Nations_Security_Council\" class=\"mw-redirect\" title=\"President of the United Nations Security Council\">President of the United Nations Security Council</a> blames North Korea.", "no_year_html": "The South Korean Navy corvette <i><a href=\"https://wikipedia.org/wiki/ROKS_Cheonan\" title=\"ROK<PERSON> Cheonan\"><PERSON><PERSON><PERSON></a></i> is <a href=\"https://wikipedia.org/wiki/ROK<PERSON>_Cheon<PERSON>_sinking\" title=\"ROK<PERSON> Cheonan sinking\">torpedoed</a>, killing 46 sailors. After an international investigation, the <a href=\"https://wikipedia.org/wiki/President_of_the_United_Nations_Security_Council\" class=\"mw-redirect\" title=\"President of the United Nations Security Council\">President of the United Nations Security Council</a> blames North Korea.", "links": [{"title": "ROKS Cheonan", "link": "https://wikipedia.org/wiki/ROK<PERSON>_Cheonan"}, {"title": "ROKS Cheonan sinking", "link": "https://wikipedia.org/wiki/ROK<PERSON>_<PERSON><PERSON><PERSON>_sinking"}, {"title": "President of the United Nations Security Council", "link": "https://wikipedia.org/wiki/President_of_the_United_Nations_Security_Council"}]}, {"year": "2017", "text": "Russia-wide anti-corruption protests in 99 cities. The Levada Center survey showed that 38% of surveyed Russians supported protests and that 67 percent held <PERSON> personally responsible for high-level corruption.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/2017%E2%80%932018_Russian_protests\" title=\"2017-2018 Russian protests\">Russia-wide anti-corruption protests</a> in 99 cities. The <a href=\"https://wikipedia.org/wiki/Levada_Center\" title=\"Levada Center\">Levada Center</a> survey showed that 38% of surveyed Russians supported protests and that 67 percent held <PERSON> personally responsible for high-level corruption.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2017%E2%80%932018_Russian_protests\" title=\"2017-2018 Russian protests\">Russia-wide anti-corruption protests</a> in 99 cities. The <a href=\"https://wikipedia.org/wiki/Levada_Center\" title=\"Levada Center\">Levada Center</a> survey showed that 38% of surveyed Russians supported protests and that 67 percent held <PERSON> personally responsible for high-level corruption.", "links": [{"title": "2017-2018 Russian protests", "link": "https://wikipedia.org/wiki/2017%E2%80%932018_Russian_protests"}, {"title": "Levada Center", "link": "https://wikipedia.org/wiki/Levada_Center"}]}, {"year": "2024", "text": "The Francis Scott Key Bridge collapses following a collision between the MV Dali container ship and one of the bridge's support pillars, killing 6 people.", "html": "2024 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Bridge_(Baltimore)\" title=\"Francis <PERSON> Bridge (Baltimore)\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Bridge_collapse\" title=\"Francis <PERSON> Key Bridge collapse\">collapses</a> following a collision between the <a href=\"https://wikipedia.org/wiki/MV_Dali\" title=\"MV Dali\">MV Dali</a> container ship and one of the bridge's support pillars, killing 6 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Bridge_(Baltimore)\" title=\"Francis <PERSON> Bridge (Baltimore)\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Bridge_collapse\" title=\"Francis <PERSON> Bridge collapse\">collapses</a> following a collision between the <a href=\"https://wikipedia.org/wiki/MV_Dali\" title=\"MV Dali\">MV Dali</a> container ship and one of the bridge's support pillars, killing 6 people.", "links": [{"title": "<PERSON> (Baltimore)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Key_Bridge_(Baltimore)"}, {"title": "<PERSON> Key Bridge collapse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Key_Bridge_collapse"}, {"title": "MV Dali", "link": "https://wikipedia.org/wiki/MV_Dali"}]}], "Births": [{"year": "1516", "text": "<PERSON>, Swiss botanist and zoologist (d. 1565)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss botanist and zoologist (d. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss botanist and zoologist (d. 1565)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1554", "text": "<PERSON> of Lorraine, duke of Mayenne (d. 1611)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mayenne\" title=\"<PERSON>, Duke of Mayenne\"><PERSON> of Lorraine</a>, duke of Mayenne (d. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mayenne\" title=\"<PERSON>, Duke of Mayenne\"><PERSON> of Lorraine</a>, duke of Mayenne (d. 1611)", "links": [{"title": "<PERSON>, Duke of Mayenne", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_May<PERSON>"}]}, {"year": "1584", "text": "<PERSON> <PERSON>, duke of Zweibrücken (d. 1635)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>, Count <PERSON><PERSON> of Zweibrücken\"><PERSON> II</a>, duke of Zweibrücken (d. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>, Count <PERSON><PERSON> of Zweibrücken\"><PERSON></a>, duke of Zweibrücken (d. 1635)", "links": [{"title": "<PERSON> <PERSON>, Count <PERSON><PERSON> of Zweibrücken", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1633", "text": "<PERSON>, British artist (d. 1699)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British artist (d. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British artist (d. 1699)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1634", "text": "<PERSON>, Italian priest and composer (d. 1710)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and composer (d. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and composer (d. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1656", "text": "<PERSON><PERSON>, Dutch mathematician and physicist (d. 1725)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and physicist (d. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and physicist (d. 1725)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1687", "text": "<PERSON> of Hanover, queen consort of Prussia (d. 1757)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hanover\" title=\"<PERSON> of Hanover\"><PERSON> of Hanover</a>, queen consort of Prussia (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hanover\" title=\"<PERSON> of Hanover\"><PERSON> of Hanover</a>, queen consort of Prussia (d. 1757)", "links": [{"title": "<PERSON> of Hanover", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Hanover"}]}, {"year": "1698", "text": "<PERSON><PERSON><PERSON>, Czech priest, scientist and inventor (d. 1765)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/Prokop_Divi%C5%A1\" title=\"Proko<PERSON> Diviš\"><PERSON><PERSON><PERSON></a>, Czech priest, scientist and inventor (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prokop_Divi%C5%A1\" title=\"Prokop Diviš\"><PERSON><PERSON><PERSON></a>, Czech priest, scientist and inventor (d. 1765)", "links": [{"title": "Prokop Diviš", "link": "https://wikipedia.org/wiki/Prokop_Divi%C5%A1"}]}, {"year": "1749", "text": "<PERSON>, American politician (d. 1800)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1753", "text": "<PERSON>, American-French physicist and politician, Under-Secretary of State for the Colonies (d. 1814)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French physicist and politician, <a href=\"https://wikipedia.org/wiki/Under-Secretary_of_State_for_the_Colonies\" title=\"Under-Secretary of State for the Colonies\">Under-Secretary of State for the Colonies</a> (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French physicist and politician, <a href=\"https://wikipedia.org/wiki/Under-Secretary_of_State_for_the_Colonies\" title=\"Under-Secretary of State for the Colonies\">Under-Secretary of State for the Colonies</a> (d. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Under-Secretary of State for the Colonies", "link": "https://wikipedia.org/wiki/Under-Secretary_of_State_for_the_Colonies"}]}, {"year": "1773", "text": "<PERSON>, American mathematician and navigator (d. 1838)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and navigator (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and navigator (d. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, German painter (d. 1872)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_von_Carolsfeld\" title=\"<PERSON> von Carolsfeld\"><PERSON> von <PERSON>feld</a>, German painter (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_von_Carolsfeld\" title=\"<PERSON> von Carolsfeld\"><PERSON> von Carolsfeld</a>, German painter (d. 1872)", "links": [{"title": "<PERSON> von Carol<PERSON>feld", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, American physician and academic (d. 1891)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON><PERSON><PERSON><PERSON>, French journalist (d. 1874)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French journalist (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French journalist (d. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1829", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French poet (d. 1886)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>\" title=\"Th<PERSON>od<PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, French poet (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>\" title=\"Th<PERSON><PERSON><PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, French poet (d. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON><PERSON>"}]}, {"year": "1829", "text": "<PERSON>, Norwegian architect (d. 1917)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian architect (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian architect (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON>, American politician, 18th Governor of Tennessee (d. 1898)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (d. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Tennessee", "link": "https://wikipedia.org/wiki/Governor_of_Tennessee"}]}, {"year": "1842", "text": "<PERSON>, French occultist (d. 1909)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Alveydre\" title=\"<PERSON>\"><PERSON></a>, French occultist (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Alveydre\" title=\"<PERSON>\"><PERSON></a>, French occultist (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_<PERSON>%27Alveydre"}]}, {"year": "1850", "text": "<PERSON>, American author, socialist, and utopian visionary (d. 1898)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, socialist, and utopian visionary (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, socialist, and utopian visionary (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON><PERSON><PERSON>, French author (d. 1925)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/%C3%89l%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89l%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author (d. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89l%C3%A9mir_Bourges"}]}, {"year": "1854", "text": "<PERSON>, French target shooter (d. 1925)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French target shooter (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French target shooter (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Irish-New Zealand farmer and politician, 19th Prime Minister of New Zealand (d. 1925)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-New Zealand farmer and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-New Zealand farmer and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1857", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French surgeon (d. 1929)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Th%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Th<PERSON><PERSON><PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, French surgeon (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Th<PERSON><PERSON><PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, French surgeon (d. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON> <PERSON><PERSON>, English poet and scholar (d. 1936)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet and scholar (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet and scholar (d. 1936)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, German-Swiss mathematician and academic (d. 1919)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss mathematician and academic (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss mathematician and academic (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, French tennis player (d. 1919)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Pr%C3%A9<PERSON>t_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, French tennis player (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Pr%C3%A9<PERSON>t_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, French tennis player (d. 1919)", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Pr%C3%A9vost_(tennis)"}]}, {"year": "1866", "text": "<PERSON>, English producer and manager (d. 1941)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English producer and manager (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English producer and manager (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "King <PERSON><PERSON> of Egypt (d. 1936)", "html": "1868 - King <a href=\"https://wikipedia.org/wiki/Fuad_I_of_Egypt\" title=\"Fuad I of Egypt\"><PERSON><PERSON> of Egypt</a> (d. 1936)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Fuad_I_of_Egypt\" title=\"Fuad I of Egypt\"><PERSON><PERSON> of Egypt</a> (d. 1936)", "links": [{"title": "<PERSON><PERSON> I of Egypt", "link": "https://wikipedia.org/wiki/Fuad_I_of_Egypt"}]}, {"year": "1873", "text": "<PERSON>, South African-German anthropologist and philologist (d. 1948)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-German anthropologist and philologist (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-German anthropologist and philologist (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American poet and playwright (d. 1963)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Polish-German physicist and academic (d. 1922)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German physicist and academic (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German physicist and academic (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, South Korean journalist and politician, 1st President of South Korea (d. 1965)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>yn<PERSON>_<PERSON>\" title=\"Syngman Rhee\"><PERSON><PERSON><PERSON></a>, South Korean journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yn<PERSON>_<PERSON>\" title=\"Syngman Rhee\"><PERSON><PERSON><PERSON></a>, South Korean journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "1876", "text": "<PERSON> Wied, prince of Albania (d. 1945)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Albania\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Albania\"><PERSON> of <PERSON>ied</a>, prince of Albania (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Albania\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Albania\"><PERSON> of Wied</a>, prince of Albania (d. 1945)", "links": [{"title": "<PERSON>, Prince of Albania", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Albania"}]}, {"year": "1876", "text": "<PERSON>, American Socialist Party activist and editor (d. 1948)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Hare\" title=\"<PERSON>\"><PERSON></a>, American Socialist Party activist and editor (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Hare\" title=\"<PERSON>\"><PERSON></a>, American Socialist Party activist and editor (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Hare"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, Swiss-American engineer, designed the George Washington Bridge and Verrazzano-Narrows Bridge (d. 1965)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss-American engineer, designed the <a href=\"https://wikipedia.org/wiki/George_Washington_Bridge\" title=\"George Washington Bridge\">George <PERSON> Bridge</a> and <a href=\"https://wikipedia.org/wiki/Verrazzano-Narrows_Bridge\" title=\"Verrazzano-Narrows Bridge\">Verrazzano-Narrows Bridge</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss-American engineer, designed the <a href=\"https://wikipedia.org/wiki/George_Washington_Bridge\" title=\"George Washington Bridge\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Verrazzano-Narrows_Bridge\" title=\"Verrazzano-Narrows Bridge\">Verrazzano-Narrows Bridge</a> (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington_Bridge"}, {"title": "Verrazzano-Narrows Bridge", "link": "https://wikipedia.org/wiki/Verrazzano-Narrows_Bridge"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON><PERSON>, German rower (d. 1917)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Wald<PERSON><PERSON>_T<PERSON>\" title=\"Wald<PERSON><PERSON> T<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German rower (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wald<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German rower (d. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waldemar_T<PERSON>gens"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Italian fashion designer, founded <PERSON><PERSON> (d. 1953)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gucci\"><PERSON><PERSON><PERSON></a>, Italian fashion designer, founded <a href=\"https://wikipedia.org/wiki/G<PERSON>\" title=\"G<PERSON>\"><PERSON><PERSON></a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gucci\"><PERSON><PERSON><PERSON></a>, Italian fashion designer, founded <a href=\"https://wikipedia.org/wiki/G<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Swiss politician (d. 1940)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, German pianist and educator (d. 1969)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and educator (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and educator (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, French chemical engineer and inventor (d. 1950)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemical engineer and inventor (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemical engineer and inventor (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Vincentian-American soldier and politician (d. 1971)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vincentian-American soldier and politician (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vincentian-American soldier and politician (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Swedish nurse and philanthropist (d. 1948)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Elsa_Br%C3%A4ndstr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish nurse and philanthropist (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elsa_Br%C3%A4ndstr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish nurse and philanthropist (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elsa_Br%C3%A4ndstr%C3%B6m"}]}, {"year": "1893", "text": "<PERSON>, American chemist, academic, and diplomat, 1st United States Ambassador to West Germany (d. 1978)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American chemist, academic, and diplomat, 1st <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_West_Germany\" class=\"mw-redirect\" title=\"United States Ambassador to West Germany\">United States Ambassador to West Germany</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American chemist, academic, and diplomat, 1st <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_West_Germany\" class=\"mw-redirect\" title=\"United States Ambassador to West Germany\">United States Ambassador to West Germany</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to West Germany", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_West_Germany"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Italian journalist and politician, Italian Minister of Justice (d. 1964)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Palmiro_Togliatti\" title=\"<PERSON><PERSON> Togliatti\"><PERSON><PERSON></a>, Italian journalist and politician, <a href=\"https://wikipedia.org/wiki/Italian_Minister_of_Justice\" class=\"mw-redirect\" title=\"Italian Minister of Justice\">Italian Minister of Justice</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Palmiro_Togliatti\" title=\"<PERSON><PERSON> Togliatti\"><PERSON><PERSON></a>, Italian journalist and politician, <a href=\"https://wikipedia.org/wiki/Italian_Minister_of_Justice\" class=\"mw-redirect\" title=\"Italian Minister of Justice\">Italian Minister of Justice</a> (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Palmiro_<PERSON>"}, {"title": "Italian Minister of Justice", "link": "https://wikipedia.org/wiki/Italian_Minister_of_Justice"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Romanian soprano and actress (d. 1985)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>ior<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Romanian soprano and actress (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Romanian soprano and actress (d. 1985)", "links": [{"title": "Viorica <PERSON>", "link": "https://wikipedia.org/wiki/Vior<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Finnish triple jumper (d. 1967)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish triple jumper (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish triple jumper (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, German businessman, founded Puma SE (d. 1974)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman, founded <a href=\"https://wikipedia.org/wiki/Puma_SE\" class=\"mw-redirect\" title=\"Puma SE\">Puma SE</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman, founded <a href=\"https://wikipedia.org/wiki/Puma_SE\" class=\"mw-redirect\" title=\"Puma SE\">Puma SE</a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Puma SE", "link": "https://wikipedia.org/wiki/Puma_SE"}]}, {"year": "1898", "text": "<PERSON>, English conductor and bandleader (d. 1979)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English conductor and bandleader (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English conductor and bandleader (d. 1979)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1900", "text": "<PERSON>, German nun, died in Auschwitz helping Jewish prisoners (d. 1941)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German nun, died in Auschwitz helping Jewish prisoners (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German nun, died in Auschwitz helping Jewish prisoners (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American mythologist and author (d. 1987)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mythologist and author (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mythologist and author (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Mexican actor, director, and screenwriter (d. 1986)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican actor, director, and screenwriter (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican actor, director, and screenwriter (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emilio_Fern%C3%A1ndez"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Italian footballer (d. 1947)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Attilio_Ferraris\" title=\"Attilio Ferraris\"><PERSON><PERSON><PERSON></a>, Italian footballer (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Attilio_Ferraris\" title=\"Attilio Ferraris\"><PERSON><PERSON><PERSON></a>, Italian footballer (d. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Attilio_Ferraris"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Greek economist and Prime Minister of Greece (d. 2004)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Xenophon Zolo<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek economist and Prime Minister of Greece (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Xenophon Zolo<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek economist and Prime Minister of Greece (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>enophon_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, English cinematographer and producer (d. 2006)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cinematographer and producer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cinematographer and producer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Belgian-French conductor and director (d. 1967)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Cluytens\" title=\"<PERSON>\"><PERSON></a>, Belgian-French conductor and director (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Cluytens\" title=\"<PERSON>\"><PERSON></a>, Belgian-French conductor and director (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Cluytens"}]}, {"year": "1905", "text": "<PERSON>, Austrian neurologist and psychiatrist (d. 1997)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian neurologist and psychiatrist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian neurologist and psychiatrist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American novelist, short story writer and poet (d. 1991)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer,_born_1905)\" title=\"<PERSON> (writer, born 1905)\"><PERSON></a>, American novelist, short story writer and poet (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer,_born_1905)\" title=\"<PERSON> (writer, born 1905)\"><PERSON></a>, American novelist, short story writer and poet (d. 1991)", "links": [{"title": "<PERSON> (writer, born 1905)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer,_born_1905)"}]}, {"year": "1906", "text": "<PERSON>, Mexican trumpet player and composer (d. 1981)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican trumpet player and composer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican trumpet player and composer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_M%C3%A9ndez"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, American entomologist and museum administrator (d. 1982)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON></a>, American entomologist and museum administrator (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, American entomologist and museum administrator (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, Postmaster General of Canada (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Postmaster_General_of_Canada\" title=\"Postmaster General of Canada\">Postmaster General of Canada</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Postmaster_General_of_Canada\" title=\"Postmaster General of Canada\">Postmaster General of Canada</a> (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Postmaster General of Canada", "link": "https://wikipedia.org/wiki/Postmaster_General_of_Canada"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet and activist (d. 1987)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and activist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and activist (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Austrian-German SS officer (d. 1971)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Australian actor (d. 1971)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rafferty\"><PERSON><PERSON></a>, Australian actor (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rafferty\"><PERSON><PERSON></a>, Australian actor (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>s_Ra<PERSON>ty"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, former President of Argentina (d. 1980)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>s%C3%A9_C%C3%A1mpora\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, former President of Argentina (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>s%C3%A9_C%C3%A1mpora\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, former President of Argentina (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_Jos%C3%A9_C%C3%A1mpora"}]}, {"year": "1910", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan lawyer and politician, 10th Sri Lankan Minister of Justice (d. 2002)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>gam\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Sri_Lanka)\" title=\"Minister of Justice (Sri Lanka)\">Sri Lankan Minister of Justice</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Sri_Lanka)\" title=\"Minister of Justice (Sri Lanka)\">Sri Lankan Minister of Justice</a> (d. 2002)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>anayagam"}, {"title": "Minister of Justice (Sri Lanka)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Sri_Lanka)"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Swedish javelin thrower (d. 2001)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Len<PERSON><PERSON>_Atterwall\" title=\"Lennart Atterwall\"><PERSON><PERSON><PERSON></a>, Swedish javelin thrower (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Len<PERSON><PERSON>_Atterwall\" title=\"Lennart Atterwall\"><PERSON><PERSON><PERSON>wall</a>, Swedish javelin thrower (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nar<PERSON>_Atterwall"}]}, {"year": "1911", "text": "<PERSON><PERSON> <PERSON><PERSON>, English philosopher and academic (d. 1960)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"J. L. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and academic (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"J. L. Austin\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and academic (d. 1960)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, German-English biophysicist, Nobel Prize laureate (d. 2003)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1911", "text": "<PERSON>, American playwright, and poet (d. 1983)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Williams\" title=\"<PERSON> Williams\"><PERSON></a>, American playwright, and poet (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tennessee_Williams\" title=\"<PERSON> Williams\"><PERSON></a>, American playwright, and poet (d. 1983)", "links": [{"title": "<PERSON> Williams", "link": "https://wikipedia.org/wiki/Tennessee_Williams"}]}, {"year": "1913", "text": "<PERSON>, Franco-Greek philologist, author, and scholar (d. 2010)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Franco-Greek philologist, author, and scholar (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Franco-Greek philologist, author, and scholar (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Hungarian-Polish mathematician and academic (d. 1996)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%91s\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Polish mathematician and academic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%91s\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Polish mathematician and academic (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Erd%C5%91s"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Japanese mathematician and academic (d. 1995)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese mathematician and academic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese mathematician and academic (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American general (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William <PERSON>\"><PERSON></a>, American general (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_<PERSON>moreland"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Swedish sprinter (d. 1989)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>berg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish sprinter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Strandberg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish sprinter (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lennart_Strandberg"}]}, {"year": "1915", "text": "<PERSON><PERSON>, North Korean author and poet (d. 2000)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-won\" title=\"<PERSON><PERSON>-won\"><PERSON><PERSON>-<PERSON></a>, North Korean author and poet (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-won\" title=\"<PERSON><PERSON>-won\"><PERSON><PERSON>-<PERSON></a>, North Korean author and poet (d. 2000)", "links": [{"title": "<PERSON><PERSON>-won", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-won"}]}, {"year": "1916", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (d. 1995)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1916", "text": "<PERSON>, English cricketer and footballer (d. 1986)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actor and author (d. 1986)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American R&B singer-songwriter (d. 2001)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, American actor (d. 1980)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Canadian ice hockey player (d. 1965)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Chilean footballer and journalist (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer and journalist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer and journalist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American politician, 44th Governor of Michigan (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Michigan", "link": "https://wikipedia.org/wiki/Governor_of_Michigan"}]}, {"year": "1922", "text": "<PERSON>, Italian-Brazilian physicist and academic (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Brazilian physicist and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Oscar Sal<PERSON>\"><PERSON></a>, Italian-Brazilian physicist and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Italian mathematician and academic (d. 1978)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and academic (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and academic (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guido_Stampacchia"}]}, {"year": "1923", "text": "<PERSON><PERSON>, German general and politician (d. 1992)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German general and politician (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German general and politician (d. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ian"}]}, {"year": "1923", "text": "<PERSON>, American comedian, actor, and screenwriter (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and screenwriter (d. 2016)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani cricketer (d. 1999)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French pianist, composer, and conductor (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and conductor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and conductor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American politician, Governor of New Hampshire (d. 2002)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>esta_Roy\" class=\"mw-redirect\" title=\"Vesta Roy\"><PERSON><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Roy\" class=\"mw-redirect\" title=\"Vesta Roy\"><PERSON><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 2002)", "links": [{"title": "Vesta Roy", "link": "https://wikipedia.org/wiki/<PERSON>esta_Roy"}, {"title": "Governor of New Hampshire", "link": "https://wikipedia.org/wiki/Governor_of_New_Hampshire"}]}, {"year": "1925", "text": "<PERSON>, <PERSON> of Edmonton, English soldier and politician (d. 2020)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Edmonton\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Edmonton\"><PERSON>, Baron <PERSON> of Edmonton</a>, English soldier and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Edmonton\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Edmonton\"><PERSON>, Baron <PERSON> of Edmonton</a>, English soldier and politician (d. 2020)", "links": [{"title": "<PERSON>, <PERSON> of Edmonton", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Edmonton"}]}, {"year": "1925", "text": "<PERSON>, Canadian-American businessman (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American saxophonist and composer (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saxophonist)\" title=\"<PERSON> (saxophonist)\"><PERSON></a>, American saxophonist and composer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(saxophonist)\" title=\"<PERSON> (saxophonist)\"><PERSON></a>, American saxophonist and composer (d. 2010)", "links": [{"title": "<PERSON> (saxophonist)", "link": "https://wikipedia.org/wiki/<PERSON>(saxophonist)"}]}, {"year": "1927", "text": "<PERSON>, English photographer (d. 2022)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, English photographer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, English photographer (d. 2022)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>(photographer)"}]}, {"year": "1929", "text": "<PERSON>, French singer and composer (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, French singer and composer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, French singer and composer (d. 2024)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1929", "text": "<PERSON>, American illustrator and caricaturist", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and caricaturist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and caricaturist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American businessman, co-founded Advanced Micro Devices (d. 2008)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Advanced_Micro_Devices\" class=\"mw-redirect\" title=\"Advanced Micro Devices\">Advanced Micro Devices</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Advanced_Micro_Devices\" class=\"mw-redirect\" title=\"Advanced Micro Devices\">Advanced Micro Devices</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Advanced Micro Devices", "link": "https://wikipedia.org/wiki/Advanced_Micro_Devices"}]}, {"year": "1930", "text": "<PERSON>, American poet (d. 2001)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": " <PERSON>, American lawyer and jurist (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Connor"}]}, {"year": "1931", "text": "<PERSON>, American actor (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American businessman", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American chemist and academic (d. 2000)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Italian director and screenwriter", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Tinto_Brass\" title=\"Tinto Brass\"><PERSON><PERSON></a>, Italian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tinto_Brass\" title=\"Tinto Brass\"><PERSON><PERSON></a>, Italian director and screenwriter", "links": [{"title": "Tinto Brass", "link": "https://wikipedia.org/wiki/Tinto_Brass"}]}, {"year": "1934", "text": "<PERSON>, American actor (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer (d. 2002)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_de_Santa_Rosa\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Rosa\"><PERSON><PERSON><PERSON> Rosa</a>, Brazilian footballer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_de_Santa_Rosa\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Santa Rosa\"><PERSON><PERSON><PERSON> Rosa</a>, Brazilian footballer (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_Rosa"}]}, {"year": "1937", "text": "<PERSON>, American basketball player and manager", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American sprinter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1937", "text": "<PERSON>, Canadian businessman and politician, 26th Premier of Prince Edward Island (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian businessman and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island\" title=\"Premier of Prince Edward Island\">Premier of Prince Edward Island</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian businessman and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island\" title=\"Premier of Prince Edward Island\">Premier of Prince Edward Island</a> (d. 2023)", "links": [{"title": "<PERSON> (Canadian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Canadian_politician)"}, {"title": "Premier of Prince Edward Island", "link": "https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island"}]}, {"year": "1938", "text": "<PERSON>, English painter and illustrator", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English-American physicist and academic, Nobel Prize laureate", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1940", "text": "<PERSON>, American actor and singer (d. 2022)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American lawyer and politician, 52nd Speaker of the United States House of Representatives", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1941", "text": "<PERSON>, Kenyan-English ethologist, biologist, and academic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English ethologist, biologist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English ethologist, biologist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Italian racing driver (d. 1992)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian racing driver (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian racing driver (d. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1942", "text": "<PERSON>, American novelist and poet", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Turkish physician and politician, Turkish Minister of the Interior", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_(Turkey)\" title=\"Ministry of the Interior (Turkey)\">Turkish Minister of the Interior</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_(Turkey)\" title=\"Ministry of the Interior (Turkey)\">Turkish Minister of the Interior</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of the Interior (Turkey)", "link": "https://wikipedia.org/wiki/Ministry_of_the_Interior_(Turkey)"}]}, {"year": "1943", "text": "<PERSON>, American journalist and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter, producer, and actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Mauritian politician, Prime Minister of Mauritius", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9renger\" title=\"<PERSON>\"><PERSON></a>, Mauritian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mauritius\" title=\"Prime Minister of Mauritius\">Prime Minister of Mauritius</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9renger\" title=\"<PERSON>\"><PERSON></a>, Mauritian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mauritius\" title=\"Prime Minister of Mauritius\">Prime Minister of Mauritius</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_B%C3%A9renger"}, {"title": "Prime Minister of Mauritius", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Mauritius"}]}, {"year": "1945", "text": "<PERSON>, Russian gymnast and coach (d. 2004)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast and coach (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast and coach (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actor and singer (d. 2021)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, French politician, French Minister of Finance", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/List_of_Finance_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Finance Ministers of France\">French Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/List_of_Finance_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Finance Ministers of France\">French Minister of Finance</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Finance Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Finance_Ministers_of_France"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Indian-American professor and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American professor and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American professor and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>has<PERSON>_<PERSON>k"}]}, {"year": "1947", "text": "<PERSON>, New Zealand-Australian singer-songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, South Korean violinist and educator", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-wha_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-wha Chung\"><PERSON><PERSON>-wha <PERSON></a>, South Korean violinist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-wha_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-wha Chung\"><PERSON><PERSON>-wha <PERSON></a>, South Korean violinist and educator", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>ha <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-wha_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English pianist and keyboard player (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and keyboard player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and keyboard player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English-Australian singer-songwriter and actor (d. 2016)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Jon_English\" title=\"Jon English\">Jon <PERSON></a>, English-Australian singer-songwriter and actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jon_English\" title=\"Jon English\">Jon <PERSON></a>, English-Australian singer-songwriter and actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jon_English"}]}, {"year": "1949", "text": "<PERSON><PERSON>, South African cricketer and umpire (d. 2022)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer and umpire (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer and umpire (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American actress, comedian, talk show host, and singer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, comedian, talk show host, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, comedian, talk show host, and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American bass player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, German author and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Patrick_S%C3%BCskind\" title=\"<PERSON>\"><PERSON></a>, German author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patrick_S%C3%BCskind\" title=\"<PERSON>\"><PERSON></a>, German author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patrick_S%C3%BCskind"}]}, {"year": "1949", "text": "<PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter (d. 2010)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>grass\"><PERSON></a>, American singer-songwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English cricketer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian-American actor, screenwriter, and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, screenwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Short\"><PERSON></a>, Canadian-American actor, screenwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American composer and conductor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian professional basketball coach and former professional player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/%C5%BDeljko_Pavli%C4%8Devi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian professional basketball coach and former professional player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%BDeljko_Pavli%C4%8Devi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian professional basketball coach and former professional player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%BDeljko_Pavli%C4%8Devi%C4%87"}]}, {"year": "1951", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1952", "text": "<PERSON><PERSON>, French racing driver (d. 1987)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French racing driver (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French racing driver (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1953", "text": "<PERSON>, American academic and politician, 74th Governor of Rhode Island", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Chafee\" title=\"<PERSON> Chafee\"><PERSON></a>, American academic and politician, 74th <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Chafee\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 74th <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a>", "links": [{"title": "Lincoln Chafee", "link": "https://wikipedia.org/wiki/Lincoln_Chafee"}, {"title": "Governor of Rhode Island", "link": "https://wikipedia.org/wiki/Governor_of_Rhode_Island"}]}, {"year": "1953", "text": "<PERSON>, Taiwanese-American banker and politician, 24th United States Secretary of Labor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American banker and politician, 24th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American banker and politician, 24th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Labor", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Labor"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Russian runner", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>vid<PERSON>\" title=\"<PERSON><PERSON><PERSON> Provid<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>vid<PERSON>\" title=\"<PERSON><PERSON><PERSON> Providok<PERSON>a\"><PERSON><PERSON><PERSON></a>, Russian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>a"}]}, {"year": "1954", "text": "<PERSON>, Australian businessman and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American talk show host and activist, founded Guardian Angels", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and activist, founded <a href=\"https://wikipedia.org/wiki/Guardian_Angels\" title=\"Guardian Angels\">Guardian Angels</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and activist, founded <a href=\"https://wikipedia.org/wiki/Guardian_Angels\" title=\"Guardian Angels\">Guardian Angels</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Guardian Angels", "link": "https://wikipedia.org/wiki/Guardian_Angels"}]}, {"year": "1954", "text": "<PERSON>, Australian poet and playwright (d. 2008)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and playwright (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and playwright (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American country music singer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Country_music\" title=\"Country music\">country music</a> singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Country_music\" title=\"Country music\">country music</a> singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Country music", "link": "https://wikipedia.org/wiki/Country_music"}]}, {"year": "1956", "text": "<PERSON>-<PERSON>, South Korean lawyer and politician, 35th Mayor of Seoul (d. 2020)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Won-soon\" title=\"<PERSON> Won-soon\"><PERSON>-<PERSON></a>, South Korean lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Mayor_of_Seoul\" title=\"Mayor of Seoul\">Mayor of Seoul</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-soon\" title=\"<PERSON> Won-soon\"><PERSON>-<PERSON></a>, South Korean lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Mayor_of_Seoul\" title=\"Mayor of Seoul\">Mayor of Seoul</a> (d. 2020)", "links": [{"title": "<PERSON> Won-soon", "link": "https://wikipedia.org/wiki/<PERSON>_Won-soon"}, {"title": "Mayor of Seoul", "link": "https://wikipedia.org/wiki/Mayor_of_Seoul"}]}, {"year": "1957", "text": "<PERSON>, Scottish lawyer and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish lawyer and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American talk show host and television personality", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American talk show host and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American talk show host and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Gibbons"}]}, {"year": "1957", "text": "<PERSON>, English journalist, producer, and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, producer, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, producer, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Iranian visual artist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian visual artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian visual artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Italian racing driver (d. 1986)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian racing driver (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian racing driver (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American football player and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actress and dancer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian-Dutch footballer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English historian and politician, First Secretary of State", "html": "1961 - <a href=\"https://wikipedia.org/wiki/William_Hague\" title=\"William Hague\"><PERSON></a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/First_Secretary_of_State\" title=\"First Secretary of State\">First Secretary of State</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Hague\" title=\"William Hague\"><PERSON></a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/First_Secretary_of_State\" title=\"First Secretary of State\">First Secretary of State</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_Hague"}, {"title": "First Secretary of State", "link": "https://wikipedia.org/wiki/First_Secretary_of_State"}]}, {"year": "1962", "text": "<PERSON>, English pianist, saxophonist, and priest", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, saxophonist, and priest", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, saxophonist, and priest", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American baseball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Russian pilot and cosmonaut", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pilot and cosmonaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pilot and cosmonaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American-Canadian actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Japanese author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian rugby league player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bella\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Irish racing driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Irish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Irish racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1964", "text": "<PERSON>, English businessman and politician, Secretary of State for Culture, Media and Sport", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Culture,_Media_and_Sport\" title=\"Secretary of State for Culture, Media and Sport\">Secretary of State for Culture, Media and Sport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Culture,_Media_and_Sport\" title=\"Secretary of State for Culture, Media and Sport\">Secretary of State for Culture, Media and Sport</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Culture, Media and Sport", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Culture,_Media_and_Sport"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Swedish-American ice hockey player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> A<PERSON>gthoth\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Azagthoth\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Romanian runner", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Violet<PERSON>_<PERSON>zekely"}]}, {"year": "1966", "text": "<PERSON>, American actor and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, French cyclist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American guitarist and songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Italian rugby player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Dutch footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Dutch footballer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Greek footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English-born Irish playwright, screenwriter, and director", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Irish playwright, screenwriter, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Irish playwright, screenwriter, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Scottish politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/1971\" title=\"1971\">1971</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1971\" title=\"1971\">1971</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Scottish politician", "links": [{"title": "1971", "link": "https://wikipedia.org/wiki/1971"}, {"title": "<PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Colombian-American disc jockey, record label owner, and music producer (d. 2020)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian-American disc jockey, record label owner, and music producer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian-American disc jockey, record label owner, and music producer (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Australian tennis player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Stubbs\" title=\"<PERSON><PERSON> Stubbs\"><PERSON><PERSON></a>, Australian tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Stub<PERSON>\" title=\"Rennae Stubbs\"><PERSON><PERSON></a>, Australian tennis player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rennae_Stubbs"}]}, {"year": "1971", "text": "<PERSON>, English footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1971)\" title=\"<PERSON> (footballer, born 1971)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1971)\" title=\"<PERSON> (footballer, born 1971)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1971)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1971)"}]}, {"year": "1972", "text": "<PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American computer scientist and businessman, co-founder of Google", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and businessman, co-founder of <a href=\"https://wikipedia.org/wiki/Google\" title=\"Google\">Google</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and businessman, co-founder of <a href=\"https://wikipedia.org/wiki/Google\" title=\"Google\">Google</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Google", "link": "https://wikipedia.org/wiki/Google"}]}, {"year": "1973", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Knight\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Knight\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Romanian tennis player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sp%C3%AErlea\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sp%C3%AE<PERSON>a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irina_Sp%C3%AErlea"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Lithuanian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress and former model", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and former model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Smart\"><PERSON></a>, American actress and former model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Chilean footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Norwegian sprint kayaker", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Eiri<PERSON>_Ver%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian sprint kayaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ver%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian sprint kayaker", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eirik_Ver%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian wrestler", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Greek basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anastasia_Kostaki"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Nacho_Nov<PERSON>\" title=\"Nacho Novo\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Nacho Novo\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nacho_Novo"}]}, {"year": "1979", "text": "<PERSON>, New Zealand rugby union footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby union footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby union footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Japanese pianist and composer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Cameroonian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wom%C3%A9"}]}, {"year": "1979", "text": "<PERSON>, Brazilian actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American journalist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>young, South Korean singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>-young", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Burmese rapper and politician (d. 2022)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>haw\"><PERSON><PERSON><PERSON></a>, Burmese rapper and politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>haw\"><PERSON><PERSON><PERSON></a>, Burmese rapper and politician (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>haw"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Ethiopian-Israeli footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Baruch_Dego\" title=\"Baruch Dego\"><PERSON><PERSON></a>, Ethiopian-Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baruch_Dego\" title=\"Baruch Dego\"><PERSON><PERSON></a>, Ethiopian-Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Baruch_Dego"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Italian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Massimo_Donati"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball_player)\" class=\"mw-redirect\" title=\"<PERSON> (baseball player)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball_player)\" class=\"mw-redirect\" title=\"<PERSON> (baseball player)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball_player)"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ta\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ta\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>iana_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>iana_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Floriana_Lima"}]}, {"year": "1983", "text": "<PERSON>, Czech footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Roman_Bedn%C3%A1%C5%99\" title=\"Roman Bednář\"><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Bedn%C3%A1%C5%99\" title=\"Roman Bednář\"><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Bedn%C3%A1%C5%99"}]}, {"year": "1983", "text": "<PERSON>, American wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, German skier", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, German footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Zimbabwean cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>dom"}]}, {"year": "1984", "text": "<PERSON>, American model, television host, and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, television host, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, television host, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, English actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American swimmer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor and singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Zimbabwean cricketer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Prosper_<PERSON>\" title=\"Prosper <PERSON>\"><PERSON><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prosper_<PERSON>\" title=\"Prosper <PERSON>\"><PERSON><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "Pro<PERSON>", "link": "https://wikipedia.org/wiki/Prosper_<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t\" title=\"Maxime Biset\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Biset\" title=\"Maxime Biset\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>e_Biset"}]}, {"year": "1986", "text": "<PERSON>, Irish rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Finnish tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, South Korean footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>k"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Scottish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1989", "text": "<PERSON>, Danish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>j%C3%A6r\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A6r\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Kj%C3%A6r"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, South Korean actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>oo-shik\" title=\"<PERSON> Woo-shik\"><PERSON>oo-shik</a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>oo-shik\" title=\"<PERSON> Woo-shik\"><PERSON>oo-shik</a>, South Korean actor", "links": [{"title": "<PERSON>shik", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>oo-shik"}]}, {"year": "1990", "text": "<PERSON>, Filipino actor, model, singer and former kart racer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor, model, singer and former kart racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor, model, singer and former kart racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Cameroonian footballer (d. 2016)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patrick_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Japanese idol, singer, dancer, model and actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese idol, singer, dancer, model and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese idol, singer, dancer, model and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, South Korean singer and actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>umin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer and actor", "links": [{"title": "Xiumin", "link": "https://wikipedia.org/wiki/<PERSON>umin"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American actor and comedian", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Danish model", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Belgian racing driver", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Vandoorne\" title=\"St<PERSON><PERSON> Vandoorne\"><PERSON><PERSON><PERSON></a>, Belgian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Vandoorne\" title=\"<PERSON><PERSON><PERSON> Vandoorne\"><PERSON><PERSON><PERSON></a>, Belgian racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>el_Vando<PERSON>e"}]}, {"year": "1994", "text": "<PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Belgian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American mixed martial artist and model", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Mexican tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Zacar%C3%ADas\" title=\"<PERSON><PERSON> Zacarías\"><PERSON><PERSON></a>, Mexican tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>acar%C3%ADas\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marcela_Zacar%C3%ADas"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Spanish internet personality", "html": "1995 - <a href=\"https://wikipedia.org/wiki/I<PERSON>i_Llanos\" title=\"Ibai Llanos\"><PERSON><PERSON><PERSON></a>, Spanish internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_Llanos\" title=\"Ibai Llanos\"><PERSON><PERSON><PERSON></a>, Spanish internet personality", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>i_<PERSON>lan<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, New Zealand rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Filipino actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Japanese figure skater", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Israeli judoka", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Gefen_Primo\" title=\"Gefen Primo\">Gefen Primo</a>, Israeli judoka", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gefen_Primo\" title=\"Gefen Primo\">Gefen Primo</a>, Israeli judoka", "links": [{"title": "Gefen Primo", "link": "https://wikipedia.org/wiki/Gefen_Primo"}]}, {"year": "2000", "text": "<PERSON>, Russian ice hockey player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American football player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American rapper and social media personality", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bhabie\" title=\"<PERSON><PERSON> Bhabie\"><PERSON><PERSON><PERSON></a>, American rapper and social media personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>habie\" title=\"<PERSON><PERSON> Bhabie\"><PERSON><PERSON> <PERSON><PERSON></a>, American rapper and social media personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ie"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Filipino actor and comedian", "html": "2004 - <a href=\"https://wikipedia.org/wiki/A<PERSON>ra_Briguela\" title=\"A<PERSON>ra Briguela\"><PERSON><PERSON><PERSON></a>, Filipino actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>ra_Brig<PERSON>a\" title=\"<PERSON><PERSON><PERSON> Briguela\"><PERSON><PERSON><PERSON></a>, Filipino actor and comedian", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Awra_<PERSON><PERSON>uela"}]}, {"year": "2005", "text": "<PERSON>, American actress", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "752", "text": "Pope-elect <PERSON>", "html": "752 - <a href=\"https://wikipedia.org/wiki/Pope-elect_<PERSON>\" title=\"Pope-elect <PERSON>\">Pope-elect <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope-elect_<PERSON>\" title=\"Pope-elect <PERSON>\">Pope-elect <PERSON></a>", "links": [{"title": "Pope-elect <PERSON>", "link": "https://wikipedia.org/wiki/Pope-elect_<PERSON>"}]}, {"year": "809", "text": "<PERSON><PERSON><PERSON>, Frisian missionary", "html": "809 - <a href=\"https://wikipedia.org/wiki/Ludger\" title=\"Ludger\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Missionary\" title=\"Missionary\">missionary</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludger\" title=\"Ludger\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Missionary\" title=\"Missionary\">missionary</a>", "links": [{"title": "Ludger", "link": "https://wikipedia.org/wiki/Ludger"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Missionary"}]}, {"year": "903", "text": "<PERSON><PERSON><PERSON>, Japanese poet", "html": "903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_no_<PERSON>chizane\" title=\"<PERSON><PERSON>ra no Michizane\"><PERSON><PERSON><PERSON> no <PERSON>zan<PERSON></a>, Japanese poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_no_<PERSON>chizane\" title=\"Sugawara no Michizane\"><PERSON><PERSON><PERSON> no <PERSON>chizan<PERSON></a>, Japanese poet", "links": [{"title": "<PERSON><PERSON><PERSON> no <PERSON>chizane", "link": "https://wikipedia.org/wiki/Sugawa<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>e"}]}, {"year": "908", "text": "<PERSON>, emperor of the Tang Dynasty (b. 892)", "html": "908 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>\" title=\"Emperor <PERSON> of Tang\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang Dynasty</a> (b. 892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>\" title=\"Emperor <PERSON> of Tang\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang Dynasty</a> (b. 892)", "links": [{"title": "Emperor <PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "922", "text": "<PERSON><PERSON>, Persian mystic and poet (b. 858)", "html": "922 - <a href=\"https://wikipedia.org/wiki/<PERSON>ur_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ur Al<PERSON>aj\"><PERSON><PERSON></a>, Persian mystic and poet (b. 858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ur_<PERSON>\" class=\"mw-redirect\" title=\"Mansur Al-Hallaj\"><PERSON><PERSON></a>, Persian mystic and poet (b. 858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "929", "text": "<PERSON>, Chinese warlord and governor (jiedushi)", "html": "929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Du\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Warlord\" title=\"Warlord\">warlord</a> and governor (<i><a href=\"https://wikipedia.org/wiki/Jiedushi\" title=\"Jiedushi\">jiedushi</a></i>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Du\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Warlord\" title=\"Warlord\">warlord</a> and governor (<i><a href=\"https://wikipedia.org/wiki/Jiedushi\" title=\"Jiedushi\">jiedushi</a></i>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Warlord", "link": "https://wikipedia.org/wiki/Warlord"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>i"}]}, {"year": "973", "text": "<PERSON><PERSON><PERSON> (\"the <PERSON>\"), Frankish nobleman", "html": "973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Rich\" title=\"<PERSON><PERSON><PERSON> the Rich\"><PERSON><PERSON><PERSON></a> (\"the Rich\"), <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Rich\" title=\"<PERSON><PERSON><PERSON> the Rich\"><PERSON><PERSON><PERSON></a> (\"the Rich\"), <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "links": [{"title": "<PERSON><PERSON><PERSON> the Rich", "link": "https://wikipedia.org/wiki/<PERSON>tram_the_Rich"}, {"title": "Nobility", "link": "https://wikipedia.org/wiki/Nobility"}]}, {"year": "983", "text": "<PERSON><PERSON><PERSON>, Iranian ruler (b. 936)", "html": "983 - <a href=\"https://wikipedia.org/wiki/%27Adu<PERSON>_<PERSON>\" title=\"'<PERSON><PERSON>\">'<PERSON><PERSON></a>, Iranian ruler (b. 936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%27A<PERSON><PERSON>_<PERSON>\" title=\"'<PERSON><PERSON>\">'<PERSON><PERSON></a>, Iranian ruler (b. 936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%27Adu<PERSON>_<PERSON>"}]}, {"year": "1091", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Andalusian poet", "html": "1091 - <a href=\"https://wikipedia.org/wiki/Wallada_bint_al-<PERSON>\" title=\"Wallada bint al-Mustakfi\">Wallada bint al-<PERSON></a>, Andalusian poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wallada_bint_al-<PERSON>\" title=\"Wallada bint al-Mustakfi\">Wallada bint al-Must<PERSON></a>, Andalusian poet", "links": [{"title": "<PERSON><PERSON> bin<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_bint_<PERSON>-<PERSON>"}]}, {"year": "1130", "text": "<PERSON><PERSON><PERSON> the Crusader, Norwegian king (b. 1090)", "html": "1130 - <a href=\"https://wikipedia.org/wiki/<PERSON>gu<PERSON>_the_Crusader\" title=\"<PERSON><PERSON><PERSON> the Crusader\"><PERSON><PERSON><PERSON> the Crusader</a>, Norwegian king (b. 1090)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Crusader\" title=\"<PERSON><PERSON><PERSON> the Crusader\"><PERSON><PERSON><PERSON> the Crusader</a>, Norwegian king (b. 1090)", "links": [{"title": "<PERSON><PERSON><PERSON> the Crusader", "link": "https://wikipedia.org/wiki/<PERSON>gu<PERSON>_the_Crusader"}]}, {"year": "1132", "text": "<PERSON> Vendôme, French cardinal and theologian (b. 1065)", "html": "1132 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>end%C3%B4me\" title=\"<PERSON> of Vendôme\"><PERSON> Vendôme</a>, French cardinal and theologian (b. 1065)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>end%C3%B4me\" title=\"<PERSON> of Vendôme\"><PERSON>endôme</a>, French cardinal and theologian (b. 1065)", "links": [{"title": "<PERSON> of Vendôme", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Vend%C3%B4me"}]}, {"year": "1212", "text": "<PERSON><PERSON> of Portugal (b. 1154)", "html": "1212 - <a href=\"https://wikipedia.org/wiki/Sancho_I_of_Portugal\" title=\"Sancho I of Portugal\">Sancho I of Portugal</a> (b. 1154)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sancho_I_of_Portugal\" title=\"Sancho I of Portugal\">Sancho I of Portugal</a> (b. 1154)", "links": [{"title": "Sancho I of Portugal", "link": "https://wikipedia.org/wiki/Sancho_I_of_Portugal"}]}, {"year": "1242", "text": "<PERSON>, 3rd Earl of Albemarle", "html": "1242 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Albemarle\" title=\"<PERSON>, 3rd Earl of Albemarle\"><PERSON>, 3rd Earl of Albemarle</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Albemarle\" title=\"<PERSON>, 3rd Earl of Albemarle\"><PERSON>, 3rd Earl of Albemarle</a>", "links": [{"title": "<PERSON>, 3rd Earl of Albemarle", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Earl_of_Albemarle"}]}, {"year": "1324", "text": "<PERSON>, Queen of France (b. 1304)", "html": "1324 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_France\" class=\"mw-redirect\" title=\"<PERSON>, Queen of France\"><PERSON>, Queen of France</a> (b. 1304)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_France\" class=\"mw-redirect\" title=\"<PERSON>, Queen of France\"><PERSON>, Queen of France</a> (b. 1304)", "links": [{"title": "<PERSON>, Queen of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_France"}]}, {"year": "1326", "text": "<PERSON><PERSON><PERSON>, anatomist (b. c. 1307)", "html": "1326 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, anatomist (b. c. 1307)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, anatomist (b. c. 1307)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1350", "text": "<PERSON> of Castile (b. 1312)", "html": "1350 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XI_of_Castile\" title=\"<PERSON> XI of Castile\"><PERSON> of Castile</a> (b. 1312)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XI_of_Castile\" title=\"<PERSON> XI of Castile\"><PERSON> of Castile</a> (b. 1312)", "links": [{"title": "Alfonso XI of Castile", "link": "https://wikipedia.org/wiki/Alfonso_XI_of_Castile"}]}, {"year": "1402", "text": "<PERSON>, Duke of Rothesay, heir to the throne of Scotland (b. 1378)", "html": "1402 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Rothesay\" title=\"<PERSON>, Duke of Rothesay\"><PERSON>, Duke of Rothesay</a>, heir to the throne of Scotland (b. 1378)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Rothesay\" title=\"<PERSON>, Duke of Rothesay\"><PERSON>, Duke of Rothesay</a>, heir to the throne of Scotland (b. 1378)", "links": [{"title": "<PERSON>, Duke of Rothesay", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Rothesay"}]}, {"year": "1437", "text": "<PERSON>, Earl of Atholl, Scottish nobleman and regicide", "html": "1437 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Atholl\" title=\"<PERSON>, Earl of Atholl\"><PERSON>, Earl of Atholl</a>, Scottish nobleman and regicide", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Atholl\" title=\"<PERSON>, Earl of Atholl\"><PERSON>, Earl of Atholl</a>, Scottish nobleman and regicide", "links": [{"title": "<PERSON>, Earl of Atholl", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_<PERSON>_<PERSON>l"}]}, {"year": "1517", "text": "<PERSON>, Flemish composer (b. 1450)", "html": "1517 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish composer (b. 1450)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish composer (b. 1450)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1535", "text": "<PERSON>, Austrian mathematician, astronomer, and cartographer (b. 1482)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician, astronomer, and cartographer (b. 1482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician, astronomer, and cartographer (b. 1482)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1546", "text": "<PERSON>, English scholar and diplomat (b. 1490)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and diplomat (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and diplomat (b. 1490)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1566", "text": "<PERSON>, Spanish organist and composer (b. 1510)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Spanish organist and composer (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>ón\"><PERSON></a>, Spanish organist and composer (b. 1510)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>z%C3%B3n"}]}, {"year": "1625", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian poet (b. 1569)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet (b. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet (b. 1569)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1649", "text": "<PERSON>, English lawyer and politician, 2nd Governor of the Massachusetts Bay Colony", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony\" class=\"mw-redirect\" title=\"Governor of the Massachusetts Bay Colony\">Governor of the Massachusetts Bay Colony</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony\" class=\"mw-redirect\" title=\"Governor of the Massachusetts Bay Colony\">Governor of the Massachusetts Bay Colony</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of the Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony"}]}, {"year": "1679", "text": "<PERSON>, Swedish historian and author (b. 1621)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish historian and author (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish historian and author (b. 1621)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1697", "text": "<PERSON>, Scottish politician (b. 1640)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1640)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1726", "text": "<PERSON>, English playwright and architect, designed Blenheim Palace and Castle Howard (b. 1664)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and architect, designed <a href=\"https://wikipedia.org/wiki/Blenheim_Palace\" title=\"Blenheim Palace\">Blenheim Palace</a> and <a href=\"https://wikipedia.org/wiki/Castle_Howard\" title=\"Castle Howard\">Castle Howard</a> (b. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and architect, designed <a href=\"https://wikipedia.org/wiki/Blenheim_Palace\" title=\"Blenheim Palace\">Blenheim Palace</a> and <a href=\"https://wikipedia.org/wiki/Castle_Howard\" title=\"Castle Howard\">Castle Howard</a> (b. 1664)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Blenheim Palace", "link": "https://wikipedia.org/wiki/Blenheim_Palace"}, {"title": "Castle Howard", "link": "https://wikipedia.org/wiki/Castle_Howard"}]}, {"year": "1772", "text": "<PERSON>, French author and politician (b. 1704)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and politician (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and politician (b. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "<PERSON>, American politician, 31st and 33rd Governor of the Colony of Rhode Island and Providence Plantations (b. 1725)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Rhode_Island_politician)\" title=\"<PERSON> (Rhode Island politician)\"><PERSON></a>, American politician, 31st and 33rd <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Rhode_Island\" title=\"List of colonial governors of Rhode Island\">Governor of the Colony of Rhode Island and Providence Plantations</a> (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Rhode_Island_politician)\" title=\"<PERSON> (Rhode Island politician)\"><PERSON></a>, American politician, 31st and 33rd <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Rhode_Island\" title=\"List of colonial governors of Rhode Island\">Governor of the Colony of Rhode Island and Providence Plantations</a> (b. 1725)", "links": [{"title": "<PERSON> (Rhode Island politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Rhode_Island_politician)"}, {"title": "List of colonial governors of Rhode Island", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Rhode_Island"}]}, {"year": "1780", "text": "<PERSON>, Duke of Brunswick-<PERSON><PERSON> (b. 1713)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Wolfenb%C3%BCttel\" title=\"<PERSON>, Duke of Brunswick-Wolfenbüttel\"><PERSON>, Duke of Brunswick-Wolfenbüttel</a> (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Wolfenb%C3%BCttel\" title=\"<PERSON>, Duke of Brunswick-Wolfenbüttel\"><PERSON>, Duke of Brunswick-Wolfenbüttel</a> (b. 1713)", "links": [{"title": "<PERSON>, Duke of Brunswick-Wolfenbüttel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Wolfenb%C3%BCttel"}]}, {"year": "1793", "text": "<PERSON>, English physician and engineer (b. 1721)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and engineer (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and engineer (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, Scottish geologist and physician (b. 1726)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish geologist and physician (b. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish geologist and physician (b. 1726)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French physician and politician (b. 1738)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French physician and politician (b. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French physician and politician (b. 1738)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, German pianist and composer (b. 1770)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, American lieutenant, engineer, and politician, 3rd United States Assistant Secretary of State (b. 1811)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, engineer, and politician, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, engineer, and politician, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Assistant Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State"}]}, {"year": "1862", "text": "<PERSON><PERSON><PERSON>, American commander (b. 1792)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American commander (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American commander (b. 1792)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Polish general and activist (b. 1800)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Roman_Sang<PERSON>ko\" title=\"Roman Sang<PERSON>ko\"><PERSON></a>, Polish general and activist (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Sangus<PERSON>ko\" title=\"Roman Sang<PERSON>ko\"><PERSON></a>, Polish general and activist (b. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, 8th Wisconsin Volunteer Infantry Regiment Masco<PERSON> (b. 1861)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Old_Abe\" title=\"Old Abe\">Old Abe</a>, <a href=\"https://wikipedia.org/wiki/8th_Wisconsin_Volunteer_Infantry_Regiment\" class=\"mw-redirect\" title=\"8th Wisconsin Volunteer Infantry Regiment\">8th Wisconsin Volunteer Infantry Regiment</a> <PERSON><PERSON><PERSON> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Old_Abe\" title=\"Old Abe\">Old Abe</a>, <a href=\"https://wikipedia.org/wiki/8th_Wisconsin_Volunteer_Infantry_Regiment\" class=\"mw-redirect\" title=\"8th Wisconsin Volunteer Infantry Regiment\">8th Wisconsin Volunteer Infantry Regiment</a> <PERSON><PERSON><PERSON> (b. 1861)", "links": [{"title": "Old Abe", "link": "https://wikipedia.org/wiki/Old_Abe"}, {"title": "8th Wisconsin Volunteer Infantry Regiment", "link": "https://wikipedia.org/wiki/8th_Wisconsin_Volunteer_Infantry_Regiment"}]}, {"year": "1885", "text": "<PERSON><PERSON>, American general and businessman, co-founded Western Union (b. 1825)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Stager\" title=\"<PERSON>son Stager\"><PERSON><PERSON></a>, American general and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Western_Union\" title=\"Western Union\">Western Union</a> (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Stager\" title=\"<PERSON>son Stager\"><PERSON><PERSON></a>, American general and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Western_Union\" title=\"Western Union\">Western Union</a> (b. 1825)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r"}, {"title": "Western Union", "link": "https://wikipedia.org/wiki/Western_Union"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON> <PERSON> of Zanzibar (b. 1837)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_of_Zanzibar\" title=\"<PERSON><PERSON><PERSON> <PERSON> of Zanzibar\"><PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_of_Zanzibar\" title=\"<PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar\"><PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar</a> (b. 1837)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON> of Zanzibar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_of_Zanzibar"}]}, {"year": "1892", "text": "<PERSON>, American poet, essayist, and journalist (b. 1819)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, essayist, and journalist (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, essayist, and journalist (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English-South African colonialist, businessman and politician, 6th Prime Minister of the Cape Colony (b. 1853)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-South African colonialist, businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Cape_Colony\" class=\"mw-redirect\" title=\"Prime Minister of the Cape Colony\">Prime Minister of the Cape Colony</a> (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-South African colonialist, businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Cape_Colony\" class=\"mw-redirect\" title=\"Prime Minister of the Cape Colony\">Prime Minister of the Cape Colony</a> (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Cape Colony", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Cape_Colony"}]}, {"year": "1905", "text": "<PERSON>, American actor (b. 1849)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, French astronomer (b. 1864)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American surgeon and lexicographer (b. 1834)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and lexicographer (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>_Minor\" title=\"<PERSON>\"><PERSON></a>, American surgeon and lexicographer (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, French actress and screenwriter (b. 1844)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and screenwriter (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and screenwriter (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "Constantin <PERSON>, German lawyer and politician, Chancellor of Germany (b. 1852)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Constant<PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>stantin <PERSON>\">Con<PERSON><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (b. 1852)", "links": [{"title": "Constantin <PERSON>", "link": "https://wikipedia.org/wiki/Constantin_<PERSON>"}, {"title": "Chancellor of Germany (German Reich)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)"}]}, {"year": "1932", "text": "<PERSON>, American machinist, inventor, engineer, automotive entrepreneur and founder of Cadillac and Lincoln (b. 1843)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American machinist, inventor, engineer, automotive entrepreneur and founder of <a href=\"https://wikipedia.org/wiki/Cadillac\" title=\"Cadillac\">Cadillac</a> and <a href=\"https://wikipedia.org/wiki/Lincoln_(automobile)\" class=\"mw-redirect\" title=\"Lincoln (automobile)\">Lincoln</a> (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American machinist, inventor, engineer, automotive entrepreneur and founder of <a href=\"https://wikipedia.org/wiki/Cadillac\" title=\"Cadillac\">Cadillac</a> and <a href=\"https://wikipedia.org/wiki/Lincoln_(automobile)\" class=\"mw-redirect\" title=\"Lincoln (automobile)\">Lincoln</a> (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cadillac", "link": "https://wikipedia.org/wiki/Cadillac"}, {"title": "Lincoln (automobile)", "link": "https://wikipedia.org/wiki/<PERSON>_(automobile)"}]}, {"year": "1934", "text": "<PERSON>, American jumper and discus thrower (b. 1877)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jumper and discus thrower (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jumper and discus thrower (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, German-Estonian astrophysicist (b. 1880)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Estonian astrophysicist (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Estonian astrophysicist (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Greek runner (b. 1873)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek runner (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek runner (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rid<PERSON>_Louis"}]}, {"year": "1942", "text": "<PERSON>, American baseball player and manager (b. 1874)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (b. 1874)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1942", "text": "<PERSON>, American novelist and poet (b. 1862)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English-Welsh lawyer and politician, Prime Minister of the United Kingdom (b. 1863)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1951", "text": "<PERSON>, American banker and politician, 6th Governor of New Mexico (b. 1864)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico\" title=\"Governor of New Mexico\">Governor of New Mexico</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico\" title=\"Governor of New Mexico\">Governor of New Mexico</a> (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of New Mexico", "link": "https://wikipedia.org/wiki/Governor_of_New_Mexico"}]}, {"year": "1954", "text": "<PERSON>, French rower (b. 1875)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rower (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rower (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, French politician, Prime Minister of France (b. 1872)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1872)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1957", "text": "<PERSON>, German-American director and screenwriter (b. 1902)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Max_Oph%C3%BCls\" title=\"<PERSON>\"><PERSON></a>, German-American director and screenwriter (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Oph%C3%BCls\" title=\"<PERSON>\"><PERSON></a>, German-American director and screenwriter (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Max_Oph%C3%BCls"}]}, {"year": "1958", "text": "<PERSON>, English cricketer and footballer (b. 1887)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Mead"}]}, {"year": "1959", "text": "<PERSON>, American crime novelist and screenwriter (b. 1888)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American crime novelist and screenwriter (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American crime novelist and screenwriter (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, German peace activist who self-immolated in protest of U.S. imperialism (b. 1882) ", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German peace activist who <a href=\"https://wikipedia.org/wiki/Self-immolation\" title=\"Self-immolation\">self-immolated</a> in protest of <a href=\"https://wikipedia.org/wiki/U.S._imperialism\" title=\"U.S. imperialism\">U.S. imperialism</a> (b. 1882) [<a rel=\"nofollow\" class=\"external text\" href=\"https://wikipedia.orghttps://www.newspapers.com/article/detroit-free-press-alice-herz-death-noti/104368395/\">1</a>]", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German peace activist who <a href=\"https://wikipedia.org/wiki/Self-immolation\" title=\"Self-immolation\">self-immolated</a> in protest of <a href=\"https://wikipedia.org/wiki/U.S._imperialism\" title=\"U.S. imperialism\">U.S. imperialism</a> (b. 1882) [<a rel=\"nofollow\" class=\"external text\" href=\"https://wikipedia.orghttps://www.newspapers.com/article/detroit-free-press-alice-herz-death-noti/104368395/\">1</a>]", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}, {"title": "Self-immolation", "link": "https://wikipedia.org/wiki/Self-immolation"}, {"title": "U.S. imperialism", "link": "https://wikipedia.org/wiki/U.S._imperialism"}]}, {"year": "1966", "text": "<PERSON>, French swimmer (b. 1883)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French swimmer (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French swimmer (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victor_Hochepied"}]}, {"year": "1966", "text": "<PERSON>, American novelist and screenwriter (b. 1900)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American novelist (b. 1937)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>,  English playwright, actor, and composer  (b. 1899)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/No%C3%ABl_Coward\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English playwright, actor, and composer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/No%C3%ABl_Coward\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English playwright, actor, and composer (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/No%C3%ABl_Coward"}]}, {"year": "1973", "text": "<PERSON>, American football player (b. 1916)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American-French painter (b. 1901)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-French painter (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-French painter (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American author and academic (b. 1915)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, French linguist and critic (b. 1915)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French linguist and critic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French linguist and critic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English historian and spy (b. 1907)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and spy (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and spy (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Guinean politician, 1st President of Guinea (b. 1922)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9kou_Tour%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Guinean politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Guinea\" class=\"mw-redirect\" title=\"President of Guinea\">President of Guinea</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9kou_Tour%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Guinean politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Guinea\" class=\"mw-redirect\" title=\"President of Guinea\">President of Guinea</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ahmed_S%C3%A9kou_Tour%C3%A9"}, {"title": "President of Guinea", "link": "https://wikipedia.org/wiki/President_of_Guinea"}]}, {"year": "1987", "text": "<PERSON><PERSON>, German conductor (b. 1902)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German conductor (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German conductor (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor (b. 1898)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American fashion designer (b. 1932)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American fashion designer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American fashion designer (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American-Canadian journalist and radio host (b. 1937)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist and radio host (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist and radio host (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American dancer and choreographer (b. 1942)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Falco"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American rapper and producer (b. 1964)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American rapper and producer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American rapper and producer (b. 1964)", "links": [{"title": "Eazy-E", "link": "https://wikipedia.org/wiki/Eazy-E"}]}, {"year": "1996", "text": "<PERSON>, American lieutenant, lawyer, and politician, 58th United States Secretary of State (b. 1914)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician, 58th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician, 58th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1996", "text": "<PERSON>, American engineer and businessman, co-founded Hewlett-Packard (b. 1912)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" title=\"He<PERSON>ett-Packard\"><PERSON><PERSON><PERSON>-<PERSON></a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-Packard\" title=\"He<PERSON><PERSON>-Packard\">Hewlett-Packard</a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Hewlett-Packard", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ett-<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English journalist (b. 1904)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English physician and author (b. 1920)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alex_Comfort"}]}, {"year": "2002", "text": "<PERSON>, American drummer and songwriter (b. 1950)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American sociologist and politician, 12th United States Ambassador to the United Nations (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations"}]}, {"year": "2004", "text": "<PERSON>, American actress (b. 1921)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English lieutenant and politician, Prime Minister of the United Kingdom (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "2005", "text": "<PERSON>, Nigerian lawyer and politician (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, Nigerian lawyer and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian lawyer and politician (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Indian journalist and politician (b. 1944)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Indian journalist and politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Indian journalist and politician (b. 1944)", "links": [{"title": "<PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(politician)"}]}, {"year": "2006", "text": "<PERSON>, American racing driver (b. 1975)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English singer-songwriter and guitarist (b. 1956)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American poet and academic (b. 1933)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Colombian rebel leader (b. 1930)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian rebel leader (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian rebel leader (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Canadian skier and BASE jumper (b. 1969)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier and <a href=\"https://wikipedia.org/wiki/BASE_jumping\" title=\"BASE jumping\">BASE</a> jumper (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier and <a href=\"https://wikipedia.org/wiki/BASE_jumping\" title=\"BASE jumping\">BASE</a> jumper (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "BASE jumping", "link": "https://wikipedia.org/wiki/BASE_jumping"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Norwegian singer and composer (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer and composer (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American art collector and curator (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector and curator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector and curator (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English-Canadian actor, producer, and screenwriter (b. 1946)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor, producer, and screenwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor, producer, and screenwriter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American lawyer and politician (b. 1935)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English author (b. 1934)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American football player (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Si<PERSON>_<PERSON>\" title=\"Sisto Averno\"><PERSON><PERSON></a>, American football player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Averno\"><PERSON><PERSON></a>, American football player (b. 1925)", "links": [{"title": "Sisto Averno", "link": "https://wikipedia.org/wiki/Sisto_Averno"}]}, {"year": "2012", "text": "<PERSON>, Irish carpenter and politician (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish carpenter and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish carpenter and politician (b. 1932)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "2012", "text": "<PERSON>, American theorist and academic (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and academic (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American organist and educator (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, American organist and educator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, American organist and educator (b. 1924)", "links": [{"title": "<PERSON> (organist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(organist)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian poet and educator (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and educator (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and educator (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Swedish theologian and academic (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish theologian and academic (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish theologian and academic (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American basketball player and sportscaster (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish journalist and politician, Polish Minister of Interior (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>%C5%82owski\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_and_Administration_(Poland)\" class=\"mw-redirect\" title=\"Ministry of Interior and Administration (Poland)\">Polish Minister of Interior</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>%C5%82owski\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_and_Administration_(Poland)\" class=\"mw-redirect\" title=\"Ministry of Interior and Administration (Poland)\">Polish Minister of Interior</a> (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ztof_Koz%C5%82owski"}, {"title": "Ministry of Interior and Administration (Poland)", "link": "https://wikipedia.org/wiki/Ministry_of_Interior_and_Administration_(Poland)"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American screenwriter and producer (b. 1964)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer (b. 1964)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2014", "text": "<PERSON>, American psychologist and author (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American businessman and politician (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, <PERSON>, English politician (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, English politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English politician (b. 1928)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON>, Iraqi patriarch (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Dinkha_IV\" title=\"Dinkha IV\"><PERSON><PERSON> <PERSON></a>, Iraqi patriarch (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>kha_IV\" title=\"Dinkha IV\"><PERSON><PERSON> <PERSON></a>, Iraqi patriarch (b. 1935)", "links": [{"title": "Dinkha IV", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_IV"}]}, {"year": "2015", "text": "<PERSON>, German mathematician, computer scientist, and academic (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, computer scientist, and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, computer scientist, and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Swedish poet, translator, and  psychologist  Nobel Prize laureate (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>tr%C3%B6mer\" title=\"<PERSON>\"><PERSON></a>, Swedish poet, translator, and psychologist <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6mer\" title=\"<PERSON>\"><PERSON></a>, Swedish poet, translator, and psychologist <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tomas_Transtr%C3%B6mer"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2016", "text": "<PERSON>, American novelist, essayist, and poet (b. 1937)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian television presenter (b. 1958)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian television presenter (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian television presenter (b. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Argentine writer and translator (b. 1937)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine writer and translator (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine writer and translator (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%AD<PERSON>_Kodama"}]}, {"year": "2023", "text": "<PERSON>, Indian actor and politician (b. 1948)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Indian actor and politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Indian actor and politician (b. 1948)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "2023", "text": "<PERSON>, Israeli electrical engineer, developed the LZ family of compression algorithms (b. 1931)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli electrical engineer, developed the <a href=\"https://wikipedia.org/wiki/LZ77_and_LZ78\" title=\"LZ77 and LZ78\">LZ family</a> of compression algorithms (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli electrical engineer, developed the <a href=\"https://wikipedia.org/wiki/LZ77_and_LZ78\" title=\"LZ77 and LZ78\">LZ family</a> of compression algorithms (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "LZ77 and LZ78", "link": "https://wikipedia.org/wiki/LZ77_and_LZ78"}]}, {"year": "2024", "text": "<PERSON>, American diplomat, UNESCO goodwill ambassador (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat, <a href=\"https://wikipedia.org/wiki/UNESCO_Goodwill_Ambassador\" title=\"UNESCO Goodwill Ambassador\">UNESCO goodwill ambassador</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat, <a href=\"https://wikipedia.org/wiki/UNESCO_Goodwill_Ambassador\" title=\"UNESCO Goodwill Ambassador\">UNESCO goodwill ambassador</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "UNESCO Goodwill Ambassador", "link": "https://wikipedia.org/wiki/UNESCO_Goodwill_Ambassador"}]}]}}