{"date": "March 5", "url": "https://wikipedia.org/wiki/March_5", "data": {"Events": [{"year": "363", "text": "Roman emperor <PERSON> leaves Antioch with an army of 90,000 to attack the Sasanian Empire, in a campaign which would bring about his own death.", "html": "363 - <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a> with an army of 90,000 to <a href=\"https://wikipedia.org/wiki/Julian%27s_Persian_War\" class=\"mw-redirect\" title=\"<PERSON>'s Persian War\">attack</a> the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian Empire</a>, in a campaign which would bring about his own death.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a> with an army of 90,000 to <a href=\"https://wikipedia.org/wiki/Julian%27s_Persian_War\" class=\"mw-redirect\" title=\"<PERSON>'s Persian War\">attack</a> the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian Empire</a>, in a campaign which would bring about his own death.", "links": [{"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(emperor)"}, {"title": "Antioch", "link": "https://wikipedia.org/wiki/Antioch"}, {"title": "<PERSON>'s Persian War", "link": "https://wikipedia.org/wiki/Julian%27s_Persian_War"}, {"title": "Sasanian Empire", "link": "https://wikipedia.org/wiki/Sasanian_Empire"}]}, {"year": "1046", "text": "<PERSON><PERSON> begins the seven-year Middle Eastern journey which he will later describe in his book Safarnam<PERSON>.", "html": "1046 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> begins the seven-year Middle Eastern journey which he will later describe in his book <i><a href=\"https://wikipedia.org/wiki/Safarnama\" title=\"Safarnama\">Safarnama</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> begins the seven-year Middle Eastern journey which he will later describe in his book <i><a href=\"https://wikipedia.org/wiki/Safarnama\" title=\"Safarnama\">Safarnama</a></i>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Safarnama"}]}, {"year": "1279", "text": "The Livonian Order is defeated in the Battle of Aizkraukle by the Grand Duchy of Lithuania.", "html": "1279 - The <a href=\"https://wikipedia.org/wiki/Livonian_Order\" title=\"Livonian Order\">Livonian Order</a> is defeated in the <a href=\"https://wikipedia.org/wiki/Battle_of_Aizkraukle\" title=\"Battle of Aizkraukle\">Battle of Aizkraukle</a> by the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Livonian_Order\" title=\"Livonian Order\">Livonian Order</a> is defeated in the <a href=\"https://wikipedia.org/wiki/Battle_of_Aizkraukle\" title=\"Battle of Aizkraukle\">Battle of Aizkraukle</a> by the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a>.", "links": [{"title": "Livonian Order", "link": "https://wikipedia.org/wiki/Livonian_Order"}, {"title": "Battle of Aizkraukle", "link": "https://wikipedia.org/wiki/Battle_of_Aiz<PERSON>ukle"}, {"title": "Grand Duchy of Lithuania", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania"}]}, {"year": "1496", "text": "King <PERSON> of England issues letters patent to <PERSON> and his sons, authorising them to explore unknown lands.", "html": "1496 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"Henry VII of England\"><PERSON> of England</a> issues letters patent to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his sons, authorising them to explore unknown lands.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Henry VII of England\"><PERSON> of England</a> issues letters patent to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his sons, authorising them to explore unknown lands.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1616", "text": "<PERSON><PERSON>'s book On the Revolutions of the Heavenly Spheres is added to the Index of Forbidden Books 73 years after it was first published.", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Copernicus\" title=\"<PERSON>us Copernicus\"><PERSON><PERSON></a>'s book <i><a href=\"https://wikipedia.org/wiki/De_revolutionibus_orbium_coelestium\" title=\"De revolutionibus orbium coelestium\">On the Revolutions of the Heavenly Spheres</a></i> is added to the <a href=\"https://wikipedia.org/wiki/Index_Librorum_Prohibitorum\" title=\"Index Librorum Prohibitorum\">Index of Forbidden Books</a> 73 years after it was first published.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>us Copernicus\"><PERSON><PERSON></a>'s book <i><a href=\"https://wikipedia.org/wiki/De_revolutionibus_orbium_coelestium\" title=\"De revolutionibus orbium coelestium\">On the Revolutions of the Heavenly Spheres</a></i> is added to the <a href=\"https://wikipedia.org/wiki/Index_Librorum_Prohibitorum\" title=\"Index Librorum Prohibitorum\">Index of Forbidden Books</a> 73 years after it was first published.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Copernicus"}, {"title": "De revolutionibus orbium coelestium", "link": "https://wikipedia.org/wiki/De_revolutionibus_orbium_coelestium"}, {"title": "Index Librorum Prohibitorum", "link": "https://wikipedia.org/wiki/Index_Librorum_Prohibitorum"}]}, {"year": "1766", "text": "<PERSON>, the first Spanish governor of Louisiana, arrives in New Orleans.", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first Spanish governor of <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>, arrives in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first Spanish governor of <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>, arrives in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Louisiana", "link": "https://wikipedia.org/wiki/Louisiana"}, {"title": "New Orleans", "link": "https://wikipedia.org/wiki/New_Orleans"}]}, {"year": "1770", "text": "Boston Massacre: Five Americans, including <PERSON><PERSON><PERSON>, are fatally shot by British troops in an event that would contribute to the outbreak of the American Revolutionary War (also known as the American War of Independence) five years later.", "html": "1770 - <a href=\"https://wikipedia.org/wiki/Boston_Massacre\" title=\"Boston Massacre\">Boston Massacre</a>: Five Americans, including <a href=\"https://wikipedia.org/wiki/Crispus_Attucks\" title=\"Crispus Attucks\">Crispus Attucks</a>, are fatally shot by British troops in an event that would contribute to the outbreak of the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a> (also known as the American War of Independence) five years later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boston_Massacre\" title=\"Boston Massacre\">Boston Massacre</a>: Five Americans, including <a href=\"https://wikipedia.org/wiki/Crispus_Attucks\" title=\"Crispus Attucks\">Crispus Attucks</a>, are fatally shot by British troops in an event that would contribute to the outbreak of the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a> (also known as the American War of Independence) five years later.", "links": [{"title": "Boston Massacre", "link": "https://wikipedia.org/wiki/Boston_Massacre"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Crispus_Attucks"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}]}, {"year": "1811", "text": "Peninsular War: A French force under the command of Marshal <PERSON> is routed while trying to prevent an Anglo-Spanish-Portuguese army from lifting the Siege of Cádiz in the Battle of Barrosa.", "html": "1811 - <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: A French force under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON>\">Marshal <PERSON></a> is routed while trying to prevent an Anglo-Spanish-Portuguese army from lifting the <a href=\"https://wikipedia.org/wiki/Siege_of_C%C3%A1diz\" title=\"Siege of Cádiz\">Siege of Cádiz</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Barrosa\" title=\"Battle of Barrosa\">Battle of Barrosa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: A French force under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON>\">Marshal <PERSON></a> is routed while trying to prevent an Anglo-Spanish-Portuguese army from lifting the <a href=\"https://wikipedia.org/wiki/Siege_of_C%C3%A1diz\" title=\"Siege of Cádiz\">Siege of Cádiz</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Barrosa\" title=\"Battle of Barrosa\">Battle of Barrosa</a>.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "<PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Siege of Cádiz", "link": "https://wikipedia.org/wiki/Siege_of_C%C3%A1diz"}, {"title": "Battle of Barrosa", "link": "https://wikipedia.org/wiki/Battle_of_Barrosa"}]}, {"year": "1824", "text": "First Anglo-Burmese War: The British officially declare war on Burma.", "html": "1824 - <a href=\"https://wikipedia.org/wiki/First_Anglo-Burmese_War\" title=\"First Anglo-Burmese War\">First Anglo-Burmese War</a>: The British officially declare war on <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Anglo-Burmese_War\" title=\"First Anglo-Burmese War\">First Anglo-Burmese War</a>: The British officially declare war on <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a>.", "links": [{"title": "First Anglo-Burmese War", "link": "https://wikipedia.org/wiki/First_Anglo-Burmese_War"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}, {"year": "1825", "text": "<PERSON>, one of the last successful Caribbean pirates, is defeated in combat and captured by authorities.", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AD\" title=\"<PERSON>\"><PERSON></a>, one of the last successful Caribbean <a href=\"https://wikipedia.org/wiki/Pirate\" class=\"mw-redirect\" title=\"Pirate\">pirates</a>, is <a href=\"https://wikipedia.org/wiki/Capture_of_the_sloop_Anne\" title=\"Capture of the sloop Anne\">defeated in combat</a> and captured by authorities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AD\" title=\"<PERSON>\"><PERSON></a>, one of the last successful Caribbean <a href=\"https://wikipedia.org/wiki/Pirate\" class=\"mw-redirect\" title=\"Pirate\">pirates</a>, is <a href=\"https://wikipedia.org/wiki/Capture_of_the_sloop_Anne\" title=\"Capture of the sloop Anne\">defeated in combat</a> and captured by authorities.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roberto_Cofres%C3%AD"}, {"title": "Pirate", "link": "https://wikipedia.org/wiki/Pirate"}, {"title": "Capture of the sloop Anne", "link": "https://wikipedia.org/wiki/Capture_of_the_sloop_<PERSON>"}]}, {"year": "1836", "text": "<PERSON> established his first factory to produce recently patented production-model revolver, the .34-caliber \"Paterson\".", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Colt\" title=\"Samuel Colt\"><PERSON></a> established his first factory to produce recently patented production-model <a href=\"https://wikipedia.org/wiki/Revolver\" title=\"Revolver\">revolver</a>, the .34-caliber \"Paterson\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Colt\" title=\"Samuel Colt\"><PERSON> Colt</a> established his first factory to produce recently patented production-model <a href=\"https://wikipedia.org/wiki/Revolver\" title=\"Revolver\">revolver</a>, the .34-caliber \"Paterson\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Revolver", "link": "https://wikipedia.org/wiki/Revolver"}]}, {"year": "1850", "text": "The Britannia Bridge across the Menai Strait between the island of Anglesey and the mainland of Wales is opened.", "html": "1850 - The <a href=\"https://wikipedia.org/wiki/Britannia_Bridge\" title=\"Britannia Bridge\">Britannia Bridge</a> across the <a href=\"https://wikipedia.org/wiki/Menai_Strait\" title=\"Menai Strait\">Menai Strait</a> between the island of <a href=\"https://wikipedia.org/wiki/Anglesey\" title=\"Anglesey\">Anglesey</a> and the mainland of Wales is opened.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Britannia_Bridge\" title=\"Britannia Bridge\">Britannia Bridge</a> across the <a href=\"https://wikipedia.org/wiki/Menai_Strait\" title=\"Menai Strait\">Menai Strait</a> between the island of <a href=\"https://wikipedia.org/wiki/Anglesey\" title=\"Anglesey\">Anglesey</a> and the mainland of Wales is opened.", "links": [{"title": "Britannia Bridge", "link": "https://wikipedia.org/wiki/Britannia_Bridge"}, {"title": "Menai Strait", "link": "https://wikipedia.org/wiki/Menai_Strait"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ey"}]}, {"year": "1860", "text": "Parma, Tuscany, Modena and Romagna vote in referendums to join the Kingdom of Sardinia.", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Parma\" title=\"Parma\">Parma</a>, <a href=\"https://wikipedia.org/wiki/Tuscany\" title=\"Tuscany\">Tuscany</a>, <a href=\"https://wikipedia.org/wiki/Modena\" title=\"Modena\">Modena</a> and <a href=\"https://wikipedia.org/wiki/Romagna\" title=\"Romagna\">Romagna</a> vote in <a href=\"https://wikipedia.org/wiki/Referendum\" title=\"Referendum\">referendums</a> to join the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sardinia_(1720%E2%80%931861)\" title=\"Kingdom of Sardinia (1720-1861)\">Kingdom of Sardinia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Parma\" title=\"Parma\">Parma</a>, <a href=\"https://wikipedia.org/wiki/Tuscany\" title=\"Tuscany\">Tuscany</a>, <a href=\"https://wikipedia.org/wiki/Modena\" title=\"Modena\">Modena</a> and <a href=\"https://wikipedia.org/wiki/Romagna\" title=\"Romagna\">Romagna</a> vote in <a href=\"https://wikipedia.org/wiki/Referendum\" title=\"Referendum\">referendums</a> to join the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sardinia_(1720%E2%80%931861)\" title=\"Kingdom of Sardinia (1720-1861)\">Kingdom of Sardinia</a>.", "links": [{"title": "Parma", "link": "https://wikipedia.org/wiki/Parma"}, {"title": "Tuscany", "link": "https://wikipedia.org/wiki/Tuscany"}, {"title": "Modena", "link": "https://wikipedia.org/wiki/Modena"}, {"title": "Romagna", "link": "https://wikipedia.org/wiki/Romagna"}, {"title": "Referendum", "link": "https://wikipedia.org/wiki/Referendum"}, {"title": "Kingdom of Sardinia (1720-1861)", "link": "https://wikipedia.org/wiki/Kingdom_of_Sardinia_(1720%E2%80%931861)"}]}, {"year": "1868", "text": "Mefisto<PERSON><PERSON>, an opera by <PERSON><PERSON><PERSON>, receives its premiere performance at La Scala.", "html": "1868 - <i><a href=\"https://wikipedia.org/wiki/Mefisto<PERSON>le\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\">Mefi<PERSON>fe<PERSON></a></i>, an opera by <a href=\"https://wikipedia.org/wiki/Arrigo_<PERSON>\" title=\"A<PERSON><PERSON>\">A<PERSON><PERSON></a>, receives its premiere performance at <a href=\"https://wikipedia.org/wiki/La_Scala\" title=\"La Scala\">La Scala</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Me<PERSON>sto<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\">Me<PERSON><PERSON><PERSON><PERSON></a></i>, an opera by <a href=\"https://wikipedia.org/wiki/Arrigo_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">A<PERSON><PERSON></a>, receives its premiere performance at <a href=\"https://wikipedia.org/wiki/La_Scala\" title=\"La Scala\">La Scala</a>.", "links": [{"title": "Mefistofele", "link": "https://wikipedia.org/wiki/Mefistofele"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arrigo_<PERSON>ito"}, {"title": "La Scala", "link": "https://wikipedia.org/wiki/La_Scala"}]}, {"year": "1872", "text": "<PERSON> patents the air brake.", "html": "1872 - <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"<PERSON>\"><PERSON></a> patents the <a href=\"https://wikipedia.org/wiki/Railway_air_brake\" title=\"Railway air brake\">air brake</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"George <PERSON>house\"><PERSON></a> patents the <a href=\"https://wikipedia.org/wiki/Railway_air_brake\" title=\"Railway air brake\">air brake</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}, {"title": "Railway air brake", "link": "https://wikipedia.org/wiki/Railway_air_brake"}]}, {"year": "1906", "text": "Moro Rebellion: United States Army troops bring overwhelming force against the native Moros in the First Battle of Bud Dajo, leaving only six survivors.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Moro_Rebellion\" title=\"Moro Rebellion\">Moro Rebellion</a>: <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> troops bring overwhelming force against the native <a href=\"https://wikipedia.org/wiki/Moro_people\" title=\"Moro people\"><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Bud_Dajo\" title=\"First Battle of Bud Dajo\">First Battle of Bud Dajo</a>, leaving only six survivors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moro_Rebellion\" title=\"Moro Rebellion\">Moro Rebellion</a>: <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> troops bring overwhelming force against the native <a href=\"https://wikipedia.org/wiki/Moro_people\" title=\"Moro people\"><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Bud_Dajo\" title=\"First Battle of Bud Dajo\">First Battle of Bud Dajo</a>, leaving only six survivors.", "links": [{"title": "Moro Rebellion", "link": "https://wikipedia.org/wiki/Moro_Rebellion"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Moro people", "link": "https://wikipedia.org/wiki/Moro_people"}, {"title": "First Battle of Bud Dajo", "link": "https://wikipedia.org/wiki/First_Battle_of_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "Italo-Turkish War: Italian forces are the first to use airships for military purposes, employing them for reconnaissance behind Turkish lines.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Italo-Turkish_War\" title=\"Italo-Turkish War\">Italo-Turkish War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> forces are the first to use <a href=\"https://wikipedia.org/wiki/Airship\" title=\"Airship\">airships</a> for military purposes, employing them for <a href=\"https://wikipedia.org/wiki/Reconnaissance\" title=\"Reconnaissance\">reconnaissance</a> behind <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Turkish</a> lines.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italo-Turkish_War\" title=\"Italo-Turkish War\">Italo-Turkish War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> forces are the first to use <a href=\"https://wikipedia.org/wiki/Airship\" title=\"Airship\">airships</a> for military purposes, employing them for <a href=\"https://wikipedia.org/wiki/Reconnaissance\" title=\"Reconnaissance\">reconnaissance</a> behind <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Turkish</a> lines.", "links": [{"title": "Italo-Turkish War", "link": "https://wikipedia.org/wiki/Italo-Turkish_War"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "Airship", "link": "https://wikipedia.org/wiki/Airship"}, {"title": "Reconnaissance", "link": "https://wikipedia.org/wiki/Reconnaissance"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1931", "text": "The British Raj: Gandhi-Irwin Pact is signed.", "html": "1931 - The <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British Raj</a>: <a href=\"https://wikipedia.org/wiki/Gandhi%E2%80%93Irwin_Pact\" title=\"Gandhi-Irwin Pact\">Gandhi-Irwin Pact</a> is signed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British Raj</a>: <a href=\"https://wikipedia.org/wiki/Gandhi%E2%80%93Irwin_Pact\" title=\"Gandhi-Irwin Pact\">Gandhi-Irwin Pact</a> is signed.", "links": [{"title": "British Raj", "link": "https://wikipedia.org/wiki/British_Raj"}, {"title": "Gandhi-Irwin Pact", "link": "https://wikipedia.org/wiki/Gandhi%E2%80%93Irwin_Pact"}]}, {"year": "1933", "text": "<PERSON>'s Nazi Party receives 43.9% at the Reichstag elections, which allows the Nazis to later pass the Enabling Act and establish a dictatorship.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a> receives 43.9% at the <a href=\"https://wikipedia.org/wiki/German_federal_election,_March_1933\" class=\"mw-redirect\" title=\"German federal election, March 1933\">Reichstag elections</a>, which allows the Nazis to later pass the <a href=\"https://wikipedia.org/wiki/Enabling_Act_of_1933\" title=\"Enabling Act of 1933\">Enabling Act</a> and establish a dictatorship.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a> receives 43.9% at the <a href=\"https://wikipedia.org/wiki/German_federal_election,_March_1933\" class=\"mw-redirect\" title=\"German federal election, March 1933\">Reichstag elections</a>, which allows the Nazis to later pass the <a href=\"https://wikipedia.org/wiki/Enabling_Act_of_1933\" title=\"Enabling Act of 1933\">Enabling Act</a> and establish a dictatorship.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nazi Party", "link": "https://wikipedia.org/wiki/Nazi_Party"}, {"title": "German federal election, March 1933", "link": "https://wikipedia.org/wiki/German_federal_election,_March_1933"}, {"title": "Enabling Act of 1933", "link": "https://wikipedia.org/wiki/Enabling_Act_of_1933"}]}, {"year": "1939", "text": "Spanish Civil War: The National Defence Council seizes control of the republican government in a coup d'etat, with the intention of negotiating an end to the war.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/National_Defence_Council_(Spain)\" title=\"National Defence Council (Spain)\">National Defence Council</a> seizes control of the <a href=\"https://wikipedia.org/wiki/Republican_faction_(Spanish_Civil_War)\" title=\"Republican faction (Spanish Civil War)\">republican</a> government in a <a href=\"https://wikipedia.org/wiki/Coup_d%27etat\" class=\"mw-redirect\" title=\"Coup d'etat\">coup d'etat</a>, with the intention of negotiating an end to the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/National_Defence_Council_(Spain)\" title=\"National Defence Council (Spain)\">National Defence Council</a> seizes control of the <a href=\"https://wikipedia.org/wiki/Republican_faction_(Spanish_Civil_War)\" title=\"Republican faction (Spanish Civil War)\">republican</a> government in a <a href=\"https://wikipedia.org/wiki/Coup_d%27etat\" class=\"mw-redirect\" title=\"Coup d'etat\">coup d'etat</a>, with the intention of negotiating an end to the war.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "National Defence Council (Spain)", "link": "https://wikipedia.org/wiki/National_Defence_Council_(Spain)"}, {"title": "Republican faction (Spanish Civil War)", "link": "https://wikipedia.org/wiki/Republican_faction_(Spanish_Civil_War)"}, {"title": "Coup d'etat", "link": "https://wikipedia.org/wiki/Coup_d%27etat"}]}, {"year": "1940", "text": "Six high-ranking members of the Soviet politburo, including <PERSON>, sign an order for the execution of 25,700 Polish intelligentsia, including 14,700 Polish POWs, in what will become known as the Katyn massacre.", "html": "1940 - Six high-ranking members of the <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Politburo of the Communist Party of the Soviet Union\">Soviet politburo</a>, including <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sign an order for the execution of 25,700 Polish <a href=\"https://wikipedia.org/wiki/Intelligentsia\" title=\"Intelligentsia\">intelligentsia</a>, including 14,700 Polish <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">POWs</a>, in what will become known as the <a href=\"https://wikipedia.org/wiki/Katyn_massacre\" title=\"Katyn massacre\">Katyn massacre</a>.", "no_year_html": "Six high-ranking members of the <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Politburo of the Communist Party of the Soviet Union\">Soviet politburo</a>, including <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sign an order for the execution of 25,700 Polish <a href=\"https://wikipedia.org/wiki/Intelligentsia\" title=\"Intelligentsia\">intelligentsia</a>, including 14,700 Polish <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">POWs</a>, in what will become known as the <a href=\"https://wikipedia.org/wiki/Katyn_massacre\" title=\"Katyn massacre\">Katyn massacre</a>.", "links": [{"title": "Politburo of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Intelligentsia", "link": "https://wikipedia.org/wiki/Intelligentsia"}, {"title": "Prisoner of war", "link": "https://wikipedia.org/wiki/Prisoner_of_war"}, {"title": "Katyn massacre", "link": "https://wikipedia.org/wiki/Katyn_massacre"}]}, {"year": "1942", "text": "World War II: Japanese forces capture Batavia, capital of Dutch East Indies, which is left undefended after the withdrawal of the KNIL garrison and Australian Blackforce battalion to Buitenzorg and Bandung.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces capture <a href=\"https://wikipedia.org/wiki/Batavia,_Dutch_East_Indies\" title=\"Batavia, Dutch East Indies\">Batavia</a>, capital of <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>, which is left undefended after the withdrawal of the <a href=\"https://wikipedia.org/wiki/Royal_Netherlands_East_Indies_Army\" title=\"Royal Netherlands East Indies Army\">KNIL</a> garrison and Australian Blackforce <a href=\"https://wikipedia.org/wiki/Battalion\" title=\"Battalion\">battalion</a> to <a href=\"https://wikipedia.org/wiki/Buitenzorg\" class=\"mw-redirect\" title=\"Buitenzorg\">Buitenzorg</a> and <a href=\"https://wikipedia.org/wiki/Bandung\" title=\"Bandung\">Bandung</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces capture <a href=\"https://wikipedia.org/wiki/Batavia,_Dutch_East_Indies\" title=\"Batavia, Dutch East Indies\">Batavia</a>, capital of <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>, which is left undefended after the withdrawal of the <a href=\"https://wikipedia.org/wiki/Royal_Netherlands_East_Indies_Army\" title=\"Royal Netherlands East Indies Army\">KNIL</a> garrison and Australian Blackforce <a href=\"https://wikipedia.org/wiki/Battalion\" title=\"Battalion\">battalion</a> to <a href=\"https://wikipedia.org/wiki/Buitenzorg\" class=\"mw-redirect\" title=\"Buitenzorg\">Buitenzorg</a> and <a href=\"https://wikipedia.org/wiki/Bandung\" title=\"Bandung\">Bandung</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Batavia, Dutch East Indies", "link": "https://wikipedia.org/wiki/Batavia,_Dutch_East_Indies"}, {"title": "Dutch East Indies", "link": "https://wikipedia.org/wiki/Dutch_East_Indies"}, {"title": "Royal Netherlands East Indies Army", "link": "https://wikipedia.org/wiki/Royal_Netherlands_East_Indies_Army"}, {"title": "Battalion", "link": "https://wikipedia.org/wiki/Battalion"}, {"title": "Buitenzorg", "link": "https://wikipedia.org/wiki/Buitenzorg"}, {"title": "Bandung", "link": "https://wikipedia.org/wiki/Bandung"}]}, {"year": "1943", "text": "World War II: General strike and protest march in Athens against rumours of forced  mobilization of Greek workers for work in Germany,  resulting in clashes with the Axis occupation forces and collaborationist police. The decree is withdrawn on the next day.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/1943_Greek_protests_against_labour_mobilization\" title=\"1943 Greek protests against labour mobilization\">General strike and protest march</a> in Athens against rumours of forced mobilization of Greek workers for work in Germany, resulting in clashes with the Axis occupation forces and collaborationist police. The decree is withdrawn on the next day.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/1943_Greek_protests_against_labour_mobilization\" title=\"1943 Greek protests against labour mobilization\">General strike and protest march</a> in Athens against rumours of forced mobilization of Greek workers for work in Germany, resulting in clashes with the Axis occupation forces and collaborationist police. The decree is withdrawn on the next day.", "links": [{"title": "1943 Greek protests against labour mobilization", "link": "https://wikipedia.org/wiki/1943_Greek_protests_against_labour_mobilization"}]}, {"year": "1944", "text": "World War II: The Red Army begins the Uman-Botoșani offensive in the western Ukrainian SSR.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> begins the <a href=\"https://wikipedia.org/wiki/Uman%E2%80%93Boto%C8%99ani_offensive\" title=\"<PERSON><PERSON>-<PERSON><PERSON> offensive\"><PERSON><PERSON>-<PERSON><PERSON> offensive</a> in the western <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian SSR</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> begins the <a href=\"https://wikipedia.org/wiki/Uman%E2%80%93Boto%C8%99ani_offensive\" title=\"<PERSON><PERSON>-<PERSON><PERSON> offensive\"><PERSON><PERSON>-<PERSON><PERSON> offensive</a> in the western <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian SSR</a>.", "links": [{"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Uman-<PERSON><PERSON><PERSON><PERSON> offensive", "link": "https://wikipedia.org/wiki/Uman%E2%80%93Boto%C8%99ani_offensive"}, {"title": "Ukrainian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic"}]}, {"year": "1946", "text": "Cold War: <PERSON> delivers his famous \"Iron Curtain\" speech at Westminster College, Missouri.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his famous \"<a href=\"https://wikipedia.org/wiki/Iron_Curtain\" title=\"Iron Curtain\">Iron Curtain</a>\" speech at <a href=\"https://wikipedia.org/wiki/Westminster_College,_Missouri\" class=\"mw-redirect\" title=\"Westminster College, Missouri\">Westminster College, Missouri</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his famous \"<a href=\"https://wikipedia.org/wiki/Iron_Curtain\" title=\"Iron Curtain\"><PERSON> Curtain</a>\" speech at <a href=\"https://wikipedia.org/wiki/Westminster_College,_Missouri\" class=\"mw-redirect\" title=\"Westminster College, Missouri\">Westminster College, Missouri</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Iron Curtain", "link": "https://wikipedia.org/wiki/Iron_Curtain"}, {"title": "Westminster College, Missouri", "link": "https://wikipedia.org/wiki/Westminster_College,_Missouri"}]}, {"year": "1953", "text": "<PERSON>, the longest serving leader of the Soviet Union, dies at his Volynskoe dacha in Moscow after suffering a cerebral hemorrhage four days earlier.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the longest serving leader of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON>\" title=\"Death and state funeral of <PERSON>\">dies</a> at his Volynskoe dacha in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a> after suffering a <a href=\"https://wikipedia.org/wiki/Intracerebral_hemorrhage\" title=\"Intracerebral hemorrhage\">cerebral hemorrhage</a> four days earlier.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the longest serving leader of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON>\" title=\"Death and state funeral of <PERSON>\">dies</a> at his Volynskoe dacha in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a> after suffering a <a href=\"https://wikipedia.org/wiki/Intracerebral_hemorrhage\" title=\"Intracerebral hemorrhage\">cerebral hemorrhage</a> four days earlier.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Death and state funeral of <PERSON>", "link": "https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON>"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}, {"title": "Intracerebral hemorrhage", "link": "https://wikipedia.org/wiki/Intracerebral_hemorrhage"}]}, {"year": "1957", "text": "Sutton Wick air crash a Blackburn Beverley of 53 Squadron, Royal Air Forces, crashes into the village of Sutton Wick, Berkshire (now Oxfordshire) killing most of the crew and passengers and two local residents.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Sutton_Wick_air_crash\" title=\"Sutton Wick air crash\">Sutton Wick air crash</a> a Blackburn Beverley of 53 Squadron, Royal Air Forces, crashes into the village of Sutton Wick, Berkshire (now Oxfordshire) killing most of the crew and passengers and two local residents.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sutton_Wick_air_crash\" title=\"Sutton Wick air crash\">Sutton Wick air crash</a> a Blackburn Beverley of 53 Squadron, Royal Air Forces, crashes into the village of Sutton Wick, Berkshire (now Oxfordshire) killing most of the crew and passengers and two local residents.", "links": [{"title": "<PERSON> air crash", "link": "https://wikipedia.org/wiki/Sutton_Wick_air_crash"}]}, {"year": "1960", "text": "Indonesian President <PERSON><PERSON><PERSON> dismissed the Dewan Perwakilan Rakyat (DPR), 1955 democratically elected parliament, and replaced with DPR-GR, the parliament of his own selected members.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">Indonesian President</a> <a href=\"https://wikipedia.org/wiki/Sukarno\" title=\"Sukarno\"><PERSON><PERSON><PERSON></a> dismissed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Perwakilan_Rakyat\" class=\"mw-redirect\" title=\"Dewan Perwakilan Rakyat\"><PERSON><PERSON> (DPR)</a>, 1955 democratically elected parliament, and replaced with DPR-GR, the parliament of his own selected members.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">Indonesian President</a> <a href=\"https://wikipedia.org/wiki/Sukarno\" title=\"Sukarno\"><PERSON><PERSON><PERSON></a> dismissed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Perwakilan_Rakyat\" class=\"mw-redirect\" title=\"Dewan Perwakilan Rakyat\"><PERSON><PERSON> (DPR)</a>, 1955 democratically elected parliament, and replaced with DPR-GR, the parliament of his own selected members.", "links": [{"title": "President of Indonesia", "link": "https://wikipedia.org/wiki/President_of_Indonesia"}, {"title": "Sukar<PERSON>", "link": "https://wikipedia.org/wiki/Sukarno"}, {"title": "<PERSON><PERSON> Raky<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Perwakilan_Rakyat"}]}, {"year": "1963", "text": "American country music stars <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and their pilot <PERSON> are killed in a plane crash in Camden, Tennessee.", "html": "1963 - American country music stars <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cline\" title=\"Patsy Cline\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cowboy_Copas\" title=\"Cowboy Copas\">Cowboy Copas</a> and their pilot <PERSON> are killed in a <a href=\"https://wikipedia.org/wiki/1963_Camden_PA-24_crash\" title=\"1963 Camden PA-24 crash\">plane crash</a> in <a href=\"https://wikipedia.org/wiki/Camden,_Tennessee\" title=\"Camden, Tennessee\">Camden, Tennessee</a>.", "no_year_html": "American country music stars <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>line\" title=\"Patsy Cline\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cowboy_Copas\" title=\"Cowboy Copas\">Cowboy Copas</a> and their pilot <PERSON> are killed in a <a href=\"https://wikipedia.org/wiki/1963_Camden_PA-24_crash\" title=\"1963 Camden PA-24 crash\">plane crash</a> in <a href=\"https://wikipedia.org/wiki/Camden,_Tennessee\" title=\"Camden, Tennessee\">Camden, Tennessee</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sy_Cline"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Cowboy Copas", "link": "https://wikipedia.org/wiki/Cowboy_Copas"}, {"title": "1963 Camden PA-24 crash", "link": "https://wikipedia.org/wiki/1963_Camden_PA-24_crash"}, {"title": "Camden, Tennessee", "link": "https://wikipedia.org/wiki/Camden,_Tennessee"}]}, {"year": "1963", "text": "Aeroflot Flight 191 crashes while landing at Aşgabat International Airport, killing 12.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_191\" title=\"Aeroflot Flight 191\">Aeroflot Flight 191</a> crashes while landing at <a href=\"https://wikipedia.org/wiki/A%C5%9Fgabat_International_Airport\" title=\"Aşgabat International Airport\">Aşgabat International Airport</a>, killing 12.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_191\" title=\"Aeroflot Flight 191\">Aeroflot Flight 191</a> crashes while landing at <a href=\"https://wikipedia.org/wiki/A%C5%9Fgabat_International_Airport\" title=\"Aşgabat International Airport\">Aşgabat International Airport</a>, killing 12.", "links": [{"title": "Aeroflot Flight 191", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_191"}, {"title": "Aşgabat International Airport", "link": "https://wikipedia.org/wiki/A%C5%9Fgabat_International_Airport"}]}, {"year": "1965", "text": "March Intifada: A Leftist uprising erupts in Bahrain against the British colonial presence.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/March_Intifada\" title=\"March Intifada\">March Intifada</a>: A <a href=\"https://wikipedia.org/wiki/Left-wing_politics\" title=\"Left-wing politics\">Leftist</a> uprising erupts in <a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a> against the British colonial presence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/March_Intifada\" title=\"March Intifada\">March Intifada</a>: A <a href=\"https://wikipedia.org/wiki/Left-wing_politics\" title=\"Left-wing politics\">Leftist</a> uprising erupts in <a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a> against the British colonial presence.", "links": [{"title": "March Intifada", "link": "https://wikipedia.org/wiki/March_Intifada"}, {"title": "Left-wing politics", "link": "https://wikipedia.org/wiki/Left-wing_politics"}, {"title": "Bahrain", "link": "https://wikipedia.org/wiki/Bahrain"}]}, {"year": "1966", "text": "BOAC Flight 911, a Boeing 707 aircraft, breaks apart in mid-air due to clear-air turbulence and crashes into Mount Fuji, Japan, killing all 124 people on board.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/BOAC_Flight_911\" title=\"BOAC Flight 911\">BOAC Flight 911</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a> aircraft, breaks apart in mid-air due to <a href=\"https://wikipedia.org/wiki/Clear-air_turbulence\" title=\"Clear-air turbulence\">clear-air turbulence</a> and crashes into <a href=\"https://wikipedia.org/wiki/Mount_Fuji\" title=\"Mount Fuji\">Mount Fuji</a>, <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>, killing all 124 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/BOAC_Flight_911\" title=\"BOAC Flight 911\">BOAC Flight 911</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a> aircraft, breaks apart in mid-air due to <a href=\"https://wikipedia.org/wiki/Clear-air_turbulence\" title=\"Clear-air turbulence\">clear-air turbulence</a> and crashes into <a href=\"https://wikipedia.org/wiki/Mount_Fuji\" title=\"Mount Fuji\">Mount Fuji</a>, <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>, killing all 124 people on board.", "links": [{"title": "BOAC Flight 911", "link": "https://wikipedia.org/wiki/BOAC_Flight_911"}, {"title": "Boeing 707", "link": "https://wikipedia.org/wiki/Boeing_707"}, {"title": "Clear-air turbulence", "link": "https://wikipedia.org/wiki/Clear-air_turbulence"}, {"title": "Mount Fuji", "link": "https://wikipedia.org/wiki/Mount_Fuji"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}]}, {"year": "1967", "text": "Lake Central Airlines Flight 527 crashes near Marseilles, Ohio, killing 38.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Lake_Central_Airlines_Flight_527\" title=\"Lake Central Airlines Flight 527\">Lake Central Airlines Flight 527</a> crashes near <a href=\"https://wikipedia.org/wiki/Marseilles,_Ohio\" title=\"Marseilles, Ohio\">Marseilles, Ohio</a>, killing 38.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lake_Central_Airlines_Flight_527\" title=\"Lake Central Airlines Flight 527\">Lake Central Airlines Flight 527</a> crashes near <a href=\"https://wikipedia.org/wiki/Marseilles,_Ohio\" title=\"Marseilles, Ohio\">Marseilles, Ohio</a>, killing 38.", "links": [{"title": "Lake Central Airlines Flight 527", "link": "https://wikipedia.org/wiki/Lake_Central_Airlines_Flight_527"}, {"title": "Marseilles, Ohio", "link": "https://wikipedia.org/wiki/Marseilles,_Ohio"}]}, {"year": "1968", "text": "Air France Flight 212 crashes into La Grande Soufrière, killing all 63 aboard.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Air_France_Flight_212_(1968)\" title=\"Air France Flight 212 (1968)\">Air France Flight 212</a> crashes into <a href=\"https://wikipedia.org/wiki/La_Grande_Soufri%C3%A8re\" title=\"La Grande Soufrière\">La Grande Soufrière</a>, killing all 63 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_France_Flight_212_(1968)\" title=\"Air France Flight 212 (1968)\">Air France Flight 212</a> crashes into <a href=\"https://wikipedia.org/wiki/La_Grande_Soufri%C3%A8re\" title=\"La Grande Soufrière\">La Grande Soufrière</a>, killing all 63 aboard.", "links": [{"title": "Air France Flight 212 (1968)", "link": "https://wikipedia.org/wiki/Air_France_Flight_212_(1968)"}, {"title": "La Grande Soufrière", "link": "https://wikipedia.org/wiki/La_Grande_Soufri%C3%A8re"}]}, {"year": "1970", "text": "The Treaty on the Non-Proliferation of Nuclear Weapons goes into effect after ratification by 43 nations.", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/Treaty_on_the_Non-Proliferation_of_Nuclear_Weapons\" title=\"Treaty on the Non-Proliferation of Nuclear Weapons\">Treaty on the Non-Proliferation of Nuclear Weapons</a> goes into effect after ratification by 43 nations.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_on_the_Non-Proliferation_of_Nuclear_Weapons\" title=\"Treaty on the Non-Proliferation of Nuclear Weapons\">Treaty on the Non-Proliferation of Nuclear Weapons</a> goes into effect after ratification by 43 nations.", "links": [{"title": "Treaty on the Non-Proliferation of Nuclear Weapons", "link": "https://wikipedia.org/wiki/Treaty_on_the_Non-Proliferation_of_Nuclear_Weapons"}]}, {"year": "1973", "text": "An Iberia McDonnell Douglas DC-9 collide in mid-air with a Spantax Convair 990 Coronado over Nantes, France, killing all 68 people aboard the DC-9, including music manager <PERSON>.", "html": "1973 - An <a href=\"https://wikipedia.org/wiki/Iberia_(airline)\" title=\"Iberia (airline)\">Iberia</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\">McDonnell Douglas DC-9</a> <a href=\"https://wikipedia.org/wiki/1973_Nantes_mid-air_collision\" title=\"1973 Nantes mid-air collision\">collide</a> in mid-air with a <a href=\"https://wikipedia.org/wiki/Spantax\" title=\"Spantax\">Spantax</a> <a href=\"https://wikipedia.org/wiki/Convair_990_Coronado\" title=\"Convair 990 Coronado\">Convair 990 Coronado</a> over <a href=\"https://wikipedia.org/wiki/Nantes\" title=\"Nantes\">Nantes</a>, France, killing all 68 people aboard the DC-9, including music manager <a href=\"https://wikipedia.org/wiki/<PERSON>(manager)\" class=\"mw-redirect\" title=\"<PERSON> (manager)\"><PERSON></a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Iberia_(airline)\" title=\"Iberia (airline)\">Iberia</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\">McDonnell Douglas DC-9</a> <a href=\"https://wikipedia.org/wiki/1973_Nantes_mid-air_collision\" title=\"1973 Nantes mid-air collision\">collide</a> in mid-air with a <a href=\"https://wikipedia.org/wiki/Spantax\" title=\"Spantax\">Spantax</a> <a href=\"https://wikipedia.org/wiki/Convair_990_Coronado\" title=\"Convair 990 Coronado\">Convair 990 Coronado</a> over <a href=\"https://wikipedia.org/wiki/Nantes\" title=\"Nantes\">Nantes</a>, France, killing all 68 people aboard the DC-9, including music manager <a href=\"https://wikipedia.org/wiki/<PERSON>(manager)\" class=\"mw-redirect\" title=\"<PERSON> (manager)\"><PERSON></a>.", "links": [{"title": "Iberia (airline)", "link": "https://wikipedia.org/wiki/Iberia_(airline)"}, {"title": "McDonnell Douglas DC-9", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_DC-9"}, {"title": "1973 Nantes mid-air collision", "link": "https://wikipedia.org/wiki/1973_Nantes_mid-air_collision"}, {"title": "Spantax", "link": "https://wikipedia.org/wiki/Spantax"}, {"title": "Convair 990 Coronado", "link": "https://wikipedia.org/wiki/Convair_990_Coronado"}, {"title": "Nantes", "link": "https://wikipedia.org/wiki/Nantes"}, {"title": "<PERSON> (manager)", "link": "https://wikipedia.org/wiki/<PERSON>_(manager)"}]}, {"year": "1974", "text": "Yom Kippur War: Israeli forces withdraw from the west bank of the Suez Canal.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Yom_Kippur_War\" title=\"Yom Kippur War\">Yom Kippur War</a>: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> forces withdraw from the west bank of the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yom_Kippur_War\" title=\"Yom Kippur War\">Yom Kippur War</a>: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> forces withdraw from the west bank of the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>.", "links": [{"title": "Yom Kippur War", "link": "https://wikipedia.org/wiki/Yom_Kippur_War"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}]}, {"year": "1978", "text": "The Landsat 3 is launched from Vandenberg Air Force Base in California.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/Landsat_3\" title=\"Landsat 3\">Landsat 3</a> is launched from <a href=\"https://wikipedia.org/wiki/Vandenberg_Air_Force_Base\" class=\"mw-redirect\" title=\"Vandenberg Air Force Base\">Vandenberg Air Force Base</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Landsat_3\" title=\"Landsat 3\">Landsat 3</a> is launched from <a href=\"https://wikipedia.org/wiki/Vandenberg_Air_Force_Base\" class=\"mw-redirect\" title=\"Vandenberg Air Force Base\">Vandenberg Air Force Base</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "links": [{"title": "Landsat 3", "link": "https://wikipedia.org/wiki/Landsat_3"}, {"title": "Vandenberg Air Force Base", "link": "https://wikipedia.org/wiki/Vandenberg_Air_Force_Base"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}]}, {"year": "1979", "text": "Soviet probes Venera 11, Venera 12 and the German-American solar satellite Helios II all are hit by \"off the scale\" gamma rays leading to the discovery of soft gamma repeaters.", "html": "1979 - Soviet probes <a href=\"https://wikipedia.org/wiki/Venera_11\" title=\"Venera 11\">Venera 11</a>, <a href=\"https://wikipedia.org/wiki/Venera_12\" title=\"Venera 12\">Venera 12</a> and the German-American solar satellite <a href=\"https://wikipedia.org/wiki/Helios_(spacecraft)\" title=\"Helio<PERSON> (spacecraft)\">Helios II</a> all are hit by <a href=\"https://wikipedia.org/wiki/Gamma-ray_burst\" title=\"Gamma-ray burst\">\"off the scale\" gamma rays</a> leading to the discovery of <a href=\"https://wikipedia.org/wiki/Soft_gamma_repeater\" title=\"Soft gamma repeater\">soft gamma repeaters</a>.", "no_year_html": "Soviet probes <a href=\"https://wikipedia.org/wiki/Venera_11\" title=\"Venera 11\">Venera 11</a>, <a href=\"https://wikipedia.org/wiki/Venera_12\" title=\"Venera 12\">Venera 12</a> and the German-American solar satellite <a href=\"https://wikipedia.org/wiki/Helio<PERSON>_(spacecraft)\" title=\"Helio<PERSON> (spacecraft)\">Helios II</a> all are hit by <a href=\"https://wikipedia.org/wiki/Gamma-ray_burst\" title=\"Gamma-ray burst\">\"off the scale\" gamma rays</a> leading to the discovery of <a href=\"https://wikipedia.org/wiki/Soft_gamma_repeater\" title=\"Soft gamma repeater\">soft gamma repeaters</a>.", "links": [{"title": "Venera 11", "link": "https://wikipedia.org/wiki/Venera_11"}, {"title": "Venera 12", "link": "https://wikipedia.org/wiki/Venera_12"}, {"title": "<PERSON><PERSON><PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/He<PERSON><PERSON>_(spacecraft)"}, {"title": "Gamma-ray burst", "link": "https://wikipedia.org/wiki/Gamma-ray_burst"}, {"title": "Soft gamma repeater", "link": "https://wikipedia.org/wiki/Soft_gamma_repeater"}]}, {"year": "1981", "text": "The ZX81, a pioneering British home computer, is launched by Sinclair Research and would go on to sell over 1.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}1⁄2 million units around the world.", "html": "1981 - The <a href=\"https://wikipedia.org/wiki/ZX81\" title=\"ZX81\">ZX81</a>, a pioneering British <a href=\"https://wikipedia.org/wiki/Home_computer\" title=\"Home computer\">home computer</a>, is launched by <a href=\"https://wikipedia.org/wiki/Sinclair_Research\" title=\"Sinclair Research\">Sinclair Research</a> and would go on to sell over 1<style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\"><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> million units around the world.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/ZX81\" title=\"ZX81\">ZX81</a>, a pioneering British <a href=\"https://wikipedia.org/wiki/Home_computer\" title=\"Home computer\">home computer</a>, is launched by <a href=\"https://wikipedia.org/wiki/Sinclair_Research\" title=\"Sinclair Research\">Sinclair Research</a> and would go on to sell over 1<style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\"><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> million units around the world.", "links": [{"title": "ZX81", "link": "https://wikipedia.org/wiki/ZX81"}, {"title": "Home computer", "link": "https://wikipedia.org/wiki/Home_computer"}, {"title": "Sinclair Research", "link": "https://wikipedia.org/wiki/Sinclair_Research"}]}, {"year": "1982", "text": "Soviet probe Venera 14 lands on Venus.", "html": "1982 - Soviet probe <a href=\"https://wikipedia.org/wiki/Venera_14\" title=\"Venera 14\">Venera 14</a> lands on <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>.", "no_year_html": "Soviet probe <a href=\"https://wikipedia.org/wiki/Venera_14\" title=\"Venera 14\">Venera 14</a> lands on <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>.", "links": [{"title": "Venera 14", "link": "https://wikipedia.org/wiki/Venera_14"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}]}, {"year": "1991", "text": "Aeropostal Alas de Venezuela Flight 109 crashes in Venezuela, killing 45.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Aeropostal_Alas_de_Venezuela_Flight_109\" title=\"Aeropostal Alas de Venezuela Flight 109\">Aeropostal Alas de Venezuela Flight 109</a> crashes in Venezuela, killing 45.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeropostal_Alas_de_Venezuela_Flight_109\" title=\"Aeropostal Alas de Venezuela Flight 109\">Aeropostal Alas de Venezuela Flight 109</a> crashes in Venezuela, killing 45.", "links": [{"title": "Aeropostal Alas de Venezuela Flight 109", "link": "https://wikipedia.org/wiki/Aeropostal_Alas_de_Venezuela_Flight_109"}]}, {"year": "1993", "text": "Palair Macedonian Airlines Flight 301 crashes at Skopje International Airport in Petrovec, North Macedonia, killing 83.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Palair_Macedonian_Airlines_Flight_301\" title=\"Palair Macedonian Airlines Flight 301\">Palair Macedonian Airlines Flight 301</a> crashes at <a href=\"https://wikipedia.org/wiki/Skopje_International_Airport\" title=\"Skopje International Airport\">Skopje International Airport</a> in <a href=\"https://wikipedia.org/wiki/Petrovec,_North_Macedonia\" title=\"Petrovec, North Macedonia\">Petrovec, North Macedonia</a>, killing 83.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Palair_Macedonian_Airlines_Flight_301\" title=\"Palair Macedonian Airlines Flight 301\">Palair Macedonian Airlines Flight 301</a> crashes at <a href=\"https://wikipedia.org/wiki/Skopje_International_Airport\" title=\"Skopje International Airport\">Skopje International Airport</a> in <a href=\"https://wikipedia.org/wiki/Petrovec,_North_Macedonia\" title=\"Petrovec, North Macedonia\">Petrovec, North Macedonia</a>, killing 83.", "links": [{"title": "Palair Macedonian Airlines Flight 301", "link": "https://wikipedia.org/wiki/Palair_Macedonian_Airlines_Flight_301"}, {"title": "Skopje International Airport", "link": "https://wikipedia.org/wiki/Skopje_International_Airport"}, {"title": "Petrovec, North Macedonia", "link": "https://wikipedia.org/wiki/Petrovec,_North_Macedonia"}]}, {"year": "2001", "text": "In Mina, Saudi Arabia, 35 pilgrims are killed in a stampede on the Jamaraat Bridge during the Hajj.", "html": "2001 - In <a href=\"https://wikipedia.org/wiki/<PERSON>,_Saudi_Arabia\" title=\"Mina, Saudi Arabia\">Mina, Saudi Arabia</a>, 35 pilgrims are killed in a <a href=\"https://wikipedia.org/wiki/2001_Hajj_stampede\" title=\"2001 Hajj stampede\">stampede</a> on the <a href=\"https://wikipedia.org/wiki/Jamaraat_Bridge\" title=\"Jamaraat Bridge\">Jamaraat Bridge</a> during the <a href=\"https://wikipedia.org/wiki/Hajj\" title=\"Hajj\">Hajj</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/<PERSON>,_Saudi_Arabia\" title=\"Mina, Saudi Arabia\">Mina, Saudi Arabia</a>, 35 pilgrims are killed in a <a href=\"https://wikipedia.org/wiki/2001_Hajj_stampede\" title=\"2001 Hajj stampede\">stampede</a> on the <a href=\"https://wikipedia.org/wiki/Jamaraat_Bridge\" title=\"Jamaraat Bridge\">Jamaraat Bridge</a> during the <a href=\"https://wikipedia.org/wiki/Hajj\" title=\"Hajj\">Hajj</a>.", "links": [{"title": "Mina, Saudi Arabia", "link": "https://wikipedia.org/wiki/Mina,_Saudi_Arabia"}, {"title": "2001 Hajj stampede", "link": "https://wikipedia.org/wiki/2001_Hajj_stampede"}, {"title": "Jamaraat Bridge", "link": "https://wikipedia.org/wiki/Jamaraat_Bridge"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hajj"}]}, {"year": "2002", "text": "An earthquake in Mindanao, Philippines, kills 15 people and injures more than 100.", "html": "2002 - An <a href=\"https://wikipedia.org/wiki/2002_Mindanao_earthquake\" title=\"2002 Mindanao earthquake\">earthquake</a> in <a href=\"https://wikipedia.org/wiki/Mindanao\" title=\"Mindanao\">Mindanao</a>, Philippines, kills 15 people and injures more than 100.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2002_Mindanao_earthquake\" title=\"2002 Mindanao earthquake\">earthquake</a> in <a href=\"https://wikipedia.org/wiki/Mindanao\" title=\"Mindanao\">Mindanao</a>, Philippines, kills 15 people and injures more than 100.", "links": [{"title": "2002 Mindanao earthquake", "link": "https://wikipedia.org/wiki/2002_Mindanao_earthquake"}, {"title": "Mindanao", "link": "https://wikipedia.org/wiki/Mindanao"}]}, {"year": "2003", "text": "In Haifa, 17 Israeli civilians are killed in the Haifa bus 37 suicide bombing.", "html": "2003 - In <a href=\"https://wikipedia.org/wiki/Haifa\" title=\"Haifa\">Haifa</a>, 17 Israeli civilians are killed in the <a href=\"https://wikipedia.org/wiki/Haifa_bus_37_suicide_bombing\" title=\"Haifa bus 37 suicide bombing\">Haifa bus 37 suicide bombing</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Haifa\" title=\"Haifa\">Haifa</a>, 17 Israeli civilians are killed in the <a href=\"https://wikipedia.org/wiki/Haifa_bus_37_suicide_bombing\" title=\"Haifa bus 37 suicide bombing\">Haifa bus 37 suicide bombing</a>.", "links": [{"title": "Haifa", "link": "https://wikipedia.org/wiki/Haifa"}, {"title": "Haifa bus 37 suicide bombing", "link": "https://wikipedia.org/wiki/Haifa_bus_37_suicide_bombing"}]}, {"year": "2011", "text": "An Antonov An-148 crashes in Russia's Alexeyevsky District, Belgorod Oblast during a test flight, killing all seven aboard.", "html": "2011 - An <a href=\"https://wikipedia.org/wiki/Antonov_An-148\" title=\"Antonov An-148\">Antonov An-148</a> <a href=\"https://wikipedia.org/wiki/2011_Garbuzovo_Antonov_An-148_crash\" title=\"2011 Garbuzovo Antonov An-148 crash\">crashes</a> in Russia's <a href=\"https://wikipedia.org/wiki/Alexeyevsky_District,_Belgorod_Oblast\" title=\"Alexeyevsky District, Belgorod Oblast\">Alexeyevsky District, Belgorod Oblast</a> during a test flight, killing all seven aboard.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Antonov_An-148\" title=\"Antonov An-148\">Antonov An-148</a> <a href=\"https://wikipedia.org/wiki/2011_Garbuzovo_Antonov_An-148_crash\" title=\"2011 Garbuzovo Antonov An-148 crash\">crashes</a> in Russia's <a href=\"https://wikipedia.org/wiki/Alexeyevsky_District,_Belgorod_Oblast\" title=\"Alexeyevsky District, Belgorod Oblast\">Alexeyevsky District, Belgorod Oblast</a> during a test flight, killing all seven aboard.", "links": [{"title": "<PERSON><PERSON> An-148", "link": "https://wikipedia.org/wiki/Antonov_An-148"}, {"title": "2011 Garbuzovo <PERSON>ov An-148 crash", "link": "https://wikipedia.org/wiki/2011_Garbuzo<PERSON>_Antonov_An-148_crash"}, {"title": "Alexeyevsky District, Belgorod Oblast", "link": "https://wikipedia.org/wiki/Alexeyevsky_District,_Belgorod_Oblast"}]}, {"year": "2012", "text": "Tropical Storm <PERSON><PERSON> kills over 75 as it passes through Madagascar.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Tropical_Storm_Irina\" title=\"Tropical Storm Irina\">Tropical Storm Irina</a> kills over 75 as it passes through <a href=\"https://wikipedia.org/wiki/Madagascar\" title=\"Madagascar\">Madagascar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tropical_Storm_Irina\" title=\"Tropical Storm Irina\">Tropical Storm Irina</a> kills over 75 as it passes through <a href=\"https://wikipedia.org/wiki/Madagascar\" title=\"Madagascar\">Madagascar</a>.", "links": [{"title": "Tropical Storm Irina", "link": "https://wikipedia.org/wiki/Tropical_Storm_Irina"}, {"title": "Madagascar", "link": "https://wikipedia.org/wiki/Madagascar"}]}, {"year": "2012", "text": "Two people are killed and six more are injured in a shooting at a hair salon in Bucharest, Romania.", "html": "2012 - Two people are killed and six more are injured in a <a href=\"https://wikipedia.org/wiki/2012_Bucharest_hair_salon_shooting\" title=\"2012 Bucharest hair salon shooting\">shooting</a> at a hair salon in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>, Romania.", "no_year_html": "Two people are killed and six more are injured in a <a href=\"https://wikipedia.org/wiki/2012_Bucharest_hair_salon_shooting\" title=\"2012 Bucharest hair salon shooting\">shooting</a> at a hair salon in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>, Romania.", "links": [{"title": "2012 Bucharest hair salon shooting", "link": "https://wikipedia.org/wiki/2012_Bucharest_hair_salon_shooting"}, {"title": "Bucharest", "link": "https://wikipedia.org/wiki/Bucharest"}]}, {"year": "2018", "text": "Syrian civil war: The Syrian Democratic Forces (SDF) pause the Deir ez-Zor campaign due to the Turkish-led invasion of Afrin.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) pause the <a href=\"https://wikipedia.org/wiki/Deir_ez-Zor_campaign_(2017%E2%80%932019)\" title=\"Deir ez-Zor campaign (2017-2019)\"><PERSON><PERSON> ez-Zor campaign</a> due to the <a href=\"https://wikipedia.org/wiki/Operation_Olive_Branch\" title=\"Operation Olive Branch\">Turkish-led invasion of Afrin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) pause the <a href=\"https://wikipedia.org/wiki/Deir_ez-Zor_campaign_(2017%E2%80%932019)\" title=\"Deir ez-Zor campaign (2017-2019)\"><PERSON>ir ez-Zor campaign</a> due to the <a href=\"https://wikipedia.org/wiki/Operation_Olive_Branch\" title=\"Operation Olive Branch\">Turkish-led invasion of Afrin</a>.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "Syrian Democratic Forces", "link": "https://wikipedia.org/wiki/Syrian_Democratic_Forces"}, {"title": "<PERSON><PERSON> ez<PERSON>Zor campaign (2017-2019)", "link": "https://wikipedia.org/wiki/Deir_<PERSON><PERSON>-<PERSON><PERSON>_campaign_(2017%E2%80%932019)"}, {"title": "Operation Olive Branch", "link": "https://wikipedia.org/wiki/Operation_Olive_Branch"}]}, {"year": "2021", "text": "<PERSON> begins a historical visit to Iraq amidst the COVID-19 pandemic.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> begins a <a href=\"https://wikipedia.org/wiki/List_of_pastoral_visits_of_<PERSON>_<PERSON>\" title=\"List of pastoral visits of <PERSON>\">historical visit to Iraq</a> amidst the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> begins a <a href=\"https://wikipedia.org/wiki/List_of_pastoral_visits_of_<PERSON>_<PERSON>\" title=\"List of pastoral visits of <PERSON>\">historical visit to Iraq</a> amidst the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of pastoral visits of <PERSON>", "link": "https://wikipedia.org/wiki/List_of_pastoral_visits_of_<PERSON>_<PERSON>"}, {"title": "COVID-19 pandemic", "link": "https://wikipedia.org/wiki/COVID-19_pandemic"}]}, {"year": "2021", "text": "Twenty people are killed and 30 injured in a suicide car bombing in Mogadishu, Somalia.", "html": "2021 - Twenty people are killed and 30 injured in a <a href=\"https://wikipedia.org/wiki/March_2021_Mogadishu_bombing\" title=\"March 2021 Mogadishu bombing\">suicide car bombing</a> in <a href=\"https://wikipedia.org/wiki/Mogadishu\" title=\"Mogadishu\">Mogadishu</a>, Somalia.", "no_year_html": "Twenty people are killed and 30 injured in a <a href=\"https://wikipedia.org/wiki/March_2021_Mogadishu_bombing\" title=\"March 2021 Mogadishu bombing\">suicide car bombing</a> in <a href=\"https://wikipedia.org/wiki/Mogadishu\" title=\"Mogadishu\">Mogadishu</a>, Somalia.", "links": [{"title": "March 2021 Mogadishu bombing", "link": "https://wikipedia.org/wiki/March_2021_Mogadishu_bombing"}, {"title": "Mogadishu", "link": "https://wikipedia.org/wiki/Mogadishu"}]}, {"year": "2023", "text": "The 2023 Estonian parliamentary election is held, with two centre-right liberal parties gaining an absolute majority for the first time.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/2023_Estonian_parliamentary_election\" title=\"2023 Estonian parliamentary election\">2023 Estonian parliamentary election</a> is held, with two centre-right liberal parties gaining an absolute majority for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2023_Estonian_parliamentary_election\" title=\"2023 Estonian parliamentary election\">2023 Estonian parliamentary election</a> is held, with two centre-right liberal parties gaining an absolute majority for the first time.", "links": [{"title": "2023 Estonian parliamentary election", "link": "https://wikipedia.org/wiki/2023_Estonian_parliamentary_election"}]}, {"year": "2023", "text": "A group of four prisoners escape from the Nouakchott Civil Prison, before being caught the next day.", "html": "2023 - A group of four prisoners <a href=\"https://wikipedia.org/wiki/2023_Nouakchott_prison_break\" title=\"2023 Nouakchott prison break\">escape from the Nouakchott Civil Prison</a>, before being caught the next day.", "no_year_html": "A group of four prisoners <a href=\"https://wikipedia.org/wiki/2023_Nouakchott_prison_break\" title=\"2023 Nouakchott prison break\">escape from the Nouakchott Civil Prison</a>, before being caught the next day.", "links": [{"title": "2023 Nouakchott prison break", "link": "https://wikipedia.org/wiki/2023_Nouakchott_prison_break"}]}], "Births": [{"year": "1133", "text": "<PERSON> of England (d. 1189)", "html": "1133 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> (d. 1189)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> (d. 1189)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_England"}]}, {"year": "1224", "text": "<PERSON> of Poland (d. 1292)", "html": "1224 - <PERSON> <a href=\"https://wikipedia.org/wiki/Kinga_of_Poland\" title=\"<PERSON><PERSON> of Poland\"><PERSON><PERSON> of Poland</a> (d. 1292)", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/Kinga_of_Poland\" title=\"<PERSON><PERSON> of Poland\"><PERSON><PERSON> of Poland</a> (d. 1292)", "links": [{"title": "Kinga of Poland", "link": "https://wikipedia.org/wiki/Kinga_of_Poland"}]}, {"year": "1324", "text": "<PERSON> of Scotland (d. 1371)", "html": "1324 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> of Scotland</a> (d. 1371)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> of Scotland</a> (d. 1371)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Scotland"}]}, {"year": "1326", "text": "<PERSON> of Hungary (d. 1382)", "html": "1326 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> (d. 1382)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> (d. 1382)", "links": [{"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary"}]}, {"year": "1340", "text": "<PERSON><PERSON><PERSON>, Lord of Verona (d. 1375)", "html": "1340 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Scala\" title=\"<PERSON><PERSON><PERSON> della Scala\"><PERSON><PERSON><PERSON> Scala</a>, Lord of Verona (d. 1375)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Scala\" title=\"<PERSON><PERSON><PERSON> della Scala\"><PERSON><PERSON><PERSON> Scala</a>, Lord of Verona (d. 1375)", "links": [{"title": "Cansignorio della Scala", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1451", "text": "<PERSON>, 2nd Earl of Pembroke, English Earl (d. 1491)", "html": "1451 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke\" title=\"<PERSON>, 2nd Earl of Pembroke\"><PERSON>, 2nd Earl of Pembroke</a>, English Earl (d. 1491)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke\" title=\"<PERSON>, 2nd Earl of Pembroke\"><PERSON>, 2nd Earl of Pembroke</a>, English Earl (d. 1491)", "links": [{"title": "<PERSON>, 2nd Earl of Pembroke", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Pembroke"}]}, {"year": "1512", "text": "<PERSON><PERSON>, Flemish mathematician, cartographer, and philosopher (d. 1594)", "html": "1512 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish mathematician, cartographer, and philosopher (d. 1594)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish mathematician, cartographer, and philosopher (d. 1594)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gerardus_Mercator"}]}, {"year": "1523", "text": "<PERSON>, Spanish cardinal (d. 1600)", "html": "1523 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cardinal (d. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cardinal (d. 1600)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1527", "text": "<PERSON>, Duke of Mecklenburg (d. 1603)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON>, Duke of Mecklenburg</a> (d. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON>, Duke of Mecklenburg</a> (d. 1603)", "links": [{"title": "<PERSON>, Duke of Mecklenburg", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mecklenburg"}]}, {"year": "1539", "text": "<PERSON>, German theologian (d. 1604)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1604)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1563", "text": "<PERSON>, English civil servant and politician (d. 1644)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and politician (d. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, English civil servant and politician (d. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1575", "text": "<PERSON>, English minister and mathematician (d. 1660)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and mathematician (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and mathematician (d. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1585", "text": "<PERSON>, Elector of Saxony (d. 1656)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1656)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1585", "text": "<PERSON>, Landgrave of Hesse-Homburg (d. 1638)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Homburg\" title=\"<PERSON>, Landgrave of Hesse-Homburg\"><PERSON>, Landgrave of Hesse-Homburg</a> (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Homburg\" title=\"<PERSON>, Landgrave of Hesse-Homburg\"><PERSON>, Landgrave of Hesse-Homburg</a> (d. 1638)", "links": [{"title": "<PERSON>, Landgrave of Hesse-Homburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse-Homburg"}]}, {"year": "1637", "text": "<PERSON>, Dutch painter and engineer (d. 1712)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and engineer (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and engineer (d. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1658", "text": "<PERSON>, French explorer and politician, 3rd Colonial Governor of Louisiana (d. 1730)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Cadillac\"><PERSON></a>, French explorer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana\" title=\"List of colonial governors of Louisiana\">Colonial Governor of Louisiana</a> (d. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Cadillac\"><PERSON></a>, French explorer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana\" title=\"List of colonial governors of Louisiana\">Colonial Governor of Louisiana</a> (d. 1730)", "links": [{"title": "<PERSON> Cadillac", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Louisiana", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana"}]}, {"year": "1693", "text": "<PERSON>, Swiss theologian and scholar (d. 1754)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and scholar (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and scholar (d. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1696", "text": "<PERSON>, Italian painter (d. 1770)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Giovanni <PERSON>\"><PERSON></a>, Italian painter (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1703", "text": "<PERSON><PERSON>, Russian poet and playwright (d. 1768)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and playwright (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and playwright (d. 1768)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, English general and politician, Governor of Gibraltar (d. 1776)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Gibraltar\" title=\"Governor of Gibraltar\">Governor of Gibraltar</a> (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Gibraltar\" title=\"Governor of Gibraltar\">Governor of Gibraltar</a> (d. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Gibraltar", "link": "https://wikipedia.org/wiki/Governor_of_Gibraltar"}]}, {"year": "1713", "text": "<PERSON>, English archbishop (d. 1783)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (d. 1783)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frederick_<PERSON>"}]}, {"year": "1723", "text": "Princess <PERSON> of Great Britain (d. 1773)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Great_Britain\" title=\"Princess <PERSON> of Great Britain\">Princess <PERSON> of Great Britain</a> (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Great_Britain\" title=\"Princess <PERSON> of Great Britain\">Princess <PERSON> of Great Britain</a> (d. 1773)", "links": [{"title": "Princess <PERSON> of Great Britain", "link": "https://wikipedia.org/wiki/Princess_Mary_of_Great_Britain"}]}, {"year": "1733", "text": "<PERSON>, Italian-Danish dancer and choreographer (d. 1816)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Danish dancer and choreographer (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Danish dancer and choreographer (d. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1739", "text": "<PERSON>, American colonel and physician (d. 1819)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and physician (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and physician (d. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON>, Swedish botanist and biologist (d. 1810)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish botanist and biologist (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish botanist and biologist (d. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON>, English violinist and composer (d. 1829)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Shield\"><PERSON></a>, English violinist and composer (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Shield\" title=\"William Shield\"><PERSON></a>, English violinist and composer (d. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French scholar and academic (d. 1805)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_d%27Ans<PERSON>_de_Villoison\" title=\"<PERSON><PERSON><PERSON>'<PERSON>sse de Villoison\"><PERSON><PERSON><PERSON><PERSON>'<PERSON> Villoison</a>, French scholar and academic (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27Ans<PERSON>_de_Villoison\" title=\"<PERSON><PERSON><PERSON>'<PERSON>sse de Villoison\"><PERSON><PERSON><PERSON><PERSON>'<PERSON> de Villoison</a>, French scholar and academic (d. 1805)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Villoison", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_d%27Ans<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON>, Czech organist, composer, and educator (d. 1829)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/Jan_<PERSON>%C5%99titel_Kucha%C5%99\" title=\"<PERSON>\"><PERSON></a>, Czech organist, composer, and educator (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_<PERSON>%C5%99titel_Kucha%C5%99\" title=\"<PERSON>\"><PERSON></a>, Czech organist, composer, and educator (d. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_K%C5%99titel_<PERSON>cha%C5%99"}]}, {"year": "1774", "text": "<PERSON>, Danish organist and composer (d. 1842)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish organist and composer (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish organist and composer (d. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, English mathematician and statistician (d. 1865)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and statistician (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and statistician (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, Italian cardinal (d. 1841)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, French physicist, mathematician, and astronomer (d. 1872)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist, mathematician, and astronomer (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist, mathematician, and astronomer (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, American lawyer and jurist (d. 1870)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, German poet and philosopher (d. 1875)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and philosopher (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and philosopher (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, German historian and academic (d. 1889)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, American journalist and politician, 19th Mayor of Chicago (d. 1888)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Illinois_politician)\" title=\"<PERSON> (Illinois politician)\"><PERSON></a>, American journalist and politician, 19th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Illinois_politician)\" title=\"<PERSON> (Illinois politician)\"><PERSON></a>, American journalist and politician, 19th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1888)", "links": [{"title": "<PERSON> (Illinois politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Illinois_politician)"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1817", "text": "<PERSON>, English archaeologist, academic, and politician, Under-Secretary of State for Foreign Affairs (d. 1894)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist, academic, and politician, <a href=\"https://wikipedia.org/wiki/Under-Secretary_of_State_for_Foreign_Affairs\" class=\"mw-redirect\" title=\"Under-Secretary of State for Foreign Affairs\">Under-Secretary of State for Foreign Affairs</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist, academic, and politician, <a href=\"https://wikipedia.org/wiki/Under-Secretary_of_State_for_Foreign_Affairs\" class=\"mw-redirect\" title=\"Under-Secretary of State for Foreign Affairs\">Under-Secretary of State for Foreign Affairs</a> (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Under-Secretary of State for Foreign Affairs", "link": "https://wikipedia.org/wiki/Under-Secretary_of_State_for_Foreign_Affairs"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON>, French physiologist and chronophotographer (d. 1904)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physiologist and chronophotographer (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physiologist and chronophotographer (d. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne-<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Scottish historian and zoologist (d. 1882)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and zoologist (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and zoologist (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, Luxembourgian politician, 6th Prime Minister of Luxembourg (d. 1915)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Luxembourg\" class=\"mw-redirect\" title=\"List of Prime Ministers of Luxembourg\">Prime Minister of Luxembourg</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Luxembourg\" class=\"mw-redirect\" title=\"List of Prime Ministers of Luxembourg\">Prime Minister of Luxembourg</a> (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_de_<PERSON>"}, {"title": "List of Prime Ministers of Luxembourg", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Luxembourg"}]}, {"year": "1834", "text": "<PERSON><PERSON>, Italian soprano (d. 1899)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pi<PERSON>lomini\" title=\"<PERSON><PERSON> Piccolomini\"><PERSON><PERSON></a>, Italian soprano (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pi<PERSON>lomini\" title=\"<PERSON><PERSON> Piccolomini\"><PERSON><PERSON></a>, Italian soprano (d. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Pi<PERSON><PERSON>mini"}]}, {"year": "1853", "text": "<PERSON>, American author and illustrator (d. 1911)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON><PERSON><PERSON>, German chess player and theoretician (d. 1934)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German chess player and theoretician (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German chess player and theoretician (d. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 14th Premier of Quebec (d. 1952)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1869", "text": "<PERSON>, German cardinal (d. 1952)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, American journalist and author (d. 1902)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON>, French-Ukrainian engineer (d. 1953)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Ev<PERSON>_<PERSON>\" title=\"<PERSON>v<PERSON>\"><PERSON><PERSON><PERSON></a>, French-Ukrainian engineer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ev<PERSON>_<PERSON>\" title=\"<PERSON>v<PERSON>\"><PERSON><PERSON><PERSON></a>, French-Ukrainian engineer (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ev<PERSON>_<PERSON><PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Polish-Russian economist and philosopher (d. 1919)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Russian economist and philosopher (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Russian economist and philosopher (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rosa_Luxemburg"}]}, {"year": "1871", "text": "<PERSON><PERSON>, Greek general and politician, Minister Governor-General of Macedonia (d. 1941)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/Minister_Governor-General_of_Macedonia\" class=\"mw-redirect\" title=\"Minister Governor-General of Macedonia\">Minister Governor-General of Macedonia</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/Minister_Governor-General_of_Macedonia\" class=\"mw-redirect\" title=\"Minister Governor-General of Macedonia\">Minister Governor-General of Macedonia</a> (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister Governor-General of Macedonia", "link": "https://wikipedia.org/wiki/Minister_Governor-General_of_Macedonia"}]}, {"year": "1873", "text": "<PERSON><PERSON>, Norwegian skier and explorer (d. 1961)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian skier and explorer (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian skier and explorer (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, English-American actor (d. 1965)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, English-American actor (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Travers\"><PERSON></a>, English-American actor (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Australian politician, 27th Premier of Victoria (d. 1952)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1952)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1876", "text": "<PERSON>, 1st Viscount <PERSON>, English lawyer and politician, 8th Lord Chief Justice of England (d. 1947)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England\" class=\"mw-redirect\" title=\"Lord Chief Justice of England\">Lord Chief Justice of England</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England\" class=\"mw-redirect\" title=\"Lord Chief Justice of England\">Lord Chief Justice of England</a> (d. 1947)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Lord Chief Justice of England", "link": "https://wikipedia.org/wiki/Lord_Chief_Justice_of_England"}]}, {"year": "1876", "text": "<PERSON>, American tennis player (d. 1959)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, English economist and academic (d. 1963)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Estonian general and politician, 1st Estonian Minister of War (d. 1943)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian general and politician, 1st <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_War\" class=\"mw-redirect\" title=\"Estonian Minister of War\">Estonian Minister of War</a> (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian general and politician, 1st <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_War\" class=\"mw-redirect\" title=\"Estonian Minister of War\">Estonian Minister of War</a> (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Estonian Minister of War", "link": "https://wikipedia.org/wiki/Estonian_Minister_of_War"}]}, {"year": "1880", "text": "<PERSON>, Russian mathematician and academic (d. 1968)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, English author and activist (d. 1960)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American mathematician (d. 1967)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Canadian ethnographer and academic (d. 1969)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ethnographer and academic (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ethnographer and academic (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Chinese judge and politician, Chairman of the People's Republic of China (d. 1975)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese judge and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"List of Presidents of the People's Republic of China\">Chairman of the People's Republic of China</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese judge and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"List of Presidents of the People's Republic of China\">Chairman of the People's Republic of China</a> (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>wu"}, {"title": "List of Presidents of the People's Republic of China", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_People%27s_Republic_of_China"}]}, {"year": "1886", "text": "<PERSON>, Welsh boxer (d. 1927)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Freddie Welsh\"><PERSON></a>, Welsh boxer (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Freddie_<PERSON>\" title=\"Freddie Welsh\"><PERSON></a>, Welsh boxer (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Brazilian guitarist and composer (d. 1959)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>itor_Villa-Lobos\" title=\"<PERSON>itor Villa-Lobos\"><PERSON><PERSON>Lo<PERSON></a>, Brazilian guitarist and composer (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>itor_Villa-Lobos\" title=\"<PERSON>itor Villa-Lobos\"><PERSON><PERSON></a>, Brazilian guitarist and composer (d. 1959)", "links": [{"title": "<PERSON>itor <PERSON>", "link": "https://wikipedia.org/wiki/Heitor_Villa-Lobos"}]}, {"year": "1894", "text": "<PERSON>, English-American actor (d. 1963)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Chinese politician, 1st Premier of the People's Republic of China (d. 1976)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Premier of the People's Republic of China\">Premier of the People's Republic of China</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Premier of the People's Republic of China\">Premier of the People's Republic of China</a> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of the People's Republic of China", "link": "https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, Japanese super-centenarian (d. 2015)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese super-centenarian (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese super-centenarian (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Jewish German doctor (d. 1944)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jewish German doctor (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jewish German doctor (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German guard and supervisor of three Nazi concentration camps (d. 1974)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German guard and supervisor of three Nazi concentration camps (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German guard and supervisor of three Nazi concentration camps (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Prince of Schwarzburg (d. 1971)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_Prince_of_Schwarzburg\" title=\"<PERSON>, Prince of Schwarzburg\"><PERSON>, Prince of Schwarzburg</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_Prince_of_Schwarzburg\" title=\"<PERSON>, Prince of Schwarzburg\"><PERSON>, Prince of Schwarzburg</a> (d. 1971)", "links": [{"title": "<PERSON>, Prince of Schwarzburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_Prince_of_Schwarzburg"}]}, {"year": "1901", "text": "<PERSON>, Polish poet, essayist and translator (d. 1970)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9B\" title=\"<PERSON>\"><PERSON></a>, Polish poet, essayist and translator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9B\" title=\"<PERSON>\"><PERSON></a>, Polish poet, essayist and translator (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julian_Przybo%C5%9B"}]}, {"year": "1904", "text": "<PERSON>, German priest and theologian (d. 1984)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and theologian (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and theologian (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian-American director and cinematographer (d. 1992)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-American director and cinematographer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-American director and cinematographer (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_<PERSON><PERSON><PERSON>"}]}, {"year": "1908", "text": "<PERSON>, German historian and author (d. 1999)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, German historian and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, German historian and author (d. 1999)", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(historian)"}]}, {"year": "1908", "text": "<PERSON>, American author and playwright (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, English actor (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Taiwanese-Japanese businessman, founded Nissin Foods (d. 2007)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ando\" title=\"Momof<PERSON> Ando\"><PERSON><PERSON><PERSON></a>, Taiwanese-Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/Nissin_Foods\" title=\"Nissin Foods\">Nissin Foods</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ando\" title=\"Momof<PERSON> Ando\"><PERSON><PERSON><PERSON></a>, Taiwanese-Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/Nissin_Foods\" title=\"Nissin Foods\">Nissin Foods</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>of<PERSON>_<PERSON>o"}, {"title": "Nissin Foods", "link": "https://wikipedia.org/wiki/<PERSON>ssin_Foods"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Italian author, screenwriter, and critic (d. 1972)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian author, screenwriter, and critic (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian author, screenwriter, and critic (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Indian Air Marshall, Father of the Indian Air Force (d. 1960)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>rot<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian Air Marshall, Father of the <a href=\"https://wikipedia.org/wiki/Indian_Air_Force\" title=\"Indian Air Force\">Indian Air Force</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian Air Marshall, Father of the <a href=\"https://wikipedia.org/wiki/Indian_Air_Force\" title=\"Indian Air Force\">Indian Air Force</a> (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Subrot<PERSON>_<PERSON>"}, {"title": "Indian Air Force", "link": "https://wikipedia.org/wiki/Indian_Air_Force"}]}, {"year": "1912", "text": "<PERSON>, New Zealand colonel, lawyer, and politician, 28th Prime Minister of New Zealand (d. 1988)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand colonel, lawyer, and politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand colonel, lawyer, and politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1915", "text": "<PERSON>, Canadian academic and politician, 16th Premier of Nova Scotia (d. 1990)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Nova_Scotia_politician)\" title=\"<PERSON> (Nova Scotia politician)\"><PERSON></a>, Canadian academic and politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Nova_Scotia_politician)\" title=\"<PERSON> (Nova Scotia politician)\"><PERSON></a>, Canadian academic and politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (d. 1990)", "links": [{"title": "<PERSON> (Nova Scotia politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Nova_Scotia_politician)"}, {"title": "Premier of Nova Scotia", "link": "https://wikipedia.org/wiki/Premier_of_Nova_Scotia"}]}, {"year": "1915", "text": "<PERSON>, French mathematician and academic (d. 2002)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American attorney and politician, 39th Governor of Pennsylvania (d. 2006)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Pennsylvania", "link": "https://wikipedia.org/wiki/Governor_of_Pennsylvania"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Canadian ice hockey player, coach, and manager (d. 2017)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Canadian football player, referee, and sportscaster (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Red_Storey\" title=\"<PERSON> Storey\"><PERSON></a>, Canadian football player, referee, and sportscaster (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Storey\" title=\"Red Storey\"><PERSON></a>, Canadian football player, referee, and sportscaster (d. 2006)", "links": [{"title": "Red Storey", "link": "https://wikipedia.org/wiki/Red_Storey"}]}, {"year": "1918", "text": "<PERSON>, American economist and academic (d. 2002)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Algerian surgeon and activist (d. 2009)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian surgeon and activist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian surgeon and activist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>ker"}]}, {"year": "1920", "text": "<PERSON>, American actress (d. 1996)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Virginia_Christine\" title=\"<PERSON> Christine\"><PERSON></a>, American actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Christine\" title=\"<PERSON> Christine\"><PERSON></a>, American actress (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virginia_Christine"}]}, {"year": "1920", "text": "<PERSON>, English actress (d. 2001)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Chinese writer (d. 1997)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese writer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese writer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American physicist and electrical engineer (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and electrical engineer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and electrical engineer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American baseball player and coach (d. 1998)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actor (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1922", "text": "<PERSON>, Italian actor, director, and screenwriter (d. 1975)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Puerto Rican biologist and academic (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Juan A<PERSON>\"><PERSON></a>, Puerto Rican biologist and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Juan A<PERSON>\"><PERSON></a>, Puerto Rican biologist and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American businessman, co-founded the Loews Corporation (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Loews_Corporation\" title=\"Loews Corporation\">Loews Corporation</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Loews_Corporation\" title=\"Loews Corporation\">Loews Corporation</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Loews Corporation", "link": "https://wikipedia.org/wiki/Loews_Corporation"}]}, {"year": "1924", "text": "<PERSON>, French footballer (d. 1997)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor and singer (d. 1976)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, 29th Earl of Crawford, Scottish businessman and politician (d. 2023)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_29th_Earl_of_Crawford\" title=\"<PERSON>, 29th Earl of Crawford\"><PERSON>, 29th Earl of Crawford</a>, Scottish businessman and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_29th_Earl_<PERSON>_Crawford\" title=\"<PERSON>, 29th Earl of Crawford\"><PERSON>, 29th Earl of Crawford</a>, Scottish businessman and politician (d. 2023)", "links": [{"title": "<PERSON>, 29th Earl of Crawford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_29th_Earl_of_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American academic and critic (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and critic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and critic (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Swedish race car driver (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish race car driver (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish race car driver (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON> <PERSON><PERSON>, American singer-songwriter and guitarist (d. 1967)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. B. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1967)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Canadian ice hockey player and referee (d. 2008)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and referee (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and referee (d. 2008)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1930", "text": "<PERSON>, American baseball player and manager (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Del_<PERSON>all"}]}, {"year": "1931", "text": "<PERSON>, French author and illustrator (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, French author and illustrator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, French author and illustrator (d. 2013)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cartoonist)"}]}, {"year": "1931", "text": "<PERSON>, Australian horn player and educator (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian horn player and educator (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian horn player and educator (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actor", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, German cardinal and theologian", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal and theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal and theologian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Israeli-American economist and psychologist, Nobel Prize laureate (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American economist and psychologist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American economist and psychologist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1934", "text": "<PERSON>, American actor (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Italian photographer and journalist (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Letizia_Battaglia\" title=\"Letizia Battaglia\">Let<PERSON><PERSON></a>, Italian photographer and journalist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Letizia_Battaglia\" title=\"Letizia Battaglia\"><PERSON><PERSON><PERSON></a>, Italian photographer and journalist (d. 2022)", "links": [{"title": "Letizia Battaglia", "link": "https://wikipedia.org/wiki/Letizia_Battaglia"}]}, {"year": "1935", "text": "<PERSON>, Australian-American astronaut and engineer (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American astronaut and engineer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American astronaut and engineer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi Islamic scholar and politician (d. 1996)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi Islamic scholar and politician (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi Islamic scholar and politician (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Zimbabwean minister and politician, 1st President of Zimbabwe (d. 2003)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Canaan_Banana\" title=\"Canaan Banana\"><PERSON><PERSON></a>, Zimbabwean minister and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Zimbabwe\" title=\"President of Zimbabwe\">President of Zimbabwe</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canaan_Banana\" title=\"Canaan Banana\"><PERSON><PERSON></a>, Zimbabwean minister and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Zimbabwe\" title=\"President of Zimbabwe\">President of Zimbabwe</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Canaan_Banana"}, {"title": "President of Zimbabwe", "link": "https://wikipedia.org/wiki/President_of_Zimbabwe"}]}, {"year": "1936", "text": "<PERSON>, American golfer (d. 2022)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian general and politician, 5th President of Nigeria", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>eg<PERSON>_Obasanjo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian general and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President of Nigeria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>eg<PERSON>_O<PERSON>anjo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian general and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President of Nigeria</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>eg<PERSON>_O<PERSON>anjo"}, {"title": "President of Nigeria", "link": "https://wikipedia.org/wiki/President_of_Nigeria"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1938", "text": "<PERSON>, American biologist and academic (d. 2011)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American football player, actor, director, producer, and screenwriter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English actress", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Australian politician, 40th Premier of Tasmania", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 40th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 40th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Indonesian actor and comedian (d. 1995)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian actor and comedian (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian actor and comedian (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Canadian serial killer (d. 2010)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian serial killer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian serial killer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Belgian chef", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian chef", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian chef", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English bishop", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1940", "text": "<PERSON>, New Zealand race car driver (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand race car driver (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand race car driver (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, New Zealand-English businessman and activist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English businessman and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English businessman and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Spanish lawyer and politician, Prime Minister of Spain", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Felipe_Gonz%C3%A1lez"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "1942", "text": "<PERSON>, American author and editor (d. 2020)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and editor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and editor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Italian singer-songwriter and guitarist (d. 1998)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and guitarist (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1944", "text": "<PERSON>, Danish painter and sculptor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American journalist and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Wilf_Tranter\" title=\"Wilf Tranter\">Wil<PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilf_Tranter\" title=\"Wilf Tranter\">Wilf <PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilf_T<PERSON>er"}]}, {"year": "1946", "text": "<PERSON>, Canadian pianist (d. 2007)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_musician)\" class=\"mw-redirect\" title=\"<PERSON> (Canadian musician)\"><PERSON></a>, Canadian pianist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_musician)\" class=\"mw-redirect\" title=\"<PERSON> (Canadian musician)\"><PERSON></a>, Canadian pianist (d. 2007)", "links": [{"title": "<PERSON> (Canadian musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_musician)"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Italian illustrator and painter (d. 2018)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian illustrator and painter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian illustrator and painter (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>to"}]}, {"year": "1946", "text": "<PERSON>, English footballer and manager (d. 2016)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actor and singer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Head\" title=\"Murray Head\"><PERSON></a>, English actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murray_Head\" title=\"Murray Head\"><PERSON></a>, English actor and singer", "links": [{"title": "Murray Head", "link": "https://wikipedia.org/wiki/<PERSON>_Head"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON>, Northern Irish singer and actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Northern Irish singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Rodgers\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Northern Irish singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American baseball player and sportscaster", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Kent_Tekulve\" title=\"<PERSON> Tekulve\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Te<PERSON>\" title=\"<PERSON> Tekulve\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>ku<PERSON>", "link": "https://wikipedia.org/wiki/Kent_Tekulve"}]}, {"year": "1948", "text": "<PERSON>, Guyanese-British singer-songwriter and musician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guy<PERSON>se-British singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guy<PERSON>se-British singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English conductor and scholar (d. 2008)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and scholar (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and scholar (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English singer and actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Spanish bullfighter (d. 1984)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish bullfighter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish bullfighter (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Dutch footballer and coach (d. 2011)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, French businessman, philanthropist, and art collector", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman, philanthropist, and art collector", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman, philanthropist, and art collector", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, German lawyer and politician, German Federal Minister of Defence", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Defence_(Germany)\" title=\"Federal Ministry of Defence (Germany)\">German Federal Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Defence_(Germany)\" title=\"Federal Ministry of Defence (Germany)\">German Federal Minister of Defence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Ministry of Defence (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Defence_(Germany)"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian cricketer and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Serbian footballer and coach (d. 2010)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and coach (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and coach (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English musician and songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>(keyboardist)\" title=\"<PERSON> (keyboardist)\"><PERSON></a>, English musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(keyboardist)\" title=\"<PERSON> (keyboardist)\"><PERSON></a>, English musician and songwriter", "links": [{"title": "<PERSON> (keyboardist)", "link": "https://wikipedia.org/wiki/<PERSON>(keyboardist)"}]}, {"year": "1952", "text": "<PERSON>, American author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>bb\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>bb\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American baseball player and scout", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and scout", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Swedish poet and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish poet and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish poet and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American philosopher and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>wale, South African businessman and politician, 1st Premier of Gauteng", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Tokyo_Sexwale\" title=\"Tokyo Sexwale\">Tokyo Sexwale</a>, South African businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Gauteng\" title=\"Premier of Gauteng\">Premier of Gauteng</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokyo_Sexwale\" title=\"Tokyo Sexwale\">Tokyo Sexwale</a>, South African businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Gauteng\" title=\"Premier of Gauteng\">Premier of Gauteng</a>", "links": [{"title": "Tokyo Sexwale", "link": "https://wikipedia.org/wiki/Tokyo_Sexwale"}, {"title": "Premier of Gauteng", "link": "https://wikipedia.org/wiki/Premier_of_Gauteng"}]}, {"year": "1954", "text": "<PERSON>, Angolan politician, 3rd President of Angola", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Louren%C3%A7o\" title=\"<PERSON>\"><PERSON></a>, Angolan politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Angola\" title=\"President of Angola\">President of Angola</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Louren%C3%A7o\" title=\"<PERSON>\"><PERSON></a>, Angolan politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Angola\" title=\"President of Angola\">President of Angola</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_Louren%C3%A7o"}, {"title": "President of Angola", "link": "https://wikipedia.org/wiki/President_of_Angola"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Marsh<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marsh<PERSON>_<PERSON>\" title=\"Marsha <PERSON>field\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marsha_Warfield"}]}, {"year": "1955", "text": "<PERSON>, American magician, actor, and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Jillette\" title=\"Penn Jillette\"><PERSON></a>, American magician, actor, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Jillette\" title=\"Penn Jillette\"><PERSON></a>, American magician, actor, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ette"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Mexican actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American singer-songwriter and producer (d. 2010)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Marie\"><PERSON><PERSON></a>, American singer-songwriter and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Marie\"><PERSON><PERSON></a>, American singer-songwriter and producer (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English engineer and academic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English singer, songwriter and musician (d. 2018)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, songwriter and musician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, songwriter and musician (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American journalist and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>ody<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Forward\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Forward\" title=\"<PERSON> Forward\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English-Australian singer-songwriter and actor (d. 1988)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and actor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and actor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Balsam\" title=\"Ta<PERSON> Balsam\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Balsam\" title=\"Ta<PERSON> Balsam\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>am"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Armenian colonel and politician, 8th Prime Minister of Armenia (d. 1999)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Vazgen_Sarg<PERSON>\" title=\"<PERSON>az<PERSON> Sargsyan\"><PERSON><PERSON><PERSON></a>, Armenian colonel and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Armenia\" title=\"Prime Minister of Armenia\">Prime Minister of Armenia</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vaz<PERSON>_<PERSON>\" title=\"<PERSON>az<PERSON> Sargsyan\"><PERSON><PERSON><PERSON></a>, Armenian colonel and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Armenia\" title=\"Prime Minister of Armenia\">Prime Minister of Armenia</a> (d. 1999)", "links": [{"title": "Vazgen Sargsyan", "link": "https://wikipedia.org/wiki/Vazgen_<PERSON>"}, {"title": "Prime Minister of Armenia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Armenia"}]}, {"year": "1960", "text": "<PERSON>, Baron <PERSON>, English businessman and politician, Minister for Defence Equipment, Support and Technology", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON><PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_Equipment,_Support_and_Technology\" class=\"mw-redirect\" title=\"Minister for Defence Equipment, Support and Technology\">Minister for Defence Equipment, Support and Technology</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_Equipment,_Support_and_Technology\" class=\"mw-redirect\" title=\"Minister for Defence Equipment, Support and Technology\">Minister for Defence Equipment, Support and Technology</a>", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Minister for Defence Equipment, Support and Technology", "link": "https://wikipedia.org/wiki/Minister_for_Defence_Equipment,_Support_and_Technology"}]}, {"year": "1960", "text": "<PERSON>, American football player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American pastor, author, and television host", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor, author, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor, author, and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, French singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bertrand_Cantat"}]}, {"year": "1964", "text": "<PERSON>, American basketball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Dutch footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American basketball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1964)\" title=\"<PERSON> (basketball, born 1964)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1964)\" title=\"<PERSON> (basketball, born 1964)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball, born 1964)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1964)"}]}, {"year": "1965", "text": "<PERSON>, Portuguese footballer and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON><PERSON><PERSON>_(footballer,_born_1965)\" title=\"<PERSON> (footballer, born 1965)\"><PERSON></a>, Portuguese footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON><PERSON><PERSON>_(footballer,_born_1965)\" title=\"<PERSON> (footballer, born 1965)\"><PERSON></a>, Portuguese footballer and coach", "links": [{"title": "<PERSON> (footballer, born 1965)", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON><PERSON><PERSON>_(footballer,_born_1965)"}]}, {"year": "1966", "text": "<PERSON>-<PERSON>, South Korean mountaineer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-sun\" title=\"Oh E<PERSON>-sun\"><PERSON> <PERSON><PERSON>-<PERSON></a>, South Korean mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-sun\" title=\"<PERSON> <PERSON><PERSON>-sun\"><PERSON> <PERSON><PERSON>-sun</a>, South Korean mountaineer", "links": [{"title": "Oh <PERSON><PERSON>-sun", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-sun"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American football player, sportscaster, and actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Indian-American actor, producer, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American actor, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>vi"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Hungarian businessman and politician, 7th Prime Minister of Hungary", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1968", "text": "<PERSON>, English lawyer and politician, Secretary of State for Northern Ireland", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland\" title=\"Secretary of State for Northern Ireland\">Secretary of State for Northern Ireland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland\" title=\"Secretary of State for Northern Ireland\">Secretary of State for Northern Ireland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Northern Ireland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland"}]}, {"year": "1969", "text": "<PERSON>, English actor and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English author and playwright", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English author and playwright", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Algerian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Moussa_Sa%C3%AFb\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ussa_Sa%C3%AFb\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Moussa_Sa%C3%AFb"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Senegalese-French rapper", "html": "1969 - <a href=\"https://wikipedia.org/wiki/M.C<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"M.C<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Senegalese-French rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M.C<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Senegalese-French rapper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M.C._Solaar"}]}, {"year": "1970", "text": "<PERSON>, American basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1970)\" title=\"<PERSON> (basketball, born 1970)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1970)\" title=\"<PERSON> (basketball, born 1970)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball, born 1970)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1970)"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Japanese illustrator", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>se"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian president", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Aleksandar_Vu%C4%8Di%C4%87\" title=\"Aleksan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian president", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksandar_Vu%C4%8Di%C4%87\" title=\"Aleksan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian president", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleksandar_Vu%C4%8Di%C4%87"}]}, {"year": "1971", "text": "<PERSON>, English footballer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American baseball player and scout", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American voice actor, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Belgian cyclist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American basketball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Greek footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Anastasiou\"><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Anastasiou\"><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Anastasiou"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Canadian author (d. 2009)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian author (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Argentine footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%A1ider\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%A1ider\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Esn%C3%A1ider"}]}, {"year": "1973", "text": "<PERSON>, American baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian tennis player, coach, and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Slovenian skier", "html": "1973 - <a href=\"https://wikipedia.org/wiki/%C5%A0pela_Pretnar\" title=\"Špela Pretnar\"><PERSON><PERSON><PERSON></a>, Slovenian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%A0pela_Pretnar\" title=\"Špela Pretnar\"><PERSON><PERSON><PERSON></a>, Slovenian skier", "links": [{"title": "Špela Pretnar", "link": "https://wikipedia.org/wiki/%C5%A0pela_Pretnar"}]}, {"year": "1974", "text": "<PERSON>, American actor and director", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, German footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>,  English actor, comedian, writer, and television personality", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian, writer, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian, writer, and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American model and actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American model and actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Brazilian race car driver and sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Australian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English cricketer and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English actor, producer, and screenwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian basketball player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/%C5%A0ar%C5%ABnas_Jasikevi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%A0ar%C5%ABnas_Jasikevi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%A0ar%C5%ABnas_Jasikevi%C4%8Dius"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, New Zealand rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Cuban-Italian volleyball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Taismary_Ag%C3%BCero\" title=\"Taismary Agüero\"><PERSON><PERSON><PERSON></a>, Cuban-Italian volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taismary_Ag%C3%BCero\" title=\"Taismary Agüero\"><PERSON><PERSON><PERSON></a>, Cuban-Italian volleyball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taismary_Ag%C3%BCero"}]}, {"year": "1977", "text": "<PERSON>, American ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American basketball player and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actress, singer, and dancer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Mexican footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Swedish drummer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Canadian baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/%C3%89rik_B%C3%A9dard\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89rik_B%C3%A9dard\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89rik_B%C3%A9dard"}]}, {"year": "1979", "text": "<PERSON>, English rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American businessman, co-founded Maker Studios", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Maker_Studios\" class=\"mw-redirect\" title=\"Maker Studios\">Maker Studios</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Maker_Studios\" class=\"mw-redirect\" title=\"Maker Studios\">Maker Studios</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Maker Studios", "link": "https://wikipedia.org/wiki/Maker_Studios"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Barret Jackman\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Barret Jackman\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Polish-American actress and model", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American actress and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, New Zealand rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, German footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/%C3%89dgar_Due%C3%B1as\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89dgar_Due%C3%B1as\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89dgar_Due%C3%B1as"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Serbian basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Branko_Cvetkovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_Cvetkovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Branko_Cvetkovi%C4%87"}]}, {"year": "1984", "text": "<PERSON>, French footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Scottish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (Scottish footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Japanese actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)"}]}, {"year": "1986", "text": "<PERSON>, French footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player and coach", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Egyptian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>a"}]}, {"year": "1987", "text": "<PERSON>, Russian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Serbian volleyball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Jo<PERSON>_Brako%C4%8Devi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON>_Brako%C4%8Devi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian volleyball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jovana_Brako%C4%8Devi%C4%87"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Algerian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Liassine_Cadamuro-Benta%C3%AFba\" class=\"mw-redirect\" title=\"Liassine Cadamuro-Bentaïba\">Liassine Cadamuro-Bentaïba</a>, Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liassine_Cadamuro-Benta%C3%AFba\" class=\"mw-redirect\" title=\"Liassine Cadamuro-Bentaïba\">Liassine Cadamuro-Bentaïba</a>, Algerian footballer", "links": [{"title": "Liassine Cadamuro-Bentaïba", "link": "https://wikipedia.org/wiki/Liassine_Cadamuro-Benta%C3%AFba"}]}, {"year": "1989", "text": "<PERSON>, American actor, singer, and dancer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sterling Knight\"><PERSON></a>, American actor, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sterling Knight\"><PERSON></a>, American actor, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alex_<PERSON>ies"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Argentine footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Funes_Mori\" title=\"Ramiro Funes Mori\"><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>es_<PERSON>ri\" title=\"Ramiro Funes Mori\"><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ramiro_Funes_Mori"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Russian pianist and composer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>-<PERSON><PERSON>, American businessman and fraudster", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Sam_Bankman-<PERSON>ied\" title=\"Sam Bankman-Fried\"><PERSON>-<PERSON></a>, American businessman and fraudster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sam_Bankman-<PERSON>ied\" title=\"Sam Bankman-Fried\"><PERSON>-<PERSON></a>, American businessman and fraudster", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bankman-Fried"}]}, {"year": "1993", "text": "<PERSON>, French footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/El_Hadji_Ba\" title=\"El Hadji Ba\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El_Hadji_Ba\" title=\"El Hadji Ba\"><PERSON></a>, French footballer", "links": [{"title": "El Hadji Ba", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American violinist and composer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Brazilian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1993)\" title=\"<PERSON> (footballer, born 1993)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1993)\" title=\"<PERSON> (footballer, born 1993)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born 1993)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1993)"}]}, {"year": "1993", "text": "<PERSON>, Egyptian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1993)\" title=\"<PERSON> (footballer, born 1993)\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1993)\" title=\"<PERSON> (footballer, born 1993)\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON> (footballer, born 1993)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1993)"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Russian-Australian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Australian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, South Korean singer and actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(South_Korean_singer)\" title=\"<PERSON><PERSON> (South Korean singer)\"><PERSON><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(South_Korean_singer)\" title=\"<PERSON><PERSON> (South Korean singer)\"><PERSON><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON><PERSON> (South Korean singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(South_Korean_singer)"}]}, {"year": "1996", "text": "<PERSON>, American model", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(model)\" title=\"<PERSON> (model)\"><PERSON></a>, American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(model)\" title=\"<PERSON> (model)\"><PERSON></a>, American model", "links": [{"title": "<PERSON> (model)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(model)"}]}, {"year": "1996", "text": "<PERSON>, Congolese-American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Cuban rower", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>enega\" title=\"<PERSON><PERSON> Venega\"><PERSON><PERSON></a>, Cuban rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>enega\" title=\"Mile<PERSON> Venega\"><PERSON><PERSON></a>, Cuban rower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>enega"}]}, {"year": "1998", "text": "<PERSON>, American baseball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bo_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American singer-songwriter", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Madison_Beer\" title=\"Madison Beer\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Madison_Beer\" title=\"Madison Beer\"><PERSON></a>, American singer-songwriter", "links": [{"title": "Madison Beer", "link": "https://wikipedia.org/wiki/Madison_Beer"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, South Korean singer and actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "2000", "text": "<PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English actor", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Roman_<PERSON>_<PERSON>\" title=\"Roman Griffin Davis\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_<PERSON>_<PERSON>\" title=\"Roman Griffin <PERSON>\"><PERSON></a>, English actor", "links": [{"title": "Roman <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Davis"}]}], "Deaths": [{"year": "254", "text": "<PERSON> I", "html": "254 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Lucius_I\" title=\"Pope Lucius I\">Pope Lucius I</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Lucius_I\" title=\"Pope Lucius I\">Pope Lucius I</a>", "links": [{"title": "<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_I"}]}, {"year": "824", "text": "<PERSON><PERSON><PERSON> <PERSON>, Frankish nobleman", "html": "824 - <a href=\"https://wikipedia.org/wiki/Suppo_I\" title=\"Suppo I\">Suppo I</a>, <PERSON>ish <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suppo_I\" title=\"Suppo I\">Suppo I</a>, <PERSON>ish <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a>", "links": [{"title": "Suppo I", "link": "https://wikipedia.org/wiki/Suppo_I"}, {"title": "Nobility", "link": "https://wikipedia.org/wiki/Nobility"}]}, {"year": "1239", "text": "<PERSON>, German knight", "html": "1239 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German knight", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German knight", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1410", "text": "<PERSON> of Kraków, Polish reformer (b. 1335)", "html": "1410 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Krak%C3%B3w\" title=\"<PERSON> of Kraków\"><PERSON> of Kraków</a>, Polish reformer (b. 1335)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Krak%C3%B3w\" title=\"<PERSON> of Kraków\"><PERSON> of Kraków</a>, Polish reformer (b. 1335)", "links": [{"title": "<PERSON> of Kraków", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>%C3%B3w"}]}, {"year": "1417", "text": "<PERSON>, Emperor of Trebizond (b. 1364)", "html": "1417 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Trebizond\" title=\"Manuel III of Trebizond\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Empire_of_Trebizond\" title=\"Empire of Trebizond\">Emperor of Trebizond</a> (b. 1364)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Trebizond\" title=\"Manuel III of Trebizond\"><PERSON> Komnenos</a>, <a href=\"https://wikipedia.org/wiki/Empire_of_Trebizond\" title=\"Empire of Trebizond\">Emperor of Trebizond</a> (b. 1364)", "links": [{"title": "<PERSON> of Trebizond", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Trebizond"}, {"title": "Empire of Trebizond", "link": "https://wikipedia.org/wiki/Empire_of_Trebizond"}]}, {"year": "1534", "text": "<PERSON>, Italian painter and educator (b. 1489)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>rreggio\"><PERSON></a>, Italian painter and educator (b. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Correggio\"><PERSON></a>, Italian painter and educator (b. 1489)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1539", "text": "<PERSON><PERSON>, Portuguese admiral and politician, Governor of Portuguese India (b. 1487)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_da_Cunha\" title=\"<PERSON><PERSON> Cunha\"><PERSON><PERSON> Cun<PERSON></a>, Portuguese admiral and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Portuguese_India\" title=\"List of governors of Portuguese India\">Governor of Portuguese India</a> (b. 1487)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_da_Cunha\" title=\"<PERSON><PERSON> Cunha\"><PERSON><PERSON> Cun<PERSON></a>, Portuguese admiral and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Portuguese_India\" title=\"List of governors of Portuguese India\">Governor of Portuguese India</a> (b. 1487)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "List of governors of Portuguese India", "link": "https://wikipedia.org/wiki/List_of_governors_of_Portuguese_India"}]}, {"year": "1599", "text": "<PERSON>, Italian historian and jurist (b. 1523)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian historian and jurist (b. 1523)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian historian and jurist (b. 1523)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1611", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (b. 1533)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1533)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1622", "text": "<PERSON><PERSON><PERSON><PERSON>, Duke of Parma (b. 1569)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Parma\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Duke of Parma\"><PERSON><PERSON><PERSON><PERSON>, Duke of Parma</a> (b. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Parma\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Duke of Parma\"><PERSON><PERSON><PERSON><PERSON>, Duke of Parma</a> (b. 1569)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Parma"}]}, {"year": "1695", "text": "<PERSON>, English writer and librarian (b. 1664)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English writer and librarian (b. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English writer and librarian (b. 1664)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>(writer)"}]}, {"year": "1726", "text": "<PERSON>, 1st Duke of Kingston-upon-Hull, English politician, Lord President of the Council (b. 1655)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Kingston-upon-Hull\" title=\"<PERSON>, 1st Duke of Kingston-upon-Hull\"><PERSON>, 1st Duke of Kingston-upon-Hull</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (b. 1655)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Kingston-upon-Hull\" title=\"<PERSON>, 1st Duke of Kingston-upon-Hull\"><PERSON>, 1st Duke of Kingston-upon-Hull</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (b. 1655)", "links": [{"title": "<PERSON>, 1st Duke of Kingston-upon-Hull", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Kingston-upon-Hull"}, {"title": "Lord President of the Council", "link": "https://wikipedia.org/wiki/Lord_President_of_the_Council"}]}, {"year": "1770", "text": "<PERSON><PERSON><PERSON>, American slave, sailor, and stevedore, generally regarded as the first victim of the Boston Massacre (b. 1723)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/Crispus_Attucks\" title=\"Crispus Attucks\"><PERSON><PERSON><PERSON> Attucks</a>, American slave, sailor, and stevedore, generally regarded as the first victim of the <a href=\"https://wikipedia.org/wiki/Boston_Massacre\" title=\"Boston Massacre\">Boston Massacre</a> (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crispus_Attucks\" title=\"Crispus Attucks\"><PERSON><PERSON><PERSON> Attucks</a>, American slave, sailor, and stevedore, generally regarded as the first victim of the <a href=\"https://wikipedia.org/wiki/Boston_Massacre\" title=\"Boston Massacre\">Boston Massacre</a> (b. 1723)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Crispus_Attucks"}, {"title": "Boston Massacre", "link": "https://wikipedia.org/wiki/Boston_Massacre"}]}, {"year": "1778", "text": "<PERSON>, English composer and educator (b. 1710)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, German physician and astrologist (b. 1734)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and astrologist (b. 1734)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and astrologist (b. 1734)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON><PERSON><PERSON>, French mathematician and astronomer (b. 1749)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and astronomer (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and astronomer (b. 1749)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1827", "text": "<PERSON>, Italian physicist and academic (b. 1745)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (b. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (b. 1745)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, English sailor and mutineer (b. 1766)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mutineer)\" title=\"<PERSON> (mutineer)\"><PERSON></a>, English sailor and mutineer (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mutineer)\" title=\"<PERSON> (mutineer)\"><PERSON></a>, English sailor and mutineer (b. 1766)", "links": [{"title": "<PERSON> (mutineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mutineer)"}]}, {"year": "1849", "text": "<PERSON>, Scottish historical painter (b. 1806)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Scottish historical painter (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Scottish historical painter (b. 1806)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(painter)"}]}, {"year": "1876", "text": "<PERSON>, German-French historian and author (b. 1805)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Agoult\" title=\"<PERSON>\"><PERSON></a>, German-French historian and author (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ago<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French historian and author (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Agoult"}]}, {"year": "1889", "text": "<PERSON>, American writer, editor and translator (b. 1831)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, editor and translator (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, editor and translator (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, French historian and critic (b. 1828)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Hippolyte_Taine\" title=\"Hippolyte Taine\"><PERSON><PERSON><PERSON></a>, French historian and critic (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hippolyte_Taine\" title=\"Hippolyte Taine\"><PERSON><PERSON><PERSON></a>, French historian and critic (b. 1828)", "links": [{"title": "Hippol<PERSON>", "link": "https://wikipedia.org/wiki/Hippolyte_Taine"}]}, {"year": "1895", "text": "<PERSON>, Russian author, playwright, and journalist (b. 1831)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author, playwright, and journalist (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author, playwright, and journalist (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "Sir <PERSON>, 1st Baronet, English general and scholar (b. 1810)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English general and scholar (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English general and scholar (b. 1810)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet"}]}, {"year": "1907", "text": "<PERSON>, German philologist, scholar, and academic (b. 1843)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist, scholar, and academic (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, German philologist, scholar, and academic (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Danish mathematician and engineer (b. 1859)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Danish mathematician and engineer (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Danish mathematician and engineer (b. 1859)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>(mathematician)"}]}, {"year": "1927", "text": "<PERSON>, Polish-Austrian mathematician and academic (b. 1840)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Austrian mathematician and academic (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Austrian mathematician and academic (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Scottish-American businessman, founded B<PERSON><PERSON> (b. 1854)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>uick\" title=\"<PERSON> Buick\"><PERSON></a>, Scottish-American businessman, founded <a href=\"https://wikipedia.org/wiki/Buick\" title=\"Buick\">Buick</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Buick\" title=\"<PERSON> Buick\"><PERSON></a>, Scottish-American businessman, founded <a href=\"https://wikipedia.org/wiki/Buick\" title=\"Buick\">Buick</a> (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ck"}, {"title": "Buick", "link": "https://wikipedia.org/wiki/Buick"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Turkish academic and politician, 6th Turkish Minister of National Education (b. 1893)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Re%C5%9Fit_Galip\" title=\"<PERSON><PERSON><PERSON> Galip\"><PERSON><PERSON><PERSON></a>, Turkish academic and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_National_Education_of_Turkey\" class=\"mw-redirect\" title=\"List of Ministers of National Education of Turkey\">Turkish Minister of National Education</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Re%C5%9Fit_Galip\" title=\"<PERSON><PERSON><PERSON> Galip\"><PERSON><PERSON><PERSON></a>, Turkish academic and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_National_Education_of_Turkey\" class=\"mw-redirect\" title=\"List of Ministers of National Education of Turkey\">Turkish Minister of National Education</a> (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Re%C5%9Fit_Galip"}, {"title": "List of Ministers of National Education of Turkey", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_National_Education_of_Turkey"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Spanish priest and engineer (b. 1877)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Roque_Rua%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish priest and engineer (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roque_<PERSON>ua%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish priest and engineer (b. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roque_Rua%C3%B1o"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Chinese philosopher and academic (b. 1868)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Cai_<PERSON>i\" title=\"C<PERSON>\"><PERSON><PERSON></a>, Chinese philosopher and academic (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cai_Yuan<PERSON>i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese philosopher and academic (b. 1868)", "links": [{"title": "Cai <PERSON>", "link": "https://wikipedia.org/wiki/Cai_Yuanpei"}]}, {"year": "1942", "text": "<PERSON>, executed Irish Republican (b. 1904)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George Plant\"><PERSON></a>, executed <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Plant\"><PERSON></a>, executed <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a> (b. 1904)", "links": [{"title": "George <PERSON>", "link": "https://wikipedia.org/wiki/George_Plant"}, {"title": "Irish Republican", "link": "https://wikipedia.org/wiki/Irish_Republican"}]}, {"year": "1944", "text": "<PERSON>, French poet and author (b. 1876)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, African American held captive post slavery-era (b. 1900)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> held captive post slavery-era (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> held captive post slavery-era (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}]}, {"year": "1947", "text": "<PERSON>, Italian pianist, composer, and conductor (b. 1883)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist, composer, and conductor (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist, composer, and conductor (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American poet, author, and playwright (b. 1868)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, author, and playwright (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, author, and playwright (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Ukrainian general and politician (b. 1907)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian general and politician (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian general and politician (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American screenwriter and producer (b. 1897)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Russian pianist, composer, and conductor (b. 1891)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Soviet dictator and politician of Georgian descent, 2nd leader of the Soviet Union (b. 1878)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet dictator and politician of <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgian descent</a>, 2nd <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_the_Soviet_Union\" title=\"List of leaders of the Soviet Union\">leader of the Soviet Union</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet dictator and politician of <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgian descent</a>, 2nd <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_the_Soviet_Union\" title=\"List of leaders of the Soviet Union\">leader of the Soviet Union</a> (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}, {"title": "List of leaders of the Soviet Union", "link": "https://wikipedia.org/wiki/List_of_leaders_of_the_Soviet_Union"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Lithuanian lawyer and politician, 14th Prime Minister of Lithuania (b. 1888)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lithuania\" title=\"Prime Minister of Lithuania\">Prime Minister of Lithuania</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lithuania\" title=\"Prime Minister of Lithuania\">Prime Minister of Lithuania</a> (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Lithuania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lithuania"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American singer-songwriter (b. 1932)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sy_Cline"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1913)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Cowboy_Copas\" title=\"Cowboy Copas\">Cowboy Copas</a>, American singer-songwriter and guitarist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cowboy_Copas\" title=\"Cowboy Copas\">Cowboy Copas</a>, American singer-songwriter and guitarist (b. 1913)", "links": [{"title": "Cowboy Copas", "link": "https://wikipedia.org/wiki/Cowboy_Copas"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1921)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Chinese general and politician, 27th Premier of the Republic of China (b. 1897)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, 27th <a href=\"https://wikipedia.org/wiki/List_of_premiers_of_the_Republic_of_China\" title=\"List of premiers of the Republic of China\">Premier of the Republic of China</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, 27th <a href=\"https://wikipedia.org/wiki/List_of_premiers_of_the_Republic_of_China\" title=\"List of premiers of the Republic of China\">Premier of the Republic of China</a> (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of premiers of the Republic of China", "link": "https://wikipedia.org/wiki/List_of_premiers_of_the_Republic_of_China"}]}, {"year": "1965", "text": "<PERSON>, American baseball player and manager (b. 1904)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Martin\"><PERSON></a>, American baseball player and manager (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pepper Martin\"><PERSON></a>, American baseball player and manager (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Ukrainian-Russian poet, author, and translator (b. 1889)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian poet, author, and translator (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian poet, author, and translator (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Russian-American actor (b. 1905)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American actor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American actor (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Iranian political scientist and politician, 60th Prime Minister of Iran (b. 1882)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian political scientist and politician, 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iran\" title=\"Prime Minister of Iran\">Prime Minister of Iran</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian political scientist and politician, 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iran\" title=\"Prime Minister of Iran\">Prime Minister of Iran</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Iran", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iran"}]}, {"year": "1967", "text": "<PERSON>, Canadian general and politician, 19th Governor General of Canada (b. 1888)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1971", "text": "<PERSON>, American journalist and author (b. 1890)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American journalist and author (b. 1918)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27B<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American journalist and author (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27B<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American journalist and author (b. 1918)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._O%27B<PERSON>_(author)"}]}, {"year": "1974", "text": "<PERSON>, Canadian colonel and politician (b. 1894)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and politician (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and politician (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor (b. 1907)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Ukrainian-American businessman (b. 1888)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>rok\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American businessman (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sol_Hurok\" title=\"Sol Hurok\"><PERSON></a>, Ukrainian-American businessman (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sol_<PERSON>rok"}]}, {"year": "1976", "text": "<PERSON>, Estonian lawyer and politician, Prime Minister of Estonia (b. 1889)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Estonia\" title=\"List of heads of government of Estonia\">Prime Minister of Estonia</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Estonia\" title=\"List of heads of government of Estonia\">Prime Minister of Estonia</a> (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of heads of government of Estonia", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Estonia"}]}, {"year": "1977", "text": "<PERSON>, Welsh race car driver (b. 1949)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh race car driver (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh race car driver (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian-American actor (b. 1912)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American songwriter and composer (b. 1896)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Yip_<PERSON>urg\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American songwriter and composer (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yi<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American songwriter and composer (b. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Harburg"}]}, {"year": "1982", "text": "<PERSON>, American actor (b. 1949)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian operatic baritone (b. 1913)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian operatic baritone (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian operatic baritone (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor (b. 1892)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Argentine comedian and actor (b. 1933)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine comedian and actor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine comedian and actor (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actor and director (b. 1915)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English singer-songwriter and musician (b. 1943)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American character actor (b. 1909)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Whit_<PERSON><PERSON>ll\" title=\"Whit <PERSON>isse<PERSON>\"><PERSON><PERSON></a>, American character actor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Whit_<PERSON><PERSON><PERSON>\" title=\"Whit <PERSON>isse<PERSON>\"><PERSON><PERSON></a>, American character actor (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Whit_<PERSON><PERSON>ll"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American writer (b. 1909)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Baker"}]}, {"year": "1997", "text": "<PERSON>, French director and screenwriter (b. 1906)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Jean_<PERSON>%C3%A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jean_<PERSON>%C3%A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ville"}]}, {"year": "1999", "text": "<PERSON>, American actor and singer (b. 1922)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, French dancer, actress and singer (b. 1963)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ferrari\" title=\"<PERSON><PERSON> Ferrari\"><PERSON><PERSON></a>, French dancer, actress and singer (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ferrari\"><PERSON><PERSON></a>, French dancer, actress and singer (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ferrari"}]}, {"year": "2005", "text": "<PERSON>, English cricketer and bishop (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and bishop (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and bishop (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, German computer scientist and author (b. 1923)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German computer scientist and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German computer scientist and author (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American director, producer, and screenwriter (b. 1938)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, British actor and writer (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor and writer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor and writer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Greek singer-songwriter (b. 1945)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Man<PERSON>_Rasoulis\" title=\"<PERSON><PERSON> Rasoulis\"><PERSON><PERSON></a>, Greek singer-songwriter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Man<PERSON>_Rasoulis\" title=\"<PERSON><PERSON> Rasoulis\"><PERSON><PERSON></a>, Greek singer-songwriter (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Man<PERSON>_Ra<PERSON>ulis"}]}, {"year": "2012", "text": "<PERSON>, New Zealand-Australian author (b. 1970)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(fiction_writer)\" title=\"<PERSON> (fiction writer)\"><PERSON></a>, New Zealand-Australian author (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(fiction_writer)\" title=\"<PERSON> (fiction writer)\"><PERSON></a>, New Zealand-Australian author (b. 1970)", "links": [{"title": "<PERSON> (fiction writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(fiction_writer)"}]}, {"year": "2012", "text": "<PERSON>, Welsh-English actor (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American sergeant (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American wrestler and manager (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Venezuelan colonel and politician, President of Venezuela (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan colonel and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Venezuela\" class=\"mw-redirect\" title=\"List of Presidents of Venezuela\">President of Venezuela</a> (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan colonel and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Venezuela\" class=\"mw-redirect\" title=\"List of Presidents of Venezuela\">President of Venezuela</a> (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ch%C3%A1vez"}, {"title": "List of Presidents of Venezuela", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Venezuela"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American biochemist and academic (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biochemist and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biochemist and academic (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actor and game show host (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Scottish economist and academic (b. 1963)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish economist and academic (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish economist and academic (b. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Spanish poet and translator (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Leopoldo_Mar%C3%ADa_Panero\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish poet and translator (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leopoldo_<PERSON>%C3%ADa_Panero\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish poet and translator (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leopoldo_Mar%C3%ADa_Panero"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American colonel, Medal of Honor recipient (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Serbian singer-songwriter and guitarist (b. 1958)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian singer-songwriter and guitarist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian singer-songwriter and guitarist (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American cardinal and former Archbishop of New York (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal and former <a href=\"https://wikipedia.org/wiki/Archbishop_of_New_York\" class=\"mw-redirect\" title=\"Archbishop of New York\">Archbishop of New York</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal and former <a href=\"https://wikipedia.org/wiki/Archbishop_of_New_York\" class=\"mw-redirect\" title=\"Archbishop of New York\">Archbishop of New York</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Archbishop of New York", "link": "https://wikipedia.org/wiki/<PERSON>_of_New_York"}]}, {"year": "2016", "text": "<PERSON>, Sudanese activist and politician (b. 1932)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Sudanese activist and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Sudanese activist and politician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American computer programmer and engineer (b. 1941)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and engineer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and engineer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American football player and coach (b. 1920)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Al_W<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Wistert\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1920)", "links": [{"title": "Al Wistert", "link": "https://wikipedia.org/wiki/Al_Wistert"}]}, {"year": "2017", "text": "<PERSON>, German opera singer (b. 1938)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German opera singer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German opera singer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}