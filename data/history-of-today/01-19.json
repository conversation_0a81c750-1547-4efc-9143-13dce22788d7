{"date": "January 19", "url": "https://wikipedia.org/wiki/January_19", "data": {"Events": [{"year": "379", "text": "Emperor <PERSON><PERSON><PERSON> elevates <PERSON><PERSON><PERSON> at Sirmium to <PERSON>, and gives him authority over all the eastern provinces of the Roman Empire.", "html": "379 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> elevates <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON>ius I\"><PERSON><PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Sirmium\" title=\"Sirmium\">Sirmium</a> to <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(honorific)\" class=\"mw-redirect\" title=\"<PERSON> (honorific)\"><PERSON></a></i>, and gives him authority over all the eastern provinces of the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> elevates <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ius_I\" title=\"<PERSON><PERSON>ius I\"><PERSON><PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Sirmium\" title=\"Sirmium\">Sirmium</a> to <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(honorific)\" class=\"mw-redirect\" title=\"<PERSON> (honorific)\"><PERSON></a></i>, and gives him authority over all the eastern provinces of the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ian"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}, {"title": "Sirmium", "link": "https://wikipedia.org/wiki/Sirmium"}, {"title": "<PERSON> (honorific)", "link": "https://wikipedia.org/wiki/<PERSON>_(honorific)"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}]}, {"year": "649", "text": "Conquest of Kucha: The forces of Kucha surrender after a forty-day siege led by Tang dynasty general <PERSON><PERSON>, establishing Tang control over the northern Tarim Basin in Xinjiang.", "html": "649 - <a href=\"https://wikipedia.org/wiki/Conquest_of_Kucha\" class=\"mw-redirect\" title=\"Conquest of Kucha\">Conquest of Kucha</a>: The forces of Kucha surrender after a forty-day <a href=\"https://wikipedia.org/wiki/Siege\" title=\"Siege\">siege</a> led by <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> general <PERSON><PERSON>, establishing Tang control over the northern <a href=\"https://wikipedia.org/wiki/Tarim_Basin\" title=\"Tarim Basin\">Tarim Basin</a> in <a href=\"https://wikipedia.org/wiki/Xinjiang\" title=\"Xinjiang\">Xinjiang</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Conquest_of_Kucha\" class=\"mw-redirect\" title=\"Conquest of Kucha\">Conquest of Kucha</a>: The forces of Kucha surrender after a forty-day <a href=\"https://wikipedia.org/wiki/Siege\" title=\"Siege\">siege</a> led by <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> general <PERSON><PERSON>, establishing Tang control over the northern <a href=\"https://wikipedia.org/wiki/Tarim_Basin\" title=\"Tarim Basin\">Tarim Basin</a> in <a href=\"https://wikipedia.org/wiki/Xinjiang\" title=\"Xinjiang\">Xinjiang</a>.", "links": [{"title": "Conquest of Kucha", "link": "https://wikipedia.org/wiki/Conquest_of_Kucha"}, {"title": "Siege", "link": "https://wikipedia.org/wiki/Siege"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}, {"title": "Tarim Basin", "link": "https://wikipedia.org/wiki/Tarim_Basin"}, {"title": "Xinjiang", "link": "https://wikipedia.org/wiki/Xinjiang"}]}, {"year": "1419", "text": "Hundred Years' War: Rouen surrenders to <PERSON> of England, completing his reconquest of Normandy.", "html": "1419 - <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/Rouen\" title=\"Rouen\">Rouen</a> surrenders to <a href=\"https://wikipedia.org/wiki/Henry_V_of_England\" title=\"Henry V of England\"><PERSON> of England</a>, completing his reconquest of <a href=\"https://wikipedia.org/wiki/Normandy\" title=\"Normandy\">Normandy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/Rouen\" title=\"Rouen\">Rouen</a> surrenders to <a href=\"https://wikipedia.org/wiki/Henry_V_of_England\" title=\"Henry V of England\"><PERSON> of England</a>, completing his reconquest of <a href=\"https://wikipedia.org/wiki/Normandy\" title=\"Normandy\">Normandy</a>.", "links": [{"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}, {"title": "Rouen", "link": "https://wikipedia.org/wiki/Rouen"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_V_of_England"}, {"title": "Normandy", "link": "https://wikipedia.org/wiki/Normandy"}]}, {"year": "1511", "text": "The Italian Duchy of Mirandola surrenders to the Pope.", "html": "1511 - The Italian Duchy of <a href=\"https://wikipedia.org/wiki/Siege_of_Mirandola_(1511)\" title=\"Siege of Mirandola (1511)\">Mirandola surrenders to the Pope</a>.", "no_year_html": "The Italian Duchy of <a href=\"https://wikipedia.org/wiki/Siege_of_Mirandola_(1511)\" title=\"Siege of Mirandola (1511)\">Mirandola surrenders to the Pope</a>.", "links": [{"title": "Siege of Mirandola (1511)", "link": "https://wikipedia.org/wiki/Siege_of_Mirandola_(1511)"}]}, {"year": "1520", "text": "<PERSON><PERSON> the <PERSON>, the Regent of Sweden, is mortally wounded at the Battle of Bogesund and dies on February 3.", "html": "1520 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_St<PERSON>_the_Younger\" title=\"St<PERSON> Sture the Younger\"><PERSON><PERSON> the Younger</a>, the <a href=\"https://wikipedia.org/wiki/Regent_of_Sweden\" class=\"mw-redirect\" title=\"Regent of Sweden\">Regent of Sweden</a>, is mortally wounded at the <a href=\"https://wikipedia.org/wiki/Battle_of_Bogesund\" title=\"Battle of Bogesund\">Battle of Bogesund</a> and dies on February 3.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_St<PERSON>_the_Younger\" title=\"<PERSON><PERSON> Sture the Younger\"><PERSON><PERSON> the Younger</a>, the <a href=\"https://wikipedia.org/wiki/Regent_of_Sweden\" class=\"mw-redirect\" title=\"Regent of Sweden\">Regent of Sweden</a>, is mortally wounded at the <a href=\"https://wikipedia.org/wiki/Battle_of_Bogesund\" title=\"Battle of Bogesund\">Battle of Bogesund</a> and dies on February 3.", "links": [{"title": "<PERSON><PERSON> Sture the Younger", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_the_Younger"}, {"title": "Regent of Sweden", "link": "https://wikipedia.org/wiki/Regent_of_Sweden"}, {"title": "Battle of Bogesund", "link": "https://wikipedia.org/wiki/Battle_of_Bogesund"}]}, {"year": "1607", "text": "San Agustin Church in Manila is officially completed; it is the oldest church still standing in the Philippines.", "html": "1607 - <a href=\"https://wikipedia.org/wiki/San_Agustin_Church_(Manila)\" title=\"San Agustin Church (Manila)\">San Agustin Church in Manila</a> is officially completed; it is the oldest church still standing in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/San_Agustin_Church_(Manila)\" title=\"San Agustin Church (Manila)\">San Agustin Church in Manila</a> is officially completed; it is the oldest church still standing in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "links": [{"title": "San Agustin Church (Manila)", "link": "https://wikipedia.org/wiki/San_Agustin_Church_(Manila)"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1639", "text": "Hämeenlinna (Swedish: Tavastehus) is granted privileges after it separated from the Vanaja parish as its own city in Tavastia.", "html": "1639 - <a href=\"https://wikipedia.org/wiki/H%C3%A4meenlinna\" title=\"Hämeenlinna\"><PERSON><PERSON><PERSON><PERSON>lin<PERSON></a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\">Tavastehus</i>) is granted <a href=\"https://wikipedia.org/wiki/Privilege_(law)\" title=\"Privilege (law)\">privileges</a> after it separated from the <a href=\"https://wikipedia.org/wiki/Vanaja_(Finland)\" title=\"Vanaja (Finland)\">Vanaja</a> parish as its own city in <a href=\"https://wikipedia.org/wiki/Tavastia_(historical_province)\" title=\"Tavastia (historical province)\">Tavastia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A4meenlinna\" title=\"Hämeenlinna\">H<PERSON><PERSON><PERSON>linna</a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\">Tavastehus</i>) is granted <a href=\"https://wikipedia.org/wiki/Privilege_(law)\" title=\"Privilege (law)\">privileges</a> after it separated from the <a href=\"https://wikipedia.org/wiki/Vanaja_(Finland)\" title=\"Vanaja (Finland)\">Vanaja</a> parish as its own city in <a href=\"https://wikipedia.org/wiki/Tavastia_(historical_province)\" title=\"Tavastia (historical province)\">Tavastia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A4meenlinna"}, {"title": "Swedish language", "link": "https://wikipedia.org/wiki/Swedish_language"}, {"title": "Privilege (law)", "link": "https://wikipedia.org/wiki/Privilege_(law)"}, {"title": "<PERSON><PERSON> (Finland)", "link": "https://wikipedia.org/wiki/Vanaja_(Finland)"}, {"title": "Tavastia (historical province)", "link": "https://wikipedia.org/wiki/Tavastia_(historical_province)"}]}, {"year": "1764", "text": "<PERSON> is expelled from the British House of Commons for seditious libel.", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is expelled from the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">British House of Commons</a> for <a href=\"https://wikipedia.org/wiki/Seditious_libel\" title=\"Seditious libel\">seditious libel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is expelled from the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">British House of Commons</a> for <a href=\"https://wikipedia.org/wiki/Seditious_libel\" title=\"Seditious libel\">seditious libel</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "House of Commons of the United Kingdom", "link": "https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom"}, {"title": "Seditious libel", "link": "https://wikipedia.org/wiki/Seditious_libel"}]}, {"year": "1764", "text": "<PERSON><PERSON> records in his diary that a mail bomb, possibly the world's first, has severely injured the Danish Colonel <PERSON><PERSON>, residing at Børglum Abbey.", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>dor<PERSON>\" title=\"<PERSON><PERSON> Lu<PERSON>dorph\"><PERSON><PERSON></a> records in his diary that a <a href=\"https://wikipedia.org/wiki/Mail_bomb\" class=\"mw-redirect\" title=\"Mail bomb\">mail bomb</a>, possibly the world's first, has severely injured the Danish Colonel <PERSON>, residing at <a href=\"https://wikipedia.org/wiki/B%C3%B8rglum_Abbey\" title=\"Børglum Abbey\">Børglum Abbey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>dor<PERSON>\" title=\"<PERSON><PERSON> Lu<PERSON>dorph\"><PERSON><PERSON></a> records in his diary that a <a href=\"https://wikipedia.org/wiki/Mail_bomb\" class=\"mw-redirect\" title=\"Mail bomb\">mail bomb</a>, possibly the world's first, has severely injured the Danish Colonel <PERSON>, residing at <a href=\"https://wikipedia.org/wiki/B%C3%B8rglum_Abbey\" title=\"Børglum Abbey\">Børglum Abbey</a>.", "links": [{"title": "<PERSON><PERSON> Lu<PERSON>dor<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>dorph"}, {"title": "Mail bomb", "link": "https://wikipedia.org/wiki/Mail_bomb"}, {"title": "Børglum Abbey", "link": "https://wikipedia.org/wiki/B%C3%B8rglum_Abbey"}]}, {"year": "1788", "text": "The second group of ships of the First Fleet arrive at Botany Bay.", "html": "1788 - The second group of ships of the <a href=\"https://wikipedia.org/wiki/First_Fleet\" title=\"First Fleet\">First Fleet</a> arrive at <a href=\"https://wikipedia.org/wiki/Botany_Bay\" title=\"Botany Bay\">Botany Bay</a>.", "no_year_html": "The second group of ships of the <a href=\"https://wikipedia.org/wiki/First_Fleet\" title=\"First Fleet\">First Fleet</a> arrive at <a href=\"https://wikipedia.org/wiki/Botany_Bay\" title=\"Botany Bay\">Botany Bay</a>.", "links": [{"title": "First Fleet", "link": "https://wikipedia.org/wiki/First_Fleet"}, {"title": "Botany Bay", "link": "https://wikipedia.org/wiki/Botany_Bay"}]}, {"year": "1795", "text": "The Batavian Republic is proclaimed in the Netherlands, replacing the Dutch Republic.", "html": "1795 - The <a href=\"https://wikipedia.org/wiki/Batavian_Republic\" title=\"Batavian Republic\">Batavian Republic</a> is proclaimed in the Netherlands, replacing the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Batavian_Republic\" title=\"Batavian Republic\">Batavian Republic</a> is proclaimed in the Netherlands, replacing the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a>.", "links": [{"title": "Batavian Republic", "link": "https://wikipedia.org/wiki/Batavian_Republic"}, {"title": "Dutch Republic", "link": "https://wikipedia.org/wiki/Dutch_Republic"}]}, {"year": "1817", "text": "An army of 5,423 soldiers, led by General <PERSON>, crosses the Andes from Argentina to liberate Chile and then Peru.", "html": "1817 - An army of 5,423 soldiers, led by General <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"José de <PERSON> Martín\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Crossing_of_the_Andes\" title=\"Crossing of the Andes\">crosses the Andes</a> from <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a> to liberate <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a> and then <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>.", "no_year_html": "An army of 5,423 soldiers, led by General <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"José de <PERSON> Martín\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Crossing_of_the_Andes\" title=\"Crossing of the Andes\">crosses the Andes</a> from <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a> to liberate <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a> and then <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn"}, {"title": "Crossing of the Andes", "link": "https://wikipedia.org/wiki/Crossing_of_the_Andes"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}]}, {"year": "1829", "text": "<PERSON>'s Faust: The First Part of the Tragedy receives its premiere performance.", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wolfgang <PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Faust:_The_First_Part_of_the_Tragedy\" class=\"mw-redirect\" title=\"Faust: The First Part of the Tragedy\">Faust: The First Part of the Tragedy</a></i> receives its premiere performance.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Faust:_The_First_Part_of_the_Tragedy\" class=\"mw-redirect\" title=\"Faust: The First Part of the Tragedy\">Faust: The First Part of the Tragedy</a></i> receives its premiere performance.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Faust: The First Part of the Tragedy", "link": "https://wikipedia.org/wiki/Faust:_The_First_Part_of_the_Tragedy"}]}, {"year": "1839", "text": "The British East India Company captures Aden.", "html": "1839 - The <a href=\"https://wikipedia.org/wiki/British_East_India_Company\" class=\"mw-redirect\" title=\"British East India Company\">British East India Company</a> captures <a href=\"https://wikipedia.org/wiki/Aden\" title=\"Aden\">Aden</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/British_East_India_Company\" class=\"mw-redirect\" title=\"British East India Company\">British East India Company</a> captures <a href=\"https://wikipedia.org/wiki/Aden\" title=\"Aden\">Aden</a>.", "links": [{"title": "British East India Company", "link": "https://wikipedia.org/wiki/British_East_India_Company"}, {"title": "Aden", "link": "https://wikipedia.org/wiki/Aden"}]}, {"year": "1853", "text": "<PERSON> Verdi's opera Il trovatore receives its premiere performance in Rome.", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Verdi\" title=\"Giuseppe Verdi\"><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/Il_trovatore\" title=\"Il trovatore\">Il trovatore</a></i> receives its premiere performance in Rome.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Verdi\" title=\"Giuseppe Verdi\"><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/Il_trovatore\" title=\"Il trovatore\">Il trovatore</a></i> receives its premiere performance in Rome.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Il trovatore", "link": "https://wikipedia.org/wiki/Il_trovatore"}]}, {"year": "1861", "text": "American Civil War: Georgia joins South Carolina, Florida, Mississippi, and Alabama in declaring secession from the United States.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a> joins <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>, and <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> in declaring <a href=\"https://wikipedia.org/wiki/Secession\" title=\"Secession\">secession</a> from the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a> joins <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>, and <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> in declaring <a href=\"https://wikipedia.org/wiki/Secession\" title=\"Secession\">secession</a> from the United States.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Georgia (U.S. state)", "link": "https://wikipedia.org/wiki/Georgia_(U.S._state)"}, {"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}, {"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "Secession", "link": "https://wikipedia.org/wiki/Secession"}]}, {"year": "1862", "text": "American Civil War: Battle of Mill Springs: The Confederacy suffers its first significant defeat in the conflict.", "html": "1862 - American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Mill_Springs\" title=\"Battle of Mill Springs\">Battle of Mill Springs</a>: The <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederacy</a> suffers its first significant defeat in the conflict.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Mill_Springs\" title=\"Battle of Mill Springs\">Battle of Mill Springs</a>: The <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederacy</a> suffers its first significant defeat in the conflict.", "links": [{"title": "Battle of Mill Springs", "link": "https://wikipedia.org/wiki/Battle_of_Mill_Springs"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1871", "text": "Franco-Prussian War: In the Siege of Paris, Prussia wins the Battle of St. Quentin. Meanwhile, the French attempt to break the siege in the Battle of Buzenval will end unsuccessfully the following day.", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: In the <a href=\"https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)\" class=\"mw-redirect\" title=\"Siege of Paris (1870-71)\">Siege of Paris</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussia</a> wins the <a href=\"https://wikipedia.org/wiki/Battle_of_St._Quentin_(1871)\" title=\"Battle of St. Quentin (1871)\">Battle of St. Quentin</a>. Meanwhile, the French attempt to break the siege in the <a href=\"https://wikipedia.org/wiki/Battle_of_Buzenval_(1871)\" title=\"Battle of Buzenval (1871)\">Battle of Buzenval</a> will end unsuccessfully the following day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: In the <a href=\"https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)\" class=\"mw-redirect\" title=\"Siege of Paris (1870-71)\">Siege of Paris</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussia</a> wins the <a href=\"https://wikipedia.org/wiki/Battle_of_St._Quentin_(1871)\" title=\"Battle of St. Quentin (1871)\">Battle of St. Quentin</a>. Meanwhile, the French attempt to break the siege in the <a href=\"https://wikipedia.org/wiki/Battle_of_Buzenval_(1871)\" title=\"Battle of Buzenval (1871)\">Battle of Buzenval</a> will end unsuccessfully the following day.", "links": [{"title": "Franco-Prussian War", "link": "https://wikipedia.org/wiki/Franco-Prussian_War"}, {"title": "Siege of Paris (1870-71)", "link": "https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)"}, {"title": "Kingdom of Prussia", "link": "https://wikipedia.org/wiki/Kingdom_of_Prussia"}, {"title": "Battle of St. Quentin (1871)", "link": "https://wikipedia.org/wiki/Battle_of_St._<PERSON>_(1871)"}, {"title": "Battle of Buzenval (1871)", "link": "https://wikipedia.org/wiki/Battle_of_Buzenval_(1871)"}]}, {"year": "1883", "text": "The first electric lighting system employing overhead wires, built by Thomas Edison, begins service at Roselle, New Jersey.", "html": "1883 - The first electric lighting system employing overhead wires, built by <a href=\"https://wikipedia.org/wiki/Thomas_Edison\" title=\"Thomas Edison\"><PERSON></a>, begins service at <a href=\"https://wikipedia.org/wiki/Rose<PERSON>,_New_Jersey\" title=\"Roselle, New Jersey\">Roselle, New Jersey</a>.", "no_year_html": "The first electric lighting system employing overhead wires, built by <a href=\"https://wikipedia.org/wiki/Thomas_Edison\" title=\"Thomas Edison\"><PERSON></a>, begins service at <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_New_Jersey\" title=\"Roselle, New Jersey\">Roselle, New Jersey</a>.", "links": [{"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Roselle, New Jersey", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_New_Jersey"}]}, {"year": "1899", "text": "Anglo-Egyptian Sudan is formed.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Anglo-Egyptian_Sudan\" title=\"Anglo-Egyptian Sudan\">Anglo-Egyptian Sudan</a> is formed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Egyptian_Sudan\" title=\"Anglo-Egyptian Sudan\">Anglo-Egyptian Sudan</a> is formed.", "links": [{"title": "Anglo-Egyptian Sudan", "link": "https://wikipedia.org/wiki/Anglo-Egyptian_Sudan"}]}, {"year": "1901", "text": "Queen <PERSON>, Queen of the United Kingdom, stricken with paralysis. She dies three days later at the age of 81.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a>, Queen of the United Kingdom, stricken with paralysis. She dies three days later at the age of 81.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a>, Queen of the United Kingdom, stricken with paralysis. She dies three days later at the age of 81.", "links": [{"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}]}, {"year": "1915", "text": "<PERSON> patents the neon discharge tube for use in advertising.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> patents the <a href=\"https://wikipedia.org/wiki/Neon_discharge_tube\" class=\"mw-redirect\" title=\"Neon discharge tube\">neon discharge tube</a> for use in advertising.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> patents the <a href=\"https://wikipedia.org/wiki/Neon_discharge_tube\" class=\"mw-redirect\" title=\"Neon discharge tube\">neon discharge tube</a> for use in advertising.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Neon discharge tube", "link": "https://wikipedia.org/wiki/Neon_discharge_tube"}]}, {"year": "1915", "text": "German strategic bombing during World War I: German zeppelins bomb the towns of Great Yarmouth and King's Lynn in the United Kingdom killing at least 20 people, in the first major aerial bombardment of a civilian target.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/German_strategic_bombing_during_World_War_I\" class=\"mw-redirect\" title=\"German strategic bombing during World War I\">German strategic bombing during World War I</a>: German zeppelins bomb the towns of <a href=\"https://wikipedia.org/wiki/Great_Yarmouth\" title=\"Great Yarmouth\">Great Yarmouth</a> and <a href=\"https://wikipedia.org/wiki/King%27s_Lynn\" title=\"King's Lynn\">King's Lynn</a> in the United Kingdom killing at least 20 people, in the first major aerial bombardment of a civilian target.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/German_strategic_bombing_during_World_War_I\" class=\"mw-redirect\" title=\"German strategic bombing during World War I\">German strategic bombing during World War I</a>: German zeppelins bomb the towns of <a href=\"https://wikipedia.org/wiki/Great_Yarmouth\" title=\"Great Yarmouth\">Great Yarmouth</a> and <a href=\"https://wikipedia.org/wiki/King%27s_Lynn\" title=\"King's Lynn\">King's Lynn</a> in the United Kingdom killing at least 20 people, in the first major aerial bombardment of a civilian target.", "links": [{"title": "German strategic bombing during World War I", "link": "https://wikipedia.org/wiki/German_strategic_bombing_during_World_War_I"}, {"title": "Great Yarmouth", "link": "https://wikipedia.org/wiki/Great_Yarmouth"}, {"title": "King's Lynn", "link": "https://wikipedia.org/wiki/<PERSON>%27s_<PERSON>"}]}, {"year": "1917", "text": "Silvertown explosion: A blast at a munitions factory in London kills 73 and injures over 400. The resulting fire causes over £2,000,000 worth of damage.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Silvertown_explosion\" title=\"Silvertown explosion\">Silvertown explosion</a>: A blast at a munitions factory in London kills 73 and injures over 400. The resulting fire causes over £2,000,000 worth of damage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Silvertown_explosion\" title=\"Silvertown explosion\">Silvertown explosion</a>: A blast at a munitions factory in London kills 73 and injures over 400. The resulting fire causes over £2,000,000 worth of damage.", "links": [{"title": "Silvertown explosion", "link": "https://wikipedia.org/wiki/Silvertown_explosion"}]}, {"year": "1920", "text": "The United States Senate votes against joining the League of Nations.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> votes against <a href=\"https://wikipedia.org/wiki/United_States_and_the_League_of_Nations\" title=\"United States and the League of Nations\">joining</a> the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> votes against <a href=\"https://wikipedia.org/wiki/United_States_and_the_League_of_Nations\" title=\"United States and the League of Nations\">joining</a> the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "United States and the League of Nations", "link": "https://wikipedia.org/wiki/United_States_and_the_League_of_Nations"}, {"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}]}, {"year": "1920", "text": "The American Civil Liberties Union (ACLU) is founded.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/American_Civil_Liberties_Union\" title=\"American Civil Liberties Union\">American Civil Liberties Union</a> (ACLU) is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/American_Civil_Liberties_Union\" title=\"American Civil Liberties Union\">American Civil Liberties Union</a> (ACLU) is founded.", "links": [{"title": "American Civil Liberties Union", "link": "https://wikipedia.org/wiki/American_Civil_Liberties_Union"}]}, {"year": "1937", "text": "<PERSON> sets a new air record by flying from Los Angeles to New York City in seven hours, 28 minutes, 25 seconds.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets a new <a href=\"https://wikipedia.org/wiki/Cross-America_flight_air_speed_record\" class=\"mw-redirect\" title=\"Cross-America flight air speed record\">air record</a> by flying from Los Angeles to New York City in seven hours, 28 minutes, 25 seconds.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets a new <a href=\"https://wikipedia.org/wiki/Cross-America_flight_air_speed_record\" class=\"mw-redirect\" title=\"Cross-America flight air speed record\">air record</a> by flying from Los Angeles to New York City in seven hours, 28 minutes, 25 seconds.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cross-America flight air speed record", "link": "https://wikipedia.org/wiki/Cross-America_flight_air_speed_record"}]}, {"year": "1941", "text": "World War II: HMS Greyhound and other escorts of convoy AS-12 sink Italian submarine <PERSON><PERSON><PERSON><PERSON> with all hands 64 kilometres (40 mi) northeast of Falkonera.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/HMS_Greyhound_(H05)\" title=\"HMS Greyhound (H05)\">HMS <i>Greyhound</i></a> and other escorts of convoy AS-12 sink Italian submarine <a href=\"https://wikipedia.org/wiki/Italian_submarine_Neghelli\" title=\"Italian submarine Neghelli\"><i>Neghelli</i></a> with all hands 64 kilometres (40 mi) northeast of <a href=\"https://wikipedia.org/wiki/Falkonera\" title=\"Falkonera\">Falkonera</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/HMS_Greyhound_(H05)\" title=\"HMS Greyhound (H05)\">HMS <i>Greyhound</i></a> and other escorts of convoy AS-12 sink Italian submarine <a href=\"https://wikipedia.org/wiki/Italian_submarine_Neghelli\" title=\"Italian submarine Neghelli\"><i>Neghelli</i></a> with all hands 64 kilometres (40 mi) northeast of <a href=\"https://wikipedia.org/wiki/Falkonera\" title=\"Falkonera\">Falkonera</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "HMS Greyhound (H05)", "link": "https://wikipedia.org/wiki/HMS_Greyhound_(H05)"}, {"title": "Italian submarine Neghelli", "link": "https://wikipedia.org/wiki/Italian_submarine_Neghelli"}, {"title": "Falkonera", "link": "https://wikipedia.org/wiki/Falkonera"}]}, {"year": "1942", "text": "World War II: The Japanese conquest of Burma begins.", "html": "1942 - World War II: The <a href=\"https://wikipedia.org/wiki/Japanese_conquest_of_Burma\" class=\"mw-redirect\" title=\"Japanese conquest of Burma\">Japanese conquest of Burma</a> begins.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Japanese_conquest_of_Burma\" class=\"mw-redirect\" title=\"Japanese conquest of Burma\">Japanese conquest of Burma</a> begins.", "links": [{"title": "Japanese conquest of Burma", "link": "https://wikipedia.org/wiki/Japanese_conquest_of_Burma"}]}, {"year": "1945", "text": "World War II: Soviet forces liberate the Łódź Ghetto. Of more than 200,000 inhabitants in 1940, fewer than 900 had survived the Nazi occupation.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Soviet</a> forces liberate the <a href=\"https://wikipedia.org/wiki/%C5%81%C3%B3d%C5%BA_Ghetto\" title=\"Łódź Ghetto\">Łódź Ghetto</a>. Of more than 200,000 inhabitants in 1940, fewer than 900 had survived the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> occupation.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Soviet</a> forces liberate the <a href=\"https://wikipedia.org/wiki/%C5%81%C3%B3d%C5%BA_Ghetto\" title=\"Łódź Ghetto\">Łódź Ghetto</a>. Of more than 200,000 inhabitants in 1940, fewer than 900 had survived the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> occupation.", "links": [{"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Łódź Ghetto", "link": "https://wikipedia.org/wiki/%C5%81%C3%B3d%C5%BA_Ghetto"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}]}, {"year": "1946", "text": "General <PERSON> establishes the International Military Tribunal for the Far East in Tokyo to try Japanese war criminals.", "html": "1946 - General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> establishes the <a href=\"https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East\" title=\"International Military Tribunal for the Far East\">International Military Tribunal for the Far East</a> in Tokyo to try Japanese <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war criminals</a>.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> establishes the <a href=\"https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East\" title=\"International Military Tribunal for the Far East\">International Military Tribunal for the Far East</a> in Tokyo to try Japanese <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war criminals</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "International Military Tribunal for the Far East", "link": "https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East"}, {"title": "War crime", "link": "https://wikipedia.org/wiki/War_crime"}]}, {"year": "1953", "text": "Almost 72 percent of all television sets in the United States are tuned into I Love Lucy to watch <PERSON> give birth.", "html": "1953 - Almost 72 percent of all television sets in the United States are tuned into <i><a href=\"https://wikipedia.org/wiki/I_Love_Lucy\" title=\"I Love Lucy\">I Love Lucy</a></i> to watch <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Ricardo\">Lucy</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_Goes_to_the_Hospital\" title=\"Lucy Goes to the Hospital\">give birth</a>.", "no_year_html": "Almost 72 percent of all television sets in the United States are tuned into <i><a href=\"https://wikipedia.org/wiki/I_Love_Lucy\" title=\"I Love Lucy\">I Love Lucy</a></i> to watch <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Ricardo\">Lucy</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_Goes_to_the_Hospital\" title=\"Lucy Goes to the Hospital\">give birth</a>.", "links": [{"title": "I Love Lucy", "link": "https://wikipedia.org/wiki/I_Love_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Goes to the Hospital", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_to_the_Hospital"}]}, {"year": "1960", "text": "Japan and the United States sign the US-Japan Mutual Security Treaty", "html": "1960 - Japan and the United States sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Mutual_Cooperation_and_Security_between_the_United_States_and_Japan\" title=\"Treaty of Mutual Cooperation and Security between the United States and Japan\">US-Japan Mutual Security Treaty</a>", "no_year_html": "Japan and the United States sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Mutual_Cooperation_and_Security_between_the_United_States_and_Japan\" title=\"Treaty of Mutual Cooperation and Security between the United States and Japan\">US-Japan Mutual Security Treaty</a>", "links": [{"title": "Treaty of Mutual Cooperation and Security between the United States and Japan", "link": "https://wikipedia.org/wiki/Treaty_of_Mutual_Cooperation_and_Security_between_the_United_States_and_Japan"}]}, {"year": "1960", "text": "Scandinavian Airlines System Flight 871 crashes near Ankara Esenboğa Airport in Turkey, killing all 42 aboard.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Scandinavian_Airlines_System_Flight_871\" title=\"Scandinavian Airlines System Flight 871\">Scandinavian Airlines System Flight 871</a> crashes near <a href=\"https://wikipedia.org/wiki/Ankara_Esenbo%C4%9Fa_Airport\" title=\"Ankara Esenboğa Airport\">Ankara Esenboğa Airport</a> in Turkey, killing all 42 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scandinavian_Airlines_System_Flight_871\" title=\"Scandinavian Airlines System Flight 871\">Scandinavian Airlines System Flight 871</a> crashes near <a href=\"https://wikipedia.org/wiki/Ankara_Esenbo%C4%9Fa_Airport\" title=\"Ankara Esenboğa Airport\">Ankara Esenboğa Airport</a> in Turkey, killing all 42 aboard.", "links": [{"title": "Scandinavian Airlines System Flight 871", "link": "https://wikipedia.org/wiki/Scandinavian_Airlines_System_Flight_871"}, {"title": "Ankara Esenboğa Airport", "link": "https://wikipedia.org/wiki/Ankara_Esenbo%C4%9Fa_Airport"}]}, {"year": "1969", "text": "Student <PERSON> dies after setting himself on fire three days earlier in Prague's Wenceslas Square to protest about the invasion of Czechoslovakia by the Soviet Union in 1968. His funeral turns into another major protest.", "html": "1969 - Student <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies after setting himself on fire three days earlier in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>'s <a href=\"https://wikipedia.org/wiki/Wenceslas_Square\" title=\"Wenceslas Square\">Wenceslas Square</a> to protest about the <a href=\"https://wikipedia.org/wiki/Warsaw_Pact_invasion_of_Czechoslovakia\" title=\"Warsaw Pact invasion of Czechoslovakia\">invasion</a> of <a href=\"https://wikipedia.org/wiki/Czechoslovak_Socialist_Republic\" title=\"Czechoslovak Socialist Republic\">Czechoslovakia</a> by the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> in 1968. His funeral turns into another major protest.", "no_year_html": "Student <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies after setting himself on fire three days earlier in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>'s <a href=\"https://wikipedia.org/wiki/Wenceslas_Square\" title=\"Wenceslas Square\">Wenceslas Square</a> to protest about the <a href=\"https://wikipedia.org/wiki/Warsaw_Pact_invasion_of_Czechoslovakia\" title=\"Warsaw Pact invasion of Czechoslovakia\">invasion</a> of <a href=\"https://wikipedia.org/wiki/Czechoslovak_Socialist_Republic\" title=\"Czechoslovak Socialist Republic\">Czechoslovakia</a> by the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> in 1968. His funeral turns into another major protest.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}, {"title": "Wenceslas Square", "link": "https://wikipedia.org/wiki/Wenceslas_Square"}, {"title": "Warsaw Pact invasion of Czechoslovakia", "link": "https://wikipedia.org/wiki/Warsaw_Pact_invasion_of_Czechoslovakia"}, {"title": "Czechoslovak Socialist Republic", "link": "https://wikipedia.org/wiki/Czechoslovak_Socialist_Republic"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1977", "text": "President <PERSON> pardons <PERSON><PERSON> (a.k.a. \"Tokyo Rose\").", "html": "1977 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> pardons <a href=\"https://wikipedia.org/wiki/I<PERSON>_Togu<PERSON>_D%27Aquino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (a.k.a. \"<a href=\"https://wikipedia.org/wiki/Tokyo_Rose\" title=\"Tokyo Rose\">Tokyo Rose</a>\").", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> pardons <a href=\"https://wikipedia.org/wiki/I<PERSON>_Toguri_D%27Aquino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (a.k.a. \"<a href=\"https://wikipedia.org/wiki/Tokyo_Rose\" title=\"Tokyo Rose\">Tokyo Rose</a>\").", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iva_Toguri_D%27Aquino"}, {"title": "Tokyo Rose", "link": "https://wikipedia.org/wiki/Tokyo_Rose"}]}, {"year": "1978", "text": "The last Volkswagen Beetle made in Germany leaves VW's plant in Emden. Beetle production in Latin America continues until 2003.", "html": "1978 - The last <a href=\"https://wikipedia.org/wiki/Volkswagen_Beetle\" title=\"Volkswagen Beetle\">Volkswagen Beetle</a> made in Germany leaves VW's plant in <a href=\"https://wikipedia.org/wiki/Emden\" title=\"Emden\">Emden</a>. Beetle production in Latin America continues until 2003.", "no_year_html": "The last <a href=\"https://wikipedia.org/wiki/Volkswagen_Beetle\" title=\"Volkswagen Beetle\">Volkswagen Beetle</a> made in Germany leaves VW's plant in <a href=\"https://wikipedia.org/wiki/Emden\" title=\"Emden\">Emden</a>. Beetle production in Latin America continues until 2003.", "links": [{"title": "Volkswagen Beetle", "link": "https://wikipedia.org/wiki/Volkswagen_Beetle"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emden"}]}, {"year": "1981", "text": "Iran hostage crisis: United States and Iranian officials sign an agreement to release 52 American hostages after 14 months of captivity.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>: United States and <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> officials sign an agreement to release 52 American hostages after 14 months of captivity.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>: United States and <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> officials sign an agreement to release 52 American hostages after 14 months of captivity.", "links": [{"title": "Iran hostage crisis", "link": "https://wikipedia.org/wiki/Iran_hostage_crisis"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}]}, {"year": "1988", "text": "Trans-Colorado Airlines Flight 2286 crashes in Bayfield, Colorado, killing nine.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Trans-Colorado_Airlines_Flight_2286\" title=\"Trans-Colorado Airlines Flight 2286\">Trans-Colorado Airlines Flight 2286</a> crashes in <a href=\"https://wikipedia.org/wiki/Bayfield,_Colorado\" title=\"Bayfield, Colorado\">Bayfield, Colorado</a>, killing nine.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trans-Colorado_Airlines_Flight_2286\" title=\"Trans-Colorado Airlines Flight 2286\">Trans-Colorado Airlines Flight 2286</a> crashes in <a href=\"https://wikipedia.org/wiki/Bayfield,_Colorado\" title=\"Bayfield, Colorado\">Bayfield, Colorado</a>, killing nine.", "links": [{"title": "Trans-Colorado Airlines Flight 2286", "link": "https://wikipedia.org/wiki/Trans-Colorado_Airlines_Flight_2286"}, {"title": "Bayfield, Colorado", "link": "https://wikipedia.org/wiki/Bayfield,_Colorado"}]}, {"year": "1990", "text": "Exodus of Kashmiri Pandits from the Kashmir valley in Indian-administered Kashmir due to an insurgency.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Exodus_of_Kashmiri_Pandits\" class=\"mw-redirect\" title=\"Exodus of Kashmiri Pandits\">Exodus of Kashmiri Pandits</a> from the <a href=\"https://wikipedia.org/wiki/Kashmir_valley\" class=\"mw-redirect\" title=\"Kashmir valley\">Kashmir valley</a> in <a href=\"https://wikipedia.org/wiki/Jammu_and_Kashmir_(union_territory)\" title=\"Jammu and Kashmir (union territory)\">Indian-administered Kashmir</a> due to an <a href=\"https://wikipedia.org/wiki/Insurgency_in_Jammu_and_Kashmir\" title=\"Insurgency in Jammu and Kashmir\">insurgency</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Exodus_of_Kashmiri_Pandits\" class=\"mw-redirect\" title=\"Exodus of Kashmiri Pandits\">Exodus of Kashmiri Pandits</a> from the <a href=\"https://wikipedia.org/wiki/Kashmir_valley\" class=\"mw-redirect\" title=\"Kashmir valley\">Kashmir valley</a> in <a href=\"https://wikipedia.org/wiki/Jammu_and_Kashmir_(union_territory)\" title=\"Jammu and Kashmir (union territory)\">Indian-administered Kashmir</a> due to an <a href=\"https://wikipedia.org/wiki/Insurgency_in_Jammu_and_Kashmir\" title=\"Insurgency in Jammu and Kashmir\">insurgency</a>.", "links": [{"title": "Exodus of Kashmiri Pandits", "link": "https://wikipedia.org/wiki/Exodus_of_Kashmiri_Pandits"}, {"title": "Kashmir valley", "link": "https://wikipedia.org/wiki/Kashmir_valley"}, {"title": "Jammu and Kashmir (union territory)", "link": "https://wikipedia.org/wiki/Jammu_and_Kashmir_(union_territory)"}, {"title": "Insurgency in Jammu and Kashmir", "link": "https://wikipedia.org/wiki/Insurgency_in_Jammu_and_Kashmir"}]}, {"year": "1991", "text": "Gulf War: Iraq fires a second Scud missile into Israel, causing 15 injuries.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>: <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> fires a second <a href=\"https://wikipedia.org/wiki/Scud\" class=\"mw-redirect\" title=\"Scud\">Scud</a> missile into Israel, causing 15 injuries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>: <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> fires a second <a href=\"https://wikipedia.org/wiki/Scud\" class=\"mw-redirect\" title=\"Scud\">Scud</a> missile into Israel, causing 15 injuries.", "links": [{"title": "Gulf War", "link": "https://wikipedia.org/wiki/Gulf_War"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Scud"}]}, {"year": "1993", "text": "Czech Republic and Slovakia join the United Nations.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a> and <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a> join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a> and <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a> join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Czech Republic", "link": "https://wikipedia.org/wiki/Czech_Republic"}, {"title": "Slovakia", "link": "https://wikipedia.org/wiki/Slovakia"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1995", "text": "After being struck by lightning the crew of Bristow Helicopters Flight 56C are forced to ditch. All 18 aboard are later rescued.", "html": "1995 - After being struck by lightning the crew of <a href=\"https://wikipedia.org/wiki/Bristow_Helicopters_Flight_56C\" title=\"Bristow Helicopters Flight 56C\">Bristow Helicopters Flight 56C</a> are forced to ditch. All 18 aboard are later rescued.", "no_year_html": "After being struck by lightning the crew of <a href=\"https://wikipedia.org/wiki/Bristow_Helicopters_Flight_56C\" title=\"Bristow Helicopters Flight 56C\">Bristow Helicopters Flight 56C</a> are forced to ditch. All 18 aboard are later rescued.", "links": [{"title": "Bristow Helicopters Flight 56C", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Helicopters_Flight_56C"}]}, {"year": "1996", "text": "The barge North Cape oil spill occurs as an engine fire forces the tugboat Scandia ashore on Moonstone Beach in South Kingstown, Rhode Island.", "html": "1996 - The barge <i><a href=\"https://wikipedia.org/wiki/North_Cape_(ship)\" class=\"mw-redirect\" title=\"North Cape (ship)\">North Cape</a></i> oil spill occurs as an engine fire forces the tugboat <i>Scandia</i> ashore on Moonstone Beach in <a href=\"https://wikipedia.org/wiki/South_Kingstown,_Rhode_Island\" title=\"South Kingstown, Rhode Island\">South Kingstown, Rhode Island</a>.", "no_year_html": "The barge <i><a href=\"https://wikipedia.org/wiki/North_Cape_(ship)\" class=\"mw-redirect\" title=\"North Cape (ship)\">North Cape</a></i> oil spill occurs as an engine fire forces the tugboat <i>Scandia</i> ashore on Moonstone Beach in <a href=\"https://wikipedia.org/wiki/South_Kingstown,_Rhode_Island\" title=\"South Kingstown, Rhode Island\">South Kingstown, Rhode Island</a>.", "links": [{"title": "North Cape (ship)", "link": "https://wikipedia.org/wiki/North_Cape_(ship)"}, {"title": "South Kingstown, Rhode Island", "link": "https://wikipedia.org/wiki/South_Kingstown,_Rhode_Island"}]}, {"year": "1997", "text": "<PERSON><PERSON> returns to Hebron after more than 30 years and joins celebrations over the handover of the last Israeli-controlled West Bank city.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Hebron\" title=\"Hebron\">Hebron</a> after more than 30 years and joins celebrations over the handover of the last Israeli-controlled <a href=\"https://wikipedia.org/wiki/West_Bank\" title=\"West Bank\">West Bank</a> city.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Hebron\" title=\"Hebron\">Hebron</a> after more than 30 years and joins celebrations over the handover of the last Israeli-controlled <a href=\"https://wikipedia.org/wiki/West_Bank\" title=\"West Bank\">West Bank</a> city.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Hebron", "link": "https://wikipedia.org/wiki/Hebron"}, {"title": "West Bank", "link": "https://wikipedia.org/wiki/West_Bank"}]}, {"year": "1999", "text": "British Aerospace agrees to acquire the defence subsidiary of the General Electric Company, forming BAE Systems in November 1999.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/British_Aerospace\" title=\"British Aerospace\">British Aerospace</a> agrees to acquire the defence subsidiary of the <a href=\"https://wikipedia.org/wiki/General_Electric_Company\" title=\"General Electric Company\">General Electric Company</a>, forming <a href=\"https://wikipedia.org/wiki/BAE_Systems\" title=\"BAE Systems\">BAE Systems</a> in November 1999.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_Aerospace\" title=\"British Aerospace\">British Aerospace</a> agrees to acquire the defence subsidiary of the <a href=\"https://wikipedia.org/wiki/General_Electric_Company\" title=\"General Electric Company\">General Electric Company</a>, forming <a href=\"https://wikipedia.org/wiki/BAE_Systems\" title=\"BAE Systems\">BAE Systems</a> in November 1999.", "links": [{"title": "British Aerospace", "link": "https://wikipedia.org/wiki/British_Aerospace"}, {"title": "General Electric Company", "link": "https://wikipedia.org/wiki/General_Electric_Company"}, {"title": "BAE Systems", "link": "https://wikipedia.org/wiki/BAE_Systems"}]}, {"year": "2006", "text": "A Slovak Air Force Antonov An-24 crashes near Hejce, Hungary, killing 42.", "html": "2006 - A <a href=\"https://wikipedia.org/wiki/Slovak_Air_Force\" title=\"Slovak Air Force\">Slovak Air Force</a> <a href=\"https://wikipedia.org/wiki/Antonov_An-24\" title=\"Antonov An-24\">Antonov An-24</a> <a href=\"https://wikipedia.org/wiki/2006_Slovak_Air_Force_Antonov_An-24_crash\" title=\"2006 Slovak Air Force Antonov An-24 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Hejce\" title=\"Hejce\"><PERSON><PERSON><PERSON></a>, Hungary, killing 42.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Slovak_Air_Force\" title=\"Slovak Air Force\">Slovak Air Force</a> <a href=\"https://wikipedia.org/wiki/Antonov_An-24\" title=\"Antonov An-24\">Antonov An-24</a> <a href=\"https://wikipedia.org/wiki/2006_Slovak_Air_Force_Antonov_An-24_crash\" title=\"2006 Slovak Air Force Antonov An-24 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Hejce\" title=\"Hejce\"><PERSON><PERSON><PERSON></a>, Hungary, killing 42.", "links": [{"title": "Slovak Air Force", "link": "https://wikipedia.org/wiki/Slovak_Air_Force"}, {"title": "Antonov An-24", "link": "https://wikipedia.org/wiki/Antonov_An-24"}, {"title": "2006 Slovak Air Force Antonov An-24 crash", "link": "https://wikipedia.org/wiki/2006_Slovak_Air_Force_Antonov_An-24_crash"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hejce"}]}, {"year": "2007", "text": "Turkish-Armenian journalist <PERSON><PERSON> is assassinated in front of his newspaper's Istanbul office by 17-year-old Turkish ultra-nationalist <PERSON><PERSON><PERSON><PERSON>.", "html": "2007 - Turkish-Armenian journalist <a href=\"https://wikipedia.org/wiki/Hrant_Din<PERSON>\" title=\"<PERSON><PERSON> Dink\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_Din<PERSON>\" title=\"Assassination of <PERSON><PERSON>\">assassinated</a> in front of his newspaper's Istanbul office by 17-year-old Turkish ultra-nationalist <PERSON><PERSON><PERSON><PERSON>.", "no_year_html": "Turkish-Armenian journalist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Din<PERSON>\" title=\"<PERSON><PERSON> Din<PERSON>\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON>\">assassinated</a> in front of his newspaper's Istanbul office by 17-year-old Turkish ultra-nationalist <PERSON><PERSON><PERSON><PERSON>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H<PERSON>_<PERSON>k"}, {"title": "Assassination of Hrant Dink", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>k"}]}, {"year": "2007", "text": "Four-man Team N2i, using only skis and kites, completes a 1,093-mile (1,759 km) trek to reach the Antarctic pole of inaccessibility for the first time since 1965 and for the first time ever without mechanical assistance.", "html": "2007 - Four-man <a href=\"https://wikipedia.org/wiki/Team_N2i\" class=\"mw-redirect\" title=\"Team N2i\">Team N2i</a>, using only <a href=\"https://wikipedia.org/wiki/Snowkiting\" title=\"Snowkiting\">skis and kites</a>, completes a 1,093-mile (1,759 km) trek to reach the <a href=\"https://wikipedia.org/wiki/Pole_of_Inaccessibility_(Antarctic_research_station)\" class=\"mw-redirect\" title=\"Pole of Inaccessibility (Antarctic research station)\">Antarctic pole of inaccessibility</a> for the first time since 1965 and for the first time ever without mechanical assistance.", "no_year_html": "Four-man <a href=\"https://wikipedia.org/wiki/Team_N2i\" class=\"mw-redirect\" title=\"Team N2i\">Team N2i</a>, using only <a href=\"https://wikipedia.org/wiki/Snowkiting\" title=\"Snowkiting\">skis and kites</a>, completes a 1,093-mile (1,759 km) trek to reach the <a href=\"https://wikipedia.org/wiki/Pole_of_Inaccessibility_(Antarctic_research_station)\" class=\"mw-redirect\" title=\"Pole of Inaccessibility (Antarctic research station)\">Antarctic pole of inaccessibility</a> for the first time since 1965 and for the first time ever without mechanical assistance.", "links": [{"title": "Team N2i", "link": "https://wikipedia.org/wiki/Team_N2i"}, {"title": "Snowkiting", "link": "https://wikipedia.org/wiki/Snowkiting"}, {"title": "Pole of Inaccessibility (Antarctic research station)", "link": "https://wikipedia.org/wiki/Pole_of_Inaccessibility_(Antarctic_research_station)"}]}, {"year": "2012", "text": "The Hong Kong-based file-sharing website Megaupload is shut down by the FBI.", "html": "2012 - The Hong Kong-based file-sharing website <a href=\"https://wikipedia.org/wiki/Megaupload\" title=\"Megaupload\">Megaupload</a> is shut down by the <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a>.", "no_year_html": "The Hong Kong-based file-sharing website <a href=\"https://wikipedia.org/wiki/Megaupload\" title=\"Megaupload\">Megaupload</a> is shut down by the <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a>.", "links": [{"title": "Megaupload", "link": "https://wikipedia.org/wiki/Megaupload"}, {"title": "FBI", "link": "https://wikipedia.org/wiki/FBI"}]}, {"year": "2014", "text": "A bomb attack on an army convoy in the city of Bannu kills at least 26 Pakistani soldiers and injures 38 others.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/2014_Bannu_bombing\" title=\"2014 Bannu bombing\">bomb attack</a> on an army convoy in the city of <a href=\"https://wikipedia.org/wiki/Bannu\" title=\"Bannu\"><PERSON><PERSON></a> kills at least 26 Pakistani soldiers and injures 38 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2014_Bannu_bombing\" title=\"2014 Bannu bombing\">bomb attack</a> on an army convoy in the city of <a href=\"https://wikipedia.org/wiki/Bannu\" title=\"Bannu\"><PERSON><PERSON></a> kills at least 26 Pakistani soldiers and injures 38 others.", "links": [{"title": "2014 Bannu bombing", "link": "https://wikipedia.org/wiki/2014_Bannu_bombing"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nu"}]}, {"year": "2024", "text": "The  Japan Aerospace Exploration Agency's probe landed on the moon, making Japan the 5th country to land a spacecraft on the moon.", "html": "2024 - The <a href=\"https://wikipedia.org/wiki/JAXA\" title=\"JAXA\"> Japan Aerospace Exploration Agency's</a> <a href=\"https://wikipedia.org/wiki/Smart_Lander_for_Investigating_Moon\" title=\"Smart Lander for Investigating Moon\">probe</a> landed on the moon, making Japan the 5th country to land a spacecraft on the moon.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/JAXA\" title=\"JAXA\"> Japan Aerospace Exploration Agency's</a> <a href=\"https://wikipedia.org/wiki/Smart_Lander_for_Investigating_Moon\" title=\"Smart Lander for Investigating Moon\">probe</a> landed on the moon, making Japan the 5th country to land a spacecraft on the moon.", "links": [{"title": "JAXA", "link": "https://wikipedia.org/wiki/JAXA"}, {"title": "Smart Lander for Investigating Moon", "link": "https://wikipedia.org/wiki/Smart_Lander_for_Investigating_Moon"}]}, {"year": "2025", "text": "Bytedance and sister companies were banned from the United States for \"security concerns\".", "html": "2025 - <a href=\"https://wikipedia.org/wiki/ByteDance\" title=\"ByteDance\">Bytedance</a> and sister companies were banned from the United States for \"security concerns\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/ByteDance\" title=\"ByteDance\">Bytedance</a> and sister companies were banned from the United States for \"security concerns\".", "links": [{"title": "ByteDance", "link": "https://wikipedia.org/wiki/ByteDance"}]}], "Births": [{"year": "399", "text": "<PERSON><PERSON><PERSON><PERSON>, Byzantine empress and saint (d. 453)", "html": "399 - <a href=\"https://wikipedia.org/wiki/Pulcheria\" title=\"Pulcheria\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine empress and saint (d. 453)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pulcheria\" title=\"Pulcheria\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine empress and saint (d. 453)", "links": [{"title": "P<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pulcheria"}]}, {"year": "1200", "text": "<PERSON><PERSON><PERSON>, founder of Sōtō Zen (d. 1253)", "html": "1200 - <a href=\"https://wikipedia.org/wiki/D%C5%8Dgen\" title=\"Dōgen\"><PERSON><PERSON><PERSON></a> <PERSON><PERSON>, founder of <a href=\"https://wikipedia.org/wiki/S%C5%8Dt%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <PERSON> (d. 1253)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C5%8Dgen\" title=\"Dōgen\">D<PERSON><PERSON></a> <PERSON><PERSON>, founder of <a href=\"https://wikipedia.org/wiki/S%C5%8Dt%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <PERSON> (d. 1253)", "links": [{"title": "Dōgen", "link": "https://wikipedia.org/wiki/D%C5%8Dgen"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C5%8Dt%C5%8D"}]}, {"year": "1544", "text": "<PERSON> of France (d. 1560)", "html": "1544 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (d. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (d. 1560)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1617", "text": "<PERSON>, Flemish sculptor and architect (d. 1697)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish sculptor and architect (d. 1697)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish sculptor and architect (d. 1697)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1628", "text": "<PERSON>, 8th Earl of Derby, English noble (d. 1672)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Earl_of_Derby\" title=\"<PERSON>, 8th Earl of Derby\"><PERSON>, 8th Earl of Derby</a>, English noble (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Earl_of_Derby\" title=\"<PERSON>, 8th Earl of Derby\"><PERSON>, 8th Earl of Derby</a>, English noble (d. 1672)", "links": [{"title": "<PERSON>, 8th Earl of Derby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Earl_of_Derby"}]}, {"year": "1676", "text": "<PERSON>, English organist and composer (d. 1736)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English organist and composer (d. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English organist and composer (d. 1736)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1721", "text": "<PERSON><PERSON><PERSON>, German scholar and author (d. 1740)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German scholar and author (d. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German scholar and author (d. 1740)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1736", "text": "<PERSON>, Scottish chemist and engineer (d. 1819)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and engineer (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and engineer (d. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1737", "text": "<PERSON>, Italian soprano, composer, and educator (d. 1802)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soprano, composer, and educator (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soprano, composer, and educator (d. 1802)", "links": [{"title": "Giuseppe <PERSON>", "link": "https://wikipedia.org/wiki/Giuseppe_Millico"}]}, {"year": "1739", "text": "<PERSON> the Elder, Italian architect, designed Longford Hall and Barrells Hall (d. 1808)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Italian architect, designed <a href=\"https://wikipedia.org/wiki/Longford_Hall,_Shropshire\" title=\"Longford Hall, Shropshire\">Longford Hall</a> and <a href=\"https://wikipedia.org/wiki/Barrells_Hall\" title=\"Barrells Hall\">Barrells Hall</a> (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Italian architect, designed <a href=\"https://wikipedia.org/wiki/Longford_Hall,_Shropshire\" title=\"Longford Hall, Shropshire\">Longford Hall</a> and <a href=\"https://wikipedia.org/wiki/Barrells_Hall\" title=\"Barrells Hall\">Barrells Hall</a> (d. 1808)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder"}, {"title": "Longford Hall, Shropshire", "link": "https://wikipedia.org/wiki/Longford_Hall,_Shropshire"}, {"title": "Barrells Hall", "link": "https://wikipedia.org/wiki/Barrells_Hall"}]}, {"year": "1752", "text": "<PERSON>, American captain (d. 1820)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American captain (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American captain (d. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "Countess <PERSON> of Ebersdorf (d. 1831)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_of_Ebersdorf\" title=\"Countess <PERSON> of Ebersdorf\">Countess <PERSON> of Ebersdorf</a> (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_of_Ebersdorf\" title=\"Countess <PERSON> of Ebersdorf\">Countess <PERSON> of Ebersdorf</a> (d. 1831)", "links": [{"title": "Countess <PERSON> of Ebersdorf", "link": "https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_of_Ebersdorf"}]}, {"year": "1788", "text": "<PERSON>, Russian general and politician (d. 1874)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON> <PERSON>, Swedish poet and academic (d. 1855)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/Per_<PERSON>_<PERSON>_<PERSON>\" title=\"Per <PERSON>\">Per <PERSON></a>, Swedish poet and academic (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Per_<PERSON>_<PERSON>_<PERSON>\" title=\"Per <PERSON>\">Per <PERSON></a>, Swedish poet and academic (d. 1855)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, French economist, sociologist, and philosopher (d. 1857)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist, sociologist, and philosopher (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist, sociologist, and philosopher (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Auguste_<PERSON>te"}]}, {"year": "1803", "text": "<PERSON>, American poet, essayist, and romantic interest of <PERSON> (d. 1878)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, essayist, and romantic interest of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, essayist, and romantic interest of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, American Confederate general (d. 1870)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Confederate general (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Confederate general (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON><PERSON><PERSON>, American philosopher and author (d. 1887)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>er\"><PERSON><PERSON><PERSON></a>, American philosopher and author (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American philosopher and author (d. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "1809", "text": "<PERSON>, American short story writer, poet, and critic (d. 1849)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, poet, and critic (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, poet, and critic (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Welsh poet and architect (d. 1869)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Talhaiarn\" title=\"Talhaiar<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Welsh poet and architect (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Talhaiarn\" title=\"Talhaiarn\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Welsh poet and architect (d. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>arn"}]}, {"year": "1813", "text": "<PERSON>, English engineer and businessman (d. 1898)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, Czech violinist and composer (d. 1875)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and composer (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and composer (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, German mathematician and academic (d. 1872)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, French painter (d. 1906)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_C%C3%A<PERSON><PERSON><PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Italian poet, of German ancestry (d. 1913)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet, of German ancestry (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet, of German ancestry (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Canadian businessman and politician (d. 1904)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>air<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, English swimmer and diver (d. 1883)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer and diver (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer and diver (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON>, Dutch astronomer and academic (d. 1922)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch astronomer and academic (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch astronomer and academic (d. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, Welsh-Australian politician, 24th Premier of South Australia (d. 1909)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Australian_politician)\" title=\"<PERSON> (South Australian politician)\"><PERSON></a>, Welsh-Australian politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(South_Australian_politician)\" title=\"<PERSON> (South Australian politician)\"><PERSON></a>, Welsh-Australian politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1909)", "links": [{"title": "<PERSON> (South Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Australian_politician)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1863", "text": "<PERSON>, German economist and sociologist (d. 1941)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German economist and sociologist (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German economist and sociologist (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, American stage and film actor (d. 1949)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American stage and film actor (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American stage and film actor (d. 1949)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1871", "text": "<PERSON>, Bulgarian educator and activist, co-founded the Internal Macedonian Revolutionary Organization (d. 1906)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian educator and activist, co-founded the <a href=\"https://wikipedia.org/wiki/Internal_Macedonian_Revolutionary_Organization\" title=\"Internal Macedonian Revolutionary Organization\">Internal Macedonian Revolutionary Organization</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian educator and activist, co-founded the <a href=\"https://wikipedia.org/wiki/Internal_Macedonian_Revolutionary_Organization\" title=\"Internal Macedonian Revolutionary Organization\">Internal Macedonian Revolutionary Organization</a> (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Internal Macedonian Revolutionary Organization", "link": "https://wikipedia.org/wiki/Internal_Macedonian_Revolutionary_Organization"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 19th <PERSON><PERSON><PERSON><PERSON> (d. 1922)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Hitachiyama_Taniemon\" title=\"Hitachiyama Taniemon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 19th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hitachiyama_Taniemon\" title=\"Hitachiyama Taniemon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 19th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hitachiyama_<PERSON>mon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 21st <PERSON><PERSON><PERSON><PERSON> (d. 1943)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Wakashima_Gonshir%C5%8D\" title=\"Wakashima Gonshirō\"><PERSON><PERSON><PERSON> Gonshirō</a>, Japanese sumo wrestler, the 21st <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wakashima_Gonshir%C5%8D\" title=\"Wakashima Gonshirō\"><PERSON><PERSON><PERSON> Gonshirō</a>, Japanese sumo wrestler, the 21st <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1943)", "links": [{"title": "Wakashima Gonshirō", "link": "https://wikipedia.org/wiki/Wakashima_Gonshir%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, Slovenian poet and author (d. 1899)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian poet and author (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian poet and author (d. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, English footballer and manager (d. 1934)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Russian soldier and author (d. 1925)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and author (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and author (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Australian politician, 34th Premier of Victoria (d. 1957)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(senior)\" class=\"mw-redirect\" title=\"<PERSON> (senior)\"><PERSON>.</a>, Australian politician, 34th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(senior)\" class=\"mw-redirect\" title=\"<PERSON> (senior)\"><PERSON>.</a>, Australian politician, 34th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1957)", "links": [{"title": "<PERSON> (senior)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(senior)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1883", "text": "<PERSON>, German conductor (d. 1956)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American actor, playwright, and critic (d. 1943)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, playwright, and critic (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, playwright, and critic (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Swiss painter and sculptor (d. 1943)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and sculptor (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and sculptor (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic lawyer and politician, Prime Minister of Iceland (d. 1964)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/%C3%93lafu<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93lafu<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93lafur_<PERSON>s"}, {"title": "Prime Minister of Iceland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iceland"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Brazilian pianist and educator (d. 1986)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Mag<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian pianist and educator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian pianist and educator (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Magda_Tagliaferro"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Scottish-Canadian ice hockey player and coach (d. 1958)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-Canadian ice hockey player and coach (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-Canadian ice hockey player and coach (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, German composer and playwright (d. 1975)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and playwright (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and playwright (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Norwegian sculptor and painter (d. 1980)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Dyre_Vaa\" title=\"Dyre Vaa\"><PERSON><PERSON></a>, Norwegian sculptor and painter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D<PERSON>_Vaa\" title=\"<PERSON>yre Vaa\"><PERSON><PERSON></a>, Norwegian sculptor and painter (d. 1980)", "links": [{"title": "Dyre Vaa", "link": "https://wikipedia.org/wiki/Dyre_Vaa"}]}, {"year": "1905", "text": "<PERSON>, English-Australian director and producer (d. 1991)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian director and producer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian director and producer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American comedian and cornet player (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian and cornet player (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian and cornet player (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Russian mathematician and theorist (d. 1971)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and theorist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and theorist (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Indian-Singaporean lawyer and judge (d. 2009)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Singaporean lawyer and judge (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Singaporean lawyer and judge (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, Russian mathematician and economist, Nobel Prize laureate (d. 1986)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and economist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and economist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1913", "text": "<PERSON>, Australian author and poet (d. 1955)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ingamells"}]}, {"year": "1913", "text": "<PERSON>, American professional pocket billiards player (d. 1996)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional pocket billiards player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional pocket billiards player (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American publisher, founded the Johnson Publishing Company (d. 2005)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded the <a href=\"https://wikipedia.org/wiki/Johnson_Publishing_Company\" title=\"Johnson Publishing Company\">Johnson Publishing Company</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded the <a href=\"https://wikipedia.org/wiki/Johnson_Publishing_Company\" title=\"Johnson Publishing Company\">Johnson Publishing Company</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Johnson Publishing Company", "link": "https://wikipedia.org/wiki/Johnson_Publishing_Company"}]}, {"year": "1920", "text": "<PERSON>, English painter and educator (d. 2017)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and educator (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and educator (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Peruvian politician and diplomat, 135th Prime Minister of Peru (d. 2020)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez_de_Cu%C3%A9llar\" title=\"<PERSON>\"><PERSON></a>, Peruvian politician and diplomat, 135th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Peru\" title=\"Prime Minister of Peru\">Prime Minister of Peru</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez_de_Cu%C3%A9llar\" title=\"<PERSON>\"><PERSON></a>, Peruvian politician and diplomat, 135th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Peru\" title=\"Prime Minister of Peru\">Prime Minister of Peru</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Javier_P%C3%A9rez_de_Cu%C3%A9llar"}, {"title": "Prime Minister of Peru", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Peru"}]}, {"year": "1921", "text": "<PERSON>, American novelist and short story writer (d. 1995)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>smith"}]}, {"year": "1922", "text": "<PERSON>, American actor (d. 1996)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Australian cricketer and journalist (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and journalist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and journalist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Spanish footballer and manager (d. 1990)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miguel_Mu%C3%B1oz"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Norwegian journalist (d. 2024)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1923", "text": "<PERSON>, American singer, impressionist, and voice-over actor (d. 2000)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, impressionist, and voice-over actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, impressionist, and voice-over actor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actress and singer (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor and director (d. 1985)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, French philosopher (d. 2006)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A7ois_Revel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A7ois_Revel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_Revel"}]}, {"year": "1925", "text": "<PERSON>, English author (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>den"}]}, {"year": "1926", "text": "<PERSON>, German-American journalist and author (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American journalist and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, American model, actress, and animal rights-welfare activist", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Tippi_Hedren\" title=\"Tippi Hedren\">T<PERSON><PERSON></a>, American model, actress, and animal rights-welfare activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tippi_Hedren\" title=\"Tippi <PERSON>dren\">T<PERSON><PERSON></a>, American model, actress, and animal rights-welfare activist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tip<PERSON>_Hedren"}]}, {"year": "1930", "text": "<PERSON>, South African cricketer (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer (d. 2011)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1931", "text": "<PERSON>, Canadian-American journalist and author (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American journalist and author (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American journalist and author (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English singer-songwriter (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter (d. 2008)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1932", "text": "<PERSON>, American-English director, producer, and screenwriter", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American chemist, businessman, and politician (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist, businessman, and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist, businessman, and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American priest, astronomer, and theologian (d. 2020)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, astronomer, and theologian (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, astronomer, and theologian (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actor (d. 2021)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2021)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1935", "text": "<PERSON>, Australian singer-songwriter (d. 1978)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe"}]}, {"year": "1936", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Bangladeshi general and politician, seventh President of Bangladesh (d. 1981)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi general and politician, seventh <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi general and politician, seventh <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Bangladesh", "link": "https://wikipedia.org/wiki/President_of_Bangladesh"}]}, {"year": "1936", "text": "<PERSON> \"<PERSON> Eyes\" <PERSON>, American singer, harmonica player, and drummer (d. 2011)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22B<PERSON>_<PERSON>%22_<PERSON>\" title='<PERSON> \"<PERSON> Eyes\" Smith'><PERSON> \"<PERSON> Eyes\" <PERSON></a>, American singer, harmonica player, and drummer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22B<PERSON>_<PERSON>%22_<PERSON>\" title='<PERSON> \"<PERSON> Eyes\" Smith'><PERSON> \"<PERSON> Eyes\" Smith</a>, American singer, harmonica player, and drummer (d. 2011)", "links": [{"title": "<PERSON> \"<PERSON> Eyes\" Smith", "link": "https://wikipedia.org/wiki/<PERSON>_%22B<PERSON>_<PERSON>%22_<PERSON>"}]}, {"year": "1937", "text": "Princess <PERSON><PERSON><PERSON><PERSON> of Sweden (d. 2024)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON><PERSON>_of_Sweden\" title=\"Princess <PERSON><PERSON><PERSON><PERSON> of Sweden\">Princess <PERSON><PERSON><PERSON><PERSON> of Sweden</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON><PERSON>_of_Sweden\" title=\"Princess <PERSON><PERSON><PERSON><PERSON> of Sweden\">Princess <PERSON><PERSON><PERSON><PERSON> of Sweden</a> (d. 2024)", "links": [{"title": "Princess <PERSON><PERSON><PERSON><PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON><PERSON>_of_Sweden"}]}, {"year": "1937", "text": "<PERSON>, Australian computer scientist and academic (d. 1998)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/John_Lions\" title=\"John Lions\"><PERSON></a>, Australian computer scientist and academic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Lions\" title=\"John Lions\"><PERSON></a>, Australian computer scientist and academic (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lions"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Italian lawyer and judge (d. 1992)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and judge (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and judge (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, British actor (d. 2002)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English theologian and academic (d. 2003)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and academic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Canadian wrestler, trainer, and referee (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" class=\"mw-redirect\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler, trainer, and referee (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" class=\"mw-redirect\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler, trainer, and referee (d. 2020)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1942", "text": "<PERSON>, English actor and singer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American singer-songwriter (d. 1970)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "Princess <PERSON><PERSON><PERSON><PERSON> of the Netherlands", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON><PERSON>_of_the_Netherlands\" title=\"Princess <PERSON><PERSON><PERSON><PERSON> of the Netherlands\">Princess <PERSON><PERSON><PERSON><PERSON> of the Netherlands</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>_of_the_Netherlands\" title=\"Princess <PERSON><PERSON><PERSON><PERSON> of the Netherlands\">Princess <PERSON><PERSON><PERSON><PERSON> of the Netherlands</a>", "links": [{"title": "Princess <PERSON><PERSON><PERSON><PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON><PERSON>_of_the_Netherlands"}]}, {"year": "1944", "text": "<PERSON>, American actress and singer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American architect and academic, designed the San Francisco Federal Building and Phare Tower", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and academic, designed the <a href=\"https://wikipedia.org/wiki/San_Francisco_Federal_Building\" title=\"San Francisco Federal Building\">San Francisco Federal Building</a> and <a href=\"https://wikipedia.org/wiki/Phare_Tower\" title=\"Phare Tower\">Phare Tower</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and academic, designed the <a href=\"https://wikipedia.org/wiki/San_Francisco_Federal_Building\" title=\"San Francisco Federal Building\">San Francisco Federal Building</a> and <a href=\"https://wikipedia.org/wiki/Phare_Tower\" title=\"Phare Tower\">Phare Tower</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "San Francisco Federal Building", "link": "https://wikipedia.org/wiki/San_Francisco_Federal_Building"}, {"title": "Phare Tower", "link": "https://wikipedia.org/wiki/Phare_Tower"}]}, {"year": "1944", "text": "<PERSON>, American football player and coach (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter and bass player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, English singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, English singer-songwriter and bass player", "links": [{"title": "<PERSON> (bassist)", "link": "https://wikipedia.org/wiki/<PERSON>(bassist)"}]}, {"year": "1946", "text": "<PERSON>, English novelist, short story writer, essayist, and critic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, short story writer, essayist, and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, short story writer, essayist, and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter and actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Norwegian political scientist and academic (d. 2017)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian political scientist and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian political scientist and academic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American chef and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American computer scientist and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian politician and diplomat, 27th Premier of New Brunswick", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician and diplomat, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician and diplomat, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New Brunswick", "link": "https://wikipedia.org/wiki/Premier_of_New_Brunswick"}]}, {"year": "1948", "text": "<PERSON>, English rugby league player and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Dutch voice actor and radio host (d. 2012)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch voice actor and radio host (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch voice actor and radio host (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2003)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1952", "text": "<PERSON>, English-American singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, German television actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German television actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German television actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>ka"}]}, {"year": "1952", "text": "<PERSON>, American computer scientist (d. 1999)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American actor and singer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, American actor and singer", "links": [{"title": "<PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>."}]}, {"year": "1953", "text": "<PERSON>, Canadian tennis player and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian footballer and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American actress and singer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1954", "text": "<PERSON>, American photographer and director", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Israeli poet and Mizrahi feminist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli poet and Mizrahi feminist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli poet and Mizrahi feminist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English-German orchestral conductor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-German orchestral conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-German orchestral conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American singer-songwriter, actor, and television host (d. 2021)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer-songwriter, actor, and television host (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer-songwriter, actor, and television host (d. 2021)", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1956", "text": "<PERSON>, American atmospheric chemist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American atmospheric chemist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American atmospheric chemist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, American football player and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English actor, screenwriter and film director", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, screenwriter and film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, screenwriter and film director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Puerto Rican public servant and politician, 22nd Secretary of State of Puerto Rico", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican public servant and politician, 22nd <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_Puerto_Rico\" title=\"Secretary of State of Puerto Rico\">Secretary of State of Puerto Rico</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican public servant and politician, 22nd <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_Puerto_Rico\" title=\"Secretary of State of Puerto Rico\">Secretary of State of Puerto Rico</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Secretary of State of Puerto Rico", "link": "https://wikipedia.org/wiki/Secretary_of_State_of_Puerto_Rico"}]}, {"year": "1958", "text": "<PERSON>, American painter (d. 2012)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Puerto Rican serial killer and rapist (d. 2023)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican serial killer and rapist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican serial killer and rapist (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American computer scientist and programmer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist and programmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist and programmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American bass player, songwriter, and actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor, director, and singer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English fashion designer, co-founded Red or Dead", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer, co-founded <a href=\"https://wikipedia.org/wiki/Red_or_Dead\" title=\"Red or Dead\">Red or Dead</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer, co-founded <a href=\"https://wikipedia.org/wiki/Red_or_Dead\" title=\"Red or Dead\">Red or Dead</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Red or Dead", "link": "https://wikipedia.org/wiki/Red_or_Dead"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Dutch cyclist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American baseball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American basketball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1963", "text": "<PERSON>, English journalist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English politician, Speaker of the House of Commons", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the House of Commons (United Kingdom)", "link": "https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Bahamian sculptor and photographer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian sculptor and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian sculptor and photographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Guatemalan singer-songwriter and basketball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan singer-songwriter and basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan singer-songwriter and basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Sylvain_C%C3%B4t%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sylvain_C%C3%B4t%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sylvain_C%C3%B4t%C3%A9"}]}, {"year": "1966", "text": "<PERSON>, Swedish tennis player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Swedish singer-songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian politician, 43rd Premier of Tasmania", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 43rd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 43rd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Crane\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Crane\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "W<PERSON><PERSON> Crane", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_Crane"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Haitian-American novelist and short story writer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Edwidge_Danticat\" title=\"Edwi<PERSON> Danticat\"><PERSON><PERSON><PERSON></a>, Haitian-American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edwidge_Danticat\" title=\"Edwidge Danticat\"><PERSON><PERSON><PERSON></a>, Haitian-American novelist and short story writer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edwidge_Danticat"}]}, {"year": "1969", "text": "<PERSON>, Australian basketball player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Montenegrin footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Predrag_<PERSON>i%C4%87\" title=\"Predra<PERSON>\"><PERSON>dra<PERSON></a>, Montenegrin footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Predrag_<PERSON>%C4%87\" title=\"Predra<PERSON>\">Predra<PERSON></a>, Montenegrin footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Predrag_Mijatovi%C4%87"}]}, {"year": "1969", "text": "<PERSON>, American football player (d. 2012)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Junior_Seau\" title=\"Junior Seau\"><PERSON></a>, American football player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Junior_Seau\" title=\"Junior Seau\"><PERSON></a>, American football player (d. 2012)", "links": [{"title": "Junior Seau", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>u"}]}, {"year": "1969", "text": "<PERSON>, Irish footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Steffen_Freund\" title=\"Steffen Freund\"><PERSON><PERSON><PERSON> Freund</a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Steffen_Freund\" title=\"Steffen Freund\"><PERSON><PERSON><PERSON>eund</a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Freund"}]}, {"year": "1970", "text": "<PERSON>, Belgian triathlete", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian triathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Japanese comedian and singer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Udo_Suzuki\" title=\"Udo Suzuki\"><PERSON><PERSON></a>, Japanese comedian and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Udo_Suzuki\" title=\"U<PERSON> Suzuki\"><PERSON><PERSON></a>, Japanese comedian and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>-young, South Korean actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-young\" title=\"<PERSON><PERSON>-young\"><PERSON><PERSON>-young</a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-young\" title=\"<PERSON><PERSON>-young\"><PERSON><PERSON>-young</a>, South Korean actress", "links": [{"title": "<PERSON><PERSON>-young", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-young"}]}, {"year": "1972", "text": "<PERSON>, Estonian chess player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian chess player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian chess player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Finnish cellist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish cellist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish cellist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Russian swimmer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian swimmer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian swimmer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Lithuanian basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Adomaitis\" title=\"Dainius Adomaitis\"><PERSON><PERSON></a>, Lithuanian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Adomaitis\" title=\"Dainius Adomaitis\"><PERSON><PERSON></a>, Lithuanian basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Adomaitis"}]}, {"year": "1974", "text": "<PERSON>, American comedian, actor, and screenwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1974", "text": "<PERSON>, Canadian-American ice hockey player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1974", "text": "<PERSON>, Bolivian footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1974)\" title=\"<PERSON> (footballer, born 1974)\"><PERSON></a>, Bolivian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1974)\" title=\"<PERSON> (footballer, born 1974)\"><PERSON></a>, Bolivian footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1974)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1974)"}]}, {"year": "1975", "text": "<PERSON>, Australian volleyball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian volleyball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech tennis player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Zde%C5%88ka_M%C3%A1lkov%C3%A1\" title=\"Zdeň<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zde%C5%88ka_M%C3%A1lkov%C3%A1\" title=\"Zdeň<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "Zdeňka <PERSON>", "link": "https://wikipedia.org/wiki/Zde%C5%88ka_M%C3%A1lkov%C3%A1"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Italian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Brazilian racing driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Tarso_Marques\" title=\"Tarso Marques\"><PERSON><PERSON><PERSON></a>, Brazilian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tarso_Marques\" title=\"Tarso Marques\"><PERSON><PERSON><PERSON></a>, Brazilian racing driver", "links": [{"title": "Tarso Marques", "link": "https://wikipedia.org/wiki/Tarso_Marques"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1976", "text": "<PERSON><PERSON>, English actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian actor, director, and photographer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Russian gymnast and sportscaster", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian gymnast and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian gymnast and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, South Korean baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>yun_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, South Korean baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>yun_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, South Korean baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-h<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English rapper and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" class=\"mw-redirect\" title=\"<PERSON> (rapper)\"><PERSON></a>, English rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(rapper)\" class=\"mw-redirect\" title=\"<PERSON> (rapper)\"><PERSON></a>, English rapper and producer", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>(rapper)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, English racing driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Button\"><PERSON><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Button\"><PERSON><PERSON></a>, English racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Russian-American dancer and choreographer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American dancer and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian-American actor and singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ar<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Sri Lankan cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Filipino basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paolo_<PERSON>ug<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Horno\" title=\"<PERSON><PERSON> del Horno\"><PERSON><PERSON> Horn<PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Horn<PERSON>\" title=\"<PERSON><PERSON> del Horno\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON> Horno", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Argentinian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gonz%C3%A1lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nz%C3%A1lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucho_Gonz%C3%A1lez"}]}, {"year": "1981", "text": "<PERSON><PERSON>, French politician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ney"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American politician", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American actress and singer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, German singer-songwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_tom_<PERSON>ink\" title=\"Robin tom Rink\"><PERSON> tom <PERSON></a>, German singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_tom_<PERSON>ink\" title=\"Robin tom Rink\"><PERSON> tom <PERSON></a>, German singer-songwriter", "links": [{"title": "Robin to<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_to<PERSON>_<PERSON>ink"}]}, {"year": "1982", "text": "<PERSON>, Australian rugby league player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>c"}]}, {"year": "1982", "text": "<PERSON>, South Korean pole vaulter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean pole vaulter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>k"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American-Japanese singer-songwriter and producer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>kar<PERSON>_<PERSON>a\" title=\"<PERSON>kar<PERSON> Utada\"><PERSON><PERSON><PERSON></a>, American-Japanese singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>kar<PERSON>_<PERSON>a\" title=\"<PERSON><PERSON><PERSON> Utada\"><PERSON><PERSON><PERSON></a>, American-Japanese singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>a"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Indian racing driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Elvis_<PERSON>\" title=\"Elvis <PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elvis_<PERSON>\" title=\"Elvis <PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Malian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9b%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Malian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9b%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Malian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jimmy_K%C3%A9b%C3%A9"}]}, {"year": "1984", "text": "<PERSON>, Austrian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, German decathlete", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American film director, screenwriter, and producer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director, screenwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director, screenwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American soccer player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Argentinian racing driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Esteban_Guerrieri\" title=\"Esteban Guerrieri\"><PERSON><PERSON><PERSON></a>, Argentinian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esteban_Guerrieri\" title=\"Esteban Guerrieri\"><PERSON><PERSON><PERSON></a>, Argentinian racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Japanese singer and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Russian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Elliott Ward\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Elliott Ward\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Italian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Senegalese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sow\" title=\"<PERSON>uss<PERSON> Sow\"><PERSON><PERSON><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>w\" title=\"<PERSON>ussa Sow\"><PERSON><PERSON><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sow"}]}, {"year": "1987", "text": "<PERSON>, Armenian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian wrestler", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> McGee", "link": "https://wikipedia.org/wiki/<PERSON>a<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Argentine tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Tatiana_B%C3%BAa\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tatiana_B%C3%BAa\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tatiana_B%C3%BAa"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Guyanese-American actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guyanese-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guyanese-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9e_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Croatian tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Petra_Marti%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petra_Marti%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Petra_Marti%C4%87"}]}, {"year": "1991", "text": "<PERSON>, American actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American gymnast", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Shawn <PERSON>\"><PERSON></a>, American gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American rapper (d. 2018)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Mexican footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Portuguese footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_M%C3%<PERSON><PERSON>_(footballer,_born_January_1993)\" title=\"<PERSON> (footballer, born January 1993)\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_M%C3%<PERSON><PERSON>_(footballer,_born_January_1993)\" title=\"<PERSON> (footballer, born January 1993)\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON> (footballer, born January 1993)", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_M%C3%<PERSON><PERSON>_(footballer,_born_January_1993)"}]}, {"year": "1993", "text": "<PERSON>, Argentine footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON> Ce<PERSON>uri<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_Centuri%C3%B3n"}]}, {"year": "1993", "text": "<PERSON>, Argentine footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1994", "text": "<PERSON>, German footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, English footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Zimbabwean footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nakamba\" title=\"Marvelous Nakamba\"><PERSON><PERSON></a>, Zimbabwean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nakamba\" title=\"Marvelous Nakamba\"><PERSON><PERSON></a>, Zimbabwean footballer", "links": [{"title": "Marvelous <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marvelous_Nakamba"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Czech footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ule<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>uler\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>r"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "2000", "text": "<PERSON>, Spanish footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2001", "text": "<PERSON>, Bahamian basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Ghanaian footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Guinean footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>ai<PERSON>_Mo<PERSON>\" title=\"Ilaix Moriba\"><PERSON><PERSON><PERSON></a>, Guinean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ilaix Moriba\"><PERSON><PERSON><PERSON></a>, Guinean footballer", "links": [{"title": "Ilaix <PERSON>", "link": "https://wikipedia.org/wiki/Ilaix_<PERSON>"}]}], "Deaths": [{"year": "520", "text": "<PERSON> of Cappadocia, patriarch of Constantinople", "html": "520 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cappadocia\" title=\"<PERSON> of Cappadocia\"><PERSON> of Cappadocia</a>, patriarch of Constantinople", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cappadocia\" title=\"<PERSON> of Cappadocia\"><PERSON> of Cappadocia</a>, patriarch of Constantinople", "links": [{"title": "<PERSON> of Cappadocia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cappadocia"}]}, {"year": "639", "text": "<PERSON><PERSON><PERSON>, Frankish king (b. 603)", "html": "639 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I\" title=\"<PERSON>gobert I\"><PERSON><PERSON><PERSON></a>, Frankish king (b. 603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>gobert I\"><PERSON><PERSON><PERSON> <PERSON></a>, Frankish king (b. 603)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "914", "text": "<PERSON>, king of León", "html": "914 - <a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_I_of_Le%C3%B3n\" title=\"<PERSON> I of León\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_I_of_Le%C3%B3n\" title=\"<PERSON> I of León\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a>", "links": [{"title": "<PERSON> I of León", "link": "https://wikipedia.org/wiki/Garc%C3%ADa_I_of_Le%C3%B3n"}, {"title": "Kingdom of León", "link": "https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n"}]}, {"year": "1003", "text": "<PERSON><PERSON> of Cologne, Irish abbot", "html": "1003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Cologne\" title=\"<PERSON><PERSON> of Cologne\"><PERSON><PERSON> of Cologne</a>, Irish abbot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Cologne\" title=\"<PERSON><PERSON> of Cologne\"><PERSON><PERSON> of Cologne</a>, Irish abbot", "links": [{"title": "<PERSON><PERSON> of Cologne", "link": "https://wikipedia.org/wiki/Kilian_of_Cologne"}]}, {"year": "1302", "text": "<PERSON><PERSON><PERSON><PERSON>, caliph of Cairo", "html": "1302 - <a href=\"https://wikipedia.org/wiki/Al-Hakim_<PERSON>\" title=\"Al-Hakim I\">Al-Ha<PERSON></a>, caliph of Cairo", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Hakim_I\" title=\"Al-Hakim I\">Al-Hakim I</a>, caliph of Cairo", "links": [{"title": "Al-Hakim I", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1401", "text": "<PERSON>, British justice", "html": "1401 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British justice", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British justice", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1526", "text": "<PERSON> of Austria, Danish queen (b. 1501)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/Isabella_of_Austria\" title=\"Isabella of Austria\">Isabella of Austria</a>, Danish queen (b. 1501)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isabella_of_Austria\" title=\"Isabella of Austria\"><PERSON> of Austria</a>, Danish queen (b. 1501)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/Isabella_of_Austria"}]}, {"year": "1547", "text": "<PERSON>, Earl of Surrey, English poet (b. 1516)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Surrey\" title=\"<PERSON>, Earl of Surrey\"><PERSON>, Earl of Surrey</a>, English poet (b. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Surrey\" title=\"<PERSON>, Earl of Surrey\"><PERSON>, Earl of Surrey</a>, English poet (b. 1516)", "links": [{"title": "<PERSON>, Earl of Surrey", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Surrey"}]}, {"year": "1565", "text": "<PERSON>, Spanish Jesuit theologian (b. 1512)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish Jesuit theologian (b. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish Jesuit theologian (b. 1512)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_Laynez"}]}, {"year": "1571", "text": "<PERSON>, Venetian painter (b. 1495)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/Paris_Bordone\" title=\"Paris Bordone\"><PERSON></a>, Venetian painter (b. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paris_Bordone\" title=\"Paris Bordone\"><PERSON></a>, Venetian painter (b. 1495)", "links": [{"title": "Paris Bordone", "link": "https://wikipedia.org/wiki/Paris_Bordone"}]}, {"year": "1576", "text": "<PERSON>, German poet and playwright (b. 1494)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (b. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (b. 1494)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1597", "text": "<PERSON><PERSON><PERSON>, Hindu Rajput king of Mewar (b.1540)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>na_<PERSON>ratap\" title=\"<PERSON>harana <PERSON>p\"><PERSON><PERSON>na <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Hindu\" class=\"mw-redirect\" title=\"Hindu\">Hindu</a> <a href=\"https://wikipedia.org/wiki/Rajput\" title=\"Raj<PERSON>\">Raj<PERSON></a> king of <a href=\"https://wikipedia.org/wiki/Mewar\" title=\"Mewar\">Mewar</a> (b.1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ratap\" title=\"<PERSON><PERSON>na <PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Hindu\" class=\"mw-redirect\" title=\"Hindu\">Hindu</a> <a href=\"https://wikipedia.org/wiki/Rajput\" title=\"Rajput\">Raj<PERSON></a> king of <a href=\"https://wikipedia.org/wiki/Mewar\" title=\"Mewar\">Mewar</a> (b.1540)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>p"}, {"title": "Hindu", "link": "https://wikipedia.org/wiki/Hindu"}, {"title": "Rajput", "link": "https://wikipedia.org/wiki/Rajput"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mewar"}]}, {"year": "1636", "text": "<PERSON> the Younger, Flemish painter (b.1561)", "html": "1636 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Flemish painter (b.1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Flemish painter (b.1561)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger"}]}, {"year": "1661", "text": "<PERSON>, English rebel leader", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rebel leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rebel leader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, English playwright and poet (b. 1670)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and poet (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and poet (b. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ve"}]}, {"year": "1755", "text": "<PERSON><PERSON><PERSON>, French physicist, mathematician, and astronomer (b. 1683)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist, mathematician, and astronomer (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist, mathematician, and astronomer (b. 1683)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1757", "text": "<PERSON>, Scottish scholar and academic (b. 1674)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish scholar and academic (b. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish scholar and academic (b. 1674)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, Italian-French architect and painter (b. 1695)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B2_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French architect and painter (b. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B2_<PERSON>\" title=\"<PERSON> Ni<PERSON>\"><PERSON></a>, Italian-French architect and painter (b. 1695)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giovanni_Niccol%C3%B2_<PERSON><PERSON>i"}]}, {"year": "1785", "text": "<PERSON>, English scholar and critic (b. 1713)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and critic (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and critic (b. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, French pianist and composer (b. 1791)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_H%C3%A9rold\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rold\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_H%C3%A9rold"}]}, {"year": "1847", "text": "<PERSON>, American soldier and politician, first Governor of New Mexico (b. 1799)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, first <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico\" title=\"Governor of New Mexico\">Governor of New Mexico</a> (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, first <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico\" title=\"Governor of New Mexico\">Governor of New Mexico</a> (b. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Mexico", "link": "https://wikipedia.org/wiki/Governor_of_New_Mexico"}]}, {"year": "1847", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek poet (b. 1772)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek poet (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek poet (b. 1772)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON>, Argentinian poet and author (b. 1805)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Esteban_Echeverr%C3%ADa\" title=\"Esteban Echeverría\"><PERSON><PERSON><PERSON></a>, Argentinian poet and author (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esteban_Echeverr%C3%ADa\" title=\"Esteban Echeverría\"><PERSON><PERSON><PERSON></a>, Argentinian poet and author (b. 1805)", "links": [{"title": "Esteban Echeverría", "link": "https://wikipedia.org/wiki/Esteban_Echeverr%C3%ADa"}]}, {"year": "1853", "text": "<PERSON>, German historian and academic (b. 1773)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (b. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American newspaperman, politician, and Confederate general (b. 1812)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American newspaperman, politician, and Confederate general (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American newspaperman, politician, and Confederate general (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, French philosopher and politician (b. 1809)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and politician (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and politician (b. 1809)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, German chemist and philosopher (b. 1788)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and philosopher (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and philosopher (b. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON> <PERSON>, German poet and scholar (b. 1798)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German poet and scholar (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German poet and scholar (b. 1798)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, French physicist and chemist (b. 1810)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON>, 1st Viscount of Seabra, Portuguese magistrate and politician (b. 1798)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Lu%C3%<PERSON><PERSON>_de_Seabra,_1st_Viscount_of_Seabra\" title=\"<PERSON><PERSON><PERSON><PERSON>, 1st Viscount of Seabra\"><PERSON><PERSON><PERSON><PERSON>, 1st Viscount of Seabra</a>, Portuguese magistrate and politician (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Lu%C3%<PERSON><PERSON>_de_Seabra,_1st_Viscount_of_Seabra\" title=\"<PERSON><PERSON><PERSON><PERSON>, 1st Viscount of Seabra\"><PERSON><PERSON><PERSON><PERSON>, 1st Viscount of Seabra</a>, Portuguese magistrate and politician (b. 1798)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, 1st Viscount of Seabra", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_Lu%C3%<PERSON><PERSON>_de_Seabra,_1st_Viscount_of_Seabra"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Argentinian historian and politician, sixth President of Argentina (b. 1821)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_Mi<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian historian and politician, sixth <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_Mi<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian historian and politician, sixth <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1821)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartolom%C3%A9_<PERSON>tre"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1915", "text": "<PERSON>, Belgian cellist and composer (b. 1840)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cellist and composer (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cellist and composer (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Chinese journalist, philosopher, and scholar (b. 1873)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese journalist, philosopher, and scholar (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese journalist, philosopher, and scholar (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, British mathematician, philosopher and economist (b. 1903)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, British mathematician, philosopher and economist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, British mathematician, philosopher and economist (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Serbian author, playwright, and journalist (b. 1864)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian author, playwright, and journalist (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian author, playwright, and journalist (b. 1864)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Nu%C5%A1i%C4%87"}]}, {"year": "1945", "text": "<PERSON><PERSON>, French general (b. 1886)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French general (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French general (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, French architect and urban planner, designed the Stade de Gerland (b. 1869)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, French architect and urban planner, designed the <a href=\"https://wikipedia.org/wiki/Stade_de_Gerland\" title=\"Stade de Gerland\">Stade de Gerland</a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, French architect and urban planner, designed the <a href=\"https://wikipedia.org/wiki/Stade_de_Gerland\" title=\"Stade de Gerland\">Stade de Gerland</a> (b. 1869)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)"}, {"title": "Stade de Gerland", "link": "https://wikipedia.org/wiki/Stade_de_Gerland"}]}, {"year": "1954", "text": "<PERSON>, German mathematician and physicist (b. 1885)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, Romanian-Hungarian activist and politician (b. 1912)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_Dud%C3%A1s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian-Hungarian activist and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_Dud%C3%A1s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian-Hungarian activist and politician (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3z<PERSON>_Dud%C3%A1s"}]}, {"year": "1963", "text": "<PERSON>, American golfer (b. 1884)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist (b. 1886)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>rm<PERSON>_<PERSON>\" title=\"<PERSON>rm<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rm<PERSON>_<PERSON>\" title=\"<PERSON>rm<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Firm<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Estonian weightlifter (b. 1905)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4%C3%A4r\" title=\"<PERSON>\"><PERSON></a>, Estonian weightlifter (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4%C3%A4r\" title=\"<PERSON>\"><PERSON></a>, Estonian weightlifter (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4%C3%A4r"}]}, {"year": "1968", "text": "<PERSON>, American race car driver and engineer (b. 1879)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and engineer (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and engineer (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American violinist (b. 1936)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(violinist)\" class=\"mw-redirect\" title=\"<PERSON> (violinist)\"><PERSON></a>, American violinist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(violinist)\" class=\"mw-redirect\" title=\"<PERSON> (violinist)\"><PERSON></a>, American violinist (b. 1936)", "links": [{"title": "<PERSON> (violinist)", "link": "https://wikipedia.org/wiki/<PERSON>(violinist)"}]}, {"year": "1973", "text": "<PERSON>, Irish-English actor (b. 1903)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Adrian\"><PERSON></a>, Irish-English actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American painter and educator (b. 1889)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter and educator (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter and educator (b. 1889)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese engineer and academic (b. 1886)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Hidet<PERSON><PERSON>_<PERSON>\" title=\"Hidetsugu Yagi\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese engineer and academic (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>det<PERSON><PERSON>_<PERSON>\" title=\"Hidetsugu Yagi\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese engineer and academic (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>det<PERSON><PERSON>_Yagi"}]}, {"year": "1979", "text": "<PERSON><PERSON>, German novelist and poet (b. 1884)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON> (writer)\"><PERSON><PERSON></a>, German novelist and poet (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON> (writer)\"><PERSON><PERSON></a>, German novelist and poet (b. 1884)", "links": [{"title": "<PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(writer)"}]}, {"year": "1980", "text": "<PERSON>, American lawyer and jurist (b. 1898)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American photographer (b. 1958)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Brazilian soprano (b. 1945)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Regina\" title=\"Elis Regina\"><PERSON><PERSON></a>, Brazilian soprano (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Elis Regina\"><PERSON><PERSON></a>, Brazilian soprano (b. 1945)", "links": [{"title": "<PERSON>s <PERSON>", "link": "https://wikipedia.org/wiki/Elis_Regina"}]}, {"year": "1983", "text": "<PERSON>, chimpanzee and animal astronaut, first hominid in space (b. 1957)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Ham_(chimpanzee)\" title=\"Ham (chimpanzee)\">Ham</a>, <a href=\"https://wikipedia.org/wiki/Chimpanzee\" title=\"Chimpanzee\">chimpanzee</a> and <a href=\"https://wikipedia.org/wiki/Animal_astronaut\" class=\"mw-redirect\" title=\"Animal astronaut\">animal astronaut</a>, first <a href=\"https://wikipedia.org/wiki/Hominid\" class=\"mw-redirect\" title=\"Hominid\">hominid</a> in space (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ham_(chimpanzee)\" title=\"Ham (chimpanzee)\">Ham</a>, <a href=\"https://wikipedia.org/wiki/Chimpanzee\" title=\"Chimpanzee\">chimpanzee</a> and <a href=\"https://wikipedia.org/wiki/Animal_astronaut\" class=\"mw-redirect\" title=\"Animal astronaut\">animal astronaut</a>, first <a href=\"https://wikipedia.org/wiki/Hominid\" class=\"mw-redirect\" title=\"Hominid\">hominid</a> in space (b. 1957)", "links": [{"title": "<PERSON> (chimpanzee)", "link": "https://wikipedia.org/wiki/<PERSON>_(chimpanzee)"}, {"title": "Chimpanzee", "link": "https://wikipedia.org/wiki/Chimpanzee"}, {"title": "Animal astronaut", "link": "https://wikipedia.org/wiki/Animal_astronaut"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hominid"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1920)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bentley\"><PERSON></a>, Canadian ice hockey player and coach (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American psychologist and academic (b. 1927)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian guru and mystic (b. 1931)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>g<PERSON> <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian guru and mystic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian guru and mystic (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English pianist, composer, and conductor (b. 1908)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, German politician, sixth Minister of Intra-German Relations (b. 1906)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, sixth <a href=\"https://wikipedia.org/wiki/Minister_of_Intra-German_Relations\" title=\"Minister of Intra-German Relations\">Minister of Intra-German Relations</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, sixth <a href=\"https://wikipedia.org/wiki/Minister_of_Intra-German_Relations\" title=\"Minister of Intra-German Relations\">Minister of Intra-German Relations</a> (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Intra-German Relations", "link": "https://wikipedia.org/wiki/Minister_of_Intra-German_Relations"}]}, {"year": "1991", "text": "<PERSON>, Canadian biochemist and journalist (b. 1918)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian biochemist and journalist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian biochemist and journalist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Canadian singer-songwriter (b. 1938)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ellan"}]}, {"year": "1996", "text": "<PERSON>, American actor, producer, and screenwriter (b. 1943)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American poet and novelist (b. 1923)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and novelist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and novelist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1932)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Italian rugby player (b. 1967)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a Baháʼí Faith Hand of the Cause of God and wife of <PERSON><PERSON><PERSON> (b. 1910)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/R%C3%BAh%C3%ADyyih_Khanum\" class=\"mw-redirect\" title=\"Rúh<PERSON><PERSON><PERSON>h Khanum\">Am<PERSON><PERSON><PERSON>l-<PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a> <a href=\"https://wikipedia.org/wiki/Hands_of_the_Cause\" title=\"Hands of the Cause\">Hand of the Cause of God</a> and wife of <a href=\"https://wikipedia.org/wiki/Shoghi_Effendi\" title=\"Shoghi Effendi\">Shoghi Effendi</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%BAh%C3%ADyyih_Khanum\" class=\"mw-redirect\" title=\"Rúh<PERSON><PERSON><PERSON><PERSON> Khanum\">Am<PERSON><PERSON>'l-<PERSON><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a> <a href=\"https://wikipedia.org/wiki/Hands_of_the_Cause\" title=\"Hands of the Cause\">Hand of the Cause of God</a> and wife of <a href=\"https://wikipedia.org/wiki/Shoghi_Effendi\" title=\"Shoghi Effendi\">Shog<PERSON> Effendi</a> (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%BAh%C3%ADyyih_Khanum"}, {"title": "Baháʼí Faith", "link": "https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith"}, {"title": "Hands of the Cause", "link": "https://wikipedia.org/wiki/Hands_of_the_Cause"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shog<PERSON>_Effendi"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Italian lawyer and politician, 45th Prime Minister of Italy (b. 1934)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Craxi"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Austrian-American actress, singer, and mathematician (b. 1914)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American actress, singer, and mathematician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American actress, singer, and mathematician (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/He<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and manager (b. 1934)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Vav%C3%A1\" title=\"Vav<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vav%C3%A1\" title=\"Vav<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (b. 1934)", "links": [{"title": "Vavá", "link": "https://wikipedia.org/wiki/Vav%C3%A1"}]}, {"year": "2003", "text": "<PERSON>, Honduran footballer (b. 1974)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran footballer (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran footballer (b. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, French journalist, screenwriter, and politician, French Minister of Culture (b. 1916)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French journalist, screenwriter, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Culture_(France)\" class=\"mw-redirect\" title=\"Minister of Culture (France)\">French Minister of Culture</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French journalist, screenwriter, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Culture_(France)\" class=\"mw-redirect\" title=\"Minister of Culture (France)\">French Minister of Culture</a> (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>"}, {"title": "Minister of Culture (France)", "link": "https://wikipedia.org/wiki/Minister_of_Culture_(France)"}]}, {"year": "2004", "text": "<PERSON>, American lawyer and judge (b. 1917)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Australian cricketer and coach (b. 1955)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, South African author and screenwriter (b. 1974)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African author and screenwriter (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Duike<PERSON>\"><PERSON><PERSON></a>, South African author and screenwriter (b. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actor (b. 1928)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter (b. 1941)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Turkish-Armenian journalist and activist (b. 1954)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-Armenian journalist and activist (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-Armenian journalist and activist (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H<PERSON>_<PERSON>k"}]}, {"year": "2007", "text": "<PERSON>, Canadian singer-songwriter (b. 1940)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Russian singer-songwriter (b. 1969)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian singer-songwriter (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian singer-songwriter (b. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actress (b. 1937)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1939)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1939)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "2008", "text": "<PERSON>, Canadian sportscaster (b. 1936)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sportscaster (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sportscaster (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Scottish rugby player and sportscaster (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rugby player and sportscaster (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rugby player and sportscaster (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Swedish ice hockey player (b. 1962)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%85slin\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%85slin\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%85slin"}]}, {"year": "2012", "text": "<PERSON>, Canadian skier (b. 1982)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier (b. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Jamaican singer-songwriter and producer (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter and producer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter and producer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Dutch ballet dancer and choreographer (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch ballet dancer and choreographer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch ballet dancer and choreographer (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 48th <PERSON><PERSON><PERSON><PERSON> (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Taih%C5%8D_K%C5%8Dki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 48th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taih%C5%8D_K%C5%8Dki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 48th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON><PERSON>na\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taih%C5%8D_K%C5%8Dki"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "2013", "text": "<PERSON>, American baseball player and manager (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "2013", "text": "<PERSON>, American conductor and composer (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and composer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player and manager (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish academician, political commentator, columnist and writer (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Toktam%C4%B1%C5%9F_Ate%C5%9F\" title=\"Toktam<PERSON>ş Ateş\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish academician, political commentator, columnist and writer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toktam%C4%B1%C5%9F_Ate%C5%9F\" title=\"Toktam<PERSON>ş Ateş\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish academician, political commentator, columnist and writer (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Ateş", "link": "https://wikipedia.org/wiki/Toktam%C4%B1%C5%9F_Ate%C5%9F"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Israeli environmentalist, co-founded the Society for the Protection of Nature in Israel (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Azaria_Alon\" title=\"Azaria Alon\"><PERSON><PERSON><PERSON></a>, Ukrainian-Israeli environmentalist, co-founded the <a href=\"https://wikipedia.org/wiki/Society_for_the_Protection_of_Nature_in_Israel\" title=\"Society for the Protection of Nature in Israel\">Society for the Protection of Nature in Israel</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Azaria_Alon\" title=\"<PERSON><PERSON>ia Al<PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Israeli environmentalist, co-founded the <a href=\"https://wikipedia.org/wiki/Society_for_the_Protection_of_Nature_in_Israel\" title=\"Society for the Protection of Nature in Israel\">Society for the Protection of Nature in Israel</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Azaria_Alon"}, {"title": "Society for the Protection of Nature in Israel", "link": "https://wikipedia.org/wiki/Society_for_the_Protection_of_Nature_in_Israel"}]}, {"year": "2014", "text": "<PERSON>, English runner, journalist, and politician (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner, journalist, and politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner, journalist, and politician (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Romanian engineer and academic (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83\" title=\"<PERSON>\"><PERSON></a>, Romanian engineer and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83\" title=\"<PERSON>\"><PERSON></a>, Romanian engineer and academic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Justin_Capr%C4%83"}]}, {"year": "2015", "text": "<PERSON>, Canadian lawyer and politician (b. 1953)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American-French singer-songwriter and conductor (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French singer-songwriter and conductor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French singer-songwriter and conductor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American ecologist and geneticist (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ecologist and geneticist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ecologist and geneticist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Italian director and screenwriter (b. 1931)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian director and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian director and screenwriter (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON> (<PERSON>), English actress (b. 1922)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (<PERSON>), English actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (<PERSON>), English actress (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American actor (b. 1955)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American baseball player and manager (b. 1941)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}