{"date": "February 10", "url": "https://wikipedia.org/wiki/February_10", "data": {"Events": [{"year": "1258", "text": "The Siege of Baghdad ends with the surrender of the last Abbasid caliph to <PERSON><PERSON><PERSON>, a prince of the Mongol Empire.", "html": "1258 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Baghdad\" title=\"Siege of Baghdad\">Siege of Baghdad</a> ends with the surrender of the last <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\"><PERSON><PERSON> caliph</a> to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Khan\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a>, a prince of the <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol Empire</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Baghdad\" title=\"Siege of Baghdad\">Siege of Baghdad</a> ends with the surrender of the last <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\"><PERSON><PERSON> caliph</a> to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a>, a prince of the <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol Empire</a>.", "links": [{"title": "Siege of Baghdad", "link": "https://wikipedia.org/wiki/Siege_of_Baghdad"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abbasid_Caliphate"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}]}, {"year": "1306", "text": "In front of the high altar of Greyfriars Church in Dumfries, <PERSON> the <PERSON> murders <PERSON>, sparking the revolution in the Wars of Scottish Independence.", "html": "1306 - In front of the high altar of Greyfriars Church in <a href=\"https://wikipedia.org/wiki/Dumfries\" title=\"Dumfries\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> murders <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Badenoch\" title=\"<PERSON> of Badenoch\"><PERSON></a>, sparking the revolution in the <a href=\"https://wikipedia.org/wiki/Wars_of_Scottish_Independence\" title=\"Wars of Scottish Independence\">Wars of Scottish Independence</a>.", "no_year_html": "In front of the high altar of Greyfriars Church in <a href=\"https://wikipedia.org/wiki/Dumfries\" title=\"Dumfries\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> murders <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Badenoch\" title=\"<PERSON> of Badenoch\"><PERSON></a>, sparking the revolution in the <a href=\"https://wikipedia.org/wiki/Wars_of_Scottish_Independence\" title=\"Wars of Scottish Independence\">Wars of Scottish Independence</a>.", "links": [{"title": "Dumfries", "link": "https://wikipedia.org/wiki/Dum<PERSON>ries"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of Badenoch", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Badenoch"}, {"title": "Wars of Scottish Independence", "link": "https://wikipedia.org/wiki/Wars_of_Scottish_Independence"}]}, {"year": "1355", "text": "The St Scholastica Day riot breaks out in Oxford, England, leaving 63 scholars and perhaps 30 locals dead in two days.", "html": "1355 - The <a href=\"https://wikipedia.org/wiki/St_Scholastica_Day_riot\" title=\"St Scholastica Day riot\">St Scholastica Day riot</a> breaks out in <a href=\"https://wikipedia.org/wiki/Oxford\" title=\"Oxford\">Oxford</a>, England, leaving 63 scholars and perhaps 30 locals dead in two days.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/St_Scholastica_Day_riot\" title=\"St Scholastica Day riot\">St Scholastica Day riot</a> breaks out in <a href=\"https://wikipedia.org/wiki/Oxford\" title=\"Oxford\">Oxford</a>, England, leaving 63 scholars and perhaps 30 locals dead in two days.", "links": [{"title": "St Scholastica Day riot", "link": "https://wikipedia.org/wiki/St_Scholastica_Day_riot"}, {"title": "Oxford", "link": "https://wikipedia.org/wiki/Oxford"}]}, {"year": "1502", "text": "<PERSON><PERSON> sets sail from Lisbon, Portugal, on his second voyage to India.", "html": "1502 - <a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\">Vasco da Gama</a> sets sail from <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a>, Portugal, on his <a href=\"https://wikipedia.org/wiki/4th_Portuguese_India_Armada_(Gama,_1502)\" title=\"4th Portuguese India Armada (Gama, 1502)\">second voyage</a> to India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\">Vasco da Gama</a> sets sail from <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a>, Portugal, on his <a href=\"https://wikipedia.org/wiki/4th_Portuguese_India_Armada_(Gama,_1502)\" title=\"4th Portuguese India Armada (Gama, 1502)\">second voyage</a> to India.", "links": [{"title": "Vasco da Gama", "link": "https://wikipedia.org/wiki/V<PERSON>_da_Gama"}, {"title": "Lisbon", "link": "https://wikipedia.org/wiki/Lisbon"}, {"title": "4th Portuguese India Armada (Gama, 1502)", "link": "https://wikipedia.org/wiki/4th_Portuguese_India_Armada_(Gama,_1502)"}]}, {"year": "1567", "text": "Lord <PERSON><PERSON>, second husband of <PERSON>, Queen of Scots, is found strangled following an explosion at the Kirk o' Field house in Edinburgh, Scotland, a suspected assassination.", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON>\" title=\"<PERSON>, Lord Dar<PERSON>ley\">Lord <PERSON></a>, second husband of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, is found strangled following an explosion at the <a href=\"https://wikipedia.org/wiki/Kirk_o%27_Field\" title=\"Kirk o' Field\">Kirk o' Field house</a> in <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>, Scotland, a suspected assassination.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord Dar<PERSON>ley\">Lord <PERSON></a>, second husband of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, is found strangled following an explosion at the <a href=\"https://wikipedia.org/wiki/Kirk_o%27_Field\" title=\"Kirk o' Field\">Kirk o' Field house</a> in <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>, Scotland, a suspected assassination.", "links": [{"title": "<PERSON>, Lord Dar<PERSON>ley", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>"}, {"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kirk_<PERSON>%27_Field"}, {"title": "Edinburgh", "link": "https://wikipedia.org/wiki/Edinburgh"}]}, {"year": "1712", "text": "<PERSON>lliches in Chiloé rebel against Spanish encomenderos.", "html": "1712 - <a href=\"https://wikipedia.org/wiki/Huilliche_uprising_of_1712\" title=\"Huilliche uprising of 1712\">Huilliches in Chiloé rebel</a> against Spanish <a href=\"https://wikipedia.org/wiki/Encomienda\" title=\"Encomienda\">encomenderos</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Huilliche_uprising_of_1712\" title=\"Huilliche uprising of 1712\">Huilliches in Chiloé rebel</a> against Spanish <a href=\"https://wikipedia.org/wiki/Encomienda\" title=\"Encomienda\">encomenderos</a>.", "links": [{"title": "Huilliche uprising of 1712", "link": "https://wikipedia.org/wiki/Huilliche_uprising_of_1712"}, {"title": "Encomienda", "link": "https://wikipedia.org/wiki/Encomienda"}]}, {"year": "1763", "text": "French and Indian War: The Treaty of Paris ends the war and France cedes Quebec to Great Britain.", "html": "1763 - <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: The <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1763)\" title=\"Treaty of Paris (1763)\">Treaty of Paris</a> ends the war and France cedes <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> to Great Britain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: The <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1763)\" title=\"Treaty of Paris (1763)\">Treaty of Paris</a> ends the war and France cedes <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> to Great Britain.", "links": [{"title": "French and Indian War", "link": "https://wikipedia.org/wiki/French_and_Indian_War"}, {"title": "Treaty of Paris (1763)", "link": "https://wikipedia.org/wiki/Treaty_of_Paris_(1763)"}, {"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}]}, {"year": "1814", "text": "Napoleonic Wars: The Battle of Champaubert ends in French victory over the Russians and the Prussians.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Champaubert\" title=\"Battle of Champaubert\">Battle of Champaubert</a> ends in French victory over the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russians</a> and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussians</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Champaubert\" title=\"Battle of Champaubert\">Battle of Champaubert</a> ends in French victory over the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russians</a> and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussians</a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Battle of Champaubert", "link": "https://wikipedia.org/wiki/Battle_of_Champaubert"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Kingdom of Prussia", "link": "https://wikipedia.org/wiki/Kingdom_of_Prussia"}]}, {"year": "1840", "text": "Queen <PERSON> of the United Kingdom marries Prince <PERSON> of Saxe-Coburg-Gotha.", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a> of the United Kingdom marries <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Saxe-Coburg-Gotha\" class=\"mw-redirect\" title=\"Prince <PERSON> of Saxe-Coburg-Gotha\">Prince <PERSON> of Saxe-Coburg-Gotha</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a> of the United Kingdom marries <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Saxe-Coburg-Gotha\" class=\"mw-redirect\" title=\"Prince <PERSON> of Saxe-Coburg-Gotha\">Prince <PERSON> of Saxe-Coburg-Gotha</a>.", "links": [{"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}, {"title": "<PERSON> of Saxe-Coburg-Gotha", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_of_Saxe-Coburg-Gotha"}]}, {"year": "1846", "text": "First Anglo-Sikh War: Battle of Sobraon: British defeat Sikhs in the final battle of the war.", "html": "1846 - <a href=\"https://wikipedia.org/wiki/First_Anglo-Sikh_War\" class=\"mw-redirect\" title=\"First Anglo-Sikh War\">First Anglo-Sikh War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Sobraon\" title=\"Battle of Sobraon\">Battle of Sobraon</a>: British defeat Sikhs in the final battle of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Anglo-Sikh_War\" class=\"mw-redirect\" title=\"First Anglo-Sikh War\">First Anglo-Sikh War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Sobraon\" title=\"Battle of Sobraon\">Battle of Sobraon</a>: British defeat Sikhs in the final battle of the war.", "links": [{"title": "First Anglo-Sikh War", "link": "https://wikipedia.org/wiki/First_Anglo-Sikh_War"}, {"title": "Battle of Sobraon", "link": "https://wikipedia.org/wiki/Battle_of_Sobraon"}]}, {"year": "1861", "text": "<PERSON> is notified by telegraph that he has been chosen as provisional President of the Confederate States of America.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is notified by telegraph that he has been chosen as provisional <a href=\"https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America\" title=\"President of the Confederate States of America\">President of the Confederate States of America</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is notified by telegraph that he has been chosen as provisional <a href=\"https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America\" title=\"President of the Confederate States of America\">President of the Confederate States of America</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Confederate States of America", "link": "https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America"}]}, {"year": "1862", "text": "American Civil War: A Union naval flotilla destroys the bulk of the Confederate Mosquito Fleet in the Battle of Elizabeth City on the Pasquotank River in North Carolina.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: A <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> naval flotilla destroys the bulk of the Confederate <a href=\"https://wikipedia.org/wiki/Mosquito_Fleet\" title=\"Mosquito Fleet\">Mosquito Fleet</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Elizabeth_City\" title=\"Battle of Elizabeth City\">Battle of Elizabeth City</a> on the <a href=\"https://wikipedia.org/wiki/Pasquotank_River\" title=\"Pasquotank River\">Pasquotank River</a> in <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: A <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> naval flotilla destroys the bulk of the Confederate <a href=\"https://wikipedia.org/wiki/Mosquito_Fleet\" title=\"Mosquito Fleet\">Mosquito Fleet</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Elizabeth_City\" title=\"Battle of Elizabeth City\">Battle of Elizabeth City</a> on the <a href=\"https://wikipedia.org/wiki/Pasquotank_River\" title=\"Pasquotank River\">Pasquotank River</a> in <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Mosquito Fleet", "link": "https://wikipedia.org/wiki/Mosquito_Fleet"}, {"title": "Battle of Elizabeth City", "link": "https://wikipedia.org/wiki/Battle_of_Elizabeth_City"}, {"title": "Pasquotank River", "link": "https://wikipedia.org/wiki/Pasquotank_River"}, {"title": "North Carolina", "link": "https://wikipedia.org/wiki/North_Carolina"}]}, {"year": "1906", "text": "HMS Dreadnought, the first of a revolutionary new breed of battleships, is christened.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/HMS_Dreadnought_(1906)\" title=\"HMS Dreadnought (1906)\">HMS <i>Dreadnought</i></a>, the first of a revolutionary <a href=\"https://wikipedia.org/wiki/Dreadnought\" title=\"Dreadnought\">new breed of battleships</a>, is christened.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Dreadnought_(1906)\" title=\"HMS Dreadnought (1906)\">HMS <i>Dreadnought</i></a>, the first of a revolutionary <a href=\"https://wikipedia.org/wiki/Dreadnought\" title=\"Dreadnought\">new breed of battleships</a>, is christened.", "links": [{"title": "HMS Dreadnought (1906)", "link": "https://wikipedia.org/wiki/HMS_Dreadnought_(1906)"}, {"title": "Dreadnought", "link": "https://wikipedia.org/wiki/Dreadnought"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON> performs the symbolic wedding of Poland to the sea, celebrating restitution of Polish access to open sea.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>_de_Hallenburg\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Hallenburg\"><PERSON><PERSON><PERSON>nburg</a> performs the <a href=\"https://wikipedia.org/wiki/Poland%27s_Wedding_to_the_Sea\" title=\"Poland's Wedding to the Sea\">symbolic wedding of Poland to the sea</a>, celebrating restitution of Polish access to open sea.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON><PERSON>_<PERSON>_de_Hallenburg\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Hallenburg\"><PERSON><PERSON><PERSON>nburg</a> performs the <a href=\"https://wikipedia.org/wiki/Poland%27s_Wedding_to_the_Sea\" title=\"Poland's Wedding to the Sea\">symbolic wedding of Poland to the sea</a>, celebrating restitution of Polish access to open sea.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>_<PERSON>_Hallenburg"}, {"title": "Poland's Wedding to the Sea", "link": "https://wikipedia.org/wiki/Poland%27s_Wedding_to_the_Sea"}]}, {"year": "1920", "text": "About 75% of the population in Zone I votes to join Denmark in the 1920 Schleswig plebiscites.", "html": "1920 - About 75% of the population in Zone I votes to join <a href=\"https://wikipedia.org/wiki/Denmark\" title=\"Denmark\">Denmark</a> in the <a href=\"https://wikipedia.org/wiki/1920_Schleswig_plebiscites\" title=\"1920 Schleswig plebiscites\">1920 Schleswig plebiscites</a>.", "no_year_html": "About 75% of the population in Zone I votes to join <a href=\"https://wikipedia.org/wiki/Denmark\" title=\"Denmark\">Denmark</a> in the <a href=\"https://wikipedia.org/wiki/1920_Schleswig_plebiscites\" title=\"1920 Schleswig plebiscites\">1920 Schleswig plebiscites</a>.", "links": [{"title": "Denmark", "link": "https://wikipedia.org/wiki/Denmark"}, {"title": "1920 Schleswig plebiscites", "link": "https://wikipedia.org/wiki/1920_Schleswig_plebiscites"}]}, {"year": "1923", "text": "Texas Tech University is founded as Texas Technological College in Lubbock, Texas.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Texas_Tech_University\" title=\"Texas Tech University\">Texas Tech University</a> is founded as Texas Technological College in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Texas\" title=\"Lubbock, Texas\">Lubbock, Texas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Texas_Tech_University\" title=\"Texas Tech University\">Texas Tech University</a> is founded as Texas Technological College in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Texas\" title=\"Lubbock, Texas\">Lubbock, Texas</a>.", "links": [{"title": "Texas Tech University", "link": "https://wikipedia.org/wiki/Texas_Tech_University"}, {"title": "Lubbock, Texas", "link": "https://wikipedia.org/wiki/Lubbock,_Texas"}]}, {"year": "1930", "text": "The Việt Nam Quốc Dân Đảng launches the failed Yên Bái mutiny in hope of overthrowing French protectorate over Vietnam.", "html": "1930 - The <a href=\"https://wikipedia.org/wiki/Vi%E1%BB%87t_Nam_Qu%E1%BB%91c_D%C3%A2n_%C4%90%E1%BA%A3ng\" title=\"Việt Nam Quốc Dân Đảng\">Việ<PERSON> Nam Quốc Dân Đảng</a> launches the failed <a href=\"https://wikipedia.org/wiki/Y%C3%AAn_B%C3%A1i_mutiny\" title=\"Yên Bái mutiny\">Yên Bái mutiny</a> in hope of overthrowing <a href=\"https://wikipedia.org/wiki/Annam_(French_protectorate)\" title=\"Annam (French protectorate)\">French protectorate over Vietnam</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Vi%E1%BB%87t_Nam_Qu%E1%BB%91c_D%C3%A2n_%C4%90%E1%BA%A3ng\" title=\"Việt Nam Quốc Dân Đảng\">Vi<PERSON><PERSON> <PERSON> Quốc Dân Đảng</a> launches the failed <a href=\"https://wikipedia.org/wiki/Y%C3%AAn_B%C3%A1i_mutiny\" title=\"Yên Bái mutiny\">Yên Bái mutiny</a> in hope of overthrowing <a href=\"https://wikipedia.org/wiki/Annam_(French_protectorate)\" title=\"Annam (French protectorate)\">French protectorate over Vietnam</a>.", "links": [{"title": "Việt Nam <PERSON> Đ<PERSON>", "link": "https://wikipedia.org/wiki/Vi%E1%BB%87t_Nam_Qu%E1%BB%91c_D%C3%A2n_%C4%90%E1%BA%A3ng"}, {"title": "<PERSON><PERSON><PERSON> mutiny", "link": "https://wikipedia.org/wiki/Y%C3%AAn_B%C3%A1i_mutiny"}, {"title": "Annam (French protectorate)", "link": "https://wikipedia.org/wiki/Annam_(French_protectorate)"}]}, {"year": "1933", "text": "In round 13 of a boxing match at New York City's Madison Square Garden, <PERSON><PERSON><PERSON> knocks out <PERSON>. <PERSON><PERSON><PERSON> dies four days later.", "html": "1933 - In round 13 of a <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a> match at New York City's <a href=\"https://wikipedia.org/wiki/Madison_Square_Garden_(1925)\" title=\"Madison Square Garden (1925)\">Madison Square Garden</a>, <a href=\"https://wikipedia.org/wiki/Primo_Carnera\" title=\"Primo Carnera\">Primo Carnera</a> knocks out <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. <PERSON><PERSON><PERSON> dies four days later.", "no_year_html": "In round 13 of a <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a> match at New York City's <a href=\"https://wikipedia.org/wiki/Madison_Square_Garden_(1925)\" title=\"Madison Square Garden (1925)\">Madison Square Garden</a>, <a href=\"https://wikipedia.org/wiki/Primo_Carnera\" title=\"Primo Carnera\">Primo Carnera</a> knocks out <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. <PERSON><PERSON><PERSON> dies four days later.", "links": [{"title": "Boxing", "link": "https://wikipedia.org/wiki/Boxing"}, {"title": "Madison Square Garden (1925)", "link": "https://wikipedia.org/wiki/Madison_Square_Garden_(1925)"}, {"title": "Primo Carnera", "link": "https://wikipedia.org/wiki/Primo_Carnera"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "Second Italo-Abyssinian War: Italian troops launch the Battle of Amba Aradam against Ethiopian defenders.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Second_Italo-Abyssinian_War\" class=\"mw-redirect\" title=\"Second Italo-Abyssinian War\">Second Italo-Abyssinian War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> troops launch the <a href=\"https://wikipedia.org/wiki/Battle_of_Amba_Aradam\" title=\"Battle of Amba Aradam\">Battle of Amba Aradam</a> against <a href=\"https://wikipedia.org/wiki/Ethiopian_Empire\" title=\"Ethiopian Empire\">Ethiopian</a> defenders.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Italo-Abyssinian_War\" class=\"mw-redirect\" title=\"Second Italo-Abyssinian War\">Second Italo-Abyssinian War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> troops launch the <a href=\"https://wikipedia.org/wiki/Battle_of_Amba_Aradam\" title=\"Battle of Amba Aradam\">Battle of Amba Aradam</a> against <a href=\"https://wikipedia.org/wiki/Ethiopian_Empire\" title=\"Ethiopian Empire\">Ethiopian</a> defenders.", "links": [{"title": "Second Italo-Abyssinian War", "link": "https://wikipedia.org/wiki/Second_Italo-Abyssinian_War"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "Battle of Amba Aradam", "link": "https://wikipedia.org/wiki/Battle_of_Amba_Aradam"}, {"title": "Ethiopian Empire", "link": "https://wikipedia.org/wiki/Ethiopian_Empire"}]}, {"year": "1939", "text": "Spanish Civil War: The Nationalists conclude their conquest of Catalonia and seal the border with France.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Francoist_Spain\" title=\"Francoist Spain\">Nationalists</a> conclude their <a href=\"https://wikipedia.org/wiki/Catalonia_Offensive\" title=\"Catalonia Offensive\">conquest of Catalonia</a> and seal the border with France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Francoist_Spain\" title=\"Francoist Spain\">Nationalists</a> conclude their <a href=\"https://wikipedia.org/wiki/Catalonia_Offensive\" title=\"Catalonia Offensive\">conquest of Catalonia</a> and seal the border with France.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Francoist Spain", "link": "https://wikipedia.org/wiki/Francoist_Spain"}, {"title": "Catalonia Offensive", "link": "https://wikipedia.org/wiki/Catalonia_Offensive"}]}, {"year": "1940", "text": "The Soviet Union begins mass deportations of Polish citizens from occupied eastern Poland to Siberia.", "html": "1940 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> begins mass deportations of Polish citizens from <a href=\"https://wikipedia.org/wiki/Territories_of_Poland_annexed_by_the_Soviet_Union\" title=\"Territories of Poland annexed by the Soviet Union\">occupied eastern Poland</a> to Siberia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> begins mass deportations of Polish citizens from <a href=\"https://wikipedia.org/wiki/Territories_of_Poland_annexed_by_the_Soviet_Union\" title=\"Territories of Poland annexed by the Soviet Union\">occupied eastern Poland</a> to Siberia.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Territories of Poland annexed by the Soviet Union", "link": "https://wikipedia.org/wiki/Territories_of_Poland_annexed_by_the_Soviet_Union"}]}, {"year": "1943", "text": "World War II: Attempting to completely lift the Siege of Leningrad, the Soviet Red Army engages German troops and Spanish volunteers in the Battle of Krasny Bor.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Attempting to completely lift the <a href=\"https://wikipedia.org/wiki/Siege_of_Leningrad\" title=\"Siege of Leningrad\">Siege of Leningrad</a>, the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Soviet Red Army</a> engages <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops and <a href=\"https://wikipedia.org/wiki/Francoist_Spain\" title=\"Francoist Spain\">Spanish</a> volunteers in the <a href=\"https://wikipedia.org/wiki/Battle_of_Krasny_Bor\" title=\"Battle of Krasny Bor\">Battle of Krasny Bor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Attempting to completely lift the <a href=\"https://wikipedia.org/wiki/Siege_of_Leningrad\" title=\"Siege of Leningrad\">Siege of Leningrad</a>, the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Soviet Red Army</a> engages <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops and <a href=\"https://wikipedia.org/wiki/Francoist_Spain\" title=\"Francoist Spain\">Spanish</a> volunteers in the <a href=\"https://wikipedia.org/wiki/Battle_of_Krasny_Bor\" title=\"Battle of Krasny Bor\">Battle of Krasny Bor</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Siege of Leningrad", "link": "https://wikipedia.org/wiki/Siege_of_Leningrad"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Francoist Spain", "link": "https://wikipedia.org/wiki/Francoist_Spain"}, {"title": "Battle of Krasny Bor", "link": "https://wikipedia.org/wiki/Battle_of_Krasny_Bor"}]}, {"year": "1947", "text": "The Paris Peace Treaties are signed by Italy, Romania, Hungary, Bulgaria, Finland and the Allies of World War II.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Paris_Peace_Treaties,_1947\" title=\"Paris Peace Treaties, 1947\">Paris Peace Treaties</a> are signed by Italy, Romania, Hungary, Bulgaria, Finland and the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allies of World War II</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Paris_Peace_Treaties,_1947\" title=\"Paris Peace Treaties, 1947\">Paris Peace Treaties</a> are signed by Italy, Romania, Hungary, Bulgaria, Finland and the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allies of World War II</a>.", "links": [{"title": "Paris Peace Treaties, 1947", "link": "https://wikipedia.org/wiki/Paris_Peace_Treaties,_1947"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}]}, {"year": "1954", "text": "U.S. President <PERSON> warns against United States intervention in Vietnam.", "html": "1954 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> warns against United States intervention in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> warns against United States intervention in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1962", "text": "Cold War: Captured American U2 spy-plane pilot <PERSON> is exchanged for captured Soviet spy <PERSON>.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Captured American <a href=\"https://wikipedia.org/wiki/Lockheed_U-2\" title=\"Lockheed U-2\">U2 spy-plane</a> pilot <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is exchanged for captured <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> spy <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Captured American <a href=\"https://wikipedia.org/wiki/Lockheed_U-2\" title=\"Lockheed U-2\">U2 spy-plane</a> pilot <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is exchanged for captured <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> spy <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Lockheed U-2", "link": "https://wikipedia.org/wiki/Lockheed_U-2"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "Melbourne-Voyager collision: The aircraft carrier HMAS Melbourne collides with and sinks the destroyer HMAS Voyager off the south coast of New South Wales, Australia, killing 82.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Melbourne%E2%80%93Voyager_collision\" title=\"Melbourne-Voyager collision\">Melbourne-Voyager collision</a>: The aircraft carrier <a href=\"https://wikipedia.org/wiki/HMAS_Melbourne_(R21)\" title=\"HMAS Melbourne (R21)\">HMAS <i>Melbourne</i></a> collides with and sinks the destroyer <a href=\"https://wikipedia.org/wiki/HMAS_Voyager_(D04)\" title=\"HMAS Voyager (D04)\">HMAS <i>Voyager</i></a> off the south coast of <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a>, Australia, killing 82.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Melbourne%E2%80%93Voyager_collision\" title=\"Melbourne-Voyager collision\">Melbourne-Voyager collision</a>: The aircraft carrier <a href=\"https://wikipedia.org/wiki/HMAS_Melbourne_(R21)\" title=\"HMAS Melbourne (R21)\">HMAS <i>Melbourne</i></a> collides with and sinks the destroyer <a href=\"https://wikipedia.org/wiki/HMAS_Voyager_(D04)\" title=\"HMAS Voyager (D04)\">HMAS <i>Voyager</i></a> off the south coast of <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a>, Australia, killing 82.", "links": [{"title": "Melbourne-Voyager collision", "link": "https://wikipedia.org/wiki/Melbourne%E2%80%93Voyager_collision"}, {"title": "HMAS Melbourne (R21)", "link": "https://wikipedia.org/wiki/HMAS_Melbourne_(R21)"}, {"title": "HMAS Voyager (D04)", "link": "https://wikipedia.org/wiki/HMAS_Voyager_(D04)"}, {"title": "New South Wales", "link": "https://wikipedia.org/wiki/New_South_Wales"}]}, {"year": "1967", "text": "The 25th Amendment to the United States Constitution is ratified.", "html": "1967 - The <a href=\"https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution\" title=\"Twenty-fifth Amendment to the United States Constitution\">25th Amendment to the United States Constitution</a> is ratified.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution\" title=\"Twenty-fifth Amendment to the United States Constitution\">25th Amendment to the United States Constitution</a> is ratified.", "links": [{"title": "Twenty-fifth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution"}]}, {"year": "1972", "text": "<PERSON><PERSON> joins the United Arab Emirates, now making up seven emirates.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Khaimah\" class=\"mw-redirect\" title=\"Ras al-Khaimah\"><PERSON><PERSON>haimah</a> joins the <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a>, now making up seven emirates.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Ras al-Khaimah\"><PERSON><PERSON>mah</a> joins the <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a>, now making up seven emirates.", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "United Arab Emirates", "link": "https://wikipedia.org/wiki/United_Arab_Emirates"}]}, {"year": "1984", "text": "Kenyan soldiers kill an estimated 5,000 ethnic Somali Kenyans in the Wagalla massacre.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Kenya_Army\" title=\"Kenya Army\">Kenyan soldiers</a> kill an estimated 5,000 ethnic Somali Kenyans in the <a href=\"https://wikipedia.org/wiki/Wagalla_massacre\" title=\"Wagalla massacre\">Wagalla massacre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kenya_Army\" title=\"Kenya Army\">Kenyan soldiers</a> kill an estimated 5,000 ethnic Somali Kenyans in the <a href=\"https://wikipedia.org/wiki/Wagalla_massacre\" title=\"Wagalla massacre\">Wagalla massacre</a>.", "links": [{"title": "Kenya Army", "link": "https://wikipedia.org/wiki/Kenya_Army"}, {"title": "Wagalla massacre", "link": "https://wikipedia.org/wiki/Waga<PERSON>_massacre"}]}, {"year": "1989", "text": "<PERSON> is elected chairman of the Democratic National Committee, becoming the first African American to lead a major American political party.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected chairman of the <a href=\"https://wikipedia.org/wiki/Democratic_National_Committee\" title=\"Democratic National Committee\">Democratic National Committee</a>, becoming the first <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African American</a> to lead a major American <a href=\"https://wikipedia.org/wiki/Political_party\" title=\"Political party\">political party</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected chairman of the <a href=\"https://wikipedia.org/wiki/Democratic_National_Committee\" title=\"Democratic National Committee\">Democratic National Committee</a>, becoming the first <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African American</a> to lead a major American <a href=\"https://wikipedia.org/wiki/Political_party\" title=\"Political party\">political party</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Democratic National Committee", "link": "https://wikipedia.org/wiki/Democratic_National_Committee"}, {"title": "African Americans", "link": "https://wikipedia.org/wiki/African_Americans"}, {"title": "Political party", "link": "https://wikipedia.org/wiki/Political_party"}]}, {"year": "1996", "text": "IBM supercomputer Deep Blue defeats <PERSON> in chess for the first time.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/IBM\" title=\"IBM\">IBM</a> supercomputer <a href=\"https://wikipedia.org/wiki/Deep_Blue_(chess_computer)\" title=\"Deep Blue (chess computer)\">Deep Blue</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in chess for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/IBM\" title=\"IBM\">IBM</a> supercomputer <a href=\"https://wikipedia.org/wiki/Deep_Blue_(chess_computer)\" title=\"Deep Blue (chess computer)\">Deep Blue</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in chess for the first time.", "links": [{"title": "IBM", "link": "https://wikipedia.org/wiki/IBM"}, {"title": "<PERSON> Blue (chess computer)", "link": "https://wikipedia.org/wiki/Deep_Blue_(chess_computer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "France and Belgium break the NATO procedure of silent approval concerning the timing of protective measures for Turkey in case of a possible war with Iraq.", "html": "2003 - France and Belgium break the <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> procedure of silent approval concerning the timing of protective measures for <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> in case of a possible war with <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>.", "no_year_html": "France and Belgium break the <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> procedure of silent approval concerning the timing of protective measures for <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> in case of a possible war with <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>.", "links": [{"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "2004", "text": "Forty-three people are killed and three are injured when a Fokker 50 crashes near Sharjah International Airport.", "html": "2004 - Forty-three people are killed and three are injured when a <a href=\"https://wikipedia.org/wiki/Fokker_50\" title=\"Fokker 50\">Fokker 50</a> <a href=\"https://wikipedia.org/wiki/Kish_Air_Flight_7170\" title=\"Kish Air Flight 7170\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Sharjah_International_Airport\" title=\"Sharjah International Airport\">Sharjah International Airport</a>.", "no_year_html": "Forty-three people are killed and three are injured when a <a href=\"https://wikipedia.org/wiki/Fokker_50\" title=\"Fokker 50\">Fokker 50</a> <a href=\"https://wikipedia.org/wiki/Kish_Air_Flight_7170\" title=\"Kish Air Flight 7170\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Sharjah_International_Airport\" title=\"Sharjah International Airport\">Sharjah International Airport</a>.", "links": [{"title": "Fokker 50", "link": "https://wikipedia.org/wiki/Fokker_50"}, {"title": "Kish Air Flight 7170", "link": "https://wikipedia.org/wiki/Kish_Air_Flight_7170"}, {"title": "Sharjah International Airport", "link": "https://wikipedia.org/wiki/Sharjah_International_Airport"}]}, {"year": "2009", "text": "The communications satellites Iridium 33 and Kosmos 2251 collide in orbit, destroying both.", "html": "2009 - The <a href=\"https://wikipedia.org/wiki/Communications_satellite\" title=\"Communications satellite\">communications satellites</a> <a href=\"https://wikipedia.org/wiki/Iridium_33\" title=\"Iridium 33\">Iridium 33</a> and <a href=\"https://wikipedia.org/wiki/Kosmos_2251\" title=\"Kosmos 2251\">Kosmos 2251</a> <a href=\"https://wikipedia.org/wiki/2009_satellite_collision\" title=\"2009 satellite collision\">collide in orbit</a>, destroying both.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Communications_satellite\" title=\"Communications satellite\">communications satellites</a> <a href=\"https://wikipedia.org/wiki/Iridium_33\" title=\"Iridium 33\">Iridium 33</a> and <a href=\"https://wikipedia.org/wiki/Kosmos_2251\" title=\"Kosmos 2251\">Kosmos 2251</a> <a href=\"https://wikipedia.org/wiki/2009_satellite_collision\" title=\"2009 satellite collision\">collide in orbit</a>, destroying both.", "links": [{"title": "Communications satellite", "link": "https://wikipedia.org/wiki/Communications_satellite"}, {"title": "Iridium 33", "link": "https://wikipedia.org/wiki/Iridium_33"}, {"title": "Kosmos 2251", "link": "https://wikipedia.org/wiki/Kosmos_2251"}, {"title": "2009 satellite collision", "link": "https://wikipedia.org/wiki/2009_satellite_collision"}]}, {"year": "2013", "text": "Thirty-six people are killed and 39 others are injured in a stampede in Allahabad, India, during the Kumbh Mela festival.", "html": "2013 - Thirty-six people are killed and 39 others are injured in a <a href=\"https://wikipedia.org/wiki/2013_Kumbh_Mela_stampede\" class=\"mw-redirect\" title=\"2013 Kumbh Mela stampede\">stampede</a> in <a href=\"https://wikipedia.org/wiki/Allahabad\" class=\"mw-redirect\" title=\"Allahabad\">Allahabad</a>, India, during the <a href=\"https://wikipedia.org/wiki/Kumbh_Mela\" title=\"Kumbh Mela\">Kumbh Mela</a> festival.", "no_year_html": "Thirty-six people are killed and 39 others are injured in a <a href=\"https://wikipedia.org/wiki/2013_Kumbh_Mela_stampede\" class=\"mw-redirect\" title=\"2013 Kumbh Mela stampede\">stampede</a> in <a href=\"https://wikipedia.org/wiki/Allahabad\" class=\"mw-redirect\" title=\"Allahabad\">Allahabad</a>, India, during the <a href=\"https://wikipedia.org/wiki/Kumbh_Mela\" title=\"Kumbh Mela\">Kumbh Mela</a> festival.", "links": [{"title": "2013 Kumbh Mela stampede", "link": "https://wikipedia.org/wiki/2013_Ku<PERSON><PERSON>_<PERSON>a_stampede"}, {"title": "Allahabad", "link": "https://wikipedia.org/wiki/Allahabad"}, {"title": "<PERSON><PERSON><PERSON> Mel<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "2016", "text": "South Korea decides to stop the operation of the Kaesong joint industrial complex with North Korea in response to the launch of Kwangmyŏngsŏng-4.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a> decides to stop the <a href=\"https://wikipedia.org/wiki/Kaesong_Industrial_Region\" title=\"Kaesong Industrial Region\">operation of the Kaesong joint industrial complex</a> with <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> in response to the launch of <a href=\"https://wikipedia.org/wiki/Kwangmy%C5%8Fngs%C5%8Fng-4\" title=\"Kwangmyŏngsŏng-4\">Kwangmyŏngsŏng-4</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a> decides to stop the <a href=\"https://wikipedia.org/wiki/Kaesong_Industrial_Region\" title=\"Kaesong Industrial Region\">operation of the Kaesong joint industrial complex</a> with <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> in response to the launch of <a href=\"https://wikipedia.org/wiki/Kwangmy%C5%8Fngs%C5%8Fng-4\" title=\"Kwangmyŏngsŏng-4\">Kwangmyŏngsŏng-4</a>.", "links": [{"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}, {"title": "Kaesong Industrial Region", "link": "https://wikipedia.org/wiki/Kaesong_Industrial_Region"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Kwangmyŏngsŏng-4", "link": "https://wikipedia.org/wiki/Kwangmy%C5%8Fngs%C5%8Fng-4"}]}, {"year": "2018", "text": "Nineteen people are killed and 66 injured when a Kowloon Motor Bus double decker on route 872 in Hong Kong overturns.", "html": "2018 - Nineteen people are killed and 66 injured when a <a href=\"https://wikipedia.org/wiki/Kowloon_Motor_Bus\" title=\"Kowloon Motor Bus\">Kowloon Motor Bus</a> <a href=\"https://wikipedia.org/wiki/Double-decker_bus\" title=\"Double-decker bus\">double decker</a> on route 872 in <a href=\"https://wikipedia.org/wiki/Hong_Kong\" title=\"Hong Kong\">Hong Kong</a> <a href=\"https://wikipedia.org/wiki/2018_Hong_Kong_bus_accident\" title=\"2018 Hong Kong bus accident\">overturns</a>.", "no_year_html": "Nineteen people are killed and 66 injured when a <a href=\"https://wikipedia.org/wiki/Kowloon_Motor_Bus\" title=\"Kowloon Motor Bus\">Kowloon Motor Bus</a> <a href=\"https://wikipedia.org/wiki/Double-decker_bus\" title=\"Double-decker bus\">double decker</a> on route 872 in <a href=\"https://wikipedia.org/wiki/Hong_Kong\" title=\"Hong Kong\">Hong Kong</a> <a href=\"https://wikipedia.org/wiki/2018_Hong_Kong_bus_accident\" title=\"2018 Hong Kong bus accident\">overturns</a>.", "links": [{"title": "Kowloon Motor Bus", "link": "https://wikipedia.org/wiki/Kowloon_Motor_Bus"}, {"title": "Double-decker bus", "link": "https://wikipedia.org/wiki/Double-decker_bus"}, {"title": "Hong Kong", "link": "https://wikipedia.org/wiki/Hong_Kong"}, {"title": "2018 Hong Kong bus accident", "link": "https://wikipedia.org/wiki/2018_Hong_Kong_bus_accident"}]}, {"year": "2021", "text": "The traditional Carnival in Rio de Janeiro, Brazil is canceled for the first time because of the COVID-19 pandemic.", "html": "2021 - The traditional <a href=\"https://wikipedia.org/wiki/Rio_Carnival\" title=\"Rio Carnival\">Carnival in Rio de Janeiro</a>, Brazil is canceled for the first time because of the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_Brazil\" title=\"COVID-19 pandemic in Brazil\">COVID-19 pandemic</a>.", "no_year_html": "The traditional <a href=\"https://wikipedia.org/wiki/Rio_Carnival\" title=\"Rio Carnival\">Carnival in Rio de Janeiro</a>, Brazil is canceled for the first time because of the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_Brazil\" title=\"COVID-19 pandemic in Brazil\">COVID-19 pandemic</a>.", "links": [{"title": "Rio Carnival", "link": "https://wikipedia.org/wiki/Rio_Carnival"}, {"title": "COVID-19 pandemic in Brazil", "link": "https://wikipedia.org/wiki/COVID-19_pandemic_in_Brazil"}]}, {"year": "2021", "text": "Texas' worst energy infrastructure failure, the 2021 Texas power crisis, starts.", "html": "2021 - Texas' worst energy infrastructure failure, the <a href=\"https://wikipedia.org/wiki/2021_Texas_power_crisis\" title=\"2021 Texas power crisis\">2021 Texas power crisis</a>, starts.", "no_year_html": "Texas' worst energy infrastructure failure, the <a href=\"https://wikipedia.org/wiki/2021_Texas_power_crisis\" title=\"2021 Texas power crisis\">2021 Texas power crisis</a>, starts.", "links": [{"title": "2021 Texas power crisis", "link": "https://wikipedia.org/wiki/2021_Texas_power_crisis"}]}], "Births": [{"year": "1486", "text": "<PERSON> of the Palatinate, German bishop (d. 1529)", "html": "1486 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Palatinate\" title=\"<PERSON> of the Palatinate\"><PERSON> of the Palatinate</a>, German bishop (d. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Palatinate\" title=\"<PERSON> of the Palatinate\"><PERSON> of the Palatinate</a>, German bishop (d. 1529)", "links": [{"title": "<PERSON> of the Palatinate", "link": "https://wikipedia.org/wiki/<PERSON>_of_the_Palatinate"}]}, {"year": "1499", "text": "<PERSON>, Swiss author and scholar (d. 1582)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and scholar (d. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and scholar (d. 1582)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1514", "text": "<PERSON>, Bishop of Milan (d. 1579)", "html": "1514 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of Milan (d. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of Milan (d. 1579)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1606", "text": "<PERSON> of France, Duchess of Savoy (d. 1663)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France\" title=\"Christine of France\"><PERSON> of France</a>, Duchess of Savoy (d. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France\" title=\"<PERSON> of France\"><PERSON> of France</a>, Duchess of Savoy (d. 1663)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_France"}]}, {"year": "1609", "text": "<PERSON>, English poet and playwright (d. 1642)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and playwright (d. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and playwright (d. 1642)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1627", "text": "<PERSON><PERSON><PERSON>, Flemish poet and jurist (d. 1715)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_<PERSON>\" title=\"<PERSON><PERSON><PERSON> de B<PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish poet and jurist (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_<PERSON>\" title=\"Corne<PERSON> de Bie\"><PERSON><PERSON><PERSON></a>, Flemish poet and jurist (d. 1715)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ie"}]}, {"year": "1685", "text": "<PERSON>, English poet and playwright (d. 1750)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English poet and playwright (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English poet and playwright (d. 1750)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1696", "text": "<PERSON>, German violinist and composer (d. 1765)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (d. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, American botanist and physician (d. 1815)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and physician (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and physician (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON>, English poet and essayist (d. 1834)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and essayist (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and essayist (d. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON><PERSON><PERSON>, French physicist and engineer (d. 1836)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and engineer (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and engineer (d. 1836)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1795", "text": "<PERSON><PERSON>, Dutch-French painter and academic (d. 1858)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-French painter and academic (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-French painter and academic (d. 1858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, 3rd Marquess of Donegall (d. 1883)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Marquess_of_Donegall\" title=\"<PERSON>, 3rd Marquess of Donegall\"><PERSON>, 3rd Marquess of Donegall</a> (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Marquess_of_Donegall\" title=\"<PERSON>, 3rd Marquess of Donegall\"><PERSON>, 3rd Marquess of Donegall</a> (d. 1883)", "links": [{"title": "<PERSON>, 3rd Marquess of Donegall", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Marquess_of_Donegall"}]}, {"year": "1821", "text": "<PERSON>, Italian painter and sculptor (d. 1908)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and sculptor (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and sculptor (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, English merchant and politician (d. 1898)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English merchant and politician (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English merchant and politician (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, Russian surgeon of Polish-German origin (d. 1868)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian surgeon of Polish-German origin (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian surgeon of Polish-German origin (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, Irish astronomer and author (d. 1907)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish astronomer and author (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish astronomer and author (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON><PERSON><PERSON>, Italian-French opera singer (d. 1919)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French <a href=\"https://wikipedia.org/wiki/Opera\" title=\"Opera\">opera</a> singer (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French <a href=\"https://wikipedia.org/wiki/Opera\" title=\"Opera\">opera</a> singer (d. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Opera", "link": "https://wikipedia.org/wiki/Opera"}]}, {"year": "1846", "text": "Lord <PERSON>, Irish admiral and politician (d. 1919)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, Irish admiral and politician (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, Irish admiral and politician (d. 1919)", "links": [{"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, American chemist and academic (d. 1927)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1847", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi poet and author (d. 1909)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi poet and author (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi poet and author (d. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, French lawyer and politician, 12th President of France (d. 1943)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1867", "text": "<PERSON>, Australian lawyer and public servant (d. 1957)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and public servant (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and public servant (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON> of Prussia (d. 1879)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Prussia_(1868%E2%80%931879)\" title=\"Prince <PERSON> of Prussia (1868-1879)\">Prince <PERSON> of Prussia</a> (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Prussia_(1868%E2%80%931879)\" title=\"Prince <PERSON> of Prussia (1868-1879)\">Prince <PERSON> of Prussia</a> (d. 1879)", "links": [{"title": "<PERSON> <PERSON><PERSON> of Prussia (1868-1879)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_of_Prussia_(1868%E2%80%931879)"}]}, {"year": "1868", "text": "<PERSON>, American journalist and author (d. 1944)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American art critic (d. 1948)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Royal_Cortissoz\" title=\"Royal Cortissoz\"><PERSON> Cort<PERSON></a>, American art critic (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Royal_Cortissoz\" title=\"Royal Cortissoz\"><PERSON> Cort<PERSON>oz</a>, American art critic (d. 1948)", "links": [{"title": "Royal Cortissoz", "link": "https://wikipedia.org/wiki/Royal_Cortissoz"}]}, {"year": "1879", "text": "<PERSON>, Estonian general (d. 1932)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B5dder\" title=\"<PERSON>\"><PERSON></a>, Estonian general (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B5dder\" title=\"<PERSON>\"><PERSON></a>, Estonian general (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_P%C3%B5dder"}]}, {"year": "1881", "text": "<PERSON>, Swedish actress and director (d. 1954)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress and director (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress and director (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American electrical engineer (d. 1959)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American electrical engineer (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American electrical engineer (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON> <PERSON><PERSON>, Australian cricketer (d. 1938)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian cricketer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON>\" title=\"H. V. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian cricketer (d. 1938)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>rn"}]}, {"year": "1888", "text": "<PERSON>, Egyptian-Italian soldier, journalist, and poet (d. 1970)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-Italian soldier, journalist, and poet (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-Italian soldier, journalist, and poet (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, Turkish general and politician, 5th President of Turkey (d. 1982)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish general and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish general and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cevdet_Sunay"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1890", "text": "<PERSON>, Ukrainian-Russian activist (d. 1918)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian activist (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian activist (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Russian poet, novelist, and literary translator Nobel Prize laureate (d. 1960)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet, novelist, and literary translator <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet, novelist, and literary translator <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1892", "text": "<PERSON>, American actor and director (d. 1950)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American actor and director (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American actor and director (d. 1950)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1893", "text": "<PERSON>, American actor, singer, and pianist (d. 1980)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and pianist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and pianist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American tennis player and coach (d. 1953)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, English captain and politician, Prime Minister of the United Kingdom (d. 1986)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1897", "text": "<PERSON>, Australian actress (d. 1992)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American virologist and academic, Nobel Prize laureate (d. 1985)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1898", "text": "<PERSON><PERSON>, German director, playwright, and poet (d. 1956)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brecht\"><PERSON><PERSON></a>, German director, playwright, and poet (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brecht\"><PERSON><PERSON></a>, German director, playwright, and poet (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>recht"}]}, {"year": "1898", "text": "<PERSON>, French journalist and author (d. 1979)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American actress and educator (d. 1992)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and educator (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Adler\"><PERSON></a>, American actress and educator (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Adler"}]}, {"year": "1902", "text": "<PERSON>, Chinese-American physicist and academic, Nobel Prize laureate (d. 1987)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON><PERSON>, German physician (d. 1948)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Waldemar_<PERSON><PERSON>\" title=\"Wald<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German physician (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wald<PERSON>r_<PERSON><PERSON>\" title=\"Wald<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German physician (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waldemar_Hoven"}]}, {"year": "1903", "text": "<PERSON>, Austrian footballer and manager (d. 1939)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Australian-American director, producer, and screenwriter (d. 1963)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American director, producer, and screenwriter (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American director, producer, and screenwriter (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American businessman, founded the Boston Celtics (d. 1964)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Boston_Celtics\" title=\"Boston Celtics\">Boston Celtics</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Boston_Celtics\" title=\"Boston Celtics\">Boston Celtics</a> (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Boston Celtics", "link": "https://wikipedia.org/wiki/Boston_Celtics"}]}, {"year": "1905", "text": "<PERSON><PERSON>, American drummer and bandleader (d. 1939)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer and bandleader (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer and bandleader (d. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, American actor (d. 1973)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, American actor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>.</a>, American actor (d. 1973)", "links": [{"title": "<PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>."}]}, {"year": "1907", "text": "<PERSON>, New Zealand rugby player (d. 1988)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 1988)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1908", "text": "<PERSON>, Canadian composer and educator (d. 2000)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and educator (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and educator (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Burmese poet, scholar, and politician (d. 2004)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burmese poet, scholar, and politician (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>hu Wu<PERSON>\"><PERSON></a>, Burmese poet, scholar, and politician (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1910", "text": "<PERSON>, Belgian friar, Nobel Prize laureate (d. 1969)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian friar, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian friar, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1914", "text": "<PERSON>, American harmonica player, composer, and actor (d. 2001)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American harmonica player, composer, and actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American harmonica player, composer, and actor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Russian actor (d. 2016)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Greek colonel and politician, Deputy Prime Minister of Greece (d. 2014)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek colonel and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece\" title=\"Deputy Prime Minister of Greece\">Deputy Prime Minister of Greece</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek colonel and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece\" title=\"Deputy Prime Minister of Greece\">Deputy Prime Minister of Greece</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece"}]}, {"year": "1920", "text": "<PERSON>, Spanish lawyer and author (d. 2001)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%B1%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%B1%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%B1%C3%B3n"}]}, {"year": "1920", "text": "<PERSON>, English physician and author (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alex_Comfort"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American actress (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actress (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian author, playwright, and politician, 1st President of Hungary (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1d_G%C3%B6ncz\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author, playwright, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Hungary\" title=\"President of Hungary\">President of Hungary</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1d_G%C3%B6ncz\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author, playwright, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Hungary\" title=\"President of Hungary\">President of Hungary</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81rp%C3%A1d_G%C3%B6ncz"}, {"title": "President of Hungary", "link": "https://wikipedia.org/wiki/President_of_Hungary"}]}, {"year": "1922", "text": "<PERSON>, Brazilian spiritual leader, founder of the União do Vegetal (d. 1971)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian spiritual leader, founder of the <a href=\"https://wikipedia.org/wiki/Uni%C3%A3o_do_Vegetal\" title=\"União do Vegetal\">União do Vegetal</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian spiritual leader, founder of the <a href=\"https://wikipedia.org/wiki/Uni%C3%A3o_do_Vegetal\" title=\"União do Vegetal\">União do Vegetal</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "União do Vegetal", "link": "https://wikipedia.org/wiki/Uni%C3%A3o_do_Vegetal"}]}, {"year": "1923", "text": "<PERSON>, American football player and coach (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Canadian radio host and actor (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio host and actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio host and actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bud Poile\"><PERSON></a>, Canadian ice hockey player and coach (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bud Poile\"><PERSON></a>, Canadian ice hockey player and coach (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bud_Poile"}]}, {"year": "1925", "text": "<PERSON>, French actor and director (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American general (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Northern Irish soldier, footballer and manager (d. 1993)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish soldier, footballer and manager (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish soldier, footballer and manager (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, American operatic soprano", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American operatic soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American operatic soprano", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American composer and conductor (d. 2004)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American mountaineer", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American mountaineer (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and illustrator (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/E<PERSON>_<PERSON>._<PERSON>nigsburg\" title=\"E. L. Koni<PERSON>burg\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E._<PERSON>._<PERSON>nigsburg\" title=\"E. L. Konigsburg\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator (d. 2013)", "links": [{"title": "E. L. Konigsburg", "link": "https://wikipedia.org/wiki/E<PERSON>_<PERSON><PERSON>_Konigsburg"}]}, {"year": "1930", "text": "<PERSON>, American actor and producer", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American inventor and acoustician", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American inventor and acoustician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American inventor and acoustician", "links": [{"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(inventor)"}]}, {"year": "1932", "text": "<PERSON><PERSON>, English-American actor (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actor (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Iranian musician and composer (d. 2009)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Farama<PERSON>_<PERSON>\" title=\"Far<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian musician and composer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Farama<PERSON>_<PERSON>\" title=\"Far<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian musician and composer (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Far<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American journalist, author, and critic (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and critic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and critic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, New Zealand poet (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ur_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand poet (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ur_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>cock\"><PERSON><PERSON><PERSON></a>, New Zealand poet (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>cock"}]}, {"year": "1935", "text": "<PERSON>, Greek composer and conductor (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek composer and conductor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek composer and conductor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American vocal coach and singer (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American vocal coach and singer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American vocal coach and singer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Scottish physiologist and academic (d. 1983)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(researcher)\" title=\"<PERSON> (researcher)\"><PERSON></a>, Scottish physiologist and academic (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(researcher)\" title=\"<PERSON> (researcher)\"><PERSON></a>, Scottish physiologist and academic (d. 1983)", "links": [{"title": "<PERSON> (researcher)", "link": "https://wikipedia.org/wiki/<PERSON>_(researcher)"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter and pianist (d. 2025)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Hong Kong-Canadian journalist and politician, 26th Governor General of Canada", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hong Kong-Canadian journalist and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hong Kong-Canadian journalist and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Angolan nationalist (d. 1967)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Deolinda_Rodr%C3%ADguez_de_Almeida\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> de Almeida\"><PERSON><PERSON><PERSON> Almeida</a>, Angolan nationalist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Deolinda_Rodr%C3%ADguez_de_Almeida\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Almeida\"><PERSON><PERSON><PERSON> Almeida</a>, Angolan nationalist (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON> Al<PERSON>", "link": "https://wikipedia.org/wiki/Deolinda_Rodr%C3%ADguez_de_Almeida"}]}, {"year": "1940", "text": "<PERSON>, English sprinter and long jumper", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mary <PERSON>\"><PERSON></a>, English sprinter and long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mary <PERSON>\"><PERSON></a>, English sprinter and long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter (d. 2009)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English director and producer (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Australian singer-songwriter, pianist, and actor (d. 1992)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian singer-songwriter, pianist, and actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian singer-songwriter, pianist, and actor (d. 1992)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1944", "text": "<PERSON>, American lawyer and politician, 25th Governor of Oklahoma", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor_of_Oklahoma\" title=\"Governor of Oklahoma\">Governor of Oklahoma</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor_of_Oklahoma\" title=\"Governor of Oklahoma\">Governor of Oklahoma</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Oklahoma", "link": "https://wikipedia.org/wiki/Governor_of_Oklahoma"}]}, {"year": "1944", "text": "<PERSON>, American author and activist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1944", "text": "<PERSON>, American bassist and composer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Puerto Rican historian, author, educator and lawyer (d. 2023)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Delma_S._Arrigoitia\" title=\"Delma S. Arrigoitia\">Delma S. Arrigo<PERSON>a</a>, Puerto Rican historian, author, educator and lawyer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Delma_S._Arrigoitia\" title=\"Delma S. Arrigoitia\">Delma S. Arrigoitia</a>, Puerto Rican historian, author, educator and lawyer (d. 2023)", "links": [{"title": "Delma S. Arrigoitia", "link": "https://wikipedia.org/wiki/Delma_S._Arrigoitia"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American basketball player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Saulters\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Saul<PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American football player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian lawyer and jurist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American cornet player, composer, and conductor (d. 2013)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cornet player, composer, and conductor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cornet player, composer, and conductor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English journalist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1949", "text": "<PERSON>, English rock drummer and singer-songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock drummer and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock drummer and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Mexican economist and politician (d. 1994)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican economist and politician (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican economist and politician (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American swimmer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American media executive", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American media executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American media executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Singaporean general and politician, 3rd Prime Minister of Singapore", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Singapore\" title=\"Prime Minister of Singapore\">Prime Minister of Singapore</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Singapore\" title=\"Prime Minister of Singapore\">Prime Minister of Singapore</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Singapore", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Singapore"}]}, {"year": "1955", "text": "<PERSON>, American television personality, pundit, and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality, pundit, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality, pundit, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American basketball player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian golfer and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American Roman Catholic priest (d. 1997)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic priest (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic priest (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Tuvaluan politician, 12th Prime Minister of Tuvalu", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tuvaluan politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu\" title=\"Prime Minister of Tuvalu\">Prime Minister of Tuvalu</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tuvaluan politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu\" title=\"Prime Minister of Tuvalu\">Prime Minister of Tuvalu</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Tuvalu", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu"}]}, {"year": "1957", "text": "<PERSON>, American astrophysicist and academic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American basketball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American biologist, computer programmer, academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, computer programmer, academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, computer programmer, academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American television journalist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American musician and songwriter (d. 1986)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Cliff_Burton\" title=\"Cliff Burton\"><PERSON></a>, American musician and songwriter (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cliff_Burton\" title=\"Cliff Burton\"><PERSON></a>, American musician and songwriter (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cliff_Burton"}]}, {"year": "1962", "text": "<PERSON>, American boxer and commentator", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and commentator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American baseball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American journalist, producer, and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, producer, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, producer, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian-English journalist and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American football player and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actress, director, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, French cyclist and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cyclist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cyclist and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Swedish ice hockey player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American engineer and astronaut", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American painter and sculptor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, South African rugby player (d. 2019)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, South African rugby player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, South African rugby player (d. 2019)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1970", "text": "<PERSON>, Australian journalist and author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Moroccan international footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Noureddine_Naybet\" title=\"Noureddine Naybet\">Noureddine Naybet</a>, Moroccan international footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Noureddine_Naybet\" title=\"Noureddine Naybet\"><PERSON><PERSON><PERSON>ybet</a>, Moroccan international footballer and manager", "links": [{"title": "Noureddine Naybet", "link": "https://wikipedia.org/wiki/Noureddine_Naybet"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Norwegian journalist and author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/%C3%85sne_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%85sne_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%85sne_<PERSON><PERSON>tad"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Mexican actress and singer (d. 2015)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress and singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress and singer (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Baroness <PERSON>-<PERSON> of Soho, English businesswoman and politician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Baroness_<PERSON>-Fox_of_Soho\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of Soho\"><PERSON>, Baroness <PERSON> of Soho</a>, English businesswoman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Baroness_<PERSON>-<PERSON>_of_Soho\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of Soho\"><PERSON>, Baroness <PERSON> of Soho</a>, English businesswoman and politician", "links": [{"title": "<PERSON>, Baroness <PERSON>-<PERSON> of Soho", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>-<PERSON>_of_Soho"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elizabeth_Banks"}]}, {"year": "1974", "text": "<PERSON>, American football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ty_Law\" title=\"Ty Law\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ty_Law\" title=\"Ty Law\"><PERSON></a>, American football player", "links": [{"title": "Ty Law", "link": "https://wikipedia.org/wiki/Ty_Law"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Israeli singer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Iv<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>vri <PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv<PERSON>_<PERSON>\" title=\"<PERSON>vri <PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv<PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, New Zealand rugby player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1975", "text": "<PERSON>, American basketball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, English actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Senegalese footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ao"}]}, {"year": "1978", "text": "<PERSON>, Puerto Rican rapper, singer, producer, and actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican rapper, singer, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican rapper, singer, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American race car driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hand\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Venezuelan baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Italian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>sca"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Croatian basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0<PERSON>ov\" title=\"<PERSON>\"><PERSON></a>, Croatian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C5%A0<PERSON>ov"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>uba"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_actor)\" title=\"<PERSON> (British actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_actor)\" title=\"<PERSON> (British actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (British actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_actor)"}]}, {"year": "1981", "text": "<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1981)\" title=\"<PERSON> (footballer, born 1981)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1981)\" title=\"<PERSON> (footballer, born 1981)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1981)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1981)"}]}, {"year": "1981", "text": "<PERSON>, English actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English model and television host", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Kuwaiti footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ad Al-Tayya<PERSON>\"><PERSON><PERSON></a>, Kuwaiti footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>Tayya<PERSON>\"><PERSON><PERSON></a>, Kuwaiti footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American sprinter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Tarm<PERSON>_N<PERSON>o\" title=\"<PERSON><PERSON><PERSON> Nee<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tarm<PERSON>_<PERSON>\" title=\"<PERSON>rm<PERSON> Neemelo\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tarm<PERSON>_<PERSON><PERSON>o"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, New Zealand rugby league player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Iaf<PERSON>_<PERSON>lea<PERSON>sin<PERSON>\" title=\"Iafeta Paleaaesina\"><PERSON>af<PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iaf<PERSON>_<PERSON>\" title=\"Iafeta Paleaaesina\">Iaf<PERSON></a>, New Zealand rugby league player", "links": [{"title": "Iafeta <PERSON>a", "link": "https://wikipedia.org/wiki/Iafeta_Palea<PERSON>sina"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, South Korean actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ji<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Georgian basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Zaza_Pachulia\" title=\"Zaza Pachulia\"><PERSON><PERSON><PERSON></a>, Georgian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zaza_Pachulia\" title=\"Zaza Pachulia\"><PERSON><PERSON><PERSON></a>, Georgian basketball player", "links": [{"title": "Zaza <PERSON>hul<PERSON>", "link": "https://wikipedia.org/wiki/Zaza_Pachulia"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Sel%C3%A7uk_%C4%B0nan\" title=\"Selçuk İnan\"><PERSON><PERSON><PERSON><PERSON> İnan</a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sel%C3%A7uk_%C4%B0nan\" title=\"Selçuk İnan\"><PERSON><PERSON><PERSON><PERSON> İnan</a>, Turkish footballer", "links": [{"title": "Selçuk İnan", "link": "https://wikipedia.org/wiki/Sel%C3%A7uk_%C4%B0nan"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Colombian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Radamel_Falcao\" title=\"Radamel Falcao\"><PERSON><PERSON><PERSON> Falcao</a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra<PERSON>el_Falcao\" title=\"Radamel Falcao\"><PERSON><PERSON><PERSON> Falcao</a>, Colombian footballer", "links": [{"title": "Radamel Falcao", "link": "https://wikipedia.org/wiki/Radamel_Falcao"}]}, {"year": "1986", "text": "<PERSON>, Spanish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nez_Gago\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nez_Gago\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nez_Gago"}]}, {"year": "1986", "text": "<PERSON>, Serbian tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_(ice_hockey)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentine footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Facundo_<PERSON>\" title=\"Facundo Ron<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Facund<PERSON>_<PERSON>\" title=\"Facundo <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "Facundo <PERSON>", "link": "https://wikipedia.org/wiki/Facundo_<PERSON>glia"}]}, {"year": "1988", "text": "<PERSON>, Italian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>i\" title=\"<PERSON> Acerbi\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>i\" title=\"<PERSON> Acerbi\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_Acerbi"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_d%27Arnaud\" title=\"<PERSON>'Arnaud\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_d%27Arnaud\" title=\"<PERSON> d'Arnaud\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_d%27A<PERSON>ud"}]}, {"year": "1989", "text": "<PERSON>, Australian baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Norwegian Paralympic athlete and social entrepreneur", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Bir<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian Paralympic athlete and social entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Bir<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian Paralympic athlete and social entrepreneur", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bir<PERSON>_<PERSON>stein"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, American actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trevant<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>-young, South Korean singer-songwriter, actress, and dancer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer-songwriter, actress, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer-songwriter, actress, and dancer", "links": [{"title": "<PERSON>young", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Scottish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Japanese singer and actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>in<PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>inhold <PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>in<PERSON>_<PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Egyptian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, German-American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Mexican footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Czech footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>k"}]}, {"year": "1994", "text": "<PERSON>, South Korean singer and actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Na-eun\" title=\"<PERSON> Na-eun\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Na-eun\" title=\"Son Na-eun\"><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON> <PERSON>-eun", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-eun"}]}, {"year": "1994", "text": "<PERSON>, South Korean singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-gi\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-gi\" class=\"mw-redirect\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>i", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-gi"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Paraguayan footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miguel_Almir%C3%B3n"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Canadian ice dancer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American golfer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Guinean footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Naby_Ke%C3%AFta\" title=\"Naby <PERSON>\"><PERSON><PERSON></a>, Guinean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Naby_Ke%C3%AFta\" title=\"Naby <PERSON>\"><PERSON><PERSON></a>, Guinean footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naby_Ke%C3%AFta"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Bulgarian-Russian ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian-Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian-Russian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v"}]}, {"year": "1996", "text": "<PERSON>, Argentine footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1997", "text": "<PERSON>, American swimmer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Chlo%C3%<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chlo%C3%<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Ch<PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chlo%C3%<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Argentine tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nadia_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, English footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1999", "text": "<PERSON>, American actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Espensen\" title=\"<PERSON> Espensen\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Espensen\" title=\"<PERSON> Espensen\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>n", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "2000", "text": "<PERSON>, Argentine tennis player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Carl%C3%A9\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Carl%C3%A9\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_Carl%C3%A9"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American actress and model", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}], "Deaths": [{"year": "547", "text": "<PERSON><PERSON><PERSON><PERSON>, Christian nun", "html": "547 - <a href=\"https://wikipedia.org/wiki/Scholastica\" title=\"Scholastica\">Scholastica</a>, Christian <a href=\"https://wikipedia.org/wiki/Nun\" title=\"Nun\">nun</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scholastica\" title=\"Scholastica\">Scholastica</a>, Christian <a href=\"https://wikipedia.org/wiki/Nun\" title=\"Nun\">nun</a>", "links": [{"title": "Scholastica", "link": "https://wikipedia.org/wiki/Scholastica"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nun"}]}, {"year": "1127", "text": "<PERSON>, Duke of Aquitaine (b. 1071)", "html": "1127 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON>, Duke of Aquitaine</a> (b. 1071)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON>, Duke of Aquitaine</a> (b. 1071)", "links": [{"title": "<PERSON>, Duke of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine"}]}, {"year": "1163", "text": "<PERSON> of Jerusalem (b. 1130)", "html": "1163 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jerusalem\" title=\"<PERSON> III of Jerusalem\"><PERSON> of Jerusalem</a> (b. 1130)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jerusalem\" title=\"<PERSON> III of Jerusalem\"><PERSON> of Jerusalem</a> (b. 1130)", "links": [{"title": "<PERSON> III of Jerusalem", "link": "https://wikipedia.org/wiki/Baldwin_III_of_Jerusalem"}]}, {"year": "1242", "text": "Emperor <PERSON><PERSON> of Japan (b. 1231)", "html": "1242 - <a href=\"https://wikipedia.org/wiki/Emperor_Shij%C5%8D\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (b. 1231)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Shij%C5%8D\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (b. 1231)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Shij%C5%8D"}]}, {"year": "1280", "text": "<PERSON>, Countess of Flanders (b. 1202)", "html": "1280 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Flanders\" title=\"<PERSON>, Countess of Flanders\"><PERSON>, Countess of Flanders</a> (b. 1202)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Flanders\" title=\"<PERSON>, Countess of Flanders\"><PERSON>, Countess of Flanders</a> (b. 1202)", "links": [{"title": "<PERSON>, Countess of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Flanders"}]}, {"year": "1306", "text": "<PERSON> \"the <PERSON>\" <PERSON>, Scottish nobleman", "html": "1306 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22the_Red%22_<PERSON>myn\" class=\"mw-redirect\" title='<PERSON> \"the <PERSON>\" Comyn'><PERSON> \"the <PERSON>\" <PERSON><PERSON><PERSON></a>, Scottish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22the_Red%22_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title='<PERSON> \"the Red\" Comyn'><PERSON> \"the <PERSON>\" <PERSON><PERSON><PERSON></a>, Scottish nobleman", "links": [{"title": "<PERSON> \"the <PERSON>\" <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22the_Red%22_<PERSON><PERSON>n"}]}, {"year": "1307", "text": "<PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan (b. 1265)", "html": "1307 - <a href=\"https://wikipedia.org/wiki/Tem%C3%<PERSON><PERSON>_<PERSON>,_Emperor_<PERSON><PERSON>_of_Yuan\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON> of Yuan\"><PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON> of Yuan</a> (b. 1265)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tem%C3%<PERSON><PERSON>_<PERSON>,_Emperor_<PERSON><PERSON>_of_Yuan\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON> of Yuan\"><PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan</a> (b. 1265)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Emperor <PERSON><PERSON> of Yuan", "link": "https://wikipedia.org/wiki/Tem%C3%<PERSON><PERSON>_<PERSON>,_Emperor_<PERSON><PERSON>_of_Yuan"}]}, {"year": "1346", "text": "<PERSON> of Rimini (b. 1282)", "html": "1346 - Blessed <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Rimini\" title=\"<PERSON> of Rimini\"><PERSON> of Rimini</a> (b. 1282)", "no_year_html": "Blessed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_R<PERSON><PERSON>\" title=\"<PERSON> of Rimini\"><PERSON> of Rimini</a> (b. 1282)", "links": [{"title": "Clare of Rimini", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1471", "text": "<PERSON>, Margrave of Brandenburg (b. 1413)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Margrave of Brandenburg</a> (b. 1413)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Margrave of Brandenburg</a> (b. 1413)", "links": [{"title": "<PERSON>, Elector of Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg"}]}, {"year": "1524", "text": "<PERSON> of Saxony, Archduchess of Austria (b. 1468)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxony,_Archduchess_of_Austria\" title=\"<PERSON> of Saxony, Archduchess of Austria\"><PERSON> of Saxony, Archduchess of Austria</a> (b. 1468)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxony,_Archduchess_of_Austria\" title=\"<PERSON> of Saxony, Archduchess of Austria\"><PERSON> of Saxony, Archduchess of Austria</a> (b. 1468)", "links": [{"title": "<PERSON> of Saxony, Archduchess of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxony,_Archduchess_of_Austria"}]}, {"year": "1526", "text": "<PERSON>, Count of Oldenburg, German noble (b. 1460)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Oldenburg\" title=\"<PERSON>, Count of Oldenburg\"><PERSON>, Count of Oldenburg</a>, German noble (b. 1460)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Oldenburg\" title=\"<PERSON>, Count of Oldenburg\"><PERSON>, Count of Oldenburg</a>, German noble (b. 1460)", "links": [{"title": "<PERSON>, Count of Oldenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Oldenburg"}]}, {"year": "1567", "text": "<PERSON>, Lord Dar<PERSON>ley, consort of <PERSON>, Queen of Scots (b. 1545)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>ley\" title=\"<PERSON>, Lord Darnley\"><PERSON>, Lord Darnley</a>, consort of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a> (b. 1545)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord Darnley\"><PERSON>, Lord Darnley</a>, consort of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a> (b. 1545)", "links": [{"title": "<PERSON>, Lord Dar<PERSON>ley", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>"}, {"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}]}, {"year": "1576", "text": "<PERSON>, German scholar, translator, and academic (b. 1532)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar, translator, and academic (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar, translator, and academic (b. 1532)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1660", "text": "<PERSON>, Dutch painter (b. 1609)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1609)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, English genealogist and historian (b. 1605)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English genealogist and historian (b. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English genealogist and historian (b. 1605)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1752", "text": "<PERSON><PERSON> of France, French Princess (b.1727)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_France\" title=\"<PERSON><PERSON> of France\"><PERSON><PERSON> of France</a>, French Princess (b.1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_France\" title=\"<PERSON><PERSON> of France\"><PERSON><PERSON> of France</a>, French Princess (b.1727)", "links": [{"title": "<PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>ette_of_France"}]}, {"year": "1755", "text": "<PERSON><PERSON><PERSON><PERSON>, French lawyer and philosopher (b. 1689)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>eu\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French lawyer and philosopher (b. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French lawyer and philosopher (b. 1689)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1782", "text": "<PERSON>, German theologian and author (b. 1702)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (b. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (b. 1702)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON> (b. 1760)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> (b. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, British naval officer (b. 1772)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British naval officer (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British naval officer (b. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, Russian poet and author (b. 1799)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (b. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Dutch feminist and pamphleteer (b. 1781)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch feminist and pamphleteer (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch feminist and pamphleteer (b. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Mexican politician and general (b. 1792)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Joaqu%C3%AD<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician and general (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Joaqu%C3%AD<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician and general (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Joaqu%C3%ADn_<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, English-Canadian surveyor and explorer (b. 1770)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English-Canadian surveyor and explorer (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English-Canadian surveyor and explorer (b. 1770)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_(explorer)"}]}, {"year": "1865", "text": "<PERSON>, Estonian-Italian physicist and academic (b. 1804)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Estonian-Italian physicist and academic (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Estonian-Italian physicist and academic (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, French illustrator and painter (b. 1808)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French illustrator and painter (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French illustrator and painter (b. 1808)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Honor%C3%A9_<PERSON><PERSON>er"}]}, {"year": "1887", "text": "<PERSON>, English author (b. 1814)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English author (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English author (b. 1814)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1891", "text": "<PERSON>, Russian-Swedish mathematician and physicist (b. 1850)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Sofia_Kovalevskaya\" class=\"mw-redirect\" title=\"Sofia Kovalevskaya\"><PERSON></a>, Russian-Swedish mathematician and physicist (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sofia_Kovalevskaya\" class=\"mw-redirect\" title=\"Sofia Kovalevskaya\"><PERSON></a>, Russian-Swedish mathematician and physicist (b. 1850)", "links": [{"title": "Sofia Kovalevskaya", "link": "https://wikipedia.org/wiki/Sofia_Kovalevskaya"}]}, {"year": "1904", "text": "<PERSON>, American lawyer and politician, 30th Mayor of Chicago (b. 1844)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1906", "text": "<PERSON>, American-Canadian businessman and politician (b. 1827)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian businessman and politician (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian businessman and politician (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, 1st Baron <PERSON>, English surgeon and academic (b. 1827)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English surgeon and academic (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English surgeon and academic (b. 1827)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Greek long jumper (b. 1888)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek long jumper (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek long jumper (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Konstantin<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English painter (b. 1849)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Ottoman sultan (b. 1842)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman sultan (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman sultan (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Italian soldier and journalist, Nobel Prize laureate (b. 1833)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier and journalist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier and journalist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1920", "text": "<PERSON>, English-Australian politician, 12th Premier of South Australia (b. 1832)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henry_Strangways"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1923", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (b. 1845)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Wilhelm_R%C3%B6ntgen\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilhelm_R%C3%B6ntgen\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wilhelm_R%C3%B6ntgen"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1928", "text": "<PERSON>, Mexican martyr and saint (b. 1913)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_S%C3%A1nchez_del_R%C3%ADo\" title=\"<PERSON>\"><PERSON></a>, Mexican martyr and saint (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_S%C3%A1nchez_del_R%C3%ADo\" title=\"<PERSON>\"><PERSON></a>, Mexican martyr and saint (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_S%C3%A1nchez_del_R%C3%ADo"}]}, {"year": "1932", "text": "<PERSON>, English author and screenwriter (b. 1875)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON> (b. 1857)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius XI\">Pope <PERSON></a> (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius <PERSON>\"><PERSON></a> (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON> <PERSON><PERSON>, Greek-French astronomer and chess player (b. 1870)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Greek-French astronomer and chess player (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"E<PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Greek-French astronomer and chess player (b. 1870)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Antoniadi"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Filipino lawyer and jurist (b. 1878)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Anacleto_D%C3%ADaz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino lawyer and jurist (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anacleto_D%C3%ADaz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino lawyer and jurist (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anacleto_D%C3%ADaz"}]}, {"year": "1950", "text": "<PERSON>, French sociologist and anthropologist (b. 1872)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist and anthropologist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist and anthropologist (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American poet and violinist (b. 1872)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and violinist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and violinist (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Greek banker and politician, 132nd Prime Minister of Greece (b. 1882)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ude<PERSON>\" title=\"<PERSON><PERSON><PERSON>ude<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek banker and politician, 132nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek banker and politician, 132nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1957", "text": "<PERSON>, American author (b. 1867)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_In<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Croatian cardinal (b. 1898)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian cardinal (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian cardinal (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American composer and songwriter (b. 1899)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Greek historian and author (b. 1884)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek historian and author (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek historian and author (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dionys<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Greek sailor and poet (b. 1910)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek sailor and poet (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek sailor and poet (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Slovene general and politician, 2nd Foreign Minister of Yugoslavia (b. 1910)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovene general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_Yugoslavia\" class=\"mw-redirect\" title=\"Foreign Minister of Yugoslavia\">Foreign Minister of Yugoslavia</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovene general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_Yugoslavia\" class=\"mw-redirect\" title=\"Foreign Minister of Yugoslavia\">Foreign Minister of Yugoslavia</a> (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Foreign Minister of Yugoslavia", "link": "https://wikipedia.org/wiki/Foreign_Minister_of_Yugoslavia"}]}, {"year": "1992", "text": "<PERSON>, American soldier, journalist, and author (b. 1921)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, New Zealand-Australian ophthalmologist and academic (b. 1929)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian ophthalmologist and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fred <PERSON>s\"><PERSON></a>, New Zealand-Australian ophthalmologist and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American author, poet, and activist (b. 1945)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and activist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and activist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Scottish musician (b. 1945)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish musician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish musician (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actor, comedian and writer (b. 1949)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian and writer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian and writer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American academic and politician, 104th Mayor of New York City (b. 1906)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 104th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 104th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "2001", "text": "<PERSON>, American saxophonist and clarinet player (b. 1913)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1936)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Mexican-American photographer (b. 1910)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American photographer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American photographer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American lawyer and politician, Mayor of San Jose (b. 1908)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_San_Jose,_California\" class=\"mw-redirect\" title=\"List of mayors of San Jose, California\">Mayor of San Jose</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_San_Jose,_California\" class=\"mw-redirect\" title=\"List of mayors of San Jose, California\">Mayor of San Jose</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of mayors of San Jose, California", "link": "https://wikipedia.org/wiki/List_of_mayors_of_San_Jose,_California"}]}, {"year": "2003", "text": "<PERSON>, American politician, 14th White House Press Secretary (b. 1939)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 14th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 14th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Press Secretary", "link": "https://wikipedia.org/wiki/White_House_Press_Secretary"}]}, {"year": "2005", "text": "<PERSON>, American actor, playwright, and author (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, playwright, and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, playwright, and author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American record producer and rapper (b. 1974)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American record producer and rapper (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American record producer and rapper (b. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actor and boxer (b. 1932)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and boxer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and boxer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American basketball player and coach (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American lieutenant and politician (b. 1933)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Texas_politician)\" title=\"<PERSON> (Texas politician)\"><PERSON></a>, American lieutenant and politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Texas_politician)\" title=\"<PERSON> (Texas politician)\"><PERSON></a>, American lieutenant and politician (b. 1933)", "links": [{"title": "<PERSON> (Texas politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Texas_politician)"}]}, {"year": "2011", "text": "<PERSON>, English cricketer and journalist (b. 1923)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and journalist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and journalist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, New Zealand banker and businessman, founded H. R. L. Morrison & Co (b. 1957)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand banker and businessman, founded <a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_%26_Co\" class=\"mw-redirect\" title=\"H. R. L. Morrison &amp; Co\">H. R. L. Morrison &amp; Co</a> (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand banker and businessman, founded <a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_%26_Co\" class=\"mw-redirect\" title=\"H. R. L. Morrison &amp; Co\">H. R. L. Morrison &amp; Co</a> (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "H. R. L. Morrison & Co", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_%26_Co"}]}, {"year": "2012", "text": "<PERSON>, American journalist and author (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American author, screenwriter, and animator (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Biggers\" title=\"<PERSON><PERSON> Biggers\"><PERSON><PERSON></a>, American author, screenwriter, and animator (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>gers\" title=\"<PERSON><PERSON> Biggers\"><PERSON><PERSON></a>, American author, screenwriter, and animator (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Biggers"}]}, {"year": "2013", "text": "<PERSON>, American-Israeli rabbi and philosopher, founded the Shalom Hartman Institute (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>(rabbi)\" title=\"<PERSON> (rabbi)\"><PERSON></a>, American-Israeli rabbi and philosopher, founded the <a href=\"https://wikipedia.org/wiki/Shalom_Hartman_Institute\" title=\"Shalom Hartman Institute\">Shalom Hartman Institute</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(rabbi)\" title=\"<PERSON> (rabbi)\"><PERSON></a>, American-Israeli rabbi and philosopher, founded the <a href=\"https://wikipedia.org/wiki/Shalom_Hartman_Institute\" title=\"Shalom Hartman Institute\">Shalom Hartman Institute</a> (b. 1931)", "links": [{"title": "<PERSON> (rabbi)", "link": "https://wikipedia.org/wiki/<PERSON>_(rabbi)"}, {"title": "Shalom Hartman Institute", "link": "https://wikipedia.org/wiki/Shalom_Hartman_Institute"}]}, {"year": "2014", "text": "<PERSON>, Jamaican-English sociologist and theorist (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cultural_theorist)\" title=\"<PERSON> (cultural theorist)\"><PERSON></a>, Jamaican-English sociologist and theorist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cultural_theorist)\" title=\"<PERSON> (cultural theorist)\"><PERSON></a>, Jamaican-English sociologist and theorist (b. 1932)", "links": [{"title": "<PERSON> (cultural theorist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cultural_theorist)"}]}, {"year": "2014", "text": "<PERSON>, American actress and diplomat (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Shirley_Temple\" title=\"Shirley Temple\"><PERSON> Temple</a>, American actress and diplomat (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shirley_Temple\" title=\"Shirley Temple\"><PERSON> Temple</a>, American actress and diplomat (b. 1928)", "links": [{"title": "Shirley Temple", "link": "https://wikipedia.org/wiki/Shirley_Temple"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Palestinian scholar and activist (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian scholar and activist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian scholar and activist (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German cardinal and theologian (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal and theologian (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal and theologian (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Chinese theorist and politician (b. 1915)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Deng_<PERSON>\" title=\"Den<PERSON>\"><PERSON><PERSON></a>, Chinese theorist and politician (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Den<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese theorist and politician (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Deng_<PERSON>un"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Indian-Pakistani author and playwright (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Surayya_Bajia\" title=\"Fatima Surayya Bajia\"><PERSON><PERSON></a>, Indian-Pakistani author and playwright (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Surayya_Bajia\" title=\"Fatima Surayya Bajia\"><PERSON><PERSON></a>, Indian-Pakistani author and playwright (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fatima_Surayya_Bajia"}]}, {"year": "2017", "text": "<PERSON>, American businessman (b. 1929)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American actor (b. 1943)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmen_<PERSON>no"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, American actor (b. 1944)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jan-<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American publisher (b. 1942)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, New Zealand rugby league player (b. 1957)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Filip<PERSON>\"><PERSON></a>, New Zealand rugby league player (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, South African rapper (b. 1988)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, South African rapper (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, South African rapper (b. 1988)", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "2025", "text": "<PERSON>, American football player and actor (b. 1963)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}