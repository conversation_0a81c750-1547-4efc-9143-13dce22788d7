{"date": "December 13", "url": "https://wikipedia.org/wiki/December_13", "data": {"Events": [{"year": "1294", "text": "<PERSON> resigns the papacy after only five months to return to his previous life as an ascetic hermit.", "html": "1294 - <a href=\"https://wikipedia.org/wiki/Pope_Celestine_V\" title=\"Pope Celestine V\">Saint <PERSON>tine V</a> <a href=\"https://wikipedia.org/wiki/Papal_resignation\" class=\"mw-redirect\" title=\"Papal resignation\">resigns</a> the <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">papacy</a> after only five months to return to his previous life as an <a href=\"https://wikipedia.org/wiki/Asceticism\" title=\"Asceticism\">ascetic</a> <a href=\"https://wikipedia.org/wiki/Hermit\" title=\"Hermit\">hermit</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Ce<PERSON>tine_V\" title=\"Pope Celestine V\">Saint <PERSON> V</a> <a href=\"https://wikipedia.org/wiki/Papal_resignation\" class=\"mw-redirect\" title=\"Papal resignation\">resigns</a> the <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">papacy</a> after only five months to return to his previous life as an <a href=\"https://wikipedia.org/wiki/Asceticism\" title=\"Asceticism\">ascetic</a> <a href=\"https://wikipedia.org/wiki/Hermit\" title=\"Hermit\">hermit</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Papal resignation", "link": "https://wikipedia.org/wiki/Papal_resignation"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}, {"title": "Asceticism", "link": "https://wikipedia.org/wiki/Asceticism"}, {"title": "Hermit", "link": "https://wikipedia.org/wiki/Hermit"}]}, {"year": "1545", "text": "The Council of Trent begins as the embodiment of the Counter-Reformation.", "html": "1545 - The <a href=\"https://wikipedia.org/wiki/Council_of_Trent\" title=\"Council of Trent\">Council of Trent</a> begins as the embodiment of the <a href=\"https://wikipedia.org/wiki/Counter-Reformation\" title=\"Counter-Reformation\">Counter-Reformation</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Council_of_Trent\" title=\"Council of Trent\">Council of Trent</a> begins as the embodiment of the <a href=\"https://wikipedia.org/wiki/Counter-Reformation\" title=\"Counter-Reformation\">Counter-Reformation</a>.", "links": [{"title": "Council of Trent", "link": "https://wikipedia.org/wiki/Council_of_Trent"}, {"title": "Counter-Reformation", "link": "https://wikipedia.org/wiki/Counter-Reformation"}]}, {"year": "1577", "text": "Sir <PERSON> sets sail from Plymouth, England, on his round-the-world voyage.", "html": "1577 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets sail from <a href=\"https://wikipedia.org/wiki/Plymouth\" title=\"Plymouth\">Plymouth</a>, England, on his <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_circumnavigation\" title=\"<PERSON>'s circumnavigation\">round-the-world voyage</a>.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets sail from <a href=\"https://wikipedia.org/wiki/Plymouth\" title=\"Plymouth\">Plymouth</a>, England, on his <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_circumnavigation\" title=\"<PERSON>'s circumnavigation\">round-the-world voyage</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Plymouth", "link": "https://wikipedia.org/wiki/Plymouth"}, {"title": "<PERSON>'s circumnavigation", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_circumnavigation"}]}, {"year": "1623", "text": "The Plymouth Colony establishes the system of trial by 12-men jury in the American colonies.", "html": "1623 - <a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">The Plymouth Colony</a> establishes the system of <a href=\"https://wikipedia.org/wiki/Jury_trial\" title=\"Jury trial\">trial</a> by 12-men jury in the American colonies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">The Plymouth Colony</a> establishes the system of <a href=\"https://wikipedia.org/wiki/Jury_trial\" title=\"Jury trial\">trial</a> by 12-men jury in the American colonies.", "links": [{"title": "Plymouth Colony", "link": "https://wikipedia.org/wiki/Plymouth_Colony"}, {"title": "Jury trial", "link": "https://wikipedia.org/wiki/Jury_trial"}]}, {"year": "1636", "text": "The Massachusetts Bay Colony organizes three militia regiments to defend the colony against the Pequot Indians, a date now considered the founding of the National Guard of the United States.", "html": "1636 - The <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> organizes three <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militia</a> regiments to defend the colony against the <a href=\"https://wikipedia.org/wiki/Pequot_people\" class=\"mw-redirect\" title=\"Pequot people\">Pequot</a> <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Indians</a>, a date now considered the founding of the <a href=\"https://wikipedia.org/wiki/National_Guard_of_the_United_States\" class=\"mw-redirect\" title=\"National Guard of the United States\">National Guard of the United States</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> organizes three <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militia</a> regiments to defend the colony against the <a href=\"https://wikipedia.org/wiki/Pequot_people\" class=\"mw-redirect\" title=\"Pequot people\">Pequot</a> <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Indians</a>, a date now considered the founding of the <a href=\"https://wikipedia.org/wiki/National_Guard_of_the_United_States\" class=\"mw-redirect\" title=\"National Guard of the United States\">National Guard of the United States</a>.", "links": [{"title": "Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Massachusetts_Bay_Colony"}, {"title": "Militia", "link": "https://wikipedia.org/wiki/Militia"}, {"title": "Pequot people", "link": "https://wikipedia.org/wiki/Pequot_people"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}, {"title": "National Guard of the United States", "link": "https://wikipedia.org/wiki/National_Guard_of_the_United_States"}]}, {"year": "1642", "text": "<PERSON> is the first recorded European to sight New Zealand.", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first recorded European to sight <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first recorded European to sight <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Abel_<PERSON>sman"}, {"title": "New Zealand", "link": "https://wikipedia.org/wiki/New_Zealand"}]}, {"year": "1643", "text": "English Civil War: The Battle of Alton takes place in Hampshire.", "html": "1643 - <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Alton\" title=\"Battle of Alton\">Battle of Alton</a> takes place in <a href=\"https://wikipedia.org/wiki/Hampshire\" title=\"Hampshire\">Hampshire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Alton\" title=\"Battle of Alton\">Battle of Alton</a> takes place in <a href=\"https://wikipedia.org/wiki/Hampshire\" title=\"Hampshire\">Hampshire</a>.", "links": [{"title": "English Civil War", "link": "https://wikipedia.org/wiki/English_Civil_War"}, {"title": "Battle of Alton", "link": "https://wikipedia.org/wiki/Battle_of_Alton"}, {"title": "Hampshire", "link": "https://wikipedia.org/wiki/Hampshire"}]}, {"year": "1758", "text": "The English transport ship <PERSON> sinks in the North Atlantic, killing over 360 people.", "html": "1758 - The English transport ship <i><a href=\"https://wikipedia.org/wiki/Duke_<PERSON>(ship)\" title=\"<PERSON> (ship)\">Duke <PERSON></a></i> sinks in the North Atlantic, killing over 360 people.", "no_year_html": "The English transport ship <i><a href=\"https://wikipedia.org/wiki/Duke_<PERSON>_(ship)\" title=\"<PERSON> (ship)\">Duke <PERSON></a></i> sinks in the North Atlantic, killing over 360 people.", "links": [{"title": "<PERSON> (ship)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ship)"}]}, {"year": "1769", "text": "Dartmouth College is founded by the Reverend <PERSON><PERSON><PERSON>, with a royal charter from King <PERSON>, on land donated by Royal governor <PERSON>.", "html": "1769 - <a href=\"https://wikipedia.org/wiki/Dartmouth_College\" title=\"Dartmouth College\">Dartmouth College</a> is founded by the Reverend <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, with a <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a> from King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> of the United Kingdom\"><PERSON> III</a>, on land donated by <a href=\"https://wikipedia.org/wiki/Governor-general#Governors-general_in_the_British_Empire\" title=\"Governor-general\">Royal governor</a> <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dartmouth_College\" title=\"Dartmouth College\">Dartmouth College</a> is founded by the Reverend <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, with a <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a> from King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> of the United Kingdom\"><PERSON> III</a>, on land donated by <a href=\"https://wikipedia.org/wiki/Governor-general#Governors-general_in_the_British_Empire\" title=\"Governor-general\">Royal governor</a> <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\"><PERSON></a>.", "links": [{"title": "Dartmouth College", "link": "https://wikipedia.org/wiki/Dartmouth_College"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Royal charter", "link": "https://wikipedia.org/wiki/Royal_charter"}, {"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom"}, {"title": "Governor-general", "link": "https://wikipedia.org/wiki/Governor-general#Governors-general_in_the_British_Empire"}, {"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}]}, {"year": "1818", "text": "<PERSON> of Constantinople resigns from his position as Ecumenical Patriarch under pressure from the Ottoman Empire.", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_Constantinople\" title=\"Cyril VI of Constantinople\"><PERSON> of Constantinople</a> resigns from his position as Ecumenical Patriarch under pressure from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_Constantinople\" title=\"<PERSON> VI of Constantinople\"><PERSON> of Constantinople</a> resigns from his position as Ecumenical Patriarch under pressure from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "<PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Cyril_<PERSON>_of_Constantinople"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1862", "text": "American Civil War: At the Battle of Fredericksburg, Confederate General <PERSON> repulses attacks by Union Major General <PERSON> on Marye's Heights, inflicting heavy casualties.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Fredericksburg\" title=\"Battle of Fredericksburg\">Battle of Fredericksburg</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> repulses attacks by <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union</a> Major General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> on Marye's Heights, inflicting heavy casualties.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Fredericksburg\" title=\"Battle of Fredericksburg\">Battle of Fredericksburg</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> repulses attacks by <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union</a> Major General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> on Marye's Heights, inflicting heavy casualties.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Fredericksburg", "link": "https://wikipedia.org/wiki/Battle_of_Fredericksburg"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "A Fenian bomb explodes in Clerkenwell, London, killing 12 people and injuring 50.", "html": "1867 - A <a href=\"https://wikipedia.org/wiki/Fenian\" title=\"Fenian\"><PERSON><PERSON></a> bomb explodes in <a href=\"https://wikipedia.org/wiki/Clerkenwell\" title=\"Clerkenwell\">Clerkenwell</a>, London, killing 12 people and injuring 50.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Fenian\" title=\"Fenian\"><PERSON><PERSON></a> bomb explodes in <a href=\"https://wikipedia.org/wiki/Clerkenwell\" title=\"Clerkenwell\">Clerkenwell</a>, London, killing 12 people and injuring 50.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fenian"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>enwell"}]}, {"year": "1937", "text": "Second Sino-Japanese War: Battle of Nanking: The city of Nanjing, defended by the National Revolutionary Army under the command of General <PERSON>, falls to the Japanese. This is followed by the Nanking Massacre, in which Japanese troops rape and slaughter hundreds of thousands of civilians.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Nanking\" title=\"Battle of Nanking\">Battle of Nanking</a>: The city of <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a>, defended by the <a href=\"https://wikipedia.org/wiki/National_Revolutionary_Army\" title=\"National Revolutionary Army\">National Revolutionary Army</a> under the command of General <a href=\"https://wikipedia.org/wiki/Tang_Shengzhi\" title=\"Tang Shen<PERSON>zhi\">Tang <PERSON></a>, falls to the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a>. This is followed by the <a href=\"https://wikipedia.org/wiki/Nanking_Massacre\" class=\"mw-redirect\" title=\"Nanking Massacre\">Nanking Massacre</a>, in which Japanese troops rape and slaughter hundreds of thousands of civilians.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Nanking\" title=\"Battle of Nanking\">Battle of Nanking</a>: The city of <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a>, defended by the <a href=\"https://wikipedia.org/wiki/National_Revolutionary_Army\" title=\"National Revolutionary Army\">National Revolutionary Army</a> under the command of General <a href=\"https://wikipedia.org/wiki/Tang_Shengzhi\" title=\"Tang Shengzhi\"><PERSON></a>, falls to the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a>. This is followed by the <a href=\"https://wikipedia.org/wiki/Nanking_Massacre\" class=\"mw-redirect\" title=\"Nanking Massacre\">Nanking Massacre</a>, in which Japanese troops rape and slaughter hundreds of thousands of civilians.", "links": [{"title": "Second Sino-Japanese War", "link": "https://wikipedia.org/wiki/Second_Sino-Japanese_War"}, {"title": "Battle of Nanking", "link": "https://wikipedia.org/wiki/Battle_of_Nanking"}, {"title": "Nanjing", "link": "https://wikipedia.org/wiki/Nanjing"}, {"title": "National Revolutionary Army", "link": "https://wikipedia.org/wiki/National_Revolutionary_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tang_<PERSON>zhi"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Nanking Massacre", "link": "https://wikipedia.org/wiki/Nanking_Massacre"}]}, {"year": "1938", "text": "The Holocaust: The Neuengamme concentration camp opens in the Bergedorf district of Hamburg, Germany.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: The <a href=\"https://wikipedia.org/wiki/Neuengamme_concentration_camp\" title=\"Neuengamme concentration camp\">Neuengamme concentration camp</a> opens in the <a href=\"https://wikipedia.org/wiki/Bergedorf\" title=\"Bergedorf\">Bergedorf</a> district of <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a>, Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: The <a href=\"https://wikipedia.org/wiki/Neuengamme_concentration_camp\" title=\"Neuengamme concentration camp\">Neuengamme concentration camp</a> opens in the <a href=\"https://wikipedia.org/wiki/Bergedorf\" title=\"Bergedorf\">Bergedorf</a> district of <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a>, Germany.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Neuengamme concentration camp", "link": "https://wikipedia.org/wiki/Neuengamme_concentration_camp"}, {"title": "Bergedorf", "link": "https://wikipedia.org/wiki/Bergedorf"}, {"title": "Hamburg", "link": "https://wikipedia.org/wiki/Hamburg"}]}, {"year": "1939", "text": "The Battle of the River Plate is fought off the coast of Uruguay; the first naval battle of World War II. The Kriegsmarine's Deutschland-class cruiser (pocket battleship) Admiral <PERSON> engages with three Royal Navy cruisers: HMS Ajax, HMNZS Achilles and HMS Exeter.", "html": "1939 - The <a href=\"https://wikipedia.org/wiki/Battle_of_the_River_Plate\" title=\"Battle of the River Plate\">Battle of the River Plate</a> is fought off the coast of Uruguay; the first naval battle of <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>. The <a href=\"https://wikipedia.org/wiki/Kriegsmarine\" title=\"Kriegsmarine\">Kriegsmarine</a>'s <a href=\"https://wikipedia.org/wiki/Deutschland-class_cruiser\" title=\"Deutschland-class cruiser\">Deutschland-class cruiser</a> (pocket battleship) <i><a href=\"https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>_<PERSON>\" title=\"German cruiser Admiral <PERSON>\">Admiral <PERSON></a></i> engages with three <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> cruisers: <a href=\"https://wikipedia.org/wiki/HMS_Ajax_(22)\" title=\"HMS Ajax (22)\">HMS <i>Ajax</i></a>, <a href=\"https://wikipedia.org/wiki/HMNZS_Achilles\" title=\"HMNZS Achilles\">HMNZS <i>Achilles</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Exeter_(68)\" title=\"HMS Exeter (68)\">HMS <i>Exeter</i></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_the_River_Plate\" title=\"Battle of the River Plate\">Battle of the River Plate</a> is fought off the coast of Uruguay; the first naval battle of <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>. The <a href=\"https://wikipedia.org/wiki/Kriegsmarine\" title=\"Kriegsmarine\">Kriegsmarine</a>'s <a href=\"https://wikipedia.org/wiki/Deutschland-class_cruiser\" title=\"Deutschland-class cruiser\">Deutschland-class cruiser</a> (pocket battleship) <i><a href=\"https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>_<PERSON>\" title=\"German cruiser Admiral <PERSON>\">Admiral <PERSON></a></i> engages with three <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> cruisers: <a href=\"https://wikipedia.org/wiki/HMS_Ajax_(22)\" title=\"HMS Ajax (22)\">HMS <i>Ajax</i></a>, <a href=\"https://wikipedia.org/wiki/HMNZS_Achilles\" title=\"HMNZS Achilles\">HMNZS <i>Achilles</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Exeter_(68)\" title=\"HMS Exeter (68)\">HMS <i>Exeter</i></a>.", "links": [{"title": "Battle of the River Plate", "link": "https://wikipedia.org/wiki/Battle_of_the_River_Plate"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Kriegsmarine", "link": "https://wikipedia.org/wiki/Kriegsmarine"}, {"title": "Deutschland-class cruiser", "link": "https://wikipedia.org/wiki/Deutschland-class_cruiser"}, {"title": "German cruiser Admiral <PERSON>", "link": "https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>_<PERSON>"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "HMS Ajax (22)", "link": "https://wikipedia.org/wiki/HMS_Ajax_(22)"}, {"title": "HMNZS Achilles", "link": "https://wikipedia.org/wiki/HMNZS_Achilles"}, {"title": "HMS Exeter (68)", "link": "https://wikipedia.org/wiki/HMS_Exeter_(68)"}]}, {"year": "1943", "text": "World War II: The Massacre of Kalavryta by German occupying forces in Greece.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/Massacre_of_Kalavryta\" class=\"mw-redirect\" title=\"Massacre of Kalavryta\">Massacre of Kalavryta</a> by German occupying forces in Greece.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Massacre_of_Kalavryta\" class=\"mw-redirect\" title=\"Massacre of Kalavryta\">Massacre of Kalavryta</a> by German occupying forces in Greece.", "links": [{"title": "Massacre of Kalavryta", "link": "https://wikipedia.org/wiki/Massacre_of_Kalavryta"}]}, {"year": "1949", "text": "The Knesset votes to move the capital of Israel from Tel Aviv to Jerusalem.", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a> votes to move the capital of <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> from <a href=\"https://wikipedia.org/wiki/Tel_Aviv\" title=\"Tel Aviv\">Tel Aviv</a> to <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a> votes to move the capital of <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> from <a href=\"https://wikipedia.org/wiki/Tel_Aviv\" title=\"Tel Aviv\">Tel Aviv</a> to <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "Knesset", "link": "https://wikipedia.org/wiki/Knesset"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Tel Aviv", "link": "https://wikipedia.org/wiki/Tel_Aviv"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "1957", "text": "The Mw  6.5 Farsinaj earthquake strikes Iran with a maximum Mercalli intensity of VII, causing at least 1,119 deaths and damaging over 5,000 homes.", "html": "1957 - The M<sub>w</sub>  6.5 <a href=\"https://wikipedia.org/wiki/1957_Farsinaj_earthquake\" title=\"1957 Farsinaj earthquake\">Farsinaj earthquake</a> strikes Iran with a maximum Mercalli intensity of VII, causing at least 1,119 deaths and damaging over 5,000 homes.", "no_year_html": "The M<sub>w</sub>  6.5 <a href=\"https://wikipedia.org/wiki/1957_Farsinaj_earthquake\" title=\"1957 Farsinaj earthquake\">Farsinaj earthquake</a> strikes Iran with a maximum Mercalli intensity of VII, causing at least 1,119 deaths and damaging over 5,000 homes.", "links": [{"title": "1957 Farsinaj earthquake", "link": "https://wikipedia.org/wiki/1957_Farsinaj_earthquake"}]}, {"year": "1959", "text": "Archbishop <PERSON><PERSON><PERSON> becomes the first President of Cyprus.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/List_of_archbishops_of_Cyprus\" title=\"List of archbishops of Cyprus\">Archbishop</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III\" title=\"<PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON> III</a> becomes the first <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/List_of_archbishops_of_Cyprus\" title=\"List of archbishops of Cyprus\">Archbishop</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III\" title=\"<PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON> III</a> becomes the first <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a>.", "links": [{"title": "List of archbishops of Cyprus", "link": "https://wikipedia.org/wiki/List_of_archbishops_of_Cyprus"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>kari<PERSON>_III"}, {"title": "President of Cyprus", "link": "https://wikipedia.org/wiki/President_of_Cyprus"}]}, {"year": "1960", "text": "While Emperor <PERSON><PERSON> of Ethiopia visits Brazil, his Imperial Bodyguard seizes the capital and proclaims him deposed and his son, Crown Prince <PERSON><PERSON>, Emperor.", "html": "1960 - While <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Haile_Selassie\" title=\"Haile Selassie\"><PERSON><PERSON> Se<PERSON></a> of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> visits Brazil, his <a href=\"https://wikipedia.org/wiki/Kebur_Zabagna\" title=\"Kebur Zabagna\">Imperial Bodyguard</a> <a href=\"https://wikipedia.org/wiki/1960_Ethiopian_coup_attempt\" title=\"1960 Ethiopian coup attempt\">seizes the capital</a> and proclaims him deposed and his son, Crown Prince <a href=\"https://wikipedia.org/wiki/Amha_Selassie\" title=\"Amha Selassie\"><PERSON><PERSON></a>, Emperor.", "no_year_html": "While <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Haile_Selassie\" title=\"Haile Selassie\"><PERSON><PERSON> Selassie</a> of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> visits Brazil, his <a href=\"https://wikipedia.org/wiki/Kebur_Zabagna\" title=\"Kebur Zabagna\">Imperial Bodyguard</a> <a href=\"https://wikipedia.org/wiki/1960_Ethiopian_coup_attempt\" title=\"1960 Ethiopian coup attempt\">seizes the capital</a> and proclaims him deposed and his son, Crown Prince <a href=\"https://wikipedia.org/wiki/Amha_Selassie\" title=\"<PERSON>ha Selassie\"><PERSON><PERSON></a>, Emperor.", "links": [{"title": "Emperor of Ethiopia", "link": "https://wikipedia.org/wiki/Emperor_of_Ethiopia"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kebur_<PERSON>"}, {"title": "1960 Ethiopian coup attempt", "link": "https://wikipedia.org/wiki/1960_Ethiopian_coup_attempt"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amha_<PERSON><PERSON>ie"}]}, {"year": "1962", "text": "NASA launches Relay 1, the first active repeater communications satellite in orbit.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <i><a href=\"https://wikipedia.org/wiki/Relay_program#Relay_1\" title=\"Relay program\">Relay 1</a></i>, the first active repeater communications <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellite</a> in orbit.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <i><a href=\"https://wikipedia.org/wiki/Relay_program#Relay_1\" title=\"Relay program\">Relay 1</a></i>, the first active repeater communications <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellite</a> in orbit.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Relay program", "link": "https://wikipedia.org/wiki/Relay_program#Relay_1"}, {"title": "Satellite", "link": "https://wikipedia.org/wiki/Satellite"}]}, {"year": "1967", "text": "<PERSON> II of Greece attempts an unsuccessful counter-coup against the Regime of the Colonels.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Constantine_II_of_Greece\" title=\"Constantine II of Greece\"><PERSON> II of Greece</a> attempts an unsuccessful <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374#The_King's_counter-coup\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">counter-coup</a> against the <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">Regime of the Colonels</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_II_of_Greece\" title=\"Constantine II of Greece\">Constantine II of Greece</a> attempts an unsuccessful <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374#The_King's_counter-coup\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">counter-coup</a> against the <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">Regime of the Colonels</a>.", "links": [{"title": "Constantine II of Greece", "link": "https://wikipedia.org/wiki/Constantine_II_of_Greece"}, {"title": "Greek military junta of 1967-74", "link": "https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374#The_King's_counter-coup"}, {"title": "Greek military junta of 1967-74", "link": "https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374"}]}, {"year": "1968", "text": "Brazilian President <PERSON><PERSON> issues AI-5 (Institutional Act No. 5), enabling government by decree and suspending habeas corpus.", "html": "1968 - Brazilian President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> issues <a href=\"https://wikipedia.org/wiki/AI-5\" class=\"mw-redirect\" title=\"AI-5\">AI-5</a> (Institutional Act No. 5), enabling government by decree and suspending <a href=\"https://wikipedia.org/wiki/Habeas_corpus\" title=\"Habeas corpus\">habeas corpus</a>.", "no_year_html": "Brazilian President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> issues <a href=\"https://wikipedia.org/wiki/AI-5\" class=\"mw-redirect\" title=\"AI-5\">AI-5</a> (Institutional Act No. 5), enabling government by decree and suspending <a href=\"https://wikipedia.org/wiki/Habeas_corpus\" title=\"Habeas corpus\">habeas corpus</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "AI-5", "link": "https://wikipedia.org/wiki/AI-5"}, {"title": "Habeas corpus", "link": "https://wikipedia.org/wiki/Habeas_corpus"}]}, {"year": "1972", "text": "Apollo program: <PERSON> and <PERSON> begin the third and final extra-vehicular activity (EVA) or \"Moonwalk\" of Apollo 17. To date they are the last humans to set foot on the Moon.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begin the third and final <a href=\"https://wikipedia.org/wiki/Extra-vehicular_activity\" class=\"mw-redirect\" title=\"Extra-vehicular activity\">extra-vehicular activity</a> (EVA) or \"Moonwalk\" of <i><a href=\"https://wikipedia.org/wiki/Apollo_17\" title=\"Apollo 17\">Apollo 17</a></i>. To date they are the last humans to set foot on the Moon.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begin the third and final <a href=\"https://wikipedia.org/wiki/Extra-vehicular_activity\" class=\"mw-redirect\" title=\"Extra-vehicular activity\">extra-vehicular activity</a> (EVA) or \"Moonwalk\" of <i><a href=\"https://wikipedia.org/wiki/Apollo_17\" title=\"Apollo 17\">Apollo 17</a></i>. To date they are the last humans to set foot on the Moon.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Extra-vehicular activity", "link": "https://wikipedia.org/wiki/Extra-vehicular_activity"}, {"title": "Apollo 17", "link": "https://wikipedia.org/wiki/Apollo_17"}]}, {"year": "1974", "text": "Malta becomes a republic within the Commonwealth of Nations.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> becomes a republic within the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> becomes a republic within the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a>.", "links": [{"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}]}, {"year": "1974", "text": "In the Vietnam War, the North Vietnamese forces launch their 1975 Spring Offensive (to 30 April 1975), which results in the final capitulation of South Vietnam.", "html": "1974 - In the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>, the <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> forces launch their <a href=\"https://wikipedia.org/wiki/1975_Spring_Offensive\" class=\"mw-redirect\" title=\"1975 Spring Offensive\">1975 Spring Offensive</a> (to 30 April 1975), which results in the final capitulation of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>, the <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> forces launch their <a href=\"https://wikipedia.org/wiki/1975_Spring_Offensive\" class=\"mw-redirect\" title=\"1975 Spring Offensive\">1975 Spring Offensive</a> (to 30 April 1975), which results in the final capitulation of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}, {"title": "1975 Spring Offensive", "link": "https://wikipedia.org/wiki/1975_Spring_Offensive"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1977", "text": "Air Indiana Flight 216 crashes near Evansville Regional Airport, killing 29, including the University of Evansville basketball team, support staff, and boosters of the team.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Air_Indiana_Flight_216\" title=\"Air Indiana Flight 216\">Air Indiana Flight 216</a> crashes near <a href=\"https://wikipedia.org/wiki/Evansville_Regional_Airport\" title=\"Evansville Regional Airport\">Evansville Regional Airport</a>, killing 29, including the <a href=\"https://wikipedia.org/wiki/Evansville_Purple_Aces_men%27s_basketball\" title=\"Evansville Purple Aces men's basketball\">University of Evansville basketball team</a>, support staff, and boosters of the team.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Indiana_Flight_216\" title=\"Air Indiana Flight 216\">Air Indiana Flight 216</a> crashes near <a href=\"https://wikipedia.org/wiki/Evansville_Regional_Airport\" title=\"Evansville Regional Airport\">Evansville Regional Airport</a>, killing 29, including the <a href=\"https://wikipedia.org/wiki/Evansville_Purple_Aces_men%27s_basketball\" title=\"Evansville Purple Aces men's basketball\">University of Evansville basketball team</a>, support staff, and boosters of the team.", "links": [{"title": "Air Indiana Flight 216", "link": "https://wikipedia.org/wiki/Air_Indiana_Flight_216"}, {"title": "Evansville Regional Airport", "link": "https://wikipedia.org/wiki/Evansville_Regional_Airport"}, {"title": "Evansville Purple Aces men's basketball", "link": "https://wikipedia.org/wiki/Evansville_Purple_Aces_men%27s_basketball"}]}, {"year": "1981", "text": "General <PERSON><PERSON><PERSON><PERSON><PERSON> declares martial law in Poland, largely due to the actions by Solidarity.", "html": "1981 - General <a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>aruzel<PERSON>\" title=\"Wojcie<PERSON>aruzel<PERSON>\">Wojcie<PERSON></a> declares <a href=\"https://wikipedia.org/wiki/Martial_law_in_Poland\" title=\"Martial law in Poland\">martial law</a> in Poland, largely due to the actions by <i><a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a></i>.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>zel<PERSON>\" title=\"Wojcie<PERSON>aruzel<PERSON>\">Woj<PERSON><PERSON></a> declares <a href=\"https://wikipedia.org/wiki/Martial_law_in_Poland\" title=\"Martial law in Poland\">martial law</a> in Poland, largely due to the actions by <i><a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a></i>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wojcie<PERSON>_<PERSON>"}, {"title": "Martial law in Poland", "link": "https://wikipedia.org/wiki/Martial_law_in_Poland"}, {"title": "Solidarity (Polish trade union)", "link": "https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)"}]}, {"year": "1982", "text": "The 6.0 Ms  North Yemen earthquake shakes southwestern Yemen with a maximum Mercalli intensity of VIII (Severe), killing 2,800, and injuring 1,500.", "html": "1982 - The 6.0 M<sub>s</sub>  <a href=\"https://wikipedia.org/wiki/1982_North_Yemen_earthquake\" title=\"1982 North Yemen earthquake\">North Yemen earthquake</a> shakes southwestern <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemen</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>), killing 2,800, and injuring 1,500.", "no_year_html": "The 6.0 M<sub>s</sub>  <a href=\"https://wikipedia.org/wiki/1982_North_Yemen_earthquake\" title=\"1982 North Yemen earthquake\">North Yemen earthquake</a> shakes southwestern <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemen</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>), killing 2,800, and injuring 1,500.", "links": [{"title": "1982 North Yemen earthquake", "link": "https://wikipedia.org/wiki/1982_North_Yemen_earthquake"}, {"title": "Yemen", "link": "https://wikipedia.org/wiki/Yemen"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1988", "text": "PLO Chairman <PERSON><PERSON> gives a speech at a UN General Assembly meeting in Geneva, Switzerland, after United States authorities refused to grant him a visa to visit UN headquarters in New York.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Chairman_of_the_Palestine_Liberation_Organization\" title=\"Chairman of the Palestine Liberation Organization\">PLO Chairman</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> gives a speech at a <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly\" title=\"United Nations General Assembly\">UN General Assembly</a> meeting in <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>, Switzerland, after United States authorities refused to grant him a <a href=\"https://wikipedia.org/wiki/Visa_(document)\" class=\"mw-redirect\" title=\"Visa (document)\">visa</a> to visit UN headquarters in New York.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chairman_of_the_Palestine_Liberation_Organization\" title=\"Chairman of the Palestine Liberation Organization\">PLO Chairman</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> gives a speech at a <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly\" title=\"United Nations General Assembly\">UN General Assembly</a> meeting in <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>, Switzerland, after United States authorities refused to grant him a <a href=\"https://wikipedia.org/wiki/Visa_(document)\" class=\"mw-redirect\" title=\"Visa (document)\">visa</a> to visit UN headquarters in New York.", "links": [{"title": "Chairman of the Palestine Liberation Organization", "link": "https://wikipedia.org/wiki/Chairman_of_the_Palestine_Liberation_Organization"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "United Nations General Assembly", "link": "https://wikipedia.org/wiki/United_Nations_General_Assembly"}, {"title": "Geneva", "link": "https://wikipedia.org/wiki/Geneva"}, {"title": "Visa (document)", "link": "https://wikipedia.org/wiki/Visa_(document)"}]}, {"year": "1989", "text": "The Troubles: Attack on Derryard checkpoint: The Provisional Irish Republican Army launches an attack on a British Army temporary vehicle checkpoint near Rosslea, Northern Ireland. Two British soldiers are killed and two others are wounded.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: <a href=\"https://wikipedia.org/wiki/Attack_on_Derryard_checkpoint\" title=\"Attack on Derryard checkpoint\">Attack on Derryard checkpoint</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> launches an attack on a <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> temporary vehicle checkpoint near <a href=\"https://wikipedia.org/wiki/Rosslea\" title=\"Rosslea\">Rosslea, Northern Ireland</a>. Two British soldiers are killed and two others are wounded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: <a href=\"https://wikipedia.org/wiki/Attack_on_Derryard_checkpoint\" title=\"Attack on Derryard checkpoint\">Attack on Derryard checkpoint</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> launches an attack on a <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> temporary vehicle checkpoint near <a href=\"https://wikipedia.org/wiki/Rosslea\" title=\"Rosslea\">Rosslea, Northern Ireland</a>. Two British soldiers are killed and two others are wounded.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Attack on Derryard checkpoint", "link": "https://wikipedia.org/wiki/Attack_on_Derryard_checkpoint"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rosslea"}]}, {"year": "1994", "text": "Flagship Airlines Flight 3379 crashes in Morrisville, North Carolina, near Raleigh-Durham International Airport, killing 15.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Flagship_Airlines_Flight_3379\" title=\"Flagship Airlines Flight 3379\">Flagship Airlines Flight 3379</a> crashes in <a href=\"https://wikipedia.org/wiki/Morrisville,_North_Carolina\" title=\"Morrisville, North Carolina\">Morrisville, North Carolina</a>, near <a href=\"https://wikipedia.org/wiki/Raleigh%E2%80%93Durham_International_Airport\" title=\"Raleigh-Durham International Airport\">Raleigh-Durham International Airport</a>, killing 15.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flagship_Airlines_Flight_3379\" title=\"Flagship Airlines Flight 3379\">Flagship Airlines Flight 3379</a> crashes in <a href=\"https://wikipedia.org/wiki/Morrisville,_North_Carolina\" title=\"Morrisville, North Carolina\">Morrisville, North Carolina</a>, near <a href=\"https://wikipedia.org/wiki/Raleigh%E2%80%93Durham_International_Airport\" title=\"Raleigh-Durham International Airport\">Raleigh-Durham International Airport</a>, killing 15.", "links": [{"title": "Flagship Airlines Flight 3379", "link": "https://wikipedia.org/wiki/Flagship_Airlines_Flight_3379"}, {"title": "Morrisville, North Carolina", "link": "https://wikipedia.org/wiki/Morrisville,_North_Carolina"}, {"title": "Raleigh-Durham International Airport", "link": "https://wikipedia.org/wiki/Raleigh%E2%80%93Durham_International_Airport"}]}, {"year": "1995", "text": "Banat Air Flight 166 crashes in Sommacampagna near Verona Villafranca Airport in Verona, Italy, killing 49.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Banat_Air_Flight_166\" title=\"Banat Air Flight 166\">Banat Air Flight 166</a> crashes in <a href=\"https://wikipedia.org/wiki/Sommacampagna\" title=\"Sommacampagna\">Sommacampagna</a> near <a href=\"https://wikipedia.org/wiki/Verona_Villafranca_Airport\" title=\"Verona Villafranca Airport\">Verona Villafranca Airport</a> in <a href=\"https://wikipedia.org/wiki/Verona\" title=\"Verona\">Verona</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>, killing 49.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Banat_Air_Flight_166\" title=\"Banat Air Flight 166\">Banat Air Flight 166</a> crashes in <a href=\"https://wikipedia.org/wiki/Sommacampagna\" title=\"Sommacampagna\">Sommacampagna</a> near <a href=\"https://wikipedia.org/wiki/Verona_Villafranca_Airport\" title=\"Verona Villafranca Airport\">Verona Villafranca Airport</a> in <a href=\"https://wikipedia.org/wiki/Verona\" title=\"Verona\">Verona</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>, killing 49.", "links": [{"title": "Banat Air Flight 166", "link": "https://wikipedia.org/wiki/Banat_Air_Flight_166"}, {"title": "Sommacampagna", "link": "https://wikipedia.org/wiki/Sommacampagna"}, {"title": "Verona Villafranca Airport", "link": "https://wikipedia.org/wiki/Verona_Villafranca_Airport"}, {"title": "Verona", "link": "https://wikipedia.org/wiki/Verona"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}]}, {"year": "2001", "text": "San<PERSON>, the building housing the Indian Parliament, is attacked by terrorists. Twelve people are killed, including the terrorists.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Sansad_Bhavan\" class=\"mw-redirect\" title=\"Sansad Bhavan\">Sansad Bhavan</a>, the building housing the <a href=\"https://wikipedia.org/wiki/Parliament_of_India\" title=\"Parliament of India\">Indian Parliament</a>, <a href=\"https://wikipedia.org/wiki/2001_Indian_Parliament_attack\" title=\"2001 Indian Parliament attack\">is attacked by terrorists</a>. Twelve people are killed, including the terrorists.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bhavan\" class=\"mw-redirect\" title=\"Sansad Bhavan\">Sansad Bhavan</a>, the building housing the <a href=\"https://wikipedia.org/wiki/Parliament_of_India\" title=\"Parliament of India\">Indian Parliament</a>, <a href=\"https://wikipedia.org/wiki/2001_Indian_Parliament_attack\" title=\"2001 Indian Parliament attack\">is attacked by terrorists</a>. Twelve people are killed, including the terrorists.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>van"}, {"title": "Parliament of India", "link": "https://wikipedia.org/wiki/Parliament_of_India"}, {"title": "2001 Indian Parliament attack", "link": "https://wikipedia.org/wiki/2001_Indian_Parliament_attack"}]}, {"year": "2002", "text": "European Union enlargement: The EU announces that Cyprus, the Czech Republic, Estonia, Hungary, Latvia, Lithuania, Malta, Poland, Slovakia, and Slovenia will become members on May 1, 2004.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/2004_enlargement_of_the_European_Union\" title=\"2004 enlargement of the European Union\">European Union enlargement</a>: The EU announces that <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a>, <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">the Czech Republic</a>, <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>, <a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a>, <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>, and <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> will become members on May 1, 2004.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2004_enlargement_of_the_European_Union\" title=\"2004 enlargement of the European Union\">European Union enlargement</a>: The EU announces that <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a>, <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">the Czech Republic</a>, <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>, <a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a>, <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>, and <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> will become members on May 1, 2004.", "links": [{"title": "2004 enlargement of the European Union", "link": "https://wikipedia.org/wiki/2004_enlargement_of_the_European_Union"}, {"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}, {"title": "Czech Republic", "link": "https://wikipedia.org/wiki/Czech_Republic"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Hungary", "link": "https://wikipedia.org/wiki/Hungary"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}, {"title": "Poland", "link": "https://wikipedia.org/wiki/Poland"}, {"title": "Slovakia", "link": "https://wikipedia.org/wiki/Slovakia"}, {"title": "Slovenia", "link": "https://wikipedia.org/wiki/Slovenia"}]}, {"year": "2003", "text": "Iraq War: Operation Red Dawn: Former Iraqi President <PERSON> is captured near his home town of Tikrit.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Red_Dawn\" class=\"mw-redirect\" title=\"Operation Red Dawn\">Operation Red Dawn</a>: Former Iraqi President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is captured near his home town of <a href=\"https://wikipedia.org/wiki/Tikrit\" title=\"Tikrit\">Tikrit</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Red_Dawn\" class=\"mw-redirect\" title=\"Operation Red Dawn\">Operation Red Dawn</a>: Former Iraqi President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is captured near his home town of <a href=\"https://wikipedia.org/wiki/Tikrit\" title=\"Tikrit\">Tikrit</a>.", "links": [{"title": "Iraq War", "link": "https://wikipedia.org/wiki/Iraq_War"}, {"title": "Operation Red Dawn", "link": "https://wikipedia.org/wiki/Operation_Red_Dawn"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tikrit"}]}, {"year": "2007", "text": "The Treaty of Lisbon is signed by the EU member states to amend both the Treaty of Rome and the Maastricht Treaty which together form the constitutional basis of the EU. The Treaty of Lisbon is effective from 1 December 2009.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Lisbon\" title=\"Treaty of Lisbon\">Treaty of Lisbon</a> is signed by the <a href=\"https://wikipedia.org/wiki/Member_state_of_the_European_Union\" title=\"Member state of the European Union\">EU member states</a> to amend both the <a href=\"https://wikipedia.org/wiki/Treaty_of_Rome\" title=\"Treaty of Rome\">Treaty of Rome</a> and the <a href=\"https://wikipedia.org/wiki/Maastricht_Treaty\" title=\"Maastricht Treaty\">Maastricht Treaty</a> which together form the constitutional basis of the EU. The Treaty of Lisbon is effective from 1 December 2009.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Lisbon\" title=\"Treaty of Lisbon\">Treaty of Lisbon</a> is signed by the <a href=\"https://wikipedia.org/wiki/Member_state_of_the_European_Union\" title=\"Member state of the European Union\">EU member states</a> to amend both the <a href=\"https://wikipedia.org/wiki/Treaty_of_Rome\" title=\"Treaty of Rome\">Treaty of Rome</a> and the <a href=\"https://wikipedia.org/wiki/Maastricht_Treaty\" title=\"Maastricht Treaty\">Maastricht Treaty</a> which together form the constitutional basis of the EU. The Treaty of Lisbon is effective from 1 December 2009.", "links": [{"title": "Treaty of Lisbon", "link": "https://wikipedia.org/wiki/Treaty_of_Lisbon"}, {"title": "Member state of the European Union", "link": "https://wikipedia.org/wiki/Member_state_of_the_European_Union"}, {"title": "Treaty of Rome", "link": "https://wikipedia.org/wiki/Treaty_of_Rome"}, {"title": "Maastricht Treaty", "link": "https://wikipedia.org/wiki/Maastricht_Treaty"}]}], "Births": [{"year": "1272", "text": "King <PERSON> of Sicily (d. 1337)", "html": "1272 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a> (d. 1337)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a> (d. 1337)", "links": [{"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily"}]}, {"year": "1363", "text": "<PERSON>, chancellor of the University of Paris (d. 1429)", "html": "1363 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, chancellor of the <a href=\"https://wikipedia.org/wiki/University_of_Paris\" title=\"University of Paris\">University of Paris</a> (d. 1429)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, chancellor of the <a href=\"https://wikipedia.org/wiki/University_of_Paris\" title=\"University of Paris\">University of Paris</a> (d. 1429)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "University of Paris", "link": "https://wikipedia.org/wiki/University_of_Paris"}]}, {"year": "1476", "text": "<PERSON>, Dominican tertiary and stigmatic (d. 1544)", "html": "1476 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican tertiary and stigmatic (d. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican tertiary and stigmatic (d. 1544)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1484", "text": "<PERSON>, German Lutheran (d. 1551)", "html": "1484 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Lutheran (d. 1551)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Lutheran (d. 1551)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1491", "text": "<PERSON>, Spanish theologian and economist (d. 1586)", "html": "1491 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_de_Azpilcueta\" title=\"<PERSON>ta\"><PERSON></a>, Spanish theologian and economist (d. 1586)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_de_Azpilcueta\" title=\"<PERSON>\"><PERSON></a>, Spanish theologian and economist (d. 1586)", "links": [{"title": "Martín de Azpilcueta", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_de_Azpilcueta"}]}, {"year": "1499", "text": "<PERSON><PERSON>, German Lutheran pastor (d. 1558)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German Lutheran pastor (d. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German Lutheran pastor (d. 1558)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>us_Menius"}]}, {"year": "1521", "text": "<PERSON> <PERSON><PERSON> (d. 1590)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/Pope_Sixtus_V\" title=\"Pope Sixtus V\"><PERSON> <PERSON><PERSON> V</a> (d. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Sixtus_V\" title=\"Pope Sixtus V\"><PERSON> <PERSON>tus V</a> (d. 1590)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Pope_Six<PERSON>_V"}]}, {"year": "1533", "text": "<PERSON> of Sweden (d. 1577)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> XIV of Sweden\"><PERSON> of Sweden</a> (d. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> XIV of Sweden\"><PERSON> of Sweden</a> (d. 1577)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1553", "text": "<PERSON> of France (d. 1610)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> (d. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> (d. 1610)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}]}, {"year": "1560", "text": "<PERSON><PERSON><PERSON>, Duke of Sully, 2nd Prime Minister of France (d. 1641)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>en_de_B%C3%A<PERSON><PERSON>,_Duke_of_Sully\" title=\"<PERSON><PERSON><PERSON>, Duke of Sully\"><PERSON><PERSON><PERSON>, Duke of Sully</a>, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_B%C3%A<PERSON><PERSON>,_Duke_of_Sully\" title=\"<PERSON><PERSON><PERSON>, Duke of Sully\"><PERSON><PERSON><PERSON>, Duke of Sully</a>, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1641)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Sully", "link": "https://wikipedia.org/wiki/Maximilien_de_B%C3%A9thune,_Duke_of_Sully"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1585", "text": "<PERSON> of Hawthornden, Scottish poet (d. 1649)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hawthornden\" title=\"<PERSON> of Hawthornden\"><PERSON> of Hawthornden</a>, Scottish poet (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hawthornden\" title=\"<PERSON> of Hawthornden\"><PERSON> of Hawthornden</a>, Scottish poet (d. 1649)", "links": [{"title": "<PERSON> of Hawthornden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Hawthornden"}]}, {"year": "1640", "text": "<PERSON>, English chemist and academic (d. 1696)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (d. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (d. 1696)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1662", "text": "<PERSON>, Italian astronomer and philosopher (d. 1729)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and philosopher (d. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and philosopher (d. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1678", "text": "Yong<PERSON>g Emperor of China (d. 1735)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/Yongzheng_Emperor\" title=\"Yongzheng Emperor\">Yongzheng Emperor</a> of China (d. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yongzheng_Emperor\" title=\"Yongzheng Emperor\">Yongzheng Emperor</a> of China (d. 1735)", "links": [{"title": "Yongzheng Emperor", "link": "https://wikipedia.org/wiki/Yongzheng_Emperor"}]}, {"year": "1720", "text": "<PERSON>, Italian playwright (d. 1804)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian playwright (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian playwright (d. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, German astronomer and philosopher (d. 1802)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and philosopher (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and philosopher (d. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1769", "text": "<PERSON>, English judge (d. 1844)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English judge (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English judge (d. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1780", "text": "<PERSON>, German chemist, invented the <PERSON><PERSON><PERSON><PERSON><PERSON>'s lamp (d. 1849)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_D%C3%B6bereiner\" title=\"<PERSON>\"><PERSON></a>, German chemist, invented the <a href=\"https://wikipedia.org/wiki/D%C3%B6<PERSON>einer%27s_lamp\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>'s lamp\"><PERSON><PERSON><PERSON><PERSON><PERSON>'s lamp</a> (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6bereiner\" title=\"<PERSON>\"><PERSON></a>, German chemist, invented the <a href=\"https://wikipedia.org/wiki/D%C3%B6<PERSON>einer%27s_lamp\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>'s lamp\"><PERSON><PERSON><PERSON><PERSON><PERSON>'s lamp</a> (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>'s lamp", "link": "https://wikipedia.org/wiki/D%C3%B6<PERSON>einer%27s_lamp"}]}, {"year": "1784", "text": "Arch<PERSON><PERSON> of Austria (d. 1864)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON> of Austria</a> (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON> of Austria</a> (d. 1864)", "links": [{"title": "Archdu<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Austria"}]}, {"year": "1797", "text": "<PERSON>, German journalist, poet, and critic (d. 1856)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist, poet, and critic (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist, poet, and critic (d. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, Canadian journalist and politician, 5th Premier of Nova Scotia (d. 1873)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Nova Scotia", "link": "https://wikipedia.org/wiki/Premier_of_Nova_Scotia"}]}, {"year": "1814", "text": "<PERSON>, Brazilian nurse and philanthropist (d. 1880)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Ana_N%C3%A9ri\" title=\"<PERSON>\"><PERSON></a>, Brazilian nurse and philanthropist (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ana_N%C3%A9ri\" title=\"<PERSON>\"><PERSON></a>, Brazilian nurse and philanthropist (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ana_N%C3%A9ri"}]}, {"year": "1816", "text": "<PERSON>, German engineer and businessman, founded Siemens (d. 1892)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Siemens\" title=\"Siemens\">Siemens</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Siemens\" title=\"Siemens\">Siemens</a> (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Siemens", "link": "https://wikipedia.org/wiki/Siemens"}]}, {"year": "1818", "text": "<PERSON>, 16th First Lady of the United States (d. 1882)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 16th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 16th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1830", "text": "<PERSON><PERSON>, Danish feminist, novelist and telegraphist (d. 1892)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish feminist, novelist and telegraphist (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish feminist, novelist and telegraphist (d. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, German painter and academic (d. 1904)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and academic (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and academic (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Dutch philosopher, theologian, and academic (d. 1921)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher, theologian, and academic (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher, theologian, and academic (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian-Austrian field marshal (d. 1920)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Svetozar_Boroevi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian-Austrian field marshal (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Svetozar_Boroevi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian-Austrian field marshal (d. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Svetozar_Boroevi%C4%87"}]}, {"year": "1860", "text": "<PERSON>, French actor (d. 1925)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, American woodcarver and politician, 36th Mayor of Milwaukee (d. 1947)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American woodcarver and politician, 36th <a href=\"https://wikipedia.org/wiki/Mayor_of_Milwaukee\" class=\"mw-redirect\" title=\"Mayor of Milwaukee\">Mayor of Milwaukee</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American woodcarver and politician, 36th <a href=\"https://wikipedia.org/wiki/Mayor_of_Milwaukee\" class=\"mw-redirect\" title=\"Mayor of Milwaukee\">Mayor of Milwaukee</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Milwaukee", "link": "https://wikipedia.org/wiki/Mayor_of_Milwaukee"}]}, {"year": "1867", "text": "<PERSON><PERSON>, Norwegian physicist and author (d. 1917)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Kristian B<PERSON>\"><PERSON><PERSON></a>, Norwegian physicist and author (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Kristian B<PERSON>\"><PERSON><PERSON></a>, Norwegian physicist and author (d. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kristian_Birk<PERSON>nd"}]}, {"year": "1870", "text": "<PERSON>, American actor and director (d. 1940)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Canadian painter and author (d. 1945)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and author (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and author (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Russian pianist and educator (d. 1944)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vinne\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and educator (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vinne\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and educator (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A9vinne"}]}, {"year": "1882", "text": "<PERSON>, African-American social worker (d. 1971)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American social worker (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American social worker (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American librarian and bibliographer (d. 1950)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian and bibliographer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian and bibliographer (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek actor, director, and playwright (d. 1951)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek actor, director, and playwright (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek actor, director, and playwright (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>eakis"}]}, {"year": "1885", "text": "<PERSON>, American mathematician (d. 1940)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Hungarian-American mathematician and academic (d. 1985)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3lya\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician and academic (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3lya\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician and academic (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_P%C3%B3lya"}]}, {"year": "1887", "text": "<PERSON>, American colonel, Medal of Honor recipient (d. 1964)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Alvin <PERSON> York\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Alvin <PERSON> York\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alvin_<PERSON>._York"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Spanish anarchist feminist (d. 1970)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Luc%C3%ADa_S%C3%A1nchez_<PERSON>il\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish anarchist feminist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luc%C3%ADa_S%C3%A1nchez_Saornil\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish anarchist feminist (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luc%C3%ADa_S%C3%A1nchez_Saornil"}]}, {"year": "1897", "text": "<PERSON>, Dutch architect, designed the Savoy Homann Bidakara Hotel (d. 1961)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch architect, designed the <a href=\"https://wikipedia.org/wiki/Savoy_Homann_Bidakara_Hotel\" class=\"mw-redirect\" title=\"Savoy Homann Bidakara Hotel\">Savoy Homann Bidakara Hotel</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch architect, designed the <a href=\"https://wikipedia.org/wiki/Savoy_Homann_Bidakara_Hotel\" class=\"mw-redirect\" title=\"Savoy Homann Bidakara Hotel\">Savoy Homann Bidakara Hotel</a> (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Savoy Homann Bidakara Hotel", "link": "https://wikipedia.org/wiki/Savoy_Homann_Bidakara_Hotel"}]}, {"year": "1897", "text": "<PERSON>, American journalist and author (d. 1969)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (d. 1969)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Romanian-American conductor and educator (d. 1970)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-American conductor and educator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-American conductor and educator (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Estonian singer, violinist, and bagpipe player (d. 1987)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Olev_Roomet\" title=\"Olev Roomet\"><PERSON><PERSON></a>, Estonian singer, violinist, and <a href=\"https://wikipedia.org/wiki/Bagpipe\" class=\"mw-redirect\" title=\"Bagpipe\">bagpipe</a> player (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>v_Roomet\" title=\"Olev Roomet\"><PERSON><PERSON></a>, Estonian singer, violinist, and <a href=\"https://wikipedia.org/wiki/Bagpipe\" class=\"mw-redirect\" title=\"Bagpipe\">bagpipe</a> player (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Olev_Roomet"}, {"title": "Bagpipe", "link": "https://wikipedia.org/wiki/Bagpipe"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek philosopher and politician, 138th Prime Minister of Greece (d. 1986)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek philosopher and politician, 138th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek philosopher and politician, 138th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, American sociologist and academic (d. 1979)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sociologist and academic (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sociologist and academic (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American activist (d. 1986)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Spanish guitarist and composer (d. 1993)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish guitarist and composer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish guitarist and composer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American writer and dance critic (d. 2007)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and dance critic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and dance critic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "Princess <PERSON> of Greece and Denmark (d. 1968)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark\" title=\"Princess <PERSON> of Greece and Denmark\">Princess <PERSON> of Greece and Denmark</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark\" title=\"Princess <PERSON> of Greece and Denmark\">Princess <PERSON> of Greece and Denmark</a> (d. 1968)", "links": [{"title": "Princess <PERSON> of Greece and Denmark", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark"}]}, {"year": "1906", "text": "<PERSON><PERSON>, South African-English soldier and author (d. 1996)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-English soldier and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-English soldier and author (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, English geologist, academic, and physicist (d. 1958)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English geologist, academic, and physicist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English geologist, academic, and physicist (d. 1958)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Brazilian historian and activist (d. 1995)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Plinio_Corr%C3%AAa_<PERSON>_<PERSON>\" title=\"Plinio Corrêa <PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian historian and activist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Plinio_Corr%C3%AAa_<PERSON>_<PERSON>\" title=\"Plinio Corrêa <PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian historian and activist (d. 1995)", "links": [{"title": "Plinio <PERSON>rr<PERSON>", "link": "https://wikipedia.org/wiki/Plinio_Corr%C3%AA<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American film actor (d. 1971)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film actor (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film actor (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Norwegian economist and mathematician, Nobel Prize laureate (d. 1999)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian economist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian economist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tryg<PERSON>_<PERSON>o"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1911", "text": "<PERSON>, American poet and painter (d. 1972)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and painter (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and painter (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, Brazilian singer-songwriter and accordion player (d. 1989)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter and accordion player (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter and accordion player (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ga"}]}, {"year": "1913", "text": "<PERSON>, American boxer and actor (d. 1998)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English historian and author (d. 2004)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English comedian and actor (d. 1993)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English comedian and actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English comedian and actor (d. 1993)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1915", "text": "<PERSON><PERSON> <PERSON><PERSON>, South African lawyer and politician, 4th State President of South Africa (d. 1983)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (d. 1983)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "State President of South Africa", "link": "https://wikipedia.org/wiki/State_President_of_South_Africa"}]}, {"year": "1916", "text": "<PERSON>, American author and illustrator (d. 2000)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, German captain and pilot (d. 1942)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German captain and pilot (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German captain and pilot (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American economist and politician, 60th United States Secretary of State (d. 2021)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American economist and politician, 60th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American economist and politician, 60th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Turkish film producer, director and screenwriter (d. 1987)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Turgut_Demira%C4%9F\" title=\"Turgut <PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish film producer, director and screenwriter (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turgut_Demira%C4%9F\" title=\"Turgut Demir<PERSON>ğ\"><PERSON><PERSON><PERSON></a>, Turkish film producer, director and screenwriter (d. 1987)", "links": [{"title": "Turgut Demirağ", "link": "https://wikipedia.org/wiki/Turgut_Demira%C4%9F"}]}, {"year": "1923", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 2020)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1923", "text": "<PERSON>, American baseball player (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor, singer, and dancer", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American poet and academic (d. 1980)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and academic (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and academic (d. 1980)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1928", "text": "<PERSON>, American philosopher and mathematician (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/1928\" title=\"1928\">1928</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and mathematician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1928\" title=\"1928\">1928</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and mathematician (d. 2016)", "links": [{"title": "1928", "link": "https://wikipedia.org/wiki/1928"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian actor and producer (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Dutch Jewish author of books for children and adults (d. 2006)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Ida_Vos\" title=\"<PERSON>\"><PERSON></a>, Dutch Jewish author of books for children and adults (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ida_Vos\" title=\"<PERSON> Vos\"><PERSON></a>, Dutch Jewish author of books for children and adults (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ida_Vos"}]}, {"year": "1933", "text": "<PERSON>, French automotive designer", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French automotive designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French automotive designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American film producer (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish physician and academic (d. 2009)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/T%C3%BCrkan_Saylan\" title=\"Türkan Saylan\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish physician and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%BCrkan_Saylan\" title=\"Türkan Saylan\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish physician and academic (d. 2009)", "links": [{"title": "Türkan <PERSON>", "link": "https://wikipedia.org/wiki/T%C3%BCrkan_Saylan"}]}, {"year": "1936", "text": "Prince <PERSON><PERSON>, <PERSON><PERSON>, Swiss humanitarian and religious leader (d. 2025)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Khan_IV\" title=\"<PERSON>ga Khan IV\">Prince <PERSON><PERSON>, <PERSON><PERSON></a>, Swiss humanitarian and religious leader (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Khan_IV\" title=\"Aga Khan IV\">Prince <PERSON><PERSON>, <PERSON><PERSON> IV</a>, Swiss humanitarian and religious leader (d. 2025)", "links": [{"title": "<PERSON><PERSON> Khan IV", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_IV"}]}, {"year": "1938", "text": "<PERSON>, American basketball player (d. 1987)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 1987)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Indian economist and academic (d. 2005)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian economist and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian economist and academic (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "1942", "text": "<PERSON>, English playwright and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian baseball player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American businessman, politician, and activist (d. 2020)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, politician, and activist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, politician, and activist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, British athlete (d. 1970)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Lillian_Board\" title=\"Lillian Board\"><PERSON></a>, British athlete (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lillian_Board\" title=\"Lillian Board\"><PERSON></a>, British athlete (d. 1970)", "links": [{"title": "Lillian Board", "link": "https://wikipedia.org/wiki/Lillian_Board"}]}, {"year": "1948", "text": "<PERSON>, American musician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American economist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian ice hockey player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American basketball player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actor and director", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American musician and actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Morris_Day\" title=\"Morris Day\"><PERSON> Day</a>, American musician and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morris_Day\" title=\"Morris Day\"><PERSON></a>, American musician and actor", "links": [{"title": "Morris Day", "link": "https://wikipedia.org/wiki/Morris_Day"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American football player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American football player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American football coach and analyst", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach and analyst", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach and analyst", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, American-Latvian politician, 23rd Prime Minister of Latvia", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Arturs_Kri%C5%A1j%C4%81<PERSON>_<PERSON><PERSON>%C5%86%C5%A1\" class=\"mw-redirect\" title=\"Arturs Krišj<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American-Latvian politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arturs_Kri%C5%A1j%C4%81<PERSON>_<PERSON><PERSON>%C5%86%C5%A1\" class=\"mw-redirect\" title=\"Arturs Krišj<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American-Latvian politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arturs_Kri%C5%A1j%C4%81nis_Kari%C5%86%C5%A1"}, {"title": "Prime Minister of Latvia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Latvia"}]}, {"year": "1965", "text": "<PERSON>, Austrian politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor, singer, songwriter, producer, and comedian", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, songwriter, producer, and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, songwriter, producer, and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Russian ice hockey player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Swedish heavy metal singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Matti_K%C3%A4rki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish heavy metal singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matti_K%C3%A4rki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish heavy metal singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matti_K%C3%A4rki"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter, guitarist, author, and filmmaker", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, author, and filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, author, and filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American baseball player and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer, songwriter and pianist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> C<PERSON>orla\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>la\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>la"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Finnish freestyle swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>p%C3%A4l%C3%A4\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish <a href=\"https://wikipedia.org/wiki/Freestyle_swimming\" title=\"Freestyle swimming\">freestyle</a> swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>p%C3%A4l%C3%A4\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish <a href=\"https://wikipedia.org/wiki/Freestyle_swimming\" title=\"Freestyle swimming\">freestyle</a> swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_Sepp%C3%A4l%C3%A4"}, {"title": "Freestyle swimming", "link": "https://wikipedia.org/wiki/Freestyle_swimming"}]}, {"year": "1987", "text": "<PERSON>, American mass murderer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mass_murderer)\" title=\"<PERSON> (mass murderer)\"><PERSON></a>, American mass murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mass_murderer)\" title=\"<PERSON> (mass murderer)\"><PERSON></a>, American mass murderer", "links": [{"title": "<PERSON> (mass murderer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mass_murderer)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American golfer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Kenyan runner", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Hellen Obiri\"><PERSON><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Hellen Obiri\"><PERSON><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>en_<PERSON><PERSON>i"}]}, {"year": "1989", "text": "<PERSON>, American author", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American singer-songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Swift\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Taylor Swift\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English YouTuber, actor, and author", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English YouTuber, actor, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English YouTuber, actor, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Dutch tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Arant<PERSON>_R<PERSON>\" title=\"Arant<PERSON> Rus\"><PERSON><PERSON><PERSON></a>, Dutch tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ara<PERSON><PERSON>_<PERSON>\" title=\"Arant<PERSON> Rus\"><PERSON><PERSON><PERSON></a>, Dutch tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nt<PERSON>_<PERSON>us"}]}, {"year": "1991", "text": "<PERSON>, Canadian martial artist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Russian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Australian rugby league player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Venezuelan baseball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Spanish tennis player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ribera\" title=\"<PERSON> Ribera\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Riber<PERSON>\" title=\"<PERSON> Ribera\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Bassols_Ribera"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Swiss tennis player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Australian cricketer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American football player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bow<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bow<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "558", "text": "<PERSON><PERSON><PERSON>, Frankish king (b. 496)", "html": "558 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON> I</a>, Frankish king (b. 496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON> I</a>, Frankish king (b. 496)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}]}, {"year": "769", "text": "<PERSON>, Chinese politician (b. 709)", "html": "769 - <a href=\"https://wikipedia.org/wiki/Du_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician (b. 709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician (b. 709)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Du_<PERSON>"}]}, {"year": "838", "text": "<PERSON><PERSON><PERSON> of Aquitaine (b. 797)", "html": "838 - <a href=\"https://wikipedia.org/wiki/Pepin_I_of_Aquitaine\" title=\"<PERSON><PERSON><PERSON> I of Aquitaine\"><PERSON><PERSON><PERSON> <PERSON> of Aquitaine</a> (b. 797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pepin_I_of_Aquitaine\" title=\"<PERSON>epin I of Aquitaine\"><PERSON><PERSON><PERSON> <PERSON> of Aquitaine</a> (b. 797)", "links": [{"title": "<PERSON><PERSON><PERSON> of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>in_I_of_Aquitaine"}]}, {"year": "859", "text": "<PERSON><PERSON><PERSON>, archbishop of Milan", "html": "859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> <PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Milan\" title=\"Roman Catholic Archdiocese of Milan\">Milan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> II</a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Milan\" title=\"Roman Catholic Archdiocese of Milan\">Milan</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Roman Catholic Archdiocese of Milan", "link": "https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Milan"}]}, {"year": "1124", "text": "<PERSON> <PERSON><PERSON><PERSON> (b. 1065)", "html": "1124 - <a href=\"https://wikipedia.org/wiki/Pope_Callixtus_II\" title=\"<PERSON> Callixtus II\">Pope <PERSON><PERSON><PERSON> II</a> (b. 1065)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Callixtus_II\" title=\"Pope Callixtus II\"><PERSON> <PERSON><PERSON><PERSON> II</a> (b. 1065)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_Callixtus_II"}]}, {"year": "1126", "text": "<PERSON>, Duke of Bavaria (b. 1075)", "html": "1126 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1075)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1075)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1204", "text": "<PERSON><PERSON><PERSON>, Spanish rabbi and philosopher (b. 1135)", "html": "1204 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish rabbi and philosopher (b. 1135)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish rabbi and philosopher (b. 1135)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1250", "text": "<PERSON>, Holy Roman Emperor (b. 1194)", "html": "1250 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1194)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1194)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1272", "text": "<PERSON><PERSON> of Regensburg, German preacher", "html": "1272 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Regensburg\" title=\"<PERSON><PERSON> of Regensburg\"><PERSON><PERSON> of Regensburg</a>, German preacher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Regensburg\" title=\"<PERSON><PERSON> of Regensburg\"><PERSON><PERSON> of Regensburg</a>, German preacher", "links": [{"title": "<PERSON><PERSON> of Regensburg", "link": "https://wikipedia.org/wiki/Bertold_of_Regensburg"}]}, {"year": "1404", "text": "<PERSON>, Duke of Bavaria (b. 1336)", "html": "1404 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1336)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1336)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1466", "text": "<PERSON><PERSON><PERSON>, Italian painter and sculptor (b. 1386)", "html": "1466 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and sculptor (b. 1386)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and sculptor (b. 1386)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1516", "text": "<PERSON>, German cryptographer and historian (b. 1462)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cryptographer and historian (b. 1462)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cryptographer and historian (b. 1462)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1521", "text": "<PERSON> of Portugal (b. 1469)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1469)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1469)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1557", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian mathematician and engineer (b. 1499)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_Fontana_Tartaglia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>ò Fontana Tartaglia\"><PERSON><PERSON><PERSON><PERSON></a>, Italian mathematician and engineer (b. 1499)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_Fontana_Tartaglia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> Fontana Tartaglia\"><PERSON><PERSON><PERSON><PERSON></a>, Italian mathematician and engineer (b. 1499)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_Fontana_Tartaglia"}]}, {"year": "1565", "text": "<PERSON>, Swiss botanist and physician (b. 1516)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss botanist and physician (b. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss botanist and physician (b. 1516)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1621", "text": "<PERSON><PERSON>, queen of <PERSON> I of Sweden (b. 1535)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (b. 1535)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1671", "text": "<PERSON>, Italian Roman Catholic priest(b. 1592)", "html": "1671 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> priest(b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> priest(b. 1592)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Roman Catholic", "link": "https://wikipedia.org/wiki/Roman_Catholic"}]}, {"year": "1716", "text": "<PERSON>, French painter (b. 1640)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1640)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1721", "text": "<PERSON>, Scottish sailor (b. 1676)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish sailor (b. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish sailor (b. 1676)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, English philosopher and author (b. 1676)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English philosopher and author (b. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English philosopher and author (b. 1676)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)"}]}, {"year": "1754", "text": "<PERSON><PERSON><PERSON>, Ottoman sultan (b. 1696)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>hmud <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1696)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON><PERSON>, Canadian Acadia leader (b. 1684)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/No%C3%ABl_Doiron\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian <a href=\"https://wikipedia.org/wiki/Acadia\" title=\"Acadia\">Acadia</a> leader (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/No%C3%ABl_Doiron\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian <a href=\"https://wikipedia.org/wiki/Acadia\" title=\"Acadia\">Acadia</a> leader (b. 1684)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/No%C3%ABl_Doiron"}, {"title": "Acadia", "link": "https://wikipedia.org/wiki/Acadia"}]}, {"year": "1769", "text": "<PERSON>, German poet and hymn-writer  (b. 1715)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/Christian_F%C3%BCrchte<PERSON><PERSON>_Gellert\" title=\"Christian Fürchtegott Gellert\"><PERSON></a>, German poet and hymn-writer (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_F%C3%BCrchtegott_Gellert\" title=\"Christian Fürchtegott Gellert\"><PERSON></a>, German poet and hymn-writer (b. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_F%C3%BCrchte<PERSON><PERSON>_Gellert"}]}, {"year": "1783", "text": "<PERSON><PERSON><PERSON>, Swedish astronomer and demographer (b. 1717)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish astronomer and demographer (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish astronomer and demographer (b. 1717)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1784", "text": "<PERSON>, English poet and lexicographer (b. 1709)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and lexicographer (b. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and lexicographer (b. 1709)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON><PERSON>, 7th Prince of Ligne, Belgian-Austrian field marshal (b. 1735)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Prince_of_Ligne\" title=\"<PERSON><PERSON><PERSON>, 7th Prince of Ligne\"><PERSON><PERSON><PERSON>, 7th Prince of Ligne</a>, Belgian-Austrian field marshal (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Prince_of_Ligne\" title=\"<PERSON><PERSON><PERSON>, 7th Prince of Ligne\"><PERSON><PERSON><PERSON>, 7th Prince of Ligne</a>, Belgian-Austrian field marshal (b. 1735)", "links": [{"title": "<PERSON><PERSON><PERSON>, 7th Prince of Ligne", "link": "https://wikipedia.org/wiki/<PERSON>,_7th_Prince_of_Ligne"}]}, {"year": "1849", "text": "<PERSON>, German botanist and entomologist (b. 1766)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and entomologist (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and entomologist (b. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American general, lawyer, and politician (b. 1823)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, German poet and playwright (b. 1813)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Christian <PERSON>\"><PERSON></a>, German poet and playwright (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, German botanist and explorer (b. 1794)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and explorer (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and explorer (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Croatian author and poet (b. 1838)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/August_%C5%A0enoa\" title=\"August <PERSON>\">August <PERSON></a>, Croatian author and poet (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_%C5%A0enoa\" title=\"August <PERSON>\">August <PERSON></a>, Croatian author and poet (b. 1838)", "links": [{"title": "August <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_%C5%A0enoa"}]}, {"year": "1883", "text": "<PERSON>, French poet and critic (b. 1812)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, German lawyer and politician, 3rd Mayor of Marburg (b. 1816)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Marburg\" class=\"mw-redirect\" title=\"Mayor of Marburg\">Mayor of Marburg</a> (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Marburg\" class=\"mw-redirect\" title=\"Mayor of Marburg\">Mayor of Marburg</a> (b. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August_<PERSON>"}, {"title": "Mayor of Marburg", "link": "https://wikipedia.org/wiki/Mayor_of_Marburg"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Hungarian physicist and engineer (b. 1800)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/%C3%81ny<PERSON>_Jedlik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian physicist and engineer (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81ny<PERSON>_Jedlik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian physicist and engineer (b. 1800)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81nyos_Jedlik"}]}, {"year": "1908", "text": "<PERSON>, French photographer and historian (b. 1825)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and historian (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and historian (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Australian cricketer (b. 1878)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON>, German physicist and academic (b. 1850)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Wold<PERSON><PERSON>_Voigt\" title=\"Wold<PERSON><PERSON> Voigt\"><PERSON><PERSON><PERSON><PERSON></a>, German physicist and academic (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wold<PERSON><PERSON>_Voigt\" title=\"Wold<PERSON><PERSON> Voigt\"><PERSON><PERSON><PERSON><PERSON></a>, German physicist and academic (b. 1850)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wold<PERSON>r_Voigt"}]}, {"year": "1922", "text": "<PERSON>, American painter and photographer (b. 1857)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and photographer (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and photographer (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Icelandic poet and politician, 1st Prime Minister of Iceland (b. 1861)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic poet and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic poet and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (b. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Iceland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iceland"}]}, {"year": "1924", "text": "<PERSON>, English-born American labor leader, founded the American Federation of Labor (b. 1850)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born American labor leader, founded the <a href=\"https://wikipedia.org/wiki/American_Federation_of_Labor\" title=\"American Federation of Labor\">American Federation of Labor</a> (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born American labor leader, founded the <a href=\"https://wikipedia.org/wiki/American_Federation_of_Labor\" title=\"American Federation of Labor\">American Federation of Labor</a> (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Federation of Labor", "link": "https://wikipedia.org/wiki/American_Federation_of_Labor"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Turkish mathematician and academic (b. 1856)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish mathematician and academic (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish mathematician and academic (b. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Finnish physician (b. 1842)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish <a href=\"https://wikipedia.org/wiki/Physician\" title=\"Physician\">physician</a> (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish <a href=\"https://wikipedia.org/wiki/Physician\" title=\"Physician\">physician</a> (b. 1842)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l"}, {"title": "Physician", "link": "https://wikipedia.org/wiki/Physician"}]}, {"year": "1930", "text": "<PERSON>, Slovenian-Austrian chemist and physician, Nobel Prize laureate (b. 1869)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian-Austrian chemist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian-Austrian chemist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1931", "text": "<PERSON><PERSON>, French psychologist, sociologist, and anthropologist (b. 1840)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French psychologist, sociologist, and anthropologist (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French psychologist, sociologist, and anthropologist (b. 1840)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Greek painter and sculptor (b. 1853)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and sculptor (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and sculptor (b. 1853)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Jakobides"}]}, {"year": "1935", "text": "<PERSON>, French chemist and academic, Nobel Prize laureate (b. 1871)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Austrian-Polish religious leader, 26th Superior-General of the Society of Jesus (b. 1866)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian-Polish religious leader, 26th <a href=\"https://wikipedia.org/wiki/Superior-General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior-General of the Society of Jesus\">Superior-General of the Society of Jesus</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian-Polish religious leader, 26th <a href=\"https://wikipedia.org/wiki/Superior-General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior-General of the Society of Jesus\">Superior-General of the Society of Jesus</a> (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wlodi<PERSON>_Led%C3%<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Superior-General of the Society of Jesus", "link": "https://wikipedia.org/wiki/Superior-General_of_the_Society_of_Jesus"}]}, {"year": "1942", "text": "<PERSON>, American architect (b. 1868)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Russian-French painter and theorist (b. 1866)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-French painter and theorist (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-French painter and theorist (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, German concentration camp guard (b. 1923)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camp</a> guard (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camp</a> guard (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_<PERSON>"}, {"title": "Nazi concentration camps", "link": "https://wikipedia.org/wiki/Nazi_concentration_camps"}]}, {"year": "1945", "text": "<PERSON>, German concentration camp commandant (b. 1906)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camp</a> commandant (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camp</a> commandant (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nazi concentration camps", "link": "https://wikipedia.org/wiki/Nazi_concentration_camps"}]}, {"year": "1945", "text": "<PERSON>, Polish-German concentration camp supervisor (b. 1919)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camp</a> supervisor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camp</a> supervisor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nazi concentration camps", "link": "https://wikipedia.org/wiki/Nazi_concentration_camps"}]}, {"year": "1947", "text": "<PERSON>, American lawyer and author (b. 1879)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biographer)\" title=\"<PERSON> (biographer)\"><PERSON></a>, American lawyer and author (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biographer)\" title=\"<PERSON> (biographer)\"><PERSON></a>, American lawyer and author (b. 1879)", "links": [{"title": "<PERSON> (biographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biographer)"}]}, {"year": "1947", "text": "<PERSON>, Russian archaeologist, painter, and philosopher (b. 1874)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian archaeologist, painter, and philosopher (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian archaeologist, painter, and philosopher (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Hungarian mathematician and academic (b. 1902)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian mathematician and academic (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian mathematician and academic (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American director and composer (b. 1879)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director and composer (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director and composer (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Portuguese psychiatrist and neurosurgeon, Nobel Prize laureate (b. 1874)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Egas_<PERSON>iz\" class=\"mw-redirect\" title=\"Egas Moniz\"><PERSON><PERSON></a>, Portuguese psychiatrist and neurosurgeon, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>gas_<PERSON>iz\" class=\"mw-redirect\" title=\"Egas Moniz\"><PERSON><PERSON></a>, Portuguese psychiatrist and neurosurgeon, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1874)", "links": [{"title": "E<PERSON>", "link": "https://wikipedia.org/wiki/Egas_Moniz"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1960", "text": "<PERSON>, English author and activist (b. 1882)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American painter (b. 1860)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Grandma_<PERSON>\" title=\"Grandma <PERSON>\"><PERSON></a>, American painter (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grandma_<PERSON>\" title=\"Grandma Moses\"><PERSON></a>, American painter (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Grandma_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter and pianist  (b. 1905)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American admiral and diplomat, United States Ambassador to the Philippines (b. 1886)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ruance\" title=\"<PERSON>\"><PERSON></a>, American admiral and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Philippines\" class=\"mw-redirect\" title=\"United States Ambassador to the Philippines\">United States Ambassador to the Philippines</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ruance\" title=\"<PERSON>\"><PERSON></a>, American admiral and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Philippines\" class=\"mw-redirect\" title=\"United States Ambassador to the Philippines\">United States Ambassador to the Philippines</a> (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ruance"}, {"title": "United States Ambassador to the Philippines", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_Philippines"}]}, {"year": "1973", "text": "<PERSON>, English author (b. 1905)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Green\"><PERSON></a>, English author (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Egyptian-Turkish journalist, author, and politician (b. 1889)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Ya<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-Turkish journalist, author, and politician (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Ya<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-Turkish journalist, author, and politician (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English-American actor (b. 1889)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American lawyer and trade commissioner (b. 1893)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and trade commissioner (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and trade commissioner (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Turkish engineer and author (b. 1934)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Ogu<PERSON>_Atay\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>ay\"><PERSON><PERSON><PERSON></a>, Turkish engineer and author (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O<PERSON><PERSON>_<PERSON>ay\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>ay\"><PERSON><PERSON><PERSON></a>, Turkish engineer and author (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>z_Atay"}]}, {"year": "1979", "text": "<PERSON>, American actor and director (b. 1915)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (b. 1915)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish author, poet and translator (b. 1916)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Beh%C3%A7et_Necatigil\" title=\"Behçet Necatigil\"><PERSON><PERSON><PERSON><PERSON> Necatigil</a>, Turkish author, poet and translator (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beh%C3%A7et_Necatigil\" title=\"Behçet Necatigil\"><PERSON><PERSON><PERSON><PERSON> Necatigil</a>, Turkish author, poet and translator (b. 1916)", "links": [{"title": "Behçet Necatigil", "link": "https://wikipedia.org/wiki/Beh%C3%A7et_Necatigil"}]}, {"year": "1983", "text": "<PERSON>, Estonian-American priest and theologian (b. 1921)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-American priest and theologian (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-American priest and theologian (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Romanian poet and critic (b. 1933)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Nichita_St%C4%83nes<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian poet and critic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nichita_St%C4%83nescu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian poet and critic (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nichita_St%C4%83nescu"}]}, {"year": "1986", "text": "<PERSON>, British-American actress (b. 1909)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, British-American actress (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, British-American actress (b. 1909)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1986", "text": "<PERSON>, American activist (b. 1903)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Indian actress and journalist (b. 1955)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress and journalist (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress and journalist (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>il"}]}, {"year": "1992", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian businessman (b. 1899)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"K<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian businessman (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"K. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian businessman (b. 1899)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American businessman and philanthropist (b. 1899)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, French author (b. 1972)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French author (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French author (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A8s"}]}, {"year": "1995", "text": "<PERSON>, American author and educator (b. 1896)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English author and educator (b. 1920)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American historian, author, and academic (b. 1920)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a>, American historian, author, and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Ukrainian-born British impresario and media proprietor (b. 1906)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Lew_Grade\" title=\"Lew Grade\">Lew Grade</a>, Ukrainian-born British impresario and media proprietor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lew_Grade\" title=\"Lew Grade\">Lew Grade</a>, Ukrainian-born British impresario and media proprietor (b. 1906)", "links": [{"title": "Lew Grade", "link": "https://wikipedia.org/wiki/Lew_Grade"}]}, {"year": "1998", "text": "<PERSON>, Royal Naval Officer (b. 1922)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, Royal Naval Officer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, Royal Naval Officer (b. 1922)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Royal_Navy_officer)"}]}, {"year": "1998", "text": "<PERSON>, civil rights activist (b. 1919)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, civil rights activist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, civil rights activist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Canadian singer-songwriter and guitarist who founded The Lovin' Spoonful (b. 1944)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist who founded <a href=\"https://wikipedia.org/wiki/The_Lovin%27_Spoonful\" title=\"The Lovin' Spoonful\">The Lovin' Spoonful</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist who founded <a href=\"https://wikipedia.org/wiki/The_Lovin%27_Spoonful\" title=\"The Lovin' Spoonful\">The Lovin' Spoonful</a> (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "The Lovin' Spoonful", "link": "https://wikipedia.org/wiki/The_Lovin%27_Spoonful"}]}, {"year": "2004", "text": "<PERSON>, English computer scientist and academic (b. 1927)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_computer_scientist)\" class=\"mw-redirect\" title=\"<PERSON> (British computer scientist)\"><PERSON></a>, English computer scientist and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_computer_scientist)\" class=\"mw-redirect\" title=\"<PERSON> (British computer scientist)\"><PERSON></a>, English computer scientist and academic (b. 1927)", "links": [{"title": "<PERSON> (British computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_computer_scientist)"}]}, {"year": "2005", "text": "<PERSON>, American painter and ferryboat captain (b. 1944) ", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and ferryboat captain (b. 1944) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and ferryboat captain (b. 1944) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American businessman, co-founded the American Football League and World Championship Tennis (b. 1932)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lamar Hunt\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/American_Football_League\" title=\"American Football League\">American Football League</a> and <a href=\"https://wikipedia.org/wiki/World_Championship_Tennis\" title=\"World Championship Tennis\">World Championship Tennis</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lamar_<PERSON>\" title=\"Lamar Hunt\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/American_Football_League\" title=\"American Football League\">American Football League</a> and <a href=\"https://wikipedia.org/wiki/World_Championship_Tennis\" title=\"World Championship Tennis\">World Championship Tennis</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Football League", "link": "https://wikipedia.org/wiki/American_Football_League"}, {"title": "World Championship Tennis", "link": "https://wikipedia.org/wiki/World_Championship_Tennis"}]}, {"year": "2016", "text": "<PERSON>, Canadian actor, songwriter, game and talk-show host (b. 1947)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, songwriter, game and talk-show host (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, songwriter, game and talk-show host (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Holocaust survivor who became an award-winning Israeli journalist (b. 1926)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">Holocaust</a> survivor who became an award-winning Israeli <a href=\"https://wikipedia.org/wiki/Journalist\" title=\"Journalist\">journalist</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">Holocaust</a> survivor who became an award-winning Israeli <a href=\"https://wikipedia.org/wiki/Journalist\" title=\"Journalist\">journalist</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Journalist", "link": "https://wikipedia.org/wiki/Journalist"}]}, {"year": "2022", "text": "<PERSON> \"t<PERSON><PERSON>\" <PERSON>, American dancer and media personality (b. 1982)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22tWitch%22_Boss\" title='<PERSON> \"tWitch\" Boss'><PERSON> \"tWitch\" <PERSON></a>, American dancer and media personality (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22tWitch%22_Boss\" title='<PERSON> \"tWitch\" Boss'><PERSON> \"tWitch\" <PERSON></a>, American dancer and media personality (b. 1982)", "links": [{"title": "<PERSON> \"tWitch\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22tWitch%22_Boss"}]}, {"year": "2024", "text": "<PERSON>, American artist (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Lorraine_O%27Grady\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lorraine_O%27Grady\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lorraine_O%27Grady"}]}]}}