{"date": "November 8", "url": "https://wikipedia.org/wiki/November_8", "data": {"Events": [{"year": "960", "text": "Battle of Andrassos: Byzantines under <PERSON> the Younger score a crushing victory over the Hamdanid Emir of Aleppo, <PERSON><PERSON>.", "html": "960 - <a href=\"https://wikipedia.org/wiki/Battle_of_Andrassos\" title=\"Battle of Andrassos\">Battle of Andrassos</a>: <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantines</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a> score a crushing victory over the <a href=\"https://wikipedia.org/wiki/Hamdanid\" class=\"mw-redirect\" title=\"Hamdanid\">Hamdanid</a> <a href=\"https://wikipedia.org/wiki/Emir_of_Aleppo\" class=\"mw-redirect\" title=\"Emir of Aleppo\">Emir of Aleppo</a>, <a href=\"https://wikipedia.org/wiki/Say<PERSON>_al-Dawla\" title=\"Say<PERSON> al-Dawla\"><PERSON><PERSON> al<PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Andrassos\" title=\"Battle of Andrassos\">Battle of Andrassos</a>: <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantines</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a> score a crushing victory over the <a href=\"https://wikipedia.org/wiki/Hamdanid\" class=\"mw-redirect\" title=\"Hamdanid\">Hamdanid</a> <a href=\"https://wikipedia.org/wiki/Emir_of_Aleppo\" class=\"mw-redirect\" title=\"Emir of Aleppo\">Emir of Aleppo</a>, <a href=\"https://wikipedia.org/wiki/Say<PERSON>_al-Dawla\" title=\"Sayf al-Dawla\"><PERSON><PERSON> al<PERSON></a>.", "links": [{"title": "Battle of Andrassos", "link": "https://wikipedia.org/wiki/Battle_of_Andrassos"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_the_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>id"}, {"title": "Emir of Aleppo", "link": "https://wikipedia.org/wiki/Emir_of_Aleppo"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1278", "text": "Trầ<PERSON><PERSON>, the second emperor of the Trần dynasty, decides to pass the throne to his crown prince <PERSON><PERSON><PERSON><PERSON> and take up the post of Retired Emperor.", "html": "1278 - <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Th%C3%A1nh_T%C3%B4ng\" title=\"Trần Thánh Tông\"><PERSON><PERSON><PERSON><PERSON></a>, the second emperor of the <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty\" title=\"Trần dynasty\">Trần dynasty</a>, decides to pass the throne to his crown prince <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Kh%C3%A2m\" class=\"mw-redirect\" title=\"Trầ<PERSON> Khâm\">T<PERSON><PERSON><PERSON></a> and take up the post of Retired Emperor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Th%C3%A1nh_T%C3%B4ng\" title=\"Trần Thánh Tông\">T<PERSON><PERSON><PERSON></a>, the second emperor of the <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty\" title=\"Trần dynasty\">Trần dynasty</a>, decides to pass the throne to his crown prince <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Kh%C3%A2m\" class=\"mw-redirect\" title=\"Trầ<PERSON> Khâm\">Tr<PERSON><PERSON></a> and take up the post of Retired Emperor.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_Th%C3%A1nh_T%C3%B4ng"}, {"title": "Trần dynasty", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_Kh%C3%A2m"}]}, {"year": "1291", "text": "The Republic of Venice enacts a law confining most of Venice's glassmaking industry to the \"island of Murano\".", "html": "1291 - The <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a> enacts a law confining most of Venice's glassmaking industry to the \"island of <a href=\"https://wikipedia.org/wiki/Murano\" title=\"Murano\">Murano</a>\".", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a> enacts a law confining most of Venice's glassmaking industry to the \"island of <a href=\"https://wikipedia.org/wiki/Murano\" title=\"Murano\">Murano</a>\".", "links": [{"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rano"}]}, {"year": "1519", "text": "<PERSON><PERSON><PERSON> enters Tenochtitlán and Aztec ruler <PERSON><PERSON><PERSON><PERSON> welcomes him with a great celebration.", "html": "1519 - <a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s\" title=\"Hernán Cortés\"><PERSON><PERSON><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Tenochtitl%C3%A1n\" class=\"mw-redirect\" title=\"Tenochtitlán\">Tenochtitlán</a> and <a href=\"https://wikipedia.org/wiki/Aztec\" class=\"mw-redirect\" title=\"Aztec\">Aztec</a> ruler <a href=\"https://wikipedia.org/wiki/Moctezuma_II\" title=\"Moctezuma II\">Moctezuma</a> welcomes him with a great celebration.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s\" title=\"Hernán Cortés\"><PERSON><PERSON><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Tenochtitl%C3%A1n\" class=\"mw-redirect\" title=\"Tenochtitlán\">Tenochtitlán</a> and <a href=\"https://wikipedia.org/wiki/Aztec\" class=\"mw-redirect\" title=\"Aztec\">Aztec</a> ruler <a href=\"https://wikipedia.org/wiki/Moctezuma_II\" title=\"Moctezuma II\">Moctezuma</a> welcomes him with a great celebration.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s"}, {"title": "Tenochtitlán", "link": "https://wikipedia.org/wiki/Tenochtitl%C3%A1n"}, {"title": "Aztec", "link": "https://wikipedia.org/wiki/Aztec"}, {"title": "Moctezuma II", "link": "https://wikipedia.org/wiki/Moctezuma_II"}]}, {"year": "1520", "text": "Stockholm Bloodbath begins: A successful invasion of Sweden by Danish forces results in the execution of around 100 people, mostly noblemen.", "html": "1520 - <a href=\"https://wikipedia.org/wiki/Stockholm_Bloodbath\" title=\"Stockholm Bloodbath\">Stockholm Bloodbath</a> begins: A successful invasion of Sweden by Danish forces results in the execution of around 100 people, mostly noblemen.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stockholm_Bloodbath\" title=\"Stockholm Bloodbath\">Stockholm Bloodbath</a> begins: A successful invasion of Sweden by Danish forces results in the execution of around 100 people, mostly noblemen.", "links": [{"title": "Stockholm Bloodbath", "link": "https://wikipedia.org/wiki/Stockholm_Bloodbath"}]}, {"year": "1576", "text": "Eighty Years' War: Pacification of Ghent: The States General of the Netherlands meet and unite to oppose Spanish occupation.", "html": "1576 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Pacification_of_Ghent\" title=\"Pacification of Ghent\">Pacification of Ghent</a>: The <a href=\"https://wikipedia.org/wiki/States_General_of_the_Netherlands\" title=\"States General of the Netherlands\">States General of the Netherlands</a> meet and unite to oppose Spanish occupation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Pacification_of_Ghent\" title=\"Pacification of Ghent\">Pacification of Ghent</a>: The <a href=\"https://wikipedia.org/wiki/States_General_of_the_Netherlands\" title=\"States General of the Netherlands\">States General of the Netherlands</a> meet and unite to oppose Spanish occupation.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Pacification of Ghent", "link": "https://wikipedia.org/wiki/Pacification_of_Ghent"}, {"title": "States General of the Netherlands", "link": "https://wikipedia.org/wiki/States_General_of_the_Netherlands"}]}, {"year": "1602", "text": "The Bodleian Library at the University of Oxford is opened to the public.", "html": "1602 - The <a href=\"https://wikipedia.org/wiki/Bodleian_Library\" title=\"Bodleian Library\">Bodleian Library</a> at the <a href=\"https://wikipedia.org/wiki/University_of_Oxford\" title=\"University of Oxford\">University of Oxford</a> is opened to the public.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bodleian_Library\" title=\"Bodleian Library\">Bodleian Library</a> at the <a href=\"https://wikipedia.org/wiki/University_of_Oxford\" title=\"University of Oxford\">University of Oxford</a> is opened to the public.", "links": [{"title": "Bodleian Library", "link": "https://wikipedia.org/wiki/Bodleian_Library"}, {"title": "University of Oxford", "link": "https://wikipedia.org/wiki/University_of_Oxford"}]}, {"year": "1605", "text": "<PERSON>, ringleader of the Gunpowder Plotters, is killed.", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ringleader of the <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plotters</a>, is killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ringleader of the <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plotters</a>, is killed.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gunpowder Plot", "link": "https://wikipedia.org/wiki/Gunpowder_Plot"}]}, {"year": "1614", "text": "Japanese daimyō Dom <PERSON><PERSON> is exiled to the Philippines by s<PERSON><PERSON>gun <PERSON> for being Christian.", "html": "1614 - Japanese <i><a href=\"https://wikipedia.org/wiki/Daimy%C5%8D\" class=\"mw-redirect\" title=\"Dai<PERSON><PERSON>\">daim<PERSON></a></i> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\">Dom <PERSON></a> is exiled to the Philippines by shōgun <a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa Ieyasu</a> for being Christian.", "no_year_html": "Japanese <i><a href=\"https://wikipedia.org/wiki/Daimy%C5%8D\" class=\"mw-redirect\" title=\"Dai<PERSON><PERSON>\">daim<PERSON></a></i> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is exiled to the Philippines by shōgun <a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa Ieyasu</a> for being Christian.", "links": [{"title": "Daimy<PERSON>", "link": "https://wikipedia.org/wiki/Daimy%C5%8D"}, {"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>u"}]}, {"year": "1620", "text": "The Battle of White Mountain takes place near Prague, ending in a decisive Catholic victory in only two hours.", "html": "1620 - The <a href=\"https://wikipedia.org/wiki/Battle_of_White_Mountain\" title=\"Battle of White Mountain\">Battle of White Mountain</a> takes place near <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>, ending in a decisive <a href=\"https://wikipedia.org/wiki/Catholic\" class=\"mw-redirect\" title=\"Catholic\">Catholic</a> victory in only two hours.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_White_Mountain\" title=\"Battle of White Mountain\">Battle of White Mountain</a> takes place near <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>, ending in a decisive <a href=\"https://wikipedia.org/wiki/Catholic\" class=\"mw-redirect\" title=\"Catholic\">Catholic</a> victory in only two hours.", "links": [{"title": "Battle of White Mountain", "link": "https://wikipedia.org/wiki/Battle_of_White_Mountain"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}, {"title": "Catholic", "link": "https://wikipedia.org/wiki/Catholic"}]}, {"year": "1644", "text": "The Shunzhi Emperor, the third emperor of the Qing dynasty, is enthroned in Beijing after the collapse of the Ming dynasty as the first Qing emperor to rule over China.", "html": "1644 - The <a href=\"https://wikipedia.org/wiki/Shunzhi_Emperor\" title=\"Shunzhi Emperor\">Shunzhi Emperor</a>, the third emperor of the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>, is enthroned in Beijing after the collapse of the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> as the first Qing emperor to rule over China.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Shunzhi_Emperor\" title=\"Shunzhi Emperor\">Shunzhi Emperor</a>, the third emperor of the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>, is enthroned in Beijing after the collapse of the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> as the first Qing emperor to rule over China.", "links": [{"title": "Shunzhi Emperor", "link": "https://wikipedia.org/wiki/Shunz<PERSON>_Emperor"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}]}, {"year": "1745", "text": "<PERSON> invades England with an army of approximately 5,000 that would later participate in the Battle of Culloden.", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> invades England with an army of approximately 5,000 that would later participate in the <a href=\"https://wikipedia.org/wiki/Battle_of_Culloden\" title=\"Battle of Culloden\">Battle of Culloden</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> invades England with an army of approximately 5,000 that would later participate in the <a href=\"https://wikipedia.org/wiki/Battle_of_Culloden\" title=\"Battle of Culloden\">Battle of Culloden</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Culloden", "link": "https://wikipedia.org/wiki/Battle_of_Culloden"}]}, {"year": "1837", "text": "<PERSON> founds Mount Holyoke Female Seminary, which later becomes Mount Holyoke College.", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mary <PERSON>\"><PERSON></a> founds Mount Holyoke Female Seminary, which later becomes <a href=\"https://wikipedia.org/wiki/Mount_Holyoke_College\" title=\"Mount Holyoke College\">Mount Holyoke College</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mary Lyon\"><PERSON></a> founds Mount Holyoke Female Seminary, which later becomes <a href=\"https://wikipedia.org/wiki/Mount_Holyoke_College\" title=\"Mount Holyoke College\">Mount Holyoke College</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mount Holyoke College", "link": "https://wikipedia.org/wiki/Mount_Holyoke_College"}]}, {"year": "1861", "text": "American Civil War: The \"Trent Affair\": The USS San Jacinto stops the British mail ship Trent and arrests two Confederate envoys, sparking a diplomatic crisis between the UK and US.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The \"<a href=\"https://wikipedia.org/wiki/Trent_Affair\" title=\"Trent Affair\">Trent Affair</a>\": The <a href=\"https://wikipedia.org/wiki/USS_San_Jacinto_(1850)\" title=\"USS San Jacinto (1850)\">USS <i>San Jacinto</i></a> stops the British mail ship <i>Trent</i> and arrests two <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> envoys, sparking a diplomatic crisis between the UK and US.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The \"<a href=\"https://wikipedia.org/wiki/Trent_Affair\" title=\"Trent Affair\">Trent Affair</a>\": The <a href=\"https://wikipedia.org/wiki/USS_San_Jacinto_(1850)\" title=\"USS San Jacinto (1850)\">USS <i>San Jacinto</i></a> stops the British mail ship <i>Trent</i> and arrests two <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> envoys, sparking a diplomatic crisis between the UK and US.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Trent Affair", "link": "https://wikipedia.org/wiki/Trent_Affair"}, {"title": "USS San Jacinto (1850)", "link": "https://wikipedia.org/wiki/USS_San_Jacinto_(1850)"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1889", "text": "Montana is admitted as the 41st U.S. state.", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a> is admitted as the 41st <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a> is admitted as the 41st <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Montana", "link": "https://wikipedia.org/wiki/Montana"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1892", "text": "The New Orleans general strike begins, uniting black and white American trade unionists in a successful four-day general strike action for the first time.", "html": "1892 - The <a href=\"https://wikipedia.org/wiki/1892_New_Orleans_general_strike\" title=\"1892 New Orleans general strike\">New Orleans general strike</a> begins, uniting <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">black</a> and <a href=\"https://wikipedia.org/wiki/White_people\" title=\"White people\">white</a> American <a href=\"https://wikipedia.org/wiki/Trade_union\" title=\"Trade union\">trade unionists</a> in a successful four-day general <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">strike action</a> for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1892_New_Orleans_general_strike\" title=\"1892 New Orleans general strike\">New Orleans general strike</a> begins, uniting <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">black</a> and <a href=\"https://wikipedia.org/wiki/White_people\" title=\"White people\">white</a> American <a href=\"https://wikipedia.org/wiki/Trade_union\" title=\"Trade union\">trade unionists</a> in a successful four-day general <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">strike action</a> for the first time.", "links": [{"title": "1892 New Orleans general strike", "link": "https://wikipedia.org/wiki/1892_New_Orleans_general_strike"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}, {"title": "White people", "link": "https://wikipedia.org/wiki/White_people"}, {"title": "Trade union", "link": "https://wikipedia.org/wiki/Trade_union"}, {"title": "Strike action", "link": "https://wikipedia.org/wiki/Strike_action"}]}, {"year": "1895", "text": "While experimenting with electricity, <PERSON> discovers the X-ray.", "html": "1895 - While experimenting with electricity, <a href=\"https://wikipedia.org/wiki/Wilhelm_R%C3%B6ntgen\" title=\"<PERSON>\"><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/X-ray\" title=\"X-ray\">X-ray</a>.", "no_year_html": "While experimenting with electricity, <a href=\"https://wikipedia.org/wiki/Wilhelm_R%C3%B6ntgen\" title=\"<PERSON>\"><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/X-ray\" title=\"X-ray\">X-ray</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wilhelm_R%C3%B6ntgen"}, {"title": "X-ray", "link": "https://wikipedia.org/wiki/X-ray"}]}, {"year": "1901", "text": "Gospel riots: Bloody clashes take place in Athens following the translation of the Gospels into demotic Greek.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Gospel_riots\" title=\"Gospel riots\">Gospel riots</a>: Bloody clashes take place in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a> following the translation of the <a href=\"https://wikipedia.org/wiki/Gospel\" title=\"Gospel\">Gospels</a> into <a href=\"https://wikipedia.org/wiki/Demotic_Greek\" title=\"Demotic Greek\">demotic Greek</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gospel_riots\" title=\"Gospel riots\">Gospel riots</a>: Bloody clashes take place in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a> following the translation of the <a href=\"https://wikipedia.org/wiki/Gospel\" title=\"Gospel\">Gospels</a> into <a href=\"https://wikipedia.org/wiki/Demotic_Greek\" title=\"Demotic Greek\">demotic Greek</a>.", "links": [{"title": "Gospel riots", "link": "https://wikipedia.org/wiki/Gospel_riots"}, {"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}, {"title": "Gospel", "link": "https://wikipedia.org/wiki/Gospel"}, {"title": "Demotic Greek", "link": "https://wikipedia.org/wiki/Demotic_Greek"}]}, {"year": "1917", "text": "The first Council of People's Commissars is formed, including <PERSON>, <PERSON> and <PERSON>.", "html": "1917 - The first <a href=\"https://wikipedia.org/wiki/Council_of_People%27s_Commissars\" title=\"Council of People's Commissars\">Council of People's Commissars</a> is formed, including <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Council_of_People%27s_Commissars\" title=\"Council of People's Commissars\">Council of People's Commissars</a> is formed, including <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Council of People's Commissars", "link": "https://wikipedia.org/wiki/Council_of_People%27s_Commissars"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lenin"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "E<PERSON><PERSON>feld massacre: Members of the Revolutionary Insurgent Army of Ukraine murder 136 Mennonite colonists at Jaskyowo, initiating a series of massacres that resulted in the deaths of 827 Ukrainian Mennonites.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Eichenfeld_massacre\" title=\"Eichenfeld massacre\">Eichenfeld massacre</a>: Members of the <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a> murder 136 <a href=\"https://wikipedia.org/wiki/Russian_Mennonites\" title=\"Russian Mennonites\">Mennonite colonists</a> at Jaskyowo, initiating a series of massacres that resulted in the deaths of 827 Ukrainian Mennonites.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eichenfeld_massacre\" title=\"Eichenfeld massacre\">Eichenfeld massacre</a>: Members of the <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a> murder 136 <a href=\"https://wikipedia.org/wiki/Russian_Mennonites\" title=\"Russian Mennonites\">Mennonite colonists</a> at Jaskyowo, initiating a series of massacres that resulted in the deaths of 827 Ukrainian Mennonites.", "links": [{"title": "Eichenfeld massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>feld_massacre"}, {"title": "Revolutionary Insurgent Army of Ukraine", "link": "https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine"}, {"title": "Russian Mennonites", "link": "https://wikipedia.org/wiki/Russian_Mennonites"}]}, {"year": "1920", "text": "<PERSON>, illustrated by <PERSON> makes his first appearance in print.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rupert Bear\"><PERSON></a>, illustrated by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes his first appearance in print.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rupert Bear\"><PERSON></a>, illustrated by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes his first appearance in print.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "Beer Hall Putsch: In Munich, <PERSON> leads the Nazis in an unsuccessful attempt to overthrow the German government.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Beer_Hall_Putsch\" title=\"Beer Hall Putsch\">Beer Hall Putsch</a>: In <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, <a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a> in an unsuccessful attempt to overthrow the German government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beer_Hall_Putsch\" title=\"Beer Hall Putsch\">Beer Hall Putsch</a>: In <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a> in an unsuccessful attempt to overthrow the German government.", "links": [{"title": "Beer Hall Putsch", "link": "https://wikipedia.org/wiki/Beer_Hall_Putsch"}, {"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}]}, {"year": "1932", "text": "<PERSON> is elected as the 32nd President of the United States, defeating incumbent president <PERSON>.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1932_United_States_presidential_election\" title=\"1932 United States presidential election\">elected</a> as the 32nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, defeating incumbent president <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1932_United_States_presidential_election\" title=\"1932 United States presidential election\">elected</a> as the 32nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, defeating incumbent president <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1932 United States presidential election", "link": "https://wikipedia.org/wiki/1932_United_States_presidential_election"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "Great Depression: New Deal: US President <PERSON> unveils the Civil Works Administration, an organization designed to create jobs for more than four million unemployed.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>: <a href=\"https://wikipedia.org/wiki/New_Deal\" title=\"New Deal\">New Deal</a>: US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> unveils the <a href=\"https://wikipedia.org/wiki/Civil_Works_Administration\" title=\"Civil Works Administration\">Civil Works Administration</a>, an organization designed to create jobs for more than four million unemployed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>: <a href=\"https://wikipedia.org/wiki/New_Deal\" title=\"New Deal\">New Deal</a>: US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> unveils the <a href=\"https://wikipedia.org/wiki/Civil_Works_Administration\" title=\"Civil Works Administration\">Civil Works Administration</a>, an organization designed to create jobs for more than four million unemployed.", "links": [{"title": "Great Depression", "link": "https://wikipedia.org/wiki/Great_Depression"}, {"title": "New Deal", "link": "https://wikipedia.org/wiki/New_Deal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Civil Works Administration", "link": "https://wikipedia.org/wiki/Civil_Works_Administration"}]}, {"year": "1936", "text": "Spanish Civil War: Francoist troops fail in their effort to capture Madrid, but begin the three-year Siege of Madrid afterwards.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Francoist_Spain\" title=\"Francoist Spain\">Francoist</a> troops fail in their effort to capture <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>, but begin the three-year <a href=\"https://wikipedia.org/wiki/Siege_of_Madrid\" title=\"Siege of Madrid\">Siege of Madrid</a> afterwards.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Francoist_Spain\" title=\"Francoist Spain\">Francoist</a> troops fail in their effort to capture <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>, but begin the three-year <a href=\"https://wikipedia.org/wiki/Siege_of_Madrid\" title=\"Siege of Madrid\">Siege of Madrid</a> afterwards.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Francoist Spain", "link": "https://wikipedia.org/wiki/Francoist_Spain"}, {"title": "Madrid", "link": "https://wikipedia.org/wiki/Madrid"}, {"title": "Siege of Madrid", "link": "https://wikipedia.org/wiki/Siege_of_Madrid"}]}, {"year": "1937", "text": "The Nazi exhibition Der ewige Jude (\"The Eternal Jew\") opens in Munich.", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> exhibition <i><a href=\"https://wikipedia.org/wiki/The_Eternal_Jew_(art_exhibition)\" title=\"The Eternal Jew (art exhibition)\">Der ewige <PERSON></a></i> (\"The Eternal Jew\") opens in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> exhibition <i><a href=\"https://wikipedia.org/wiki/The_Eternal_Jew_(art_exhibition)\" title=\"The Eternal Jew (art exhibition)\">Der ewige <PERSON></a></i> (\"The Eternal Jew\") opens in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>.", "links": [{"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}, {"title": "The Eternal Jew (art exhibition)", "link": "https://wikipedia.org/wiki/The_Eternal_Jew_(art_exhibition)"}, {"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}]}, {"year": "1939", "text": "Venlo Incident: Two British agents of SIS are captured by the Germans.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Venlo_Incident\" class=\"mw-redirect\" title=\"Venlo Incident\">Venlo Incident</a>: Two British agents of <a href=\"https://wikipedia.org/wiki/Secret_Intelligence_Service\" class=\"mw-redirect\" title=\"Secret Intelligence Service\">SIS</a> are captured by the Germans.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venlo_Incident\" class=\"mw-redirect\" title=\"Venlo Incident\">Venlo Incident</a>: Two British agents of <a href=\"https://wikipedia.org/wiki/Secret_Intelligence_Service\" class=\"mw-redirect\" title=\"Secret Intelligence Service\">SIS</a> are captured by the Germans.", "links": [{"title": "Venlo Incident", "link": "https://wikipedia.org/wiki/Venlo_Incident"}, {"title": "Secret Intelligence Service", "link": "https://wikipedia.org/wiki/Secret_Intelligence_Service"}]}, {"year": "1939", "text": "In Munich, <PERSON> narrowly escapes the assassination attempt of <PERSON> while celebrating the 16th anniversary of the Beer Hall Putsch.", "html": "1939 - In <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> narrowly escapes the assassination attempt of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> while celebrating the 16th anniversary of the <a href=\"https://wikipedia.org/wiki/Beer_Hall_Putsch\" title=\"Beer Hall Putsch\">Beer Hall Putsch</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> narrowly escapes the assassination attempt of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> while celebrating the 16th anniversary of the <a href=\"https://wikipedia.org/wiki/Beer_Hall_Putsch\" title=\"Beer Hall Putsch\">Beer Hall Putsch</a>.", "links": [{"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Beer Hall Putsch", "link": "https://wikipedia.org/wiki/Beer_Hall_Putsch"}]}, {"year": "1940", "text": "Greco-Italian War: The Italian invasion of Greece fails as outnumbered Greek units repulse the Italians in the Battle of Elaia-Kalamas.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Greco-Italian_War\" title=\"Greco-Italian War\">Greco-Italian War</a>: The Italian invasion of Greece fails as outnumbered Greek units repulse the Italians in the <a href=\"https://wikipedia.org/wiki/Battle_of_Elaia%E2%80%93Kalamas\" title=\"Battle of Elaia-Kalamas\">Battle of Elaia-Kalamas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greco-Italian_War\" title=\"Greco-Italian War\">Greco-Italian War</a>: The Italian invasion of Greece fails as outnumbered Greek units repulse the Italians in the <a href=\"https://wikipedia.org/wiki/Battle_of_Elaia%E2%80%93Kalamas\" title=\"Battle of Elaia-Kalamas\">Battle of Elaia-Kalamas</a>.", "links": [{"title": "Greco-Italian War", "link": "https://wikipedia.org/wiki/Greco-Italian_War"}, {"title": "Battle of Elaia-Kalamas", "link": "https://wikipedia.org/wiki/Battle_of_Elaia%E2%80%93Kalamas"}]}, {"year": "1942", "text": "World War II: French Resistance coup in Algiers, in which 400 civilian French patriots neutralize Vichyist XIXth Army Corps after 15 hours of fighting, and arrest several Vichyist generals, allowing the immediate success of Operation Torch in Algiers.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/French_Resistance\" title=\"French Resistance\">French Resistance</a> coup in <a href=\"https://wikipedia.org/wiki/Algiers\" title=\"Algiers\">Algiers</a>, in which 400 civilian French patriots neutralize <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichyist</a> XIXth Army Corps after 15 hours of fighting, and arrest several Vichyist generals, allowing the immediate success of <a href=\"https://wikipedia.org/wiki/Operation_Torch\" title=\"Operation Torch\">Operation Torch</a> in Algiers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/French_Resistance\" title=\"French Resistance\">French Resistance</a> coup in <a href=\"https://wikipedia.org/wiki/Algiers\" title=\"Algiers\">Algiers</a>, in which 400 civilian French patriots neutralize <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichyist</a> XIXth Army Corps after 15 hours of fighting, and arrest several Vichyist generals, allowing the immediate success of <a href=\"https://wikipedia.org/wiki/Operation_Torch\" title=\"Operation Torch\">Operation Torch</a> in Algiers.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "French Resistance", "link": "https://wikipedia.org/wiki/French_Resistance"}, {"title": "Algiers", "link": "https://wikipedia.org/wiki/Algiers"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vichy_France"}, {"title": "Operation Torch", "link": "https://wikipedia.org/wiki/Operation_Torch"}]}, {"year": "1950", "text": "Korean War: United States Air Force Lt. <PERSON>, while piloting an F-80 Shooting Star, shoots down two North Korean MiG-15s in the first jet aircraft-to-jet aircraft dogfight in history.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> Lt. <PERSON>, while piloting an <a href=\"https://wikipedia.org/wiki/F-80_Shooting_Star\" class=\"mw-redirect\" title=\"F-80 Shooting Star\">F-80 Shooting Star</a>, shoots down two <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korean</a> <a href=\"https://wikipedia.org/wiki/Mikoyan-Gurevich_MiG-15\" title=\"Mikoyan-Gurevich MiG-15\">MiG-15s</a> in the first <a href=\"https://wikipedia.org/wiki/Jet_aircraft\" title=\"Jet aircraft\">jet aircraft</a>-to-jet aircraft <a href=\"https://wikipedia.org/wiki/Dogfight\" title=\"Dogfight\">dogfight</a> in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> Lt. <PERSON>, while piloting an <a href=\"https://wikipedia.org/wiki/F-80_Shooting_Star\" class=\"mw-redirect\" title=\"F-80 Shooting Star\">F-80 Shooting Star</a>, shoots down two <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korean</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-Gurevich_MiG-15\" title=\"<PERSON><PERSON><PERSON>-G<PERSON>vich MiG-15\">MiG-15s</a> in the first <a href=\"https://wikipedia.org/wiki/Jet_aircraft\" title=\"Jet aircraft\">jet aircraft</a>-to-jet aircraft <a href=\"https://wikipedia.org/wiki/Dogfight\" title=\"Dogfight\">dogfight</a> in history.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "F-80 Shooting Star", "link": "https://wikipedia.org/wiki/F-80_Shooting_Star"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Mikoyan<PERSON><PERSON><PERSON><PERSON> MiG-15", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-15"}, {"title": "Jet aircraft", "link": "https://wikipedia.org/wiki/Jet_aircraft"}, {"title": "Dogfight", "link": "https://wikipedia.org/wiki/Dogfight"}]}, {"year": "1957", "text": "Pan Am Flight 7 disappears between San Francisco and Honolulu. Wreckage and bodies are discovered a week later.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_7\" title=\"Pan Am Flight 7\">Pan Am Flight 7</a> disappears between <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> and <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu</a>. Wreckage and bodies are discovered a week later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_7\" title=\"Pan Am Flight 7\">Pan Am Flight 7</a> disappears between <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> and <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu</a>. Wreckage and bodies are discovered a week later.", "links": [{"title": "Pan Am Flight 7", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_7"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}, {"title": "Honolulu", "link": "https://wikipedia.org/wiki/Honolulu"}]}, {"year": "1957", "text": "Operation Grapple X, Round C1: The United Kingdom conducts its first successful hydrogen bomb test over Kiritimati in the Pacific.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Operation_Grapple#Grapple_X_(1_test)\" title=\"Operation Grapple\">Operation Grapple X, Round C1</a>: The United Kingdom conducts its first successful <a href=\"https://wikipedia.org/wiki/Hydrogen_bomb\" class=\"mw-redirect\" title=\"Hydrogen bomb\">hydrogen bomb</a> test over <a href=\"https://wikipedia.org/wiki/Kiritimati\" title=\"Kiritima<PERSON>\">Kiritimati</a> in the Pacific.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Grapple#Grapple_X_(1_test)\" title=\"Operation Grapple\">Operation Grapple X, Round C1</a>: The United Kingdom conducts its first successful <a href=\"https://wikipedia.org/wiki/Hydrogen_bomb\" class=\"mw-redirect\" title=\"Hydrogen bomb\">hydrogen bomb</a> test over <a href=\"https://wikipedia.org/wiki/Kiritimati\" title=\"Kiritima<PERSON>\">Kiritimati</a> in the Pacific.", "links": [{"title": "Operation Grapple", "link": "https://wikipedia.org/wiki/Operation_Grapple#Grapple_X_(1_test)"}, {"title": "Hydrogen bomb", "link": "https://wikipedia.org/wiki/Hydrogen_bomb"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON> is elected as the 35th President of the United States, defeating incumbent Vice President <PERSON>, who would later be elected president in 1968  and 1972.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1960_United_States_presidential_election\" title=\"1960 United States presidential election\">elected</a> as the 35th President of the United States, defeating incumbent <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who would later be elected president in <a href=\"https://wikipedia.org/wiki/1968_United_States_presidential_election\" title=\"1968 United States presidential election\">1968</a> and <a href=\"https://wikipedia.org/wiki/1972_United_States_presidential_election\" title=\"1972 United States presidential election\">1972</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1960_United_States_presidential_election\" title=\"1960 United States presidential election\">elected</a> as the 35th President of the United States, defeating incumbent <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who would later be elected president in <a href=\"https://wikipedia.org/wiki/1968_United_States_presidential_election\" title=\"1968 United States presidential election\">1968</a> and <a href=\"https://wikipedia.org/wiki/1972_United_States_presidential_election\" title=\"1972 United States presidential election\">1972</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "1960 United States presidential election", "link": "https://wikipedia.org/wiki/1960_United_States_presidential_election"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1968 United States presidential election", "link": "https://wikipedia.org/wiki/1968_United_States_presidential_election"}, {"title": "1972 United States presidential election", "link": "https://wikipedia.org/wiki/1972_United_States_presidential_election"}]}, {"year": "1963", "text": "Finnair's Aero Flight 217 crashes near Mariehamn Airport in Jomala, Åland, killing 22 people.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Finnair\" title=\"Finnair\">Finnair</a>'s <a href=\"https://wikipedia.org/wiki/Aero_Flight_217\" title=\"Aero Flight 217\">Aero Flight 217</a> crashes near <a href=\"https://wikipedia.org/wiki/Mariehamn_Airport\" title=\"Mariehamn Airport\">Mariehamn Airport</a> in <a href=\"https://wikipedia.org/wiki/Jomala\" title=\"Jomala\">Jomala</a>, <a href=\"https://wikipedia.org/wiki/%C3%85land\" title=\"Åland\">Åland</a>, killing 22 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnair\" title=\"Finnair\">Finnair</a>'s <a href=\"https://wikipedia.org/wiki/Aero_Flight_217\" title=\"Aero Flight 217\">Aero Flight 217</a> crashes near <a href=\"https://wikipedia.org/wiki/Mariehamn_Airport\" title=\"Mariehamn Airport\">Mariehamn Airport</a> in <a href=\"https://wikipedia.org/wiki/Jomala\" title=\"Jomala\">Jomala</a>, <a href=\"https://wikipedia.org/wiki/%C3%85land\" title=\"Åland\">Åland</a>, killing 22 people.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Finnair"}, {"title": "Aero Flight 217", "link": "https://wikipedia.org/wiki/Aero_Flight_217"}, {"title": "Mariehamn Airport", "link": "https://wikipedia.org/wiki/Mariehamn_Airport"}, {"title": "Jo<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jomala"}, {"title": "Åland", "link": "https://wikipedia.org/wiki/%C3%85land"}]}, {"year": "1965", "text": "The British Indian Ocean Territory is created, consisting of Chagos Archipelago, Aldabra, Farquhar and Des Roches islands.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/British_Indian_Ocean_Territory\" title=\"British Indian Ocean Territory\">British Indian Ocean Territory</a> is created, consisting of <a href=\"https://wikipedia.org/wiki/Chagos_Archipelago\" title=\"Chagos Archipelago\">Chagos Archipelago</a>, <a href=\"https://wikipedia.org/wiki/Aldabra\" title=\"Aldabra\">Aldabra</a>, <a href=\"https://wikipedia.org/wiki/Farquhar_Group\" title=\"Farquhar Group\">Farquhar</a> and <a href=\"https://wikipedia.org/wiki/Des_Roches\" class=\"mw-redirect\" title=\"Des Roches\">Des Roche<PERSON></a> islands.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/British_Indian_Ocean_Territory\" title=\"British Indian Ocean Territory\">British Indian Ocean Territory</a> is created, consisting of <a href=\"https://wikipedia.org/wiki/Chagos_Archipelago\" title=\"Chagos Archipelago\">Chagos Archipelago</a>, <a href=\"https://wikipedia.org/wiki/Aldabra\" title=\"Aldabra\">Aldabra</a>, <a href=\"https://wikipedia.org/wiki/Farquhar_Group\" title=\"Farquhar Group\">Farquhar</a> and <a href=\"https://wikipedia.org/wiki/Des_Roches\" class=\"mw-redirect\" title=\"Des Roches\">Des Roche<PERSON></a> islands.", "links": [{"title": "British Indian Ocean Territory", "link": "https://wikipedia.org/wiki/British_Indian_Ocean_Territory"}, {"title": "Chagos Archipelago", "link": "https://wikipedia.org/wiki/Chagos_Archipelago"}, {"title": "Aldabra", "link": "https://wikipedia.org/wiki/Aldabra"}, {"title": "Farquhar Group", "link": "https://wikipedia.org/wiki/Farquhar_Group"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Des_Roches"}]}, {"year": "1965", "text": "The Murder (Abolition of Death Penalty) Act 1965 is given Royal Assent, formally abolishing the death penalty in the United Kingdom for almost all crimes.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/Murder_(Abolition_of_Death_Penalty)_Act_1965\" title=\"Murder (Abolition of Death Penalty) Act 1965\">Murder (Abolition of Death Penalty) Act 1965</a> is given Royal Assent, formally abolishing the death penalty in the United Kingdom for almost all crimes.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Murder_(Abolition_of_Death_Penalty)_Act_1965\" title=\"Murder (Abolition of Death Penalty) Act 1965\">Murder (Abolition of Death Penalty) Act 1965</a> is given Royal Assent, formally abolishing the death penalty in the United Kingdom for almost all crimes.", "links": [{"title": "Murder (Abolition of Death Penalty) Act 1965", "link": "https://wikipedia.org/wiki/Murder_(Abolition_of_Death_Penalty)_Act_1965"}]}, {"year": "1965", "text": "The 173rd Airborne is ambushed by over 1,200 Viet Cong in Operation Hump during the Vietnam War, while the 1st Battalion, Royal Australian Regiment fight one of the first set-piece engagements of the war between Australian forces and the Viet Cong at the Battle of Gang Toi.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/173rd_Airborne\" class=\"mw-redirect\" title=\"173rd Airborne\">173rd Airborne</a> is ambushed by over 1,200 <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> in <a href=\"https://wikipedia.org/wiki/Operation_Hump\" title=\"Operation Hump\">Operation Hump</a> during the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>, while the <a href=\"https://wikipedia.org/wiki/1st_Battalion,_Royal_Australian_Regiment\" title=\"1st Battalion, Royal Australian Regiment\">1st Battalion, Royal Australian Regiment</a> fight one of the first set-piece engagements of the war between Australian forces and the Viet Cong at the <a href=\"https://wikipedia.org/wiki/Battle_of_Gang_Toi\" title=\"Battle of Gang Toi\">Battle of Gang Toi</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/173rd_Airborne\" class=\"mw-redirect\" title=\"173rd Airborne\">173rd Airborne</a> is ambushed by over 1,200 <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> in <a href=\"https://wikipedia.org/wiki/Operation_Hump\" title=\"Operation Hump\">Operation Hump</a> during the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>, while the <a href=\"https://wikipedia.org/wiki/1st_Battalion,_Royal_Australian_Regiment\" title=\"1st Battalion, Royal Australian Regiment\">1st Battalion, Royal Australian Regiment</a> fight one of the first set-piece engagements of the war between Australian forces and the Viet Cong at the <a href=\"https://wikipedia.org/wiki/Battle_of_Gang_Toi\" title=\"Battle of Gang Toi\">Battle of Gang Toi</a>.", "links": [{"title": "173rd Airborne", "link": "https://wikipedia.org/wiki/173rd_Airborne"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}, {"title": "Operation Hump", "link": "https://wikipedia.org/wiki/Operation_Hump"}, {"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "1st Battalion, Royal Australian Regiment", "link": "https://wikipedia.org/wiki/1st_Battalion,_Royal_Australian_Regiment"}, {"title": "Battle of Gang Toi", "link": "https://wikipedia.org/wiki/Battle_of_Gang_Toi"}]}, {"year": "1965", "text": "American Airlines Flight 383 crashes in Constance, Kentucky, killing 58.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_383_(1965)\" title=\"American Airlines Flight 383 (1965)\">American Airlines Flight 383</a> crashes in <a href=\"https://wikipedia.org/wiki/Constance,_Kentucky\" title=\"Constance, Kentucky\">Constance, Kentucky</a>, killing 58.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_383_(1965)\" title=\"American Airlines Flight 383 (1965)\">American Airlines Flight 383</a> crashes in <a href=\"https://wikipedia.org/wiki/Constance,_Kentucky\" title=\"Constance, Kentucky\">Constance, Kentucky</a>, killing 58.", "links": [{"title": "American Airlines Flight 383 (1965)", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_383_(1965)"}, {"title": "Constance, Kentucky", "link": "https://wikipedia.org/wiki/Constance,_Kentucky"}]}, {"year": "1966", "text": "Former Massachusetts Attorney General <PERSON> becomes the first African American elected to the United States Senate since Reconstruction.", "html": "1966 - Former <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> Attorney General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> <a href=\"https://wikipedia.org/wiki/1966_United_States_Senate_election_in_Massachusetts\" title=\"1966 United States Senate election in Massachusetts\">elected</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> since <a href=\"https://wikipedia.org/wiki/Reconstruction_Era\" class=\"mw-redirect\" title=\"Reconstruction Era\">Reconstruction</a>.", "no_year_html": "Former <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> Attorney General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> <a href=\"https://wikipedia.org/wiki/1966_United_States_Senate_election_in_Massachusetts\" title=\"1966 United States Senate election in Massachusetts\">elected</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> since <a href=\"https://wikipedia.org/wiki/Reconstruction_Era\" class=\"mw-redirect\" title=\"Reconstruction Era\">Reconstruction</a>.", "links": [{"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}, {"title": "1966 United States Senate election in Massachusetts", "link": "https://wikipedia.org/wiki/1966_United_States_Senate_election_in_Massachusetts"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "Reconstruction Era", "link": "https://wikipedia.org/wiki/Reconstruction_Era"}]}, {"year": "1966", "text": "U.S. President <PERSON> signs into law an antitrust exemption allowing the National Football League to merge with the upstart American Football League.", "html": "1966 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs into law an <a href=\"https://wikipedia.org/wiki/Antitrust\" class=\"mw-redirect\" title=\"Antitrust\">antitrust</a> exemption allowing the <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a> to <a href=\"https://wikipedia.org/wiki/AFL%E2%80%93NFL_merger\" title=\"AFL-NFL merger\">merge</a> with the upstart <a href=\"https://wikipedia.org/wiki/American_Football_League\" title=\"American Football League\">American Football League</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs into law an <a href=\"https://wikipedia.org/wiki/Antitrust\" class=\"mw-redirect\" title=\"Antitrust\">antitrust</a> exemption allowing the <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a> to <a href=\"https://wikipedia.org/wiki/AFL%E2%80%93NFL_merger\" title=\"AFL-NFL merger\">merge</a> with the upstart <a href=\"https://wikipedia.org/wiki/American_Football_League\" title=\"American Football League\">American Football League</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Antitrust", "link": "https://wikipedia.org/wiki/Antitrust"}, {"title": "National Football League", "link": "https://wikipedia.org/wiki/National_Football_League"}, {"title": "AFL-NFL merger", "link": "https://wikipedia.org/wiki/AFL%E2%80%93NFL_merger"}, {"title": "American Football League", "link": "https://wikipedia.org/wiki/American_Football_League"}]}, {"year": "1968", "text": "The Vienna Convention on Road Traffic is signed to facilitate international road traffic and to increase road safety by standardising the uniform traffic rules among the signatories.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/Vienna_Convention_on_Road_Traffic\" title=\"Vienna Convention on Road Traffic\">Vienna Convention on Road Traffic</a> is signed to facilitate international road traffic and to increase road safety by standardising the uniform traffic rules among the signatories.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Vienna_Convention_on_Road_Traffic\" title=\"Vienna Convention on Road Traffic\">Vienna Convention on Road Traffic</a> is signed to facilitate international road traffic and to increase road safety by standardising the uniform traffic rules among the signatories.", "links": [{"title": "Vienna Convention on Road Traffic", "link": "https://wikipedia.org/wiki/Vienna_Convention_on_Road_Traffic"}]}, {"year": "1972", "text": "American pay television network Home Box Office (HBO) launches.", "html": "1972 - American pay television network <a href=\"https://wikipedia.org/wiki/HBO\" title=\"HBO\">Home Box Office (HBO)</a> launches.", "no_year_html": "American pay television network <a href=\"https://wikipedia.org/wiki/HBO\" title=\"HBO\">Home Box Office (HBO)</a> launches.", "links": [{"title": "HBO", "link": "https://wikipedia.org/wiki/HBO"}]}, {"year": "1973", "text": "The right ear of <PERSON> is delivered to a newspaper outlet along with a ransom note, convincing his father to pay US$2.9 million.", "html": "1973 - The right ear of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a> is delivered to a newspaper outlet along with a ransom note, convincing his father to pay US$2.9 million.", "no_year_html": "The right ear of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is delivered to a newspaper outlet along with a ransom note, convincing his father to pay US$2.9 million.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "Man<PERSON> Andronikos, a Greek archaeologist and professor at the Aristotle University of Thessaloniki, discovers the tomb of <PERSON> of Macedon at Vergina.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Manolis_Andronikos\" title=\"Manolis Andronikos\">Manolis Andronikos</a>, a Greek archaeologist and professor at the <a href=\"https://wikipedia.org/wiki/Aristotle_University_of_Thessaloniki\" title=\"Aristotle University of Thessaloniki\">Aristotle University of Thessaloniki</a>, discovers the tomb of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Macedon\" title=\"<PERSON> of Macedon\"><PERSON> of Macedon</a> at <a href=\"https://wikipedia.org/wiki/Vergina\" title=\"Vergina\">Vergina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manolis_Andronikos\" title=\"Manolis Andronikos\">Manolis Andronikos</a>, a Greek archaeologist and professor at the <a href=\"https://wikipedia.org/wiki/Aristotle_University_of_Thessaloniki\" title=\"Aristotle University of Thessaloniki\">Aristotle University of Thessaloniki</a>, discovers the tomb of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Macedon\" title=\"<PERSON> of Macedon\"><PERSON> of Macedon</a> at <a href=\"https://wikipedia.org/wiki/Vergina\" title=\"Vergina\">Vergina</a>.", "links": [{"title": "Manolis Andronikos", "link": "https://wikipedia.org/wiki/Manolis_Andronikos"}, {"title": "Aristotle University of Thessaloniki", "link": "https://wikipedia.org/wiki/Aristotle_University_of_Thessaloniki"}, {"title": "<PERSON> of Macedon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Macedon"}, {"title": "Vergina", "link": "https://wikipedia.org/wiki/Vergina"}]}, {"year": "1981", "text": "Aeroméxico Flight 110 crashes near Zihuatanejo, Mexico, killing all 18 people on board.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Aerom%C3%A9xico_Flight_110\" title=\"Aeroméxico Flight 110\">Aeroméxico Flight 110</a> crashes near <a href=\"https://wikipedia.org/wiki/Zihuatanejo\" title=\"Zihuatanejo\">Zihuatanejo</a>, Mexico, killing all 18 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aerom%C3%A9xico_Flight_110\" title=\"Aeroméxico Flight 110\">Aeroméxico Flight 110</a> crashes near <a href=\"https://wikipedia.org/wiki/Zihuatanejo\" title=\"Zihuatanejo\">Zihuatanejo</a>, Mexico, killing all 18 people on board.", "links": [{"title": "Aeroméxico Flight 110", "link": "https://wikipedia.org/wiki/Aerom%C3%A9xico_Flight_110"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zihuatanejo"}]}, {"year": "1983", "text": "TAAG Angola Airlines Flight 462 crashes after takeoff from Lubango Airport killing all 130 people on board. UNITA claims to have shot down the aircraft, though this is disputed.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/TAAG_Angola_Airlines_Flight_462\" class=\"mw-redirect\" title=\"TAAG Angola Airlines Flight 462\">TAAG Angola Airlines Flight 462</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Lubango_Airport\" title=\"Lubango Airport\">Lubango Airport</a> killing all 130 people on board. <a href=\"https://wikipedia.org/wiki/UNITA\" title=\"UNITA\">UNITA</a> claims to have shot down the aircraft, though this is disputed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TAAG_Angola_Airlines_Flight_462\" class=\"mw-redirect\" title=\"TAAG Angola Airlines Flight 462\">TAAG Angola Airlines Flight 462</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Lubango_Airport\" title=\"Lubango Airport\">Lubango Airport</a> killing all 130 people on board. <a href=\"https://wikipedia.org/wiki/UNITA\" title=\"UNITA\">UNITA</a> claims to have shot down the aircraft, though this is disputed.", "links": [{"title": "TAAG Angola Airlines Flight 462", "link": "https://wikipedia.org/wiki/TAAG_Angola_Airlines_Flight_462"}, {"title": "Lubango Airport", "link": "https://wikipedia.org/wiki/Lubango_Airport"}, {"title": "UNITA", "link": "https://wikipedia.org/wiki/UNITA"}]}, {"year": "1987", "text": "Remembrance Day bombing: A Provisional IRA bomb explodes in Enniskillen, Northern Ireland during a ceremony honouring those who had died in wars involving British forces. Twelve people are killed and sixty-three wounded.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Remembrance_Day_bombing\" title=\"Remembrance Day bombing\">Remembrance Day bombing</a>: A <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> bomb explodes in <a href=\"https://wikipedia.org/wiki/Enniskillen\" title=\"Enniskillen\">Enniskillen</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> during a ceremony honouring those who had died in wars involving British forces. Twelve people are killed and sixty-three wounded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Remembrance_Day_bombing\" title=\"Remembrance Day bombing\">Remembrance Day bombing</a>: A <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> bomb explodes in <a href=\"https://wikipedia.org/wiki/Enniskillen\" title=\"Enniskillen\">Enniskillen</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> during a ceremony honouring those who had died in wars involving British forces. Twelve people are killed and sixty-three wounded.", "links": [{"title": "Remembrance Day bombing", "link": "https://wikipedia.org/wiki/Remembrance_Day_bombing"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Enniskillen", "link": "https://wikipedia.org/wiki/Enniskillen"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "1988", "text": "U.S. Vice President <PERSON> is elected as the 41st president.", "html": "1988 - U.S. <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1988_United_States_presidential_election\" title=\"1988 United States presidential election\">elected</a> as the 41st president.", "no_year_html": "U.S. <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1988_United_States_presidential_election\" title=\"1988 United States presidential election\">elected</a> as the 41st president.", "links": [{"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "1988 United States presidential election", "link": "https://wikipedia.org/wiki/1988_United_States_presidential_election"}]}, {"year": "1994", "text": "Republican Revolution: On the night of the 1994 United States midterm elections, Republicans make historic electoral gains by securing massive majorities in both houses of Congress (54 seats in the House and eight seats in the Senate, additionally), thus bringing to a close four decades of Democratic domination.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Republican_Revolution\" title=\"Republican Revolution\">Republican Revolution</a>: On the night of the <a href=\"https://wikipedia.org/wiki/1994_United_States_elections\" title=\"1994 United States elections\">1994 United States midterm elections</a>, <a href=\"https://wikipedia.org/wiki/Republican_Party_(United_States)\" title=\"Republican Party (United States)\">Republicans</a> make historic electoral gains by securing massive majorities in both houses of <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> (54 seats in the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House</a> and eight seats in the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">Senate</a>, additionally), thus bringing to a close four decades of <a href=\"https://wikipedia.org/wiki/Democratic_Party_(United_States)\" title=\"Democratic Party (United States)\">Democratic</a> domination.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Republican_Revolution\" title=\"Republican Revolution\">Republican Revolution</a>: On the night of the <a href=\"https://wikipedia.org/wiki/1994_United_States_elections\" title=\"1994 United States elections\">1994 United States midterm elections</a>, <a href=\"https://wikipedia.org/wiki/Republican_Party_(United_States)\" title=\"Republican Party (United States)\">Republicans</a> make historic electoral gains by securing massive majorities in both houses of <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> (54 seats in the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House</a> and eight seats in the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">Senate</a>, additionally), thus bringing to a close four decades of <a href=\"https://wikipedia.org/wiki/Democratic_Party_(United_States)\" title=\"Democratic Party (United States)\">Democratic</a> domination.", "links": [{"title": "Republican Revolution", "link": "https://wikipedia.org/wiki/Republican_Revolution"}, {"title": "1994 United States elections", "link": "https://wikipedia.org/wiki/1994_United_States_elections"}, {"title": "Republican Party (United States)", "link": "https://wikipedia.org/wiki/Republican_Party_(United_States)"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "Democratic Party (United States)", "link": "https://wikipedia.org/wiki/Democratic_Party_(United_States)"}]}, {"year": "1997", "text": "Eritrea adopts the nakfa as its official currency.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Eritrea\" title=\"Eritrea\">Eritrea</a> adopts the <a href=\"https://wikipedia.org/wiki/Eritrean_nakfa\" title=\"Eritrean nakfa\">nakfa</a> as its official currency.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eritrea\" title=\"Eritrea\">Eritrea</a> adopts the <a href=\"https://wikipedia.org/wiki/Eritrean_nakfa\" title=\"Eritrean nakfa\">nakfa</a> as its official currency.", "links": [{"title": "Eritrea", "link": "https://wikipedia.org/wiki/Eritrea"}, {"title": "Eritrean nakfa", "link": "https://wikipedia.org/wiki/Eritrean_nakfa"}]}, {"year": "1999", "text": "<PERSON> is killed at his junkyard near Flint, Michigan. His wife <PERSON><PERSON><PERSON>, who convinced her online lover <PERSON> to kill him (before later killing himself) was convicted of the crime, in what became the world's first Internet murder.", "html": "1999 - <PERSON> is killed at his junkyard near Flint, Michigan. His wife <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, who convinced her online lover <PERSON> to kill him (before later killing himself) was convicted of the crime, in what became the world's first Internet murder.", "no_year_html": "<PERSON> is killed at his junkyard near Flint, Michigan. His wife <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, who convinced her online lover <PERSON> to kill him (before later killing himself) was convicted of the crime, in what became the world's first Internet murder.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "Iraq disarmament crisis: UN Security Council Resolution 1441: The United Nations Security Council unanimously approves a resolution on Iraq, forcing <PERSON> to disarm or face \"serious consequences\".", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Iraq_disarmament_crisis\" title=\"Iraq disarmament crisis\">Iraq disarmament crisis</a>: <a href=\"https://wikipedia.org/wiki/UN_Security_Council_Resolution_1441\" class=\"mw-redirect\" title=\"UN Security Council Resolution 1441\">UN Security Council Resolution 1441</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council</a> unanimously approves a resolution on <a href=\"https://wikipedia.org/wiki/Ba%27athist_Iraq\" title=\"Ba'athist Iraq\">Iraq</a>, forcing <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Disarmament\" title=\"Disarmament\">disarm</a> or face \"serious consequences\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_disarmament_crisis\" title=\"Iraq disarmament crisis\">Iraq disarmament crisis</a>: <a href=\"https://wikipedia.org/wiki/UN_Security_Council_Resolution_1441\" class=\"mw-redirect\" title=\"UN Security Council Resolution 1441\">UN Security Council Resolution 1441</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council</a> unanimously approves a resolution on <a href=\"https://wikipedia.org/wiki/Ba%27athist_Iraq\" title=\"Ba'athist Iraq\">Iraq</a>, forcing <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Disarmament\" title=\"Disarmament\">disarm</a> or face \"serious consequences\".", "links": [{"title": "Iraq disarmament crisis", "link": "https://wikipedia.org/wiki/Iraq_disarmament_crisis"}, {"title": "UN Security Council Resolution 1441", "link": "https://wikipedia.org/wiki/UN_Security_Council_Resolution_1441"}, {"title": "United Nations Security Council", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council"}, {"title": "Ba'athist Iraq", "link": "https://wikipedia.org/wiki/Ba%27athist_Iraq"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Disarmament", "link": "https://wikipedia.org/wiki/Disarmament"}]}, {"year": "2004", "text": "Iraq War: More than 10,000 U.S. troops and a small number of Iraqi army units participate in a siege on the insurgent stronghold of Fallujah.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: More than 10,000 U.S. troops and a small number of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraqi</a> army units participate in a siege on the insurgent stronghold of <a href=\"https://wikipedia.org/wiki/Fallujah\" title=\"Fallujah\">Fallujah</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: More than 10,000 U.S. troops and a small number of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraqi</a> army units participate in a siege on the insurgent stronghold of <a href=\"https://wikipedia.org/wiki/Fallujah\" title=\"Fallujah\">Fallujah</a>.", "links": [{"title": "Iraq War", "link": "https://wikipedia.org/wiki/Iraq_War"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Fallujah", "link": "https://wikipedia.org/wiki/Fallujah"}]}, {"year": "2006", "text": "Israeli-Palestinian conflict: The Israeli Defense Force kill 19 Palestinian civilians in their homes during the shelling of Beit Hanoun.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: The <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israeli Defense Force</a> kill 19 <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> civilians in their homes during the <a href=\"https://wikipedia.org/wiki/2006_shelling_of_Beit_Hanoun\" title=\"2006 shelling of Beit Hanoun\">shelling of Beit Hanoun</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: The <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israeli Defense Force</a> kill 19 <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian</a> civilians in their homes during the <a href=\"https://wikipedia.org/wiki/2006_shelling_of_Beit_Hanoun\" title=\"2006 shelling of Beit Hanoun\">shelling of Beit Hanoun</a>.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "Israel Defense Forces", "link": "https://wikipedia.org/wiki/Israel_Defense_Forces"}, {"title": "Palestinians", "link": "https://wikipedia.org/wiki/Palestinians"}, {"title": "2006 shelling of Beit <PERSON>", "link": "https://wikipedia.org/wiki/2006_shelling_of_Beit_<PERSON>"}]}, {"year": "2011", "text": "The potentially hazardous asteroid 2005 YU55 passes 0.85 lunar distances from Earth (about 324,600 kilometres or 201,700 miles), the closest known approach by an asteroid of its brightness since 2010 XC15 in 1976.", "html": "2011 - The <a href=\"https://wikipedia.org/wiki/Potentially_hazardous_asteroid\" class=\"mw-redirect\" title=\"Potentially hazardous asteroid\">potentially hazardous asteroid</a> <a href=\"https://wikipedia.org/wiki/2005_YU55\" class=\"mw-redirect\" title=\"2005 YU55\">2005 YU55</a> passes 0.85 <a href=\"https://wikipedia.org/wiki/Lunar_distance_(astronomy)\" class=\"mw-redirect\" title=\"Lunar distance (astronomy)\">lunar distances</a> from Earth (about 324,600 kilometres or 201,700 miles), the closest known approach by an asteroid of its <a href=\"https://wikipedia.org/wiki/Absolute_magnitude#Solar_system\" title=\"Absolute magnitude\">brightness</a> since <span class=\"nowrap\"><a href=\"https://wikipedia.org/wiki/2010_XC15\" class=\"mw-redirect\" title=\"2010 XC15\">2010 XC<span style=\"position: relative; top: 0.3em;\"><span style=\"font-size:80%;\">15</span></span></a></span> in 1976.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Potentially_hazardous_asteroid\" class=\"mw-redirect\" title=\"Potentially hazardous asteroid\">potentially hazardous asteroid</a> <a href=\"https://wikipedia.org/wiki/2005_YU55\" class=\"mw-redirect\" title=\"2005 YU55\">2005 YU55</a> passes 0.85 <a href=\"https://wikipedia.org/wiki/Lunar_distance_(astronomy)\" class=\"mw-redirect\" title=\"Lunar distance (astronomy)\">lunar distances</a> from Earth (about 324,600 kilometres or 201,700 miles), the closest known approach by an asteroid of its <a href=\"https://wikipedia.org/wiki/Absolute_magnitude#Solar_system\" title=\"Absolute magnitude\">brightness</a> since <span class=\"nowrap\"><a href=\"https://wikipedia.org/wiki/2010_XC15\" class=\"mw-redirect\" title=\"2010 XC15\">2010 XC<span style=\"position: relative; top: 0.3em;\"><span style=\"font-size:80%;\">15</span></span></a></span> in 1976.", "links": [{"title": "Potentially hazardous asteroid", "link": "https://wikipedia.org/wiki/Potentially_hazardous_asteroid"}, {"title": "2005 YU55", "link": "https://wikipedia.org/wiki/2005_YU55"}, {"title": "Lunar distance (astronomy)", "link": "https://wikipedia.org/wiki/Lunar_distance_(astronomy)"}, {"title": "Absolute magnitude", "link": "https://wikipedia.org/wiki/Absolute_magnitude#Solar_system"}, {"title": "2010 XC15", "link": "https://wikipedia.org/wiki/2010_XC15"}]}, {"year": "2013", "text": "Typhoon <PERSON><PERSON>, one of the strongest tropical cyclones ever recorded, strikes the Visayas region of the Philippines; the storm left at least 6,340 people dead with over 1,000 still missing, and caused $2.86 billion (2013 USD; equivalent to $3.86 billion in 2024) in damage.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Typhoon_Haiyan\" title=\"Typhoon Haiyan\">Typhoon Haiyan</a>, one of the <a href=\"https://wikipedia.org/wiki/List_of_tropical_cyclone_records\" title=\"List of tropical cyclone records\">strongest tropical cyclones</a> ever recorded, strikes the <a href=\"https://wikipedia.org/wiki/Visayas\" title=\"Visayas\">Visayas</a> region of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>; the storm left at least 6,340 people dead with over 1,000 still missing, and caused $2.86 billion (2013 <a href=\"https://wikipedia.org/wiki/US_dollar\" class=\"mw-redirect\" title=\"US dollar\">USD</a>; equivalent to $3.86 billion in 2024) in damage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Typhoon_Haiyan\" title=\"Typhoon Haiyan\">Typhoon Haiyan</a>, one of the <a href=\"https://wikipedia.org/wiki/List_of_tropical_cyclone_records\" title=\"List of tropical cyclone records\">strongest tropical cyclones</a> ever recorded, strikes the <a href=\"https://wikipedia.org/wiki/Visayas\" title=\"Visayas\">Visayas</a> region of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>; the storm left at least 6,340 people dead with over 1,000 still missing, and caused $2.86 billion (2013 <a href=\"https://wikipedia.org/wiki/US_dollar\" class=\"mw-redirect\" title=\"US dollar\">USD</a>; equivalent to $3.86 billion in 2024) in damage.", "links": [{"title": "Typhoon Haiyan", "link": "https://wikipedia.org/wiki/Typhoon_Haiyan"}, {"title": "List of tropical cyclone records", "link": "https://wikipedia.org/wiki/List_of_tropical_cyclone_records"}, {"title": "Visayas", "link": "https://wikipedia.org/wiki/Visayas"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "US dollar", "link": "https://wikipedia.org/wiki/US_dollar"}]}, {"year": "2016", "text": "Indian Prime Minister <PERSON><PERSON><PERSON> publicly announces the withdrawal of ₹500 and ₹1000 denomination banknotes.", "html": "2016 - Indian Prime Minister <PERSON><PERSON><PERSON> publicly announces <a href=\"https://wikipedia.org/wiki/2016_Indian_banknote_demonetisation\" title=\"2016 Indian banknote demonetisation\">the withdrawal</a> of ₹500 and ₹1000 denomination banknotes.", "no_year_html": "Indian Prime Minister <PERSON><PERSON><PERSON> publicly announces <a href=\"https://wikipedia.org/wiki/2016_Indian_banknote_demonetisation\" title=\"2016 Indian banknote demonetisation\">the withdrawal</a> of ₹500 and ₹1000 denomination banknotes.", "links": [{"title": "2016 Indian banknote demonetisation", "link": "https://wikipedia.org/wiki/2016_Indian_banknote_demonetisation"}]}, {"year": "2016", "text": "<PERSON> is elected the 45th President of the United States, defeating <PERSON>, the first woman ever to receive a major party's nomination.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/2016_United_States_presidential_election\" title=\"2016 United States presidential election\">elected</a> the <a href=\"https://wikipedia.org/wiki/First_presidency_of_<PERSON>\" title=\"First presidency of <PERSON>\">45th</a> President of the United States, defeating <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first woman ever to receive a major party's <a href=\"https://wikipedia.org/wiki/2016_Democratic_Party_presidential_primaries\" title=\"2016 Democratic Party presidential primaries\">nomination</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/2016_United_States_presidential_election\" title=\"2016 United States presidential election\">elected</a> the <a href=\"https://wikipedia.org/wiki/First_presidency_of_<PERSON>_<PERSON>\" title=\"First presidency of <PERSON>\">45th</a> President of the United States, defeating <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first woman ever to receive a major party's <a href=\"https://wikipedia.org/wiki/2016_Democratic_Party_presidential_primaries\" title=\"2016 Democratic Party presidential primaries\">nomination</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "2016 United States presidential election", "link": "https://wikipedia.org/wiki/2016_United_States_presidential_election"}, {"title": "First presidency of <PERSON>", "link": "https://wikipedia.org/wiki/First_presidency_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2016 Democratic Party presidential primaries", "link": "https://wikipedia.org/wiki/2016_Democratic_Party_presidential_primaries"}]}, {"year": "2020", "text": "Myanmar holds the 2020 general election, re-electing a government led by the National League for Democracy, which is deposed by the Burmese military the following February during the 2021 Myanmar coup d'état.", "html": "2020 - Myanmar holds the <a href=\"https://wikipedia.org/wiki/2020_Myanmar_general_election\" title=\"2020 Myanmar general election\">2020 general election</a>, re-electing a government led by the <a href=\"https://wikipedia.org/wiki/National_League_for_Democracy\" title=\"National League for Democracy\">National League for Democracy</a>, which is deposed by the <a href=\"https://wikipedia.org/wiki/Tatmadaw\" title=\"Tatmadaw\">Burmese military</a> the following February during the <a href=\"https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat\" title=\"2021 Myanmar coup d'état\">2021 Myanmar coup d'état</a>.", "no_year_html": "Myanmar holds the <a href=\"https://wikipedia.org/wiki/2020_Myanmar_general_election\" title=\"2020 Myanmar general election\">2020 general election</a>, re-electing a government led by the <a href=\"https://wikipedia.org/wiki/National_League_for_Democracy\" title=\"National League for Democracy\">National League for Democracy</a>, which is deposed by the <a href=\"https://wikipedia.org/wiki/Tatmadaw\" title=\"Tatmadaw\">Burmese military</a> the following February during the <a href=\"https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat\" title=\"2021 Myanmar coup d'état\">2021 Myanmar coup d'état</a>.", "links": [{"title": "2020 Myanmar general election", "link": "https://wikipedia.org/wiki/2020_Myanmar_general_election"}, {"title": "National League for Democracy", "link": "https://wikipedia.org/wiki/National_League_for_Democracy"}, {"title": "Tatmadaw", "link": "https://wikipedia.org/wiki/Tatmadaw"}, {"title": "2021 Myanmar coup d'état", "link": "https://wikipedia.org/wiki/2021_Myanmar_coup_d%27%C3%A9tat"}]}], "Births": [{"year": "30", "text": "<PERSON><PERSON><PERSON>, Roman emperor (d. 98)", "html": "30 - AD 30 - <a href=\"https://wikipedia.org/wiki/Nerva\" title=\"Nerva\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 98)", "no_year_html": "AD 30 - <a href=\"https://wikipedia.org/wiki/Nerva\" title=\"Nerva\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 98)", "links": [{"title": "Nerva", "link": "https://wikipedia.org/wiki/Nerva"}]}, {"year": "1407", "text": "<PERSON>, French cardinal (d. 1474)", "html": "1407 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABtivy\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1474)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABtivy\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1474)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alain_de_Co%C3%ABtivy"}]}, {"year": "1417", "text": "<PERSON>, Count of Hanau-Lichtenberg (1458-1480) (d. 1480)", "html": "1417 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-Lichtenberg\" title=\"<PERSON>, Count of Hanau-Lichtenberg\"><PERSON>, Count of Hanau-Lichtenberg</a> (1458-1480) (d. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-Lichtenberg\" title=\"<PERSON>, Count of Hanau-Lichtenberg\"><PERSON>, Count of Hanau-Lichtenberg</a> (1458-1480) (d. 1480)", "links": [{"title": "<PERSON>, Count of Hanau-Lichtenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1456", "text": "Queen <PERSON><PERSON><PERSON>, Korean royal consort (d. 1474)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>\" title=\"Queen <PERSON><PERSON><PERSON>\">Queen <PERSON><PERSON><PERSON></a>, Korean royal consort (d. 1474)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>\" title=\"Queen <PERSON><PERSON><PERSON>\">Queen <PERSON><PERSON><PERSON></a>, Korean royal consort (d. 1474)", "links": [{"title": "Queen <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1491", "text": "<PERSON><PERSON><PERSON>, Italian monk and poet (d. 1544)", "html": "1491 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian monk and poet (d. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian monk and poet (d. 1544)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1543", "text": "<PERSON><PERSON><PERSON>, English noblewoman  (d. 1634)", "html": "1543 - <a href=\"https://wikipedia.org/wiki/Let<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English noblewoman (d. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Let<PERSON><PERSON>_<PERSON>\" title=\"Let<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English noblewoman (d. 1634)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lettice_<PERSON><PERSON>s"}]}, {"year": "1555", "text": "<PERSON><PERSON><PERSON><PERSON>, King of Burma (d. 1605)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/Nyaungyan_Min\" title=\"Nyaungyan Min\"><PERSON><PERSON><PERSON><PERSON></a>, King of Burma (d. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nyaungyan_Min\" title=\"Nyaungyan Min\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, King of Burma (d. 1605)", "links": [{"title": "Nyaungyan Min", "link": "https://wikipedia.org/wiki/Nyaungyan_Min"}]}, {"year": "1563", "text": "<PERSON>, Duke of Lorraine (d. 1624)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (d. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (d. 1624)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine"}]}, {"year": "1572", "text": "<PERSON>, Elector of Brandenburg (d. 1619)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (d. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (d. 1619)", "links": [{"title": "<PERSON>, Elector of Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>,_Elector_of_Brandenburg"}]}, {"year": "1622", "text": "<PERSON> of Sweden (d. 1660)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of <PERSON></a> (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of <PERSON>\"><PERSON> of <PERSON></a> (d. 1660)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1656", "text": "<PERSON>, English astronomer and mathematician (d. 1742)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and mathematician (d. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and mathematician (d. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1706", "text": "<PERSON>, German philosopher and judge (d. 1772)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and judge (d. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and judge (d. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, English author (d. 1768)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON> of Brunswick-Wolfenbüttel-Bevern (d. 1797)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/Elisabeth_<PERSON>_of_Brunswick-Wolfenb%C3%BCttel-Bevern\" title=\"<PERSON> of Brunswick-Wolfenbüttel-Bevern\"><PERSON> of Brunswick-Wolfenbüttel-Bevern</a> (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elisabeth_<PERSON>_of_Brunswick-Wolfenb%C3%BCttel-Bevern\" title=\"<PERSON> of Brunswick-Wolfenbüttel-Bevern\"><PERSON> of Brunswick-Wolfenbüttel-Bevern</a> (d. 1797)", "links": [{"title": "<PERSON> of Brunswick-Wolfenbüttel-Bevern", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Brunswick-Wolfenb%C3%BCttel-Bevern"}]}, {"year": "1723", "text": "<PERSON>, English admiral and politician, 24th Commodore Governor of Newfoundland (d. 1786)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, 24th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore Governor of Newfoundland</a> (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, 24th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore Governor of Newfoundland</a> (d. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of lieutenant governors of Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador"}]}, {"year": "1725", "text": "<PERSON>, German flute player and composer (d. 1805)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German flute player and composer (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German flute player and composer (d. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1738", "text": "<PERSON>, Finnish poet (d. 1776)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>na_Mj%C3%B6dh\" title=\"<PERSON>\"><PERSON></a>, Finnish poet (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Mj%C3%B6dh\" title=\"<PERSON>\"><PERSON></a>, Finnish poet (d. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_Mj%C3%B6dh"}]}, {"year": "1739", "text": "<PERSON>, Finnish professor and historian (d. 1804)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish professor and historian (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish professor and historian (d. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, German-Estonian linguist and author (d. 1832)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Estonian linguist and author (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Estonian linguist and author (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "Princess <PERSON> of the United Kingdom (d. 1840)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/Princess_Augusta_<PERSON>_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Augusta_Sophia_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (d. 1840)", "links": [{"title": "Princess <PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/Princess_Augusta_Sophia_of_the_United_Kingdom"}]}, {"year": "1772", "text": "<PERSON>, American lawyer and politician, 9th United States Attorney General (d. 1834)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Attorney_General)\" class=\"mw-redirect\" title=\"<PERSON> (Attorney General)\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Attorney_General)\" class=\"mw-redirect\" title=\"<PERSON> (Attorney General)\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 1834)", "links": [{"title": "<PERSON> (Attorney General)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Attorney_General)"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1788", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovene poet and educator (d. 1853)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/Mih%C3%A1<PERSON>_<PERSON>alanits\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovene poet and educator (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mih%C3%<PERSON><PERSON>_<PERSON>alanits\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovene poet and educator (d. 1853)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mih%C3%A1ly_Bertalanits"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON><PERSON>, 1st Earl of Lytton, English poet and diplomat, 30th Governor-General of India (d. 1880)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_L<PERSON>\" title=\"<PERSON>, 1st Earl of Lytton\"><PERSON>, 1st Earl of Lytton</a>, English poet and diplomat, 30th <a href=\"https://wikipedia.org/wiki/List_of_Governors-General_of_India\" class=\"mw-redirect\" title=\"List of Governors-General of India\">Governor-General of India</a> (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Lytton\" title=\"<PERSON>, 1st Earl of Lytton\"><PERSON>, 1st Earl of Lytton</a>, English poet and diplomat, 30th <a href=\"https://wikipedia.org/wiki/List_of_Governors-General_of_India\" class=\"mw-redirect\" title=\"List of Governors-General of India\">Governor-General of India</a> (d. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, 1st Earl of Lytton", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_1st_Earl_of_L<PERSON>ton"}, {"title": "List of Governors-General of India", "link": "https://wikipedia.org/wiki/List_of_Governors-General_of_India"}]}, {"year": "1836", "text": "<PERSON>, American businessman, founded the Milton Bradley Company (d. 1911)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Milton Bradley\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Milton_Bradley_Company\" title=\"Milton Bradley Company\">Milton Bradley Company</a> (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bradley\" title=\"Milton Bradley\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Milton_Bradley_Company\" title=\"Milton Bradley Company\">Milton Bradley Company</a> (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Milton Bradley Company", "link": "https://wikipedia.org/wiki/Milton_Bradley_Company"}]}, {"year": "1837", "text": "<PERSON><PERSON>, Georgian journalist, lawyer, and politician (d. 1907)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian journalist, lawyer, and politician (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian journalist, lawyer, and politician (d. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, French politician, 6th President of France (d. 1907)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1847", "text": "<PERSON>, Irish novelist and critic, created <PERSON> (d. 1912)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist and critic, created <a href=\"https://wikipedia.org/wiki/Count_Dracula\" title=\"Count Dracula\">Count Dracula</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist and critic, created <a href=\"https://wikipedia.org/wiki/Count_Dracula\" title=\"Count Dracula\">Count Dracula</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Count <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON>, German mathematician and philosopher (d. 1925)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ege\" title=\"<PERSON><PERSON><PERSON>ege\"><PERSON><PERSON><PERSON></a>, German mathematician and philosopher (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ege\"><PERSON><PERSON><PERSON></a>, German mathematician and philosopher (d. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Swedish physicist and academic (d. 1919)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and academic (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and academic (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON><PERSON>, Greek politician, Prime Minister of Greece (d. 1939)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>antafyllakos"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1866", "text": "<PERSON>, 1st Baron <PERSON>, English businessman, founded the Austin Motor Company (d. 1941)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/Austin_Motor_Company\" title=\"Austin Motor Company\">Austin Motor Company</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/Austin_Motor_Company\" title=\"Austin Motor Company\">Austin Motor Company</a> (d. 1941)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>"}, {"title": "Austin Motor Company", "link": "https://wikipedia.org/wiki/Austin_Motor_Company"}]}, {"year": "1868", "text": "<PERSON>, German mathematician and academic (d. 1942)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, English palaeontologist and archaeozoologist (d. 1951)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English palaeontologist and archaeozoologist (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English palaeontologist and archaeozoologist (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Canadian painter and illustrator (d. 1942)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and illustrator (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and illustrator (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, English composer and poet (d. 1953)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and poet (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and poet (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American painter (d. 1935)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Swiss psychiatrist and psychoanalyst (d. 1922)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist and psychoanalyst (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist and psychoanalyst (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Greek painter (d. 1959)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek painter (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek painter (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, German geologist and academic (d. 1951)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geologist and academic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geologist and academic (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, German architect and academic (d. 1966)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect and academic (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect and academic (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Japanese general and politician, 4th Japanese Military Governors of the Philippines (d. 1946)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Philippines\" title=\"Governor-General of the Philippines\">Japanese Military Governors of the Philippines</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Philippines\" title=\"Governor-General of the Philippines\">Japanese Military Governors of the Philippines</a> (d. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor-General of the Philippines", "link": "https://wikipedia.org/wiki/Governor-General_of_the_Philippines"}]}, {"year": "1888", "text": "<PERSON>, Norwegian pianist and composer (d. 1974)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian pianist and composer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian pianist and composer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Thai king (d. 1941)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>raj<PERSON><PERSON><PERSON>\" title=\"<PERSON>raj<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Thai king (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Thai king (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Praj<PERSON>hipok"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Austrian painter and graphic artist (d. 1975)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27Albert\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian painter and graphic artist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_d%27Albert\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian painter and graphic artist (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_d%27Albert"}]}, {"year": "1896", "text": "<PERSON><PERSON>, American baseball player and manager (d. 1977)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Canadian-American actress and singer (d. 1937)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American journalist and activist (d. 1980)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Day\" title=\"Dorothy Day\"><PERSON></a>, American journalist and activist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dorothy_Day\" title=\"Dorothy Day\"><PERSON></a>, American journalist and activist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American journalist and author (d. 1949)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Canadian poet and anthologist (d. 1980)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian poet and anthologist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian poet and anthologist (d. 1980)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, English-American journalist and author, co-founded the National Guardian (d. 1990)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American journalist and author, co-founded the <i><a href=\"https://wikipedia.org/wiki/National_Guardian\" title=\"National Guardian\">National Guardian</a></i> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>frage\"><PERSON><PERSON></a>, English-American journalist and author, co-founded the <i><a href=\"https://wikipedia.org/wiki/National_Guardian\" title=\"National Guardian\">National Guardian</a></i> (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>frage"}, {"title": "National Guardian", "link": "https://wikipedia.org/wiki/National_Guardian"}]}, {"year": "1908", "text": "<PERSON>, American journalist and author (d. 1998)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American general (d. 1975)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American golfer (d. 1975)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1975)", "links": [{"title": "Al <PERSON>", "link": "https://wikipedia.org/wiki/Al_Brosch"}]}, {"year": "1911", "text": "<PERSON>, Australian public servant and diplomat (d. 1991)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(UN_administrator)\" title=\"<PERSON> (UN administrator)\"><PERSON></a>, Australian public servant and diplomat (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(UN_administrator)\" title=\"<PERSON> (UN administrator)\"><PERSON></a>, Australian public servant and diplomat (d. 1991)", "links": [{"title": "<PERSON> (UN administrator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(UN_administrator)"}]}, {"year": "1912", "text": "June Ha<PERSON>, American actress, singer and dancer (d. 2010)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/June_Havoc\" title=\"June Havoc\">June Havoc</a>, American actress, singer and dancer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_Havoc\" title=\"June Havoc\">June Havoc</a>, American actress, singer and dancer (d. 2010)", "links": [{"title": "June Havoc", "link": "https://wikipedia.org/wiki/June_Havoc"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek general and politician, Deputy Prime Minister of Greece (d. 2016)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Stylianos_Pattakos\" title=\"<PERSON><PERSON><PERSON>s Pattakos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece\" title=\"Deputy Prime Minister of Greece\">Deputy Prime Minister of Greece</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yl<PERSON>s_<PERSON>kos\" title=\"<PERSON>yl<PERSON>s Pattakos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece\" title=\"Deputy Prime Minister of Greece\">Deputy Prime Minister of Greece</a> (d. 2016)", "links": [{"title": "Stylianos <PERSON>", "link": "https://wikipedia.org/wiki/Stylianos_Pattakos"}, {"title": "Deputy Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece"}]}, {"year": "1913", "text": "<PERSON>, American boxer (d. 1995)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor, director, and producer (d. 2021)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American Episcopal priest and gay rights activist (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, American Episcopal priest and gay rights activist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, American Episcopal priest and gay rights activist (d. 2006)", "links": [{"title": "<PERSON> (priest)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Japanese soldier (d. 1999)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese soldier (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese soldier (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, German typographer and calligrapher (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German typographer and calligrapher (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German typographer and calligrapher (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American historian and academic (d. 2016)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Indian actress, dancer, and choreographer (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress, dancer, and choreographer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Indian actress, dancer, and choreographer (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sitara_Devi"}]}, {"year": "1920", "text": "<PERSON>, American actress (d. 1998)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian cardinal (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Eug%C3%AAnio_Sales\" title=\"Eugênio Sales\">Eugênio Sales</a>, Brazilian cardinal (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%AAnio_Sales\" title=\"Eugênio Sales\">Eugênio Sales</a>, Brazilian cardinal (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%AAnio_Sales"}]}, {"year": "1921", "text": "<PERSON>, American composer, musicologist, and academic (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, musicologist, and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, musicologist, and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, South African surgeon and academic (d. 2001)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African surgeon and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African surgeon and academic (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, American computer scientist and academic (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American computer scientist and academic (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American computer scientist and academic (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer, coach, and sportscaster (d. 1996)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Menezes\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Menezes\"><PERSON><PERSON><PERSON></a>, Brazilian footballer, coach, and sportscaster (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>ezes\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> de Menezes\"><PERSON><PERSON><PERSON></a>, Brazilian footballer, coach, and sportscaster (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Romanian-born Israeli rabbi (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>)\" title=\"<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON> Rebbe)\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian-born Israeli rabbi (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(<PERSON><PERSON><PERSON><PERSON>_<PERSON>bbe)\" title=\"<PERSON><PERSON><PERSON><PERSON> (Pash<PERSON><PERSON>bbe)\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian-born Israeli rabbi (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (Pashkaner <PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(<PERSON><PERSON><PERSON><PERSON>_<PERSON>)"}]}, {"year": "1923", "text": "<PERSON>, American physicist and engineer, Nobel Prize laureate (d. 2005)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1924", "text": "<PERSON>, Canadian ice hockey player and soldier (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and soldier (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and soldier (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor (d. 1974)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor (d. 1974)", "links": [{"title": "<PERSON> (American actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)"}]}, {"year": "1924", "text": "<PERSON>, American statistician and academic (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, South Korean archbishop", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>-hi\" title=\"<PERSON><PERSON>-hi\"><PERSON><PERSON>-hi</a>, South Korean archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>-hi\" title=\"<PERSON><PERSON>-hi\"><PERSON><PERSON>-hi</a>, South Korean archbishop", "links": [{"title": "<PERSON><PERSON>-hi", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>-hi"}]}, {"year": "1924", "text": "<PERSON>, Marshal of the Soviet Union (d. 2020)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Marshal_of_the_Soviet_Union\" title=\"Marshal of the Soviet Union\">Marshal of the Soviet Union</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Marshal_of_the_Soviet_Union\" title=\"Marshal of the Soviet Union\">Marshal of the Soviet Union</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Marshal of the Soviet Union", "link": "https://wikipedia.org/wiki/Marshal_of_the_Soviet_Union"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, American nuclear chemist", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American nuclear chemist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American nuclear chemist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, 7th Deputy Prime Minister of India", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India\" title=\"Deputy Prime Minister of India\">Deputy Prime Minister of India</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India\" title=\"Deputy Prime Minister of India\">Deputy Prime Minister of India</a>", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of India", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India"}]}, {"year": "1927", "text": "<PERSON>, American singer (d. 2009)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English singer and comedian (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and comedian (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and comedian (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese general and politician, 4th President of the Republic of Vietnam (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese general and politician, 4th <a href=\"https://wikipedia.org/wiki/Leaders_of_South_Vietnam\" class=\"mw-redirect\" title=\"Leaders of South Vietnam\">President of the Republic of Vietnam</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese general and politician, 4th <a href=\"https://wikipedia.org/wiki/Leaders_of_South_Vietnam\" class=\"mw-redirect\" title=\"Leaders of South Vietnam\">President of the Republic of Vietnam</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh"}, {"title": "Leaders of South Vietnam", "link": "https://wikipedia.org/wiki/Leaders_of_South_Vietnam"}]}, {"year": "1927", "text": "<PERSON>, American singer and actress (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Page"}]}, {"year": "1928", "text": "<PERSON>, Australian politician, 37th Premier of South Australia (d. 2004)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Des_Corcoran"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1929", "text": "<PERSON>, American football player and coach (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese philosopher and academic", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Castanheira_Neves\" title=\"António Castanheira Neves\"><PERSON><PERSON><PERSON><PERSON> Castanheira <PERSON></a>, Portuguese philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Castanheira_Neves\" title=\"<PERSON>t<PERSON>io Castanheira Neves\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese philosopher and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_Castanheira_Neves"}]}, {"year": "1931", "text": "<PERSON>, English-Rhodesian motorcycle racer", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Rhodesian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Rhodesian motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian-American journalist and author (d. 2016)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Safer\"><PERSON></a>, Canadian-American journalist and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Safer\"><PERSON></a>, Canadian-American journalist and author (d. 2016)", "links": [{"title": "<PERSON>r", "link": "https://wikipedia.org/wiki/<PERSON>_Safer"}]}, {"year": "1931", "text": "<PERSON>, Italian film director and screenwriter (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian film director and screenwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian film director and screenwriter (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress (d. 2018)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress (d. <a href=\"https://wikipedia.org/wiki/2018\" title=\"2018\">2018</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress (d. <a href=\"https://wikipedia.org/wiki/2018\" title=\"2018\">2018</a>)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>n"}, {"title": "2018", "link": "https://wikipedia.org/wiki/2018"}]}, {"year": "1932", "text": "<PERSON>, American journalist and author (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English race car driver (d. 2009)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, French-Swiss actor, producer, screenwriter (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss actor, producer, screenwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss actor, producer, screenwriter (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter (d. 1990)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St<PERSON>s_Dionysiou"}]}, {"year": "1935", "text": "<PERSON>, Colombian cardinal (d. 2008)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Alfonso_L%C3%B3pez_Trujillo\" title=\"<PERSON>\"><PERSON></a>, Colombian cardinal (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonso_L%C3%B3pez_Trujillo\" title=\"<PERSON>\"><PERSON></a>, Colombian cardinal (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfonso_L%C3%B3pez_Trujillo"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Italian actress (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Moroccan police officer and politician (d. 2007)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan police officer and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan police officer and politician (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ri"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American basketball player", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English composer, author, and poet (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, author, and poet (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, author, and poet (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, British actress (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Welsh actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Puerto Rican-American jockey", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, Puerto Rican-American jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, Puerto Rican-American jockey", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>."}]}, {"year": "1942", "text": "<PERSON><PERSON>, Italian footballer and sportscaster", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English footballer and manager (d. 2019)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer and actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian-Swiss architect, painter, and author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Ard<PERSON><PERSON>_Cantafora\" title=\"<PERSON>rd<PERSON><PERSON> Cantafora\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-Swiss architect, painter, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ard<PERSON><PERSON>_<PERSON>tafora\" title=\"<PERSON><PERSON><PERSON><PERSON> Can<PERSON>fo<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-Swiss architect, painter, and author", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arduino_Cantafora"}]}, {"year": "1945", "text": "<PERSON>, American serial killer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Australian-born music producer, songwriter, arranger, singer, and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-born music producer, songwriter, arranger, singer, and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-born music producer, songwriter, arranger, singer, and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American drummer (d. 1996)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer (d. 1996)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(drummer)"}]}, {"year": "1945", "text": "<PERSON>, English cardinal", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American composer (d. 2013)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Dutch footballer and manager", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English bishop (d. 2017)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (d. 2017)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American singer-songwriter (d. 1979)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ton\"><PERSON><PERSON></a>, American singer-songwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American physician and astronaut", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physician and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physician and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American physician and surgeon (d. 2013)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and surgeon (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and surgeon (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American captain and astronaut (d. 2014)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and astronaut (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and astronaut (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American businessman, author, and activist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, author, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, author, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American journalist and actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American R&B singer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Argentine military commander", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine military commander", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine military commander", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English lawyer and judge", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)"}]}, {"year": "1951", "text": "<PERSON>, American philosopher and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American baseball player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American publisher and businesswoman", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Dutch cyclist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American baseball player and sportscaster (d. 2021)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alf<PERSON>_<PERSON>ard"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Greek footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Foiros\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>iorgos Foiros\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American animator, director, producer, and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Indian politician (d. 2013)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, French-English journalist and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American lawyer and radio host", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American journalist and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Japanese-British novelist, screenwriter, and short story writer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-British novelist, screenwriter, and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-British novelist, screenwriter, and short story writer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Greek jurist and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Than<PERSON>_Pafilis\" title=\"Thanasis Pafilis\"><PERSON><PERSON></a>, Greek jurist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Than<PERSON>_Pafilis\" title=\"Thanasis Pafilis\"><PERSON><PERSON></a>, Greek jurist and politician", "links": [{"title": "Thanasis <PERSON>", "link": "https://wikipedia.org/wiki/Thanasis_Pafilis"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American author and educator", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Norwegian singer-songwriter and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON>, New Zealand-English screenwriter, film and television producer, and film director", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English screenwriter, film and television producer, and film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English screenwriter, film and television producer, and film director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American record producer and engineer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(record_producer)\" title=\"<PERSON> (record producer)\"><PERSON></a>, American record producer and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(record_producer)\" title=\"<PERSON> (record producer)\"><PERSON></a>, American record producer and engineer", "links": [{"title": "<PERSON> (record producer)", "link": "https://wikipedia.org/wiki/<PERSON>_(record_producer)"}]}, {"year": "1957", "text": "<PERSON>, English footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American swimmer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(swimmer)"}]}, {"year": "1957", "text": "<PERSON><PERSON>, English guitarist and songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Estonian singer and director", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian singer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian singer and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mer"}]}, {"year": "1958", "text": "<PERSON>, American clarinet player and composer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American author and educator", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer and journalist (d. 2013)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Sel%C3%A7uk_Yu<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Yula\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and journalist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sel%C3%A7uk_Yu<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Yula\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and journalist (d. 2013)", "links": [{"title": "Selçuk Yula", "link": "https://wikipedia.org/wiki/Sel%C3%A7uk_Yula"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Czech footballer and manager (d. 2013)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Jan%C5%AF\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Jan%C5%AF\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer and manager (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miroslav_Jan%C5%AF"}]}, {"year": "1959", "text": "<PERSON>, American drag queen performer and director", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Chi_LaRue\" title=\"Chi Chi LaRue\"><PERSON> Chi LaRue</a>, American <a href=\"https://wikipedia.org/wiki/Drag_queen\" title=\"Drag queen\">drag queen</a> performer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Chi_LaRue\" title=\"Chi Chi LaRue\"><PERSON> Chi LaRue</a>, American <a href=\"https://wikipedia.org/wiki/Drag_queen\" title=\"Drag queen\">drag queen</a> performer and director", "links": [{"title": "Chi Chi <PERSON>ue", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Drag queen", "link": "https://wikipedia.org/wiki/Drag_queen"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Russian actor, singer, and director", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian actor, singer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian actor, singer, and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Swedish actor and producer (d. 2017)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, English footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American singer, actor, and television personality", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, actor, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, actor, and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leif_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English hypnotist and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hypnotist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hypnotist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American baseball player and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American bodybuilder and boxer (d. 2014)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and boxer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and boxer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Venezuelan journalist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, British chef, restaurateur, and television host/personality", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chef, restaurateur, and television host/personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chef, restaurateur, and television host/personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Dominican baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON> (outfielder)", "link": "https://wikipedia.org/wiki/<PERSON>_Rodr%C3%<PERSON><PERSON><PERSON>_(outfielder)"}]}, {"year": "1968", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1968", "text": "<PERSON>, Dominican baseball player and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Offerman\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Offerman\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>erman"}]}, {"year": "1968", "text": "<PERSON>, Italian footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American businessman, co-founded Myspace", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Myspace\" title=\"Myspace\">Myspace</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Myspace\" title=\"Myspace\">Myspace</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Myspace", "link": "https://wikipedia.org/wiki/Myspace"}]}, {"year": "1970", "text": "<PERSON>, Bermudian cricketer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bermudian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bermudian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1970", "text": "<PERSON>, Jamaican singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, Jamaican singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, Jamaican singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Costa Rican footballer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Po<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>s"}]}, {"year": "1971", "text": "<PERSON>, Spanish director, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON> N9ne, American musician, record producer, and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Tech_N9ne\" title=\"Tech N9ne\">Tech N9ne</a>, American musician, record producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tech_N9ne\" title=\"Tech N9ne\">Tech N9ne</a>, American musician, record producer, and actor", "links": [{"title": "Tech N9ne", "link": "https://wikipedia.org/wiki/Tech_N9ne"}]}, {"year": "1972", "text": "<PERSON>, Australian swimmer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American model and actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian artistic gymnast", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Shadbolt\" title=\"<PERSON> Shadbolt\"><PERSON></a>, Australian artistic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Shadbolt\" title=\"<PERSON> Shadbolt\"><PERSON></a>, Australian artistic gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>bolt"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%<PERSON>ek_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_<PERSON>le"}]}, {"year": "1973", "text": "<PERSON>, Estonian politician, 22nd Estonian Minister of Defence", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, 22nd <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, 22nd <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Defence (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)"}]}, {"year": "1973", "text": "<PERSON>, American journalist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American author", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, South African swimmer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Japanese author and illustrator, created <PERSON><PERSON><PERSON>", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator, created <i><a href=\"https://wikipedia.org/wiki/Naruto\" title=\"<PERSON>ru<PERSON>\"><PERSON>ru<PERSON></a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator, created <i><a href=\"https://wikipedia.org/wiki/Naruto\" title=\"<PERSON>ru<PERSON>\"><PERSON>ru<PERSON></a></i>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naruto"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Japanese illustrator", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Welsh actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English film maker, actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English film maker, actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English film maker, actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American basketball player and sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Czech tennis player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Va%C5%A1kov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Va%C5%A1kov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alena_Va%C5%A1kov%C3%A1"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n%C3%A1%C5%99\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1%C5%99\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bedn%C3%A1%C5%99"}]}, {"year": "1976", "text": "<PERSON>, Australian cricketer and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American director, producer, and visual effects designer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Strause\" class=\"mw-redirect\" title=\"Brothers Strause\"><PERSON></a>, American director, producer, and visual effects designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Strause\" class=\"mw-redirect\" title=\"Brothers Strause\"><PERSON></a>, American director, producer, and visual effects designer", "links": [{"title": "Brothers Strause", "link": "https://wikipedia.org/wiki/Brothers_Strause"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Canadian singer-songwriter, producer, and actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Jully_<PERSON>\" title=\"<PERSON><PERSON> Black\"><PERSON><PERSON></a>, Canadian singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Black\"><PERSON><PERSON></a>, Canadian singer-songwriter, producer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Dutch footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1978", "text": "<PERSON>, Iranian footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Japanese actor and entertainer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and entertainer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>-<PERSON>, English social worker and politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English social worker and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English social worker and politician", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/J%C3%BAlio_S%C3%A9rgio\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BAlio_S%C3%A9rgio\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BAlio_S%C3%A9rgio"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Belizean rapper and politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belizean rapper and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belizean rapper and politician", "links": [{"title": "Shyne", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ne"}]}, {"year": "1979", "text": "<PERSON>, Italian rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Irish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Dominican actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian writer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lu%C3%<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English radio and television host", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, French footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Yan<PERSON>_<PERSON>\" title=\"Yan<PERSON> Kermorgant\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Yan<PERSON>organt\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yan<PERSON>_<PERSON>gant"}]}, {"year": "1982", "text": "<PERSON>, Jr., American wrestler and actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American wrestler and actor", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1982", "text": "<PERSON><PERSON>, Finnish motorcycle racer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish motorcycle racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1982", "text": "<PERSON>, Australian singer-songwriter and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American short story writer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Turkish basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Sinan_G%C3%BCler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sinan_G%C3%BCler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sinan_G%C3%BCler"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, German javelin thrower", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German javelin thrower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>veer"}]}, {"year": "1983", "text": "<PERSON>, Russian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English-New Zealand singer-songwriter and producer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Bangladeshi cricketer (d. 2012)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi cricketer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi cricketer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Japanese model and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Canadian actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_(footballer,_born_1985)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1985)\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_(footballer,_born_1985)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1985)\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1985)", "link": "https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_(footballer,_born_1985)"}]}, {"year": "1985", "text": "<PERSON>, English-American television personality", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Austrian tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Welsh rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American computer programmer and activist (d. 2013)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and activist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Paraguayan footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/%C3%89dgar_Ben%C3%ADtez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89dgar_Ben%C3%ADtez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Paraguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89dgar_Ben%C3%ADtez"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Malaysian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Mohd_Faiz_Subri\" title=\"Mo<PERSON>d Faiz Subri\"><PERSON><PERSON><PERSON> Fai<PERSON></a>, Malaysian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>d_Faiz_Subri\" title=\"Mohd Faiz Subri\"><PERSON><PERSON><PERSON> Fai<PERSON></a>, Malaysian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mohd_Faiz_Subri"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Cuban-American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Yasmani_Grandal\" title=\"Yasmani Grandal\"><PERSON><PERSON><PERSON></a>, Cuban-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yasmani_Grandal\" title=\"Yasmani Grandal\"><PERSON><PERSON><PERSON></a>, Cuban-American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON>i_Grandal"}]}, {"year": "1988", "text": "<PERSON>, Canadian actress and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Slovak heptathlete", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Lucia_Slani%C4%8Dkov%C3%A1\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Slovak heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucia_Slani%C4%8Dkov%C3%A1\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Slovak heptathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lucia_Slani%C4%8Dkov%C3%A1"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1988)\" title=\"<PERSON> (basketball, born 1988)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1988)\" title=\"<PERSON> (basketball, born 1988)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1988)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(basketball,_born_1988)"}]}, {"year": "1989", "text": "<PERSON>, French footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/SZA\" title=\"SZA\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SZA\" title=\"SZA\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "SZA", "link": "https://wikipedia.org/wiki/SZA"}]}, {"year": "1990", "text": "<PERSON>, Estonian sailor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian sailor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American wheelchair athlete", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wheelchair athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wheelchair athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American actor and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English YouTube personality and pro gamer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English YouTube personality and pro gamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English YouTube personality and pro gamer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, French footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Scottish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American singer and songwriter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Alain<PERSON>\"><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Alain<PERSON>\"><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Egyptian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>w<PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Akram_<PERSON>wfik"}]}, {"year": "1999", "text": "<PERSON>, German basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian rhythmic gymnast", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian rhythmic gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actress", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "397", "text": "<PERSON> of Tours, Frankish bishop and saint", "html": "397 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tours\" title=\"<PERSON> of Tours\"><PERSON> Tours</a>, Frankish bishop and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Martin of Tours\"><PERSON> of Tours</a>, Frankish bishop and saint", "links": [{"title": "<PERSON> of Tours", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "618", "text": "<PERSON><PERSON><PERSON><PERSON>, pope of the Catholic Church", "html": "618 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>tus_I\" title=\"<PERSON> <PERSON><PERSON><PERSON>tus I\"><PERSON><PERSON><PERSON><PERSON> I</a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>tus_I\" title=\"Pope <PERSON><PERSON><PERSON>tus I\"><PERSON><PERSON><PERSON><PERSON> I</a>, pope of the Catholic Church", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_I"}]}, {"year": "785", "text": "<PERSON><PERSON>, Japanese prince", "html": "785 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>\" title=\"Prince <PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese prince", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>\" title=\"Prince <PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese prince", "links": [{"title": "Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "789", "text": "<PERSON><PERSON><PERSON>, bishop of Bremen", "html": "789 - <a href=\"https://wikipedia.org/wiki/Willehad\" title=\"<PERSON>eh<PERSON>\"><PERSON><PERSON><PERSON></a>, bishop of Bremen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ehad\" title=\"<PERSON>eh<PERSON>\"><PERSON><PERSON><PERSON></a>, bishop of Bremen", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ehad"}]}, {"year": "928", "text": "<PERSON><PERSON>, Chinese general", "html": "928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese general", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ng"}]}, {"year": "940", "text": "<PERSON>, Chinese chancellor (b. 866)", "html": "940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chancellor (b. 866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chancellor (b. 866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "943", "text": "<PERSON>, empress of Qi (Ten Kingdoms) (b. 877)", "html": "943 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(Li_<PERSON>%27s_wife)\" title=\"Empress <PERSON> (<PERSON>'s wife)\"><PERSON></a>, empress of <a href=\"https://wikipedia.org/wiki/Qi_(Li_<PERSON>%27s_state)\" title=\"<PERSON> (<PERSON>'s state)\">Qi</a> (<a href=\"https://wikipedia.org/wiki/Ten_Kingdoms\" class=\"mw-redirect\" title=\"Ten Kingdoms\">Ten Kingdoms</a>) (b. 877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(Li_<PERSON>%27s_wife)\" title=\"Empress <PERSON> (<PERSON>'s wife)\"><PERSON></a>, empress of <a href=\"https://wikipedia.org/wiki/Qi_(Li_Mao<PERSON>%27s_state)\" title=\"<PERSON> (<PERSON>'s state)\">Qi</a> (<a href=\"https://wikipedia.org/wiki/Ten_Kingdoms\" class=\"mw-redirect\" title=\"Ten Kingdoms\">Ten Kingdoms</a>) (b. 877)", "links": [{"title": "<PERSON> <PERSON> (<PERSON>'s wife)", "link": "https://wikipedia.org/wiki/Empress_<PERSON>_(<PERSON>_<PERSON>%27s_wife)"}, {"title": "Qi (Li Maozhen's state)", "link": "https://wikipedia.org/wiki/<PERSON>_(Li_Maozhen%27s_state)"}, {"title": "Ten Kingdoms", "link": "https://wikipedia.org/wiki/Ten_Kingdoms"}]}, {"year": "955", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, pope of the Catholic Church", "html": "955 - <a href=\"https://wikipedia.org/wiki/Pope_Agapetus_II\" title=\"Pope Agapetus II\"><PERSON>ga<PERSON><PERSON> II</a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Agapetus_II\" title=\"Pope Agapetus II\"><PERSON>ga<PERSON><PERSON> II</a>, pope of the Catholic Church", "links": [{"title": "Pope Agapetus II", "link": "https://wikipedia.org/wiki/Pope_Agapetus_II"}]}, {"year": "977", "text": "<PERSON>, Andalusian historian", "html": "977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_al-Q%C5%AB%E1%B9%ADiyya\" class=\"mw-redirect\" title=\"Ibn al-Qūṭiyya\"><PERSON> al-Qūṭ<PERSON></a>, Andalusian historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-Q%C5%AB%E1%B9%ADiyya\" class=\"mw-redirect\" title=\"Ibn al-Qūṭiyya\"><PERSON> al-Qūṭi<PERSON></a>, Andalusian historian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Q%C5%AB%E1%B9%ADiyya"}]}, {"year": "1067", "text": "<PERSON><PERSON> of León, Queen of León (b. c. 1018)", "html": "1067 - <a href=\"https://wikipedia.org/wiki/San<PERSON>_of_Le%C3%B3n\" title=\"<PERSON><PERSON> of León\"><PERSON><PERSON> of León</a>, Queen of León (b. c. 1018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/San<PERSON>_of_Le%C3%B3n\" title=\"<PERSON><PERSON> of León\"><PERSON><PERSON> of León</a>, Queen of León (b. c. 1018)", "links": [{"title": "Sancha of León", "link": "https://wikipedia.org/wiki/Sancha_of_Le%C3%B3n"}]}, {"year": "1115", "text": "<PERSON> of Amiens, French bishop and saint (b. 1066)", "html": "1115 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Amiens\" title=\"<PERSON> of Amiens\"><PERSON> of Amiens</a>, French bishop and saint (b. 1066)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Amiens\" title=\"<PERSON> of Amiens\"><PERSON> of Amiens</a>, French bishop and saint (b. 1066)", "links": [{"title": "<PERSON> of Amiens", "link": "https://wikipedia.org/wiki/Godfrey_of_Amiens"}]}, {"year": "1122", "text": "<PERSON><PERSON><PERSON>, Artuqid ruler of Mardin", "html": "1122 - <a href=\"https://wikipedia.org/wiki/Ilghazi\" title=\"Ilghazi\">Ilghazi</a>, <a href=\"https://wikipedia.org/wiki/Artuqid\" class=\"mw-redirect\" title=\"Artuqid\">Artuqi<PERSON></a> ruler of <a href=\"https://wikipedia.org/wiki/Mardin\" title=\"Mardin\"><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ilghazi\" title=\"Ilghazi\">Ilghazi</a>, <a href=\"https://wikipedia.org/wiki/Artuqid\" class=\"mw-redirect\" title=\"Artuqid\">Artuqid</a> ruler of <a href=\"https://wikipedia.org/wiki/Mardin\" title=\"Mardin\"><PERSON><PERSON></a>", "links": [{"title": "Ilghazi", "link": "https://wikipedia.org/wiki/Ilghazi"}, {"title": "Art<PERSON>qi<PERSON>", "link": "https://wikipedia.org/wiki/Artuqid"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>din"}]}, {"year": "1171", "text": "<PERSON>, Count of Hainaut (b. 1108)", "html": "1171 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Count_of_Hainaut\" title=\"<PERSON> IV, Count of Hainaut\"><PERSON> IV, Count of Hainaut</a> (b. 1108)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Count_of_Hainaut\" title=\"<PERSON> IV, Count of Hainaut\"><PERSON> IV, Count of Hainaut</a> (b. 1108)", "links": [{"title": "<PERSON>, Count of Hainaut", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_Hai<PERSON>ut"}]}, {"year": "1195", "text": "<PERSON>, Count <PERSON><PERSON> of the Rhine (b. 1135)", "html": "1195 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_the_Rhine\" title=\"<PERSON>, Count <PERSON><PERSON> of the Rhine\"><PERSON>, Count <PERSON><PERSON> of the Rhine</a> (b. 1135)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_the_Rhine\" title=\"<PERSON>, Count <PERSON><PERSON> of the Rhine\"><PERSON>, Count <PERSON><PERSON> of the Rhine</a> (b. 1135)", "links": [{"title": "<PERSON>, Count <PERSON><PERSON> of the Rhine", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_the_Rhine"}]}, {"year": "1226", "text": "<PERSON>, king of France (b. 1187)", "html": "1226 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> VIII of France\"><PERSON></a>, king of France (b. 1187)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> VIII of France\"><PERSON></a>, king of France (b. 1187)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VIII_of_France"}]}, {"year": "1246", "text": "<PERSON><PERSON><PERSON><PERSON> of Castile (b. 1179)", "html": "1246 - <a href=\"https://wikipedia.org/wiki/Berengaria_of_Castile\" title=\"Berengaria of Castile\">Berengaria of Castile</a> (b. 1179)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Berengaria_of_Castile\" title=\"Berengaria of Castile\">Berengaria of Castile</a> (b. 1179)", "links": [{"title": "Berengaria of Castile", "link": "https://wikipedia.org/wiki/Berengaria_of_Castile"}]}, {"year": "1263", "text": "<PERSON> of Béthune, French countess", "html": "1263 - <a href=\"https://wikipedia.org/wiki/Matilda_of_B%C3%A9thune\" title=\"Matilda of Béthune\"><PERSON> of Béthune</a>, French countess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matilda_of_B%C3%A9thune\" title=\"Matilda of Béthune\"><PERSON> of Béthune</a>, French countess", "links": [{"title": "Matilda of Béthune", "link": "https://wikipedia.org/wiki/Matilda_of_B%C3%A9thune"}]}, {"year": "1308", "text": "<PERSON><PERSON>, Scottish priest, philosopher, and academic (b. 1266)", "html": "1308 - <a href=\"https://wikipedia.org/wiki/Duns_Scotus\" title=\"Duns Scotus\"><PERSON><PERSON></a>, Scottish priest, philosopher, and academic (b. 1266)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Duns_Scotus\" title=\"Duns Scotus\"><PERSON><PERSON></a>, Scottish priest, philosopher, and academic (b. 1266)", "links": [{"title": "Duns Scotus", "link": "https://wikipedia.org/wiki/Duns_Scotus"}]}, {"year": "1400", "text": "<PERSON> of Aragon, Aragonese infante (b. 1398)", "html": "1400 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(heir_of_Sicily)\" title=\"<PERSON> of Aragon (heir of Sicily)\"><PERSON> of Aragon</a>, Aragonese infante (b. 1398)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(heir_of_Sicily)\" title=\"<PERSON> of Aragon (heir of Sicily)\"><PERSON> of Aragon</a>, Aragonese infante (b. 1398)", "links": [{"title": "<PERSON> of Aragon (heir of Sicily)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(heir_of_Sicily)"}]}, {"year": "1478", "text": "<PERSON><PERSON>, emperor of Ethiopia (b. 1448)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>eda Maryam I\"><PERSON><PERSON> I</a>, emperor of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> (b. 1448)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>eda Maryam I\"><PERSON><PERSON> I</a>, emperor of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> (b. 1448)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_I"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "1494", "text": "<PERSON><PERSON><PERSON>, Italian painter (b. c. 1438)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Forl%C3%AC\" title=\"<PERSON><PERSON><PERSON> Forlì\"><PERSON><PERSON><PERSON>ì</a>, Italian painter (b. c. 1438)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Forl%C3%AC\" title=\"<PERSON><PERSON><PERSON> Forlì\"><PERSON><PERSON><PERSON>lì</a>, Italian painter (b. c. 1438)", "links": [{"title": "<PERSON><PERSON><PERSON>lì", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_da_Forl%C3%AC"}]}, {"year": "1517", "text": "<PERSON>, Spanish cardinal (b. 1436)", "html": "1517 - <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>%C3%A9nez_de_Cisneros\" title=\"<PERSON>neros\"><PERSON></a>, Spanish cardinal (b. 1436)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Jim%C3%A9nez_de_Cisneros\" title=\"<PERSON>neros\"><PERSON></a>, Spanish cardinal (b. 1436)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Jim%C3%A9nez_de_C<PERSON>neros"}]}, {"year": "1527", "text": "<PERSON>, German theologian and reformer (b. 1477)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and reformer (b. 1477)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and reformer (b. 1477)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1599", "text": "<PERSON>, Spanish composer (b. 1528)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Spanish composer (b. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Spanish composer (b. 1528)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1600", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (b. 1562)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1562)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1605", "text": "<PERSON>, English conspirator, leader of the Gunpowder Plot (b. 1573)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conspirator, leader of the <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a> (b. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conspirator, leader of the <a href=\"https://wikipedia.org/wiki/Gunpowder_Plot\" title=\"Gunpowder Plot\">Gunpowder Plot</a> (b. 1573)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gunpowder Plot", "link": "https://wikipedia.org/wiki/Gunpowder_Plot"}]}, {"year": "1606", "text": "<PERSON><PERSON><PERSON>, Italian philologist and physician (b. 1530)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/Girolamo_Mercuriale\" title=\"Girolamo Mercuriale\"><PERSON><PERSON><PERSON></a>, Italian philologist and physician (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Girolamo_Mercuriale\" title=\"Girolamo Mercuriale\"><PERSON><PERSON><PERSON></a>, Italian philologist and physician (b. 1530)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Girolamo_Mercuriale"}]}, {"year": "1658", "text": "<PERSON><PERSON>, Dutch admiral (b. 1599)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/Witte_de_With\" title=\"Witte de With\"><PERSON><PERSON> <PERSON></a>, Dutch admiral (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Witte_de_With\" title=\"Witte de With\"><PERSON><PERSON> <PERSON> With</a>, Dutch admiral (b. 1599)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Witte_de_With"}]}, {"year": "1674", "text": "<PERSON>, English poet and philosopher (b. 1608)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and philosopher (b. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and philosopher (b. 1608)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1719", "text": "<PERSON>, French mathematician and author (b. 1652)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and author (b. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and author (b. 1652)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON>, Prussian general (b. 1721)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian general (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Se<PERSON>litz\"><PERSON></a>, Prussian general (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, Italian painter and educator (b. 1754)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and educator (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and educator (b. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, English engraver, illustrator and author (b.1753)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engraver, illustrator and author (b.1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engraver, illustrator and author (b.1753)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON> of the Two Sicilies (b. 1777)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies\" title=\"<PERSON> I of the Two Sicilies\"><PERSON> of the Two Sicilies</a> (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies\" title=\"<PERSON> I of the Two Sicilies\"><PERSON> of the Two Sicilies</a> (b. 1777)", "links": [{"title": "<PERSON> of the Two Sicilies", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies"}]}, {"year": "1873", "text": "<PERSON> Herreros, Spanish poet, playwright, and critic (b. 1796)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_de_los_Herreros\" title=\"Manuel Bretón de los Herreros\"><PERSON> los Herreros</a>, Spanish poet, playwright, and critic (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_de_los_Herreros\" title=\"Manuel Bretón de los Herreros\"><PERSON> los Herreros</a>, Spanish poet, playwright, and critic (b. 1796)", "links": [{"title": "<PERSON> de los Herreros", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ret%C3%B3n_de_los_Herreros"}]}, {"year": "1887", "text": "<PERSON>, American dentist and poker player (b. 1851)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and poker player (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and poker player (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ho<PERSON>day"}]}, {"year": "1890", "text": "<PERSON>, Belgian organist and composer (b. 1822)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian organist and composer (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian organist and composer (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>ck"}]}, {"year": "1895", "text": "<PERSON>, American surgeon and academic (b. 1828)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and academic (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and academic (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Irish-Australian politician, 16th Premier of Tasmania (b. 1815)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1905", "text": "<PERSON>, Russian painter (b. 1870)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English cricketer and soldier (b. 1879)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Slovak poet and playwright (b. 1849)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Pavol_Orsz%C3%A1gh_Hviezdoslav\" title=\"Pavol Országh Hviezdoslav\"><PERSON><PERSON><PERSON> Orszá<PERSON></a>, Slovak poet and playwright (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pavol_Orsz%C3%A1gh_Hviezdoslav\" title=\"Pavol Országh Hviezdoslav\"><PERSON><PERSON><PERSON> Orsz<PERSON></a>, Slovak poet and playwright (b. 1849)", "links": [{"title": "Pavol Országh Hviezdoslav", "link": "https://wikipedia.org/wiki/Pavol_Orsz%C3%A1gh_Hviezdoslav"}]}, {"year": "1934", "text": "<PERSON>, Brazilian physician and bacteriologist (b. 1879)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian physician and bacteriologist (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian physician and bacteriologist (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Austrian-German soldier and pilot (b. 1920)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German soldier and pilot (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German soldier and pilot (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German field marshal (b. 1849)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German field marshal (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German field marshal (b. 1849)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Belgian-Austrian priest and activist (b. 1874)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>eve\" title=\"<PERSON><PERSON>eve\"><PERSON><PERSON></a>, Belgian-Austrian priest and activist (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>eve\"><PERSON><PERSON></a>, Belgian-Austrian priest and activist (b. 1874)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>eve"}]}, {"year": "1953", "text": "<PERSON>, Russian author and poet, Nobel Prize laureate (b. 1870)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1953", "text": "<PERSON>, Dutch-South African author and educator (b. 1887)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-South African author and educator (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-South African author and educator (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Japanese chemist (b. 1884)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese chemist (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese chemist (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1959", "text": "<PERSON>, American activist, founded the DeMolay International (b. 1890)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Frank_<PERSON>._Land\" title=\"Frank S. Land\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/DeMolay_International\" title=\"DeMolay International\">DeMolay International</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frank_S._Land\" title=\"Frank S. Land\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/DeMolay_International\" title=\"DeMolay International\">DeMolay International</a> (b. 1890)", "links": [{"title": "Frank <PERSON>", "link": "https://wikipedia.org/wiki/Frank_S._Land"}, {"title": "DeMolay International", "link": "https://wikipedia.org/wiki/DeMolay_International"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Indian soldier; Chief of the Air Staff of the Indian Air Force (b. 1911)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>rot<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian soldier; Chief of the Air Staff of the Indian Air Force (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian soldier; Chief of the Air Staff of the Indian Air Force (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Subrot<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American journalist, television personality, and game show panelist (b. 1913)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, television personality, and game show panelist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, television personality, and game show panelist (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor and politician (b. 1914)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and politician (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Faroese educator and politician, 3rd Prime Minister of the Faroe Islands (b. 1898)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Faroese educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Faroese educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Welsh poet and politician (b. 1892)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Welsh poet and politician (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Welsh poet and politician (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Turkish poet, author, and politician (b. 1898)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Faruk_Nafiz_%C3%87aml%C4%B1bel\" title=\"<PERSON>uk Nafiz <PERSON>lıbel\"><PERSON><PERSON></a>, Turkish poet, author, and politician (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Faruk_Nafiz_%C3%87aml%C4%B1bel\" title=\"<PERSON>uk Nafiz Ç<PERSON>lıbel\"><PERSON><PERSON></a>, Turkish poet, author, and politician (b. 1898)", "links": [{"title": "Faruk Nafiz <PERSON>", "link": "https://wikipedia.org/wiki/Faruk_Nafiz_%C3%87aml%C4%B1bel"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter and pianist (b. 1914)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ivory_Joe_<PERSON>\" title=\"Ivory Joe Hunter\"><PERSON></a>, American singer-songwriter and pianist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ivory_Joe_Hunter\" title=\"Ivory Joe Hunter\"><PERSON></a>, American singer-songwriter and pianist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Greek actor and producer (b. 1931)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor and producer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor and producer (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American baseball player and manager (b. 1896)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American painter and illustrator (b. 1894)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American singer and pianist (b. 1939)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian-American rabbi and educator (b. 1881)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian-American rabbi and educator (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian-American rabbi and educator (b. 1881)", "links": [{"title": "Mordecai Kaplan", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Kaplan"}]}, {"year": "1985", "text": "<PERSON>, Luxembourger cyclist (b. 1899)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger cyclist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger cyclist (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Ukrainian-American painter and illustrator (b. 1915)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American painter and illustrator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American painter and illustrator (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian politician and diplomat, Soviet Minister of Foreign Affairs (b. 1890)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Soviet_Union)\" title=\"Ministry of Foreign Affairs (Soviet Union)\">Soviet Minister of Foreign Affairs</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Soviet_Union)\" title=\"Ministry of Foreign Affairs (Soviet Union)\">Soviet Minister of Foreign Affairs</a> (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs (Soviet Union)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Soviet_Union)"}]}, {"year": "1994", "text": "<PERSON>, American actor and screenwriter (b. 1940)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donoghue\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donoghue\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donoghue"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, English author and poet (b. 1907)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Rumer_<PERSON><PERSON>\" title=\"<PERSON>ume<PERSON>\"><PERSON><PERSON><PERSON></a>, English author and poet (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rume<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>ume<PERSON>\"><PERSON><PERSON><PERSON></a>, English author and poet (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rumer_<PERSON>den"}]}, {"year": "1998", "text": "<PERSON>, Baron <PERSON>, English colonel, mountaineer, and academic (b. 1910)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English colonel, mountaineer, and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English colonel, mountaineer, and academic (b. 1910)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, French actor and director (b. 1913)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American trumpet player and composer (b. 1941)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Slovenian gymnast and judge (b. 1898)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0tukelj\" title=\"<PERSON>\"><PERSON></a>, Slovenian gymnast and judge (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0tukelj\" title=\"<PERSON>\"><PERSON></a>, Slovenian gymnast and judge (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leon_%C5%A0tukelj"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek santouri player and educator (b. 1930)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek <a href=\"https://wikipedia.org/wiki/Santouri\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\">sa<PERSON><PERSON></a> player and educator (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek <a href=\"https://wikipedia.org/wiki/Santouri\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\">santouri</a> player and educator (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Santouri"}]}, {"year": "2002", "text": "<PERSON>, Pakistani poet, philosopher, and scholar (b. 1931)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani poet, philosopher, and scholar (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani poet, philosopher, and scholar (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English actor and screenwriter (b. 1932)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and screenwriter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and screenwriter (b. 1932)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress, fashion designer, and author (b. 1920)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Guest\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> Guest\"><PERSON><PERSON><PERSON><PERSON></a>, American actress, fashion designer, and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Guest\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress, fashion designer, and author (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Guest"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter (b. 1956)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English-Australian author and playwright (b. 1931)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian author and playwright (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian author and playwright (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Greek actor and director (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor and director (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor and director (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American soldier and author (b. 1917)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American composer and conductor (b. 1945)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Austrian physicist and academic (b. 1941)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Hanns<PERSON>er_Winter\" title=\"Hannspeter Winter\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian physicist and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Han<PERSON><PERSON>er_Winter\" title=\"Hannspeter Winter\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian physicist and academic (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Winter", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Winter"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Dutch journalist, poet, and politician (b. 1933)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist, poet, and politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist, poet, and politician (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>uis"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Filipino politician, 10th Filipino Secretary of Social Welfare and Development (b. 1943)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino politician, 10th <a href=\"https://wikipedia.org/wiki/Secretary_of_Social_Welfare_and_Development_(Philippines)\" class=\"mw-redirect\" title=\"Secretary of Social Welfare and Development (Philippines)\">Filipino Secretary of Social Welfare and Development</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino politician, 10th <a href=\"https://wikipedia.org/wiki/Secretary_of_Social_Welfare_and_Development_(Philippines)\" class=\"mw-redirect\" title=\"Secretary of Social Welfare and Development (Philippines)\">Filipino Secretary of Social Welfare and Development</a> (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gu<PERSON>g"}, {"title": "Secretary of Social Welfare and Development (Philippines)", "link": "https://wikipedia.org/wiki/Secretary_of_Social_Welfare_and_Development_(Philippines)"}]}, {"year": "2007", "text": "<PERSON>, English priest, founded The Samaritans (b. 1911)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Chad_Varah\" title=\"<PERSON>\"><PERSON></a>, English priest, founded <a href=\"https://wikipedia.org/wiki/Samaritans_(charity)\" title=\"Samaritans (charity)\">The Samaritans</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chad_Varah\" title=\"<PERSON>\"><PERSON></a>, English priest, founded <a href=\"https://wikipedia.org/wiki/Samaritans_(charity)\" title=\"Samaritans (charity)\">The Samaritans</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chad_Varah"}, {"title": "Samaritans (charity)", "link": "https://wikipedia.org/wiki/Samaritans_(charity)"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Russian physicist and astrophysicist, Nobel Prize laureate (b. 1916)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ly <PERSON>\"><PERSON><PERSON></a>, Russian physicist and astrophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and astrophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitaly_<PERSON>inzburg"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, American basketball player (b. 1961)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>uin<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>uin<PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uin<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>uin<PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Quintin_<PERSON><PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American soldier and painter (b. 1915)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Argentinian admiral (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian admiral (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian admiral (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON> <PERSON>, Jamaican-American rapper, producer, and actor (b. 1967)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Heavy_D\" title=\"Heavy D\">Heavy D</a>, Jamaican-American rapper, producer, and actor (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Heavy_D\" title=\"Heavy D\">Heavy D</a>, Jamaican-American rapper, producer, and actor (b. 1967)", "links": [{"title": "Heavy D", "link": "https://wikipedia.org/wiki/Heavy_D"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American cartoonist (b. 1922)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/B<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, American cartoonist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, American cartoonist (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B<PERSON>_<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American businessman (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, German composer and producer (b. 1960)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and producer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and producer (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American author (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American physicist, mathematician, and academic (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist, mathematician, and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist, mathematician, and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American journalist and academic (b. 1915)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Penn_Kimball\" title=\"Penn Kimball\"><PERSON></a>, American journalist and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Penn_Kimball\" title=\"Penn Kimball\"><PERSON></a>, American journalist and academic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Penn_Kimball"}]}, {"year": "2013", "text": "<PERSON>, American composer (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Japanese singer and actress (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Indian journalist and actor (b. 1957)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Amanchi_Venkata_Subrahmanyam\" class=\"mw-redirect\" title=\"Amanchi Venkata Subrahmanyam\"><PERSON><PERSON><PERSON> Venkata Subrahmanyam</a>, Indian journalist and actor (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amanchi_Venkata_Subrahmanyam\" class=\"mw-redirect\" title=\"Amanchi Venkata Subrahmanyam\"><PERSON>anchi Venkata Subrahmanyam</a>, Indian journalist and actor (b. 1957)", "links": [{"title": "Amanchi Venkata Subrahmanyam", "link": "https://wikipedia.org/wiki/Amanchi_Venkata_Subrahmanyam"}]}, {"year": "2014", "text": "<PERSON>, American academic and politician (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Italian soldier and pilot (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier and pilot (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier and pilot (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football player and sportscaster (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linebacker)\" title=\"<PERSON> (linebacker)\"><PERSON></a>, American football player and sportscaster (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linebacker)\" title=\"<PERSON> (linebacker)\"><PERSON></a>, American football player and sportscaster (b. 1925)", "links": [{"title": "<PERSON> (linebacker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(linebacker)"}]}, {"year": "2014", "text": "<PERSON>, Spanish-Mexican footballer and sportscaster (b. 1984)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1nchez_Portugal\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish-Mexican footballer and sportscaster (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1nchez_Portugal\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish-Mexican footballer and sportscaster (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hugo_S%C3%A1nchez_Portugal"}]}, {"year": "2014", "text": "<PERSON>, Canadian-American basketball player and physician (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American basketball player and physician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American basketball player and physician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American philanthropist, founded the Polk Museum of Art (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/R<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Polk_Museum_of_Art\" class=\"mw-redirect\" title=\"Polk Museum of Art\">Polk Museum of Art</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Polk_Museum_of_Art\" class=\"mw-redirect\" title=\"Polk Museum of Art\">Polk Museum of Art</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rhea_<PERSON>s"}, {"title": "Polk Museum of Art", "link": "https://wikipedia.org/wiki/Polk_Museum_of_Art"}]}, {"year": "2015", "text": "<PERSON>, American ice hockey player and actor (b. 1984)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joseph Cure\"><PERSON></a>, American ice hockey player and actor (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joseph Cure\"><PERSON></a>, American ice hockey player and actor (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian-English astronomer and academic (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English astronomer and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English astronomer and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian air marshal and politician (b. 1919)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Indian air marshal and politician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Indian air marshal and politician (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Sri Lankan monk and activist (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Maduluwawe_Sobitha_Thero\" title=\"Maduluwawe Sobitha Thero\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan monk and activist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maduluwawe_Sobitha_Thero\" title=\"Maduluwawe Sobitha Thero\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan monk and activist (b. 1942)", "links": [{"title": "Maduluwawe Sobitha <PERSON>ro", "link": "https://wikipedia.org/wiki/Maduluwawe_Sobitha_Thero"}]}, {"year": "2020", "text": "<PERSON>, Canadian-American television personality and longtime host of Jeopardy! (b. 1940)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American television personality and longtime host of <i><a href=\"https://wikipedia.org/wiki/Jeopardy!\" title=\"Je<PERSON>ard<PERSON>!\">Jeopard<PERSON>!</a></i> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American television personality and longtime host of <i><a href=\"https://wikipedia.org/wiki/Jeopardy!\" title=\"Je<PERSON>ard<PERSON>!\">Jeopard<PERSON>!</a></i> (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jeopardy!", "link": "https://wikipedia.org/wiki/Jeopardy!"}]}, {"year": "2024", "text": "<PERSON>, American novelist (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, English actress (b. 1919)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/June_<PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, English actress (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, English actress (b. 1919)", "links": [{"title": "June <PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Scottish hairdresser (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish hairdresser (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish hairdresser (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}