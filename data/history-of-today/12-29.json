{"date": "December 29", "url": "https://wikipedia.org/wiki/December_29", "data": {"Events": [{"year": "1170", "text": "<PERSON>, Archbishop of Canterbury, is assassinated inside Canterbury Cathedral by followers of King <PERSON>; he subsequently becomes a saint and martyr in the Anglican Communion and the Catholic Church.", "html": "1170 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>, is assassinated inside <a href=\"https://wikipedia.org/wiki/Canterbury_Cathedral\" title=\"Canterbury Cathedral\">Canterbury Cathedral</a> by followers of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\">King <PERSON> II</a>; he subsequently becomes a saint and martyr in the <a href=\"https://wikipedia.org/wiki/Anglican_Communion\" title=\"Anglican Communion\">Anglican Communion</a> and the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>, is assassinated inside <a href=\"https://wikipedia.org/wiki/Canterbury_Cathedral\" title=\"Canterbury Cathedral\">Canterbury Cathedral</a> by followers of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\">King <PERSON> II</a>; he subsequently becomes a saint and martyr in the <a href=\"https://wikipedia.org/wiki/Anglican_Communion\" title=\"Anglican Communion\">Anglican Communion</a> and the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}, {"title": "Canterbury Cathedral", "link": "https://wikipedia.org/wiki/Canterbury_Cathedral"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_England"}, {"title": "Anglican Communion", "link": "https://wikipedia.org/wiki/Anglican_Communion"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "1503", "text": "The Battle of Garigliano was fought between a Spanish army under <PERSON><PERSON><PERSON> and a French army commanded by <PERSON><PERSON><PERSON><PERSON>, Marquess of Saluzzo.", "html": "1503 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Garigliano_(1503)\" title=\"Battle of Garigliano (1503)\">Battle of Garigliano</a> was fought between a Spanish army under <a href=\"https://wikipedia.org/wiki/Gonzalo_Fern%C3%A1ndez_de_C%C3%B3rdoba\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and a French army commanded by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_Marquess_of_Saluzzo\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Marquess of Saluzzo\"><PERSON><PERSON><PERSON><PERSON>, Marquess of Saluzzo</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Garigliano_(1503)\" title=\"Battle of Garigliano (1503)\">Battle of Garigliano</a> was fought between a Spanish army under <a href=\"https://wikipedia.org/wiki/Go<PERSON>lo_Fern%C3%A1ndez_de_C%C3%B3rdoba\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and a French army commanded by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_Marquess_of_Saluzzo\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>, Marquess of Saluzzo\"><PERSON><PERSON><PERSON><PERSON> <PERSON>, Marquess of Saluzzo</a>.", "links": [{"title": "Battle of Garigliano (1503)", "link": "https://wikipedia.org/wiki/Battle_of_Garigliano_(1503)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gonzalo_Fern%C3%A1ndez_de_C%C3%B3rdoba"}, {"title": "<PERSON><PERSON><PERSON><PERSON>, Marquess of Saluzzo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_Marquess_of_Saluzzo"}]}, {"year": "1607", "text": "According to <PERSON>, <PERSON><PERSON><PERSON><PERSON>, daughter of Powhatan leader <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, successfully pleads for his life after tribal leaders attempt to execute him.", "html": "1607 - According to <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Po<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/Powhatan\" title=\"Powhatan\">Pow<PERSON>an</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>hat<PERSON>_(Native_American_leader)\" title=\"<PERSON><PERSON><PERSON><PERSON> (Native American leader)\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, successfully pleads for his life after tribal leaders attempt to execute him.", "no_year_html": "According to <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Po<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/Powhatan\" title=\"Powhatan\"><PERSON><PERSON><PERSON><PERSON></a> leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>hat<PERSON>_(Native_American_leader)\" title=\"<PERSON><PERSON><PERSON><PERSON> (Native American leader)\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, successfully pleads for his life after tribal leaders attempt to execute him.", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>(explorer)"}, {"title": "Pocahontas", "link": "https://wikipedia.org/wiki/Pocahontas"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>whatan"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (Native American leader)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(Native_American_leader)"}]}, {"year": "1778", "text": "American Revolutionary War: British forces under the command of Lieutenant Colonel <PERSON> defeat American forces under Major General <PERSON> and capture the port city of Savannah, Georgia.", "html": "1778 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British forces under the command of <a href=\"https://wikipedia.org/wiki/Lieutenant_colonel_(United_Kingdom)\" title=\"Lieutenant colonel (United Kingdom)\">Lieutenant Colonel</a> <a href=\"https://wikipedia.org/wiki/<PERSON>(British_Army_officer,_born_1739)\" title=\"<PERSON> (British Army officer, born 1739)\"><PERSON></a> defeat American forces under <a href=\"https://wikipedia.org/wiki/Major_general_(United_States)\" title=\"Major general (United States)\">Major General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_(Continental_Army_officer)\" title=\"<PERSON> (Continental Army officer)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Capture_of_Savannah\" title=\"Capture of Savannah\">capture</a> the port city of <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British forces under the command of <a href=\"https://wikipedia.org/wiki/Lieutenant_colonel_(United_Kingdom)\" title=\"Lieutenant colonel (United Kingdom)\">Lieutenant Colonel</a> <a href=\"https://wikipedia.org/wiki/<PERSON>(British_Army_officer,_born_1739)\" title=\"<PERSON> (British Army officer, born 1739)\"><PERSON></a> defeat American forces under <a href=\"https://wikipedia.org/wiki/Major_general_(United_States)\" title=\"Major general (United States)\">Major General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_(Continental_Army_officer)\" title=\"<PERSON> (Continental Army officer)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Capture_of_Savannah\" title=\"Capture of Savannah\">capture</a> the port city of <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Lieutenant colonel (United Kingdom)", "link": "https://wikipedia.org/wiki/Lieutenant_colonel_(United_Kingdom)"}, {"title": "<PERSON> (British Army officer, born 1739)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1739)"}, {"title": "Major general (United States)", "link": "https://wikipedia.org/wiki/Major_general_(United_States)"}, {"title": "<PERSON> (Continental Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Continental_Army_officer)"}, {"title": "Capture of Savannah", "link": "https://wikipedia.org/wiki/Capture_of_Savannah"}, {"title": "Savannah, Georgia", "link": "https://wikipedia.org/wiki/Savannah,_Georgia"}]}, {"year": "1812", "text": "USS Constitution, under the command of Captain <PERSON>, captures HMS Java off the coast of Brazil after a three-hour battle.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/USS_Constitution\" title=\"USS Constitution\">USS <i>Constitution</i></a>, under the command of <a href=\"https://wikipedia.org/wiki/Captain_(United_States_O-6)\" title=\"Captain (United States O-6)\">Captain</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, captures <a href=\"https://wikipedia.org/wiki/HMS_Java_(1811)\" title=\"HMS Java (1811)\">HMS <i>Java</i></a> off the coast of Brazil after a three-hour battle.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USS_Constitution\" title=\"USS Constitution\">USS <i>Constitution</i></a>, under the command of <a href=\"https://wikipedia.org/wiki/Captain_(United_States_O-6)\" title=\"Captain (United States O-6)\">Captain</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, captures <a href=\"https://wikipedia.org/wiki/HMS_Java_(1811)\" title=\"HMS Java (1811)\">HMS <i>Java</i></a> off the coast of Brazil after a three-hour battle.", "links": [{"title": "USS Constitution", "link": "https://wikipedia.org/wiki/USS_Constitution"}, {"title": "Captain (United States O-6)", "link": "https://wikipedia.org/wiki/Captain_(United_States_O-6)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "HMS Java (1811)", "link": "https://wikipedia.org/wiki/HMS_Java_(1811)"}]}, {"year": "1835", "text": "The Treaty of New Echota is signed, ceding all the lands of the Cherokee east of the Mississippi River to the United States.", "html": "1835 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_New_Echota\" title=\"Treaty of New Echota\">Treaty of New Echota</a> is signed, ceding all the lands of the <a href=\"https://wikipedia.org/wiki/Cherokee\" title=\"Cherokee\">Cherokee</a> east of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> to the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_New_Echota\" title=\"Treaty of New Echota\">Treaty of New Echota</a> is signed, ceding all the lands of the <a href=\"https://wikipedia.org/wiki/Cherokee\" title=\"Cherokee\">Cherokee</a> east of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> to the United States.", "links": [{"title": "Treaty of New Echota", "link": "https://wikipedia.org/wiki/Treaty_of_New_Echota"}, {"title": "Cherokee", "link": "https://wikipedia.org/wiki/Cherokee"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}]}, {"year": "1845", "text": "The United States annexes the Republic of Texas and admits it as the 28th state.", "html": "1845 - The United States <a href=\"https://wikipedia.org/wiki/Texas_annexation\" title=\"Texas annexation\">annexes</a> the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a> and admits it as the 28th state.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Texas_annexation\" title=\"Texas annexation\">annexes</a> the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a> and admits it as the 28th state.", "links": [{"title": "Texas annexation", "link": "https://wikipedia.org/wiki/Texas_annexation"}, {"title": "Republic of Texas", "link": "https://wikipedia.org/wiki/Republic_of_Texas"}]}, {"year": "1860", "text": "The launch of HMS Warrior, with her combination of screw propeller, iron hull and iron armour, renders all previous warships obsolete.", "html": "1860 - The launch of <a href=\"https://wikipedia.org/wiki/HMS_Warrior_(1860)\" title=\"HMS Warrior (1860)\">HMS <i>Warrior</i></a>, with her combination of screw <a href=\"https://wikipedia.org/wiki/Propeller\" title=\"Propeller\">propeller</a>, iron hull and <a href=\"https://wikipedia.org/wiki/Ironclad_warship\" title=\"Ironclad warship\">iron armour</a>, renders all previous warships obsolete.", "no_year_html": "The launch of <a href=\"https://wikipedia.org/wiki/HMS_Warrior_(1860)\" title=\"HMS Warrior (1860)\">HMS <i>Warrior</i></a>, with her combination of screw <a href=\"https://wikipedia.org/wiki/Propeller\" title=\"Propeller\">propeller</a>, iron hull and <a href=\"https://wikipedia.org/wiki/Ironclad_warship\" title=\"Ironclad warship\">iron armour</a>, renders all previous warships obsolete.", "links": [{"title": "HMS Warrior (1860)", "link": "https://wikipedia.org/wiki/HMS_Warrior_(1860)"}, {"title": "Propeller", "link": "https://wikipedia.org/wiki/Propeller"}, {"title": "Ironclad warship", "link": "https://wikipedia.org/wiki/Ironclad_warship"}]}, {"year": "1862", "text": "American Civil War: The Battle of Chickasaw Bayou ends in a Union defeat as forces under General <PERSON> are repulsed with heavy losses by Confederate troops under General <PERSON>.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Chickasaw_Bayou\" title=\"Battle of Chickasaw Bayou\">Battle of Chickasaw Bayou</a> ends in a <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> defeat as forces under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> are repulsed with heavy losses by <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>n\" title=\"John <PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Chickasaw_Bayou\" title=\"Battle of Chickasaw Bayou\">Battle of Chickasaw Bayou</a> ends in a <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> defeat as forces under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> are repulsed with heavy losses by <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>berton\" title=\"John <PERSON>\"><PERSON></a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Chickasaw Bayou", "link": "https://wikipedia.org/wiki/Battle_of_Chickasaw_Bayou"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "The military coup of <PERSON><PERSON> in Sagunto ends the failed First Spanish Republic and the monarchy is restored as Prince <PERSON> is proclaimed King of Spain.", "html": "1874 - The military coup of <a href=\"https://wikipedia.org/wiki/Arsenio_Mart%C3%ADnez-Campos_y_Ant%C3%B3n\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> y Ant<PERSON>\">Gen<PERSON></a> in <a href=\"https://wikipedia.org/wiki/Sagunto\" title=\"Sagunto\">Sagunto</a> ends the failed <a href=\"https://wikipedia.org/wiki/First_Spanish_Republic\" title=\"First Spanish Republic\">First Spanish Republic</a> and the <a href=\"https://wikipedia.org/wiki/Restoration_(Spain)\" title=\"Restoration (Spain)\">monarchy is restored</a> as <a href=\"https://wikipedia.org/wiki/Alfonso_XII_of_Spain\" class=\"mw-redirect\" title=\"Alfonso <PERSON> of Spain\">Prince <PERSON></a> is proclaimed King of <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>.", "no_year_html": "The military coup of <a href=\"https://wikipedia.org/wiki/Arsenio_Mart%C3%ADnez-Campos_y_Ant%C3%B3n\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> y Ant<PERSON>\">Gen<PERSON></a> in <a href=\"https://wikipedia.org/wiki/Sagunto\" title=\"Sagunto\">Sagunto</a> ends the failed <a href=\"https://wikipedia.org/wiki/First_Spanish_Republic\" title=\"First Spanish Republic\">First Spanish Republic</a> and the <a href=\"https://wikipedia.org/wiki/Restoration_(Spain)\" title=\"Restoration (Spain)\">monarchy is restored</a> as <a href=\"https://wikipedia.org/wiki/Alfonso_XII_of_Spain\" class=\"mw-redirect\" title=\"Alfonso <PERSON> of Spain\">Prince <PERSON></a> is proclaimed King of <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>-Campos y Antón", "link": "https://wikipedia.org/wiki/Arsenio_Mart%C3%ADnez-Campos_y_Ant%C3%B3n"}, {"title": "Sagunto", "link": "https://wikipedia.org/wiki/Sagunto"}, {"title": "First Spanish Republic", "link": "https://wikipedia.org/wiki/First_Spanish_Republic"}, {"title": "Restoration (Spain)", "link": "https://wikipedia.org/wiki/Restoration_(Spain)"}, {"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Alfonso_XII_of_Spain"}, {"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}]}, {"year": "1876", "text": "The Ashtabula River railroad disaster occurs, leaving 64 injured and 92 dead at Ashtabula, Ohio.", "html": "1876 - The <a href=\"https://wikipedia.org/wiki/Ashtabula_River_railroad_disaster\" title=\"Ashtabula River railroad disaster\">Ashtabula River railroad disaster</a> occurs, leaving 64 injured and 92 dead at <a href=\"https://wikipedia.org/wiki/Ashtabula,_Ohio\" title=\"Ashtabula, Ohio\">Ashtabula, Ohio</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ashtabula_River_railroad_disaster\" title=\"Ashtabula River railroad disaster\">Ashtabula River railroad disaster</a> occurs, leaving 64 injured and 92 dead at <a href=\"https://wikipedia.org/wiki/Ashtabula,_Ohio\" title=\"Ashtabula, Ohio\">Ashtabula, Ohio</a>.", "links": [{"title": "Ashtabula River railroad disaster", "link": "https://wikipedia.org/wiki/Ashtabula_River_railroad_disaster"}, {"title": "Ashtabula, Ohio", "link": "https://wikipedia.org/wiki/Ashtabula,_Ohio"}]}, {"year": "1890", "text": "Wounded Knee Massacre: On Pine Ridge Indian Reservation, 300 Lakota are killed by the United States 7th Cavalry Regiment.", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Wounded_Knee_Massacre\" title=\"Wounded Knee Massacre\">Wounded Knee Massacre</a>: On <a href=\"https://wikipedia.org/wiki/Pine_Ridge_Indian_Reservation\" title=\"Pine Ridge Indian Reservation\">Pine Ridge Indian Reservation</a>, 300 <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> are killed by the United States <a href=\"https://wikipedia.org/wiki/7th_Cavalry_Regiment\" title=\"7th Cavalry Regiment\">7th Cavalry Regiment</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wounded_Knee_Massacre\" title=\"Wounded Knee Massacre\">Wounded Knee Massacre</a>: On <a href=\"https://wikipedia.org/wiki/Pine_Ridge_Indian_Reservation\" title=\"Pine Ridge Indian Reservation\">Pine Ridge Indian Reservation</a>, 300 <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> are killed by the United States <a href=\"https://wikipedia.org/wiki/7th_Cavalry_Regiment\" title=\"7th Cavalry Regiment\">7th Cavalry Regiment</a>.", "links": [{"title": "Wounded Knee Massacre", "link": "https://wikipedia.org/wiki/Wounded_Knee_Massacre"}, {"title": "Pine Ridge Indian Reservation", "link": "https://wikipedia.org/wiki/Pine_Ridge_Indian_Reservation"}, {"title": "Lakota people", "link": "https://wikipedia.org/wiki/Lakota_people"}, {"title": "7th Cavalry Regiment", "link": "https://wikipedia.org/wiki/7th_Cavalry_Regiment"}]}, {"year": "1911", "text": "Mongolia gains independence from the Qing dynasty, enthroning 8th <PERSON><PERSON><PERSON><PERSON><PERSON> as K<PERSON>gan of Mongolia.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a> gains independence from the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>, enthroning <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\">8th Jebtsundamba Khutughtu</a> as <a href=\"https://wikipedia.org/wiki/Khagan_of_Mongolia\" class=\"mw-redirect\" title=\"Khagan of Mongolia\">Khagan of Mongolia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a> gains independence from the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>, enthroning <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\">8th Jebtsundamba Khutughtu</a> as <a href=\"https://wikipedia.org/wiki/Khagan_of_Mongolia\" class=\"mw-redirect\" title=\"Khagan of Mongolia\">Khagan of Mongolia</a>.", "links": [{"title": "Mongolia", "link": "https://wikipedia.org/wiki/Mongolia"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bog<PERSON>_<PERSON>"}, {"title": "Khagan of Mongolia", "link": "https://wikipedia.org/wiki/Khagan_of_Mongolia"}]}, {"year": "1913", "text": "<PERSON> starts filming Hollywood's first feature film, The Squaw Man.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> starts filming <a href=\"https://wikipedia.org/wiki/Hollywood,_Los_Angeles\" title=\"Hollywood, Los Angeles\">Hollywood's</a> first feature film, <i><a href=\"https://wikipedia.org/wiki/The_Squaw_Man_(1914_film)\" title=\"The Squaw Man (1914 film)\">The Squaw Man</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> De<PERSON>\"><PERSON></a> starts filming <a href=\"https://wikipedia.org/wiki/Hollywood,_Los_Angeles\" title=\"Hollywood, Los Angeles\">Hollywood's</a> first feature film, <i><a href=\"https://wikipedia.org/wiki/The_Squaw_Man_(1914_film)\" title=\"The Squaw Man (1914 film)\">The Squaw Man</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Hollywood, Los Angeles", "link": "https://wikipedia.org/wiki/Hollywood,_Los_Angeles"}, {"title": "The Squaw Man (1914 film)", "link": "https://wikipedia.org/wiki/The_Squaw_Man_(1914_film)"}]}, {"year": "1930", "text": "Sir <PERSON>'s presidential address in Allahabad introduces the two-nation theory and outlines a vision for the creation of Pakistan.", "html": "1930 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s presidential address in <a href=\"https://wikipedia.org/wiki/Allahabad\" class=\"mw-redirect\" title=\"Allahabad\">Allahabad</a> introduces the <a href=\"https://wikipedia.org/wiki/Two-nation_theory\" title=\"Two-nation theory\">two-nation theory</a> and outlines a vision for the creation of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s presidential address in <a href=\"https://wikipedia.org/wiki/Allahabad\" class=\"mw-redirect\" title=\"Allahabad\">Allahabad</a> introduces the <a href=\"https://wikipedia.org/wiki/Two-nation_theory\" title=\"Two-nation theory\">two-nation theory</a> and outlines a vision for the creation of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Allahabad", "link": "https://wikipedia.org/wiki/Allahabad"}, {"title": "Two-nation theory", "link": "https://wikipedia.org/wiki/Two-nation_theory"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "1934", "text": "Japan renounces the Washington Naval Treaty of 1922 and the London Naval Treaty of 1930.", "html": "1934 - Japan renounces the <a href=\"https://wikipedia.org/wiki/Washington_Naval_Treaty\" title=\"Washington Naval Treaty\">Washington Naval Treaty</a> of 1922 and the <a href=\"https://wikipedia.org/wiki/London_Naval_Treaty\" title=\"London Naval Treaty\">London Naval Treaty</a> of 1930.", "no_year_html": "Japan renounces the <a href=\"https://wikipedia.org/wiki/Washington_Naval_Treaty\" title=\"Washington Naval Treaty\">Washington Naval Treaty</a> of 1922 and the <a href=\"https://wikipedia.org/wiki/London_Naval_Treaty\" title=\"London Naval Treaty\">London Naval Treaty</a> of 1930.", "links": [{"title": "Washington Naval Treaty", "link": "https://wikipedia.org/wiki/Washington_Naval_Treaty"}, {"title": "London Naval Treaty", "link": "https://wikipedia.org/wiki/London_Naval_Treaty"}]}, {"year": "1937", "text": "The Irish Free State is replaced by a new state called Ireland with the adoption of a new constitution.", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> is replaced by a new state called <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Ireland</a> with the adoption of a new <a href=\"https://wikipedia.org/wiki/Constitution_of_Ireland\" title=\"Constitution of Ireland\">constitution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> is replaced by a new state called <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Ireland</a> with the adoption of a new <a href=\"https://wikipedia.org/wiki/Constitution_of_Ireland\" title=\"Constitution of Ireland\">constitution</a>.", "links": [{"title": "Irish Free State", "link": "https://wikipedia.org/wiki/Irish_Free_State"}, {"title": "Republic of Ireland", "link": "https://wikipedia.org/wiki/Republic_of_Ireland"}, {"title": "Constitution of Ireland", "link": "https://wikipedia.org/wiki/Constitution_of_Ireland"}]}, {"year": "1940", "text": "In the Second Great Fire of London, the Luftwaffe fire-bombs London, England, killing almost 200 civilians during World War II.", "html": "1940 - In the <a href=\"https://wikipedia.org/wiki/Second_Great_Fire_of_London\" title=\"Second Great Fire of London\">Second Great Fire of London</a>, the <a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a> fire-bombs <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, England, killing almost 200 civilians during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Second_Great_Fire_of_London\" title=\"Second Great Fire of London\">Second Great Fire of London</a>, the <a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a> fire-bombs <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, England, killing almost 200 civilians during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "links": [{"title": "Second Great Fire of London", "link": "https://wikipedia.org/wiki/Second_Great_Fire_of_London"}, {"title": "Luftwaffe", "link": "https://wikipedia.org/wiki/Luftwaffe"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1972", "text": "Eastern Air Lines Flight 401 (a Lockheed L-1011 TriStar) crashes in the Florida Everglades on approach to Miami International Airport, Florida, killing 101 of the 176 people on board.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_401\" title=\"Eastern Air Lines Flight 401\">Eastern Air Lines Flight 401</a> (a <a href=\"https://wikipedia.org/wiki/Lockheed_L-1011_TriStar\" title=\"Lockheed L-1011 TriStar\">Lockheed L-1011 TriStar</a>) crashes in the <a href=\"https://wikipedia.org/wiki/Florida_Everglades\" class=\"mw-redirect\" title=\"Florida Everglades\">Florida Everglades</a> on approach to <a href=\"https://wikipedia.org/wiki/Miami_International_Airport\" title=\"Miami International Airport\">Miami International Airport</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, killing 101 of the 176 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_401\" title=\"Eastern Air Lines Flight 401\">Eastern Air Lines Flight 401</a> (a <a href=\"https://wikipedia.org/wiki/Lockheed_L-1011_TriStar\" title=\"Lockheed L-1011 TriStar\">Lockheed L-1011 TriStar</a>) crashes in the <a href=\"https://wikipedia.org/wiki/Florida_Everglades\" class=\"mw-redirect\" title=\"Florida Everglades\">Florida Everglades</a> on approach to <a href=\"https://wikipedia.org/wiki/Miami_International_Airport\" title=\"Miami International Airport\">Miami International Airport</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, killing 101 of the 176 people on board.", "links": [{"title": "Eastern Air Lines Flight 401", "link": "https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_401"}, {"title": "Lockheed L-1011 TriStar", "link": "https://wikipedia.org/wiki/Lockheed_L-1011_TriStar"}, {"title": "Florida Everglades", "link": "https://wikipedia.org/wiki/Florida_Everglades"}, {"title": "Miami International Airport", "link": "https://wikipedia.org/wiki/Miami_International_Airport"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}]}, {"year": "1975", "text": "A bomb explodes at LaGuardia Airport in New York City, killing 11 people and injuring more than 75.", "html": "1975 - A <a href=\"https://wikipedia.org/wiki/1975_LaGuardia_Airport_bombing\" title=\"1975 LaGuardia Airport bombing\">bomb explodes</a> at <a href=\"https://wikipedia.org/wiki/LaGuardia_Airport\" title=\"LaGuardia Airport\">LaGuardia Airport</a> in New York City, killing 11 people and injuring more than 75.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1975_LaGuardia_Airport_bombing\" title=\"1975 LaGuardia Airport bombing\">bomb explodes</a> at <a href=\"https://wikipedia.org/wiki/LaGuardia_Airport\" title=\"LaGuardia Airport\">LaGuardia Airport</a> in New York City, killing 11 people and injuring more than 75.", "links": [{"title": "1975 LaGuardia Airport bombing", "link": "https://wikipedia.org/wiki/1975_LaGuardia_Airport_bombing"}, {"title": "LaGuardia Airport", "link": "https://wikipedia.org/wiki/LaGuardia_Airport"}]}, {"year": "1989", "text": "Czech writer, philosopher and dissident <PERSON><PERSON><PERSON><PERSON> is elected the first post-communist President of Czechoslovakia.", "html": "1989 - Czech writer, philosopher and dissident <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is elected the first post-communist President of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>.", "no_year_html": "Czech writer, philosopher and dissident <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is elected the first post-communist President of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>l"}, {"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}]}, {"year": "1989", "text": "The Nikkei 225 for the Tokyo Stock Exchange hits its all-time intra-day high of 38,957.44 and closing high at 38,915.87, serving as the apex of the Japanese asset price bubble.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/Nikkei_225\" title=\"Nikkei 225\">Nikkei 225</a> for the <a href=\"https://wikipedia.org/wiki/Tokyo_Stock_Exchange\" title=\"Tokyo Stock Exchange\">Tokyo Stock Exchange</a> hits its all-time intra-day high of 38,957.44 and closing high at 38,915.87, serving as the apex of the <a href=\"https://wikipedia.org/wiki/Japanese_asset_price_bubble\" title=\"Japanese asset price bubble\">Japanese asset price bubble</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nikkei_225\" title=\"Nikkei 225\">Nikkei 225</a> for the <a href=\"https://wikipedia.org/wiki/Tokyo_Stock_Exchange\" title=\"Tokyo Stock Exchange\">Tokyo Stock Exchange</a> hits its all-time intra-day high of 38,957.44 and closing high at 38,915.87, serving as the apex of the <a href=\"https://wikipedia.org/wiki/Japanese_asset_price_bubble\" title=\"Japanese asset price bubble\">Japanese asset price bubble</a>.", "links": [{"title": "Nikkei 225", "link": "https://wikipedia.org/wiki/Nikkei_225"}, {"title": "Tokyo Stock Exchange", "link": "https://wikipedia.org/wiki/Tokyo_Stock_Exchange"}, {"title": "Japanese asset price bubble", "link": "https://wikipedia.org/wiki/Japanese_asset_price_bubble"}]}, {"year": "1992", "text": "<PERSON>, president of Brazil, tries to resign amidst corruption charges, but is then impeached.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, president of Brazil, tries to resign amidst corruption charges, but is then impeached.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, president of Brazil, tries to resign amidst corruption charges, but is then impeached.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "Turkish Airlines Flight 278 (a Boeing 737-400) crashes on approach to Van Ferit Melen Airport in Van, Turkey, killing 57 of the 76 people on board.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_278\" title=\"Turkish Airlines Flight 278\">Turkish Airlines Flight 278</a> (a <a href=\"https://wikipedia.org/wiki/Boeing_737_Classic\" title=\"Boeing 737 Classic\">Boeing 737-400</a>) crashes on approach to <a href=\"https://wikipedia.org/wiki/Van_Ferit_Melen_Airport\" title=\"Van Ferit Melen Airport\">Van Ferit Melen Airport</a> in <a href=\"https://wikipedia.org/wiki/Van,_Turkey\" title=\"Van, Turkey\">Van, Turkey</a>, killing 57 of the 76 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_278\" title=\"Turkish Airlines Flight 278\">Turkish Airlines Flight 278</a> (a <a href=\"https://wikipedia.org/wiki/Boeing_737_Classic\" title=\"Boeing 737 Classic\">Boeing 737-400</a>) crashes on approach to <a href=\"https://wikipedia.org/wiki/Van_Ferit_Melen_Airport\" title=\"Van Ferit Melen Airport\">Van Ferit Melen Airport</a> in <a href=\"https://wikipedia.org/wiki/Van,_Turkey\" title=\"Van, Turkey\">Van, Turkey</a>, killing 57 of the 76 people on board.", "links": [{"title": "Turkish Airlines Flight 278", "link": "https://wikipedia.org/wiki/Turkish_Airlines_Flight_278"}, {"title": "Boeing 737 Classic", "link": "https://wikipedia.org/wiki/Boeing_737_Classic"}, {"title": "Van Ferit Melen Airport", "link": "https://wikipedia.org/wiki/Van_Ferit_Melen_Airport"}, {"title": "Van, Turkey", "link": "https://wikipedia.org/wiki/Van,_Turkey"}]}, {"year": "1996", "text": "Guatemala and leaders of Guatemalan National Revolutionary Unity sign a peace accord ending a 36-year civil war.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a> and leaders of <a href=\"https://wikipedia.org/wiki/Guatemalan_National_Revolutionary_Unity\" title=\"Guatemalan National Revolutionary Unity\">Guatemalan National Revolutionary Unity</a> sign a peace accord ending a 36-year <a href=\"https://wikipedia.org/wiki/Guatemalan_Civil_War\" title=\"Guatemalan Civil War\">civil war</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a> and leaders of <a href=\"https://wikipedia.org/wiki/Guatemalan_National_Revolutionary_Unity\" title=\"Guatemalan National Revolutionary Unity\">Guatemalan National Revolutionary Unity</a> sign a peace accord ending a 36-year <a href=\"https://wikipedia.org/wiki/Guatemalan_Civil_War\" title=\"Guatemalan Civil War\">civil war</a>.", "links": [{"title": "Guatemala", "link": "https://wikipedia.org/wiki/Guatemala"}, {"title": "Guatemalan National Revolutionary Unity", "link": "https://wikipedia.org/wiki/Guatemalan_National_Revolutionary_Unity"}, {"title": "Guatemalan Civil War", "link": "https://wikipedia.org/wiki/Guatemalan_Civil_War"}]}, {"year": "1998", "text": "Leaders of the Khmer Rouge apologize for the Cambodian genocide that claimed over one million lives.", "html": "1998 - Leaders of the <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> apologize for the <a href=\"https://wikipedia.org/wiki/Cambodian_genocide\" title=\"Cambodian genocide\">Cambodian genocide</a> that claimed over one million lives.", "no_year_html": "Leaders of the <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> apologize for the <a href=\"https://wikipedia.org/wiki/Cambodian_genocide\" title=\"Cambodian genocide\">Cambodian genocide</a> that claimed over one million lives.", "links": [{"title": "Khmer Rouge", "link": "https://wikipedia.org/wiki/Khmer_Rouge"}, {"title": "Cambodian genocide", "link": "https://wikipedia.org/wiki/Cambodian_genocide"}]}, {"year": "2003", "text": "The last known speaker of Akkala Sami dies, rendering the language extinct.", "html": "2003 - The last known speaker of <a href=\"https://wikipedia.org/wiki/Akkala_Sami_language\" class=\"mw-redirect\" title=\"Akkala Sami language\">Akkala Sami</a> dies, rendering the language extinct.", "no_year_html": "The last known speaker of <a href=\"https://wikipedia.org/wiki/Akkala_Sami_language\" class=\"mw-redirect\" title=\"Akkala Sami language\">Akkala Sami</a> dies, rendering the language extinct.", "links": [{"title": "Akkala Sami language", "link": "https://wikipedia.org/wiki/Akkala_Sami_language"}]}, {"year": "2006", "text": "The UK settles its Anglo-American loan, post-WWII loan debt.", "html": "2006 - The UK settles its <a href=\"https://wikipedia.org/wiki/Anglo-American_loan\" title=\"Anglo-American loan\">Anglo-American loan</a>, post-WWII loan debt.", "no_year_html": "The UK settles its <a href=\"https://wikipedia.org/wiki/Anglo-American_loan\" title=\"Anglo-American loan\">Anglo-American loan</a>, post-WWII loan debt.", "links": [{"title": "Anglo-American loan", "link": "https://wikipedia.org/wiki/Anglo-American_loan"}]}, {"year": "2012", "text": "A Tupolev Tu-204 airliner crashes in a ditch between the airport fence and the M3 highway after overshooting a runway at Vnukovo International Airport in Moscow, Russia, killing five people and leaving three others critically injured.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-204\" title=\"Tupolev Tu-204\">Tupolev Tu-204</a> airliner <a href=\"https://wikipedia.org/wiki/Red_Wings_Airlines_Flight_9268\" title=\"Red Wings Airlines Flight 9268\">crashes in a ditch</a> between the airport fence and the <a href=\"https://wikipedia.org/wiki/M3_highway_(Russia)\" title=\"M3 highway (Russia)\">M3 highway</a> after overshooting a runway at <a href=\"https://wikipedia.org/wiki/Vnukovo_International_Airport\" title=\"Vnukovo International Airport\">Vnukovo International Airport</a> in Moscow, Russia, killing five people and leaving three others critically injured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-204\" title=\"Tupolev Tu-204\">Tupolev Tu-204</a> airliner <a href=\"https://wikipedia.org/wiki/Red_Wings_Airlines_Flight_9268\" title=\"Red Wings Airlines Flight 9268\">crashes in a ditch</a> between the airport fence and the <a href=\"https://wikipedia.org/wiki/M3_highway_(Russia)\" title=\"M3 highway (Russia)\">M3 highway</a> after overshooting a runway at <a href=\"https://wikipedia.org/wiki/Vnukovo_International_Airport\" title=\"Vnukovo International Airport\">Vnukovo International Airport</a> in Moscow, Russia, killing five people and leaving three others critically injured.", "links": [{"title": "Tupolev Tu-204", "link": "https://wikipedia.org/wiki/Tupolev_Tu-204"}, {"title": "Red Wings Airlines Flight 9268", "link": "https://wikipedia.org/wiki/Red_Wings_Airlines_Flight_9268"}, {"title": "M3 highway (Russia)", "link": "https://wikipedia.org/wiki/M3_highway_(Russia)"}, {"title": "Vnukovo International Airport", "link": "https://wikipedia.org/wiki/Vnukovo_International_Airport"}]}, {"year": "2013", "text": "A suicide bomb attack at the Volgograd-1 railway station in the southern Russian city of Volgograd kills at least 18 people and wounds 40 others.", "html": "2013 - A <a href=\"https://wikipedia.org/wiki/December_2013_Volgograd_bombings\" title=\"December 2013 Volgograd bombings\">suicide bomb attack</a> at the <a href=\"https://wikipedia.org/wiki/Volgograd_railway_station\" title=\"Volgograd railway station\">Volgograd-1 railway station</a> in the southern Russian city of <a href=\"https://wikipedia.org/wiki/Volgograd\" title=\"Volgograd\">Volgograd</a> kills at least 18 people and wounds 40 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/December_2013_Volgograd_bombings\" title=\"December 2013 Volgograd bombings\">suicide bomb attack</a> at the <a href=\"https://wikipedia.org/wiki/Volgograd_railway_station\" title=\"Volgograd railway station\">Volgograd-1 railway station</a> in the southern Russian city of <a href=\"https://wikipedia.org/wiki/Volgograd\" title=\"Volgograd\">Volgograd</a> kills at least 18 people and wounds 40 others.", "links": [{"title": "December 2013 Volgograd bombings", "link": "https://wikipedia.org/wiki/December_2013_Volgograd_bombings"}, {"title": "Volgograd railway station", "link": "https://wikipedia.org/wiki/Volgograd_railway_station"}, {"title": "Volgograd", "link": "https://wikipedia.org/wiki/Volgograd"}]}, {"year": "2013", "text": "Seven-time Formula One champion <PERSON> suffers a massive head injury while skiing in the French Alps.", "html": "2013 - Seven-time <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">champion</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> suffers a massive head injury while skiing in the French Alps.", "no_year_html": "Seven-time <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">champion</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> suffers a massive head injury while skiing in the French Alps.", "links": [{"title": "Formula One", "link": "https://wikipedia.org/wiki/Formula_One"}, {"title": "List of Formula One World Drivers' Champions", "link": "https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2020", "text": "A large explosion at the airport in the southern Yemeni city of Aden kills at least 22 people and wounds 50.", "html": "2020 - A <a href=\"https://wikipedia.org/wiki/2020_Aden_airport_attack\" title=\"2020 Aden airport attack\">large explosion</a> at the airport in the southern Yemeni city of <a href=\"https://wikipedia.org/wiki/Aden\" title=\"Aden\">Aden</a> kills at least 22 people and wounds 50.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2020_Aden_airport_attack\" title=\"2020 Aden airport attack\">large explosion</a> at the airport in the southern Yemeni city of <a href=\"https://wikipedia.org/wiki/Aden\" title=\"Aden\">Aden</a> kills at least 22 people and wounds 50.", "links": [{"title": "2020 Aden airport attack", "link": "https://wikipedia.org/wiki/2020_Aden_airport_attack"}, {"title": "Aden", "link": "https://wikipedia.org/wiki/Aden"}]}, {"year": "2020", "text": "A magnitude 6.4 earthquake hits near the town of Petrinja in Sisak-Moslavina County, Croatia, killing seven people.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/2020_Petrinja_earthquake\" title=\"2020 Petrinja earthquake\">A magnitude 6.4 earthquake</a> hits near the town of <a href=\"https://wikipedia.org/wiki/Petrinja\" title=\"Petrinja\">Petrinja</a> in <a href=\"https://wikipedia.org/wiki/Sisak-Moslavina_County\" title=\"Sisak-Moslavina County\">Sisak-Moslavina County</a>, <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a>, killing seven people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2020_Petrinja_earthquake\" title=\"2020 Petrinja earthquake\">A magnitude 6.4 earthquake</a> hits near the town of <a href=\"https://wikipedia.org/wiki/Petrinja\" title=\"Petrinja\">Petrinja</a> in <a href=\"https://wikipedia.org/wiki/Sisak-Moslavina_County\" title=\"Sisak-Moslavina County\">Sisak-Moslavina County</a>, <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a>, killing seven people.", "links": [{"title": "2020 Petrinja earthquake", "link": "https://wikipedia.org/wiki/2020_Petrinja_earthquake"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ja"}, {"title": "Sisak-Moslavina County", "link": "https://wikipedia.org/wiki/Sisak-Moslavina_County"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}]}, {"year": "2024", "text": "Jeju Air Flight 2216 crashes into a wall in Muan, South Korea, killing 179 of the 181 occupants. It is the worst aircraft accident on South Korean soil in history.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Jeju_Air_Flight_2216\" title=\"Jeju Air Flight 2216\">Jeju Air Flight 2216</a> crashes into a wall in <a href=\"https://wikipedia.org/wiki/Muan_International_Airport\" title=\"Muan International Airport\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>, killing 179 of the 181 occupants. It is the worst aircraft accident on South Korean soil in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jeju_Air_Flight_2216\" title=\"Jeju Air Flight 2216\">Jeju Air Flight 2216</a> crashes into a wall in <a href=\"https://wikipedia.org/wiki/Muan_International_Airport\" title=\"Muan International Airport\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>, killing 179 of the 181 occupants. It is the worst aircraft accident on South Korean soil in history.", "links": [{"title": "Jeju Air Flight 2216", "link": "https://wikipedia.org/wiki/Jeju_Air_Flight_2216"}, {"title": "Muan International Airport", "link": "https://wikipedia.org/wiki/Muan_International_Airport"}, {"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}]}], "Births": [{"year": "1536", "text": "<PERSON>, German nobleman (d. 1572)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Burgrave_of_Plauen\" title=\"<PERSON>, Burgrave of Plauen\"><PERSON></a>, German nobleman (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Burgrave_of_Plauen\" title=\"<PERSON>, Burgrave of Plauen\"><PERSON> VI</a>, German nobleman (d. 1572)", "links": [{"title": "<PERSON>, Burgrave of Plauen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Burgrave_of_Plauen"}]}, {"year": "1550", "text": "<PERSON>, Spanish diplomat and traveller (d. 1624)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/Garc%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish diplomat and traveller (d. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garc%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish diplomat and traveller (d. 1624)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Garc%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1633", "text": "<PERSON>, Swiss vicar (d. 1692)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss vicar (d. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss vicar (d. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1721", "text": "<PERSON>, mistress of King <PERSON> (d. 1764)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/Madame_<PERSON>_<PERSON>\" title=\"Madame de Pompadour\">Madame <PERSON></a>, mistress of King <PERSON> (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Madame_<PERSON>_<PERSON>\" title=\"Madame de Pompadour\">Madame <PERSON></a>, mistress of King <PERSON> (d. 1764)", "links": [{"title": "<PERSON> de Pompadour", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1746", "text": "<PERSON><PERSON>, Maltese priest and rebel leader (d. 1805)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/Save<PERSON>_Cassar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese priest and rebel leader (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cassar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese priest and rebel leader (d. 1805)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saverio_Cassar"}]}, {"year": "1766", "text": "<PERSON>, Scottish chemist and the inventor of waterproof fabric (d. 1843)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and the inventor of waterproof fabric (d. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and the inventor of waterproof fabric (d. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, Danish antiquarian (d. 1865)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/Christian_J%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish antiquarian (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_J%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish antiquarian (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_J%C3%<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, German physicist and journalist (d. 1877)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and journalist (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and journalist (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, American chemist and engineer (d. 1860)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, American librarian (d. 1885)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, American general and politician, 17th President of the United States (d. 1875)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1809", "text": "<PERSON>, American politician, journalist and educator (d. 1882)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/George_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, journalist and educator (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, journalist and educator (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>_Baines"}]}, {"year": "1809", "text": "<PERSON>, English lawyer and politician, Prime Minister of the United Kingdom (d. 1898)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1811", "text": "<PERSON>, Catalan Discalced Carmelite friar and priest (d. 1872)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a>, Catalan Discalced Carmelite friar and priest (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a>, Catalan Discalced Carmelite friar and priest (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, German physician and physiologist (d. 1895)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and physiologist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and physiologist (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON><PERSON><PERSON>, Indian barrister and first president of Indian National Congress (d. 1906)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/Womesh_<PERSON><PERSON>\" title=\"Womesh <PERSON>\">Wome<PERSON><PERSON></a>, Indian barrister and first president of <a href=\"https://wikipedia.org/wiki/The_Indian_National_Congress\" class=\"mw-redirect\" title=\"The Indian National Congress\">Indian National Congress</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wome<PERSON>_<PERSON><PERSON>\" title=\"Wome<PERSON>\">Wome<PERSON></a>, Indian barrister and first president of <a href=\"https://wikipedia.org/wiki/The_Indian_National_Congress\" class=\"mw-redirect\" title=\"The Indian National Congress\">Indian National Congress</a> (d. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Womesh_<PERSON><PERSON>_<PERSON>"}, {"title": "The Indian National Congress", "link": "https://wikipedia.org/wiki/The_Indian_National_Congress"}]}, {"year": "1855", "text": "<PERSON>, Estonian author and poet (d. 1927)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Estonian author and poet (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Estonian author and poet (d. 1927)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Dutch-French mathematician and academic (d. 1894)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-French mathematician and academic (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-French mathematician and academic (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, English chemist (d. 1937)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English chemist (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English chemist (d. 1937)", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON>, Mexican soldier and politician, 37th President of Mexico (d. 1920)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican soldier and politician, 37th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican soldier and politician, 37th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1870", "text": "<PERSON>, American historian, bibliographer and librarian (d. 1965)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, bibliographer and librarian (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, bibliographer and librarian (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Catalan cellist and conductor (d. 1973)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catalan cellist and conductor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catalan cellist and conductor (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, English violist (d. 1975)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lionel_<PERSON>is"}]}, {"year": "1879", "text": "<PERSON>, American general and pilot (d. 1936)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American boxer (d. 1968)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, German astronomer (d. 1933)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON>, American politician (d. 1984)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Mexican painter (d. 1974)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican painter (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican painter (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Brazilian painter (d. 1962)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Candido_Portinari\" title=\"Candido Portinari\"><PERSON><PERSON><PERSON></a>, Brazilian painter (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Candido_Portinari\" title=\"Candido Portinari\"><PERSON><PERSON><PERSON></a>, Brazilian painter (d. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Candido_Portinari"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author and poet (d. 1994)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Kuvempu\" title=\"Kuvempu\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and poet (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kuvempu\" title=\"Kuvempu\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and poet (d. 1994)", "links": [{"title": "Kuvempu", "link": "https://wikipedia.org/wiki/Kuvempu"}]}, {"year": "1908", "text": "<PERSON>, German theologian and author (d. 1993)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, English scientist and author (d. 1992)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist and author (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist and author (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English-American economist, author, and academic, Nobel Prize laureate  (d. 2013)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American economist, author, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American economist, author, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1911", "text": "<PERSON>, German physicist and spy (d. 1988)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and spy (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and spy (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Bangladeshi painter and academic (d. 1976)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi painter and academic (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi painter and academic (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American pianist and saxophonist (d. 1989)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and saxophonist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and saxophonist (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Australian painter and illustrator (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Australian painter and illustrator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Australian painter and illustrator (d. 1999)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>(artist)"}]}, {"year": "1915", "text": "<PERSON>, American hunter and author (d. 1965)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hunter and author (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hunter and author (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actress (d. 1996)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American lieutenant, lawyer, and politician, 38th Mayor of Los Angeles (d. 1998)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)\" class=\"mw-redirect\" title=\"<PERSON> (American politician)\"><PERSON></a>, American lieutenant, lawyer, and politician, 38th <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_politician)\" class=\"mw-redirect\" title=\"<PERSON> (American politician)\"><PERSON></a>, American lieutenant, lawyer, and politician, 38th <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a> (d. 1998)", "links": [{"title": "<PERSON> (American politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)"}, {"title": "Mayor of Los Angeles", "link": "https://wikipedia.org/wiki/Mayor_of_Los_Angeles"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Indian director and producer (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Ramanan<PERSON>_<PERSON>r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>r\" title=\"<PERSON><PERSON><PERSON> Sagar\"><PERSON><PERSON><PERSON></a>, Indian director and producer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rama<PERSON>d_Sagar"}]}, {"year": "1919", "text": "<PERSON>, American political scientist and author (d. 2014)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Italian pianist and composer (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Roman_Vlad\" title=\"Roman Vlad\"><PERSON></a>, Italian pianist and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Vlad\" title=\"Roman Vlad\"><PERSON></a>, Italian pianist and composer (d. 2013)", "links": [{"title": "Roman <PERSON>", "link": "https://wikipedia.org/wiki/Roman_Vlad"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Swedish-American actress, singer and poet (d. 1995)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Vive<PERSON>_Lindfo<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American actress, singer and poet (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vive<PERSON>_Lindfors\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American actress, singer and poet (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viveca_Lindfors"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Serbian politician, 1st President of the Federal Republic of Yugoslavia (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Dobrica_%C4%86osi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"President of the Federal Republic of Yugoslavia\">President of the Federal Republic of Yugoslavia</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dobrica_%C4%86osi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"President of the Federal Republic of Yugoslavia\">President of the Federal Republic of Yugoslavia</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dobrica_%C4%86osi%C4%87"}, {"title": "President of the Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/President_of_the_Federal_Republic_of_Yugoslavia"}]}, {"year": "1921", "text": "<PERSON>, English structural engineer, scientist and academic (d. 2000)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, English structural engineer, scientist and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, English structural engineer, scientist and academic (d. 2000)", "links": [{"title": "<PERSON> (engineer)", "link": "https://wikipedia.org/wiki/<PERSON>(engineer)"}]}, {"year": "1922", "text": "<PERSON>, American singer-songwriter (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Little Joe <PERSON>\"><PERSON> <PERSON></a>, American singer-songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Little <PERSON>\"><PERSON> <PERSON></a>, American singer-songwriter (d. 2014)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American author and academic (d. 1998)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Senegalese historian, anthropologist, and physicist (d. 1986)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Chei<PERSON>_<PERSON>_<PERSON>\" title=\"Chei<PERSON>\">Ch<PERSON><PERSON> <PERSON></a>, Senegalese historian, anthropologist, and physicist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ei<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ei<PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Senegalese historian, anthropologist, and physicist (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ei<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Hungarian-born Holocaust survivor (d. 2024)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-born Holocaust survivor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-born Holocaust survivor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American pianist and educator (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> E<PERSON>\"><PERSON></a>, American pianist and educator (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morton_<PERSON>\" title=\"<PERSON> E<PERSON>rin\"><PERSON></a>, American pianist and educator (d. 2017)", "links": [{"title": "Morton <PERSON>", "link": "https://wikipedia.org/wiki/Morton_Estrin"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American actress, game show panelist, socialite, heiress, and businesswoman (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, game show panelist, socialite, heiress, and businesswoman (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, game show panelist, socialite, heiress, and businesswoman (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Greek-Italian author and Holocaust survivor (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Shlomo_Venezia\" title=\"Shlomo Venezia\">Shlomo Venezia</a>, Greek-Italian author and <a href=\"https://wikipedia.org/wiki/Holocaust_survivor\" class=\"mw-redirect\" title=\"Holocaust survivor\">Holocaust survivor</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shlomo_Venezia\" title=\"Shlomo Venezia\">Shl<PERSON> Venezia</a>, Greek-Italian author and <a href=\"https://wikipedia.org/wiki/Holocaust_survivor\" class=\"mw-redirect\" title=\"Holocaust survivor\">Holocaust survivor</a> (d. 2012)", "links": [{"title": "Shlomo Venezia", "link": "https://wikipedia.org/wiki/Shlomo_Venezia"}, {"title": "Holocaust survivor", "link": "https://wikipedia.org/wiki/Holocaust_survivor"}]}, {"year": "1924", "text": "<PERSON>, American businessman and publisher, founded the Allbritton Communications Company (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and publisher, founded the <a href=\"https://wikipedia.org/wiki/Allbritton_Communications_Company\" class=\"mw-redirect\" title=\"Allbritton Communications Company\">Allbritton Communications Company</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and publisher, founded the <a href=\"https://wikipedia.org/wiki/Allbritton_Communications_Company\" class=\"mw-redirect\" title=\"Allbritton Communications Company\">Allbritton Communications Company</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Allbritton Communications Company", "link": "https://wikipedia.org/wiki/Allbritton_Communications_Company"}]}, {"year": "1924", "text": "<PERSON>, Korean politician (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ae"}]}, {"year": "1925", "text": "<PERSON>, American golfer and architect (d. 2020)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and architect (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and architect (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, British actor (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American actress and singer (d. 2023)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Inga_S<PERSON><PERSON>\" title=\"Inga Swenson\"><PERSON><PERSON></a>, American actress and singer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inga_S<PERSON>son\" title=\"Inga Swenson\"><PERSON><PERSON></a>, American actress and singer (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English journalist and author (d. 2020)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actor (d. 1995)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Ed_<PERSON>\" title=\"Ed <PERSON>\"><PERSON></a>, American actor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ed_<PERSON>\" title=\"Ed <PERSON>\"><PERSON></a>, American actor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ed_Flanders"}]}, {"year": "1936", "text": "<PERSON>, American actress and producer (d. 2017)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American football player (d. 1998)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American businessman, founded AutoNation (d. 2018)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/AutoNation\" title=\"AutoNation\">AutoNation</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/AutoNation\" title=\"AutoNation\">AutoNation</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "AutoNation", "link": "https://wikipedia.org/wiki/AutoNation"}]}, {"year": "1938", "text": "<PERSON>, American actor and producer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>oight\" title=\"<PERSON>oight\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>oight\" title=\"<PERSON> Voight\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>oight"}]}, {"year": "1939", "text": "<PERSON>, American country music singer-songwriter (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English singer-songwriter and flute player (d. 2018)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and flute player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and flute player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Indian actor  (d. 2012)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American equestrian", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American equestrian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American equestrian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American talent manager (d. 2010)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talent manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talent manager (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American author and illustrator", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian singer-songwriter, bass player, and producer (d. 1999)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, bass player, and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, bass player, and producer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Australian author and literary critic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and literary critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and literary critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, British artist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English singer-songwriter and actress (d. 2025)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actress (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actress (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American attorney, politician and academic administrator", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney, politician and academic administrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney, politician and academic administrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American physicist and computer scientist (d. 2012)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and computer scientist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and computer scientist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Estonian architect and poet (d. 2022)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect and poet (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect and poet (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, English drummer, songwriter, and producer (d. 1998)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English drummer, songwriter, and producer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English drummer, songwriter, and producer (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Scottish actor, director, and production manager (d. 1998)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, director, and production manager (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, director, and production manager (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American gospel singer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gospel singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gospel singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Northern Irish politician, 3rd First Minister of Northern Ireland", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Northern_Ireland_politician)\" title=\"<PERSON> (Northern Ireland politician)\"><PERSON></a>, Northern Irish politician, 3rd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland\" class=\"mw-redirect\" title=\"First Minister of Northern Ireland\">First Minister of Northern Ireland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Northern_Ireland_politician)\" title=\"<PERSON> (Northern Ireland politician)\"><PERSON></a>, Northern Irish politician, 3rd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland\" class=\"mw-redirect\" title=\"First Minister of Northern Ireland\">First Minister of Northern Ireland</a>", "links": [{"title": "<PERSON> (Northern Ireland politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Northern_Ireland_politician)"}, {"title": "First Minister of Northern Ireland", "link": "https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland"}]}, {"year": "1949", "text": "<PERSON>, English rugby league player and coach (d. 2008)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player and coach (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player and coach (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor (d. 2016)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Dutch historical anthropologist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Willem_<PERSON>_<PERSON>l%C3%A9court\" title=\"<PERSON>\"><PERSON></a>, Dutch historical anthropologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Willem_<PERSON>_<PERSON>l%C3%A9court\" title=\"<PERSON>\"><PERSON></a>, Dutch historical anthropologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Willem_de_Bl%C3%A9court"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and actress", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, American ballerina and choreographer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Gelsey_Kirkland\" title=\"Gelsey Kirkland\"><PERSON><PERSON><PERSON></a>, American ballerina and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gelsey_Kirkland\" title=\"Gelsey Kirkland\"><PERSON><PERSON><PERSON></a>, American ballerina and choreographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>ey_Kirkland"}]}, {"year": "1953", "text": "<PERSON>, Zambian-English journalist and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian-English journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian-English journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American gang leader, co-founded the Crips (d. 2005)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gang leader, co-founded the <a href=\"https://wikipedia.org/wiki/Crips\" title=\"Crips\">Crips</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gang leader, co-founded the <a href=\"https://wikipedia.org/wiki/Crips\" title=\"Crips\">Crips</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Crips", "link": "https://wikipedia.org/wiki/Crips"}]}, {"year": "1955", "text": "<PERSON>, English businessman and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American quantitative psychologist and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Quantitative_psychology\" title=\"Quantitative psychology\">quantitative psychologist</a> and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Quantitative_psychology\" title=\"Quantitative psychology\">quantitative psychologist</a> and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Quantitative psychology", "link": "https://wikipedia.org/wiki/Quantitative_psychology"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Lebanese-British journalist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese-British journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese-British journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American writer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American screenwriter and producer (d. 2017)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American author, playwright, and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English-Canadian actor, theatre director and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor, theatre director and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor, theatre director and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>-<PERSON>, American colonel, engineer, and astronaut", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, engineer, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, American colonel, engineer, and astronaut", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Belgian fashion designer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American comedian and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian cricketer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Congolese militia leader, founded the Union of Congolese Patriots", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese militia leader, founded the <a href=\"https://wikipedia.org/wiki/Union_of_Congolese_Patriots\" title=\"Union of Congolese Patriots\">Union of Congolese Patriots</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese militia leader, founded the <a href=\"https://wikipedia.org/wiki/Union_of_Congolese_Patriots\" title=\"Union of Congolese Patriots\">Union of Congolese Patriots</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Union of Congolese Patriots", "link": "https://wikipedia.org/wiki/Union_of_Congolese_Patriots"}]}, {"year": "1960", "text": "<PERSON>, American politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American engineer and academic (d. 2007)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Catalan politician and journalist, former president", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Catalan politician and journalist, former president", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Catalan politician and journalist, former president", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Swedish politician, Leader of the Swedish Moderate Party and 35th Prime Minister of Sweden", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician, Leader of the Swedish Moderate Party and 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician, Leader of the Swedish Moderate Party and 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sweden"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American investor and sports team owner", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American investor and sports team owner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American investor and sports team owner", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}]}, {"year": "1965", "text": "<PERSON>, American musician, singer, songwriter, and biologist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer, songwriter, and biologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer, songwriter, and biologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1966", "text": "<PERSON>, American actor and singer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Swiss author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, Swiss author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, Swiss author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American director, screenwriter and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director, screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director, screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American baseball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American financial journalist and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American financial journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American financial journalist and author", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American radio journalist and podcaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio journalist and podcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio journalist and podcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Swedish ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jude_Law\" title=\"Jude Law\"><PERSON> Law</a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jude_Law\" title=\"Jude Law\"><PERSON> Law</a>, English actor", "links": [{"title": "Jude Law", "link": "https://wikipedia.org/wiki/Jude_Law"}]}, {"year": "1973", "text": "<PERSON>, American businessman", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American journalist and author", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Indian actress and writer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Twinkle <PERSON>\"><PERSON><PERSON></a>, Indian actress and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Twin<PERSON>\"><PERSON><PERSON></a>, Indian actress and writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Twin<PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Filipino actress, comedian and vlogger (d. 2021)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(actress)\" title=\"<PERSON><PERSON> (actress)\"><PERSON><PERSON></a>, Filipino actress, comedian and vlogger (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(actress)\" title=\"<PERSON><PERSON> (actress)\"><PERSON><PERSON></a>, Filipino actress, comedian and vlogger (d. 2021)", "links": [{"title": "<PERSON><PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(actress)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian composer and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ryan <PERSON>\"><PERSON></a>, Canadian composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ryan Shore\"><PERSON></a>, Canadian composer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor, producer and screenwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, English footballer and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English footballer and journalist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Mexican actor, director and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Diego_Luna\" title=\"Diego Luna\"><PERSON></a>, Mexican actor, director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_Luna\" title=\"Diego Luna\"><PERSON></a>, Mexican actor, director and producer", "links": [{"title": "Diego <PERSON>", "link": "https://wikipedia.org/wiki/Diego_Luna"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American political commentator, columnist and author", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American political commentator, columnist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American political commentator, columnist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Japanese figure skater and sportscaster", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese figure skater and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese figure skater and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Bahamian-Canadian author", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian-Canadian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian-Canadian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Dutch author", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American cartoonist, writer and urban explorer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, writer and urban explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, writer and urban explorer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}]}, {"year": "1983", "text": "<PERSON>, American singer and songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American playwright", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American playwright", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "Alex<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1987", "text": "<PERSON>, Scottish actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Christen_Press\" title=\"Christen Press\">Christen Press</a>, American footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christen_Press\" title=\"Christen Press\">Christen Press</a>, American footballer", "links": [{"title": "Christen Press", "link": "https://wikipedia.org/wiki/<PERSON>en_Press"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Hungarian tennis player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/%C3%81gnes_Sz%C3%A1vay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81gnes_Sz%C3%A1vay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81gnes_Sz%C3%A1vay"}]}, {"year": "1989", "text": "<PERSON>, American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Japanese tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Nishi<PERSON>i"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Harri_S%C3%A4teri\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harri_S%C3%A4teri\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Harri_S%C3%A4teri"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American sprinter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Croatian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mislav_Or%C5%A1i%C4%87"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Canadian artistic gymnast", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_May\" title=\"Gabby May\"><PERSON><PERSON> May</a>, Canadian artistic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_May\" title=\"Gabby May\"><PERSON><PERSON> May</a>, Canadian artistic gymnast", "links": [{"title": "<PERSON><PERSON> May", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_May"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American singer and actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Japanese singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actor, musician and singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, musician and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, musician and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American actor", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Nigerian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Ghanaian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Danish footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Portuguese footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Francisco_Trinc%C3%A3o\" title=\"<PERSON> Trincão\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Trinc%C3%A3o\" title=\"<PERSON>ncão\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Trinc%C3%A3o"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Dutch-Turkish footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Orkun_K%C3%B6k%C3%A7%C3%BC\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orkun_K%C3%B6k%C3%A7%C3%BC\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-Turkish footballer", "links": [{"title": "Orkun <PERSON>", "link": "https://wikipedia.org/wiki/Orkun_K%C3%B6k%C3%A7%C3%BC"}]}, {"year": "2000", "text": "<PERSON>, Dominican baseball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julio_Rodr%C3%ADguez"}]}, {"year": "2006", "text": "<PERSON>, French footballer", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>bapp%C3%A9"}]}], "Deaths": [{"year": "1170", "text": "<PERSON>, English archbishop and saint (b. 1118)", "html": "1170 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and saint (b. 1118)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and saint (b. 1118)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1208", "text": "Emperor <PERSON><PERSON> of Jin, (b. 1168)", "html": "1208 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON> of Jin</a>, (b. 1168)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON> of Jin</a>, (b. 1168)", "links": [{"title": "Emperor <PERSON><PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin"}]}, {"year": "1380", "text": "<PERSON> of Poland, queen consort of Hungary (b. 1305)", "html": "1380 - <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Poland\" class=\"mw-redirect\" title=\"<PERSON> of Poland\"><PERSON> of Poland</a>, queen consort of Hungary (b. 1305)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_of_Poland\" class=\"mw-redirect\" title=\"<PERSON> of Poland\"><PERSON> of Poland</a>, queen consort of Hungary (b. 1305)", "links": [{"title": "<PERSON> of Poland", "link": "https://wikipedia.org/wiki/Elizabeth_of_Poland"}]}, {"year": "1550", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> VII, King of Kotte (b. 1468)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/Bhuvanaikabahu_VII_of_Kotte\" class=\"mw-redirect\" title=\"B<PERSON><PERSON><PERSON>bah<PERSON> VII of Kotte\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> VII</a>, King of Kotte (b. 1468)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>ikabahu_VII_of_Kotte\" class=\"mw-redirect\" title=\"Bhuvanaikabahu VII of Kotte\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> VII</a>, King of Kotte (b. 1468)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>bah<PERSON> VII of Kotte", "link": "https://wikipedia.org/wiki/Bhuvanaikabahu_VII_of_Kotte"}]}, {"year": "1563", "text": "<PERSON>, French preacher and theologian (b. 1515)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French preacher and theologian (b. 1515)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French preacher and theologian (b. 1515)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1606", "text": "<PERSON>, Prince of Transylvania (b. 1557)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prince of Transylvania (b. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prince of Transylvania (b. 1557)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1661", "text": "<PERSON>, French poet (b. 1594)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet (b. 1594)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet (b. 1594)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1689", "text": "<PERSON>, English physician and author (b. 1624)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author (b. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author (b. 1624)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_<PERSON>ydenham"}]}, {"year": "1720", "text": "<PERSON>, German astronomer and educator (b. 1670)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and educator (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and educator (b. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1731", "text": "<PERSON>, English mathematician and theorist (b. 1685)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Taylor\"><PERSON></a>, English mathematician and theorist (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Taylor\"><PERSON></a>, English mathematician and theorist (b. 1685)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, German composer (b. 1716)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (b. 1716)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, Norwegian-Danish poet and playwright (b. 1742)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Danish poet and playwright (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Danish poet and playwright (b. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON><PERSON>, Portuguese diplomat and scientist (b. 1750)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Carvalho_e_Sampayo\" title=\"<PERSON><PERSON> Carvalho e Sampayo\"><PERSON><PERSON> e <PERSON></a>, Portuguese diplomat and scientist (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Carvalho_e_Sampayo\" title=\"<PERSON><PERSON> Carvalho e Sampayo\"><PERSON><PERSON> e <PERSON></a>, Portuguese diplomat and scientist (b. 1750)", "links": [{"title": "<PERSON><PERSON> de Carvalho e Sampayo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_e_Sampayo"}]}, {"year": "1825", "text": "<PERSON><PERSON><PERSON>, French painter and illustrator (b. 1748)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and illustrator (b. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and illustrator (b. 1748)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1838", "text": "<PERSON><PERSON><PERSON>, Norwegian priest and botanist (b. 1794)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>_<PERSON>mmerfelt\" title=\"<PERSON><PERSON><PERSON>mmerfelt\"><PERSON><PERSON><PERSON></a>, Norwegian priest and botanist (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>_<PERSON>mmerfelt\" title=\"<PERSON><PERSON><PERSON>mmerfelt\"><PERSON><PERSON><PERSON></a>, Norwegian priest and botanist (b. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>_<PERSON>lt"}]}, {"year": "1890", "text": "<PERSON><PERSON>, American tribal leader (b. 1826)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Spotted_Elk\" title=\"Spotted Elk\">Spotted Elk</a>, American tribal leader (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spotted_Elk\" title=\"Spotted Elk\">Spotted Elk</a>, American tribal leader (b. 1826)", "links": [{"title": "Spotted Elk", "link": "https://wikipedia.org/wiki/Spotted_Elk"}]}, {"year": "1890", "text": "<PERSON><PERSON>, French novelist and dramatist (b. 1821)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Octave_Feuillet\" title=\"Octave Feuillet\">Oct<PERSON></a>, French novelist and dramatist (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octave_Feuillet\" title=\"Octave Feuillet\">Oct<PERSON></a>, French novelist and dramatist (b. 1821)", "links": [{"title": "Octave <PERSON>", "link": "https://wikipedia.org/wiki/Octave_<PERSON><PERSON>let"}]}, {"year": "1891", "text": "<PERSON>, Polish-German mathematician and academic (b. 1823)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German mathematician and academic (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German mathematician and academic (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, English poet and hymn-writer (b. 1830)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and hymn-writer (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and hymn-writer (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English-American painter, author, and activist (b. 1812)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American painter, author, and activist (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American painter, author, and activist (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, English entomologist (b. 1862)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English entomologist (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English entomologist (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American financier (b. 1837)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Anglo-Irish classical scholar and politician (b. 1850)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classicist)\" title=\"<PERSON> (classicist)\"><PERSON></a>, Anglo-Irish classical scholar and politician (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classicist)\" title=\"<PERSON> (classicist)\"><PERSON></a>, Anglo-Irish classical scholar and politician (b. 1850)", "links": [{"title": "<PERSON> (classicist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classicist)"}]}, {"year": "1910", "text": "<PERSON>, English tennis player (b. 1872)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, English poet, author and critic (b. 1860)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English poet, author and critic (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English poet, author and critic (b. 1860)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American educator (b. 1855)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ch\"><PERSON></a>, American educator (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Canadian physician and professor (b. 1849)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and professor (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and professor (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, German philologist, linguist and lexicographer (b. 1846)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist, linguist and lexicographer (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist, linguist and lexicographer (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Swiss poet and academic, Nobel Prize laureate (b. 1845)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss poet and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss poet and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1925", "text": "<PERSON>, Swiss-French painter (b. 1865)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_<PERSON>ton\" title=\"<PERSON>\"><PERSON></a>, Swiss-French painter (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_<PERSON>ton\" title=\"<PERSON>\"><PERSON></a>, Swiss-French painter (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Vallotton"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Austrian poet and author (b. 1875)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian poet and author (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian poet and author (b. 1875)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German engineer and businessman, founded <PERSON><PERSON> (b. 1846)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/May<PERSON>\" title=\"May<PERSON>\"><PERSON><PERSON></a> (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/May<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>bach", "link": "https://wikipedia.org/wiki/Maybach"}]}, {"year": "1929", "text": "<PERSON>, American librarian (b. 1871)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American journalist, author, and playwright (b. 1878)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and playwright (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and playwright (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American mathematician, sociologist, essayist, newspaper columnist and author (b. 1863)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, American mathematician, sociologist, essayist, newspaper columnist and author (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, American mathematician, sociologist, essayist, newspaper columnist and author (b. 1863)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)"}]}, {"year": "1939", "text": "<PERSON>, French psychiatrist, feminist and political activist (b. 1874)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French psychiatrist, feminist and political activist (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French psychiatrist, feminist and political activist (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American businessman (b. 1873)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American painter (b. 1864)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Italian mathematician and scholar (b. 1873)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mathematician and scholar (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mathematician and scholar (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American cartoonist and writer (b. 1866)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Art_Young\" title=\"<PERSON> Young\"><PERSON></a>, American cartoonist and writer (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Young\" title=\"<PERSON> Young\"><PERSON></a>, American cartoonist and writer (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Young"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Chechen rebel (b. 1910)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Khasa<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Khasa<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chechen_people\" class=\"mw-redirect\" title=\"Chechen people\">Chechen</a> rebel (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Khasa<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Khasa<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chechen_people\" class=\"mw-redirect\" title=\"Chechen people\">Chechen</a> rebel (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chechen people", "link": "https://wikipedia.org/wiki/Chechen_people"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1887)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dark_Cloud\" title=\"Beulah Dark Cloud\"><PERSON><PERSON><PERSON></a>, American actress (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Cloud\" title=\"Beulah Dark Cloud\"><PERSON><PERSON><PERSON></a>, American actress (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dark_Cloud"}]}, {"year": "1946", "text": "<PERSON><PERSON>, German composer and organist (b. 1872)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German composer and organist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German composer and organist (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1948", "text": "<PERSON>, British composer and music teacher (b. 1878)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British composer and music teacher (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British composer and music teacher (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American historian and author (b. 1883)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American pianist, composer, and bandleader (b. 1897)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American pianist, composer and teacher (b. 1898)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, composer and teacher (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, composer and teacher (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American chemist (b. 1865)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American physician and author (b. 1871)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lynk"}]}, {"year": "1958", "text": "<PERSON>, American dancer and choreographer (b. 1895)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English soldier and composer (b. 1903)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and composer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and composer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English author and poet (b. 1862)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Eden_Phillpotts\" title=\"<PERSON> Phillpotts\"><PERSON></a>, English author and poet (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eden_Phillpotts\" title=\"Eden Phillpotts\"><PERSON></a>, English author and poet (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eden_Phillpotts"}]}, {"year": "1965", "text": "<PERSON>, American screenwriter, journalist and film reviewer (b. 1908)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, journalist and film reviewer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, journalist and film reviewer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese composer and conductor (b. 1886)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/K%C5%8Dsak<PERSON>_Yamada\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese composer and conductor (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8Dsak<PERSON>_Yamada\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese composer and conductor (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8Ds<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American violinist, composer, and conductor (b. 1890)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist, composer, and conductor (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist, composer, and conductor (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English theologian and philosopher (b. 1904)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Austin_Farrer\" title=\"<PERSON> Farrer\"><PERSON></a>, English theologian and philosopher (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Farrer\" title=\"<PERSON> Farrer\"><PERSON></a>, English theologian and philosopher (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rer"}]}, {"year": "1970", "text": "<PERSON>, American zoologist and anatomist (b. 1876)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist and anatomist (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist and anatomist (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American director and painter (b. 1909)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and painter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and painter (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American lawyer and jurist (b. 1899)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American sculptor and director (b. 1903)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and director (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and director (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American author and naturalist (b. 1911)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and naturalist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and naturalist (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Belgian runner (b. 1954)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian runner (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian runner (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American journalist and politician (b. 1901)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and politician (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%A9bert\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American journalist and politician (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%A9bert"}]}, {"year": "1979", "text": "<PERSON>, Welsh biochemist (b. 1909)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh biochemist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh biochemist (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian author and educator (b. 1899)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Nadezhda_Mandelstam\" title=\"<PERSON><PERSON><PERSON><PERSON>delsta<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian author and educator (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nadez<PERSON><PERSON>_<PERSON>delstam\" title=\"<PERSON><PERSON><PERSON><PERSON> Mandelsta<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian author and educator (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nadezh<PERSON>_Mandelstam"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American businessman and social reformer (b. 1879)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and social reformer (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman and social reformer (b. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American nutritionist, and biochemist (b. 1917)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nutritionist, and biochemist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nutritionist, and biochemist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Croatian author, poet, and playwright (b. 1893)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%BEa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian author, poet, and playwright (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%BEa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian author, poet, and playwright (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miroslav_Krle%C5%BEa"}]}, {"year": "1984", "text": "<PERSON><PERSON> <PERSON><PERSON>, American photographer (b. 1898)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American photographer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American photographer (b. 1898)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American composer, lyricist and songwriter (b. 1900)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Robin\"><PERSON></a>, American composer, lyricist and songwriter (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Robin\" title=\"Leo Robin\"><PERSON></a>, American composer, lyricist and songwriter (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English captain and politician, Prime Minister of the United Kingdom (b. 1894)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1986", "text": "<PERSON>, Russian director and screenwriter (b. 1932)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and screenwriter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and screenwriter (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Japanese author (b. 1899)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese author (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese author (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American sociologist (b. 1914)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sociologist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sociologist (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Egyptian race car driver (b. 1940)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian race car driver (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian race car driver (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Welsh scientist and nuclear researcher (b. 1917)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>euan_<PERSON>\" title=\"Ieuan Maddock\"><PERSON><PERSON><PERSON></a>, Welsh scientist and nuclear researcher (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>euan_<PERSON>\" title=\"Ieuan Maddock\"><PERSON><PERSON><PERSON></a>, Welsh scientist and nuclear researcher (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ieuan_<PERSON>dock"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American actress and singer (b. 1897)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "1994", "text": "<PERSON>, Australian actor (b. 1926)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Dutch pianist, composer and psychiatrist (b. 1913)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pianist, composer and psychiatrist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pianist, composer and psychiatrist (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Welsh clergyman and author (b. 1911)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh clergyman and author (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh clergyman and author (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, French singer-songwriter and actress (b. 1906)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer-songwriter and actress (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer-songwriter and actress (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Scottish politician (b. 1907)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American scholar, military and civil servant, and author (b. 1917)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, military and civil servant, and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, military and civil servant, and author (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor and film director (b. 1920)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_filmmaker)\" title=\"<PERSON> (American filmmaker)\"><PERSON></a>, American actor and film director (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_filmmaker)\" title=\"<PERSON> (American filmmaker)\"><PERSON></a>, American actor and film director (b. 1920)", "links": [{"title": "<PERSON> (American filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_filmmaker)"}]}, {"year": "1999", "text": "<PERSON>, Polish-English criminologist and academic (b. 1906)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English criminologist and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English criminologist and academic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Japanese conductor (b. 1908)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese conductor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese conductor (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>na"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian painter, photographer, designer, educator and art theorist (b. 1906)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON>yö<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian painter, photographer, designer, educator and art theorist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON>yö<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian painter, photographer, designer, educator and art theorist (b. 1906)", "links": [{"title": "György <PERSON>", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_Kepes"}]}, {"year": "2002", "text": "<PERSON>, American lawyer and politician (b. 1925)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, English actor (b. 1932)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Dinsdale_Landen\" title=\"Dinsdale Landen\"><PERSON><PERSON> Landen</a>, English actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dinsdale_Landen\" title=\"Dinsdale Landen\"><PERSON><PERSON> Landen</a>, English actor (b. 1932)", "links": [{"title": "Dinsdale Landen", "link": "https://wikipedia.org/wiki/Dinsdale_Landen"}]}, {"year": "2003", "text": "<PERSON>, English comedian, actor, and game show host (b. 1928)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and game show host (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and game show host (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (b. 1912)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2004", "text": "<PERSON>, American poet, essayist, teacher, lecturer, editor and publisher (b. 1928)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet, essayist, teacher, lecturer, editor and publisher (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet, essayist, teacher, lecturer, editor and publisher (b. 1928)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "2005", "text": "<PERSON>, British historian and academic director (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British historian and academic director (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British historian and academic director (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, British art scholar and author (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British art scholar and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British art scholar and author (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Scottish footballer (b. 1972)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Donnell_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Donnell_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer (b. 1972)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Phil_O%27D<PERSON>_(footballer)"}]}, {"year": "2007", "text": "<PERSON>, American advertising executive (b. 1936)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American advertising executive (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American advertising executive (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American trumpet player and composer (b. 1938)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American soldier (b. 1913)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Polish journalist and writer (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist and writer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist and writer (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American artist and illustrator (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and illustrator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and illustrator (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Israeli footballer and manager (b. 1956)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer and manager (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer and manager (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Avi_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actor and cartoonist (b. 1914)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and cartoonist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and cartoonist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American scholar (b. 1928)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, South African-Australian cricketer and sportscaster (b. 1946)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian cricketer and sportscaster (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian cricketer and sportscaster (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, British newspaper journalist (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British newspaper journalist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British newspaper journalist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON> <PERSON><PERSON>, Chinese-American critic and scholar (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/C._T._Hsia\" title=\"C. T. Hsia\"><PERSON><PERSON> <PERSON><PERSON></a>, Chinese-American critic and scholar (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._T._Hsia\" title=\"C. T. Hsia\"><PERSON><PERSON> <PERSON><PERSON></a>, Chinese-American critic and scholar (b. 1921)", "links": [{"title": "C. T. H<PERSON>", "link": "https://wikipedia.org/wiki/C._T._Hsia"}]}, {"year": "2013", "text": "<PERSON>, American guitarist, drummer, and songwriter (b. 1978)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, drummer, and songwriter (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, drummer, and songwriter (b. 1978)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2013", "text": "<PERSON>, American basketball player (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish classical and film music composer (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>\" title=\"Woj<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish classical and film music composer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>\" title=\"Woj<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish classical and film music composer (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Indian-Hong Kong businessman and philanthropist (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Hong Kong businessman and philanthropist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Hong Kong businessman and philanthropist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Sr., Filipino lawyer and politician, Governor of Cavite (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Sr.\"><PERSON><PERSON>, Sr.</a>, Filipino lawyer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Cavite\" title=\"Governor of Cavite\">Governor of Cavite</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Sr.\"><PERSON><PERSON>, Sr.</a>, Filipino lawyer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Cavite\" title=\"Governor of Cavite\">Governor of Cavite</a> (b. 1933)", "links": [{"title": "<PERSON><PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>."}, {"title": "Governor of Cavite", "link": "https://wikipedia.org/wiki/Governor_of_Cavite"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian general and politician, 25th Governor of Punjab (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Indian general and politician, 25th <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Punjab_(India)\" class=\"mw-redirect\" title=\"List of governors of Punjab (India)\">Governor of Punjab</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Indian general and politician, 25th <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Punjab_(India)\" class=\"mw-redirect\" title=\"List of governors of Punjab (India)\">Governor of Punjab</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "List of governors of Punjab (India)", "link": "https://wikipedia.org/wiki/List_of_governors_of_Punjab_(India)"}]}, {"year": "2015", "text": "<PERSON>, Czech footballer and coach (b. 1968)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%AD%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and coach (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%AD%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and coach (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pavel_Srn%C3%AD%C4%8Dek"}]}, {"year": "2016", "text": "<PERSON><PERSON>, American football defensive back (b. 1977)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football defensive back (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football defensive back (b. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, American football head coach (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football head coach (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football head coach (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Irish actress (b. 1925)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American neofuturistic architect and real estate developer  (b. 1924)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American neofuturistic architect and real estate developer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American neofuturistic architect and real estate developer (b. 1924)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2018", "text": "<PERSON>, American novelist, historian and screenwriter (b. 1939)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, historian and screenwriter (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, historian and screenwriter (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Mexican actress (b. 1935)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress (b. 1935)", "links": [{"title": "Rosenda Monteros", "link": "https://wikipedia.org/wiki/Rosenda_Monteros"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON><PERSON>, Scottish writer and artist (b. 1934)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>as<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Scottish writer and artist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Scottish writer and artist (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, English writer, comedian and musician (b. 1944)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer, comedian and musician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer, comedian and musician (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Italian-French fashion designer (b. 1922)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French fashion designer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French fashion designer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American educator (b. 1937)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, South African composer (b. 1945)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African composer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African composer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer (b. 1940)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Pel%C3%A9\" title=\"Pelé\"><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pel%C3%A9\" title=\"Pelé\"><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pel%C3%A9"}]}, {"year": "2022", "text": "<PERSON>, Estonian politician, Estonian Minister of the Interior (b. 1950)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a> (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a> (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of the Interior (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, English fashion designer (b. 1941)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English fashion designer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English fashion designer (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, French-born Brazilian racing driver, CART champion (2000, 2001), 2003 Indianapolis 500 winner (b. 1967)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-born Brazilian racing driver, <a href=\"https://wikipedia.org/wiki/Championship_Auto_Racing_Teams\" title=\"Championship Auto Racing Teams\">CART</a> champion (<a href=\"https://wikipedia.org/wiki/2000_CART_season\" class=\"mw-redirect\" title=\"2000 CART season\">2000</a>, <a href=\"https://wikipedia.org/wiki/2001_CART_season\" class=\"mw-redirect\" title=\"2001 CART season\">2001</a>), <a href=\"https://wikipedia.org/wiki/2003_Indianapolis_500\" title=\"2003 Indianapolis 500\">2003 Indianapolis 500</a> winner (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-born Brazilian racing driver, <a href=\"https://wikipedia.org/wiki/Championship_Auto_Racing_Teams\" title=\"Championship Auto Racing Teams\">CART</a> champion (<a href=\"https://wikipedia.org/wiki/2000_CART_season\" class=\"mw-redirect\" title=\"2000 CART season\">2000</a>, <a href=\"https://wikipedia.org/wiki/2001_CART_season\" class=\"mw-redirect\" title=\"2001 CART season\">2001</a>), <a href=\"https://wikipedia.org/wiki/2003_Indianapolis_500\" title=\"2003 Indianapolis 500\">2003 Indianapolis 500</a> winner (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Championship Auto Racing Teams", "link": "https://wikipedia.org/wiki/Championship_Auto_Racing_Teams"}, {"title": "2000 CART season", "link": "https://wikipedia.org/wiki/2000_CART_season"}, {"title": "2001 CART season", "link": "https://wikipedia.org/wiki/2001_CART_season"}, {"title": "2003 Indianapolis 500", "link": "https://wikipedia.org/wiki/2003_Indianapolis_500"}]}, {"year": "2024", "text": "<PERSON>, American journalist and academic (b. 1948)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and academic (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and academic (b. 1948)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "2024", "text": "<PERSON>, American politician, 39th President of the United States (b. 1924)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "2024", "text": "<PERSON>, American actress and singer (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Japanese supercentenarian (b. 1908)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Itook<PERSON>\"><PERSON><PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/Supercentenarian\" title=\"Supercentenarian\">supercentenarian</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Itooka\"><PERSON><PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/Supercentenarian\" title=\"Supercentenarian\">supercentenarian</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ooka"}, {"title": "Supercentenarian", "link": "https://wikipedia.org/wiki/Supercentenarian"}]}]}}