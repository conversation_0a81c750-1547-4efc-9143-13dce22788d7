{"date": "September 26", "url": "https://wikipedia.org/wiki/September_26", "data": {"Events": [{"year": "46 BC", "text": "<PERSON> dedicates a temple to Venus Genetrix, fulfilling a vow he made at the Battle of Pharsalus.", "html": "46 BC - 46 BC - <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\">Julius Caesar</a> dedicates a <a href=\"https://wikipedia.org/wiki/Temple_of_Venus_Genetrix\" title=\"Temple of Venus Genetrix\">temple to Venus Genetrix</a>, fulfilling a vow he made at the <a href=\"https://wikipedia.org/wiki/Battle_of_Pharsalus\" title=\"Battle of Pharsalus\">Battle of Pharsalus</a>.", "no_year_html": "46 BC - <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\"><PERSON> Caesar</a> dedicates a <a href=\"https://wikipedia.org/wiki/Temple_of_Venus_Genetrix\" title=\"Temple of Venus Genetrix\">temple to Venus Genetrix</a>, fulfilling a vow he made at the <a href=\"https://wikipedia.org/wiki/Battle_of_Pharsalus\" title=\"Battle of Pharsalus\">Battle of Pharsalus</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Temple of Venus Genetrix", "link": "https://wikipedia.org/wiki/Temple_of_Venus_Genetrix"}, {"title": "Battle of Pharsalus", "link": "https://wikipedia.org/wiki/Battle_of_Pharsalus"}]}, {"year": "715", "text": "<PERSON><PERSON><PERSON><PERSON> defeats <PERSON><PERSON><PERSON> at the Battle of Compiègne.", "html": "715 - <PERSON><PERSON><PERSON><PERSON> defeats <PERSON><PERSON><PERSON> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Compi%C3%A8gne\" title=\"Battle of Compiègne\">Battle of Compiègne</a>.", "no_year_html": "<PERSON><PERSON><PERSON><PERSON> defeats <PERSON><PERSON><PERSON> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Compi%C3%A8gne\" title=\"Battle of Compiègne\">Battle of Compiègne</a>.", "links": [{"title": "Battle of Compiègne", "link": "https://wikipedia.org/wiki/Battle_of_Compi%C3%A8gne"}]}, {"year": "1087", "text": "<PERSON> is crowned King of England, and reigns until 1100.", "html": "1087 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"William II of England\"><PERSON> II</a> is crowned King of England, and reigns until 1100.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> II</a> is crowned King of England, and reigns until 1100.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1212", "text": "The Golden Bull of Sicily is issued to confirm the hereditary royal title in Bohemia for the Přemyslid dynasty.", "html": "1212 - The <a href=\"https://wikipedia.org/wiki/Golden_Bull_of_Sicily\" title=\"Golden Bull of Sicily\">Golden Bull of Sicily</a> is issued to confirm the hereditary royal title in Bohemia for the Přemyslid dynasty.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Golden_Bull_of_Sicily\" title=\"Golden Bull of Sicily\">Golden Bull of Sicily</a> is issued to confirm the hereditary royal title in Bohemia for the Přemyslid dynasty.", "links": [{"title": "Golden Bull of Sicily", "link": "https://wikipedia.org/wiki/Golden_Bull_of_Sicily"}]}, {"year": "1345", "text": "Friso-Hollandic Wars: Frisians defeat Holland in the Battle of Warns.", "html": "1345 - Friso-Hollandic Wars: Frisians defeat Holland in the <a href=\"https://wikipedia.org/wiki/Battle_of_Warns\" title=\"Battle of Warns\">Battle of Warns</a>.", "no_year_html": "Friso-Hollandic Wars: Frisians defeat Holland in the <a href=\"https://wikipedia.org/wiki/Battle_of_Warns\" title=\"Battle of Warns\">Battle of Warns</a>.", "links": [{"title": "Battle of Warns", "link": "https://wikipedia.org/wiki/Battle_of_Warns"}]}, {"year": "1371", "text": "Serbian-Turkish wars: Ottoman Turks fought against a Serbian army at the Battle of Maritsa.", "html": "1371 - Serbian-Turkish wars: Ottoman Turks fought against a Serbian army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Maritsa\" title=\"Battle of Maritsa\">Battle of Maritsa</a>.", "no_year_html": "Serbian-Turkish wars: Ottoman Turks fought against a Serbian army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Maritsa\" title=\"Battle of Maritsa\">Battle of Maritsa</a>.", "links": [{"title": "Battle of Maritsa", "link": "https://wikipedia.org/wiki/Battle_of_Maritsa"}]}, {"year": "1423", "text": "Hundred Years' War: A French army defeats the English at the Battle of La Brossinière.", "html": "1423 - <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: A French army defeats the English at the <a href=\"https://wikipedia.org/wiki/Battle_of_La_Brossini%C3%A8re\" title=\"Battle of La Brossinière\">Battle of La Brossinière</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: A French army defeats the English at the <a href=\"https://wikipedia.org/wiki/Battle_of_La_Brossini%C3%A8re\" title=\"Battle of La Brossinière\">Battle of La Brossinière</a>.", "links": [{"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}, {"title": "Battle of La Brossinière", "link": "https://wikipedia.org/wiki/Battle_of_La_Brossini%C3%A8re"}]}, {"year": "1493", "text": "<PERSON> <PERSON> issues the papal bull Dudum siquidem to the Spanish, extending the grant of new lands he made them in Inter caetera.", "html": "1493 - Pope <PERSON> VI issues the papal bull <i><a href=\"https://wikipedia.org/wiki/Dudum_siquidem\" title=\"Dudum siquidem\">Dudum siquidem</a></i> to the Spanish, extending the grant of new lands he made them in <i><a href=\"https://wikipedia.org/wiki/Inter_caetera\" title=\"Inter caetera\">Inter caetera</a></i>.", "no_year_html": "Pope <PERSON> issues the papal bull <i><a href=\"https://wikipedia.org/wiki/Dudum_siquidem\" title=\"Dudum siquidem\">Dudum siquidem</a></i> to the Spanish, extending the grant of new lands he made them in <i><a href=\"https://wikipedia.org/wiki/Inter_caetera\" title=\"Inter caetera\">Inter caetera</a></i>.", "links": [{"title": "<PERSON><PERSON><PERSON> siquidem", "link": "https://wikipedia.org/wiki/Dudum_siquidem"}, {"title": "Inter caetera", "link": "https://wikipedia.org/wiki/Inter_caetera"}]}, {"year": "1580", "text": "<PERSON> finishes his circumnavigation of the Earth in Plymouth, England.", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> finishes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_circumnavigation\" title=\"<PERSON>'s circumnavigation\">his circumnavigation of the Earth</a> in Plymouth, England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> finishes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_circumnavigation\" title=\"<PERSON>'s circumnavigation\">his circumnavigation of the Earth</a> in Plymouth, England.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s circumnavigation", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_circumnavigation"}]}, {"year": "1687", "text": "Morean War: The Parthenon in Athens, used as a gunpowder depot by the Ottoman garrison, is partially destroyed after being bombarded during the Siege of the Acropolis by Venetian forces.", "html": "1687 - <a href=\"https://wikipedia.org/wiki/Morean_War\" title=\"Morean War\">Morean War</a>: The <a href=\"https://wikipedia.org/wiki/Parthenon\" title=\"Parthenon\">Parthenon</a> in Athens, used as a gunpowder depot by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> garrison, is partially destroyed after being bombarded during the <a href=\"https://wikipedia.org/wiki/Siege_of_the_Acropolis_(1687)\" title=\"Siege of the Acropolis (1687)\">Siege of the Acropolis</a> by <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetian</a> forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morean_War\" title=\"Morean War\">Morean War</a>: The <a href=\"https://wikipedia.org/wiki/Parthenon\" title=\"Parthenon\">Parthenon</a> in Athens, used as a gunpowder depot by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> garrison, is partially destroyed after being bombarded during the <a href=\"https://wikipedia.org/wiki/Siege_of_the_Acropolis_(1687)\" title=\"Siege of the Acropolis (1687)\">Siege of the Acropolis</a> by <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetian</a> forces.", "links": [{"title": "Morean War", "link": "https://wikipedia.org/wiki/Morean_War"}, {"title": "Parthenon", "link": "https://wikipedia.org/wiki/Parthenon"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Siege of the Acropolis (1687)", "link": "https://wikipedia.org/wiki/Siege_of_the_Acropolis_(1687)"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}]}, {"year": "1688", "text": "The city council of Amsterdam votes to support <PERSON> Orange's invasion of England, which became the Glorious Revolution.", "html": "1688 - The city council of Amsterdam votes to support <PERSON> Orange's invasion of England, which became the <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>.", "no_year_html": "The city council of Amsterdam votes to support <PERSON> Orange's invasion of England, which became the <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>.", "links": [{"title": "Glorious Revolution", "link": "https://wikipedia.org/wiki/Glorious_Revolution"}]}, {"year": "1777", "text": "American Revolution: British troops occupy Philadelphia.", "html": "1777 - American Revolution: British troops <a href=\"https://wikipedia.org/wiki/Philadelphia_campaign\" title=\"Philadelphia campaign\">occupy</a> Philadelphia.", "no_year_html": "American Revolution: British troops <a href=\"https://wikipedia.org/wiki/Philadelphia_campaign\" title=\"Philadelphia campaign\">occupy</a> Philadelphia.", "links": [{"title": "Philadelphia campaign", "link": "https://wikipedia.org/wiki/Philadelphia_campaign"}]}, {"year": "1789", "text": "<PERSON> appoints <PERSON> the first United States Secretary of State.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George <PERSON>\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> the first <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> the first <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1799", "text": "War of the 2nd Coalition: French troops defeat Austro-Russian forces, leading to the collapse of <PERSON><PERSON><PERSON>'s campaign.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/War_of_the_2nd_Coalition\" class=\"mw-redirect\" title=\"War of the 2nd Coalition\">War of the 2nd Coalition</a>: French troops <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Zurich\" title=\"Second Battle of Zurich\">defeat</a> Austro-Russian forces, leading to the collapse of <PERSON><PERSON><PERSON>'s campaign.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_2nd_Coalition\" class=\"mw-redirect\" title=\"War of the 2nd Coalition\">War of the 2nd Coalition</a>: French troops <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Zurich\" title=\"Second Battle of Zurich\">defeat</a> Austro-Russian forces, leading to the collapse of <PERSON><PERSON><PERSON>'s campaign.", "links": [{"title": "War of the 2nd Coalition", "link": "https://wikipedia.org/wiki/War_of_the_2nd_Coalition"}, {"title": "Second Battle of Zurich", "link": "https://wikipedia.org/wiki/Second_Battle_of_Zurich"}]}, {"year": "1810", "text": "A new Act of Succession is adopted by the Riksdag of the Estates, and <PERSON> becomes heir to the Swedish throne.", "html": "1810 - A new <a href=\"https://wikipedia.org/wiki/Swedish_Act_of_Succession\" title=\"Swedish Act of Succession\">Act of Succession</a> is adopted by the Riksdag of the Estates, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes heir to the Swedish throne.", "no_year_html": "A new <a href=\"https://wikipedia.org/wiki/Swedish_Act_of_Succession\" title=\"Swedish Act of Succession\">Act of Succession</a> is adopted by the Riksdag of the Estates, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes heir to the Swedish throne.", "links": [{"title": "Swedish Act of Succession", "link": "https://wikipedia.org/wiki/Swedish_Act_of_Succession"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON> publishes the third of his <PERSON><PERSON> Mira<PERSON>is papers, introducing the special theory of relativity.", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes the third of his <a href=\"https://wikipedia.org/wiki/Annus_Mirabilis_papers\" class=\"mw-redirect\" title=\"Annus Mirabilis papers\">Annus Mirabilis papers</a>, introducing the special theory of relativity.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Einstein\"><PERSON></a> publishes the third of his <a href=\"https://wikipedia.org/wiki/Annus_Mirabilis_papers\" class=\"mw-redirect\" title=\"Annus Mirabilis papers\">Annus Mirabilis papers</a>, introducing the special theory of relativity.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Annus Mira<PERSON>is papers", "link": "https://wikipedia.org/wiki/Annus_Mirabilis_papers"}]}, {"year": "1907", "text": "Four months after the 1907 Imperial Conference, New Zealand and Newfoundland are promoted from colonies to dominions within the British Empire.", "html": "1907 - Four months after the <a href=\"https://wikipedia.org/wiki/1907_Imperial_Conference\" title=\"1907 Imperial Conference\">1907 Imperial Conference</a>, New Zealand and Newfoundland are promoted from colonies to dominions within the British Empire.", "no_year_html": "Four months after the <a href=\"https://wikipedia.org/wiki/1907_Imperial_Conference\" title=\"1907 Imperial Conference\">1907 Imperial Conference</a>, New Zealand and Newfoundland are promoted from colonies to dominions within the British Empire.", "links": [{"title": "1907 Imperial Conference", "link": "https://wikipedia.org/wiki/1907_Imperial_Conference"}]}, {"year": "1914", "text": "The United States Federal Trade Commission is established by the Federal Trade Commission Act.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/Federal_Trade_Commission\" title=\"Federal Trade Commission\">United States Federal Trade Commission</a> is established by the <a href=\"https://wikipedia.org/wiki/Federal_Trade_Commission_Act\" class=\"mw-redirect\" title=\"Federal Trade Commission Act\">Federal Trade Commission Act</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_Trade_Commission\" title=\"Federal Trade Commission\">United States Federal Trade Commission</a> is established by the <a href=\"https://wikipedia.org/wiki/Federal_Trade_Commission_Act\" class=\"mw-redirect\" title=\"Federal Trade Commission Act\">Federal Trade Commission Act</a>.", "links": [{"title": "Federal Trade Commission", "link": "https://wikipedia.org/wiki/Federal_Trade_Commission"}, {"title": "Federal Trade Commission Act", "link": "https://wikipedia.org/wiki/Federal_Trade_Commission_Act"}]}, {"year": "1917", "text": "World War I: The Battle of Polygon Wood begins.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Polygon_Wood\" title=\"Battle of Polygon Wood\">Battle of Polygon Wood</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Polygon_Wood\" title=\"Battle of Polygon Wood\">Battle of Polygon Wood</a> begins.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Polygon Wood", "link": "https://wikipedia.org/wiki/Battle_of_Polygon_Wood"}]}, {"year": "1918", "text": "World War I: The Meuse-Argonne Offensive began which would last until the total surrender of German forces.", "html": "1918 - World War I: The <a href=\"https://wikipedia.org/wiki/Meuse-Argonne_Offensive\" class=\"mw-redirect\" title=\"Meuse-Argonne Offensive\">Meuse-Argonne Offensive</a> began which would last until the total surrender of German forces.", "no_year_html": "World War I: The <a href=\"https://wikipedia.org/wiki/Meuse-Argonne_Offensive\" class=\"mw-redirect\" title=\"Meuse-Argonne Offensive\">Meuse-Argonne Offensive</a> began which would last until the total surrender of German forces.", "links": [{"title": "Meuse-Argonne Offensive", "link": "https://wikipedia.org/wiki/Meuse-Argonne_Offensive"}]}, {"year": "1923", "text": "The German government calls off the passive resistance to the French and Belgian occupation of the Ruhr.", "html": "1923 - The German government calls off the passive resistance to the French and Belgian <a href=\"https://wikipedia.org/wiki/Occupation_of_the_Ruhr\" title=\"Occupation of the Ruhr\">occupation of the Ruhr</a>.", "no_year_html": "The German government calls off the passive resistance to the French and Belgian <a href=\"https://wikipedia.org/wiki/Occupation_of_the_Ruhr\" title=\"Occupation of the Ruhr\">occupation of the Ruhr</a>.", "links": [{"title": "Occupation of the Ruhr", "link": "https://wikipedia.org/wiki/Occupation_of_the_Ruhr"}]}, {"year": "1933", "text": "As gangster <PERSON> surrenders to the FBI, he shouts out, \"Don't shoot, G-Men!\", which becomes a nickname for FBI agents.", "html": "1933 - As gangster <a href=\"https://wikipedia.org/wiki/Machine_Gun_<PERSON>_(gangster)\" title=\"Machine Gun Kelly (gangster)\">Machine Gun <PERSON></a> surrenders to the FBI, he shouts out, \"Don't shoot, <a href=\"https://wikipedia.org/wiki/G-Man_(slang)\" class=\"mw-redirect\" title=\"G-Man (slang)\">G-Men</a>!\", which becomes a nickname for FBI agents.", "no_year_html": "As gangster <a href=\"https://wikipedia.org/wiki/Machine_Gun_<PERSON>_(gangster)\" title=\"Machine Gun Kelly (gangster)\">Machine Gun <PERSON></a> surrenders to the FBI, he shouts out, \"Don't shoot, <a href=\"https://wikipedia.org/wiki/G-Man_(slang)\" class=\"mw-redirect\" title=\"G-Man (slang)\">G-Men</a>!\", which becomes a nickname for FBI agents.", "links": [{"title": "<PERSON> Gun <PERSON> (gangster)", "link": "https://wikipedia.org/wiki/Machine_Gun_<PERSON>_(gangster)"}, {"title": "G-Man (slang)", "link": "https://wikipedia.org/wiki/G-Man_(slang)"}]}, {"year": "1936", "text": "Spanish Civil War: Lluis Companys reshuffles the Generalitat de Catalunya, with the marxist POUM and anarcho-syndicalist CNT joining the government.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Lluis_Companys\" class=\"mw-redirect\" title=\"Lluis Companys\">Lluis Companys</a> <a href=\"https://wikipedia.org/wiki/Cabinet_reshuffle\" title=\"Cabinet reshuffle\">reshuffles</a> the <a href=\"https://wikipedia.org/wiki/Generalitat_de_Catalunya\" title=\"Generalitat de Catalunya\">Generalitat de Catalunya</a>, with the <a href=\"https://wikipedia.org/wiki/Libertarian_Marxism\" class=\"mw-redirect\" title=\"Libertarian Marxism\">marxist</a> <a href=\"https://wikipedia.org/wiki/POUM\" title=\"POUM\">POUM</a> and <a href=\"https://wikipedia.org/wiki/Anarcho-syndicalist\" class=\"mw-redirect\" title=\"Anarcho-syndicalist\">anarcho-syndicalist</a> <a href=\"https://wikipedia.org/wiki/National_Confederation_of_Labor\" class=\"mw-redirect\" title=\"National Confederation of Labor\">CNT</a> joining the government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Lluis_Companys\" class=\"mw-redirect\" title=\"Lluis Companys\">Lluis Companys</a> <a href=\"https://wikipedia.org/wiki/Cabinet_reshuffle\" title=\"Cabinet reshuffle\">reshuffles</a> the <a href=\"https://wikipedia.org/wiki/Generalitat_de_Catalunya\" title=\"Generalitat de Catalunya\">Generalitat de Catalunya</a>, with the <a href=\"https://wikipedia.org/wiki/Libertarian_Marxism\" class=\"mw-redirect\" title=\"Libertarian Marxism\">marxist</a> <a href=\"https://wikipedia.org/wiki/POUM\" title=\"POUM\">POUM</a> and <a href=\"https://wikipedia.org/wiki/Anarcho-syndicalist\" class=\"mw-redirect\" title=\"Anarcho-syndicalist\">anarcho-syndicalist</a> <a href=\"https://wikipedia.org/wiki/National_Confederation_of_Labor\" class=\"mw-redirect\" title=\"National Confederation of Labor\">CNT</a> joining the government.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Lluis Companys", "link": "https://wikipedia.org/wiki/Lluis_Companys"}, {"title": "Cabinet reshuffle", "link": "https://wikipedia.org/wiki/Cabinet_reshuffle"}, {"title": "Generalitat de Catalunya", "link": "https://wikipedia.org/wiki/Generalitat_de_Catalunya"}, {"title": "Libertarian Marxism", "link": "https://wikipedia.org/wiki/Libertarian_Marxism"}, {"title": "POUM", "link": "https://wikipedia.org/wiki/POUM"}, {"title": "Anarcho-syndicalist", "link": "https://wikipedia.org/wiki/Anarcho-syndicalist"}, {"title": "National Confederation of Labor", "link": "https://wikipedia.org/wiki/National_Confederation_of_Labor"}]}, {"year": "1942", "text": "Holocaust: Senior SS official <PERSON> issues a memorandum detailing how Jews should be \"evacuated\".", "html": "1942 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">Holocaust</a>: Senior SS official August <PERSON> issues <a href=\"https://wikipedia.org/wiki/August_Frank_memorandum\" title=\"August Frank memorandum\">a memorandum</a> detailing how Jews should be \"evacuated\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">Holocaust</a>: Senior SS official August <PERSON> issues <a href=\"https://wikipedia.org/wiki/August_Frank_memorandum\" title=\"August Frank memorandum\">a memorandum</a> detailing how Jews should be \"evacuated\".", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "August Frank memorandum", "link": "https://wikipedia.org/wiki/August_<PERSON>_memorandum"}]}, {"year": "1950", "text": "Korean War: United Nations troops recapture Seoul from North Korean forces.", "html": "1950 - Korean War: United Nations troops <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Seoul\" title=\"Second Battle of Seoul\">recapture Seoul</a> from North Korean forces.", "no_year_html": "Korean War: United Nations troops <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Seoul\" title=\"Second Battle of Seoul\">recapture Seoul</a> from North Korean forces.", "links": [{"title": "Second Battle of Seoul", "link": "https://wikipedia.org/wiki/Second_Battle_of_Seoul"}]}, {"year": "1953", "text": "Rationing of sugar in the United Kingdom ends.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Rationing_in_the_United_Kingdom\" title=\"Rationing in the United Kingdom\">Rationing</a> of sugar in the United Kingdom ends.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rationing_in_the_United_Kingdom\" title=\"Rationing in the United Kingdom\">Rationing</a> of sugar in the United Kingdom ends.", "links": [{"title": "Rationing in the United Kingdom", "link": "https://wikipedia.org/wiki/Rationing_in_the_United_Kingdom"}]}, {"year": "1954", "text": "The Japanese rail ferry Tōya Maru sinks during a typhoon in the Tsugaru Strait, Japan, killing 1,172.", "html": "1954 - The Japanese rail ferry <i><a href=\"https://wikipedia.org/wiki/T%C5%8Dya_Maru\" title=\"Tōya Maru\"><PERSON><PERSON><PERSON></a></i> sinks during a typhoon in the Tsugaru Strait, Japan, killing 1,172.", "no_year_html": "The Japanese rail ferry <i><a href=\"https://wikipedia.org/wiki/T%C5%8Dya_Maru\" title=\"Tōya Maru\"><PERSON><PERSON><PERSON></a></i> sinks during a typhoon in the Tsugaru Strait, Japan, killing 1,172.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C5%8Dya_Maru"}]}, {"year": "1959", "text": "Typhoon <PERSON>, the strongest typhoon to hit Japan in recorded history, makes landfall, killing 4,580 people and leaving nearly 1.6 million others homeless.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Typhoon_Vera\" title=\"Typhoon Vera\">Typhoon <PERSON></a>, the strongest typhoon to hit Japan in recorded history, makes landfall, killing 4,580 people and leaving nearly 1.6 million others homeless.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Typhoon_Vera\" title=\"Typhoon Vera\">Typhoon <PERSON></a>, the strongest typhoon to hit Japan in recorded history, makes landfall, killing 4,580 people and leaving nearly 1.6 million others homeless.", "links": [{"title": "Typhoon Vera", "link": "https://wikipedia.org/wiki/Typhoon_Vera"}]}, {"year": "1960", "text": "In Chicago, the first televised debate takes place between presidential candidates <PERSON> and <PERSON>.", "html": "1960 - In Chicago, the first televised <a href=\"https://wikipedia.org/wiki/United_States_presidential_debates#1960_<PERSON>-<PERSON>_debates\" title=\"United States presidential debates\">debate</a> takes place between presidential candidates <PERSON> and <PERSON>.", "no_year_html": "In Chicago, the first televised <a href=\"https://wikipedia.org/wiki/United_States_presidential_debates#1960_<PERSON>-<PERSON>_debates\" title=\"United States presidential debates\">debate</a> takes place between presidential candidates <PERSON> and <PERSON>.", "links": [{"title": "United States presidential debates", "link": "https://wikipedia.org/wiki/United_States_presidential_debates#1960_<PERSON>-<PERSON>_debates"}]}, {"year": "1978", "text": "Air Caribbean Flight 309 crashes in Residencial Las Casas in San Juan, Puerto Rico, killing six.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Air_Caribbean_Flight_309\" title=\"Air Caribbean Flight 309\">Air Caribbean Flight 309</a> crashes in <a href=\"https://wikipedia.org/wiki/Residencial_Las_Casas\" title=\"Residencial Las Casas\">Residencial Las Casas</a> in <a href=\"https://wikipedia.org/wiki/San_Juan,_Puerto_Rico\" title=\"San Juan, Puerto Rico\">San Juan, Puerto Rico</a>, killing six.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Caribbean_Flight_309\" title=\"Air Caribbean Flight 309\">Air Caribbean Flight 309</a> crashes in <a href=\"https://wikipedia.org/wiki/Residencial_Las_Casas\" title=\"Residencial Las Casas\">Residencial Las Casas</a> in <a href=\"https://wikipedia.org/wiki/San_Juan,_Puerto_Rico\" title=\"San Juan, Puerto Rico\">San Juan, Puerto Rico</a>, killing six.", "links": [{"title": "Air Caribbean Flight 309", "link": "https://wikipedia.org/wiki/Air_Caribbean_Flight_309"}, {"title": "Residencial Las Casas", "link": "https://wikipedia.org/wiki/Residencial_Las_Casas"}, {"title": "San Juan, Puerto Rico", "link": "https://wikipedia.org/wiki/San_Juan,_Puerto_Rico"}]}, {"year": "1980", "text": "A terrorist bombing at the Oktoberfest in Munich, Germany, kills 13 people and injures 213 others.", "html": "1980 - A <a href=\"https://wikipedia.org/wiki/Oktoberfest_bombing\" title=\"Oktoberfest bombing\">terrorist bombing</a> at the <a href=\"https://wikipedia.org/wiki/Oktoberfest\" title=\"Oktoberfest\">Oktoberfest</a> in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, Germany, kills 13 people and injures 213 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Oktoberfest_bombing\" title=\"Oktoberfest bombing\">terrorist bombing</a> at the <a href=\"https://wikipedia.org/wiki/Oktoberfest\" title=\"Oktoberfest\">Oktoberfest</a> in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, Germany, kills 13 people and injures 213 others.", "links": [{"title": "Oktoberfest bombing", "link": "https://wikipedia.org/wiki/Oktoberfest_bombing"}, {"title": "Oktoberfest", "link": "https://wikipedia.org/wiki/Oktoberfest"}, {"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}]}, {"year": "1983", "text": "Soviet Air Force officer <PERSON><PERSON> identifies a report of an incoming nuclear missile as a computer error and not an American first strike, thus preventing nuclear war.", "html": "1983 - Soviet Air Force officer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> identifies a report of an incoming nuclear missile as a <a href=\"https://wikipedia.org/wiki/1983_Soviet_nuclear_false_alarm_incident\" title=\"1983 Soviet nuclear false alarm incident\">computer error and not an American first strike</a>, thus preventing nuclear war.", "no_year_html": "Soviet Air Force officer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> identifies a report of an incoming nuclear missile as a <a href=\"https://wikipedia.org/wiki/1983_Soviet_nuclear_false_alarm_incident\" title=\"1983 Soviet nuclear false alarm incident\">computer error and not an American first strike</a>, thus preventing nuclear war.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "1983 Soviet nuclear false alarm incident", "link": "https://wikipedia.org/wiki/1983_Soviet_nuclear_false_alarm_incident"}]}, {"year": "1983", "text": "Soyuz 7K-ST No. 16L, intended to launch a crew to the Salyut 7 space station, explodes on the launch pad. The launch escape system is activated before the Soyuz-U rocket explodes, saving the crew.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Soyuz_7K-ST_No._16L\" class=\"mw-redirect\" title=\"Soyuz 7K-ST No. 16L\">Soyuz 7K-ST No. 16L</a>, intended to launch a crew to the <a href=\"https://wikipedia.org/wiki/Salyut_7\" title=\"Salyut 7\">Salyut 7</a> space station, explodes on the launch pad. The <a href=\"https://wikipedia.org/wiki/Launch_escape_system\" title=\"Launch escape system\">launch escape system</a> is activated before the <a href=\"https://wikipedia.org/wiki/Soyuz-U\" title=\"Soyuz-U\">Soyuz-U</a> rocket explodes, saving the crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soyuz_7K-ST_No._16L\" class=\"mw-redirect\" title=\"Soyuz 7K-ST No. 16L\">Soyuz 7K-ST No. 16L</a>, intended to launch a crew to the <a href=\"https://wikipedia.org/wiki/Salyut_7\" title=\"Salyut 7\">Salyut 7</a> space station, explodes on the launch pad. The <a href=\"https://wikipedia.org/wiki/Launch_escape_system\" title=\"Launch escape system\">launch escape system</a> is activated before the <a href=\"https://wikipedia.org/wiki/Soyuz-U\" title=\"Soyuz-U\">Soyuz-U</a> rocket explodes, saving the crew.", "links": [{"title": "Soyuz 7K-ST No. 16L", "link": "https://wikipedia.org/wiki/Soyuz_7K-ST_No._16L"}, {"title": "Salyut 7", "link": "https://wikipedia.org/wiki/Salyut_7"}, {"title": "Launch escape system", "link": "https://wikipedia.org/wiki/Launch_escape_system"}, {"title": "Soyuz-U", "link": "https://wikipedia.org/wiki/Soyuz-U"}]}, {"year": "1984", "text": "The United Kingdom and China agree to a transfer of sovereignty over Hong Kong, to take place in 1997.", "html": "1984 - The United Kingdom and China agree to a <a href=\"https://wikipedia.org/wiki/Transfer_of_sovereignty_over_Hong_Kong\" class=\"mw-redirect\" title=\"Transfer of sovereignty over Hong Kong\">transfer of sovereignty over Hong Kong</a>, to take place in 1997.", "no_year_html": "The United Kingdom and China agree to a <a href=\"https://wikipedia.org/wiki/Transfer_of_sovereignty_over_Hong_Kong\" class=\"mw-redirect\" title=\"Transfer of sovereignty over Hong Kong\">transfer of sovereignty over Hong Kong</a>, to take place in 1997.", "links": [{"title": "Transfer of sovereignty over Hong Kong", "link": "https://wikipedia.org/wiki/Transfer_of_sovereignty_over_Hong_Kong"}]}, {"year": "1992", "text": "A Nigerian Air Force Lockheed C-130 Hercules crashes in Ejigbo, Lagos, killing 159.", "html": "1992 - A <a href=\"https://wikipedia.org/wiki/Nigerian_Air_Force\" title=\"Nigerian Air Force\">Nigerian Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">Lockheed C-130 Hercules</a> <a href=\"https://wikipedia.org/wiki/1992_Nigerian_Air_Force_C-130_crash\" title=\"1992 Nigerian Air Force C-130 crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Ejigbo,_Lagos\" title=\"Ejigbo, Lagos\">Ejigbo, Lagos</a>, killing 159.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Nigerian_Air_Force\" title=\"Nigerian Air Force\">Nigerian Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">Lockheed C-130 Hercules</a> <a href=\"https://wikipedia.org/wiki/1992_Nigerian_Air_Force_C-130_crash\" title=\"1992 Nigerian Air Force C-130 crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Ejigbo,_Lagos\" title=\"Ejigbo, Lagos\">Ejigbo, Lagos</a>, killing 159.", "links": [{"title": "Nigerian Air Force", "link": "https://wikipedia.org/wiki/Nigerian_Air_Force"}, {"title": "Lockheed C-130 Hercules", "link": "https://wikipedia.org/wiki/Lockheed_C-130_Hercules"}, {"title": "1992 Nigerian Air Force C-130 crash", "link": "https://wikipedia.org/wiki/1992_Nigerian_Air_Force_C-130_crash"}, {"title": "Ejigbo, Lagos", "link": "https://wikipedia.org/wiki/Ejigbo,_Lagos"}]}, {"year": "1994", "text": "A Yakovlev Yak-40 crashes into a river near Vanavara, Russia, killing 24.", "html": "1994 - A <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>lev_Yak-40\" title=\"Yakovlev Yak-40\"><PERSON><PERSON><PERSON> Yak-40</a> <a href=\"https://wikipedia.org/wiki/1994_Vanavara_air_disaster\" class=\"mw-redirect\" title=\"1994 Vanavara air disaster\">crashes</a> into a river near <a href=\"https://wikipedia.org/wiki/Vanavara_(rural_locality)\" title=\"Vanavara (rural locality)\">Vanavara</a>, Russia, killing 24.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>lev_Yak-40\" title=\"Yakovlev Yak-40\"><PERSON><PERSON><PERSON> Yak-40</a> <a href=\"https://wikipedia.org/wiki/1994_Vanavara_air_disaster\" class=\"mw-redirect\" title=\"1994 Vanavara air disaster\">crashes</a> into a river near <a href=\"https://wikipedia.org/wiki/Vanavara_(rural_locality)\" title=\"Vanavara (rural locality)\">Vanavara</a>, Russia, killing 24.", "links": [{"title": "<PERSON><PERSON><PERSON>-40", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yak-40"}, {"title": "1994 Vanavara air disaster", "link": "https://wikipedia.org/wiki/1994_Vanavara_air_disaster"}, {"title": "Vanavara (rural locality)", "link": "https://wikipedia.org/wiki/Vanavara_(rural_locality)"}]}, {"year": "1997", "text": "A Garuda Indonesia Airbus A300 crashes near Medan airport, killing 234.", "html": "1997 - A <a href=\"https://wikipedia.org/wiki/Garuda_Indonesia\" title=\"Garuda Indonesia\">Garuda Indonesia</a> <a href=\"https://wikipedia.org/wiki/Airbus_A300\" title=\"Airbus A300\">Airbus A300</a> <a href=\"https://wikipedia.org/wiki/Garuda_Indonesia_Flight_152\" title=\"Garuda Indonesia Flight 152\">crashes</a> near Medan airport, killing 234.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Garuda_Indonesia\" title=\"Garuda Indonesia\">Garuda Indonesia</a> <a href=\"https://wikipedia.org/wiki/Airbus_A300\" title=\"Airbus A300\">Airbus A300</a> <a href=\"https://wikipedia.org/wiki/Garuda_Indonesia_Flight_152\" title=\"Garuda Indonesia Flight 152\">crashes</a> near Medan airport, killing 234.", "links": [{"title": "Garuda Indonesia", "link": "https://wikipedia.org/wiki/Garuda_Indonesia"}, {"title": "Airbus A300", "link": "https://wikipedia.org/wiki/Airbus_A300"}, {"title": "Garuda Indonesia Flight 152", "link": "https://wikipedia.org/wiki/Garuda_Indonesia_Flight_152"}]}, {"year": "1997", "text": "An earthquake strikes the Italian regions of Umbria and the Marche, causing part of the Basilica of St. Francis at Assisi to collapse.", "html": "1997 - An <a href=\"https://wikipedia.org/wiki/1997_Umbria_and_Marche_earthquake\" title=\"1997 Umbria and Marche earthquake\">earthquake</a> strikes the Italian regions of Umbria and the Marche, causing part of the Basilica of St. Francis at Assisi to collapse.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1997_Umbria_and_Marche_earthquake\" title=\"1997 Umbria and Marche earthquake\">earthquake</a> strikes the Italian regions of Umbria and the Marche, causing part of the Basilica of St. Francis at Assisi to collapse.", "links": [{"title": "1997 Umbria and Marche earthquake", "link": "https://wikipedia.org/wiki/1997_Umbria_and_Marche_earthquake"}]}, {"year": "2000", "text": "Anti-globalization protests in Prague (some 20,000 protesters) turn violent during the IMF and World Bank summits.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Anti-globalization_protests_in_Prague\" title=\"Anti-globalization protests in Prague\">Anti-globalization protests in Prague</a> (some 20,000 protesters) turn violent during the IMF and World Bank summits.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anti-globalization_protests_in_Prague\" title=\"Anti-globalization protests in Prague\">Anti-globalization protests in Prague</a> (some 20,000 protesters) turn violent during the IMF and World Bank summits.", "links": [{"title": "Anti-globalization protests in Prague", "link": "https://wikipedia.org/wiki/Anti-globalization_protests_in_Prague"}]}, {"year": "2000", "text": "The MS Express Samina sinks off Paros in the Aegean Sea killing 80 passengers.", "html": "2000 - The <a href=\"https://wikipedia.org/wiki/MS_Express_Samina\" title=\"MS Express Samina\">MS <i>Express Samina</i></a> sinks off Paros in the Aegean Sea killing 80 passengers.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/MS_Express_Samina\" title=\"MS Express Samina\">MS <i>Express Samina</i></a> sinks off Paros in the Aegean Sea killing 80 passengers.", "links": [{"title": "MS Express Samina", "link": "https://wikipedia.org/wiki/MS_Express_Samina"}]}, {"year": "2002", "text": "The overcrowded Senegalese ferry, MV Le Joola, capsizes off the coast of the Gambia killing more than 1,000.", "html": "2002 - The overcrowded Senegalese ferry, <a href=\"https://wikipedia.org/wiki/MV_Le_Joola\" title=\"MV Le Joola\">MV <i><PERSON> Jo<PERSON></i></a>, capsizes off the coast of the Gambia killing more than 1,000.", "no_year_html": "The overcrowded Senegalese ferry, <a href=\"https://wikipedia.org/wiki/MV_Le_Joola\" title=\"MV Le Joola\">MV <i><PERSON> Jo<PERSON></i></a>, capsizes off the coast of the Gambia killing more than 1,000.", "links": [{"title": "MV Le Joola", "link": "https://wikipedia.org/wiki/MV_Le_Joola"}]}, {"year": "2009", "text": "Typhoon <PERSON><PERSON><PERSON> hits the Philippines, China, Vietnam, Cambodia, Laos and Thailand, causing 700 fatalities.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Typhoon_Ketsana\" title=\"Typhoon Ketsana\">Typhoon <PERSON></a> hits the Philippines, China, Vietnam, Cambodia, Laos and Thailand, causing 700 fatalities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Typhoon_Ketsana\" title=\"Typhoon Ketsana\">Typhoon <PERSON></a> hits the Philippines, China, Vietnam, Cambodia, Laos and Thailand, causing 700 fatalities.", "links": [{"title": "Typhoon Ketsana", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "The Philippine Bar exam bombing occurred near the De La Salle University in Taft Avenue, Manila injuring 47 people.", "html": "2010 - The <a href=\"https://wikipedia.org/wiki/2010_Philippine_Bar_exam_bombing\" title=\"2010 Philippine Bar exam bombing\">Philippine Bar exam bombing</a> occurred near the <a href=\"https://wikipedia.org/wiki/De_La_Salle_University\" title=\"De La Salle University\">De La Salle University</a> in Taft Avenue, Manila injuring 47 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2010_Philippine_Bar_exam_bombing\" title=\"2010 Philippine Bar exam bombing\">Philippine Bar exam bombing</a> occurred near the <a href=\"https://wikipedia.org/wiki/De_La_Salle_University\" title=\"De La Salle University\">De La Salle University</a> in Taft Avenue, Manila injuring 47 people.", "links": [{"title": "2010 Philippine Bar exam bombing", "link": "https://wikipedia.org/wiki/2010_Philippine_Bar_exam_bombing"}, {"title": "De La Salle University", "link": "https://wikipedia.org/wiki/De_La_Salle_University"}]}, {"year": "2014", "text": "A mass kidnapping occurs in Iguala, Mexico.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/2014_Iguala_mass_kidnapping\" class=\"mw-redirect\" title=\"2014 Iguala mass kidnapping\">mass kidnapping</a> occurs in Iguala, Mexico.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2014_Iguala_mass_kidnapping\" class=\"mw-redirect\" title=\"2014 Iguala mass kidnapping\">mass kidnapping</a> occurs in Iguala, Mexico.", "links": [{"title": "2014 Iguala mass kidnapping", "link": "https://wikipedia.org/wiki/2014_Iguala_mass_kidnapping"}]}, {"year": "2022", "text": "A mass shooting occurs at a school in Izhevsk, Udmurtia, Russia, resulting in the deaths of 18 people, including 11 children.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Izhevsk_school_shooting\" title=\"Izhevsk school shooting\">A mass shooting</a> occurs at a school in <a href=\"https://wikipedia.org/wiki/Izhevsk\" title=\"Izhevsk\">Izhevsk</a>, <a href=\"https://wikipedia.org/wiki/Udmurtia\" title=\"Udmurtia\">Udmurtia</a>, Russia, resulting in the deaths of 18 people, including 11 children.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Izhevsk_school_shooting\" title=\"Izhevsk school shooting\">A mass shooting</a> occurs at a school in <a href=\"https://wikipedia.org/wiki/Izhevsk\" title=\"Izhevsk\">Izhevsk</a>, <a href=\"https://wikipedia.org/wiki/Udmurtia\" title=\"Udmurtia\">Udmurtia</a>, Russia, resulting in the deaths of 18 people, including 11 children.", "links": [{"title": "Izhevsk school shooting", "link": "https://wikipedia.org/wiki/Izhevsk_school_shooting"}, {"title": "Izhevsk", "link": "https://wikipedia.org/wiki/Izhevsk"}, {"title": "Udmurtia", "link": "https://wikipedia.org/wiki/Udmurtia"}]}, {"year": "2024", "text": "Hurricane <PERSON><PERSON> makes landfall in Perry, Florida as a category four hurricane, killing over 250 people and becoming the deadliest hurricane in the mainland United States since Katrina. ", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Hurricane_Helene\" title=\"Hurricane Helene\">Hurricane <PERSON><PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Perry,_Florida\" title=\"Perry, Florida\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a> as a <a href=\"https://wikipedia.org/wiki/Saffir-Simpson_hurricane_wind_scale#Category_4\" class=\"mw-redirect\" title=\"Saffir-Simpson hurricane wind scale\">category four hurricane</a>, killing over 250 people and becoming the deadliest hurricane in the <a href=\"https://wikipedia.org/wiki/Lower_48\" class=\"mw-redirect\" title=\"Lower 48\">mainland</a> <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> since <a href=\"https://wikipedia.org/wiki/Hurricane_Katrina\" title=\"Hurricane Katrina\">Katrina</a>. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Helene\" title=\"Hurricane Helene\">Hurricane <PERSON><PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Perry,_Florida\" title=\"Perry, Florida\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a> as a <a href=\"https://wikipedia.org/wiki/Saffir-Simpson_hurricane_wind_scale#Category_4\" class=\"mw-redirect\" title=\"Saffir-Simpson hurricane wind scale\">category four hurricane</a>, killing over 250 people and becoming the deadliest hurricane in the <a href=\"https://wikipedia.org/wiki/Lower_48\" class=\"mw-redirect\" title=\"Lower 48\">mainland</a> <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> since <a href=\"https://wikipedia.org/wiki/Hurricane_Katrina\" title=\"Hurricane Katrina\">Katrina</a>. ", "links": [{"title": "Hurricane <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hurricane_<PERSON>e"}, {"title": "Perry, Florida", "link": "https://wikipedia.org/wiki/Perry,_Florida"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "Saffir-Simpson hurricane wind scale", "link": "https://wikipedia.org/wiki/Saffir-Simpson_hurricane_wind_scale#Category_4"}, {"title": "Lower 48", "link": "https://wikipedia.org/wiki/Lower_48"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Hurricane Katrina", "link": "https://wikipedia.org/wiki/Hurricane_Katrina"}]}], "Births": [{"year": "932", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, Arab caliph (d. 975)", "html": "932 - <a href=\"https://wikipedia.org/wiki/Al-Mu%27izz_li-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>izz li-<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON></a>, Arab caliph (d. 975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Mu%27izz_li-<PERSON>_<PERSON>\" title=\"Al-<PERSON><PERSON>izz li-<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON>zz l<PERSON><PERSON><PERSON></a>, Arab caliph (d. 975)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Mu%27izz_li-<PERSON>_<PERSON>"}]}, {"year": "1329", "text": "<PERSON> of Bavaria, German queen consort (d. 1353)", "html": "1329 - <a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, German queen consort (d. 1353)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, German queen consort (d. 1353)", "links": [{"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bavaria"}]}, {"year": "1406", "text": "<PERSON>, 8th Baron <PERSON>, English soldier and politician (d. 1430)", "html": "1406 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 8th Baron <PERSON>\"><PERSON>, 8th Baron <PERSON></a>, English soldier and politician (d. 1430)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 8th Baron <PERSON>\"><PERSON>, 8th Baron <PERSON></a>, English soldier and politician (d. 1430)", "links": [{"title": "<PERSON>, 8th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_8th_Baron_<PERSON>_<PERSON>"}]}, {"year": "1462", "text": "<PERSON><PERSON><PERSON>, Count of Nevers, younger son of <PERSON>, Duke of Cleves (d. 1506)", "html": "1462 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Nevers\" title=\"<PERSON><PERSON><PERSON>, Count of Nevers\"><PERSON><PERSON><PERSON>, Count of Nevers</a>, younger son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Cleves\" title=\"<PERSON>, Duke of Cleves\"><PERSON>, Duke of Cleves</a> (d. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Nevers\" title=\"<PERSON><PERSON><PERSON>, Count of Nevers\"><PERSON><PERSON><PERSON>, Count of Nevers</a>, younger son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Cleves\" title=\"<PERSON>, Duke of Cleves\"><PERSON>, Duke of Cleves</a> (d. 1506)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Nevers", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_<PERSON>_<PERSON>"}, {"title": "<PERSON>, Duke of Cleves", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_C<PERSON>"}]}, {"year": "1526", "text": "<PERSON>, Count <PERSON><PERSON> of Zweibrücken (d. 1569)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>, Count <PERSON><PERSON> of Zweibrücken\"><PERSON>, Count <PERSON><PERSON> of Zweibrücken</a> (d. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>, Count <PERSON><PERSON> of Zweibrücken\"><PERSON>, Count <PERSON><PERSON> of Zweibrücken</a> (d. 1569)", "links": [{"title": "<PERSON>, Count <PERSON><PERSON> of Zweibrücken", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_Zweibr%C3%<PERSON><PERSON>n"}]}, {"year": "1637", "text": "<PERSON><PERSON><PERSON><PERSON>, French painter (d. 1714)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_(1637%E2%80%931714)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (1637-1714)\"><PERSON><PERSON><PERSON><PERSON></a>, French painter (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_(1637%E2%80%931714)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (1637-1714)\"><PERSON><PERSON><PERSON><PERSON></a>, French painter (d. 1714)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (1637-1714)", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(1637%E2%80%931714)"}]}, {"year": "1641", "text": "<PERSON><PERSON><PERSON><PERSON>, English plant anatomist and physiologist (d. 1712)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/Nehemiah_Grew\" title=\"Nehemiah Grew\"><PERSON><PERSON><PERSON><PERSON></a>, English plant anatomist and physiologist (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nehemia<PERSON>_Grew\" title=\"Nehemiah Grew\"><PERSON><PERSON><PERSON><PERSON></a>, English plant anatomist and physiologist (d. 1712)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nehemiah_<PERSON>rew"}]}, {"year": "1651", "text": "<PERSON>, founder of Germantown, Philadelphia (d. 1720)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of <a href=\"https://wikipedia.org/wiki/Germantown,_Philadelphia\" title=\"Germantown, Philadelphia\">Germantown, Philadelphia</a> (d. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of <a href=\"https://wikipedia.org/wiki/Germantown,_Philadelphia\" title=\"Germantown, Philadelphia\">Germantown, Philadelphia</a> (d. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Germantown, Philadelphia", "link": "https://wikipedia.org/wiki/Germantown,_Philadelphia"}]}, {"year": "1660", "text": "<PERSON>, Duke of Liegnitz (d. 1675)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Liegnitz\" title=\"<PERSON>, Duke of Liegnitz\"><PERSON>, Duke of Liegnitz</a> (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Liegnitz\" title=\"<PERSON>, Duke of Liegnitz\"><PERSON>, Duke of Liegnitz</a> (d. 1675)", "links": [{"title": "<PERSON>, Duke of Liegnitz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_Lie<PERSON>tz"}]}, {"year": "1698", "text": "<PERSON>, 3rd Duke of Devonshire (d. 1755)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Devonshire\" title=\"<PERSON>, 3rd Duke of Devonshire\"><PERSON>, 3rd Duke of Devonshire</a> (d. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Devonshire\" title=\"<PERSON>, 3rd Duke of Devonshire\"><PERSON>, 3rd Duke of Devonshire</a> (d. 1755)", "links": [{"title": "<PERSON>, 3rd Duke of Devonshire", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Devonshire"}]}, {"year": "1711", "text": "<PERSON><PERSON>, 2nd Earl <PERSON>, English politician, First Lord of the Admiralty (d. 1779)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/First_Lord_of_the_Admiralty\" title=\"First Lord of the Admiralty\">First Lord of the Admiralty</a> (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/First_Lord_of_the_Admiralty\" title=\"First Lord of the Admiralty\">First Lord of the Admiralty</a> (d. 1779)", "links": [{"title": "<PERSON><PERSON>, 2nd Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_2nd_<PERSON>_<PERSON>"}, {"title": "First Lord of the Admiralty", "link": "https://wikipedia.org/wiki/First_Lord_of_the_Admiralty"}]}, {"year": "1750", "text": "<PERSON>, 1st Baron <PERSON>, English admiral (d. 1810)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English admiral (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English admiral (d. 1810)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1758", "text": "<PERSON><PERSON><PERSON>, Argentinian physician (d. 1820)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian physician (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian physician (d. 1820)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1767", "text": "<PERSON><PERSON>, Austrian composer and conductor (d. 1835)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian composer and conductor (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian composer and conductor (d. 1835)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>zel_M%C3%BCller"}]}, {"year": "1774", "text": "<PERSON>, American gardener and environmentalist (d. 1845)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gardener and environmentalist (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gardener and environmentalist (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>seed"}]}, {"year": "1783", "text": "<PERSON>, 3rd Baron <PERSON>, English politician and literary figure (d. 1858)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English politician and literary figure (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English politician and literary figure (d. 1858)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1791", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French painter and lithographer (d. 1824)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9odore_G%C3%A9ricault\" title=\"Th<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter and lithographer (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9odore_G%C3%A9ricault\" title=\"Théod<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter and lithographer (d. 1824)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9odore_G%C3%A9ricault"}]}, {"year": "1792", "text": "<PERSON>, Irish-New Zealand explorer and politician, 1st Governor of New Zealand (d. 1842)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-New Zealand explorer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_New_Zealand\" class=\"mw-redirect\" title=\"Governor of New Zealand\">Governor of New Zealand</a> (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-New Zealand explorer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_New_Zealand\" class=\"mw-redirect\" title=\"Governor of New Zealand\">Governor of New Zealand</a> (d. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of New Zealand", "link": "https://wikipedia.org/wiki/Governor_of_New_Zealand"}]}, {"year": "1820", "text": "<PERSON><PERSON><PERSON>, Indian philosopher, painter, and academic (d. 1891)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher, painter, and academic (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher, painter, and academic (d. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 8th Premier of Quebec (d. 1923)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1843", "text": "<PERSON>, Australian author and poet (d. 1912)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, American art collector and philanthropist (d. 1931)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector and philanthropist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector and philanthropist (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, Russian physiologist and physician, Nobel Prize laureate (d. 1936)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physiologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physiologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1856", "text": "<PERSON>, Norwegian opera singer and music teacher (d. 1935)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian opera singer and music teacher (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian opera singer and music teacher (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, United States Army Officer (d. 1912)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> Officer (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> Officer (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}]}, {"year": "1865", "text": "<PERSON>, Duchess of Bedford (d. 1937)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Bedford\" title=\"<PERSON>, Duchess of Bedford\"><PERSON>, Duchess of Bedford</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Bedford\" title=\"<PERSON>, Duchess of Bedford\"><PERSON>, Duchess of Bedford</a> (d. 1937)", "links": [{"title": "<PERSON>, Duchess of Bedford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Bedford"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Armenian-French priest and composer (d. 1935)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian-French priest and composer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian-French priest and composer (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1870", "text": "<PERSON> Denmark (d. 1947)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Christian_X_of_Denmark\" title=\"Christian X of Denmark\"><PERSON> of Denmark</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_X_of_Denmark\" title=\"Christian X of Denmark\">Christian <PERSON> of Denmark</a> (d. 1947)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/Christian_X_of_Denmark"}]}, {"year": "1872", "text": "<PERSON>, American poet and lawyer (d. 1945)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and lawyer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and lawyer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Polish author and translator (d. 1940)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Wac%C5%82aw_Berent\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author and translator (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wac%C5%82aw_Berent\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author and translator (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wac%C5%82aw_Berent"}]}, {"year": "1874", "text": "<PERSON>, American photographer and activist (d. 1940)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and activist (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and activist (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, 3rd Raj of Sarawak (d. 1963)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Raj_of_Sarawak\" title=\"Raj of Sarawak\">3rd Raj of Sarawak</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Raj_of_Sarawak\" title=\"Raj of Sarawak\">3rd Raj of Sarawak</a> (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Raj of Sarawak", "link": "https://wikipedia.org/wiki/Raj_of_Sarawak"}]}, {"year": "1875", "text": "<PERSON>, English-American actor (d. 1959)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American economist, social worker, and author (d. 1957)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, social worker, and author (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, social worker, and author (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, Indian poet, lawyer, and politician (d. 1952)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian poet, lawyer, and politician (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian poet, lawyer, and politician (d. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON>, Italian neurologist and academic (d. 1963)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian neurologist and academic (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian neurologist and academic (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Swiss pianist and conductor (d. 1962)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pianist and conductor (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pianist and conductor (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON>, Belgian physician (d. 1958)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian physician (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian physician (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, German actor (d. 1942)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Canadian businessman and philanthropist (d. 1951)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, English physiologist, academic, and politician, Nobel Prize laureate (d. 1977)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Archibald Hill\"><PERSON></a>, English physiologist, academic, and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Archibald Hill\"><PERSON></a>, English physiologist, academic, and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Archibald_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1887", "text": "<PERSON>, English author and poet (d. 1958)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Spanish-American actor and director (d. 1967)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-American actor and director (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-American actor and director (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, English scientist and engineer, invented the Bouncing bomb (d. 1979)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist and engineer, invented the <a href=\"https://wikipedia.org/wiki/Bouncing_bomb\" title=\"Bouncing bomb\">Bouncing bomb</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist and engineer, invented the <a href=\"https://wikipedia.org/wiki/Bouncing_bomb\" title=\"Bouncing bomb\">Bouncing bomb</a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bouncing bomb", "link": "https://wikipedia.org/wiki/Bouncing_bomb"}]}, {"year": "1888", "text": "<PERSON><PERSON>, American journalist and author (d. 1964)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American journalist and author (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON> <PERSON><PERSON>, English poet, playwright, critic, Nobel Prize laureate (d. 1965)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"T. S. Eliot\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet, playwright, critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"T. S. Eliot\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet, playwright, critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1965)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1889", "text": "<PERSON>, Irish cartoonist (d. 1946)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cartoonist (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cartoonist (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, German philosopher and academic (d. 1976)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, English footballer and manager (d. 1959)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1891", "text": "<PERSON>, Australian politician, 12th Governor General of Australia (d. 1985)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a> (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor General of Australia", "link": "https://wikipedia.org/wiki/Governor_General_of_Australia"}]}, {"year": "1891", "text": "<PERSON>, French violinist and conductor (d. 1968)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCnch\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French violinist and conductor (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCnch\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French violinist and conductor (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_M%C3%BCnch"}]}, {"year": "1891", "text": "<PERSON>, German philosopher from the Vienna Circle (d. 1953)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher from the Vienna Circle (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher from the Vienna Circle (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American sociologist and academic (d. 1970)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ynd\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ynd"}]}, {"year": "1894", "text": "<PERSON>, American actress (d. 1929)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, German general (d. 1952)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/J%C3%BCrgen_Stroop\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German general (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BCrgen_Stroop\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German general (d. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCrgen_Stroop"}]}, {"year": "1897", "text": "<PERSON> (d. 1978)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul <PERSON>\"><PERSON></a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English lieutenant and pilot (d. 1917)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and pilot (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and pilot (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American pianist and composer (d. 1937)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, French jewelry designer (d. 1983)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French jewelry designer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French jewelry designer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American actor, singer, and dancer (d. 1980)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American bandleader and musician (d. 1963)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and musician (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and musician (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>,  Italian-American mobster (d. 1957)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mobster (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mobster (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Puerto Rican baseball player (d. 2011)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican baseball player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican baseball player (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Austrian footballer and coach (d. 1996)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and coach (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and coach (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, English historian and spy (d. 1983)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and spy (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and spy (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American singer-songwriter, musician, actor, and comedian (d. 1984)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Shug_<PERSON>\" title=\"<PERSON>g <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, musician, actor, and comedian (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, musician, actor, and comedian (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shug_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Dutch boxer (d. 1992)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch boxer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch boxer (d. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Sr., American race car driver, founded NASCAR (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American race car driver, founded <a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American race car driver, founded <a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> (d. 1992)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr."}, {"title": "NASCAR", "link": "https://wikipedia.org/wiki/NASCAR"}]}, {"year": "1909", "text": "<PERSON><PERSON> <PERSON><PERSON>, American lieutenant, lawyer, and politician (d. 1977)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American lieutenant, lawyer, and politician (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American lieutenant, lawyer, and politician (d. 1977)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American sportscaster (d. 1975)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American ice hockey player (d. 1998)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Italian skier and mountaineer (d. 2009)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian skier and mountaineer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian skier and mountaineer (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1914", "text": "<PERSON>, American fitness expert (d. 2011)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fitness expert (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fitness expert (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Canadian journalist and politician (d. 1976)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/R%C3%A9al_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9al_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9al_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Vietnamese-French philosopher and theorist (d. 1993)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Tran Du<PERSON>\"><PERSON><PERSON> <PERSON></a>, Vietnamese-French philosopher and theorist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"T<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Vietnamese-French philosopher and theorist (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English businessman and television host, founded the Miss World (d. 2000)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and television host, founded the <a href=\"https://wikipedia.org/wiki/Miss_World\" title=\"Miss World\">Miss World</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and television host, founded the <a href=\"https://wikipedia.org/wiki/Miss_World\" title=\"Miss World\">Miss World</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Miss World", "link": "https://wikipedia.org/wiki/Miss_World"}]}, {"year": "1919", "text": "<PERSON>, American actress (d. 1980)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Spanish poet and author (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish poet and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish poet and author (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Greek actor (d. 1985)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Prince of Russia (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Russia\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Russia\"><PERSON>, Prince of Russia</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Russia\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Russia\"><PERSON>, Prince of Russia</a> (d. 2014)", "links": [{"title": "<PERSON>, Prince of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Russia"}]}, {"year": "1923", "text": "<PERSON>, Indian actor, director, producer, and screenwriter (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Anand\"><PERSON></a>, Indian actor, director, producer, and screenwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dev_<PERSON>\" title=\"<PERSON> Anand\"><PERSON></a>, Indian actor, director, producer, and screenwriter (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Anand"}]}, {"year": "1923", "text": "<PERSON>, <PERSON>, English cricketer, lawyer, and judge (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English cricketer, lawyer, and judge (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English cricketer, lawyer, and judge (d. 2015)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English businessman and diplomat (d. 2024)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English businessman and diplomat (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English businessman and diplomat (d. 2024)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)"}]}, {"year": "1924", "text": "<PERSON>, Swiss physicist, inventor and businessman (d. 1997)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist, inventor and businessman (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist, inventor and businessman (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American-Canadian ice hockey player (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian ice hockey player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian ice hockey player (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American singer-songwriter, guitarist, actor, and race car driver (d. 1982)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, actor, and race car driver (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, actor, and race car driver (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American singer and actress (d. 2000)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Julie <PERSON>\"><PERSON></a>, American singer and actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Julie <PERSON>\"><PERSON></a>, American singer and actress (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Austrian philologist and academic (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philologist and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philologist and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American physician and educator, co-invented Gatorade (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and educator, co-invented <a href=\"https://wikipedia.org/wiki/Gatorade\" title=\"Gatorade\">Gatorade</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and educator, co-invented <a href=\"https://wikipedia.org/wiki/Gatorade\" title=\"Gatorade\">Gatorade</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gatorade", "link": "https://wikipedia.org/wiki/Gatorade"}]}, {"year": "1927", "text": "<PERSON>, American actor (d. 1994)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neal_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON>_(actor)"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Italian footballer and manager (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zot"}]}, {"year": "1928", "text": "<PERSON>, Belgian actor (d. 2019)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Belgian actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Belgian actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, American football player (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American actor (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English mountaineer and author (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(climber)\" title=\"<PERSON> (climber)\"><PERSON></a>, English mountaineer and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(climber)\" title=\"<PERSON> (climber)\"><PERSON></a>, English mountaineer and author (d. 2020)", "links": [{"title": "<PERSON> (climber)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(climber)"}]}, {"year": "1931", "text": "<PERSON>, American sex offender (d. 2008)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sex offender (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sex offender (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actress (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actress (d. 1987)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Indian economist and politician, 13th Prime Minister of India (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian economist and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian economist and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1932", "text": "<PERSON>, Russian author and poet (d. 2018)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English golfer and architect", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer and architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer and architect", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English cricketer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1935", "text": "<PERSON>, American actor (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2013)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1935", "text": "<PERSON>, Irish politician (d. 2007)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American sailor and songwriter (d. 2010)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and songwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, South African academic and politician, 8th First Lady of South Africa (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Madikizela-Mandela\" title=\"<PERSON>nie Madikizela-Mandela\"><PERSON><PERSON></a>, South African academic and politician, 8th <a href=\"https://wikipedia.org/wiki/First_Lady_of_South_Africa\" title=\"First Lady of South Africa\">First Lady of South Africa</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Madikizela-Mandela\" title=\"Winnie Madikizela-Mandela\"><PERSON><PERSON></a>, South African academic and politician, 8th <a href=\"https://wikipedia.org/wiki/First_Lady_of_South_Africa\" title=\"First Lady of South Africa\">First Lady of South Africa</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON>-Mandela", "link": "https://wikipedia.org/wiki/Winnie_Madikizela-Mandela"}, {"title": "First Lady of South Africa", "link": "https://wikipedia.org/wiki/First_Lady_of_South_Africa"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Russian banker and politician, 11th Premier of the Soviet Union (d. 2003)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian banker and politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_the_Soviet_Union\" title=\"Premier of the Soviet Union\">Premier of the Soviet Union</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian banker and politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_the_Soviet_Union\" title=\"Premier of the Soviet Union\">Premier of the Soviet Union</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Premier of the Soviet Union", "link": "https://wikipedia.org/wiki/Premier_of_the_Soviet_Union"}]}, {"year": "1937", "text": "<PERSON>, American film producer and agent (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer and agent (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer and agent (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, New Zealand-Australian ballerina and educator (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Aldous\" title=\"<PERSON><PERSON> Aldous\"><PERSON><PERSON></a>, New Zealand-Australian ballerina and educator (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Aldous\" title=\"<PERSON>tte Aldous\"><PERSON><PERSON></a>, New Zealand-Australian ballerina and educator (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucette_Aldous"}]}, {"year": "1938", "text": "<PERSON>, American actor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Norwegian journalist (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English actor and screenwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Italian violinist and conductor", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Jamaican-English model and actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-English model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-English model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American country music singer-songwriter and guitarist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Kent_M<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_M<PERSON>\" title=\"<PERSON>Cord\"><PERSON></a>, American actor", "links": [{"title": "Kent McCord", "link": "https://wikipedia.org/wiki/Kent_M<PERSON>ord"}]}, {"year": "1942", "text": "<PERSON>, American scholar of Chicana cultural theory (d. 2004)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._Anzald%C3%BAa\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American scholar of Chicana cultural theory (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._Anzald%C3%BAa\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American scholar of Chicana cultural theory (d. 2004)", "links": [{"title": "<PERSON> E<PERSON>", "link": "https://wikipedia.org/wiki/Gloria_E._Anzald%C3%BAa"}]}, {"year": "1943", "text": "<PERSON>, Australian cricketer and sportscaster", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Australian racing driver", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American politician, 22nd Governor of Arizona", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 22nd <a href=\"https://wikipedia.org/wiki/Governor_of_Arizona\" class=\"mw-redirect\" title=\"Governor of Arizona\">Governor of Arizona</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 22nd <a href=\"https://wikipedia.org/wiki/Governor_of_Arizona\" class=\"mw-redirect\" title=\"Governor of Arizona\">Governor of Arizona</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Arizona", "link": "https://wikipedia.org/wiki/Governor_of_Arizona"}]}, {"year": "1944", "text": "<PERSON>, English geologist and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Nions\" title=\"<PERSON>\"><PERSON></a>, English geologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Nions\" title=\"<PERSON>\"><PERSON></a>, English geologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Keith_O%27Nions"}]}, {"year": "1944", "text": "<PERSON>, English journalist and game show host", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and game show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian academic and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Brazilian singer (d. 2022)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"G<PERSON> Costa\"><PERSON><PERSON></a>, Brazilian singer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American activist and author (d. 2005)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian actor, playwright, and composer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, playwright, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, playwright, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Nepalese politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nepalese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nepalese politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Haitian Prime Minister", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian Prime Minister", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian Prime Minister", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American basketball player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer and actress (d. 2015)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, French singer and actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American swimmer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>-<PERSON>, English-Australian singer-songwriter and actress (d. 2022)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and actress (d. 2022)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech politician, diplomat, cosmonaut and military pilot", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>im%C3%ADr_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech politician, diplomat, cosmonaut and military pilot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>im%C3%ADr_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech politician, diplomat, cosmonaut and military pilot", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vladim%C3%ADr_Remek"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Clodoaldo"}]}, {"year": "1949", "text": "<PERSON>, Australian singer and journalist (d. 2013)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and journalist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and journalist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American novelist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, English journalist and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON><PERSON> Walters", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Walters"}]}, {"year": "1950", "text": "<PERSON>, New Zealand rugby player (d. 2020)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English footballer and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1951)\" title=\"<PERSON> (footballer, born 1951)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1951)\" title=\"<PERSON> (footballer, born 1951)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1951)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1951)"}]}, {"year": "1951", "text": "<PERSON>, Scottish singer-songwriter and drummer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Irish singer and actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American biologist and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Douglas <PERSON>\"><PERSON></a>, American biologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English police officer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(police_officer)\" title=\"<PERSON> (police officer)\"><PERSON></a>, English police officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(police_officer)\" title=\"<PERSON> (police officer)\"><PERSON></a>, English police officer", "links": [{"title": "<PERSON> (police officer)", "link": "https://wikipedia.org/wiki/<PERSON>(police_officer)"}]}, {"year": "1954", "text": "<PERSON>, American guitarist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American baseball player, manager, and sportscaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, manager, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, manager, and sportscaster", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_(baseball)"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Mexican-American singer-songwriter and guitarist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American race car driver and engineer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American author and illustrator", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American photographer and director", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, German figure skater and journalist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German figure skater and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German figure skater and journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON>rne"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American singer-songwriter (d. 1980)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Crash\" title=\"Darby Crash\"><PERSON><PERSON></a>, American singer-songwriter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Crash\" title=\"Darby Crash\"><PERSON><PERSON></a>, American singer-songwriter (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Greek-American historian and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English footballer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American sailor and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American sailor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American sailor and politician", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1959", "text": "<PERSON>, Australian journalist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Namibian golfer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American baseball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Russian poet and translator (d. 2007)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and translator (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and translator (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v"}]}, {"year": "1960", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bein\" title=\"<PERSON>we Bein\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bein\" title=\"<PERSON>we Bein\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uwe_Bein"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Dutch academic and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch academic and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American country music singer-songwriter and guitarist (d. 2020)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American sports executive", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sports executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sports executive", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Estonian journalist and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English novelist and journalist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Self\" title=\"Will Self\"><PERSON></a>, English novelist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Self\" title=\"Will Self\"><PERSON></a>, English novelist and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American-Canadian actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Swedish ice hockey player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English author and poet", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian runner", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American guitarist and songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Pitrelli"}]}, {"year": "1962", "text": "<PERSON><PERSON>, English singer-songwriter and writer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Taiwanese singer, actor, and television host", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese singer, actor, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese singer, actor, and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, English actress and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American race car driver", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Serbian-Israeli basketball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Radisav_%C4%86ur%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian-Israeli basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Radisav_%C4%86ur%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian-Israeli basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Radisav_%C4%86ur%C4%8Di%C4%87"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Ukrainian businessman and politician, 5th President of Ukraine", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian businessman and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President of Ukraine</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian businessman and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President of Ukraine</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Ukraine", "link": "https://wikipedia.org/wiki/President_of_Ukraine"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Canadian actress and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Greek singer-songwriter and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, New Zealand jockey", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand jockey", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American football player (d. 2006)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Bosnian footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>vi%C4%87"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1995)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American ice hockey player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian footballer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English director and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian motorcycle racer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American actress and fashion designer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and fashion designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Swedish guitarist (d. 2013)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American rapper and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Bet<PERSON>_O%27Rourke\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bet<PERSON>_O%27Rourke\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beto_O%27Rourke"}]}, {"year": "1972", "text": "<PERSON>, American singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American actress, producer, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON> <PERSON>, American record producer and songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Dr._<PERSON>\" title=\"Dr. <PERSON>\">Dr. <PERSON></a>, American record producer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dr._<PERSON>\" title=\"Dr. <PERSON>\">Dr. <PERSON></a>, American record producer and songwriter", "links": [{"title": "Dr. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Scottish snooker player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish snooker player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish snooker player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Greek triple jumper", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek triple jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, German-Ecuadorian pianist and diplomat", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Ecuadorian pianist and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Ecuadorian pianist and diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American swimmer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American swimmer", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1974", "text": "<PERSON>, Estonian basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BC%C3%BCrsepp\" title=\"<PERSON>\"><PERSON></a>, Estonian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BC%C3%BCrsepp\" title=\"<PERSON>\"><PERSON></a>, Estonian basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_M%C3%BC%C3%BCrsepp"}]}, {"year": "1975", "text": "<PERSON>, Swedish singer and violinist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_H%C3%A4rde<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer and violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emma_H%C3%A4rde<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer and violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emma_H%C3%A4rdelin"}]}, {"year": "1975", "text": "<PERSON>, American director and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, German actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1976", "text": "<PERSON>, German footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Finnish bass player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Sami_V%C3%A4nsk%C3%A4\" title=\"<PERSON>\"><PERSON></a>, Finnish bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sami_V%C3%A4nsk%C3%A4\" title=\"<PERSON>\"><PERSON></a>, Finnish bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sami_V%C3%A4nsk%C3%A4"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Turkish singer-songwriter and guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Kerem_%C3%96zye%C4%9Fen\" class=\"mw-redirect\" title=\"<PERSON><PERSON>ğ<PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kerem_%C3%96zye%C4%9Fen\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kerem_%C3%96zye%C4%9Fen"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Japanese comedian and actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese comedian and actor", "links": [{"title": "A<PERSON>", "link": "https://wikipedia.org/wiki/Aka_Plu"}]}, {"year": "1978", "text": "<PERSON>, Kenyan runner", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>eruiyot\" title=\"<PERSON>eruiyo<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>eruiyot\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>t"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German sprinter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Japanese wrestler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ji"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Tongan-New Zealand rugby league player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Moimo<PERSON>\"><PERSON><PERSON><PERSON></a>, Tongan-New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mo<PERSON>\"><PERSON><PERSON><PERSON></a>, Tongan-New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1979", "text": "<PERSON>, Australian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American gymnast", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Estonian politician, 16th Prime Minister of Estonia", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Taavi_R%C3%B5ivas\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">16th Prime Minister of Estonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_R%C3%B5ivas\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">16th Prime Minister of Estonia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taavi_R%C3%B5ivas"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1979", "text": "<PERSON>, Canadian actor, director, and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Austrian racing driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Swedish ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Swedish ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Japanese professional wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Japanese professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Japanese professional wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1981", "text": "<PERSON>, Chinese singer (d. 2015)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yao Beina\"><PERSON></a>, Chinese singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yao Beina\"><PERSON></a>, Chinese singer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Yao_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter, dancer, and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_T<PERSON>tsu"}]}, {"year": "1981", "text": "<PERSON>, American tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English rugby player (d. 2024)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Italian rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Argentinian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1982)\" title=\"<PERSON> (ice hockey, born 1982)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1982)\" title=\"<PERSON> (ice hockey, born 1982)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1982)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(ice_hockey,_born_1982)"}]}, {"year": "1982", "text": "<PERSON>, Chinese actress ", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Chinese actress ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Chinese actress ", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Portuguese footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American photographer, television host, and producer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer, television host, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer, television host, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nev_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, French road bicycle racer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French road bicycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French road bicycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Swedish singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Romanian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English singer-songwriter and producer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Finnish figure skater", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1988", "text": "<PERSON>, Australian wrestler", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, English cricketer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Hungarian football defender", "html": "1991 - <a href=\"https://wikipedia.org/wiki/R%C3%A9ka_Demeter\" title=\"<PERSON><PERSON><PERSON> Demeter\"><PERSON><PERSON><PERSON></a>, Hungarian football defender", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9ka_Demeter\" title=\"<PERSON><PERSON><PERSON> De<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian football defender", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9ka_Demeter"}]}, {"year": "1991", "text": "<PERSON>, French actress, fashion model and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, fashion model and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, fashion model and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, South Korean singer and actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>-<PERSON><PERSON><PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Spanish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American swimmer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Serbian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Veljkovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Veljkovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milo%C5%A1_Veljkovi%C4%87"}]}, {"year": "1996", "text": "<PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, French tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ponchet\"><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ponchet\"><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>et"}]}, {"year": "2000", "text": "<PERSON>, American soccer player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "Princess <PERSON><PERSON> bin<PERSON>, Jordanian princess", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_bin<PERSON>_<PERSON>\" title=\"Princess <PERSON><PERSON> bin<PERSON>\">Princess <PERSON><PERSON> bin<PERSON></a>, Jordanian princess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_bin<PERSON>_<PERSON>\" title=\"Princess <PERSON><PERSON> bin<PERSON>\">Princess <PERSON><PERSON> bin<PERSON></a>, Jordanian princess", "links": [{"title": "Princess <PERSON><PERSON> bin<PERSON>", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_bint_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Chinese tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "800", "text": "<PERSON><PERSON><PERSON>, bishop of Würzburg", "html": "800 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Bishopric_of_W%C3%BCrzburg\" class=\"mw-redirect\" title=\"Bishopric of Würzburg\">Würzburg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Bishopric_of_W%C3%BCrzburg\" class=\"mw-redirect\" title=\"Bishopric of Würzburg\">Würzburg</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Be<PERSON>ulf"}, {"title": "Bishopric of Würzburg", "link": "https://wikipedia.org/wiki/Bishopric_of_W%C3%BCrzburg"}]}, {"year": "862", "text": "<PERSON> ibn <PERSON>, Muslim military leader (b. c. 790)", "html": "862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Muslim military leader (b. c. 790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Muslim military leader (b. c. 790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1241", "text": "<PERSON><PERSON>, Japanese poet", "html": "1241 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Teika\" title=\"Fujiwara no Teika\"><PERSON><PERSON> no Teika</a>, Japanese poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Teika\" title=\"Fujiwara no Teika\"><PERSON><PERSON> no Teika</a>, Japanese poet", "links": [{"title": "<PERSON><PERSON> no <PERSON>", "link": "https://wikipedia.org/wiki/Fuji<PERSON>_no_<PERSON>ika"}]}, {"year": "1290", "text": "<PERSON>, Maid of Norway Queen of Scotland (b. 1283)", "html": "1290 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Maid_of_Norway\" title=\"<PERSON>, Maid of Norway\"><PERSON>, Maid of Norway</a> Queen of Scotland (b. 1283)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Maid_of_Norway\" title=\"<PERSON>, Maid of Norway\"><PERSON>, Maid of Norway</a> Queen of Scotland (b. 1283)", "links": [{"title": "<PERSON>, Maid of Norway", "link": "https://wikipedia.org/wiki/<PERSON>,_Maid_of_Norway"}]}, {"year": "1313", "text": "<PERSON><PERSON><PERSON>, Alsatian theologian, medical doctor, and poet", "html": "1313 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Alsatian theologian, medical doctor, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Alsatian theologian, medical doctor, and poet", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1327", "text": "<PERSON><PERSON>, Italian encyclopaedist, physician and poet (b. 1257)", "html": "1327 - <a href=\"https://wikipedia.org/wiki/Ce<PERSON>_d%27Ascoli\" title=\"<PERSON><PERSON> d'Ascoli\"><PERSON><PERSON> <PERSON></a>, Italian encyclopaedist, physician and poet (b. 1257)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27Ascoli\" title=\"<PERSON><PERSON> d'Ascoli\"><PERSON><PERSON> <PERSON></a>, Italian encyclopaedist, physician and poet (b. 1257)", "links": [{"title": "<PERSON><PERSON> d'Ascoli", "link": "https://wikipedia.org/wiki/Cecco_d%27Ascoli"}]}, {"year": "1328", "text": "<PERSON>, Islamic scholar and philosopher of Harran (b. 1263)", "html": "1328 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Islamic scholar and philosopher of Harran (b. 1263)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Islamic scholar and philosopher of Harran (b. 1263)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1345", "text": "<PERSON>, Count of Hainaut", "html": "1345 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hainaut\" title=\"<PERSON>, Count of Hainaut\"><PERSON>, Count of Hainaut</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hainaut\" title=\"<PERSON>, Count of Hainaut\"><PERSON>, Count of Hainaut</a>", "links": [{"title": "<PERSON>, Count of Hainaut", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_Hai<PERSON>ut"}]}, {"year": "1371", "text": "<PERSON><PERSON>, Serbian despot", "html": "1371 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lje%C5%A1a\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian despot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l<PERSON>%C5%A1a\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian despot", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jovan_Uglje%C5%A1a"}]}, {"year": "1413", "text": "<PERSON>, Duke of Bavaria (b. 1337)", "html": "1413 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1337)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1337)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1417", "text": "<PERSON>, Italian cardinal (b. 1360)", "html": "1417 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1360)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1360)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1468", "text": "<PERSON>, Spanish cardinal and theologian (b. 1388)", "html": "1468 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, Spanish cardinal and theologian (b. 1388)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, Spanish cardinal and theologian (b. 1388)", "links": [{"title": "<PERSON> (cardinal)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(cardinal)"}]}, {"year": "1536", "text": "<PERSON><PERSON>, 46th Grandmaster of the Knights Hospitaller", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_Saint-Jaille\" title=\"<PERSON><PERSON> de Saint-Jaille\"><PERSON><PERSON> <PERSON> Saint-Jaille</a>, 46th Grandmaster of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_Saint-Jaille\" title=\"<PERSON><PERSON> de Saint-Jaille\"><PERSON><PERSON> <PERSON> Saint-Jaille</a>, 46th Grandmaster of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a>", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_de_Saint-Jaille"}, {"title": "Knights Hospitaller", "link": "https://wikipedia.org/wiki/Knights_Hospitaller"}]}, {"year": "1588", "text": "<PERSON><PERSON>, Governor of Jersey (b. 1532)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Governor of Jersey (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Governor of Jersey (b. 1532)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amias_Paulet"}]}, {"year": "1600", "text": "<PERSON>, French composer (b. 1530)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1530)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1620", "text": "<PERSON><PERSON> Emperor of China (b. 1582)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/Taichang_Emperor\" title=\"Taichang Emperor\">Taichang Emperor</a> of China (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taichang_Emperor\" title=\"Taichang Emperor\">Taichang Emperor</a> of China (b. 1582)", "links": [{"title": "Taichang Emperor", "link": "https://wikipedia.org/wiki/Tai<PERSON>_Emperor"}]}, {"year": "1623", "text": "<PERSON>, 7th Earl of Kent, English politician, Lord Lieutenant of Bedfordshire (b. 1540)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Earl_of_Kent\" title=\"<PERSON>, 7th Earl of Kent\"><PERSON>, 7th Earl of Kent</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Bedfordshire\" title=\"Lord Lieutenant of Bedfordshire\">Lord Lieutenant of Bedfordshire</a> (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Earl_of_Kent\" title=\"<PERSON>, 7th Earl of Kent\"><PERSON>, 7th Earl of Kent</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Bedfordshire\" title=\"Lord Lieutenant of Bedfordshire\">Lord Lieutenant of Bedfordshire</a> (b. 1540)", "links": [{"title": "<PERSON>, 7th Earl of Kent", "link": "https://wikipedia.org/wiki/<PERSON>,_7th_Earl_of_Kent"}, {"title": "Lord Lieutenant of Bedfordshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Bedfordshire"}]}, {"year": "1626", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese daimyō (b. 1554)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/Waki<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Waki<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1554)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waki<PERSON><PERSON>_<PERSON><PERSON><PERSON>u"}]}, {"year": "1716", "text": "<PERSON>, French mathematician and theorist (b. 1666)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theorist (b. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theorist (b. 1666)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, Spanish monk and scholar (b. 1676)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3nimo_Feij%C3%B3o_y_Montenegro\" title=\"<PERSON>\"><PERSON></a>, Spanish monk and scholar (b. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3nimo_Feij%C3%B3o_y_Montenegro\" title=\"<PERSON>\"><PERSON></a>, Spanish monk and scholar (b. 1676)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3nimo_Feij%C3%B3o_y_Montenegro"}]}, {"year": "1800", "text": "<PERSON>, American composer and educator (b. 1746)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1746)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON><PERSON><PERSON>, Slovene mathematician and physicist (b. 1754)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene mathematician and physicist (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene mathematician and physicist (b. 1754)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, American hunter and explorer (b. 1734)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hunter and explorer (b. 1734)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hunter and explorer (b. 1734)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, Marquis of Torre Tagle, Peruvian soldier and politician, 2nd President of Peru (b. 1779)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>_<PERSON>_y_<PERSON>carrero,_Marquis_of_Torre_Tagle\" class=\"mw-redirect\" title=\"<PERSON>, Marquis of Torre Tagle\"><PERSON>, Marquis of Torre Tagle</a>, Peruvian soldier and politician, 2nd President of Peru (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>_<PERSON>_y_Portocarrero,_Marquis_of_Torre_Tagle\" class=\"mw-redirect\" title=\"<PERSON>, Marquis of Torre Tagle\"><PERSON>, Marquis of Torre Tagle</a>, Peruvian soldier and politician, 2nd President of Peru (b. 1779)", "links": [{"title": "<PERSON>, Marquis of Torre Tagle", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_<PERSON>_of_Torre_<PERSON>le"}]}, {"year": "1846", "text": "<PERSON>, English abolitionist (b. 1760)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English abolitionist (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English abolitionist (b. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON> <PERSON>, German mathematician and astronomer (b. 1790)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>_M%C3%B6bius\" title=\"August <PERSON>\">August <PERSON></a>, German mathematician and astronomer (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>_M%C3%B6bius\" title=\"August <PERSON>\">August <PERSON></a>, German mathematician and astronomer (b. 1790)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_M%C3%B6bius"}]}, {"year": "1877", "text": "<PERSON>, German mathematician and physicist (b. 1809)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, German-American businessman, founded Levi Strauss & Co. (b. 1829)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Co.\" title=\"Levi Strauss &amp; Co.\">Levi Strauss &amp; Co.</a> (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Co.\" title=\"Levi Strauss &amp; Co.\">Levi Strauss &amp; Co.</a> (b. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Levi Strauss & Co.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Co."}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek-Japanese author and academic (b. 1850)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Lafcadio_Hearn\" title=\"Lafcadio Hearn\"><PERSON><PERSON>ca<PERSON></a>, Greek-Japanese author and academic (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lafcadio_Hearn\" title=\"Lafcadio Hearn\"><PERSON><PERSON><PERSON><PERSON></a>, Greek-Japanese author and academic (b. 1850)", "links": [{"title": "Lafcadio Hearn", "link": "https://wikipedia.org/wiki/Lafcadio_Hearn"}]}, {"year": "1904", "text": "<PERSON>, Canadian businessman and politician (b. 1848)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>air<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Australian politician, 17th Premier of New South Wales (b. 1863)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1935", "text": "<PERSON>, American author (b. 1859)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author (b. 1859)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Slovene-Hungarian priest and author (b. 1861)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Persa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovene-Hungarian priest and author (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Persa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovene-Hungarian priest and author (b. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_Persa"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American singer and actress (b. 1894)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, French Resistance fighter (b. 1926)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/French_Resistance\" title=\"French Resistance\">French Resistance</a> fighter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/French_Resistance\" title=\"French Resistance\">French Resistance</a> fighter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "French Resistance", "link": "https://wikipedia.org/wiki/French_Resistance"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Hungarian pianist and composer (b. 1881)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/B%C3%A9la_Bart%C3%B3k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian pianist and composer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9la_Bart%C3%B3k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian pianist and composer (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9la_Bart%C3%B3k"}]}, {"year": "1946", "text": "<PERSON>, American author and educator (b. 1869)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American author and educator (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American author and educator (b. 1869)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1947", "text": "<PERSON>, English-American author and poet (b. 1886)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author and poet (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author and poet (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, German geologist and academic (b. 1885)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geologist and academic (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geologist and academic (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Spanish philosopher, novelist, and poet (b. 1863)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish philosopher, novelist, and poet (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish philosopher, novelist, and poet (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Chinese painter and educator (b. 1895)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese painter and educator (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese painter and educator (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American tennis player (b. 1868)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American minister and author (b. 1902)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American minister and author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American minister and author (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Sri Lankan lawyer and politician, 4th Prime Minister of Sri Lanka (b. 1899)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/S._W._R._D._Bandaranaike\" title=\"S. W. R. D. Bandaranaike\">S. W. R. <PERSON><PERSON></a>, Sri Lankan lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka\" title=\"Prime Minister of Sri Lanka\">Prime Minister of Sri Lanka</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._W._R._D._Bandaranaike\" title=\"S. W. R. D. Bandaranaike\">S. W. R<PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka\" title=\"Prime Minister of Sri Lanka\">Prime Minister of Sri Lanka</a> (b. 1899)", "links": [{"title": "S. W. R. <PERSON>", "link": "https://wikipedia.org/wiki/S._W._<PERSON><PERSON>_<PERSON>._Bandaranaike"}, {"title": "Prime Minister of Sri Lanka", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka"}]}, {"year": "1959", "text": "<PERSON>, Australian general (b. 1889)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Estonian furniture designer and educator (b. 1878)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian furniture designer and educator (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian furniture designer and educator (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American politician, 5th United States Secretary of Defense (b. 1890)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">5th United States Secretary of Defense</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">5th United States Secretary of Defense</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "1965", "text": "<PERSON>, Irish soldier and pilot (b. 1898)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)\" class=\"mw-redirect\" title=\"<PERSON> (pilot)\"><PERSON></a>, Irish soldier and pilot (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(pilot)\" class=\"mw-redirect\" title=\"<PERSON> (pilot)\"><PERSON></a>, Irish soldier and pilot (b. 1898)", "links": [{"title": "<PERSON> (pilot)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)"}]}, {"year": "1968", "text": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>, Polish-Israeli neurologist and physician (b. 1902)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>hl<PERSON>-He<PERSON>\"><PERSON></a>, Polish-Israeli neurologist and physician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli neurologist and physician (b. 1902)", "links": [{"title": "Ben Shl<PERSON>-Heilprin", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian lawyer and politician, 20th Premier of Quebec (b. 1915)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1915)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish pianist (b. 1918)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_K%C4%99dra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_K%C4%99dra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_K%C4%99dra"}]}, {"year": "1972", "text": "<PERSON>, American actor and screenwriter (b. 1890)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American historian and author (b. 1891)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American race car driver (b. 1928)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Italian actress and singer (b. 1908)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress and singer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress and singer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Croatian-Swiss chemist and academic, Nobel Prize laureate (b. 1887)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>u%C5%BEi%C4%8Dka\" title=\"<PERSON>\"><PERSON></a>, Croatian-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>u%C5%BEi%C4%8Dka\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, Croatian-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leopold_Ru%C5%BEi%C4%8Dka"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Indian dancer and choreographer (b. 1900)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian dancer and choreographer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian dancer and choreographer (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Swedish physicist and academic, Nobel Prize laureate (b. 1886)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Manne_Siegbahn\" title=\"Manne Siegbahn\"><PERSON>e Siegbahn</a>, Swedish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manne_Siegbahn\" title=\"Manne Siegbahn\"><PERSON><PERSON> Siegbahn</a>, Swedish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1886)", "links": [{"title": "Manne Siegbahn", "link": "https://wikipedia.org/wiki/Manne_Siegbahn"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1979", "text": "<PERSON>, American actor (b. 1910)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian cricketer (b. 1902)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Spanish bullfighter (b. 1948)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish bullfighter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish bullfighter (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American sportscaster (b. 1913)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Indonesian footballer and manager (b. 1928)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ramang\" class=\"mw-redirect\" title=\"Ramang\"><PERSON><PERSON></a>, Indonesian footballer and manager (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ramang\" class=\"mw-redirect\" title=\"Ramang\"><PERSON><PERSON></a>, Indonesian footballer and manager (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ramang"}]}, {"year": "1987", "text": "<PERSON>, Austrian geologist, journalist, and mountaineer (b. 1912)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist, journalist, and mountaineer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist, journalist, and mountaineer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Croatian and Yugoslav football player and coach (b. 1929)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian and Yugoslav football player and coach (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian and Yugoslav football player and coach (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Zeb<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Indian singer-songwriter and producer (b. 1920)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter and producer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter and producer (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Turkish intelligence officer (b. 1932)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish intelligence officer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish intelligence officer (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Italian author and critic (b. 1907)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and critic (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and critic (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberto_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American singer and bandleader (b. 1919)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bandleader (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bandleader (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Estonian chess player (b. 1931)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON>_<PERSON>ksaar"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Romanian politician (b. 1951)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian politician (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian politician (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ceau%C8%99escu"}]}, {"year": "1997", "text": "<PERSON>, American screenwriter and producer (b. 1909)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American singer (b. 1930)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, American philanthropist (b. 1908)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Oseola_McCarty\" title=\"Oseola McCarty\"><PERSON><PERSON><PERSON></a>, American philanthropist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oseola_McCarty\" title=\"Oseola McCarty\"><PERSON>se<PERSON></a>, American philanthropist (b. 1908)", "links": [{"title": "Oseola McCarty", "link": "https://wikipedia.org/wiki/Oseola_M<PERSON>y"}]}, {"year": "2000", "text": "<PERSON>, American actor (b. 1932)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Brazilian guitarist and composer (b. 1937)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian guitarist and composer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian guitarist and composer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Swedish engineer, invented three-point safety belt (b. 1920)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish engineer, invented <a href=\"https://wikipedia.org/wiki/Three-point_safety_belt\" class=\"mw-redirect\" title=\"Three-point safety belt\">three-point safety belt</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish engineer, invented <a href=\"https://wikipedia.org/wiki/Three-point_safety_belt\" class=\"mw-redirect\" title=\"Three-point safety belt\">three-point safety belt</a> (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Three-point safety belt", "link": "https://wikipedia.org/wiki/Three-point_safety_belt"}]}, {"year": "2003", "text": "<PERSON>, American guitarist, songwriter, and producer (b. 1963)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Shawn <PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Shawn Lane\"><PERSON></a>, American guitarist, songwriter, and producer (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English singer-songwriter (b. 1949)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Canadian bodybuilder, model, and wrestler (b. 1969)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian bodybuilder, model, and wrestler (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian bodybuilder, model, and wrestler (b. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English author and screenwriter (b. 1934)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American golfer and coach (b. 1912)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and coach (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and coach (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American wartime propaganda broadcaster (b. 1916)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_D%27Aquino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wartime propaganda broadcaster (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_D%27Aquino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wartime propaganda broadcaster (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iva_Toguri_D%27Aquino"}]}, {"year": "2007", "text": "<PERSON>, American businessman (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}]}, {"year": "2008", "text": "<PERSON>, Belgian keyboard player, producer, and journalist (b. 1942)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian keyboard player, producer, and journalist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian keyboard player, producer, and journalist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actor, director, producer, and businessman (b. 1925)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and businessman (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and businessman (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English rugby player (b. 1978)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actress (b. 1910)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American sculptor, founded the City Museum (b. 1949)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor, founded the <a href=\"https://wikipedia.org/wiki/City_Museum\" title=\"City Museum\">City Museum</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor, founded the <a href=\"https://wikipedia.org/wiki/City_Museum\" title=\"City Museum\">City Museum</a> (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "City Museum", "link": "https://wikipedia.org/wiki/City_Museum"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American actress and singer (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/M%27el_Dowd\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%27el_Dowd\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%27el_Dowd"}]}, {"year": "2012", "text": "<PERSON>, Canadian physicist and politician, 17th Lieutenant Governor of Saskatchewan (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and politician, 17th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Saskatchewan\" title=\"Lieutenant Governor of Saskatchewan\">Lieutenant Governor of Saskatchewan</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and politician, 17th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Saskatchewan\" title=\"Lieutenant Governor of Saskatchewan\">Lieutenant Governor of Saskatchewan</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Saskatchewan", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Saskatchewan"}]}, {"year": "2012", "text": "<PERSON>, American historian and author (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eugene_Genovese"}]}, {"year": "2012", "text": "<PERSON>, American journalist and politician (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Steiger"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Malaysian politician, 10th <PERSON><PERSON><PERSON> of Kedah (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Menteris_Besar_of_Kedah\" class=\"mw-redirect\" title=\"List of Menteris Besar of Kedah\"><PERSON><PERSON><PERSON> of Kedah</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Menteris_Besar_of_Kedah\" class=\"mw-redirect\" title=\"List of Menteris Besar of Kedah\"><PERSON><PERSON><PERSON> of Kedah</a> (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Menteris <PERSON> of Kedah", "link": "https://wikipedia.org/wiki/List_of_<PERSON><PERSON><PERSON>_Besar_of_Kedah"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Irish hurler (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Se%C3%<PERSON>nie_Du<PERSON>an\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish hurler (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%<PERSON><PERSON>_Du<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish hurler (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%<PERSON><PERSON>_<PERSON>an"}]}, {"year": "2013", "text": "<PERSON>, Puerto Rican-American actor (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Armenian actor and director (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian actor and director (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian actor and director (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football player and coach (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American screenwriter (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter (b. 1921)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2014", "text": "<PERSON>, American astronomer and physicist (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American astronomer and physicist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American astronomer and physicist (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Georgian-American businessman (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian-American businessman (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian-American businessman (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian zoologist (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Eud%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian zoologist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eud%C3%B3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian zoologist (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eud%C3%B3<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American soldier, physician, and author (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, physician, and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, physician, and author (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Sri Lankan police officer and diplomat (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan police officer and diplomat (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan police officer and diplomat (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ana_Seneviratne"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, last known <PERSON><PERSON>' fringe-limbed treefrog (h. fl. 2005)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(frog)\" title=\"<PERSON><PERSON><PERSON> (frog)\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Endling\" title=\"Endling\">last known</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27_fringe-limbed_treefrog\" class=\"mw-redirect\" title=\"<PERSON><PERSON>' fringe-limbed treefrog\"><PERSON><PERSON>' fringe-limbed treefrog</a> (h. <abbr title=\"floruit ('flourished' - known to have been active at a particular time or during a particular period)\">fl.</abbr><span style=\"white-space:nowrap;\"> 2005</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(frog)\" title=\"<PERSON><PERSON><PERSON> (frog)\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Endling\" title=\"Endling\">last known</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27_fringe-limbed_treefrog\" class=\"mw-redirect\" title=\"<PERSON><PERSON>' fringe-limbed treefrog\"><PERSON><PERSON>' fringe-limbed treefrog</a> (h. <abbr title=\"floruit ('flourished' - known to have been active at a particular time or during a particular period)\">fl.</abbr><span style=\"white-space:nowrap;\"> 2005</span>)", "links": [{"title": "<PERSON><PERSON><PERSON> (frog)", "link": "https://wikipedia.org/wiki/To<PERSON><PERSON>_(frog)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Endling"}, {"title": "<PERSON><PERSON>' fringe-limbed treefrog", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>%27_fringe-limbed_treefrog"}]}, {"year": "2019", "text": "<PERSON>, French politician, President of France (b. 1932)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, President of France (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, President of France (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American actor (b. 1948)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1948)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}]}}