{"date": "December 4", "url": "https://wikipedia.org/wiki/December_4", "data": {"Events": [{"year": "771", "text": "Austrasian king <PERSON><PERSON> I dies, leaving his brother <PERSON><PERSON><PERSON><PERSON> as sole king of the Frankish Kingdom.", "html": "771 - <a href=\"https://wikipedia.org/wiki/Austrasia\" title=\"Austrasia\">Austrasian</a> king <a href=\"https://wikipedia.org/wiki/Carloman_I\" title=\"Carloman I\">Carloman I</a> dies, leaving his brother <a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON><PERSON>\" title=\"Charlema<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> as sole king of the <a href=\"https://wikipedia.org/wiki/Francia\" title=\"Francia\">Frankish Kingdom</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austrasia\" title=\"Austrasia\">Austrasian</a> king <a href=\"https://wikipedia.org/wiki/Carloman_I\" title=\"Carloman I\">Carlo<PERSON> I</a> dies, leaving his brother <a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON><PERSON>\" title=\"Charlemagne\"><PERSON><PERSON><PERSON><PERSON></a> as sole king of the <a href=\"https://wikipedia.org/wiki/Francia\" title=\"Francia\">Frankish Kingdom</a>.", "links": [{"title": "Austrasia", "link": "https://wikipedia.org/wiki/Austrasia"}, {"title": "Carloman I", "link": "https://wikipedia.org/wiki/Carloman_I"}, {"title": "Charlemagne", "link": "https://wikipedia.org/wiki/Charlemagne"}, {"title": "Francia", "link": "https://wikipedia.org/wiki/Francia"}]}, {"year": "963", "text": "The lay papal protonotary is elected pope and takes the name <PERSON>, being consecrated on 6 December after ordination.", "html": "963 - The lay <a href=\"https://wikipedia.org/wiki/Protonotary_apostolic\" title=\"Protonotary apostolic\">papal protonotary</a> is elected pope and takes the name <a href=\"https://wikipedia.org/wiki/Pope_Leo_VIII\" title=\"Pope Leo VIII\"><PERSON></a>, being consecrated on 6 December after ordination.", "no_year_html": "The lay <a href=\"https://wikipedia.org/wiki/Protonotary_apostolic\" title=\"Protonotary apostolic\">papal protonotary</a> is elected pope and takes the name <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_VIII\" title=\"Pope Leo VIII\"><PERSON></a>, being consecrated on 6 December after ordination.", "links": [{"title": "Protonotary apostolic", "link": "https://wikipedia.org/wiki/Protonotary_apostolic"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1110", "text": "An army led by <PERSON> of Jerusalem and <PERSON><PERSON><PERSON> the Crusader of Norway captures Sidon at the end of the First Crusade.", "html": "1110 - An army led by <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Jerusalem\" title=\"<PERSON> I of Jerusalem\"><PERSON> of Jerusalem</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>gu<PERSON>_the_Crusader\" title=\"<PERSON><PERSON><PERSON> the Crusader\"><PERSON><PERSON><PERSON> the Crusader</a> of <a href=\"https://wikipedia.org/wiki/Norway\" title=\"Norway\">Norway</a> captures <a href=\"https://wikipedia.org/wiki/Siege_of_Sidon\" title=\"Siege of Sidon\">Sidon</a> at the end of the <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>.", "no_year_html": "An army led by <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Jerusalem\" title=\"<PERSON> I of Jerusalem\"><PERSON> of Jerusalem</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>gu<PERSON>_the_Crusader\" title=\"<PERSON><PERSON><PERSON> the Crusader\"><PERSON><PERSON><PERSON> the Crusader</a> of <a href=\"https://wikipedia.org/wiki/Norway\" title=\"Norway\">Norway</a> captures <a href=\"https://wikipedia.org/wiki/Siege_of_Sidon\" title=\"Siege of Sidon\">Sidon</a> at the end of the <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>.", "links": [{"title": "<PERSON> of Jerusalem", "link": "https://wikipedia.org/wiki/Baldwin_I_of_Jerusalem"}, {"title": "<PERSON><PERSON><PERSON> the Crusader", "link": "https://wikipedia.org/wiki/<PERSON>gu<PERSON>_the_Crusader"}, {"title": "Norway", "link": "https://wikipedia.org/wiki/Norway"}, {"title": "Siege of Sidon", "link": "https://wikipedia.org/wiki/Siege_of_Sidon"}, {"title": "First Crusade", "link": "https://wikipedia.org/wiki/First_Crusade"}]}, {"year": "1259", "text": "Kings <PERSON> of France and <PERSON> of England agree to the Treaty of Paris, in which <PERSON> renounces his claims to French-controlled territory on continental Europe (including Normandy) in exchange for <PERSON> withdrawing his support for English rebels.", "html": "1259 - Kings <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> agree to the <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1259)\" title=\"Treaty of Paris (1259)\">Treaty of Paris</a>, in which <PERSON> renounces his claims to French-controlled territory on continental Europe (including <a href=\"https://wikipedia.org/wiki/Normandy\" title=\"Normandy\">Normandy</a>) in exchange for <PERSON> withdrawing his support for English rebels.", "no_year_html": "Kings <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> agree to the <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1259)\" title=\"Treaty of Paris (1259)\">Treaty of Paris</a>, in which <PERSON> renounces his claims to French-controlled territory on continental Europe (including <a href=\"https://wikipedia.org/wiki/Normandy\" title=\"Normandy\">Normandy</a>) in exchange for <PERSON> withdrawing his support for English rebels.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IX_of_France"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Treaty of Paris (1259)", "link": "https://wikipedia.org/wiki/Treaty_of_Paris_(1259)"}, {"title": "Normandy", "link": "https://wikipedia.org/wiki/Normandy"}]}, {"year": "1563", "text": "The final session of the Council of Trent is held nearly 18 years after the body held its first session on December 13, 1545.", "html": "1563 - The final session of the <a href=\"https://wikipedia.org/wiki/Council_of_Trent\" title=\"Council of Trent\">Council of Trent</a> is held nearly 18 years after the body held its first session on December 13, 1545.", "no_year_html": "The final session of the <a href=\"https://wikipedia.org/wiki/Council_of_Trent\" title=\"Council of Trent\">Council of Trent</a> is held nearly 18 years after the body held its first session on December 13, 1545.", "links": [{"title": "Council of Trent", "link": "https://wikipedia.org/wiki/Council_of_Trent"}]}, {"year": "1619", "text": "Thirty-eight colonists arrive at Berkeley Hundred, Virginia. The group's charter proclaims that the day \"be yearly and perpetually kept holy as a day of thanksgiving to Almighty God.\"", "html": "1619 - Thirty-eight <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colonists</a> arrive at <a href=\"https://wikipedia.org/wiki/Berkeley_Hundred\" title=\"Berkeley Hundred\">Berkeley Hundred</a>, <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Virginia</a>. The group's charter proclaims that the day \"be yearly and perpetually kept holy as a day of thanksgiving to Almi<PERSON> <PERSON>.\"", "no_year_html": "Thirty-eight <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colonists</a> arrive at <a href=\"https://wikipedia.org/wiki/Berkeley_Hundred\" title=\"Berkeley Hundred\">Berkeley Hundred</a>, <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Virginia</a>. The group's charter proclaims that the day \"be yearly and perpetually kept holy as a day of thanksgiving to <PERSON><PERSON><PERSON> <PERSON>.\"", "links": [{"title": "Colony", "link": "https://wikipedia.org/wiki/Colony"}, {"title": "Berkeley Hundred", "link": "https://wikipedia.org/wiki/Berkeley_Hundred"}, {"title": "Colony of Virginia", "link": "https://wikipedia.org/wiki/Colony_of_Virginia"}]}, {"year": "1623", "text": "50 Christians are executed in Edo, Japan, during the Great Martyrdom of Edo.", "html": "1623 - 50 Christians are executed in <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a>, Japan, during the <a href=\"https://wikipedia.org/wiki/Great_Martyrdom_of_Edo\" title=\"Great Martyrdom of Edo\">Great Martyrdom of Edo</a>.", "no_year_html": "50 Christians are executed in <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a>, Japan, during the <a href=\"https://wikipedia.org/wiki/Great_Martyrdom_of_Edo\" title=\"Great Martyrdom of Edo\">Great Marty<PERSON><PERSON> of Edo</a>.", "links": [{"title": "Edo (Tokyo)", "link": "https://wikipedia.org/wiki/Edo_(Tokyo)"}, {"title": "Great <PERSON><PERSON><PERSON> of Edo", "link": "https://wikipedia.org/wiki/Great_Martyrdom_of_Edo"}]}, {"year": "1676", "text": "The Battle of Lund, becomes the bloodiest battle in Scandinavian history.", "html": "1676 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Lund\" title=\"Battle of Lund\">Battle of Lund</a>, becomes the bloodiest battle in Scandinavian history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Lund\" title=\"Battle of Lund\">Battle of Lund</a>, becomes the bloodiest battle in Scandinavian history.", "links": [{"title": "Battle of Lund", "link": "https://wikipedia.org/wiki/Battle_of_Lund"}]}, {"year": "1745", "text": "<PERSON>'s army reaches Derby, its furthest point during the Second Jacobite Rising.", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s army reaches <a href=\"https://wikipedia.org/wiki/Derby\" title=\"Derby\">Derby</a>, its furthest point during the <a href=\"https://wikipedia.org/wiki/Jacobite_rising_of_1745\" title=\"Jacobite rising of 1745\">Second Jacobite Rising</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s army reaches <a href=\"https://wikipedia.org/wiki/Derby\" title=\"Derby\">Derby</a>, its furthest point during the <a href=\"https://wikipedia.org/wiki/Jacobite_rising_of_1745\" title=\"Jacobite rising of 1745\">Second Jacobite Rising</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Derby", "link": "https://wikipedia.org/wiki/Derby"}, {"title": "Jacobite rising of 1745", "link": "https://wikipedia.org/wiki/Jacobite_rising_of_1745"}]}, {"year": "1783", "text": "At Fraunces Tavern in New York City, U.S. General <PERSON> bids farewell to his officers.", "html": "1783 - At <a href=\"https://wikipedia.org/wiki/Fraunces_Tavern\" title=\"Fraunces Tavern\">Fraunces Tavern</a> in New York City, U.S. General <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Fraunces_Tavern#Washington's_farewell_to_his_officers\" title=\"Fraunces Tavern\">bids farewell to his officers</a>.", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/Fraunces_Tavern\" title=\"Fraunces Tavern\">Fraunces Tavern</a> in New York City, U.S. General <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Fraunces_Tavern#Washington's_farewell_to_his_officers\" title=\"Fraunces Tavern\">bids farewell to his officers</a>.", "links": [{"title": "Fraunces Tavern", "link": "https://wikipedia.org/wiki/Fraunces_Tavern"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "Fraunces Tavern", "link": "https://wikipedia.org/wiki/Fraunces_Tavern#Washington's_farewell_to_his_officers"}]}, {"year": "1786", "text": "Mission Santa Barbara is dedicated (on the feast day of Saint Barbara).", "html": "1786 - <a href=\"https://wikipedia.org/wiki/Mission_Santa_Barbara\" title=\"Mission Santa Barbara\">Mission Santa Barbara</a> is dedicated (on the feast day of <a href=\"https://wikipedia.org/wiki/Saint_Barbara\" title=\"Saint Barbara\">Saint Barbara</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mission_Santa_Barbara\" title=\"Mission Santa Barbara\">Mission Santa Barbara</a> is dedicated (on the feast day of <a href=\"https://wikipedia.org/wiki/Saint_Barbara\" title=\"Saint Barbara\">Saint Barbara</a>).", "links": [{"title": "Mission Santa Barbara", "link": "https://wikipedia.org/wiki/Mission_Santa_Barbara"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint_Barbara"}]}, {"year": "1791", "text": "The first edition of The Observer, the world's first Sunday newspaper, is published.", "html": "1791 - The first edition of <i><a href=\"https://wikipedia.org/wiki/The_Observer\" title=\"The Observer\">The Observer</a></i>, the world's first Sunday newspaper, is published.", "no_year_html": "The first edition of <i><a href=\"https://wikipedia.org/wiki/The_Observer\" title=\"The Observer\">The Observer</a></i>, the world's first Sunday newspaper, is published.", "links": [{"title": "The Observer", "link": "https://wikipedia.org/wiki/The_Observer"}]}, {"year": "1804", "text": "The United States House of Representatives adopts articles of impeachment against Supreme Court Justice <PERSON>.", "html": "1804 - The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>\" title=\"Impeachment of <PERSON>\">adopts</a> <a href=\"https://wikipedia.org/wiki/Articles_of_impeachment\" class=\"mw-redirect\" title=\"Articles of impeachment\">articles of impeachment</a> against <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Supreme Court Justice</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>\" title=\"Impeachment of <PERSON>\">adopts</a> <a href=\"https://wikipedia.org/wiki/Articles_of_impeachment\" class=\"mw-redirect\" title=\"Articles of impeachment\">articles of impeachment</a> against <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Supreme Court Justice</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "Impeachment of <PERSON>", "link": "https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>"}, {"title": "Articles of impeachment", "link": "https://wikipedia.org/wiki/Articles_of_impeachment"}, {"title": "Associate Justice of the Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "In the face of fierce local opposition, British Governor-General Lord <PERSON> issues a regulation declaring that anyone who abets sati in Bengal is guilty of culpable homicide.", "html": "1829 - In the face of fierce local opposition, British <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General</a> <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a> issues a regulation declaring that anyone who abets <a href=\"https://wikipedia.org/wiki/Sati_(practise)\" class=\"mw-redirect\" title=\"<PERSON>ti (practise)\">sati</a> in <a href=\"https://wikipedia.org/wiki/Bengal_Presidency\" title=\"Bengal Presidency\">Bengal</a> is guilty of <a href=\"https://wikipedia.org/wiki/Culpable_homicide\" title=\"Culpable homicide\">culpable homicide</a>.", "no_year_html": "In the face of fierce local opposition, British <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General</a> <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a> issues a regulation declaring that anyone who abets <a href=\"https://wikipedia.org/wiki/Sati_(practise)\" class=\"mw-redirect\" title=\"<PERSON>ti (practise)\">sati</a> in <a href=\"https://wikipedia.org/wiki/Bengal_Presidency\" title=\"Bengal Presidency\">Bengal</a> is guilty of <a href=\"https://wikipedia.org/wiki/Culpable_homicide\" title=\"Culpable homicide\">culpable homicide</a>.", "links": [{"title": "Governor-General of India", "link": "https://wikipedia.org/wiki/Governor-General_of_India"}, {"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>"}, {"title": "<PERSON><PERSON> (practise)", "link": "https://wikipedia.org/wiki/Sati_(practise)"}, {"title": "Bengal Presidency", "link": "https://wikipedia.org/wiki/Bengal_Presidency"}, {"title": "Culpable homicide", "link": "https://wikipedia.org/wiki/Culpable_homicide"}]}, {"year": "1861", "text": "American Civil War: The 109 electors of the several states of the Confederate States of America unanimously elect <PERSON> as President and <PERSON> as Vice President.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The 109 <a href=\"https://wikipedia.org/wiki/Electoral_College_(Confederate_States)\" class=\"mw-redirect\" title=\"Electoral College (Confederate States)\">electors</a> of the several states of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate States of America</a> unanimously <a href=\"https://wikipedia.org/wiki/1861_Confederate_States_presidential_election\" title=\"1861 Confederate States presidential election\">elect</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America\" title=\"President of the Confederate States of America\">President</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Confederate_States_of_America\" title=\"Vice President of the Confederate States of America\">Vice President</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The 109 <a href=\"https://wikipedia.org/wiki/Electoral_College_(Confederate_States)\" class=\"mw-redirect\" title=\"Electoral College (Confederate States)\">electors</a> of the several states of the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate States of America</a> unanimously <a href=\"https://wikipedia.org/wiki/1861_Confederate_States_presidential_election\" title=\"1861 Confederate States presidential election\">elect</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America\" title=\"President of the Confederate States of America\">President</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Confederate_States_of_America\" title=\"Vice President of the Confederate States of America\">Vice President</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Electoral College (Confederate States)", "link": "https://wikipedia.org/wiki/Electoral_College_(Confederate_States)"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "1861 Confederate States presidential election", "link": "https://wikipedia.org/wiki/1861_Confederate_States_presidential_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Confederate States of America", "link": "https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the Confederate States of America", "link": "https://wikipedia.org/wiki/Vice_President_of_the_Confederate_States_of_America"}]}, {"year": "1863", "text": "American Civil War: Confederate General <PERSON> lifts his unsuccessful siege of Knoxville, Tennessee after failing to capture the city.", "html": "1863 - American Civil War: Confederate General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lifts his unsuccessful <a href=\"https://wikipedia.org/wiki/Siege_of_Knoxville\" title=\"Siege of Knoxville\">siege</a> of <a href=\"https://wikipedia.org/wiki/Knoxville,_Tennessee\" title=\"Knoxville, Tennessee\">Knoxville, Tennessee</a> after failing to capture the city.", "no_year_html": "American Civil War: Confederate General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lifts his unsuccessful <a href=\"https://wikipedia.org/wiki/Siege_of_Knoxville\" title=\"Siege of Knoxville\">siege</a> of <a href=\"https://wikipedia.org/wiki/Knoxville,_Tennessee\" title=\"Knoxville, Tennessee\">Knoxville, Tennessee</a> after failing to capture the city.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Siege of Knoxville", "link": "https://wikipedia.org/wiki/Siege_of_Knoxville"}, {"title": "Knoxville, Tennessee", "link": "https://wikipedia.org/wiki/Knoxville,_Tennessee"}]}, {"year": "1864", "text": "American Civil War: <PERSON>'s March to the Sea: Union cavalry forces defeat Confederate cavalry in the Battle of Waynesboro, Georgia, opening the way for General <PERSON>'s army to approach the coast.", "html": "1864 - American Civil War: <a href=\"https://wikipedia.org/wiki/Sherman%27s_March_to_the_Sea\" title=\"<PERSON>'s March to the Sea\"><PERSON>'s March to the Sea</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> cavalry forces defeat Confederate cavalry in the <a href=\"https://wikipedia.org/wiki/Battle_of_Waynesboro,_Georgia\" title=\"Battle of Waynesboro, Georgia\">Battle of Waynesboro, Georgia</a>, opening the way for General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s army to approach the coast.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Sherman%27s_March_to_the_Sea\" title=\"<PERSON>'s March to the Sea\"><PERSON>'s March to the Sea</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> cavalry forces defeat Confederate cavalry in the <a href=\"https://wikipedia.org/wiki/Battle_of_Waynesboro,_Georgia\" title=\"Battle of Waynesboro, Georgia\">Battle of Waynesboro, Georgia</a>, opening the way for General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s army to approach the coast.", "links": [{"title": "<PERSON>'s March to the Sea", "link": "https://wikipedia.org/wiki/Sherman%27s_March_to_the_Sea"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Battle of Waynesboro, Georgia", "link": "https://wikipedia.org/wiki/Battle_of_Waynesboro,_Georgia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "North Carolina ratifies 13th Amendment to the U.S. Constitution, followed two days later by Georgia, and U.S. slaves were legally free within two weeks.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a> ratifies <a href=\"https://wikipedia.org/wiki/13th_Amendment_to_the_U.S._Constitution\" class=\"mw-redirect\" title=\"13th Amendment to the U.S. Constitution\">13th Amendment to the U.S. Constitution</a>, followed two days later by <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a>, and <a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">U.S. slaves</a> were legally free within two weeks.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a> ratifies <a href=\"https://wikipedia.org/wiki/13th_Amendment_to_the_U.S._Constitution\" class=\"mw-redirect\" title=\"13th Amendment to the U.S. Constitution\">13th Amendment to the U.S. Constitution</a>, followed two days later by <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a>, and <a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">U.S. slaves</a> were legally free within two weeks.", "links": [{"title": "North Carolina", "link": "https://wikipedia.org/wiki/North_Carolina"}, {"title": "13th Amendment to the U.S. Constitution", "link": "https://wikipedia.org/wiki/13th_Amendment_to_the_U.S._Constitution"}, {"title": "Georgia (U.S. state)", "link": "https://wikipedia.org/wiki/Georgia_(U.S._state)"}, {"title": "Slavery in the United States", "link": "https://wikipedia.org/wiki/Slavery_in_the_United_States"}]}, {"year": "1867", "text": "Former Minnesota farmer <PERSON> founds the Order of the Patrons of Husbandry (better known today as the Grange).", "html": "1867 - Former <a href=\"https://wikipedia.org/wiki/Minnesota\" title=\"Minnesota\">Minnesota</a> farmer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> founds the Order of the Patrons of Husbandry (better known today as <a href=\"https://wikipedia.org/wiki/National_Grange_of_the_Order_of_Patrons_of_Husbandry\" class=\"mw-redirect\" title=\"National Grange of the Order of Patrons of Husbandry\">the Grange</a>).", "no_year_html": "Former <a href=\"https://wikipedia.org/wiki/Minnesota\" title=\"Minnesota\">Minnesota</a> farmer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> founds the Order of the Patrons of Husbandry (better known today as <a href=\"https://wikipedia.org/wiki/National_Grange_of_the_Order_of_Patrons_of_Husbandry\" class=\"mw-redirect\" title=\"National Grange of the Order of Patrons of Husbandry\">the Grange</a>).", "links": [{"title": "Minnesota", "link": "https://wikipedia.org/wiki/Minnesota"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "National Grange of the Order of Patrons of Husbandry", "link": "https://wikipedia.org/wiki/National_Grange_of_the_Order_of_Patrons_of_Husbandry"}]}, {"year": "1872", "text": "The American brigantine <PERSON> is discovered drifting in the Atlantic. Her crew is never found.", "html": "1872 - The American brigantine <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><i><PERSON></i></a> is discovered drifting in the Atlantic. Her crew is never found.", "no_year_html": "The American brigantine <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><i><PERSON></i></a> is discovered drifting in the Atlantic. Her crew is never found.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "Notorious New York City politician <PERSON> escapes from prison; he is later recaptured in Spain.", "html": "1875 - Notorious New York City politician <a href=\"https://wikipedia.org/wiki/Boss_Tweed\" class=\"mw-redirect\" title=\"Boss Tweed\"><PERSON></a> escapes from prison; he is later recaptured in Spain.", "no_year_html": "Notorious New York City politician <a href=\"https://wikipedia.org/wiki/Boss_Tweed\" class=\"mw-redirect\" title=\"Boss Tweed\"><PERSON></a> escapes from prison; he is later recaptured in Spain.", "links": [{"title": "Boss Tweed", "link": "https://wikipedia.org/wiki/<PERSON>_Tweed"}]}, {"year": "1881", "text": "The first edition of the Los Angeles Times is published.", "html": "1881 - The first edition of the <i><a href=\"https://wikipedia.org/wiki/Los_Angeles_Times\" title=\"Los Angeles Times\">Los Angeles Times</a></i> is published.", "no_year_html": "The first edition of the <i><a href=\"https://wikipedia.org/wiki/Los_Angeles_Times\" title=\"Los Angeles Times\">Los Angeles Times</a></i> is published.", "links": [{"title": "Los Angeles Times", "link": "https://wikipedia.org/wiki/Los_Angeles_Times"}]}, {"year": "1893", "text": "First Matabele War: A patrol of 34 British South Africa Company soldiers is ambushed and annihilated by more than 3,000 Matabele warriors on the Shangani River in Matabeleland.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/First_Matabele_War\" title=\"First Matabele War\">First Matabele War</a>: A patrol of 34 <a href=\"https://wikipedia.org/wiki/British_South_Africa_Company\" title=\"British South Africa Company\">British South Africa Company</a> soldiers is <a href=\"https://wikipedia.org/wiki/Shangani_Patrol\" title=\"Shangani Patrol\">ambushed and annihilated</a> by more than 3,000 <a href=\"https://wikipedia.org/wiki/Northern_Ndebele_people\" title=\"Northern Ndebele people\">Matabele</a> warriors on the <a href=\"https://wikipedia.org/wiki/Shangani_River\" title=\"Shangani River\">Shangani River</a> in <a href=\"https://wikipedia.org/wiki/Matabeleland\" title=\"Matabeleland\">Matabeleland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Matabele_War\" title=\"First Matabele War\">First Matabele War</a>: A patrol of 34 <a href=\"https://wikipedia.org/wiki/British_South_Africa_Company\" title=\"British South Africa Company\">British South Africa Company</a> soldiers is <a href=\"https://wikipedia.org/wiki/Shangani_Patrol\" title=\"Shangani Patrol\">ambushed and annihilated</a> by more than 3,000 <a href=\"https://wikipedia.org/wiki/Northern_Ndebele_people\" title=\"Northern Ndebele people\">Matabele</a> warriors on the <a href=\"https://wikipedia.org/wiki/Shangani_River\" title=\"Shangani River\">Shangani River</a> in <a href=\"https://wikipedia.org/wiki/Matabeleland\" title=\"Matabeleland\">Matabeleland</a>.", "links": [{"title": "First Matabele War", "link": "https://wikipedia.org/wiki/First_Matabele_War"}, {"title": "British South Africa Company", "link": "https://wikipedia.org/wiki/British_South_Africa_Company"}, {"title": "Shangani Patrol", "link": "https://wikipedia.org/wiki/Shangani_Patrol"}, {"title": "Northern Ndebele people", "link": "https://wikipedia.org/wiki/Northern_Ndebele_people"}, {"title": "Shangani River", "link": "https://wikipedia.org/wiki/Shangani_River"}, {"title": "Matabeleland", "link": "https://wikipedia.org/wiki/Matabeleland"}]}, {"year": "1906", "text": "Alpha Phi Alpha the first intercollegiate Greek lettered fraternity for African-Americans was founded at Cornell University in Ithaca, New York.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Alpha_Phi_Alpha\" title=\"Alpha Phi Alpha\">Alpha Phi Alpha</a> the first intercollegiate Greek lettered fraternity for African-Americans was founded at <a href=\"https://wikipedia.org/wiki/Cornell_University\" title=\"Cornell University\">Cornell University</a> in <a href=\"https://wikipedia.org/wiki/Ithaca,_New_York\" title=\"Ithaca, New York\">Ithaca, New York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alpha_Phi_Alpha\" title=\"Alpha Phi Alpha\">Alpha Phi Alpha</a> the first intercollegiate Greek lettered fraternity for African-Americans was founded at <a href=\"https://wikipedia.org/wiki/Cornell_University\" title=\"Cornell University\">Cornell University</a> in <a href=\"https://wikipedia.org/wiki/Ithaca,_New_York\" title=\"Ithaca, New York\">Ithaca, New York</a>.", "links": [{"title": "Alpha Phi Alpha", "link": "https://wikipedia.org/wiki/Alpha_Phi_Alpha"}, {"title": "Cornell University", "link": "https://wikipedia.org/wiki/Cornell_University"}, {"title": "Ithaca, New York", "link": "https://wikipedia.org/wiki/Ithaca,_New_York"}]}, {"year": "1909", "text": "In Canadian football, the First Grey Cup game is played. The University of Toronto Varsity Blues defeat the Toronto Parkdale Canoe Club, 26-6.", "html": "1909 - In <a href=\"https://wikipedia.org/wiki/Canadian_football\" title=\"Canadian football\">Canadian football</a>, the <a href=\"https://wikipedia.org/wiki/1st_Grey_Cup\" title=\"1st Grey Cup\">First Grey Cup</a> game is played. The <a href=\"https://wikipedia.org/wiki/University_of_Toronto\" title=\"University of Toronto\">University of Toronto</a> <a href=\"https://wikipedia.org/wiki/Toronto_Varsity_Blues\" title=\"Toronto Varsity Blues\">Varsity Blues</a> defeat the <a href=\"https://wikipedia.org/wiki/Toronto_Parkdale_Canoe_Club\" class=\"mw-redirect\" title=\"Toronto Parkdale Canoe Club\">Toronto Parkdale Canoe Club</a>, 26-6.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Canadian_football\" title=\"Canadian football\">Canadian football</a>, the <a href=\"https://wikipedia.org/wiki/1st_Grey_Cup\" title=\"1st Grey Cup\">First Grey Cup</a> game is played. The <a href=\"https://wikipedia.org/wiki/University_of_Toronto\" title=\"University of Toronto\">University of Toronto</a> <a href=\"https://wikipedia.org/wiki/Toronto_Varsity_Blues\" title=\"Toronto Varsity Blues\">Varsity Blues</a> defeat the <a href=\"https://wikipedia.org/wiki/Toronto_Parkdale_Canoe_Club\" class=\"mw-redirect\" title=\"Toronto Parkdale Canoe Club\">Toronto Parkdale Canoe Club</a>, 26-6.", "links": [{"title": "Canadian football", "link": "https://wikipedia.org/wiki/Canadian_football"}, {"title": "1st Grey Cup", "link": "https://wikipedia.org/wiki/1st_Grey_Cup"}, {"title": "University of Toronto", "link": "https://wikipedia.org/wiki/University_of_Toronto"}, {"title": "Toronto Varsity Blues", "link": "https://wikipedia.org/wiki/Toronto_Varsity_Blues"}, {"title": "Toronto Parkdale Canoe Club", "link": "https://wikipedia.org/wiki/Toronto_Parkdale_Canoe_Club"}]}, {"year": "1909", "text": "The Montreal Canadiens ice hockey club, the oldest surviving professional hockey franchise in the world, is founded as a charter member of the National Hockey Association.", "html": "1909 - The <a href=\"https://wikipedia.org/wiki/Montreal_Canadiens\" title=\"Montreal Canadiens\">Montreal Canadiens</a> <a href=\"https://wikipedia.org/wiki/Ice_hockey\" title=\"Ice hockey\">ice hockey</a> club, the oldest surviving professional hockey franchise in the world, is founded as a charter member of the <a href=\"https://wikipedia.org/wiki/National_Hockey_Association\" title=\"National Hockey Association\">National Hockey Association</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Montreal_Canadiens\" title=\"Montreal Canadiens\">Montreal Canadiens</a> <a href=\"https://wikipedia.org/wiki/Ice_hockey\" title=\"Ice hockey\">ice hockey</a> club, the oldest surviving professional hockey franchise in the world, is founded as a charter member of the <a href=\"https://wikipedia.org/wiki/National_Hockey_Association\" title=\"National Hockey Association\">National Hockey Association</a>.", "links": [{"title": "Montreal Canadiens", "link": "https://wikipedia.org/wiki/Montreal_Canadiens"}, {"title": "Ice hockey", "link": "https://wikipedia.org/wiki/Ice_hockey"}, {"title": "National Hockey Association", "link": "https://wikipedia.org/wiki/National_Hockey_Association"}]}, {"year": "1917", "text": "The Finnish Senate submits to the Parliament of Finland a proposal for the form of government of the Republic of Finland and issued a communication to Parliament declaring the independence of Finland.", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/Senate_of_Finland\" title=\"Senate of Finland\">Finnish Senate</a> submits to the Parliament of Finland a proposal for the form of government of the <a href=\"https://wikipedia.org/wiki/Republic_of_Finland\" class=\"mw-redirect\" title=\"Republic of Finland\">Republic of Finland</a> and issued a communication to Parliament declaring the <a href=\"https://wikipedia.org/wiki/Independence_of_Finland\" title=\"Independence of Finland\">independence of Finland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Senate_of_Finland\" title=\"Senate of Finland\">Finnish Senate</a> submits to the Parliament of Finland a proposal for the form of government of the <a href=\"https://wikipedia.org/wiki/Republic_of_Finland\" class=\"mw-redirect\" title=\"Republic of Finland\">Republic of Finland</a> and issued a communication to Parliament declaring the <a href=\"https://wikipedia.org/wiki/Independence_of_Finland\" title=\"Independence of Finland\">independence of Finland</a>.", "links": [{"title": "Senate of Finland", "link": "https://wikipedia.org/wiki/Senate_of_Finland"}, {"title": "Republic of Finland", "link": "https://wikipedia.org/wiki/Republic_of_Finland"}, {"title": "Independence of Finland", "link": "https://wikipedia.org/wiki/Independence_of_Finland"}]}, {"year": "1918", "text": "U.S. President <PERSON> sails for the World War I peace talks in Versailles, becoming the first US president to travel to Europe while in office.", "html": "1918 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sails for the World War I peace talks in <a href=\"https://wikipedia.org/wiki/Palace_of_Versailles\" title=\"Palace of Versailles\">Versailles</a>, becoming the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">US president</a> to travel to Europe while in office.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sails for the World War I peace talks in <a href=\"https://wikipedia.org/wiki/Palace_of_Versailles\" title=\"Palace of Versailles\">Versailles</a>, becoming the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">US president</a> to travel to Europe while in office.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Palace of Versailles", "link": "https://wikipedia.org/wiki/Palace_of_Versailles"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1919", "text": "Ukrainian War of Independence: The <PERSON><PERSON> conspiracy is initiated, with an attempt to assassinate the high command of the Revolutionary Insurgent Army of Ukraine.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Polonsky_conspiracy\" title=\"Polonsky conspiracy\">Polonsky conspiracy</a> is initiated, with an attempt to assassinate the high command of the <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Polonsky_conspiracy\" title=\"Polonsky conspiracy\">Polonsky conspiracy</a> is initiated, with an attempt to assassinate the high command of the <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a>.", "links": [{"title": "Ukrainian War of Independence", "link": "https://wikipedia.org/wiki/Ukrainian_War_of_Independence"}, {"title": "<PERSON><PERSON> conspiracy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_conspiracy"}, {"title": "Revolutionary Insurgent Army of Ukraine", "link": "https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine"}]}, {"year": "1928", "text": "Cosmo <PERSON> was enthroned as the Archbishop of Canterbury, the first bachelor to be appointed in 150 years.", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Cosmo <PERSON>\">Cosmo <PERSON></a> was enthroned as the <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>, the first bachelor to be appointed in 150 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Cosmo <PERSON>\">Cosmo <PERSON></a> was enthroned as the <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>, the first bachelor to be appointed in 150 years.", "links": [{"title": "Cosmo <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}]}, {"year": "1939", "text": "World War II: HMS Nelson is struck by a mine (laid by U-31) off the Scottish coast and is laid up for repairs until August 1940.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/HMS_Nelson_(28)\" title=\"<PERSON> Nelson (28)\">HMS <i><PERSON></i></a> is struck by a <a href=\"https://wikipedia.org/wiki/Naval_mine\" title=\"Naval mine\">mine</a> (laid by <a href=\"https://wikipedia.org/wiki/German_submarine_U-31_(1936)\" title=\"German submarine U-31 (1936)\"><i>U-31</i></a>) off the Scottish coast and is laid up for repairs until August 1940.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/HMS_Nelson_(28)\" title=\"<PERSON> Nelson (28)\">HMS <i><PERSON></i></a> is struck by a <a href=\"https://wikipedia.org/wiki/Naval_mine\" title=\"Naval mine\">mine</a> (laid by <a href=\"https://wikipedia.org/wiki/German_submarine_U-31_(1936)\" title=\"German submarine U-31 (1936)\"><i>U-31</i></a>) off the Scottish coast and is laid up for repairs until August 1940.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "HMS Nelson (28)", "link": "https://wikipedia.org/wiki/HMS_Nelson_(28)"}, {"title": "Naval mine", "link": "https://wikipedia.org/wiki/Naval_mine"}, {"title": "German submarine U<PERSON><PERSON> (1936)", "link": "https://wikipedia.org/wiki/German_submarine_U-31_(1936)"}]}, {"year": "1942", "text": "World War II: <PERSON>'s patrol during the Guadalcanal Campaign ends.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Carlson%27s_patrol\" title=\"<PERSON>'s patrol\"><PERSON>'s patrol</a> during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a> ends.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_patrol\" title=\"<PERSON>'s patrol\"><PERSON>'s patrol</a> during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a> ends.", "links": [{"title": "<PERSON>'s patrol", "link": "https://wikipedia.org/wiki/Carlson%27s_patrol"}, {"title": "Guadalcanal Campaign", "link": "https://wikipedia.org/wiki/Guadalcanal_Campaign"}]}, {"year": "1943", "text": "World War II: In Yugoslavia, resistance leader Marshal <PERSON><PERSON><PERSON> proclaims a provisional democratic Yugoslav government in-exile.", "html": "1943 - World War II: In <a href=\"https://wikipedia.org/wiki/Kingdom_of_Yugoslavia\" title=\"Kingdom of Yugoslavia\">Yugoslavia</a>, resistance leader Marshal <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> proclaims a <a href=\"https://wikipedia.org/wiki/Democratic_Federal_Yugoslavia\" title=\"Democratic Federal Yugoslavia\">provisional democratic Yugoslav government in-exile</a>.", "no_year_html": "World War II: In <a href=\"https://wikipedia.org/wiki/Kingdom_of_Yugoslavia\" title=\"Kingdom of Yugoslavia\">Yugoslavia</a>, resistance leader Marshal <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> proclaims a <a href=\"https://wikipedia.org/wiki/Democratic_Federal_Yugoslavia\" title=\"Democratic Federal Yugoslavia\">provisional democratic Yugoslav government in-exile</a>.", "links": [{"title": "Kingdom of Yugoslavia", "link": "https://wikipedia.org/wiki/Kingdom_of_Yugoslavia"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Democratic Federal Yugoslavia", "link": "https://wikipedia.org/wiki/Democratic_Federal_Yugoslavia"}]}, {"year": "1943", "text": "World War II: U.S. President <PERSON> closes down the Works Progress Administration, because of the high levels of wartime employment in the United States.", "html": "1943 - World War II: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> closes down the <a href=\"https://wikipedia.org/wiki/Works_Progress_Administration\" title=\"Works Progress Administration\">Works Progress Administration</a>, because of the high levels of wartime employment in the United States.", "no_year_html": "World War II: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> closes down the <a href=\"https://wikipedia.org/wiki/Works_Progress_Administration\" title=\"Works Progress Administration\">Works Progress Administration</a>, because of the high levels of wartime employment in the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Works Progress Administration", "link": "https://wikipedia.org/wiki/Works_Progress_Administration"}]}, {"year": "1945", "text": "By a vote of 65-7, the United States Senate approves United States participation in the United Nations. (The UN had been established on October 24, 1945.)", "html": "1945 - By a vote of 65-7, the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> approves United States <a href=\"https://wikipedia.org/wiki/United_States_and_the_United_Nations\" title=\"United States and the United Nations\">participation</a> in the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>. (The UN had been established on October 24, 1945.)", "no_year_html": "By a vote of 65-7, the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> approves United States <a href=\"https://wikipedia.org/wiki/United_States_and_the_United_Nations\" title=\"United States and the United Nations\">participation</a> in the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>. (The UN had been established on October 24, 1945.)", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "United States and the United Nations", "link": "https://wikipedia.org/wiki/United_States_and_the_United_Nations"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1948", "text": "Chinese Civil War: The SS Kiangya, carrying Nationalist refugees from Shanghai, explodes in the Huangpu River.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Chinese_Civil_War\" title=\"Chinese Civil War\">Chinese Civil War</a>: The <a href=\"https://wikipedia.org/wiki/SS_Kiangya\" title=\"SS Kiangya\">SS <i>Kiangya</i></a>, carrying <a href=\"https://wikipedia.org/wiki/Nationalist_government\" title=\"Nationalist government\">Nationalist</a> refugees from <a href=\"https://wikipedia.org/wiki/Shanghai\" title=\"Shanghai\">Shanghai</a>, explodes in the <a href=\"https://wikipedia.org/wiki/Huangpu_River\" title=\"Huangpu River\">Huangpu River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chinese_Civil_War\" title=\"Chinese Civil War\">Chinese Civil War</a>: The <a href=\"https://wikipedia.org/wiki/SS_Kiangya\" title=\"SS Kiangya\">SS <i>Kiangya</i></a>, carrying <a href=\"https://wikipedia.org/wiki/Nationalist_government\" title=\"Nationalist government\">Nationalist</a> refugees from <a href=\"https://wikipedia.org/wiki/Shanghai\" title=\"Shanghai\">Shanghai</a>, explodes in the <a href=\"https://wikipedia.org/wiki/Huangpu_River\" title=\"Huangpu River\">Huangpu River</a>.", "links": [{"title": "Chinese Civil War", "link": "https://wikipedia.org/wiki/Chinese_Civil_War"}, {"title": "SS Kiangya", "link": "https://wikipedia.org/wiki/SS_Kiangya"}, {"title": "Nationalist government", "link": "https://wikipedia.org/wiki/Nationalist_government"}, {"title": "Shanghai", "link": "https://wikipedia.org/wiki/Shanghai"}, {"title": "Huangpu River", "link": "https://wikipedia.org/wiki/Huangpu_River"}]}, {"year": "1949", "text": "Sir <PERSON>, governor of the Crown Colony of Sarawak, was fatally stabbed by a member of the Rukun 13.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\">Sir <PERSON></a>, governor of the <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Sarawak\" title=\"Crown Colony of Sarawak\">Crown Colony of Sarawak</a>, was fatally stabbed by a member of the <a href=\"https://wikipedia.org/wiki/Rukun_13\" title=\"Rukun 13\">Rukun 13</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\">Sir <PERSON></a>, governor of the <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Sarawak\" title=\"Crown Colony of Sarawak\">Crown Colony of Sarawak</a>, was fatally stabbed by a member of the <a href=\"https://wikipedia.org/wiki/Rukun_13\" title=\"Rukun 13\">Rukun 13</a>.", "links": [{"title": "Sir <PERSON>", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Crown Colony of Sarawak", "link": "https://wikipedia.org/wiki/Crown_Colony_of_Sarawak"}, {"title": "Rukun 13", "link": "https://wikipedia.org/wiki/Rukun_13"}]}, {"year": "1950", "text": "Korean War: <PERSON> (the 1st African-American Naval aviator) is killed in action during the Battle of Chosin Reservoir.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (the 1st African-American Naval aviator) is killed in action during the <a href=\"https://wikipedia.org/wiki/Battle_of_Chosin_Reservoir\" title=\"Battle of Chosin Reservoir\">Battle of Chosin Reservoir</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (the 1st African-American Naval aviator) is killed in action during the <a href=\"https://wikipedia.org/wiki/Battle_of_Chosin_Reservoir\" title=\"Battle of Chosin Reservoir\">Battle of Chosin Reservoir</a>.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Chosin Reservoir", "link": "https://wikipedia.org/wiki/Battle_of_Chosin_Reservoir"}]}, {"year": "1950", "text": "Korean War: Associated Press photographer <PERSON> photographs hundreds of Korean refugees crossing a downed bridge in the Taedong River: 1951 Pulitzer Prize winner Flight of Refugees Across Wrecked Bridge in Korea.", "html": "1950 - Korean War: <a href=\"https://wikipedia.org/wiki/Associated_Press\" title=\"Associated Press\">Associated Press</a> photographer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> photographs hundreds of Korean refugees crossing a downed bridge in the <a href=\"https://wikipedia.org/wiki/Taedong_River\" title=\"Taedong River\">Taedong River</a>: <a href=\"https://wikipedia.org/wiki/1951_Pulitzer_Prize\" title=\"1951 Pulitzer Prize\">1951 Pulitzer Prize</a> winner <i><a href=\"https://wikipedia.org/wiki/Flight_of_Refugees_Across_Wrecked_Bridge_in_Korea\" title=\"Flight of Refugees Across Wrecked Bridge in Korea\">Flight of Refugees Across Wrecked Bridge in Korea</a></i>.", "no_year_html": "Korean War: <a href=\"https://wikipedia.org/wiki/Associated_Press\" title=\"Associated Press\">Associated Press</a> photographer <a href=\"https://wikipedia.org/wiki/<PERSON>_Des<PERSON>\" title=\"<PERSON>\"><PERSON></a> photographs hundreds of Korean refugees crossing a downed bridge in the <a href=\"https://wikipedia.org/wiki/Taedong_River\" title=\"Taedong River\">Taedong River</a>: <a href=\"https://wikipedia.org/wiki/1951_Pulitzer_Prize\" title=\"1951 Pulitzer Prize\">1951 Pulitzer Prize</a> winner <i><a href=\"https://wikipedia.org/wiki/Flight_of_Refugees_Across_Wrecked_Bridge_in_Korea\" title=\"Flight of Refugees Across Wrecked Bridge in Korea\">Flight of Refugees Across Wrecked Bridge in Korea</a></i>.", "links": [{"title": "Associated Press", "link": "https://wikipedia.org/wiki/Associated_Press"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>for"}, {"title": "Taedong River", "link": "https://wikipedia.org/wiki/Taedong_River"}, {"title": "1951 Pulitzer Prize", "link": "https://wikipedia.org/wiki/1951_Pulitzer_Prize"}, {"title": "Flight of Refugees Across Wrecked Bridge in Korea", "link": "https://wikipedia.org/wiki/Flight_of_Refugees_Across_Wrecked_Bridge_in_Korea"}]}, {"year": "1956", "text": "The Million Dollar Quartet (<PERSON>, <PERSON>, <PERSON>, and <PERSON>) get together at Sun Studio for the first and last time.", "html": "1956 - The <a href=\"https://wikipedia.org/wiki/Million_Dollar_Quartet\" title=\"Million Dollar Quartet\">Million Dollar Quartet</a> (<a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_Cash\" title=\"<PERSON> Cash\"><PERSON></a>) get together at <a href=\"https://wikipedia.org/wiki/Sun_Studio\" title=\"Sun Studio\">Sun Studio</a> for the first and last time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Million_Dollar_Quartet\" title=\"Million Dollar Quartet\">Million Dollar Quartet</a> (<a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/Johnny_Cash\" title=\"<PERSON> Cash\"><PERSON></a>) get together at <a href=\"https://wikipedia.org/wiki/Sun_Studio\" title=\"Sun Studio\">Sun Studio</a> for the first and last time.", "links": [{"title": "Million Dollar Quartet", "link": "https://wikipedia.org/wiki/Million_Dollar_Quartet"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sun Studio", "link": "https://wikipedia.org/wiki/Sun_Studio"}]}, {"year": "1964", "text": "Free Speech Movement: Police arrest over 800 students at the University of California, Berkeley, following their takeover and sit-in at the administration building in protest of the UC Regents' decision to forbid protests on UC property.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Free_Speech_Movement\" title=\"Free Speech Movement\">Free Speech Movement</a>: Police arrest over 800 students at the <a href=\"https://wikipedia.org/wiki/University_of_California,_Berkeley\" title=\"University of California, Berkeley\">University of California, Berkeley</a>, following their takeover and sit-in at the administration building in protest of the <a href=\"https://wikipedia.org/wiki/Regents_of_the_University_of_California\" title=\"Regents of the University of California\">UC Regents</a>' decision to forbid protests on UC property.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Free_Speech_Movement\" title=\"Free Speech Movement\">Free Speech Movement</a>: Police arrest over 800 students at the <a href=\"https://wikipedia.org/wiki/University_of_California,_Berkeley\" title=\"University of California, Berkeley\">University of California, Berkeley</a>, following their takeover and sit-in at the administration building in protest of the <a href=\"https://wikipedia.org/wiki/Regents_of_the_University_of_California\" title=\"Regents of the University of California\">UC Regents</a>' decision to forbid protests on UC property.", "links": [{"title": "Free Speech Movement", "link": "https://wikipedia.org/wiki/Free_Speech_Movement"}, {"title": "University of California, Berkeley", "link": "https://wikipedia.org/wiki/University_of_California,_Berkeley"}, {"title": "Regents of the University of California", "link": "https://wikipedia.org/wiki/Regents_of_the_University_of_California"}]}, {"year": "1965", "text": "Launch of Gemini 7 with crew members <PERSON> and <PERSON>. The Gemini 7 spacecraft was the passive target for the first crewed space rendezvous performed by the crew of Gemini 6A.", "html": "1965 - Launch of <a href=\"https://wikipedia.org/wiki/Gemini_7\" title=\"Gemini 7\">Gemini 7</a> with crew members <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. The Gemini 7 spacecraft was the passive target for the first crewed <a href=\"https://wikipedia.org/wiki/Space_rendezvous\" title=\"Space rendezvous\">space rendezvous</a> performed by the crew of <a href=\"https://wikipedia.org/wiki/Gemini_6A\" title=\"Gemini 6A\">Gemini 6A</a>.", "no_year_html": "Launch of <a href=\"https://wikipedia.org/wiki/Gemini_7\" title=\"Gemini 7\">Gemini 7</a> with crew members <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. The Gemini 7 spacecraft was the passive target for the first crewed <a href=\"https://wikipedia.org/wiki/Space_rendezvous\" title=\"Space rendezvous\">space rendezvous</a> performed by the crew of <a href=\"https://wikipedia.org/wiki/Gemini_6A\" title=\"Gemini 6A\">Gemini 6A</a>.", "links": [{"title": "Gemini 7", "link": "https://wikipedia.org/wiki/Gemini_7"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Space rendezvous", "link": "https://wikipedia.org/wiki/Space_rendezvous"}, {"title": "Gemini 6A", "link": "https://wikipedia.org/wiki/Gemini_6A"}]}, {"year": "1969", "text": "Black Panther Party members <PERSON> and <PERSON> are shot and killed during a raid by 14 Chicago police officers.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a> members <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(Black_Panther)\" class=\"mw-redirect\" title=\"<PERSON> (Black Panther)\"><PERSON></a> are shot and killed during a raid by 14 Chicago police officers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a> members <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(Black_Panther)\" class=\"mw-redirect\" title=\"<PERSON> (Black Panther)\"><PERSON></a> are shot and killed during a raid by 14 Chicago police officers.", "links": [{"title": "Black Panther Party", "link": "https://wikipedia.org/wiki/Black_Panther_Party"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (Black Panther)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Black_Panther)"}]}, {"year": "1971", "text": "The PNS Ghazi, a Pakistan Navy submarine, sinks during the course of the Indo-Pakistani Naval War of 1971.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"P<PERSON> Ghazi\">PNS <i><PERSON><PERSON><PERSON></i></a>, a <a href=\"https://wikipedia.org/wiki/Pakistan_Navy\" title=\"Pakistan Navy\">Pakistan Navy</a> submarine, sinks during the course of the <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_Naval_War_of_1971\" title=\"Indo-Pakistani Naval War of 1971\">Indo-Pakistani Naval War of 1971</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Ghazi\">PNS <i><PERSON><PERSON><PERSON></i></a>, a <a href=\"https://wikipedia.org/wiki/Pakistan_Navy\" title=\"Pakistan Navy\">Pakistan Navy</a> submarine, sinks during the course of the <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_Naval_War_of_1971\" title=\"Indo-Pakistani Naval War of 1971\">Indo-Pakistani Naval War of 1971</a>.", "links": [{"title": "P<PERSON>", "link": "https://wikipedia.org/wiki/PNS_<PERSON>zi"}, {"title": "Pakistan Navy", "link": "https://wikipedia.org/wiki/Pakistan_Navy"}, {"title": "Indo-Pakistani Naval War of 1971", "link": "https://wikipedia.org/wiki/Indo-Pakistani_Naval_War_of_1971"}]}, {"year": "1971", "text": "During a concert by <PERSON> and The Mothers of Invention at the Montreux Casino, an audience member fires a flare gun into the ceiling, causing a fire that destroys the venue. The incident served as the inspiration for <PERSON> Purple's 1973 song Smoke on the Water.", "html": "1971 - During a concert by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/The_Mothers_of_Invention\" title=\"The Mothers of Invention\">The Mothers of Invention</a> at the <a href=\"https://wikipedia.org/wiki/Montreux_Casino\" title=\"Montreux Casino\">Montreux Casino</a>, an audience member fires a <a href=\"https://wikipedia.org/wiki/Flare_gun\" title=\"Flare gun\">flare gun</a> into the ceiling, causing a fire that destroys the venue. The incident served as the inspiration for <a href=\"https://wikipedia.org/wiki/Deep_Purple\" title=\"Deep Purple\">Deep Purple</a>'s 1973 song <a href=\"https://wikipedia.org/wiki/Smoke_on_the_Water\" title=\"Smoke on the Water\">Smoke on the Water</a>.", "no_year_html": "During a concert by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/The_Mothers_of_Invention\" title=\"The Mothers of Invention\">The Mothers of Invention</a> at the <a href=\"https://wikipedia.org/wiki/Montreux_Casino\" title=\"Montreux Casino\">Montreux Casino</a>, an audience member fires a <a href=\"https://wikipedia.org/wiki/Flare_gun\" title=\"Flare gun\">flare gun</a> into the ceiling, causing a fire that destroys the venue. The incident served as the inspiration for <a href=\"https://wikipedia.org/wiki/Deep_Purple\" title=\"Deep Purple\">Deep Purple</a>'s 1973 song <a href=\"https://wikipedia.org/wiki/Smoke_on_the_Water\" title=\"Smoke on the Water\">Smoke on the Water</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Mothers of Invention", "link": "https://wikipedia.org/wiki/The_Mothers_of_Invention"}, {"title": "Montreux Casino", "link": "https://wikipedia.org/wiki/Montreux_Casino"}, {"title": "Flare gun", "link": "https://wikipedia.org/wiki/Flare_gun"}, {"title": "Deep Purple", "link": "https://wikipedia.org/wiki/Deep_Purple"}, {"title": "Smoke on the Water", "link": "https://wikipedia.org/wiki/Smoke_on_the_Water"}]}, {"year": "1974", "text": "Martinair Flight 138 crashes into the Saptha Kanya mountain range in Maskeliya, Sri Lanka, killing 191.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Martinair_Flight_138\" title=\"Martinair Flight 138\">Martinair Flight 138</a> crashes into the <a href=\"https://wikipedia.org/wiki/Saptha_Kanya\" title=\"Saptha Kanya\">Saptha Kanya</a> mountain range in <a href=\"https://wikipedia.org/wiki/Maskeliya\" title=\"<PERSON><PERSON><PERSON>\">Maskeliya</a>, Sri Lanka, killing 191.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Martinair_Flight_138\" title=\"Martinair Flight 138\">Martinair Flight 138</a> crashes into the <a href=\"https://wikipedia.org/wiki/Saptha_Kanya\" title=\"Saptha Kanya\">Saptha Kanya</a> mountain range in <a href=\"https://wikipedia.org/wiki/<PERSON>eliya\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lanka, killing 191.", "links": [{"title": "Martinair Flight 138", "link": "https://wikipedia.org/wiki/Martinair_Flight_138"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saptha_Kanya"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ya"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, president of the Central African Republic, crowns himself Emperor <PERSON><PERSON><PERSON> of the Central African Empire.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, president of the <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a>, crowns himself Emperor <PERSON><PERSON><PERSON> I of the <a href=\"https://wikipedia.org/wiki/Central_African_Empire\" title=\"Central African Empire\">Central African Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, president of the <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a>, crowns himself Emperor <PERSON><PERSON><PERSON> I of the <a href=\"https://wikipedia.org/wiki/Central_African_Empire\" title=\"Central African Empire\">Central African Empire</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9del_<PERSON>"}, {"title": "Central African Republic", "link": "https://wikipedia.org/wiki/Central_African_Republic"}, {"title": "Central African Empire", "link": "https://wikipedia.org/wiki/Central_African_Empire"}]}, {"year": "1977", "text": "Malaysian Airline System Flight 653 is hijacked and crashes in Tanjong Kupang, Johor, killing 100.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Malaysian_Airline_System_Flight_653\" title=\"Malaysian Airline System Flight 653\">Malaysian Airline System Flight 653</a> is hijacked and crashes in Tanjong Kupang, <a href=\"https://wikipedia.org/wiki/Jo<PERSON>\" title=\"Jo<PERSON>\"><PERSON><PERSON></a>, killing 100.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malaysian_Airline_System_Flight_653\" title=\"Malaysian Airline System Flight 653\">Malaysian Airline System Flight 653</a> is hijacked and crashes in Tanjong Kupang, <a href=\"https://wikipedia.org/wiki/Jo<PERSON>\" title=\"Jo<PERSON>\"><PERSON><PERSON></a>, killing 100.", "links": [{"title": "Malaysian Airline System Flight 653", "link": "https://wikipedia.org/wiki/Malaysian_Airline_System_Flight_653"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Johor"}]}, {"year": "1978", "text": "Following the murder of Mayor <PERSON>, <PERSON><PERSON> becomes San Francisco's first female mayor.", "html": "1978 - Following the <a href=\"https://wikipedia.org/wiki/Moscone%E2%80%93Milk_assassinations\" title=\"Moscone-Milk assassinations\">murder of Mayor <PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>'s first female <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">mayor</a>.", "no_year_html": "Following the <a href=\"https://wikipedia.org/wiki/Moscone%E2%80%93Milk_assassinations\" title=\"Moscone-Milk assassinations\">murder of Mayor <PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>'s first female <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">mayor</a>.", "links": [{"title": "Moscone-Milk assassinations", "link": "https://wikipedia.org/wiki/Moscone%E2%80%93Milk_assassinations"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}, {"title": "Mayor of San Francisco", "link": "https://wikipedia.org/wiki/Mayor_of_San_Francisco"}]}, {"year": "1979", "text": "The Hastie fire in Hull kills three schoolboys and eventually leads police to arrest <PERSON>.", "html": "1979 - The Hastie fire in <a href=\"https://wikipedia.org/wiki/Kingston_upon_Hull\" title=\"Kingston upon Hull\">Hull</a> kills three schoolboys and eventually leads police to arrest <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Bruce <PERSON>\"><PERSON></a>.", "no_year_html": "The Hastie fire in <a href=\"https://wikipedia.org/wiki/Kingston_upon_Hull\" title=\"Kingston upon Hull\">Hull</a> kills three schoolboys and eventually leads police to arrest <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Bruce <PERSON>\"><PERSON></a>.", "links": [{"title": "Kingston upon Hull", "link": "https://wikipedia.org/wiki/Kingston_upon_Hull"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "South Africa grants independence to the Ciskei \"homeland\" (not recognized by any government outside South Africa).", "html": "1981 - South Africa grants independence to the <a href=\"https://wikipedia.org/wiki/Ciskei\" title=\"Ciskei\">Ciskei</a> \"<a href=\"https://wikipedia.org/wiki/Bantustan\" title=\"Bantustan\">homeland</a>\" (not recognized by any government outside South Africa).", "no_year_html": "South Africa grants independence to the <a href=\"https://wikipedia.org/wiki/Ciskei\" title=\"Ciskei\">Ciskei</a> \"<a href=\"https://wikipedia.org/wiki/Bantustan\" title=\"Bantustan\">homeland</a>\" (not recognized by any government outside South Africa).", "links": [{"title": "Ciskei", "link": "https://wikipedia.org/wiki/Ciskei"}, {"title": "Bantustan", "link": "https://wikipedia.org/wiki/Bantustan"}]}, {"year": "1982", "text": "The People's Republic of China adopts its current constitution.", "html": "1982 - The <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">People's Republic of China</a> adopts its current <a href=\"https://wikipedia.org/wiki/Constitution_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Constitution of the People's Republic of China\">constitution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">People's Republic of China</a> adopts its current <a href=\"https://wikipedia.org/wiki/Constitution_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Constitution of the People's Republic of China\">constitution</a>.", "links": [{"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "Constitution of the People's Republic of China", "link": "https://wikipedia.org/wiki/Constitution_of_the_People%27s_Republic_of_China"}]}, {"year": "1983", "text": "US Navy aircraft from USS John F. Kennedy and USS Independence attack Syrian missile sites in Lebanon in response to an F-14 being fired on by an SA-7. One A-6 Intruder and A-7 Corsair are shot down. One American pilot is killed, one is rescued, and one is captured.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">US Navy</a> aircraft from <a href=\"https://wikipedia.org/wiki/USS_<PERSON>_<PERSON><PERSON>_<PERSON>_(CV-67)\" title=\"USS John <PERSON> (CV-67)\">USS <i><PERSON></i></a> and <a href=\"https://wikipedia.org/wiki/USS_Independence_(CV-62)\" title=\"USS Independence (CV-62)\">USS <i>Independence</i></a> attack Syrian missile sites in Lebanon in response to an <a href=\"https://wikipedia.org/wiki/Grumman_F-14_Tomcat\" title=\"Grumman F-14 Tomcat\">F-14</a> being fired on by an <a href=\"https://wikipedia.org/wiki/9K32_Strela-2\" title=\"9K32 Strela-2\">SA-7</a>. One <a href=\"https://wikipedia.org/wiki/A-6_Intruder\" class=\"mw-redirect\" title=\"A-6 Intruder\">A-6 Intruder</a> and <a href=\"https://wikipedia.org/wiki/LTV_A-7_Corsair_II\" title=\"LTV A-7 Corsair II\">A-7 Corsair</a> are shot down. One American pilot is killed, one is rescued, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Goodman\">one is captured.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">US Navy</a> aircraft from <a href=\"https://wikipedia.org/wiki/USS_John_<PERSON>._<PERSON>_(CV-67)\" title=\"USS John <PERSON> (CV-67)\">USS <i><PERSON></i></a> and <a href=\"https://wikipedia.org/wiki/USS_Independence_(CV-62)\" title=\"USS Independence (CV-62)\">USS <i>Independence</i></a> attack Syrian missile sites in Lebanon in response to an <a href=\"https://wikipedia.org/wiki/Grumman_F-14_Tomcat\" title=\"Grumman F-14 Tomcat\">F-14</a> being fired on by an <a href=\"https://wikipedia.org/wiki/9K32_Strela-2\" title=\"9K32 Strela-2\">SA-7</a>. One <a href=\"https://wikipedia.org/wiki/A-6_Intruder\" class=\"mw-redirect\" title=\"A-6 Intruder\">A-6 Intruder</a> and <a href=\"https://wikipedia.org/wiki/LTV_A-7_Corsair_II\" title=\"LTV A-7 Corsair II\">A-7 Corsair</a> are shot down. One American pilot is killed, one is rescued, and <a href=\"https://wikipedia.org/wiki/<PERSON>_Goodman\" title=\"<PERSON> Goodman\">one is captured.</a>", "links": [{"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "USS <PERSON> (CV-67)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_(CV-67)"}, {"title": "USS Independence (CV-62)", "link": "https://wikipedia.org/wiki/USS_Independence_(CV-62)"}, {"title": "Grumman F-14 Tomcat", "link": "https://wikipedia.org/wiki/G<PERSON>man_F-14_<PERSON><PERSON>"}, {"title": "9K32 Strela-2", "link": "https://wikipedia.org/wiki/9K32_Strela-2"}, {"title": "A-6 Intruder", "link": "https://wikipedia.org/wiki/A-6_Intruder"}, {"title": "LTV A-7 Corsair II", "link": "https://wikipedia.org/wiki/LTV_A-7_Corsair_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "Sri Lankan Civil War: Sri Lankan Army soldiers kill 107-150 civilians in Mannar.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>: <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Army\" class=\"mw-redirect\" title=\"Sri Lankan Army\">Sri Lankan Army</a> soldiers <a href=\"https://wikipedia.org/wiki/1984_Mannar_massacre\" title=\"1984 Mannar massacre\">kill</a> 107-150 civilians in <a href=\"https://wikipedia.org/wiki/Mannar,_Sri_Lanka\" title=\"Mannar, Sri Lanka\">Mannar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>: <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Army\" class=\"mw-redirect\" title=\"Sri Lankan Army\">Sri Lankan Army</a> soldiers <a href=\"https://wikipedia.org/wiki/1984_Mannar_massacre\" title=\"1984 Mannar massacre\">kill</a> 107-150 civilians in <a href=\"https://wikipedia.org/wiki/Mannar,_Sri_Lanka\" title=\"Mannar, Sri Lanka\">Mannar</a>.", "links": [{"title": "Sri Lankan civil war", "link": "https://wikipedia.org/wiki/Sri_Lankan_civil_war"}, {"title": "Sri Lankan Army", "link": "https://wikipedia.org/wiki/Sri_Lankan_Army"}, {"title": "1984 Mannar massacre", "link": "https://wikipedia.org/wiki/1984_Mannar_massacre"}, {"title": "Mannar, Sri Lanka", "link": "https://wikipedia.org/wiki/Mannar,_Sri_Lanka"}]}, {"year": "1986", "text": "The MV Amazon Venture oil tanker begins leaking oil while at the port of Savannah in the United States, resulting in an oil spill of approximately 500,000 US gallons (1,900,000 L).", "html": "1986 - The MV <i>Amazon Venture</i> oil tanker begins leaking oil while at the <a href=\"https://wikipedia.org/wiki/Port_of_Savannah\" title=\"Port of Savannah\">port of Savannah</a> in the United States, resulting in <a href=\"https://wikipedia.org/wiki/Amazon_Venture_oil_spill\" title=\"Amazon Venture oil spill\">an oil spill</a> of approximately 500,000 US gallons (1,900,000 L).", "no_year_html": "The MV <i>Amazon Venture</i> oil tanker begins leaking oil while at the <a href=\"https://wikipedia.org/wiki/Port_of_Savannah\" title=\"Port of Savannah\">port of Savannah</a> in the United States, resulting in <a href=\"https://wikipedia.org/wiki/Amazon_Venture_oil_spill\" title=\"Amazon Venture oil spill\">an oil spill</a> of approximately 500,000 US gallons (1,900,000 L).", "links": [{"title": "Port of Savannah", "link": "https://wikipedia.org/wiki/Port_of_Savannah"}, {"title": "Amazon Venture oil spill", "link": "https://wikipedia.org/wiki/Amazon_Venture_oil_spill"}]}, {"year": "1991", "text": "<PERSON> is released after seven years in captivity as a hostage in Beirut; he is the last and longest-held American hostage in Lebanon.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is released after seven years in captivity as a hostage in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>; he is the last and longest-held American hostage in <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is released after seven years in captivity as a hostage in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>; he is the last and longest-held American hostage in <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}]}, {"year": "1991", "text": "Pan American World Airways ceases its operations after 64 years.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Pan_American_World_Airways\" class=\"mw-redirect\" title=\"Pan American World Airways\">Pan American World Airways</a> ceases its operations after 64 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_American_World_Airways\" class=\"mw-redirect\" title=\"Pan American World Airways\">Pan American World Airways</a> ceases its operations after 64 years.", "links": [{"title": "Pan American World Airways", "link": "https://wikipedia.org/wiki/Pan_American_World_Airways"}]}, {"year": "1992", "text": "Somali Civil War: President <PERSON> orders 28,000 U.S. troops to Somalia in Northeast Africa.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Somali_Civil_War\" title=\"Somali Civil War\">Somali Civil War</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders 28,000 U.S. troops to <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a> in <a href=\"https://wikipedia.org/wiki/Horn_of_Africa\" title=\"Horn of Africa\">Northeast Africa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Somali_Civil_War\" title=\"Somali Civil War\">Somali Civil War</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders 28,000 U.S. troops to <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a> in <a href=\"https://wikipedia.org/wiki/Horn_of_Africa\" title=\"Horn of Africa\">Northeast Africa</a>.", "links": [{"title": "Somali Civil War", "link": "https://wikipedia.org/wiki/Somali_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Somalia", "link": "https://wikipedia.org/wiki/Somalia"}, {"title": "Horn of Africa", "link": "https://wikipedia.org/wiki/Horn_of_Africa"}]}, {"year": "1998", "text": "The Unity Module, the second module of the International Space Station, is launched.", "html": "1998 - The <i><a href=\"https://wikipedia.org/wiki/Unity_Module\" class=\"mw-redirect\" title=\"Unity Module\">Unity Module</a></i>, the second module of the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>, is launched.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Unity_Module\" class=\"mw-redirect\" title=\"Unity Module\">Unity Module</a></i>, the second module of the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>, is launched.", "links": [{"title": "Unity Module", "link": "https://wikipedia.org/wiki/Unity_Module"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2005", "text": "Tens of thousands of people in Hong Kong protest for democracy and call on the government to allow universal and equal suffrage.", "html": "2005 - Tens of thousands of people in Hong Kong <a href=\"https://wikipedia.org/wiki/December_2005_protest_for_democracy_in_Hong_Kong\" title=\"December 2005 protest for democracy in Hong Kong\">protest for democracy</a> and call on the <a href=\"https://wikipedia.org/wiki/Hong_Kong_Government\" class=\"mw-redirect\" title=\"Hong Kong Government\">government</a> to allow <a href=\"https://wikipedia.org/wiki/Universal_suffrage\" title=\"Universal suffrage\">universal and equal suffrage</a>.", "no_year_html": "Tens of thousands of people in Hong Kong <a href=\"https://wikipedia.org/wiki/December_2005_protest_for_democracy_in_Hong_Kong\" title=\"December 2005 protest for democracy in Hong Kong\">protest for democracy</a> and call on the <a href=\"https://wikipedia.org/wiki/Hong_Kong_Government\" class=\"mw-redirect\" title=\"Hong Kong Government\">government</a> to allow <a href=\"https://wikipedia.org/wiki/Universal_suffrage\" title=\"Universal suffrage\">universal and equal suffrage</a>.", "links": [{"title": "December 2005 protest for democracy in Hong Kong", "link": "https://wikipedia.org/wiki/December_2005_protest_for_democracy_in_Hong_Kong"}, {"title": "Hong Kong Government", "link": "https://wikipedia.org/wiki/Hong_Kong_Government"}, {"title": "Universal suffrage", "link": "https://wikipedia.org/wiki/Universal_suffrage"}]}, {"year": "2006", "text": "Six black youths assault a white teenager in Jena, Louisiana.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Jena_Six\" title=\"Jena Six\">Six black youths</a> assault a white teenager in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Louisiana\" title=\"Jena, Louisiana\">Jena, Louisiana</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jen<PERSON>_Six\" title=\"Jena Six\">Six black youths</a> assault a white teenager in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Louisiana\" title=\"Jena, Louisiana\">Jena, Louisiana</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Jena, Louisiana", "link": "https://wikipedia.org/wiki/Jen<PERSON>,_Louisiana"}]}, {"year": "2014", "text": "Islamic insurgents kill three state police at a traffic circle before taking an empty school and a \"press house\" in Grozny. Ten state forces die with 28 injured in gun battles ending with ten insurgents killed.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Caucasus_Emirate\" title=\"Caucasus Emirate\">Islamic insurgents</a> <a href=\"https://wikipedia.org/wiki/2014_Grozny_clashes\" title=\"2014 Grozny clashes\">kill</a> three <a href=\"https://wikipedia.org/wiki/Law_enforcement_in_Russia\" title=\"Law enforcement in Russia\">state police</a> at a traffic circle before taking an empty school and a \"press house\" in <a href=\"https://wikipedia.org/wiki/Grozny\" title=\"Grozny\">Grozny</a>. Ten state forces die with 28 injured in gun battles ending with ten insurgents killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Caucasus_Emirate\" title=\"Caucasus Emirate\">Islamic insurgents</a> <a href=\"https://wikipedia.org/wiki/2014_Grozny_clashes\" title=\"2014 Grozny clashes\">kill</a> three <a href=\"https://wikipedia.org/wiki/Law_enforcement_in_Russia\" title=\"Law enforcement in Russia\">state police</a> at a traffic circle before taking an empty school and a \"press house\" in <a href=\"https://wikipedia.org/wiki/Grozny\" title=\"Grozny\">Grozny</a>. Ten state forces die with 28 injured in gun battles ending with ten insurgents killed.", "links": [{"title": "Caucasus Emirate", "link": "https://wikipedia.org/wiki/Caucasus_Emirate"}, {"title": "2014 Grozny clashes", "link": "https://wikipedia.org/wiki/2014_Grozny_clashes"}, {"title": "Law enforcement in Russia", "link": "https://wikipedia.org/wiki/Law_enforcement_in_Russia"}, {"title": "Grozny", "link": "https://wikipedia.org/wiki/Grozny"}]}, {"year": "2015", "text": "A firebomb is thrown into a restaurant in the Egyptian capital of Cairo, killing 17 people.", "html": "2015 - A <a href=\"https://wikipedia.org/wiki/2015_Cairo_restaurant_fire\" title=\"2015 Cairo restaurant fire\">firebomb</a> is thrown into a restaurant in the Egyptian capital of <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, killing 17 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2015_Cairo_restaurant_fire\" title=\"2015 Cairo restaurant fire\">firebomb</a> is thrown into a restaurant in the Egyptian capital of <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, killing 17 people.", "links": [{"title": "2015 Cairo restaurant fire", "link": "https://wikipedia.org/wiki/2015_Cairo_restaurant_fire"}, {"title": "Cairo", "link": "https://wikipedia.org/wiki/Cairo"}]}, {"year": "2017", "text": "The Thomas Fire starts near Santa Paula in California. It eventually became the largest wildfire in modern California history to date after burning 440 square miles (1,140 km2) in Ventura and Santa Barbara Counties.", "html": "2017 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Fire\">Thomas Fire</a> starts near <a href=\"https://wikipedia.org/wiki/Santa_Paula,_California\" title=\"Santa Paula, California\">Santa Paula</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>. It eventually became the largest <a href=\"https://wikipedia.org/wiki/List_of_California_wildfires\" title=\"List of California wildfires\">wildfire in modern California history</a> to date after burning 440 square miles (1,140 km) in <a href=\"https://wikipedia.org/wiki/Ventura_County,_California\" title=\"Ventura County, California\">Ventura</a> and <a href=\"https://wikipedia.org/wiki/Santa_Barbara_County,_California\" title=\"Santa Barbara County, California\">Santa Barbara</a> Counties.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Fire\">Thomas Fire</a> starts near <a href=\"https://wikipedia.org/wiki/Santa_Paula,_California\" title=\"Santa Paula, California\">Santa Paula</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>. It eventually became the largest <a href=\"https://wikipedia.org/wiki/List_of_California_wildfires\" title=\"List of California wildfires\">wildfire in modern California history</a> to date after burning 440 square miles (1,140 km) in <a href=\"https://wikipedia.org/wiki/Ventura_County,_California\" title=\"Ventura County, California\">Ventura</a> and <a href=\"https://wikipedia.org/wiki/Santa_Barbara_County,_California\" title=\"Santa Barbara County, California\">Santa Barbara</a> Counties.", "links": [{"title": "Thomas Fire", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Santa Paula, California", "link": "https://wikipedia.org/wiki/Santa_Paula,_California"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "List of California wildfires", "link": "https://wikipedia.org/wiki/List_of_California_wildfires"}, {"title": "Ventura County, California", "link": "https://wikipedia.org/wiki/Ventura_County,_California"}, {"title": "Santa Barbara County, California", "link": "https://wikipedia.org/wiki/Santa_Barbara_County,_California"}]}, {"year": "2021", "text": "Semeru on the Indonesian island of Java erupts, killing at least 68 people.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Semeru\" title=\"Semeru\">Semeru</a> on the Indonesian island of Java <a href=\"https://wikipedia.org/wiki/2021_Semeru_eruption\" title=\"2021 Semeru eruption\">erupts</a>, killing at least 68 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Semeru\" title=\"Semeru\">Semeru</a> on the Indonesian island of Java <a href=\"https://wikipedia.org/wiki/2021_Semeru_eruption\" title=\"2021 Semeru eruption\">erupts</a>, killing at least 68 people.", "links": [{"title": "Semeru", "link": "https://wikipedia.org/wiki/Semeru"}, {"title": "2021 Semeru eruption", "link": "https://wikipedia.org/wiki/2021_Semeru_eruption"}]}], "Births": [{"year": "34", "text": "<PERSON><PERSON>, Roman poet (d. 62)", "html": "34 - AD 34 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman poet (d. 62)", "no_year_html": "AD 34 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman poet (d. 62)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "846", "text": "<PERSON> 11th Imam of Twelver Shia Islam (d. 874)", "html": "846 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> 11th <a href=\"https://wikipedia.org/wiki/Imam\" title=\"Imam\">Imam</a> of Twelver <PERSON> (d. 874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> 11th <a href=\"https://wikipedia.org/wiki/Imam\" title=\"Imam\">Imam</a> of Twelver <PERSON> (d. 874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Imam"}]}, {"year": "1428", "text": "<PERSON>, Lord of Lippe (d. 1511)", "html": "1428 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_of_Lippe\" title=\"<PERSON>, Lord of Lippe\"><PERSON>, Lord of Lippe</a> (d. 1511)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_of_Lippe\" title=\"<PERSON>, Lord of Lippe\"><PERSON>, Lord of Lippe</a> (d. 1511)", "links": [{"title": "<PERSON>, Lord of Lippe", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_of_Lippe"}]}, {"year": "1506", "text": "<PERSON>, 1st Baron <PERSON> of Chiche (d. 1558)", "html": "1506 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_of_Chiche\" title=\"<PERSON>, 1st Baron <PERSON> of Chiche\"><PERSON>, 1st Baron <PERSON> of Chiche</a> (d. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_of_Chiche\" title=\"<PERSON>, 1st Baron <PERSON> of Chiche\"><PERSON>, 1st Baron <PERSON> of Chiche</a> (d. 1558)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Chiche", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_of_Chiche"}]}, {"year": "1555", "text": "<PERSON>, German poet and historian (d. 1625)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, German poet and historian (d. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, German poet and historian (d. 1625)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1575", "text": "Sister <PERSON>, Italian nun (d. 1650)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/The_Nun_of_Monza\" title=\"The Nun of Monza\">Sister <PERSON></a>, Italian nun (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Nun_of_Monza\" title=\"The Nun of Monza\">Sister <PERSON></a>, Italian nun (d. 1650)", "links": [{"title": "The Nun of Monza", "link": "https://wikipedia.org/wiki/The_Nun_of_Monza"}]}, {"year": "1580", "text": "<PERSON>, English adventurer and naval officer (d. 1626)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English adventurer and naval officer (d. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English adventurer and naval officer (d. 1626)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1585", "text": "<PERSON>, English-American minister and theologian (d. 1652)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, English-American minister and theologian (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, English-American minister and theologian (d. 1652)", "links": [{"title": "<PERSON> (minister)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)"}]}, {"year": "1595", "text": "<PERSON>, French poet and critic (d. 1674)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (d. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (d. 1674)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1647", "text": "<PERSON>, German composer (d. 1715)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1660", "text": "<PERSON>, French composer and conductor (d. 1744)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (d. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (d. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Campra"}]}, {"year": "1667", "text": "<PERSON>, French composer and educator (d. 1737)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9clair\" title=\"<PERSON> Montéclair\"><PERSON></a>, French composer and educator (d. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9clair\" title=\"<PERSON> Monté<PERSON>lair\"><PERSON></a>, French composer and educator (d. 1737)", "links": [{"title": "<PERSON>lair", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9clair"}]}, {"year": "1670", "text": "<PERSON>, English politician, Chancellor of the Exchequer (d. 1742)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1713", "text": "<PERSON><PERSON><PERSON>, Italian playwright and critic (d. 1786)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian playwright and critic (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian playwright and critic (d. 1786)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1727", "text": "<PERSON>, German anatomist and botanist (d. 1759)", "html": "1727 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anatomist and botanist (d. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anatomist and botanist (d. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, French businesswoman (d. 1849)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/Juliette_R%C3%A9camier\" title=\"<PERSON>\"><PERSON></a>, French businesswoman (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juliette_R%C3%A9camier\" title=\"<PERSON>\"><PERSON></a>, French businesswoman (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juliette_R%C3%A9camier"}]}, {"year": "1795", "text": "<PERSON>, Scottish-English historian, philosopher, and academic (d. 1881)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English historian, philosopher, and academic (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English historian, philosopher, and academic (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, French lawyer and politician, 33rd Prime Minister of France (d. 1881)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON>, Georgian poet and author (d. 1845)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/Nikoloz_Bar<PERSON>shvili\" title=\"Nik<PERSON>z <PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian poet and author (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nikoloz_<PERSON>shvili\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian poet and author (d. 1845)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nikoloz_Baratashvili"}]}, {"year": "1835", "text": "<PERSON>, English author and critic (d. 1902)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, English author and critic (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, English author and critic (d. 1902)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_(novelist)"}]}, {"year": "1844", "text": "<PERSON>, German religious leader, 25th Superior General of the Society of Jesus (d. 1914)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German religious leader, 25th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German religious leader, 25th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Superior General of the Society of Jesus", "link": "https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus"}]}, {"year": "1861", "text": "<PERSON><PERSON>, Icelandic poet and politician, 1st Prime Minister of Iceland (d. 1922)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic poet and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic poet and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (d. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Iceland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iceland"}]}, {"year": "1865", "text": "<PERSON>, English nurse, humanitarian, and saint (Anglicanism) (d. 1915)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse, humanitarian, and saint (<a href=\"https://wikipedia.org/wiki/Anglicanism\" title=\"Anglicanism\">Anglicanism</a>) (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse, humanitarian, and saint (<a href=\"https://wikipedia.org/wiki/Anglicanism\" title=\"Anglicanism\">Anglicanism</a>) (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Anglicanism", "link": "https://wikipedia.org/wiki/Anglicanism"}]}, {"year": "1867", "text": "<PERSON>, Australian politician, 32nd Premier of Victoria (d. 1940)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1868", "text": "<PERSON>, American baseball player, coach, and manager (d. 1953)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Scottish medical doctor (d. 1964)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish medical doctor (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish medical doctor (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, American baseball player and coach (d. 1945)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON>, Austrian-Swiss poet and author (d. 1926)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swiss poet and author (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swiss poet and author (d. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, South African politician (d. 1946)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Polish-German field marshal (d. 1944)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German field marshal (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German field marshal (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Australian psychologist (d. 1963)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian psychologist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian psychologist (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Australian author and playwright (d. 1969)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian author and playwright (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian author and playwright (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian historian (d. 1980)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian historian (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian historian (d. 1980)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Irish suffragist, trade unionist, and Irish republican (d. 1943)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish suffragist, trade unionist, and Irish republican (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish suffragist, trade unionist, and Irish republican (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Spanish general and dictator, Prime Minister of Spain (d. 1975)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general and dictator, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Spain\" class=\"mw-redirect\" title=\"List of Prime Ministers of Spain\">Prime Minister of Spain</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general and dictator, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Spain\" class=\"mw-redirect\" title=\"List of Prime Ministers of Spain\">Prime Minister of Spain</a> (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Franco"}, {"title": "List of Prime Ministers of Spain", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Spain"}]}, {"year": "1892", "text": "<PERSON>, Chinese commander and politician (d. 1986)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese commander and politician (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese commander and politician (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, English poet and critic (d. 1968)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Read\"><PERSON></a>, English poet and critic (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Chinese philosopher and academic (d. 1990)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese philosopher and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese philosopher and academic (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American anthropologist of Mexico (d. 1958)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist of Mexico (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist of Mexico (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, German physician and politician (d. 1934)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%<PERSON><PERSON><PERSON>_He<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> He<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German physician and politician (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%<PERSON><PERSON><PERSON>_He<PERSON>oth\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Heimsoth\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German physician and politician (d. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Karl-G%C3%<PERSON><PERSON><PERSON>_He<PERSON>oth"}]}, {"year": "1899", "text": "<PERSON>, English footballer and manager (d. 1953)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American author (d. 1968)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Woolrich\"><PERSON></a>, American author (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> W<PERSON>rich\"><PERSON></a>, American author (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cornell_Woolrich"}]}, {"year": "1904", "text": "<PERSON>, German journalist and politician (d. 1982)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American bacteriologist and geneticist, Nobel Prize laureate (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bacteriologist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bacteriologist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1910", "text": "<PERSON>, American composer and conductor (d. 1991)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Alex_North\" title=\"Alex North\"><PERSON></a>, American composer and conductor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alex_North\" title=\"Alex North\"><PERSON></a>, American composer and conductor (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alex_North"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Indian lawyer and politician, 6th President of India (d. 2009)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/R._Venkataraman\" class=\"mw-redirect\" title=\"R. V<PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R._Venkataraman\" class=\"mw-redirect\" title=\"R. <PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R._Venkataraman"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American colonel and pilot, Medal of Honor recipient (d. 1988)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Pa<PERSON>\"><PERSON><PERSON></a>, American colonel and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Pa<PERSON>\"><PERSON><PERSON></a>, American colonel and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1913", "text": "<PERSON>, Canadian-American director and producer (d. 1978)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, Canadian-American director and producer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, Canadian-American director and producer (d. 1978)", "links": [{"title": "<PERSON> (film director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)"}]}, {"year": "1914", "text": "<PERSON>, Austrian painter and sculptor (d. 1995)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter and sculptor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter and sculptor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, French cinematographer (d. 1993)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cinematographer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cinematographer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American pianist and composer (d. 1989)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American journalist and author (d. 1994)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American journalist and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American journalist and author (d. 1994)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1919", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian poet and politician, 12th Prime Minister of India (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"I. K. Gujral\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian poet and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"I. K<PERSON> Gujral\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian poet and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Portuguese painter and architect (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese painter and architect (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese painter and architect (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>_<PERSON><PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English actor (d. 1978)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 1978)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1920", "text": "<PERSON>, American educator and activist, co-founded PFLAG (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and activist, co-founded <a href=\"https://wikipedia.org/wiki/PFLAG\" title=\"PFLAG\">PFLAG</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and activist, co-founded <a href=\"https://wikipedia.org/wiki/PFLAG\" title=\"PFLAG\">PFLAG</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "PFLAG", "link": "https://wikipedia.org/wiki/PFLAG"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Canadian actress and singer (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and singer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American lawyer and financier (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and financier (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and financier (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American-Canadian football player and coach (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Eagle_Keys\" title=\"Eagle Keys\"><PERSON></a>, American-Canadian football player and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eagle_Keys\" title=\"Eagle Keys\"><PERSON></a>, American-Canadian football player and coach (d. 2012)", "links": [{"title": "Eagle Keys", "link": "https://wikipedia.org/wiki/Eagle_Keys"}]}, {"year": "1923", "text": "<PERSON>, English director and screenwriter (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American architect, designed the Renaissance Center and Tomorrow Square (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Renaissance_Center\" title=\"Renaissance Center\">Renaissance Center</a> and <a href=\"https://wikipedia.org/wiki/Tomorrow_Square\" title=\"Tomorrow Square\">Tomorrow Square</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Renaissance_Center\" title=\"Renaissance Center\">Renaissance Center</a> and <a href=\"https://wikipedia.org/wiki/Tomorrow_Square\" title=\"Tomorrow Square\">Tomorrow Square</a> (d. 2017)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Renaissance Center", "link": "https://wikipedia.org/wiki/Renaissance_Center"}, {"title": "Tomorrow Square", "link": "https://wikipedia.org/wiki/Tomorrow_Square"}]}, {"year": "1925", "text": "<PERSON>, Canadian-American psychologist and academic (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American psychologist and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American psychologist and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor and opera singer (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and opera singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and opera singer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Turkish pharmacist, photographer, and businessman (d. 2010)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/%C5%9Eakir_Eczac%C4%B1ba%C5%9F%C4%B1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish pharmacist, photographer, and businessman (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%9Eakir_Eczac%C4%B1ba%C5%9F%C4%B1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish pharmacist, photographer, and businessman (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%9Eakir_Eczac%C4%B1ba%C5%9F%C4%B1"}]}, {"year": "1930", "text": "<PERSON>, Scottish actor and comedian (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and comedian (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and comedian (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American guitarist and composer (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and composer (d. 2013)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1931", "text": "<PERSON>, Canadian ice hockey player, coach, and manager", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American radio and television host (d. 2003)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, South Korean general and politician, 6th President of South Korea (d. 2021)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-woo\" title=\"<PERSON><PERSON>-woo\"><PERSON><PERSON>woo</a>, South Korean general and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-woo\" title=\"<PERSON><PERSON>-woo\"><PERSON><PERSON>woo</a>, South Korean general and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON>-woo", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON><PERSON>-woo"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "1933", "text": "<PERSON><PERSON>, German actor (d. 2003)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American game show host and producer", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American game show host and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American game show host and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wink_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American baseball and basketball player (d. 1988)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball and basketball player (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball and basketball player (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Australian film critic and author (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_presenter)\" title=\"<PERSON> (television presenter)\"><PERSON></a>, Australian film critic and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_presenter)\" title=\"<PERSON> (television presenter)\"><PERSON></a>, Australian film critic and author (d. 2019)", "links": [{"title": "<PERSON> (television presenter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_presenter)"}]}, {"year": "1934", "text": "<PERSON>, American actor and director (d. 1989)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Victor_French\" title=\"Victor French\"><PERSON></a>, American actor and director (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victor_French\" title=\"Victor French\"><PERSON></a>, American actor and director (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American businessman and politician, 72nd United States Secretary of the Treasury (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American businessman and politician, 72nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27<PERSON><PERSON><PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American businessman and politician, 72nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 2020)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(politician)"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1936", "text": "<PERSON>, American singer and guitarist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cannon\"><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American poet and performance artist (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and performance artist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and performance artist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actor, director, and producer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American actor, director, and producer", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1938", "text": "<PERSON>, American lawyer and politician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Australian-English soprano and actress", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American academic and diplomat, United States Ambassador to South Korea (d. 2016)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_South_Korea\" class=\"mw-redirect\" title=\"United States Ambassador to South Korea\">United States Ambassador to South Korea</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_South_Korea\" class=\"mw-redirect\" title=\"United States Ambassador to South Korea\">United States Ambassador to South Korea</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to South Korea", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_South_Korea"}]}, {"year": "1939", "text": "<PERSON>, American-British author (d. 2024)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American-British_writer)\" title=\"<PERSON> (American-British writer)\"><PERSON></a>, American-British author (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American-British_writer)\" title=\"<PERSON> (American-British writer)\"><PERSON></a>, American-British author (d. 2024)", "links": [{"title": "<PERSON> (American-British writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American-British_writer)"}]}, {"year": "1940", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American murderer (d. 1977)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American tennis player and coach", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian musician and singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian musician and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian musician and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, French race car driver (d. 2012)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>lt\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Mi<PERSON>lt\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Migault"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter, producer, and drummer (d. 1983)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and drummer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and drummer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian neurologist, academic, and astronaut", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian neurologist, academic, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian neurologist, academic, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Spanish singer/actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Spanish_singer)\" title=\"<PERSON><PERSON> (Spanish singer)\"><PERSON><PERSON></a>, Spanish singer/actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Spanish_singer)\" title=\"<PERSON><PERSON> (Spanish singer)\"><PERSON><PERSON></a>, Spanish singer/actress", "links": [{"title": "<PERSON><PERSON> (Spanish singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(Spanish_singer)"}]}, {"year": "1947", "text": "<PERSON>, American ecologist, academic, and diplomat", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ecologist, academic, and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ecologist, academic, and diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "South<PERSON>, American singer-songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Southside_Johnny\" title=\"Southside Johnny\">Southside Johnny</a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Southside_Johnny\" title=\"Southside Johnny\">Southside Johnny</a>, American singer-songwriter", "links": [{"title": "Southside Johnny", "link": "https://wikipedia.org/wiki/Southside_Johnny"}]}, {"year": "1949", "text": "<PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Baron <PERSON>, English air marshal and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON>, Baron <PERSON></a>, English air marshal and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON>, Baron <PERSON></a>, English air marshal and politician", "links": [{"title": "<PERSON><PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian bassist and composer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian bassist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian bassist and composer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B8rn_<PERSON><PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American guitarist (d. 2023)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actress and playwright", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Belgian footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor (d. 2024)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English businessman and politician, former Chancellor of the Exchequer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, former <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, former <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1955", "text": "<PERSON>, Canadian-American ice hockey player and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player and manager", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Welsh educator and politician, former Shadow Secretary of State for Wales", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh educator and politician, former <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales\" title=\"Shadow Secretary of State for Wales\">Shadow Secretary of State for Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh educator and politician, former <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales\" title=\"Shadow Secretary of State for Wales\">Shadow Secretary of State for Wales</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Shadow Secretary of State for Wales", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales"}]}, {"year": "1956", "text": "<PERSON>, American basketball player and sportscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Brazilian race car driver and radio host", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian race car driver and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian race car driver and radio host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American computer programmer and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American baseball player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1958", "text": "<PERSON>, Russian ice hockey player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Nicaraguan-American baseball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Nicaraguan-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Nicaraguan-American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Australian heptathlete and hurler", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian heptathlete and hurler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian heptathlete and hurler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1961", "text": "<PERSON>, American football player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank <PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American-Australian television host[better source needed]", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter and musician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, New Zealand rugby league player, coach, and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, New Zealand rugby league player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, New Zealand rugby league player, coach, and sportscaster", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1962", "text": "<PERSON>, Kenyan runner", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1963", "text": "<PERSON>, Ukrainian pole vaulter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian pole vaulter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English rugby player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Scottish rugby player and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Scottish rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Scottish rugby player and sportscaster", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1964", "text": "<PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Noble\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Noble\"><PERSON></a>, American actress", "links": [{"title": "Chelsea Noble", "link": "https://wikipedia.org/wiki/Chelsea_Noble"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON> la Iglesia, Spanish director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/%C3%81lex_de_la_Iglesia\" title=\"Álex de la Iglesia\"><PERSON><PERSON> de la Iglesia</a>, Spanish director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81lex_de_la_Iglesia\" title=\"Álex de la Iglesia\"><PERSON><PERSON> de la Iglesia</a>, Spanish director, producer, and screenwriter", "links": [{"title": "Álex de la Iglesia", "link": "https://wikipedia.org/wiki/%C3%81lex_de_la_Iglesia"}]}, {"year": "1965", "text": "<PERSON>, English race car driver and businessman", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor and musician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American bass player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American journalist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American lawyer and academic", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Suze<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suzette_<PERSON>_<PERSON>aux\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Malveaux"}]}, {"year": "1967", "text": "<PERSON>, Spanish footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Pakistani police officer and Pashto poet (d. 2018)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani police officer and Pashto poet (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani police officer and Pashto poet (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American singer-songwriter, producer and actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, American rapper, producer, actor, and co-founder of Roc-A-Fella Records", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper, producer, actor, and co-founder of <a href=\"https://wikipedia.org/wiki/Roc-A-Fella_Records\" title=\"Roc-A-Fella Records\">Roc-A-Fella Records</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper, producer, actor, and co-founder of <a href=\"https://wikipedia.org/wiki/Roc-A-Fella_Records\" title=\"Roc-A-Fella Records\">Roc-A-Fella Records</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}, {"title": "Roc-A-Fella Records", "link": "https://wikipedia.org/wiki/Roc-A-Fella_Records"}]}, {"year": "1969", "text": "<PERSON><PERSON>, English journalist and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor and comedian", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American boxer and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American model, actress, and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress, and producer", "links": [{"title": "Tyra Banks", "link": "https://wikipedia.org/wiki/Tyra_Banks"}]}, {"year": "1973", "text": "<PERSON>, Dutch keyboard player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(keyboardist)\" class=\"mw-redirect\" title=\"<PERSON> (keyboardist)\"><PERSON></a>, Dutch keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(keyboardist)\" class=\"mw-redirect\" title=\"<PERSON> (keyboardist)\"><PERSON></a>, Dutch keyboard player", "links": [{"title": "<PERSON> (keyboardist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(keyboardist)"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and keyboard player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Caputo\"><PERSON></a>, American singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Caputo\"><PERSON></a>, American singer-songwriter and keyboard player", "links": [{"title": "Mina Caputo", "link": "https://wikipedia.org/wiki/Mina_Caputo"}]}, {"year": "1973", "text": "<PERSON>, English footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1973)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, born 1973)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1973)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, born 1973)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1973)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1973)"}]}, {"year": "1973", "text": "<PERSON>, Australian rugby league player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Canadian speed skater", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s\" title=\"Kristina Groves\"><PERSON><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kris<PERSON>_<PERSON>s\" title=\"Kristina Groves\"><PERSON><PERSON></a>, Canadian speed skater", "links": [{"title": "Kristina Groves", "link": "https://wikipedia.org/wiki/Kristina_Groves"}]}, {"year": "1976", "text": "<PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American sprinter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Norwegian guitarist and songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Morten_Veland\" title=\"Morten Veland\"><PERSON><PERSON></a>, Norwegian guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mo<PERSON>_Veland\" title=\"Morten Veland\"><PERSON><PERSON></a>, Norwegian guitarist and songwriter", "links": [{"title": "Morten Veland", "link": "https://wikipedia.org/wiki/Morten_Veland"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Malaysian singer and actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Brave\" title=\"<PERSON><PERSON>bella Brave\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Brave\" title=\"<PERSON><PERSON><PERSON> Brave\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American soccer player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1980", "text": "<PERSON>, Canadian wrestler and manager", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler and manager", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(wrestler)"}]}, {"year": "1981", "text": "<PERSON>, Danish cyclist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English triple jumper", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English triple jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Chilean footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Waldo_<PERSON>\" title=\"Waldo Ponce\"><PERSON><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Waldo_<PERSON>\" title=\"Waldo Ponce\"><PERSON><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waldo_Ponce"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch-Chinese race car driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch-Chinese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch-Chinese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian evangelist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian evangelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian evangelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American rapper (d. 2015)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Chinx\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chinx\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chinx"}]}, {"year": "1984", "text": "<PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Russian basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American singer and rapper", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> <PERSON> (singer)\"><PERSON><PERSON></a>, American singer and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> <PERSON> (singer)\"><PERSON><PERSON></a>, American singer and rapper", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_tackle)\" title=\"<PERSON> (offensive tackle)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_tackle)\" title=\"<PERSON> (offensive tackle)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (offensive tackle)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_tackle)"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Irish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Dominican baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_G%C3%B3mez"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Estonian skier", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor and rapper", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and rapper", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Filipina singer and songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Yeng_Constantino\" title=\"Yeng Constantino\">Ye<PERSON></a>, Filipina singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yeng_Constantino\" title=\"Yeng Constantino\"><PERSON><PERSON></a>, Filipina singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yeng_Constantino"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Nigerian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Estonian triple jumper", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian triple jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Croatian basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American mixed martial artist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "Reality Winner, American intelligence specialist convicted of espionage", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Reality_Winner\" title=\"Reality Winner\">Reality Winner</a>, American intelligence specialist convicted of espionage", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reality_Winner\" title=\"Reality Winner\">Reality Winner</a>, American intelligence specialist convicted of espionage", "links": [{"title": "Reality Winner", "link": "https://wikipedia.org/wiki/Reality_Winner"}]}, {"year": "1992", "text": "<PERSON>, Belgian politician", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_B<PERSON><PERSON>%C3%A8re"}]}, {"year": "1992", "text": "<PERSON><PERSON>, New Zealand rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Hi<PERSON>\"><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Hiku\"><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Rwandan footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Rwandan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Rwandan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, South Korean singer, songwriter and actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer, songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer, songwriter and actor", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Danish basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Russian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, Chilean footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Vegas\" title=\"Sebastián Vegas\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Vegas\" title=\"Sebastián Vegas\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean footballer", "links": [{"title": "Se<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1n_Vegas"}]}, {"year": "1999", "text": "<PERSON>, South Korean singer and actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "2003", "text": "<PERSON>, American baseball player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "530 BC", "text": "<PERSON> the Great, king of Persia (b. 600 BC)", "html": "530 BC - 530 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, king of Persia (b. 600 BC)", "no_year_html": "530 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, king of Persia (b. 600 BC)", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}]}, {"year": "749", "text": "<PERSON> of Damascus, Syrian priest and saint (b. 676)", "html": "749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Damascus\" title=\"<PERSON> of Damascus\"><PERSON> of Damascus</a>, Syrian priest and saint (b. 676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Damascus\" title=\"<PERSON> of Damascus\"><PERSON> of Damascus</a>, Syrian priest and saint (b. 676)", "links": [{"title": "<PERSON> of Damascus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Damascus"}]}, {"year": "771", "text": "<PERSON><PERSON>, Frankish king (b. 751)", "html": "771 - <a href=\"https://wikipedia.org/wiki/Carloman_I\" title=\"Carloman I\">Carloman I</a>, Frankish king (b. 751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carloman_I\" title=\"Carloman I\">Carloman I</a>, Frankish king (b. 751)", "links": [{"title": "Carloman I", "link": "https://wikipedia.org/wiki/Carloman_I"}]}, {"year": "870", "text": "<PERSON><PERSON><PERSON><PERSON> ind <PERSON><PERSON><PERSON>, Irish bishop", "html": "870 - <a href=\"https://wikipedia.org/wiki/Suairlech_ind_Eidn%C3%A9n_mac_Ciar%C3%A1in\" title=\"Suairlech ind Eidnén mac <PERSON>iar<PERSON>in\">Suairlech ind Eidnén mac <PERSON></a>, Irish <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishop</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suairlech_ind_Eidn%C3%A9n_mac_Ciar%C3%A1in\" title=\"Suairlech ind Eidnén mac <PERSON>iaráin\">Suairlech ind E<PERSON>én mac <PERSON></a>, Irish <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishop</a>", "links": [{"title": "Su<PERSON><PERSON>ch ind <PERSON>én mac <PERSON>", "link": "https://wikipedia.org/wiki/Suairlech_ind_Eidn%C3%A9n_mac_Ciar%C3%A1in"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bishop"}]}, {"year": "1075", "text": "<PERSON><PERSON> <PERSON>, German archbishop and saint (b. 1010)", "html": "1075 - <a href=\"https://wikipedia.org/wiki/Anno_II\" title=\"Anno II\"><PERSON><PERSON> <PERSON></a>, German archbishop and saint (b. 1010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anno_II\" title=\"Anno II\"><PERSON><PERSON> <PERSON></a>, German archbishop and saint (b. 1010)", "links": [{"title": "Anno II", "link": "https://wikipedia.org/wiki/Anno_II"}]}, {"year": "1131", "text": "<PERSON>, Persian poet, astronomer, mathematician, and philosopher (b. 1048)", "html": "1131 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1m\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Persian poet, astronomer, mathematician, and philosopher (b. 1048)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1m\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Persian poet, astronomer, mathematician, and philosopher (b. 1048)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1m"}]}, {"year": "1214", "text": "<PERSON> the Lion, Scottish king (b. 1143)", "html": "1214 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Lion\" title=\"<PERSON> the Lion\"><PERSON> the Lion</a>, Scottish king (b. 1143)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Lion\" title=\"<PERSON> the Lion\"><PERSON> the Lion</a>, Scottish king (b. 1143)", "links": [{"title": "<PERSON> the Lion", "link": "https://wikipedia.org/wiki/<PERSON>_the_Lion"}]}, {"year": "1260", "text": "<PERSON><PERSON><PERSON>, Bishop of Winchester (b. 1222)", "html": "1260 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON><PERSON><PERSON> (bishop)\"><PERSON><PERSON><PERSON> de <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Bishop_of_Winchester\" title=\"Bishop of Winchester\">Bishop of Winchester</a> (b. 1222)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON><PERSON><PERSON> de <PERSON> (bishop)\">A<PERSON><PERSON> de <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Bishop_of_Winchester\" title=\"Bishop of Winchester\">Bishop of Winchester</a> (b. 1222)", "links": [{"title": "<PERSON><PERSON><PERSON> (bishop)", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>_<PERSON>_(bishop)"}, {"title": "Bishop of Winchester", "link": "https://wikipedia.org/wiki/<PERSON>_of_Winchester"}]}, {"year": "1270", "text": "<PERSON><PERSON><PERSON> of Navarre (b. 1238)", "html": "1270 - <a href=\"https://wikipedia.org/wiki/<PERSON>bald_II_of_Navarre\" title=\"Theobald II of Navarre\">Theobald II of Navarre</a> (b. 1238)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>bal<PERSON>_II_of_Navarre\" title=\"Theobald II of Navarre\">Theobald II of Navarre</a> (b. 1238)", "links": [{"title": "Theobald II of Navarre", "link": "https://wikipedia.org/wiki/Theobald_II_of_Navarre"}]}, {"year": "1334", "text": "<PERSON> (b. 1249)", "html": "1334 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John X<PERSON>II\"><PERSON> John X<PERSON></a> (b. 1249)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John X<PERSON>II\"><PERSON> <PERSON> X<PERSON></a> (b. 1249)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1340", "text": "<PERSON>, English bishop and politician, Lord Chancellor of England (b. 1292)", "html": "1340 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of England</a> (b. 1292)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of England</a> (b. 1292)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henry_<PERSON>"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1341", "text": "<PERSON><PERSON>, Archbishop of Gniezno", "html": "1341 - <a href=\"https://wikipedia.org/wiki/Janis%C5%82aw_(archbishop_of_Gniezno)\" title=\"<PERSON><PERSON> (archbishop of Gniezno)\"><PERSON><PERSON></a>, Archbishop of Gniezno", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Janis%C5%82aw_(archbishop_of_Gniezno)\" title=\"<PERSON><PERSON> (archbishop of Gniezno)\"><PERSON><PERSON></a>, Archbishop of Gniezno", "links": [{"title": "<PERSON><PERSON> (archbishop of Gniezno)", "link": "https://wikipedia.org/wiki/Janis%C5%82aw_(archbishop_of_Gniezno)"}]}, {"year": "1408", "text": "<PERSON><PERSON>, wife of <PERSON>, Duke of Orléans", "html": "1408 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_Orl%C3%A9ans\" title=\"<PERSON><PERSON>, Duchess of Orléans\"><PERSON><PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Orl%C3%A9ans\" class=\"mw-redirect\" title=\"<PERSON>ois, Duke of Orléans\"><PERSON> of Valois, Duke of Orléans</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_Orl%C3%A9ans\" title=\"<PERSON><PERSON>, Duchess of Orléans\"><PERSON><PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Orl%C3%A9ans\" class=\"mw-redirect\" title=\"<PERSON>ois, Duke of Orléans\"><PERSON> Valois, Duke of Orléans</a>", "links": [{"title": "<PERSON><PERSON>, Duchess of Orléans", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_Orl%C3%A9ans"}, {"title": "<PERSON> Valois, Duke of Orléans", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_<PERSON>_Orl%C3%A9ans"}]}, {"year": "1456", "text": "<PERSON>, Duke of Bourbon (b. 1401)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a> (b. 1401)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a> (b. 1401)", "links": [{"title": "<PERSON>, Duke of Bourbon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon"}]}, {"year": "1459", "text": "<PERSON><PERSON><PERSON>, Count of Holstein (b. 1401)", "html": "1459 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Holstein\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> VIII, Count of Holstein\"><PERSON><PERSON><PERSON>, Count of Holstein</a> (b. 1401)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Holstein\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Count of Holstein\"><PERSON><PERSON><PERSON>, Count of Holstein</a> (b. 1401)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Holstein", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Holstein"}]}, {"year": "1576", "text": "<PERSON>, Austrian-Slovak mathematician and cartographer (b. 1514)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Slovak mathematician and cartographer (b. 1514)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Slovak mathematician and cartographer (b. 1514)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1585", "text": "<PERSON>, Scottish minister and reformer (b. 1515)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and reformer (b. 1515)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and reformer (b. 1515)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1603", "text": "<PERSON><PERSON>, Flemish painter and draughtsman (b. 1532)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> V<PERSON>\"><PERSON><PERSON></a>, Flemish painter and draughtsman (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> V<PERSON>\"><PERSON><PERSON></a>, Flemish painter and draughtsman (b. 1532)", "links": [{"title": "<PERSON><PERSON> de V<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_de_<PERSON>"}]}, {"year": "1609", "text": "<PERSON>, Scottish poet (b. 1560)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet (b. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet (b. 1560)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1637", "text": "<PERSON>, English trader (b. 1592)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trader (b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trader (b. 1592)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1642", "text": "<PERSON><PERSON><PERSON>, French cardinal and politician, Chief Minister to the French Monarch (b. 1585)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\">Cardinal <PERSON></a>, French cardinal and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Chief Minister to the French Monarch</a> (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\">Cardinal <PERSON></a>, French cardinal and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Chief Minister to the French Monarch</a> (b. 1585)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France"}]}, {"year": "1649", "text": "<PERSON> of Hawthornden, Scottish poet (b. 1585)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hawthornden\" title=\"<PERSON> of Hawthornden\"><PERSON> of Hawthornden</a>, Scottish poet (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hawthornden\" title=\"<PERSON> of Hawthornden\"><PERSON> of Hawthornden</a>, Scottish poet (b. 1585)", "links": [{"title": "<PERSON> of Hawthornden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Hawthornden"}]}, {"year": "1679", "text": "<PERSON>, English philosopher and theorist (b. 1588)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and theorist (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and theorist (b. 1588)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON>, Danish physician, mathematician, and theologian (b. 1616)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish physician, mathematician, and theologian (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish physician, mathematician, and theologian (b. 1616)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1696", "text": "Empress <PERSON><PERSON><PERSON> of Japan (b. 1624)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>%C5%8D\" title=\"Empress <PERSON><PERSON><PERSON>\">Empress <PERSON><PERSON><PERSON></a> of Japan (b. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>%C5%8D\" title=\"Empress <PERSON><PERSON><PERSON>\">Empress <PERSON><PERSON><PERSON></a> of Japan (b. 1624)", "links": [{"title": "Empress <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%8D"}]}, {"year": "1732", "text": "<PERSON>, English poet and playwright (b. 1685)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1685)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, Italian physician, physicist, and philosopher (b. 1737)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician, physicist, and philosopher (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician, physicist, and philosopher (b. 1737)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, 2nd Earl of Liverpool, English politician, Prime Minister of the United Kingdom (b. 1770)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Liverpool\" title=\"<PERSON>, 2nd Earl of Liverpool\"><PERSON>, 2nd Earl of Liverpool</a>, English politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"List of Prime Ministers of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Liverpool\" title=\"<PERSON>, 2nd Earl of Liverpool\"><PERSON>, 2nd Earl of Liverpool</a>, English politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"List of Prime Ministers of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1770)", "links": [{"title": "<PERSON>, 2nd Earl of Liverpool", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Liverpool"}, {"title": "List of Prime Ministers of the United Kingdom", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_the_United_Kingdom"}]}, {"year": "1839", "text": "<PERSON>, Irish-American merchant (b. 1757)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(merchant)\" title=\"<PERSON> (merchant)\"><PERSON></a>, Irish-American merchant (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(merchant)\" title=\"<PERSON> (merchant)\"><PERSON></a>, Irish-American merchant (b. 1757)", "links": [{"title": "<PERSON> (merchant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(merchant)"}]}, {"year": "1841", "text": "<PERSON>, Welsh-English physician and academic (b. 1777)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English physician and academic (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English physician and academic (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, Scottish soldier and explorer (b. 1786)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier and explorer (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier and explorer (b. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, English physicist, invented the electric motor (b. 1783)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, invented the <a href=\"https://wikipedia.org/wiki/Electric_motor\" title=\"Electric motor\">electric motor</a> (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, invented the <a href=\"https://wikipedia.org/wiki/Electric_motor\" title=\"Electric motor\">electric motor</a> (b. 1783)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Electric motor", "link": "https://wikipedia.org/wiki/Electric_motor"}]}, {"year": "1893", "text": "<PERSON>, Irish-English physicist and chemist (b. 1820)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English physicist and chemist (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English physicist and chemist (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Welsh conductor (b. 1834)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Welsh conductor (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Welsh conductor (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American journalist and publisher, co-founded the Dow Jones & Company (b. 1851)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher, co-founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Company\" title=\"Dow Jones &amp; Company\">Dow Jones &amp; Company</a> (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher, co-founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Company\" title=\"Dow Jones &amp; Company\">Dow Jones &amp; Company</a> (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dow Jones & Company", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Company"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Slovenian painter (b. 1861)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian painter (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian painter (b. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, German-Swiss poet and translator (b. 1868)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss poet and translator (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss poet and translator (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Norwegian violinist, composer, and conductor (b. 1864)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian violinist, composer, and conductor (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian violinist, composer, and conductor (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, French physiologist and academic, Nobel Prize laureate (b. 1850)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 32nd <PERSON><PERSON><PERSON><PERSON> (b. 1903)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27emon\" title=\"<PERSON><PERSON><PERSON><PERSON> San'emon\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 32nd <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yoko<PERSON>na (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27emon\" title=\"<PERSON><PERSON><PERSON><PERSON> San'emon\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 32nd <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>na_(sumo)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>na (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> San'emon", "link": "https://wikipedia.org/wiki/Tamanishiki_San%27emon"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (sumo)", "link": "https://wikipedia.org/wiki/Yokozuna_(sumo)"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Estonian politician, 3rd Head of State of Estonia (b. 1885)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician, 3rd <a href=\"https://wikipedia.org/wiki/Head_of_State_of_Estonia\" class=\"mw-redirect\" title=\"Head of State of Estonia\">Head of State of Estonia</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician, 3rd <a href=\"https://wikipedia.org/wiki/Head_of_State_of_Estonia\" class=\"mw-redirect\" title=\"Head of State of Estonia\">Head of State of Estonia</a> (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k"}, {"title": "Head of State of Estonia", "link": "https://wikipedia.org/wiki/Head_of_State_of_Estonia"}]}, {"year": "1942", "text": "<PERSON>-<PERSON><PERSON>, Jewish Austrian librettist, lyricist and writer (b. 1883)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>-<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Jewish Austrian librettist, lyricist and writer (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>-<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Jewish Austrian librettist, lyricist and writer (b. 1883)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fritz_L%C3%B6<PERSON><PERSON>-<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American baseball player and manager (b. 1879)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American geneticist and biologist, Nobel Prize laureate (b. 1866)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Hungarian Olympic champion wrestler (b. 1879)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Rich%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian Olympic champion wrestler (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rich%C3%<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian Olympic champion wrestler (b. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rich%C3%A1rd_<PERSON>sz"}]}, {"year": "1948", "text": "<PERSON>, American physicist and engineer (b. 1883)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, 1st African-American Naval aviator (b. 1926)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 1st African-American Naval aviator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 1st African-American Naval aviator (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, 1st Baron <PERSON> (b. 1881)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON></a>, 1st Baron <PERSON> (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON></a>, 1st Baron <PERSON> (b. 1881)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian-American engineer (b. 1881)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-American engineer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-American engineer (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian psychologist (b. 1882)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian psychologist (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian psychologist (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor (b. 1895)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American Black Panthers activist (b. 1948)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Black_Panthers\" class=\"mw-redirect\" title=\"Black Panthers\">Black Panthers</a> activist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Black_Panthers\" class=\"mw-redirect\" title=\"Black Panthers\">Black Panthers</a> activist (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Black Panthers", "link": "https://wikipedia.org/wiki/Black_Panthers"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese-American monk and educator, founded the San Francisco Zen Center (b. 1904)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Shunry%C5%AB_Suzuki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese-American monk and educator, founded the <a href=\"https://wikipedia.org/wiki/San_Francisco_Zen_Center\" title=\"San Francisco Zen Center\">San Francisco Zen Center</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shunry%C5%AB_Suzuki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese-American monk and educator, founded the <a href=\"https://wikipedia.org/wiki/San_Francisco_Zen_Center\" title=\"San Francisco Zen Center\">San Francisco Zen Center</a> (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shunry%C5%AB_Suzuki"}, {"title": "San Francisco Zen Center", "link": "https://wikipedia.org/wiki/San_Francisco_Zen_Center"}]}, {"year": "1975", "text": "<PERSON>, German-American historian, theorist, and academic (b. 1906)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian, theorist, and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian, theorist, and academic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American guitarist and songwriter (b. 1951)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English pianist, composer, and conductor (b. 1913)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON> <PERSON><PERSON>, Irish soldier, lawyer, and politician (b. 1886)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish soldier, lawyer, and politician (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish soldier, lawyer, and politician (b. 1886)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Portuguese lawyer and politician, 111th Prime Minister of Portugal (b. 1934)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Francisco_de_S%C3%A1_Carneiro\" class=\"mw-redirect\" title=\"<PERSON> de Sá Carneiro\"><PERSON></a>, Portuguese lawyer and politician, 111th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_de_S%C3%A1_Carneiro\" class=\"mw-redirect\" title=\"<PERSON> Sá Carneiro\"><PERSON></a>, Portuguese lawyer and politician, 111th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> (b. 1934)", "links": [{"title": "<PERSON> Carneiro", "link": "https://wikipedia.org/wiki/Francisco_de_S%C3%A1_Carneiro"}, {"title": "Prime Minister of Portugal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Portugal"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Polish-American runner (b. 1911)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American runner (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American runner (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82awa_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian football player (b. 1948)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, Canadian football player (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, Canadian football player (b. 1948)", "links": [{"title": "<PERSON> (Canadian football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)"}]}, {"year": "1981", "text": "<PERSON>, American psychologist (b. 1923)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jeanne <PERSON>\"><PERSON></a>, American psychologist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jeanne <PERSON>\"><PERSON></a>, American psychologist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jeanne_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American animator, screenwriter, voice actor, and singer (b. 1910)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, screenwriter, voice actor, and singer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, screenwriter, voice actor, and singer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American author and illustrator (b. 1933)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Armenian-American director and screenwriter (b. 1897)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian-American director and screenwriter (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian-American director and screenwriter (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Polish chemist and academic (b. 1899)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish chemist and academic (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish chemist and academic (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American lawyer and author (b. 1905)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American missionary and author (b. 1903)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary and author (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary and author (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1940)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American academic and judge, 25th Chief Justice of California (b. 1936)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and judge, 25th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_California\" class=\"mw-redirect\" title=\"Chief Justice of California\">Chief Justice of California</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bird\"><PERSON></a>, American academic and judge, 25th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_California\" class=\"mw-redirect\" title=\"Chief Justice of California\">Chief Justice of California</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of California", "link": "https://wikipedia.org/wiki/Chief_Justice_of_California"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Surinamese banker and politician, 1st Prime Minister of the Republic of Suriname (b. 1936)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Surinamese banker and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Suriname#Prime_Ministers_of_the_Republic_of_Suriname\" class=\"mw-redirect\" title=\"List of Prime Ministers of Suriname\">Prime Minister of the Republic of Suriname</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Surinamese banker and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Suriname#Prime_Ministers_of_the_Republic_of_Suriname\" class=\"mw-redirect\" title=\"List of Prime Ministers of Suriname\">Prime Minister of the Republic of Suriname</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "List of Prime Ministers of Suriname", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Suriname#Prime_Ministers_of_the_Republic_of_Suriname"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American race car driver (b. 1916)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Iggy Katona\"><PERSON><PERSON></a>, American race car driver (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Iggy Katona\"><PERSON><PERSON></a>, American race car driver (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_<PERSON><PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Greek soprano and actress (b. 1943)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek soprano and actress (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek soprano and actress (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, New Zealand soldier and author (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand soldier and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand soldier and author (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ite"}]}, {"year": "2005", "text": "<PERSON>, American film producer (b. 1963)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Sri Lankan accountant and politician, Mayor of Colombo (b. 1938)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/K._<PERSON>\" title=\"K. G<PERSON>\"><PERSON><PERSON></a>, Sri Lankan accountant and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Colombo\" title=\"Mayor of Colombo\">Mayor of Colombo</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K._<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan accountant and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Colombo\" title=\"Mayor of Colombo\">Mayor of Colombo</a> (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K<PERSON>_<PERSON><PERSON><PERSON>am"}, {"title": "Mayor of Colombo", "link": "https://wikipedia.org/wiki/Mayor_of_Colombo"}]}, {"year": "2006", "text": "<PERSON>, American soldier, Medal of Honor recipient (b. 1987)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American rapper (b. 1973)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Pimp_C\" title=\"Pimp C\"><PERSON><PERSON></a>, American rapper (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pimp_C\" title=\"Pimp C\"><PERSON><PERSON></a>, American rapper (b. 1973)", "links": [{"title": "Pimp C", "link": "https://wikipedia.org/wiki/Pimp_C"}]}, {"year": "2009", "text": "<PERSON>, Irish singer, actor, and guitarist (b. 1935)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer, actor, and guitarist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer, actor, and guitarist (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American wrestler (b. 1937)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"King <PERSON>\">King <PERSON></a>, American wrestler (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"King <PERSON>\"><PERSON> <PERSON></a>, American wrestler (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Haitian-Dominican activist (b. 1965)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian-Dominican activist (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian-Dominican activist (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and manager (b. 1954)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/S%C3%B3crates\" title=\"Sócrates\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%B3crates\" title=\"Sócrates\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%B3crates"}]}, {"year": "2011", "text": "<PERSON>, American singer and guitarist (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Russian author, poet, and playwright (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author, poet, and playwright (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author, poet, and playwright (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American colonel, lawyer, and politician (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American colonel, lawyer, and politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American colonel, lawyer, and politician (b. 1922)", "links": [{"title": "<PERSON> (American politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)"}]}, {"year": "2012", "text": "<PERSON>, Colombian footballer and manager (b. 1971)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer and manager (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer and manager (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, English general (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Spanish author and poet (b. 1913)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_i_Juanola\" title=\"<PERSON><PERSON> Ra<PERSON>all i Juanola\"><PERSON><PERSON> Juanola</a>, Spanish author and poet (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_i_Juanola\" title=\"<PERSON><PERSON> i Juanola\"><PERSON><PERSON></a>, Spanish author and poet (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American poet and academic (b. 1957)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and judge (b. 1914)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\">V<PERSON> <PERSON><PERSON></a>, Indian lawyer and judge (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and judge (b. 1914)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and judge (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English lawyer and politician (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian lawyer and politician, 27th Premier of British Columbia (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of British Columbia", "link": "https://wikipedia.org/wiki/Premier_of_British_Columbia"}]}, {"year": "2015", "text": "<PERSON>, American actor and director (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Israeli journalist and politician, 15th Israeli Minister of Education (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli journalist and politician, 15th <a href=\"https://wikipedia.org/wiki/Ministry_of_Education_(Israel)\" title=\"Ministry of Education (Israel)\">Israeli Minister of Education</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli journalist and politician, 15th <a href=\"https://wikipedia.org/wiki/Ministry_of_Education_(Israel)\" title=\"Ministry of Education (Israel)\">Israeli Minister of Education</a> (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Education (Israel)", "link": "https://wikipedia.org/wiki/Ministry_of_Education_(Israel)"}]}, {"year": "2016", "text": "<PERSON>, British writer and WAAF officer (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer and WAAF officer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer and WAAF officer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Indian actor (b. 1938)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American singer and actor (b. 1932)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, French race car driver (b. 1949)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "Princess <PERSON><PERSON><PERSON><PERSON> of Sweden, Swedish royal (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON><PERSON>_of_Sweden\" title=\"Princess <PERSON><PERSON><PERSON><PERSON> of Sweden\">Princess <PERSON><PERSON><PERSON><PERSON> of Sweden</a>, Swedish royal (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON><PERSON>_of_Sweden\" title=\"Princess <PERSON><PERSON><PERSON><PERSON> of Sweden\">Princess <PERSON><PERSON><PERSON><PERSON> of Sweden</a>, Swedish royal (b. 1937)", "links": [{"title": "Princess <PERSON><PERSON><PERSON><PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON><PERSON>_of_Sweden"}]}, {"year": "2024", "text": "<PERSON>, American insurance executive (b. 1974)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" class=\"mw-redirect\" title=\"<PERSON> (businessman)\"><PERSON></a>, American insurance executive (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" class=\"mw-redirect\" title=\"<PERSON> (businessman)\"><PERSON></a>, American insurance executive (b. 1974)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_(businessman)"}]}]}}