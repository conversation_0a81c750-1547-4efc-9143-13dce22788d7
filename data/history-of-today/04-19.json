{"date": "April 19", "url": "https://wikipedia.org/wiki/April_19", "data": {"Events": [{"year": "65", "text": "The freedman <PERSON><PERSON><PERSON> betrays <PERSON><PERSON>'s plot to kill the Emperor <PERSON> and all of the conspirators are arrested.", "html": "65 - AD 65 - The <a href=\"https://wikipedia.org/wiki/Freedman\" title=\"Freedman\">freedman</a> <a href=\"https://wikipedia.org/wiki/Milichus\" class=\"mw-redirect\" title=\"Milichus\"><PERSON><PERSON><PERSON></a> betrays <a href=\"https://wikipedia.org/wiki/Pisonian_conspiracy\" title=\"Pisonian conspiracy\"><PERSON><PERSON>'s plot</a> to kill the <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Nero\" title=\"<PERSON>\"><PERSON></a> and all of the <a href=\"https://wikipedia.org/wiki/List_of_conspiracies_(political)\" class=\"mw-redirect\" title=\"List of conspiracies (political)\">conspirators</a> are arrested.", "no_year_html": "AD 65 - The <a href=\"https://wikipedia.org/wiki/Freedman\" title=\"Freedman\">freedman</a> <a href=\"https://wikipedia.org/wiki/Milichus\" class=\"mw-redirect\" title=\"Milichus\"><PERSON><PERSON><PERSON></a> betrays <a href=\"https://wikipedia.org/wiki/Pisonian_conspiracy\" title=\"Pisonian conspiracy\"><PERSON><PERSON>'s plot</a> to kill the <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Nero\" title=\"<PERSON>\"><PERSON></a> and all of the <a href=\"https://wikipedia.org/wiki/List_of_conspiracies_(political)\" class=\"mw-redirect\" title=\"List of conspiracies (political)\">conspirators</a> are arrested.", "links": [{"title": "<PERSON>dman", "link": "https://wikipedia.org/wiki/<PERSON>dman"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milichus"}, {"title": "Pisonian conspiracy", "link": "https://wikipedia.org/wiki/Pisonian_conspiracy"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nero"}, {"title": "List of conspiracies (political)", "link": "https://wikipedia.org/wiki/List_of_conspiracies_(political)"}]}, {"year": "531", "text": "Battle of Callinicum: A Byzantine army under <PERSON><PERSON><PERSON> is defeated by the Persians at Raqqa (northern Syria).", "html": "531 - <a href=\"https://wikipedia.org/wiki/Battle_of_Callinicum\" title=\"Battle of Callinicum\">Battle of Callinicum</a>: A <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> army under <a href=\"https://wikipedia.org/wiki/Belisarius\" title=\"Belisarius\"><PERSON><PERSON><PERSON></a> is defeated by the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Persians</a> at <a href=\"https://wikipedia.org/wiki/Raqqa\" title=\"<PERSON>q<PERSON>\"><PERSON>q<PERSON></a> (northern <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Callinicum\" title=\"Battle of Callinicum\">Battle of Callinicum</a>: A <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> army under <a href=\"https://wikipedia.org/wiki/Belisarius\" title=\"Belisarius\"><PERSON><PERSON><PERSON></a> is defeated by the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Persians</a> at <a href=\"https://wikipedia.org/wiki/Raqqa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON>q<PERSON></a> (northern <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>).", "links": [{"title": "Battle of Callinicum", "link": "https://wikipedia.org/wiki/Battle_of_Callinicum"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Belisarius"}, {"title": "Sasanian Empire", "link": "https://wikipedia.org/wiki/Sasanian_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>qqa"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}, {"year": "1506", "text": "The Lisbon Massacre begins, in which accused Jews are slaughtered by Portuguese Catholics.", "html": "1506 - The <a href=\"https://wikipedia.org/wiki/Lisbon_Massacre\" class=\"mw-redirect\" title=\"Lisbon Massacre\">Lisbon Massacre</a> begins, in which accused Jews are slaughtered by <a href=\"https://wikipedia.org/wiki/Portuguese_people\" title=\"Portuguese people\">Portuguese</a> Catholics.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lisbon_Massacre\" class=\"mw-redirect\" title=\"Lisbon Massacre\">Lisbon Massacre</a> begins, in which accused Jews are slaughtered by <a href=\"https://wikipedia.org/wiki/Portuguese_people\" title=\"Portuguese people\">Portuguese</a> Catholics.", "links": [{"title": "Lisbon Massacre", "link": "https://wikipedia.org/wiki/Lisbon_Massacre"}, {"title": "Portuguese people", "link": "https://wikipedia.org/wiki/Portuguese_people"}]}, {"year": "1529", "text": "Beginning of the Protestant Reformation: After the Second Diet of Speyer bans Lutheranism, a group of rulers (German: Fürst) and independent cities protests the reinstatement of the Edict of Worms.", "html": "1529 - Beginning of the <a href=\"https://wikipedia.org/wiki/Protestant_Reformation\" class=\"mw-redirect\" title=\"Protestant Reformation\">Protestant Reformation</a>: After the <a href=\"https://wikipedia.org/wiki/Second_Diet_of_Speyer\" class=\"mw-redirect\" title=\"Second Diet of Speyer\">Second Diet of Speyer</a> bans Lutheranism, a group of rulers (<i>German:</i> <a href=\"https://wikipedia.org/wiki/F%C3%BCrst\" title=\"Fürst\">Fürst</a>) and <a href=\"https://wikipedia.org/wiki/Free_imperial_city\" title=\"Free imperial city\">independent cities</a> <a href=\"https://wikipedia.org/wiki/Protestation_at_Speyer\" title=\"Protestation at Speyer\">protests</a> the reinstatement of the <a href=\"https://wikipedia.org/wiki/Edict_of_Worms\" class=\"mw-redirect\" title=\"Edict of Worms\">Edict of Worms</a>.", "no_year_html": "Beginning of the <a href=\"https://wikipedia.org/wiki/Protestant_Reformation\" class=\"mw-redirect\" title=\"Protestant Reformation\">Protestant Reformation</a>: After the <a href=\"https://wikipedia.org/wiki/Second_Diet_of_Speyer\" class=\"mw-redirect\" title=\"Second Diet of Speyer\">Second Diet of Speyer</a> bans Lutheranism, a group of rulers (<i>German:</i> <a href=\"https://wikipedia.org/wiki/F%C3%BCrst\" title=\"Fürst\">Fürst</a>) and <a href=\"https://wikipedia.org/wiki/Free_imperial_city\" title=\"Free imperial city\">independent cities</a> <a href=\"https://wikipedia.org/wiki/Protestation_at_Speyer\" title=\"Protestation at Speyer\">protests</a> the reinstatement of the <a href=\"https://wikipedia.org/wiki/Edict_of_Worms\" class=\"mw-redirect\" title=\"Edict of Worms\">Edict of Worms</a>.", "links": [{"title": "Protestant Reformation", "link": "https://wikipedia.org/wiki/Protestant_Reformation"}, {"title": "Second Diet of Speyer", "link": "https://wikipedia.org/wiki/Second_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F%C3%BCrst"}, {"title": "Free imperial city", "link": "https://wikipedia.org/wiki/Free_imperial_city"}, {"title": "Protestation at Speyer", "link": "https://wikipedia.org/wiki/Protestation_at_Speyer"}, {"title": "Edict of Worms", "link": "https://wikipedia.org/wiki/Edict_of_Worms"}]}, {"year": "1539", "text": "The Treaty of Frankfurt between Protestants and the Holy Roman Emperor is signed.", "html": "1539 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Frankfurt_(1539)\" title=\"Treaty of Frankfurt (1539)\">Treaty of Frankfurt</a> between Protestants and the Holy Roman Emperor is signed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Frankfurt_(1539)\" title=\"Treaty of Frankfurt (1539)\">Treaty of Frankfurt</a> between Protestants and the Holy Roman Emperor is signed.", "links": [{"title": "Treaty of Frankfurt (1539)", "link": "https://wikipedia.org/wiki/Treaty_of_Frankfurt_(1539)"}]}, {"year": "1608", "text": "In Ireland, <PERSON><PERSON>'s Rebellion is launched by the Burning of Derry.", "html": "1608 - In Ireland, <a href=\"https://wikipedia.org/wiki/O%27D<PERSON>erty%27s_Rebellion\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Rebellion\"><PERSON><PERSON><PERSON>'s Rebellion</a> is launched by the <a href=\"https://wikipedia.org/wiki/Burning_of_Derry\" title=\"Burning of Derry\">Burning of Derry</a>.", "no_year_html": "In Ireland, <a href=\"https://wikipedia.org/wiki/O%27Doh<PERSON>%27s_Rebellion\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Rebellion\"><PERSON><PERSON><PERSON>'s Rebellion</a> is launched by the <a href=\"https://wikipedia.org/wiki/Burning_of_Derry\" title=\"Burning of Derry\">Burning of Derry</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>'s Rebellion", "link": "https://wikipedia.org/wiki/O%27Doherty%27s_Rebellion"}, {"title": "Burning of Derry", "link": "https://wikipedia.org/wiki/Burning_of_Derry"}]}, {"year": "1677", "text": "The French army captures the town of Cambrai held by Spanish troops.", "html": "1677 - The French army <a href=\"https://wikipedia.org/wiki/Siege_of_Cambrai_(1677)\" title=\"Siege of Cambrai (1677)\">captures the town of Cambrai</a> held by Spanish troops.", "no_year_html": "The French army <a href=\"https://wikipedia.org/wiki/Siege_of_Cambrai_(1677)\" title=\"Siege of Cambrai (1677)\">captures the town of Cambrai</a> held by Spanish troops.", "links": [{"title": "Siege of Cambrai (1677)", "link": "https://wikipedia.org/wiki/Siege_of_Cambrai_(1677)"}]}, {"year": "1713", "text": "With no living male heirs, <PERSON>, Holy Roman Emperor, issues the Pragmatic Sanction of 1713 to ensure that Habsburg lands and the Austrian throne would be inheritable by a female; his daughter and successor, <PERSON>, was not born until 1717.", "html": "1713 - With no living male heirs, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VI, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, issues the <a href=\"https://wikipedia.org/wiki/Pragmatic_Sanction_of_1713\" title=\"Pragmatic Sanction of 1713\">Pragmatic Sanction of 1713</a> to ensure that <a href=\"https://wikipedia.org/wiki/House_of_Habsburg\" title=\"House of Habsburg\">Habsburg</a> lands and the <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Austrian throne</a> would be inheritable by a female; his daughter and successor, <a href=\"https://wikipedia.org/wiki/<PERSON>_Theresa\" title=\"<PERSON>\"><PERSON></a>, was not born until 1717.", "no_year_html": "With no living male heirs, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VI, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, issues the <a href=\"https://wikipedia.org/wiki/Pragmatic_Sanction_of_1713\" title=\"Pragmatic Sanction of 1713\">Pragmatic Sanction of 1713</a> to ensure that <a href=\"https://wikipedia.org/wiki/House_of_Habsburg\" title=\"House of Habsburg\">Habsburg</a> lands and the <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Austrian throne</a> would be inheritable by a female; his daughter and successor, <a href=\"https://wikipedia.org/wiki/<PERSON>_Theresa\" title=\"<PERSON>\"><PERSON></a>, was not born until 1717.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Pragmatic Sanction of 1713", "link": "https://wikipedia.org/wiki/Pragmatic_Sanction_of_1713"}, {"title": "House of Habsburg", "link": "https://wikipedia.org/wiki/House_of_Habsburg"}, {"title": "Habsburg monarchy", "link": "https://wikipedia.org/wiki/Habsburg_monarchy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "Captain <PERSON>, still holding the rank of lieutenant, sights the eastern coast of what is now Australia.", "html": "1770 - Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, still holding the rank of lieutenant, sights the eastern coast of what is now Australia.", "no_year_html": "Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, still holding the rank of lieutenant, sights the eastern coast of what is now Australia.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON> marries <PERSON> in a proxy wedding.", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_XVI\" title=\"<PERSON> XVI\"><PERSON></a> in a <a href=\"https://wikipedia.org/wiki/Proxy_marriage\" title=\"Proxy marriage\">proxy wedding</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/Louis_XVI\" title=\"Louis XVI\"><PERSON></a> in a <a href=\"https://wikipedia.org/wiki/Proxy_marriage\" title=\"Proxy marriage\">proxy wedding</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Proxy marriage", "link": "https://wikipedia.org/wiki/Proxy_marriage"}]}, {"year": "1775", "text": "American Revolutionary War: The war begins with an American victory in Concord during the battles of Lexington and Concord.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The war begins with an American victory in Concord during the <a href=\"https://wikipedia.org/wiki/Battles_of_Lexington_and_Concord\" title=\"Battles of Lexington and Concord\">battles of Lexington and Concord</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The war begins with an American victory in Concord during the <a href=\"https://wikipedia.org/wiki/Battles_of_Lexington_and_Concord\" title=\"Battles of Lexington and Concord\">battles of Lexington and Concord</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battles of Lexington and Concord", "link": "https://wikipedia.org/wiki/Battles_of_Lexington_and_Concord"}]}, {"year": "1782", "text": "<PERSON> secures Dutch recognition of the United States as an independent government. The house which he had purchased in The Hague becomes the first American embassy.", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> secures <a href=\"https://wikipedia.org/wiki/Netherlands%E2%80%93United_States_relations\" title=\"Netherlands-United States relations\">Dutch recognition of the United States</a> as an independent government. The house which he had purchased in <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a> becomes the first American embassy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> secures <a href=\"https://wikipedia.org/wiki/Netherlands%E2%80%93United_States_relations\" title=\"Netherlands-United States relations\">Dutch recognition of the United States</a> as an independent government. The house which he had purchased in <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a> becomes the first American embassy.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Netherlands-United States relations", "link": "https://wikipedia.org/wiki/Netherlands%E2%80%93United_States_relations"}, {"title": "The Hague", "link": "https://wikipedia.org/wiki/The_Hague"}]}, {"year": "1809", "text": "An Austrian corps is defeated by the forces of the Duchy of Warsaw in the Battle of Raszyn, part of the struggles of the Fifth Coalition. On the same day the Austrian main army is defeated by a First French Empire Corps led by <PERSON><PERSON><PERSON> at the Battle of Teugen-Hausen in Bavaria, part of a four-day campaign that ended in a French victory.", "html": "1809 - An Austrian corps is defeated by the forces of the <a href=\"https://wikipedia.org/wiki/Duchy_of_Warsaw\" title=\"Duchy of Warsaw\">Duchy of Warsaw</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Raszyn_(1809)\" title=\"Battle of Raszyn (1809)\">Battle of Raszyn</a>, part of the struggles of the <a href=\"https://wikipedia.org/wiki/Fifth_Coalition\" class=\"mw-redirect\" title=\"Fifth Coalition\">Fifth Coalition</a>. On the same day the Austrian main army is defeated by a <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">First French Empire</a> Corps led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Teugen-Hausen\" title=\"Battle of Teugen-Hausen\">Battle of Teugen-Hausen</a> in Bavaria, part of a four-day campaign that ended in a French victory.", "no_year_html": "An Austrian corps is defeated by the forces of the <a href=\"https://wikipedia.org/wiki/Duchy_of_Warsaw\" title=\"Duchy of Warsaw\">Duchy of Warsaw</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Raszyn_(1809)\" title=\"Battle of Raszyn (1809)\">Battle of Raszyn</a>, part of the struggles of the <a href=\"https://wikipedia.org/wiki/Fifth_Coalition\" class=\"mw-redirect\" title=\"Fifth Coalition\">Fifth Coalition</a>. On the same day the Austrian main army is defeated by a <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">First French Empire</a> Corps led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Teugen-Hausen\" title=\"Battle of Teugen-Hausen\">Battle of Teugen-Hausen</a> in Bavaria, part of a four-day campaign that ended in a French victory.", "links": [{"title": "Duchy of Warsaw", "link": "https://wikipedia.org/wiki/Duchy_of_Warsaw"}, {"title": "Battle of Raszyn (1809)", "link": "https://wikipedia.org/wiki/Battle_of_Raszyn_(1809)"}, {"title": "Fifth Coalition", "link": "https://wikipedia.org/wiki/Fifth_Coalition"}, {"title": "First French Empire", "link": "https://wikipedia.org/wiki/First_French_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Battle of Teugen-Hausen", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON>-<PERSON>n"}]}, {"year": "1810", "text": "Venezuela achieves home rule: <PERSON>, Governor of the Captaincy General is removed by the people of Caracas and a junta is installed.", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a> achieves home rule: <a href=\"https://wikipedia.org/wiki/Vicente_Empar%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Governor\" title=\"Governor\">Governor</a> of the <a href=\"https://wikipedia.org/wiki/Captaincy_General\" class=\"mw-redirect\" title=\"Captaincy General\">Captaincy General</a> is removed by the people of <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas</a> and a <a href=\"https://wikipedia.org/wiki/Supreme_Junta\" title=\"Supreme Junta\">junta</a> is installed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a> achieves home rule: <a href=\"https://wikipedia.org/wiki/Vicente_Empar%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Governor\" title=\"Governor\">Governor</a> of the <a href=\"https://wikipedia.org/wiki/Captaincy_General\" class=\"mw-redirect\" title=\"Captaincy General\">Captaincy General</a> is removed by the people of <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas</a> and a <a href=\"https://wikipedia.org/wiki/Supreme_Junta\" title=\"Supreme Junta\">junta</a> is installed.", "links": [{"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vicente_Empar%C3%A1n"}, {"title": "Governor", "link": "https://wikipedia.org/wiki/Governor"}, {"title": "Captaincy General", "link": "https://wikipedia.org/wiki/Captaincy_General"}, {"title": "Caracas", "link": "https://wikipedia.org/wiki/Caracas"}, {"title": "Supreme Junta", "link": "https://wikipedia.org/wiki/Supreme_Junta"}]}, {"year": "1818", "text": "French physicist <PERSON><PERSON> signs his preliminary \"Note on the Theory of Diffraction\" (deposited on the following day). The document ends with what we now call the <PERSON><PERSON><PERSON> integrals.", "html": "1818 - French physicist <a href=\"https://wikipedia.org/wiki/August<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">August<PERSON></a> signs his preliminary \"Note on the Theory of Diffraction\" (deposited on the following day). The document ends with what we now call the <a href=\"https://wikipedia.org/wiki/Fresnel_integral\" title=\"Fresnel integral\">Fresnel integrals</a>.", "no_year_html": "French physicist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">August<PERSON></a> signs his preliminary \"Note on the Theory of Diffraction\" (deposited on the following day). The document ends with what we now call the <a href=\"https://wikipedia.org/wiki/Fresnel_integral\" title=\"Fresnel integral\">Fresnel integrals</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}, {"title": "Fresnel integral", "link": "https://wikipedia.org/wiki/Fresnel_integral"}]}, {"year": "1839", "text": "The Treaty of London establishes Belgium as a kingdom and guarantees its neutrality.", "html": "1839 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_London_(1839)\" title=\"Treaty of London (1839)\">Treaty of London</a> establishes Belgium as a kingdom and guarantees its neutrality.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_London_(1839)\" title=\"Treaty of London (1839)\">Treaty of London</a> establishes Belgium as a kingdom and guarantees its neutrality.", "links": [{"title": "Treaty of London (1839)", "link": "https://wikipedia.org/wiki/Treaty_of_London_(1839)"}]}, {"year": "1861", "text": "American Civil War: Baltimore riot of 1861: A pro-Secession mob in Baltimore attacks United States Army troops marching through the city.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Baltimore_riot_of_1861\" title=\"Baltimore riot of 1861\">Baltimore riot of 1861</a>: A pro-<a href=\"https://wikipedia.org/wiki/Secession_in_the_United_States#Confederate_States_of_America\" title=\"Secession in the United States\">Secession</a> mob in <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a> attacks United States Army troops marching through the city.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Baltimore_riot_of_1861\" title=\"Baltimore riot of 1861\">Baltimore riot of 1861</a>: A pro-<a href=\"https://wikipedia.org/wiki/Secession_in_the_United_States#Confederate_States_of_America\" title=\"Secession in the United States\">Secession</a> mob in <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a> attacks United States Army troops marching through the city.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Baltimore riot of 1861", "link": "https://wikipedia.org/wiki/Baltimore_riot_of_1861"}, {"title": "Secession in the United States", "link": "https://wikipedia.org/wiki/Secession_in_the_United_States#Confederate_States_of_America"}, {"title": "Baltimore", "link": "https://wikipedia.org/wiki/Baltimore"}]}, {"year": "1903", "text": "The Kishinev pogrom in Kishinev (Bessarabia) begins, forcing tens of thousands of Jews to later seek refuge in Palestine and the Western world.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_pogrom_(1903)\" class=\"mw-redirect\" title=\"Chișinău pogrom (1903)\">Kishinev pogrom</a> in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>shin<PERSON>\"><PERSON><PERSON><PERSON></a> (<a href=\"https://wikipedia.org/wiki/Bessarabia\" title=\"Bessarabia\">Bessarabia</a>) begins, forcing tens of thousands of Jews to later seek refuge in <a href=\"https://wikipedia.org/wiki/History_of_Palestine#Ottoman_period\" title=\"History of Palestine\">Palestine</a> and the <a href=\"https://wikipedia.org/wiki/Western_world\" title=\"Western world\">Western world</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_pogrom_(1903)\" class=\"mw-redirect\" title=\"Chișinău pogrom (1903)\">Ki<PERSON>ev pogrom</a> in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>shin<PERSON>\"><PERSON><PERSON><PERSON></a> (<a href=\"https://wikipedia.org/wiki/Bessarabia\" title=\"Bessarabia\">Bessarabia</a>) begins, forcing tens of thousands of Jews to later seek refuge in <a href=\"https://wikipedia.org/wiki/History_of_Palestine#Ottoman_period\" title=\"History of Palestine\">Palestine</a> and the <a href=\"https://wikipedia.org/wiki/Western_world\" title=\"Western world\">Western world</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> pogrom (1903)", "link": "https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_pogrom_(1903)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Bessarabia", "link": "https://wikipedia.org/wiki/Bessarabia"}, {"title": "History of Palestine", "link": "https://wikipedia.org/wiki/History_of_Palestine#Ottoman_period"}, {"title": "Western world", "link": "https://wikipedia.org/wiki/Western_world"}]}, {"year": "1927", "text": "<PERSON> is sentenced to ten days in jail for obscenity for her play <PERSON>.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Mae_West\" title=\"Mae West\"><PERSON></a> is sentenced to ten days in jail for <a href=\"https://wikipedia.org/wiki/Obscenity\" title=\"Obscenity\">obscenity</a> for her play <i><a href=\"https://wikipedia.org/wiki/Sex_(play)\" title=\"Sex (play)\">Sex</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mae_West\" title=\"Mae West\"><PERSON></a> is sentenced to ten days in jail for <a href=\"https://wikipedia.org/wiki/Obscenity\" title=\"Obscenity\">obscenity</a> for her play <i><a href=\"https://wikipedia.org/wiki/Sex_(play)\" title=\"Sex (play)\">Sex</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mae_West"}, {"title": "Obscenity", "link": "https://wikipedia.org/wiki/Obscenity"}, {"title": "Sex (play)", "link": "https://wikipedia.org/wiki/Sex_(play)"}]}, {"year": "1936", "text": "The Jaffa riots commence, initiating the 1936-1939 Arab revolt in Palestine.", "html": "1936 - The <a href=\"https://wikipedia.org/wiki/Jaffa_riots_(April_1936)\" title=\"Jaffa riots (April 1936)\">Jaffa riots</a> commence, initiating the <a href=\"https://wikipedia.org/wiki/1936%E2%80%931939_Arab_revolt_in_Palestine\" title=\"1936-1939 Arab revolt in Palestine\">1936-1939 Arab revolt in Palestine</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Jaffa_riots_(April_1936)\" title=\"Jaffa riots (April 1936)\">Jaffa riots</a> commence, initiating the <a href=\"https://wikipedia.org/wiki/1936%E2%80%931939_Arab_revolt_in_Palestine\" title=\"1936-1939 Arab revolt in Palestine\">1936-1939 Arab revolt in Palestine</a>.", "links": [{"title": "Jaffa riots (April 1936)", "link": "https://wikipedia.org/wiki/Jaffa_riots_(April_1936)"}, {"title": "1936-1939 Arab revolt in Palestine", "link": "https://wikipedia.org/wiki/1936%E2%80%931939_Arab_revolt_in_Palestine"}]}, {"year": "1942", "text": "World War II: In German-occupied Poland, the Majdan-Tatarski ghetto is established, situated between the Lublin Ghetto and a Majdanek subcamp.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In German-occupied Poland, the Majdan-Tatarski <a href=\"https://wikipedia.org/wiki/Ghetto\" title=\"Ghetto\">ghetto</a> is established, situated between the <a href=\"https://wikipedia.org/wiki/Lublin_Ghetto\" title=\"Lublin Ghetto\">Lublin Ghetto</a> and a <a href=\"https://wikipedia.org/wiki/Majdanek\" class=\"mw-redirect\" title=\"Majdanek\">Majdanek</a> subcamp.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In German-occupied Poland, the Majdan-Tatarski <a href=\"https://wikipedia.org/wiki/Ghetto\" title=\"Ghetto\">ghetto</a> is established, situated between the <a href=\"https://wikipedia.org/wiki/Lublin_Ghetto\" title=\"Lublin Ghetto\">Lublin Ghetto</a> and a <a href=\"https://wikipedia.org/wiki/Majdanek\" class=\"mw-redirect\" title=\"Majdanek\">Majdanek</a> subcamp.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Ghetto", "link": "https://wikipedia.org/wiki/Ghetto"}, {"title": "Lublin Ghetto", "link": "https://wikipedia.org/wiki/Lublin_Ghetto"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1943", "text": "World War II: In German-occupied Poland, the Warsaw Ghetto Uprising begins, after German troops enter the Warsaw Ghetto to round up the remaining Jews.", "html": "1943 - World War II: In German-occupied Poland, the <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising\" title=\"Warsaw Ghetto Uprising\">Warsaw Ghetto Uprising</a> begins, after <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops enter the <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto\" title=\"Warsaw Ghetto\">Warsaw Ghetto</a> to round up the remaining Jews.", "no_year_html": "World War II: In German-occupied Poland, the <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising\" title=\"Warsaw Ghetto Uprising\">Warsaw Ghetto Uprising</a> begins, after <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops enter the <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto\" title=\"Warsaw Ghetto\">Warsaw Ghetto</a> to round up the remaining Jews.", "links": [{"title": "Warsaw Ghetto Uprising", "link": "https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Warsaw Ghetto", "link": "https://wikipedia.org/wiki/Warsaw_Ghetto"}]}, {"year": "1943", "text": "<PERSON> deliberately doses himself with LSD for the first time, three days after having discovered its effects on April 16, an event commonly known and celebrated as Bicycle Day.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> deliberately doses himself with <a href=\"https://wikipedia.org/wiki/LSD\" title=\"LSD\">LSD</a> for the first time, three days after having discovered its effects on <a href=\"https://wikipedia.org/wiki/April_16\" title=\"April 16\">April 16</a>, an event commonly known and celebrated as <a href='https://wikipedia.org/wiki/History_of_lysergic_acid_diethylamide#\"Bicycle_Day\"' class=\"mw-redirect\" title=\"History of lysergic acid diethylamide\">Bicycle Day</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> deliberately doses himself with <a href=\"https://wikipedia.org/wiki/LSD\" title=\"LSD\">LSD</a> for the first time, three days after having discovered its effects on <a href=\"https://wikipedia.org/wiki/April_16\" title=\"April 16\">April 16</a>, an event commonly known and celebrated as <a href='https://wikipedia.org/wiki/History_of_lysergic_acid_diethylamide#\"Bicycle_Day\"' class=\"mw-redirect\" title=\"History of lysergic acid diethylamide\">Bicycle Day</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "LSD", "link": "https://wikipedia.org/wiki/LSD"}, {"title": "April 16", "link": "https://wikipedia.org/wiki/April_16"}, {"title": "History of lysergic acid diethylamide", "link": "https://wikipedia.org/wiki/History_of_lysergic_acid_diethylamide#\"Bicycle_Day\""}]}, {"year": "1956", "text": "Actress <PERSON> marries <PERSON> of Monaco.", "html": "1956 - Actress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON><PERSON>, Prince of Monaco\">Prince <PERSON> of Monaco</a>.", "no_year_html": "Actress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON><PERSON>, Prince of Monaco\">Prince <PERSON> of Monaco</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco"}]}, {"year": "1960", "text": "Students in South Korea hold a nationwide pro-democracy protest against president <PERSON><PERSON><PERSON>, eventually forcing him to resign.", "html": "1960 - Students in South Korea hold <a href=\"https://wikipedia.org/wiki/April_Revolution\" title=\"April Revolution\">a nationwide pro-democracy protest</a> against <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">president</a> <a href=\"https://wikipedia.org/wiki/Syngman_<PERSON><PERSON>\" title=\"Syngman <PERSON>hee\">Syn<PERSON></a>, eventually forcing him to resign.", "no_year_html": "Students in South Korea hold <a href=\"https://wikipedia.org/wiki/April_Revolution\" title=\"April Revolution\">a nationwide pro-democracy protest</a> against <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">president</a> <a href=\"https://wikipedia.org/wiki/Syn<PERSON>_<PERSON>\" title=\"Syn<PERSON>hee\"><PERSON><PERSON><PERSON></a>, eventually forcing him to resign.", "links": [{"title": "April Revolution", "link": "https://wikipedia.org/wiki/April_Revolution"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "Sierra Leone becomes a republic, and <PERSON><PERSON> the president.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Sierra_Leone\" title=\"Sierra Leone\">Sierra Leone</a> becomes a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> the president.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sierra_Leone\" title=\"Sierra Leone\">Sierra Leone</a> becomes a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> the president.", "links": [{"title": "Sierra Leone", "link": "https://wikipedia.org/wiki/Sierra_Leone"}, {"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "Launch of Salyut 1, the first space station.", "html": "1971 - Launch of <a href=\"https://wikipedia.org/wiki/Salyut_1\" title=\"Salyut 1\">Salyut 1</a>, the first <a href=\"https://wikipedia.org/wiki/Space_station\" title=\"Space station\">space station</a>.", "no_year_html": "Launch of <a href=\"https://wikipedia.org/wiki/Salyut_1\" title=\"Salyut 1\">Salyut 1</a>, the first <a href=\"https://wikipedia.org/wiki/Space_station\" title=\"Space station\">space station</a>.", "links": [{"title": "Salyut 1", "link": "https://wikipedia.org/wiki/Salyut_1"}, {"title": "Space station", "link": "https://wikipedia.org/wiki/Space_station"}]}, {"year": "1971", "text": "<PERSON> is sentenced to death (later commuted to life imprisonment) for conspiracy in the Tate-LaBianca murders.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to death (later commuted to life imprisonment) for <a href=\"https://wikipedia.org/wiki/Conspiracy_(criminal)\" class=\"mw-redirect\" title=\"Conspiracy (criminal)\">conspiracy</a> in the <a href=\"https://wikipedia.org/wiki/Tate%E2%80%93LaBianca_murders\" title=\"Tate-LaBianca murders\">Tate-LaBianca murders</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to death (later commuted to life imprisonment) for <a href=\"https://wikipedia.org/wiki/Conspiracy_(criminal)\" class=\"mw-redirect\" title=\"Conspiracy (criminal)\">conspiracy</a> in the <a href=\"https://wikipedia.org/wiki/Tate%E2%80%93LaBianca_murders\" title=\"Tate-LaBianca murders\">Tate-LaBianca murders</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Conspiracy (criminal)", "link": "https://wikipedia.org/wiki/Conspiracy_(criminal)"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> murders", "link": "https://wikipedia.org/wiki/Tate%E2%80%93LaBianca_murders"}]}, {"year": "1973", "text": "The Portuguese Socialist Party is founded in the German town of Bad Münstereifel.", "html": "1973 - The Portuguese <a href=\"https://wikipedia.org/wiki/Socialist_Party_(Portugal)\" title=\"Socialist Party (Portugal)\">Socialist Party</a> is founded in the German town of <a href=\"https://wikipedia.org/wiki/Bad_M%C3%BCnstereifel\" title=\"Bad Münstereifel\">Bad Münstereifel</a>.", "no_year_html": "The Portuguese <a href=\"https://wikipedia.org/wiki/Socialist_Party_(Portugal)\" title=\"Socialist Party (Portugal)\">Socialist Party</a> is founded in the German town of <a href=\"https://wikipedia.org/wiki/Bad_M%C3%BCnstereifel\" title=\"Bad Münstereifel\">Bad Münstereifel</a>.", "links": [{"title": "Socialist Party (Portugal)", "link": "https://wikipedia.org/wiki/Socialist_Party_(Portugal)"}, {"title": "Bad Münstereifel", "link": "https://wikipedia.org/wiki/Bad_M%C3%BCnstereifel"}]}, {"year": "1975", "text": "India's first satellite Aryabhata launched in orbit from Kapustin Yar, Russia.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>'s first satellite <a href=\"https://wikipedia.org/wiki/Arya<PERSON><PERSON>_(satellite)\" title=\"<PERSON><PERSON><PERSON><PERSON> (satellite)\">A<PERSON><PERSON><PERSON></a> launched in orbit from <a href=\"https://wikipedia.org/wiki/Kapustin_Yar\" title=\"Kapustin Yar\"><PERSON><PERSON><PERSON></a>, Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>'s first satellite <a href=\"https://wikipedia.org/wiki/Arya<PERSON><PERSON>_(satellite)\" title=\"<PERSON><PERSON><PERSON><PERSON> (satellite)\">A<PERSON><PERSON><PERSON></a> launched in orbit from <a href=\"https://wikipedia.org/wiki/Kapustin_Yar\" title=\"Kapustin Yar\"><PERSON><PERSON><PERSON> Ya<PERSON></a>, Russia.", "links": [{"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "<PERSON>rya<PERSON><PERSON> (satellite)", "link": "https://wikipedia.org/wiki/Arya<PERSON><PERSON>_(satellite)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON>_Yar"}]}, {"year": "1975", "text": "South Vietnamese forces withdrew from the town of Xuan Loc in the last major battle of the Vietnam War.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">South Vietnamese forces</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Xuan_Loc\" class=\"mw-redirect\" title=\"Battle of Xuan Loc\">withdrew from the town of Xuan Loc</a> in the last major battle of the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">South Vietnamese forces</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Xuan_Loc\" class=\"mw-redirect\" title=\"Battle of Xuan Loc\">withdrew from the town of Xuan Loc</a> in the last major battle of the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>.", "links": [{"title": "Army of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam"}, {"title": "Battle of Xuan Loc", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON>_<PERSON>c"}, {"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}]}, {"year": "1976", "text": "A violent F5 tornado strikes around Brownwood, Texas, injuring 11 people. Two people were thrown at least 1,000 yards (910 m) by the tornado and survived uninjured.", "html": "1976 - A <a href=\"https://wikipedia.org/wiki/1976_Brownwood_tornado\" title=\"1976 Brownwood tornado\">violent F5 tornado</a> strikes around <a href=\"https://wikipedia.org/wiki/Brownwood,_Texas\" title=\"Brownwood, Texas\">Brownwood, Texas</a>, injuring 11 people. Two people were thrown at least 1,000 yards (910 m) by the tornado and survived uninjured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1976_Brownwood_tornado\" title=\"1976 Brownwood tornado\">violent F5 tornado</a> strikes around <a href=\"https://wikipedia.org/wiki/Brownwood,_Texas\" title=\"Brownwood, Texas\">Brownwood, Texas</a>, injuring 11 people. Two people were thrown at least 1,000 yards (910 m) by the tornado and survived uninjured.", "links": [{"title": "1976 Brownwood tornado", "link": "https://wikipedia.org/wiki/1976_Brownwood_tornado"}, {"title": "Brownwood, Texas", "link": "https://wikipedia.org/wiki/Brownwood,_Texas"}]}, {"year": "1984", "text": "Advance Australia Fair is proclaimed as Australia's national anthem, and green and gold as the national colours.", "html": "1984 - <i><a href=\"https://wikipedia.org/wiki/Advance_Australia_Fair\" title=\"Advance Australia Fair\">Advance Australia Fair</a></i> is proclaimed as Australia's <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a>, and green and gold as the <a href=\"https://wikipedia.org/wiki/National_colours_of_Australia\" title=\"National colours of Australia\">national colours</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Advance_Australia_Fair\" title=\"Advance Australia Fair\">Advance Australia Fair</a></i> is proclaimed as Australia's <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a>, and green and gold as the <a href=\"https://wikipedia.org/wiki/National_colours_of_Australia\" title=\"National colours of Australia\">national colours</a>.", "links": [{"title": "Advance Australia Fair", "link": "https://wikipedia.org/wiki/Advance_Australia_Fair"}, {"title": "National anthem", "link": "https://wikipedia.org/wiki/National_anthem"}, {"title": "National colours of Australia", "link": "https://wikipedia.org/wiki/National_colours_of_Australia"}]}, {"year": "1985", "text": "Two hundred ATF and FBI agents lay siege to the compound of the white supremacist survivalist group The Covenant, the Sword, and the Arm of the Lord in Arkansas; the CSA surrenders two days later.", "html": "1985 - Two hundred <a href=\"https://wikipedia.org/wiki/Bureau_of_Alcohol,_Tobacco_and_Firearms\" class=\"mw-redirect\" title=\"Bureau of Alcohol, Tobacco and Firearms\">ATF</a> and <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agents lay siege to the compound of the <a href=\"https://wikipedia.org/wiki/White_supremacy\" title=\"White supremacy\">white supremacist</a> <a href=\"https://wikipedia.org/wiki/Survivalism\" title=\"Survivalism\">survivalist</a> group <a href=\"https://wikipedia.org/wiki/The_Covenant,_the_Sword,_and_the_Arm_of_the_Lord\" title=\"The Covenant, the Sword, and the Arm of the Lord\">The Covenant, the Sword, and the Arm of the Lord</a> in Arkansas; the CSA surrenders two days later.", "no_year_html": "Two hundred <a href=\"https://wikipedia.org/wiki/Bureau_of_Alcohol,_Tobacco_and_Firearms\" class=\"mw-redirect\" title=\"Bureau of Alcohol, Tobacco and Firearms\">ATF</a> and <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agents lay siege to the compound of the <a href=\"https://wikipedia.org/wiki/White_supremacy\" title=\"White supremacy\">white supremacist</a> <a href=\"https://wikipedia.org/wiki/Survivalism\" title=\"Survivalism\">survivalist</a> group <a href=\"https://wikipedia.org/wiki/The_Covenant,_the_Sword,_and_the_Arm_of_the_Lord\" title=\"The Covenant, the Sword, and the Arm of the Lord\">The Covenant, the Sword, and the Arm of the Lord</a> in Arkansas; the CSA surrenders two days later.", "links": [{"title": "Bureau of Alcohol, Tobacco and Firearms", "link": "https://wikipedia.org/wiki/Bureau_of_Alcohol,_Tobacco_and_Firearms"}, {"title": "FBI", "link": "https://wikipedia.org/wiki/FBI"}, {"title": "White supremacy", "link": "https://wikipedia.org/wiki/White_supremacy"}, {"title": "Survivalism", "link": "https://wikipedia.org/wiki/Survivalism"}, {"title": "The Covenant, the Sword, and the Arm of the Lord", "link": "https://wikipedia.org/wiki/The_Covenant,_the_Sword,_and_the_Arm_of_the_Lord"}]}, {"year": "1987", "text": "The Simpsons first appear as a series of shorts on The Tracey Ullman Show, first starting with \"Good Night\".", "html": "1987 - <i><a href=\"https://wikipedia.org/wiki/The_Simpsons\" title=\"The Simpsons\">The Simpsons</a></i> first appear as a series of <a href=\"https://wikipedia.org/wiki/The_Simpsons\" title=\"The Simpsons\">shorts</a> on <i><a href=\"https://wikipedia.org/wiki/The_Tracey_Ullman_Show\" title=\"The Tracey Ullman Show\">The Tracey Ullman Show</a></i>, first starting with \"<a href=\"https://wikipedia.org/wiki/Good_Night_(The_Simpsons_short)\" class=\"mw-redirect\" title=\"Good Night (The Simpsons short)\">Good Night</a>\".", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Simpsons\" title=\"The Simpsons\">The Simpsons</a></i> first appear as a series of <a href=\"https://wikipedia.org/wiki/The_Simpsons\" title=\"The Simpsons\">shorts</a> on <i><a href=\"https://wikipedia.org/wiki/The_Tracey_Ullman_Show\" title=\"The Tracey Ullman Show\">The Tracey Ullman Show</a></i>, first starting with \"<a href=\"https://wikipedia.org/wiki/Good_Night_(The_Simpsons_short)\" class=\"mw-redirect\" title=\"Good Night (The Simpsons short)\">Good Night</a>\".", "links": [{"title": "The Simpsons", "link": "https://wikipedia.org/wiki/The_Simpsons"}, {"title": "The Simpsons", "link": "https://wikipedia.org/wiki/The_Simpsons"}, {"title": "The <PERSON><PERSON> Show", "link": "https://wikipedia.org/wiki/The_<PERSON><PERSON>_<PERSON>_Show"}, {"title": "Good Night (The Simpsons short)", "link": "https://wikipedia.org/wiki/Good_Night_(The_Simpsons_short)"}]}, {"year": "1989", "text": "A gun turret explodes on the USS Iowa, killing 47 sailors.", "html": "1989 - A <a href=\"https://wikipedia.org/wiki/Gun_turret\" title=\"Gun turret\">gun turret</a> <a href=\"https://wikipedia.org/wiki/USS_Iowa_turret_explosion\" title=\"USS Iowa turret explosion\">explodes</a> on the <a href=\"https://wikipedia.org/wiki/USS_Iowa_(BB-61)\" title=\"USS Iowa (BB-61)\">USS <i>Iowa</i></a>, killing 47 sailors.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Gun_turret\" title=\"Gun turret\">gun turret</a> <a href=\"https://wikipedia.org/wiki/USS_Iowa_turret_explosion\" title=\"USS Iowa turret explosion\">explodes</a> on the <a href=\"https://wikipedia.org/wiki/USS_Iowa_(BB-61)\" title=\"USS Iowa (BB-61)\">USS <i>Iowa</i></a>, killing 47 sailors.", "links": [{"title": "Gun turret", "link": "https://wikipedia.org/wiki/Gun_turret"}, {"title": "USS Iowa turret explosion", "link": "https://wikipedia.org/wiki/USS_Iowa_turret_explosion"}, {"title": "USS Iowa (BB-61)", "link": "https://wikipedia.org/wiki/USS_Iowa_(BB-61)"}]}, {"year": "1993", "text": "The 51-day FBI siege of the Branch Davidian building in Waco, Texas, USA, ends when a fire breaks out. Seventy-six Davidians, including 18 children under age 10, died in the fire.", "html": "1993 - The 51-day FBI <a href=\"https://wikipedia.org/wiki/Waco_siege\" title=\"Waco siege\">siege</a> of the <a href=\"https://wikipedia.org/wiki/<PERSON>_David<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Davidian\"><PERSON></a> building in <a href=\"https://wikipedia.org/wiki/Waco,_Texas\" title=\"Waco, Texas\">Waco, Texas</a>, USA, ends when a fire breaks out. Seventy-six Davidians, including 18 children under age 10, died in the fire.", "no_year_html": "The 51-day FBI <a href=\"https://wikipedia.org/wiki/Waco_siege\" title=\"Waco siege\">siege</a> of the <a href=\"https://wikipedia.org/wiki/<PERSON>_David<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Davidian\"><PERSON></a> building in <a href=\"https://wikipedia.org/wiki/Waco,_Texas\" title=\"Waco, Texas\">Waco, Texas</a>, USA, ends when a fire breaks out. Seventy-six Davidians, including 18 children under age 10, died in the fire.", "links": [{"title": "Waco siege", "link": "https://wikipedia.org/wiki/Waco_siege"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Branch_Davidian"}, {"title": "Waco, Texas", "link": "https://wikipedia.org/wiki/Waco,_Texas"}]}, {"year": "1995", "text": "Oklahoma City bombing: The Alfred P<PERSON> Murrah Federal Building in Oklahoma City, USA, is bombed, killing 168 people including 19 children under the age of six.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a>: The <a href=\"https://wikipedia.org/wiki/Alfred_<PERSON>._Murrah_Federal_Building\" title=\"Alfred P<PERSON> Federal Building\">Alfred P<PERSON> Federal Building</a> in <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City</a>, USA, is bombed, killing 168 people including 19 children under the age of six.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a>: The <a href=\"https://wikipedia.org/wiki/Alfred_P._Murrah_Federal_Building\" title=\"Alfred P. Murrah Federal Building\">Alfred P<PERSON> Mu<PERSON> Federal Building</a> in <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City</a>, USA, is bombed, killing 168 people including 19 children under the age of six.", "links": [{"title": "Oklahoma City bombing", "link": "https://wikipedia.org/wiki/Oklahoma_City_bombing"}, {"title": "<PERSON> Federal Building", "link": "https://wikipedia.org/wiki/Alfred_<PERSON>_Murrah_Federal_Building"}, {"title": "Oklahoma City", "link": "https://wikipedia.org/wiki/Oklahoma_City"}]}, {"year": "1999", "text": "The German Bundestag returns to Berlin.", "html": "1999 - The German <a href=\"https://wikipedia.org/wiki/Bundestag\" title=\"Bundestag\">Bundestag</a> <a href=\"https://wikipedia.org/wiki/Decision_on_the_Capital_of_Germany\" title=\"Decision on the Capital of Germany\">returns</a> to Berlin.", "no_year_html": "The German <a href=\"https://wikipedia.org/wiki/Bundestag\" title=\"Bundestag\">Bundestag</a> <a href=\"https://wikipedia.org/wiki/Decision_on_the_Capital_of_Germany\" title=\"Decision on the Capital of Germany\">returns</a> to Berlin.", "links": [{"title": "Bundestag", "link": "https://wikipedia.org/wiki/Bundestag"}, {"title": "Decision on the Capital of Germany", "link": "https://wikipedia.org/wiki/Decision_on_the_Capital_of_Germany"}]}, {"year": "2000", "text": "Air Philippines Flight 541 crashes in Samal, Davao del Norte, killing all 131 people on board.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Air_Philippines_Flight_541\" title=\"Air Philippines Flight 541\">Air Philippines Flight 541</a> crashes in <a href=\"https://wikipedia.org/wiki/Samal,_Davao_del_Norte\" title=\"Samal, Davao del Norte\">Samal, Davao del Norte</a>, killing all 131 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Philippines_Flight_541\" title=\"Air Philippines Flight 541\">Air Philippines Flight 541</a> crashes in <a href=\"https://wikipedia.org/wiki/Samal,_Davao_del_Norte\" title=\"Samal, Davao del Norte\">Samal, Davao del Norte</a>, killing all 131 people on board.", "links": [{"title": "Air Philippines Flight 541", "link": "https://wikipedia.org/wiki/Air_Philippines_Flight_541"}, {"title": "Samal, Davao del Norte", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Davao_del_Norte"}]}, {"year": "2001", "text": "Space Shuttle Endeavour is launched on STS-100 carrying the Canadarm2 to the International Space Station.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-100\" title=\"STS-100\">STS-100</a> carrying the <a href=\"https://wikipedia.org/wiki/Mobile_Servicing_System\" title=\"Mobile Servicing System\">Canadarm2</a> to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-100\" title=\"STS-100\">STS-100</a> carrying the <a href=\"https://wikipedia.org/wiki/Mobile_Servicing_System\" title=\"Mobile Servicing System\">Canadarm2</a> to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-100", "link": "https://wikipedia.org/wiki/STS-100"}, {"title": "Mobile Servicing System", "link": "https://wikipedia.org/wiki/Mobile_Servicing_System"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2005", "text": "Cardinal <PERSON> is elected to the papacy and becomes Pope <PERSON>.", "html": "2005 - Cardinal <PERSON> is <a href=\"https://wikipedia.org/wiki/Papal_conclave,_2005\" class=\"mw-redirect\" title=\"Papal conclave, 2005\">elected</a> to the papacy and becomes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a>.", "no_year_html": "Cardinal <PERSON> is <a href=\"https://wikipedia.org/wiki/Papal_conclave,_2005\" class=\"mw-redirect\" title=\"Papal conclave, 2005\">elected</a> to the papacy and becomes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a>.", "links": [{"title": "Papal conclave, 2005", "link": "https://wikipedia.org/wiki/Papal_conclave,_2005"}, {"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON> resigns as First Secretary of the Communist Party of Cuba after holding the title since July 1961.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/First_Secretary_of_the_Communist_Party_of_Cuba\" title=\"First Secretary of the Communist Party of Cuba\">First Secretary of the Communist Party of Cuba</a> after holding the title since July 1961.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/First_Secretary_of_the_Communist_Party_of_Cuba\" title=\"First Secretary of the Communist Party of Cuba\">First Secretary of the Communist Party of Cuba</a> after holding the title since July 1961.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "First Secretary of the Communist Party of Cuba", "link": "https://wikipedia.org/wiki/First_Secretary_of_the_Communist_Party_of_Cuba"}]}, {"year": "2013", "text": "Boston Marathon bombing suspect <PERSON><PERSON><PERSON> is killed in a shootout with police. His brother <PERSON><PERSON><PERSON><PERSON> is later captured hiding in a boat inside a backyard in the suburb of Watertown.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Boston_Marathon_bombing\" title=\"Boston Marathon bombing\">Boston Marathon bombing</a> suspect <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is killed in a shootout with police. His brother <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is later captured hiding in a boat inside a backyard in the suburb of <a href=\"https://wikipedia.org/wiki/Watertown,_Massachusetts\" title=\"Watertown, Massachusetts\">Watertown</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boston_Marathon_bombing\" title=\"Boston Marathon bombing\">Boston Marathon bombing</a> suspect <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nae<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is killed in a shootout with police. His brother <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is later captured hiding in a boat inside a backyard in the suburb of <a href=\"https://wikipedia.org/wiki/Watertown,_Massachusetts\" title=\"Watertown, Massachusetts\">Watertown</a>.", "links": [{"title": "Boston Marathon bombing", "link": "https://wikipedia.org/wiki/Boston_Marathon_bombing"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Watertown, Massachusetts", "link": "https://wikipedia.org/wiki/Watertown,_Massachusetts"}]}, {"year": "2020", "text": "A killing spree in Nova Scotia, Canada, leaves 22 people and the perpetrator dead, making it the deadliest rampage in the country's history.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/2020_Nova_Scotia_attacks\" title=\"2020 Nova Scotia attacks\">A killing spree</a> in <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a>, Canada, leaves 22 people and the perpetrator dead, making it the deadliest rampage in the country's history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2020_Nova_Scotia_attacks\" title=\"2020 Nova Scotia attacks\">A killing spree</a> in <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a>, Canada, leaves 22 people and the perpetrator dead, making it the deadliest rampage in the country's history.", "links": [{"title": "2020 Nova Scotia attacks", "link": "https://wikipedia.org/wiki/2020_Nova_Scotia_attacks"}, {"title": "Nova Scotia", "link": "https://wikipedia.org/wiki/Nova_Scotia"}]}, {"year": "2021", "text": "The Ingenuity helicopter becomes the first aircraft to achieve flight on another planet.", "html": "2021 - The <i><a href=\"https://wikipedia.org/wiki/Ingenuity_(helicopter)\" title=\"Ingenuity (helicopter)\">Ingenuity</a></i> helicopter becomes the first aircraft to achieve flight on another planet.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Ingenuity_(helicopter)\" title=\"Ingenuity (helicopter)\">Ingenuity</a></i> helicopter becomes the first aircraft to achieve flight on another planet.", "links": [{"title": "Ingenuity (helicopter)", "link": "https://wikipedia.org/wiki/Ingenuity_(helicopter)"}]}], "Births": [{"year": "1452", "text": "<PERSON>, King  of Naples (d. 1504)", "html": "1452 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> IV</a>, King of Naples (d. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> IV</a>, King of Naples (d. 1504)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/Frederick_of_Naples"}]}, {"year": "1593", "text": "Sir <PERSON>, 2nd Baronet, English politician (d. 1647)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English politician (d. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English politician (d. 1647)", "links": [{"title": "Sir <PERSON>, 2nd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet"}]}, {"year": "1603", "text": "<PERSON>, French politician, French Minister of Defence (d. 1685)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of Defence</a> (d. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of Defence</a> (d. 1685)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1613", "text": "<PERSON>, German musician (d. 1661)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, German musician (d. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, German musician (d. 1661)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1633", "text": "<PERSON>, Dutch painter (d. 1659)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1659)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Willem_<PERSON>"}]}, {"year": "1655", "text": "<PERSON>(e), Royal Navy officer and administrator (d. 1718)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/George_St_Lo\" title=\"George St Lo\"><PERSON>(e)</a>, Royal Navy officer and administrator (d. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_St_Lo\" title=\"George St Lo\"><PERSON>(e)</a>, Royal Navy officer and administrator (d. 1718)", "links": [{"title": "George <PERSON> Lo", "link": "https://wikipedia.org/wiki/George_St_Lo"}]}, {"year": "1658", "text": "<PERSON>, Elector <PERSON>, German husband of Archduchess <PERSON> of Austria (d. 1716)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>ector_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a>, German husband of <a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_<PERSON>_of_Austria\" title=\"Archduchess <PERSON> of Austria\">Archduchess <PERSON> of Austria</a> (d. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>ector_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a>, German husband of <a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_of_Austria\" title=\"Archduchess <PERSON> of Austria\">Archduchess <PERSON> of Austria</a> (d. 1716)", "links": [{"title": "<PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>"}, {"title": "Archduchess <PERSON> of Austria", "link": "https://wikipedia.org/wiki/Archduchess_<PERSON>_<PERSON>_<PERSON>_of_Austria"}]}, {"year": "1665", "text": "<PERSON>, French author (d. 1721)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON><PERSON>, Russian ethnographer and politician (d. 1750)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ethnographer and politician (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ethnographer and politician (d. 1750)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, English organist and composer (d. 1783)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (d. 1783)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1721", "text": "<PERSON>, American lawyer and politician (d. 1793)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1734", "text": "<PERSON>, Austrian violinist and composer (d. 1786)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3%C3%B1ez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3%C3%B1ez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (d. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3%C3%B1ez"}]}, {"year": "1757", "text": "<PERSON>, 1st Viscount Ex<PERSON>, English admiral and politician (d. 1833)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Exmouth\" title=\"<PERSON>, 1st Viscount Exmouth\"><PERSON>, 1st Viscount Ex<PERSON></a>, English admiral and politician (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Exmouth\" title=\"<PERSON>, 1st Viscount Exmouth\"><PERSON>, 1st Viscount Ex<PERSON></a>, English admiral and politician (d. 1833)", "links": [{"title": "<PERSON>, 1st Viscount Ex<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Exmouth"}]}, {"year": "1758", "text": "<PERSON>, 7th Earl of Northesk, Scottish admiral (d. 1831)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Earl_of_Northesk\" title=\"<PERSON>, 7th Earl of Northesk\"><PERSON>, 7th Earl of Northesk</a>, Scottish admiral (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Earl_of_Northesk\" title=\"<PERSON>, 7th Earl of Northesk\"><PERSON>, 7th Earl of Northesk</a>, Scottish admiral (d. 1831)", "links": [{"title": "<PERSON>, 7th Earl of Northesk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Earl_of_Northesk"}]}, {"year": "1785", "text": "<PERSON>, French pianist and composer (d. 1858)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Bo%C3%ABly\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Bo%C3%ABly\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Bo%C3%ABly"}]}, {"year": "1787", "text": "<PERSON><PERSON>, American soldier (d. 1837)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American soldier (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American soldier (d. 1837)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON> of Austria (d. 1875)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1875)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria"}]}, {"year": "1806", "text": "<PERSON>, American labor organizer (d. 1889)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor organizer (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor organizer (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, French journalist and author (d. 1875)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Louis_Am%C3%A9d%C3%A9e_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_Am%C3%A9d%C3%A9e_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Am%C3%A9d%C3%A9e_Achard"}]}, {"year": "1831", "text": "<PERSON>, American writer, editor and translator (d. 1889)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, editor and translator (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, editor and translator (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, Spanish poet and playwright, Nobel Prize laureate (d. 1916)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Echegaray\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Echegaray\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Echegaray"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1835", "text": "<PERSON>, Finnish poet and journalist (d. 1888)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish poet and journalist (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish poet and journalist (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON>, Norwegian actress (d. 1924)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actress)\" title=\"<PERSON><PERSON><PERSON> (actress)\"><PERSON><PERSON><PERSON></a>, Norwegian actress (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actress)\" title=\"<PERSON><PERSON><PERSON> (actress)\"><PERSON><PERSON><PERSON></a>, Norwegian actress (d. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actress)"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON>, Finnish actor (d. 1940)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish actor (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish actor (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>o"}]}, {"year": "1872", "text": "<PERSON>, German social reformer (d. 1948)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German social reformer (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German social reformer (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alice_Salomon"}]}, {"year": "1873", "text": "<PERSON>, English cricketer (d. 1967)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Sydney_Barnes\" title=\"Sydney Barnes\"><PERSON></a>, English cricketer (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Barnes\" title=\"<PERSON> Barnes\"><PERSON></a>, English cricketer (d. 1967)", "links": [{"title": "Sydney Barnes", "link": "https://wikipedia.org/wiki/Sydney_Barnes"}]}, {"year": "1874", "text": "<PERSON>, Swiss psychiatrist, geneticist, and eugenicist (d. 1952)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Ernst_<PERSON>%C3%BCdin\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist, geneticist, and eugenicist (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCdin\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist, geneticist, and eugenicist (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_R%C3%BCdin"}]}, {"year": "1877", "text": "<PERSON>, Norwegian-American engineer, invented the outboard motor (d. 1934)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American engineer, invented the <a href=\"https://wikipedia.org/wiki/Outboard_motor\" title=\"Outboard motor\">outboard motor</a> (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American engineer, invented the <a href=\"https://wikipedia.org/wiki/Outboard_motor\" title=\"Outboard motor\">outboard motor</a> (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Outboard motor", "link": "https://wikipedia.org/wiki/Outboard_motor"}]}, {"year": "1879", "text": "<PERSON>, Scottish runner (d. 1957)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Scottish runner (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Scottish runner (d. 1957)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Brazilian lawyer and politician, 14th President of Brazil (d. 1954)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Get%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Get%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Get%C3%BAlio_Vargas"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1883", "text": "<PERSON>, American soccer player (d. 1938)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Austrian-American mathematician and physicist (d. 1953)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American mathematician and physicist (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American mathematician and physicist (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Estonian architect (d. 1975)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, German jurist and politician (d. 1946)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and politician (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and politician (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON>, French actress (d. 1974)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (d. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7oise_<PERSON>y"}]}, {"year": "1892", "text": "<PERSON><PERSON>, French composer and educator (d. 1983)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French composer and educator (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French composer and educator (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American author and activist (d. 1966)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Indian businessman and philanthropist (d. 1970)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian businessman and philanthropist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian businessman and philanthropist (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Japanese super-centenarian, oldest verified man ever (d. 2013)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese super-centenarian, oldest verified man ever (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese super-centenarian, oldest verified man ever (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American actress and producer (d. 1973)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American actor (d. 1985)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1985)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(actor)"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Turkish lieutenant and painter (d. 1968)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll<PERSON>\" title=\"Ce<PERSON>ll<PERSON>\"><PERSON><PERSON></a>, Turkish lieutenant and painter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll<PERSON>\" title=\"Ce<PERSON>ll<PERSON>\"><PERSON><PERSON></a>, Turkish lieutenant and painter (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>llu"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Brazilian film actress (d. 1978)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian film actress (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>car\"><PERSON><PERSON><PERSON></a>, Brazilian film actress (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, English author, poet, and playwright (d. 1976)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON> (writer)\"><PERSON></a>, English author, poet, and playwright (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON> (writer)\"><PERSON></a>, English author, poet, and playwright (d. 1976)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1900", "text": "<PERSON>, Canadian lawyer and politician, 20th Governor General of Canada (d. 1991)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American Yiddish songwriter and singing teacher (d. 1959)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American Yiddish songwriter and singing teacher (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American Yiddish songwriter and singing teacher (d. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Russian author and screenwriter (d. 1989)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author and screenwriter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author and screenwriter (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>in"}]}, {"year": "1903", "text": "<PERSON>, American law enforcement agent (d. 1957)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American law enforcement agent (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> N<PERSON>\"><PERSON></a>, American law enforcement agent (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ess"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Polish actress  (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3wna\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3wna\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actress (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C3%B3wna"}]}, {"year": "1912", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 1999)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1913", "text": "<PERSON>, American discus thrower and coach (d. 1984)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(discus_thrower)\" title=\"<PERSON> (discus thrower)\"><PERSON></a>, American discus thrower and coach (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(discus_thrower)\" title=\"<PERSON> (discus thrower)\"><PERSON></a>, American discus thrower and coach (d. 1984)", "links": [{"title": "<PERSON> (discus thrower)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(discus_thrower)"}]}, {"year": "1917", "text": "<PERSON>, Danish-German soldier and author (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-German soldier and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-German soldier and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American pianist and composer (d. 1990)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Kaplan"}]}, {"year": "1920", "text": "<PERSON>, American lawyer and politician, 56th Governor of Maryland (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 56th <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 56th <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Maryland", "link": "https://wikipedia.org/wiki/Governor_of_Maryland"}]}, {"year": "1920", "text": "<PERSON>, Belgian cardinal (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cardinal (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cardinal (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Norwegian journalist and war historian (d. 2019)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist and war historian (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist and war historian (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American jockey (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American logician (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American logician (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American logician (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Italian Jesuit leader, cardinal, and theologian (d. 2015)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Jesuit leader, cardinal, and theologian (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Jesuit leader, cardinal, and theologian (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, German colonel and pilot (d. 1993)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Sr., Dutch actor (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, Dutch actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, Dutch actor (d. 2011)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Sr."}]}, {"year": "1925", "text": "<PERSON>, American actor (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brian\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brian\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Brian"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Egyptian captain and politician (d. 1997)[citation needed]", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian captain and politician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian captain and politician (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ya_At<PERSON>a"}]}, {"year": "1928", "text": "<PERSON>, English engineer and academic (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON> of Perak, <PERSON> of Malaysia (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Perak\" title=\"<PERSON><PERSON><PERSON> of Perak\"><PERSON><PERSON><PERSON> of Perak</a>, <PERSON> of Malaysia (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Perak\" title=\"<PERSON><PERSON><PERSON> of Perak\"><PERSON><PERSON><PERSON> of Perak</a>, <PERSON> of Malaysia (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON> of Perak", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Perak"}]}, {"year": "1931", "text": "<PERSON>, Canadian journalist and author (d. 2004)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Canadian journalist and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Canadian journalist and author (d. 2004)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1932", "text": "<PERSON>, Colombian painter and sculptor (d. 2023)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian painter and sculptor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian painter and sculptor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American model and actress (d. 1967)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American singer-songwriter and producer (d. 1989)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English actor, comedian, and pianist (d. 2002)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian, and pianist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian, and pianist (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American cardinal", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Belgian politician, 60th Prime Minister of Belgium (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, Belgian politician, 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, Belgian politician, 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}]}, {"year": "1936", "text": "<PERSON>, American football player and coach (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Italian-English chef and author (d. 2017)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English chef and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English chef and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American actress", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Filipino politician, 13th President of the Philippines", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1938", "text": "<PERSON>, American theorist, author, and scholar", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stanley Fish\"><PERSON></a>, American theorist, author, and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stanley Fish\"><PERSON></a>, American theorist, author, and scholar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American accountant, judge, and politician (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American accountant, judge, and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American accountant, judge, and politician (d. 2013)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1941", "text": "<PERSON>, French-English chef and author (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English chef and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English chef and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter (d. 1992)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English keyboard player, singer, and composer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, singer, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, singer, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Scottish journalist and politician (d. 2014)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish journalist and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish journalist and politician (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American economist and academic, Nobel Prize laureate", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1944", "text": "<PERSON>, American keyboard player and songwriter (d. 2016)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actor and singer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, Faroese educator and politician, Prime Minister of the Faroe Islands", "html": "1951 - <a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Faroese educator and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Faroese educator and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1952", "text": "<PERSON>, English conservationist and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conservationist)\" title=\"<PERSON> (conservationist)\"><PERSON></a>, English conservationist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(conservationist)\" title=\"<PERSON> (conservationist)\"><PERSON></a>, English conservationist and author", "links": [{"title": "<PERSON> (conservationist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conservationist)"}]}, {"year": "1954", "text": "<PERSON>, English footballer and manager (d. 2023)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Scottish biologist and academic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biologist)\" title=\"<PERSON> (biologist)\"><PERSON></a>, Scottish biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(biologist)\" title=\"<PERSON> (biologist)\"><PERSON></a>, Scottish biologist and academic", "links": [{"title": "<PERSON> (biologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(biologist)"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Indian businessman, chairman of Reliance Industries", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Mukesh_Ambani\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian businessman, chairman of <a href=\"https://wikipedia.org/wiki/Reliance_Industries_Limited\" class=\"mw-redirect\" title=\"Reliance Industries Limited\">Reliance Industries</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ban<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian businessman, chairman of <a href=\"https://wikipedia.org/wiki/Reliance_Industries_Limited\" class=\"mw-redirect\" title=\"Reliance Industries Limited\">Reliance Industries</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mukesh_<PERSON>bani"}, {"title": "Reliance Industries Limited", "link": "https://wikipedia.org/wiki/Reliance_Industries_Limited"}]}, {"year": "1960", "text": "<PERSON>, Armenian pianist, composer, and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian pianist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian pianist, composer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American astrophysicist, astronomer, and academic", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist, astronomer, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist, astronomer, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American record executive", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Knight\" title=\"Suge Knight\"><PERSON><PERSON></a>, American record executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Knight\" title=\"Suge Knight\"><PERSON><PERSON></a>, American record executive", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, French soprano and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/V%C3%A9ronique_Gens\" title=\"Véronique Gens\">V<PERSON><PERSON><PERSON></a>, French soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%A9ronique_Gens\" title=\"Véronique Gens\">V<PERSON><PERSON><PERSON></a>, French soprano and actress", "links": [{"title": "Véronique Gens", "link": "https://wikipedia.org/wiki/V%C3%A9ronique_Gens"}]}, {"year": "1968", "text": "<PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, King (<PERSON><PERSON><PERSON>) of Eswatini (Swaziland)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III\" title=\"<PERSON><PERSON> III\"><PERSON><PERSON> III</a>, King (<i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i>) of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (Swaziland)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III\" title=\"<PERSON><PERSON> III\"><PERSON><PERSON> III</a>, King (<i><a href=\"https://wikipedia.org/wiki/Ngwenyama\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i>) of <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>ni\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (Swaziland)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_III"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ni"}]}, {"year": "1970", "text": "<PERSON>, English athlete and double Olympic champion", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English athlete and double Olympic champion", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English athlete and double Olympic champion", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Rivaldo_Vitor_Borba_Ferreira\" class=\"mw-redirect\" title=\"Rivaldo V<PERSON>\">Rival<PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rivaldo_<PERSON>itor_Borba_Ferreira\" class=\"mw-redirect\" title=\"Rivaldo V<PERSON>\">Rival<PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rivaldo_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American-Austrian painter and educator", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Austrian painter and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Austrian painter and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Norwegian footballer and lawyer, president of the Norwegian Football Federation", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lav<PERSON>\" title=\"Lise Klaveness\"><PERSON><PERSON></a>, Norwegian footballer and lawyer, president of the <a href=\"https://wikipedia.org/wiki/Norwegian_Football_Federation\" title=\"Norwegian Football Federation\">Norwegian Football Federation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Klavene<PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and lawyer, president of the <a href=\"https://wikipedia.org/wiki/Norwegian_Football_Federation\" title=\"Norwegian Football Federation\">Norwegian Football Federation</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>se_Klaveness"}, {"title": "Norwegian Football Federation", "link": "https://wikipedia.org/wiki/Norwegian_Football_Federation"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Troy_Polamalu"}]}, {"year": "1982", "text": "<PERSON>, Jr., Liberian-American journalist, producer, and screenwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Liberian-American journalist, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Liberian-American journalist, producer, and screenwriter", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1982", "text": "<PERSON>, American comedian and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Russian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Canadian actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American baseball player", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1990", "text": "<PERSON>, Filipino actress, singer, and dancer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American singer and internet personality", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and internet personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American baseball player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American internet personality", "html": "2016 - <a href=\"https://wikipedia.org/wiki/The_Rizzler\" title=\"The Rizzler\"><PERSON></a>, American internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Rizzler\" title=\"The Rizzler\"><PERSON></a>, American internet personality", "links": [{"title": "The Rizzler", "link": "https://wikipedia.org/wiki/The_Rizzler"}]}], "Deaths": [{"year": "843", "text": "<PERSON> of Bavaria, Frankish empress", "html": "843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bavaria_(died_843)\" title=\"<PERSON> of Bavaria (died 843)\"><PERSON> of Bavaria</a>, Frankish empress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bavaria_(died_843)\" title=\"<PERSON> of Bavaria (died 843)\"><PERSON> of Bavaria</a>, Frankish empress", "links": [{"title": "<PERSON> of Bavaria (died 843)", "link": "https://wikipedia.org/wiki/<PERSON>_of_Bavaria_(died_843)"}]}, {"year": "1012", "text": "<PERSON><PERSON><PERSON><PERSON> of Canterbury, English archbishop and saint (b. 954)", "html": "1012 - <a href=\"https://wikipedia.org/wiki/%C3%86lfheah_of_Canterbury\" title=\"<PERSON><PERSON><PERSON><PERSON> of Canterbury\"><PERSON><PERSON><PERSON><PERSON> of Canterbury</a>, English archbishop and saint (b. 954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86lfheah_of_Canterbury\" title=\"<PERSON><PERSON><PERSON><PERSON> of Canterbury\"><PERSON><PERSON><PERSON><PERSON> of Canterbury</a>, English archbishop and saint (b. 954)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Canterbury", "link": "https://wikipedia.org/wiki/%C3%86lfheah_of_Canterbury"}]}, {"year": "1013", "text": "<PERSON><PERSON> <PERSON>, Umayyad caliph of Córdoba (b. 966)", "html": "1013 - <a href=\"https://wikipedia.org/wiki/<PERSON>ham_II\" title=\"Hisham II\"><PERSON><PERSON> II</a>, Umayyad caliph of Córdoba (b. 966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hisham_II\" title=\"Hisham II\"><PERSON><PERSON> II</a>, Umayyad caliph of Córdoba (b. 966)", "links": [{"title": "Hisham II", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_II"}]}, {"year": "1044", "text": "<PERSON><PERSON><PERSON>, duke of Lorraine", "html": "1044 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Lorraine\"><PERSON><PERSON><PERSON> <PERSON></a>, duke of Lorraine", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Lorraine\"><PERSON><PERSON><PERSON> <PERSON></a>, duke of Lorraine", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Lorraine"}]}, {"year": "1054", "text": "<PERSON>, pope of the Catholic Church (b. 1002)", "html": "1054 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Leo <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Leo <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1321", "text": "<PERSON><PERSON><PERSON><PERSON>, patriarch of Constantinople", "html": "1321 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON><PERSON> I of Constantinople\">G<PERSON><PERSON><PERSON> <PERSON></a>, patriarch of Constantinople", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON> of Constantinople\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, patriarch of Constantinople", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Gerasimus_I_of_Constantinople"}]}, {"year": "1390", "text": "<PERSON>, king of Scotland (b. 1316)", "html": "1390 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> II</a>, king of Scotland (b. 1316)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> II</a>, king of Scotland (b. 1316)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1405", "text": "<PERSON>, 1st Baron <PERSON>, English nobleman (b. 1335)", "html": "1405 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English nobleman (b. 1335)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English nobleman (b. 1335)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1431", "text": "<PERSON><PERSON><PERSON>, count of Waldeck (b. 1362)", "html": "1431 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III,_Count_of_Waldeck\" title=\"<PERSON><PERSON><PERSON>, Count of Waldeck\"><PERSON><PERSON><PERSON></a>, count of Waldeck (b. 1362)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III,_Count_of_Waldeck\" title=\"<PERSON><PERSON><PERSON> III, Count of Waldeck\"><PERSON><PERSON><PERSON></a>, count of Waldeck (b. 1362)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Waldeck", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1560", "text": "<PERSON>, German theologian and reformer (b. 1497)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and reformer (b. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and reformer (b. 1497)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1567", "text": "<PERSON>, German monk and mathematician (b. 1487)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German monk and mathematician (b. 1487)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German monk and mathematician (b. 1487)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1578", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese samurai and warlord (b. 1530)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/Uesug<PERSON>_<PERSON>\" title=\"Uesugi Kenshin\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese samurai and warlord (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uesug<PERSON>_<PERSON>\" title=\"Uesugi Kenshin\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese samurai and warlord (b. 1530)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>es<PERSON><PERSON>_<PERSON>"}]}, {"year": "1588", "text": "<PERSON>, Italian painter (b. 1528)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1528)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1608", "text": "<PERSON>, 1st Earl of Dorset, English poet, playwright, and politician, Lord High Treasurer (b. 1536)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Dorset\" title=\"<PERSON>, 1st Earl of Dorset\"><PERSON>, 1st Earl of Dorset</a>, English poet, playwright, and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Dorset\" title=\"<PERSON>, 1st Earl of Dorset\"><PERSON>, 1st Earl of Dorset</a>, English poet, playwright, and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1536)", "links": [{"title": "<PERSON>, 1st Earl of Dorset", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Dorset"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1618", "text": "<PERSON>, English priest and author (b. 1566)", "html": "1618 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1566)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>stard"}]}, {"year": "1619", "text": "<PERSON><PERSON><PERSON>, Mughal empress (b. 1573)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/Jaga<PERSON>_Gosain\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mughal empress (b. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaga<PERSON>_<PERSON>sain\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mughal empress (b. 1573)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jagat_Gosain"}]}, {"year": "1629", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer (b. 1582)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/Sigis<PERSON>o_d%27India\" title=\"Sigismondo d'India\"><PERSON><PERSON><PERSON><PERSON> d'India</a>, Italian composer (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sigis<PERSON><PERSON>_d%27India\" title=\"Sigismondo d'India\"><PERSON><PERSON><PERSON><PERSON> d'India</a>, Italian composer (b. 1582)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> d'India", "link": "https://wikipedia.org/wiki/Sigismondo_d%27India"}]}, {"year": "1686", "text": "<PERSON>, Spanish historian and playwright (b. 1610)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADs_y_Ribadeneyra\" title=\"<PERSON> y Ribadeneyra\"><PERSON> y <PERSON></a>, Spanish historian and playwright (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADs_y_Ribadeneyra\" title=\"<PERSON>ís y Ribadeneyra\"><PERSON> y <PERSON></a>, Spanish historian and playwright (b. 1610)", "links": [{"title": "<PERSON>ís y Ribadeneyra", "link": "https://wikipedia.org/wiki/<PERSON>_de_Sol%C3%ADs_y_Ribadeneyra"}]}, {"year": "1689", "text": "<PERSON>, queen of Sweden (b. 1626)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden\" title=\"<PERSON>, Queen of Sweden\"><PERSON></a>, queen of Sweden (b. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden\" title=\"<PERSON>, Queen of Sweden\"><PERSON></a>, queen of Sweden (b. 1626)", "links": [{"title": "<PERSON>, Queen of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden"}]}, {"year": "1733", "text": "<PERSON>, countess of Orkney (b. 1657)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Orkney\" title=\"<PERSON>, Countess of Orkney\"><PERSON></a>, countess of Orkney (b. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Orkney\" title=\"<PERSON>, Countess of Orkney\"><PERSON></a>, countess of Orkney (b. 1657)", "links": [{"title": "<PERSON>, Countess of Orkney", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Orkney"}]}, {"year": "1739", "text": "<PERSON>, English mathematician and academic (b. 1682)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1682)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON><PERSON>, Italian painter and etcher (b. 1697)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/Canaletto\" title=\"Canaletto\"><PERSON><PERSON></a>, Italian painter and etcher (b. 1697)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canaletto\" title=\"Canaletto\"><PERSON><PERSON></a>, Italian painter and etcher (b. 1697)", "links": [{"title": "Canaletto", "link": "https://wikipedia.org/wiki/Canaletto"}]}, {"year": "1776", "text": "<PERSON>, German rabbi and author (b. 1697)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rabbi and author (b. 1697)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rabbi and author (b. 1697)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1791", "text": "<PERSON>, Welsh-English preacher and philosopher (b. 1723)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English preacher and philosopher (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English preacher and philosopher (b. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, American physician and educator (b. 1745)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and educator (b. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benjamin Rush\"><PERSON></a>, American physician and educator (b. 1745)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, English-Scottish poet and playwright (b. 1788)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English-Scottish poet and playwright (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English-Scottish poet and playwright (b. 1788)", "links": [{"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON><PERSON>, German astronomer and mathematician (b. 1765)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, German astronomer and mathematician (b. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German astronomer and mathematician (b. 1765)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, 1st Baron <PERSON>, Bahamian-English admiral and politician, 36th Commodore Governor of Newfoundland (b. 1756)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Bahamian-English admiral and politician, 36th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore Governor of Newfoundland</a> (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Bahamian-English admiral and politician, 36th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore Governor of Newfoundland</a> (b. 1756)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "List of lieutenant governors of Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON>, Canadian bishop (b. 1777)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian bishop (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian bishop (b. 1777)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Scottish mineralogist and academic (b. 1774)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mineralogist and academic (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mineralogist and academic (b. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, English journalist and politician, Prime Minister of the United Kingdom (b. 1804)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1882", "text": "<PERSON>, English biologist and theorist (b. 1809)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and theorist (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and theorist (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Estonian-German pastor, composer, and conductor (b. 1817)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rber\" title=\"<PERSON>\"><PERSON></a>, Estonian-German pastor, composer, and conductor (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rber\" title=\"<PERSON>\"><PERSON></a>, Estonian-German pastor, composer, and conductor (b. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_K%C3%B6rber"}]}, {"year": "1901", "text": "<PERSON>, American publisher, founded The Dallas Morning News (b. 1839)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <i><a href=\"https://wikipedia.org/wiki/The_Dallas_Morning_News\" title=\"The Dallas Morning News\">The Dallas Morning News</a></i> (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <i><a href=\"https://wikipedia.org/wiki/The_Dallas_Morning_News\" title=\"The Dallas Morning News\">The Dallas Morning News</a></i> (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "The Dallas Morning News", "link": "https://wikipedia.org/wiki/The_Dallas_Morning_News"}]}, {"year": "1903", "text": "<PERSON>, Canadian politician, third Premier of Ontario, eighth Lieutenant Governor of Ontario (b. 1820)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, third <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a>, eighth <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Ontario\" title=\"Lieutenant Governor of Ontario\">Lieutenant Governor of Ontario</a> (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, third <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a>, eighth <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Ontario\" title=\"Lieutenant Governor of Ontario\">Lieutenant Governor of Ontario</a> (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Ontario", "link": "https://wikipedia.org/wiki/Premier_of_Ontario"}, {"title": "Lieutenant Governor of Ontario", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Ontario"}]}, {"year": "1906", "text": "<PERSON>, French physicist and academic, Nobel Prize laureate (b. 1859)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1906", "text": "<PERSON>, English tennis player and cricketer (b. 1850)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportsman)\" title=\"<PERSON> (sportsman)\"><PERSON></a>, English tennis player and cricketer (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportsman)\" title=\"<PERSON> (sportsman)\"><PERSON></a>, English tennis player and cricketer (b. 1850)", "links": [{"title": "<PERSON> (sportsman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(sportsman)"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Greenland-born Danish writer and ethnologist (b. 1836)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Sign<PERSON>ink\"><PERSON><PERSON></a>, Greenland-born Danish writer and ethnologist (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Sign<PERSON>ink\"><PERSON><PERSON></a>, Greenland-born Danish writer and ethnologist (b. 1836)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Signe_Rink"}]}, {"year": "1914", "text": "<PERSON>, American mathematician and philosopher (b. 1839)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and philosopher (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and philosopher (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English-Australian politician, 17th Premier of South Australia (b. 1837)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ford II\"><PERSON></a>, English-Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ford II\"><PERSON></a>, English-Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1837)", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, American engineer, designed the Shay locomotive (b. 1839)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American engineer, designed the <a href=\"https://wikipedia.org/wiki/Shay_locomotive\" title=\"Shay locomotive\">Shay locomotive</a> (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American engineer, designed the <a href=\"https://wikipedia.org/wiki/Shay_locomotive\" title=\"Shay locomotive\">Shay locomotive</a> (b. 1839)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Shay locomotive", "link": "https://wikipedia.org/wiki/Shay_locomotive"}]}, {"year": "1926", "text": "<PERSON>, Russian-Swiss statistician and theorist (b. 1874)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss statistician and theorist (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss statistician and theorist (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Canadian businessman and politician (b. 1827)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businessman and politician (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businessman and politician (b. 1827)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1937", "text": "<PERSON>, 1st Baron <PERSON> of Allington, English cartographer and politician (b. 1856)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_of_Allington\" title=\"<PERSON>, 1st Baron <PERSON> of Allington\"><PERSON>, 1st Baron <PERSON> of Allington</a>, English cartographer and politician (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Allington\" title=\"<PERSON>, 1st Baron <PERSON> of Allington\"><PERSON>, 1st Baron <PERSON> of Allington</a>, English cartographer and politician (b. 1856)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Allington", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Allington"}]}, {"year": "1937", "text": "<PERSON>, American entomologist and zoologist (b. 1865)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entomologist and zoologist (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entomologist and zoologist (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Irish Republican died on hunger strike", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Republican died on hunger strike", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Republican died on hunger strike", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Austrian composer (b. 1878)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_M%C3%BCller-<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Danish-Swedish figure skater (b. 1877)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Swedish figure skater (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Swedish figure skater (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, French-German philologist and scholar (b. 1886)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German philologist and scholar (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German philologist and scholar (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, British singer (b. 1921)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British singer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British singer (b. 1921)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1955", "text": "<PERSON>, British-Indian colonel, hunter, and author (b. 1875)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Indian colonel, hunter, and author (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Indian colonel, hunter, and author (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American economist and statistician (b. 1894)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>sley_Ruml\" title=\"Beardsley Ruml\"><PERSON><PERSON></a>, American economist and statistician (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sley_Ruml\" title=\"Beardsley Ruml\"><PERSON><PERSON></a>, American economist and statistician (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ruml"}]}, {"year": "1961", "text": "<PERSON>, German swimmer (b. 1882)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish politician of Social Democratic Party of Finland; the Prime Minister of Finland (b. 1881)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/V%C3%A4in%C3%B<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish politician of <a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_of_Finland\" title=\"Social Democratic Party of Finland\">Social Democratic Party of Finland</a>; the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a> (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%A4in%C3%B<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish politician of <a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_of_Finland\" title=\"Social Democratic Party of Finland\">Social Democratic Party of Finland</a>; the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a> (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A4in%C3%B6_Tanner"}, {"title": "Social Democratic Party of Finland", "link": "https://wikipedia.org/wiki/Social_Democratic_Party_of_Finland"}, {"title": "Prime Minister of Finland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Finland"}]}, {"year": "1967", "text": "<PERSON>, German politician, 1st Chancellor of Germany (b. 1876)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, 1st <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, 1st <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany"}]}, {"year": "1971", "text": "<PERSON>, Italian race car driver (b. 1913)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American chemist and academic (b. 1899)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Korean pilot (b. 1901)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"Kwon Ki-ok\"><PERSON><PERSON></a>, Korean pilot (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"Kwon Ki-ok\"><PERSON><PERSON></a>, Korean pilot (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ok"}]}, {"year": "1989", "text": "<PERSON>, English novelist and playwright (b. 1907)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English-Australian director and producer (b. 1905)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian director and producer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian director and producer (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English actor and screenwriter (b. 1917)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American cult leader (b. 1959)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American captain, lawyer, and politician, 28th Governor of South Dakota (b. 1941)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician, 28th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Dakota\" title=\"Governor of South Dakota\">Governor of South Dakota</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician, 28th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Dakota\" title=\"Governor of South Dakota\">Governor of South Dakota</a> (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of South Dakota", "link": "https://wikipedia.org/wiki/Governor_of_South_Dakota"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Mexican poet, philosopher, and academic Nobel Prize laureate (b. 1914)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Octavio_Paz\" title=\"Octavio Paz\"><PERSON><PERSON><PERSON></a>, Mexican poet, philosopher, and academic <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octavio_<PERSON>\" title=\"Octavio Paz\"><PERSON><PERSON><PERSON></a>, Mexican poet, philosopher, and academic <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>avi<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Austrian-German SS officer (b. 1919)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "2000", "text": "<PERSON>, Canadian composer and conductor (b. 1918)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and conductor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and conductor (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American writer (b. 1920)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English author and activist co-founded the Guinness World Records (b. 1925)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist co-founded the <a href=\"https://wikipedia.org/wiki/Guinness_World_Records\" title=\"Guinness World Records\">Guinness World Records</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist co-founded the <a href=\"https://wikipedia.org/wiki/Guinness_World_Records\" title=\"Guinness World Records\">Guinness World Records</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Guinness World Records", "link": "https://wikipedia.org/wiki/Guinness_World_Records"}]}, {"year": "2004", "text": "<PERSON>, English biologist and geneticist (b. 1920)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and geneticist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and geneticist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Canadian WWII servicewoman and photographer (b. 1922)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian WWII servicewoman and photographer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian WWII servicewoman and photographer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American engineer, pilot, and astronaut (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, pilot, and astronaut (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, pilot, and astronaut (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, French actor (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON> <PERSON><PERSON>, English novelist, short story writer, and essayist (b. 1930)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, short story writer, and essayist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, short story writer, and essayist (b. 1930)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English actress (b. 1946)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American musician and actor (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lev<PERSON> Helm\"><PERSON><PERSON></a>, American musician and actor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Levon_<PERSON>lm\" title=\"<PERSON><PERSON> Helm\"><PERSON><PERSON></a>, American musician and actor (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lm"}]}, {"year": "2013", "text": "<PERSON>, French biologist and academic, Nobel Prize laureate (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2013", "text": "<PERSON>, American journalist, author, and publisher, founded USA Today (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and publisher, founded <i><a href=\"https://wikipedia.org/wiki/USA_Today\" title=\"USA Today\">USA Today</a></i> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and publisher, founded <i><a href=\"https://wikipedia.org/wiki/USA_Today\" title=\"USA Today\">USA Today</a></i> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_<PERSON>eu<PERSON>h"}, {"title": "USA Today", "link": "https://wikipedia.org/wiki/USA_Today"}]}, {"year": "2015", "text": "<PERSON>, English historian and academic (b. 1919)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English miner and politician, Secretary of State for Defence (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English miner and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Defence\" title=\"Secretary of State for Defence\">Secretary of State for Defence</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English miner and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Defence\" title=\"Secretary of State for Defence\">Secretary of State for Defence</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Defence", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Defence"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Chilean politician (b. 1918)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Pat<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean politician (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patric<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Taiwanese guitarist, performer and educator. (b. 1929)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese guitarist, performer and educator. (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese guitarist, performer and educator. (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American politician, 42nd Vice President of the United States (b. 1928)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 42nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 42nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "2021", "text": "<PERSON>, American composer, lyricist (b. 1947)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, lyricist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, lyricist (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Japanese supercentenarian (b. 1903)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese supercentenarian (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese supercentenarian (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, South Korean singer and actor (b. 1998)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer and actor (b. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer and actor (b. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>bin"}]}, {"year": "2023", "text": "<PERSON>, American musician (b. 1950)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician (b. 1950)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2024", "text": "<PERSON>, American philosopher and author (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}]}}