{"date": "April 24", "url": "https://wikipedia.org/wiki/April_24", "data": {"Events": [{"year": "1479 BC", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> III ascends to the throne of Egypt, although power effectively shifts to Hatshepsut (according to the Low Chronology of the 18th dynasty).", "html": "1479 BC - 1479 BC - <a href=\"https://wikipedia.org/wiki/Thutmose_III\" title=\"Thutmose III\">Thutmose III</a> ascends to the throne of <a href=\"https://wikipedia.org/wiki/Ancient_Egypt\" title=\"Ancient Egypt\">Egypt</a>, although power effectively shifts to <a href=\"https://wikipedia.org/wiki/Hatshepsut\" title=\"Hatshepsut\">Hatshepsut</a> (according to the Low Chronology of the 18th dynasty).", "no_year_html": "1479 BC - <a href=\"https://wikipedia.org/wiki/Thutmose_III\" title=\"Thutmose III\">Thutmose III</a> ascends to the throne of <a href=\"https://wikipedia.org/wiki/Ancient_Egypt\" title=\"Ancient Egypt\">Egypt</a>, although power effectively shifts to <a href=\"https://wikipedia.org/wiki/Hatshepsut\" title=\"Hatshepsut\">Hatshepsut</a> (according to the Low Chronology of the 18th dynasty).", "links": [{"title": "Thutmose III", "link": "https://wikipedia.org/wiki/Thutmose_III"}, {"title": "Ancient Egypt", "link": "https://wikipedia.org/wiki/Ancient_Egypt"}, {"title": "Hatshepsut", "link": "https://wikipedia.org/wiki/Hatshepsut"}]}, {"year": "1183 BC", "text": "Traditional reckoning of the Fall of Troy marking the end of the legendary Trojan War, given by chief librarian of the Library of Alexandria Eratosthenes, among others.", "html": "1183 BC - 1183 BC - Traditional reckoning of the Fall of <a href=\"https://wikipedia.org/wiki/Troy\" title=\"Troy\">Troy</a> marking the end of the legendary <a href=\"https://wikipedia.org/wiki/Trojan_War\" title=\"Trojan War\">Trojan War</a>, given by chief librarian of the <a href=\"https://wikipedia.org/wiki/Library_of_Alexandria\" title=\"Library of Alexandria\">Library of Alexandria</a> <a href=\"https://wikipedia.org/wiki/Eratosthenes\" title=\"Eratosthenes\">Eratosthenes</a>, among others.", "no_year_html": "1183 BC - Traditional reckoning of the Fall of <a href=\"https://wikipedia.org/wiki/Troy\" title=\"Troy\">Troy</a> marking the end of the legendary <a href=\"https://wikipedia.org/wiki/Trojan_War\" title=\"Trojan War\">Trojan War</a>, given by chief librarian of the <a href=\"https://wikipedia.org/wiki/Library_of_Alexandria\" title=\"Library of Alexandria\">Library of Alexandria</a> <a href=\"https://wikipedia.org/wiki/Eratosthenes\" title=\"Eratosthenes\">Eratosthenes</a>, among others.", "links": [{"title": "Troy", "link": "https://wikipedia.org/wiki/Troy"}, {"title": "Trojan War", "link": "https://wikipedia.org/wiki/Trojan_War"}, {"title": "Library of Alexandria", "link": "https://wikipedia.org/wiki/Library_of_Alexandria"}, {"title": "Eratosthenes", "link": "https://wikipedia.org/wiki/Eratosthenes"}]}, {"year": "1547", "text": "Battle of Mühlberg. Duke of Alba, commanding Spanish-Imperial forces of Charles I of Spain, defeats the troops of Schmalkaldic League.", "html": "1547 - <a href=\"https://wikipedia.org/wiki/Battle_of_M%C3%BChlberg\" title=\"Battle of Mühlberg\">Battle of Mühlberg</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_3rd_Duke_<PERSON>_Alba\" title=\"<PERSON>, 3rd Duke of Alba\">Duke of Alba</a>, commanding Spanish-Imperial forces of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON> of Spain</a>, defeats the troops of <a href=\"https://wikipedia.org/wiki/Schmalkaldic_League\" title=\"Schmalkaldic League\">Schmalkaldic League</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_M%C3%BChlberg\" title=\"Battle of Mühlberg\">Battle of Mühlberg</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>_%C<PERSON>%<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_3rd_Duke_of_Alba\" title=\"<PERSON>, 3rd Duke of Alba\">Duke of Alba</a>, commanding Spanish-Imperial forces of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON> of Spain</a>, defeats the troops of <a href=\"https://wikipedia.org/wiki/Schmalkaldic_League\" title=\"Schmalkaldic League\">Schmalkaldic League</a>.", "links": [{"title": "Battle of Mühlberg", "link": "https://wikipedia.org/wiki/Battle_of_M%C3%<PERSON><PERSON>berg"}, {"title": "<PERSON>, 3rd Duke of Alba", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Alba"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Schmalkaldic League", "link": "https://wikipedia.org/wiki/Schmalkaldic_League"}]}, {"year": "1558", "text": "<PERSON>, Queen of Scots, marries the <PERSON><PERSON><PERSON> of France, <PERSON>, at Notre-Dame de Paris.", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, <a href=\"https://wikipedia.org/wiki/Wedding_of_<PERSON>,_Queen_of_Scots,_and_<PERSON>,_<PERSON><PERSON><PERSON>_of_France\" title=\"<PERSON> of Mary, Queen of Scots, and <PERSON>, <PERSON><PERSON><PERSON> of France\">marries</a> the <a href=\"https://wikipedia.org/wiki/Dauphin_of_France\" title=\"<PERSON><PERSON><PERSON> of France\"><PERSON><PERSON><PERSON> of France</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON></a>, at <a href=\"https://wikipedia.org/wiki/Notre-Dame_de_Paris\" title=\"Notre-Dame de Paris\">Notre-Dame de Paris</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, <a href=\"https://wikipedia.org/wiki/Wedding_of_<PERSON>,_Queen_of_Scots,_and_<PERSON>,_<PERSON><PERSON><PERSON>_of_France\" title=\"<PERSON> <PERSON> Mary, Queen of Scots, and <PERSON>, <PERSON><PERSON><PERSON> of France\">marries</a> the <a href=\"https://wikipedia.org/wiki/Dauphin_of_France\" title=\"<PERSON><PERSON><PERSON> of France\"><PERSON><PERSON><PERSON> of France</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON></a>, at <a href=\"https://wikipedia.org/wiki/Notre-Dame_de_Paris\" title=\"Notre-Dame de Paris\">Notre-Dame de Paris</a>.", "links": [{"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}, {"title": "<PERSON> of <PERSON>, Queen of Scots, and <PERSON>, <PERSON><PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/Wedding_of_<PERSON>,_Queen_of_Scots,_and_<PERSON>,_<PERSON><PERSON><PERSON>_of_France"}, {"title": "<PERSON><PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/Dauphin_of_France"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "Notre-Dame de Paris", "link": "https://wikipedia.org/wiki/Notre-Dame_de_Paris"}]}, {"year": "1704", "text": "The first regular newspaper in British Colonial America, The Boston News-Letter, is published.", "html": "1704 - The first regular newspaper in <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">British Colonial America</a>, <i><a href=\"https://wikipedia.org/wiki/The_Boston_News-Letter\" title=\"The Boston News-Letter\">The Boston News-Letter</a></i>, is published.", "no_year_html": "The first regular newspaper in <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">British Colonial America</a>, <i><a href=\"https://wikipedia.org/wiki/The_Boston_News-Letter\" title=\"The Boston News-Letter\">The Boston News-Letter</a></i>, is published.", "links": [{"title": "Thirteen Colonies", "link": "https://wikipedia.org/wiki/Thirteen_Colonies"}, {"title": "The Boston News-Letter", "link": "https://wikipedia.org/wiki/The_Boston_News-Letter"}]}, {"year": "1793", "text": "French revolutionary <PERSON><PERSON><PERSON> is acquitted by the Revolutionary Tribunal of charges brought by the Girondin in Paris.", "html": "1793 - French revolutionary <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is acquitted by the Revolutionary Tribunal of charges brought by the Girondin in Paris.", "no_year_html": "French revolutionary <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is acquitted by the Revolutionary Tribunal of charges brought by the Girondin in Paris.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1800", "text": "The United States Library of Congress is established when President <PERSON> signs legislation to appropriate $5,000 to purchase \"such books as may be necessary for the use of Congress\".", "html": "1800 - The United States <a href=\"https://wikipedia.org/wiki/Library_of_Congress\" title=\"Library of Congress\">Library of Congress</a> is established when President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs legislation to appropriate $5,000 to purchase \"such books as may be necessary for the use of <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a>\".", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Library_of_Congress\" title=\"Library of Congress\">Library of Congress</a> is established when President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs legislation to appropriate $5,000 to purchase \"such books as may be necessary for the use of <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a>\".", "links": [{"title": "Library of Congress", "link": "https://wikipedia.org/wiki/Library_of_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1837", "text": "The great fire in Surat city of India caused more than 500 deaths and destruction of more than 9,000 houses.", "html": "1837 - The <a href=\"https://wikipedia.org/wiki/1837_Surat_fire\" title=\"1837 Surat fire\">great fire in Surat</a> city of India caused more than 500 deaths and destruction of more than 9,000 houses.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1837_Surat_fire\" title=\"1837 Surat fire\">great fire in Surat</a> city of India caused more than 500 deaths and destruction of more than 9,000 houses.", "links": [{"title": "1837 Surat fire", "link": "https://wikipedia.org/wiki/1837_Surat_fire"}]}, {"year": "1877", "text": "Russo-Turkish War: Russian Empire declares war on Ottoman Empire.", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1877%E2%80%9378)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1877-78)\">Russo-Turkish War</a>: <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a> declares war on <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1877%E2%80%9378)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1877-78)\">Russo-Turkish War</a>: <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a> declares war on <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "Russo-Turkish War (1877-78)", "link": "https://wikipedia.org/wiki/Russo-Turkish_War_(1877%E2%80%9378)"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1885", "text": "American sharpshooter <PERSON> is hired by <PERSON> to be a part of Buffalo Bill's Wild West.", "html": "1885 - American <a href=\"https://wikipedia.org/wiki/Sharpshooter\" title=\"Sharpshooter\">sharpshooter</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is hired by <PERSON> to be a part of <a href=\"https://wikipedia.org/wiki/Buffalo_Bill%27s_Wild_West\" class=\"mw-redirect\" title=\"Buffalo Bill's Wild West\">Buffalo Bill's Wild West</a>.", "no_year_html": "American <a href=\"https://wikipedia.org/wiki/Sharpshooter\" title=\"Sharpshooter\">sharpshooter</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is hired by <PERSON> to be a part of <a href=\"https://wikipedia.org/wiki/Buffalo_Bill%27s_Wild_West\" class=\"mw-redirect\" title=\"Buffalo Bill's Wild West\">Buffalo Bill's Wild West</a>.", "links": [{"title": "Sharpshooter", "link": "https://wikipedia.org/wiki/Sharpshooter"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Buffalo Bill's Wild West", "link": "https://wikipedia.org/wiki/Buffalo_Bill%27s_Wild_West"}]}, {"year": "1895", "text": "<PERSON>, the first person to sail single-handedly around the world, sets sail from Boston, Massachusetts aboard the sloop <PERSON><PERSON><PERSON>.", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first person to sail single-handedly around the world, sets sail from Boston, Massachusetts aboard the sloop <i>Spray</i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first person to sail single-handedly around the world, sets sail from Boston, Massachusetts aboard the sloop <i>Spray</i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "The Woolworth Building, a skyscraper in New York City, is opened.", "html": "1913 - The <a href=\"https://wikipedia.org/wiki/Woolworth_Building\" title=\"Woolworth Building\">Woolworth Building</a>, a <a href=\"https://wikipedia.org/wiki/Skyscraper\" title=\"Skyscraper\">skyscraper</a> in New York City, is opened.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Woolworth_Building\" title=\"Woolworth Building\">Woolworth Building</a>, a <a href=\"https://wikipedia.org/wiki/Skyscraper\" title=\"Skyscraper\">skyscraper</a> in New York City, is opened.", "links": [{"title": "Woolworth Building", "link": "https://wikipedia.org/wiki/Woolworth_Building"}, {"title": "Skyscraper", "link": "https://wikipedia.org/wiki/Skyscraper"}]}, {"year": "1914", "text": "The <PERSON><PERSON><PERSON><PERSON> experiment, a pillar of quantum mechanics, is presented to the German Physical Society.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/Franck%E2%80%93Hertz_experiment\" title=\"<PERSON><PERSON><PERSON>-Hertz experiment\"><PERSON><PERSON><PERSON>-<PERSON>tz experiment</a>, a pillar of <a href=\"https://wikipedia.org/wiki/Introduction_to_quantum_mechanics\" title=\"Introduction to quantum mechanics\">quantum mechanics</a>, is presented to the <a href=\"https://wikipedia.org/wiki/Deutsche_Physikalische_Gesellschaft\" class=\"mw-redirect\" title=\"Deutsche Physikalische Gesellschaft\">German Physical Society</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Franck%E2%80%93Hertz_experiment\" title=\"<PERSON><PERSON><PERSON>-<PERSON><PERSON> experiment\"><PERSON><PERSON><PERSON>-<PERSON>tz experiment</a>, a pillar of <a href=\"https://wikipedia.org/wiki/Introduction_to_quantum_mechanics\" title=\"Introduction to quantum mechanics\">quantum mechanics</a>, is presented to the <a href=\"https://wikipedia.org/wiki/Deutsche_Physikalische_Gesellschaft\" class=\"mw-redirect\" title=\"Deutsche Physikalische Gesellschaft\">German Physical Society</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> experiment", "link": "https://wikipedia.org/wiki/Franck%E2%80%93Hertz_experiment"}, {"title": "Introduction to quantum mechanics", "link": "https://wikipedia.org/wiki/Introduction_to_quantum_mechanics"}, {"title": "Deutsche Physikalische Gesellschaft", "link": "https://wikipedia.org/wiki/Deutsche_Physikalische_Gesellschaft"}]}, {"year": "1915", "text": "The arrest of 250 Armenian intellectuals and community leaders in Istanbul marks the beginning of the Armenian genocide.", "html": "1915 - The arrest of <a href=\"https://wikipedia.org/wiki/Deportation_of_Armenian_intellectuals_on_24_April_1915\" title=\"Deportation of Armenian intellectuals on 24 April 1915\">250 Armenian intellectuals and community leaders</a> in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a> marks the beginning of the <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a>.", "no_year_html": "The arrest of <a href=\"https://wikipedia.org/wiki/Deportation_of_Armenian_intellectuals_on_24_April_1915\" title=\"Deportation of Armenian intellectuals on 24 April 1915\">250 Armenian intellectuals and community leaders</a> in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a> marks the beginning of the <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a>.", "links": [{"title": "Deportation of Armenian intellectuals on 24 April 1915", "link": "https://wikipedia.org/wiki/Deportation_of_Armenian_intellectuals_on_24_April_1915"}, {"title": "Istanbul", "link": "https://wikipedia.org/wiki/Istanbul"}, {"title": "Armenian genocide", "link": "https://wikipedia.org/wiki/Armenian_genocide"}]}, {"year": "1916", "text": "Easter Rising: Irish rebels, led by <PERSON> and <PERSON>, launch an uprising in Dublin against British rule and proclaim an Irish Republic.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>: Irish rebels, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, launch an uprising in Dublin against British rule and <a href=\"https://wikipedia.org/wiki/Proclamation_of_the_Irish_Republic\" title=\"Proclamation of the Irish Republic\">proclaim an Irish Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>: Irish rebels, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, launch an uprising in Dublin against British rule and <a href=\"https://wikipedia.org/wiki/Proclamation_of_the_Irish_Republic\" title=\"Proclamation of the Irish Republic\">proclaim an Irish Republic</a>.", "links": [{"title": "Easter Rising", "link": "https://wikipedia.org/wiki/Easter_Rising"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Proclamation of the Irish Republic", "link": "https://wikipedia.org/wiki/Proclamation_of_the_Irish_Republic"}]}, {"year": "1916", "text": "<PERSON> and five men of the Imperial Trans-Antarctic Expedition launch a lifeboat from uninhabited Elephant Island in the Southern Ocean to organise a rescue for the crew of the sunken Endurance.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and five men of the <a href=\"https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition\" title=\"Imperial Trans-Antarctic Expedition\">Imperial Trans-Antarctic Expedition</a> launch a lifeboat from uninhabited <a href=\"https://wikipedia.org/wiki/Elephant_Island\" title=\"Elephant Island\">Elephant Island</a> in the <a href=\"https://wikipedia.org/wiki/Southern_Ocean\" title=\"Southern Ocean\">Southern Ocean</a> to organise a rescue for the crew of the sunken <a href=\"https://wikipedia.org/wiki/Endurance_(1912_ship)\" title=\"Endurance (1912 ship)\"><i>Endurance</i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and five men of the <a href=\"https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition\" title=\"Imperial Trans-Antarctic Expedition\">Imperial Trans-Antarctic Expedition</a> launch a lifeboat from uninhabited <a href=\"https://wikipedia.org/wiki/Elephant_Island\" title=\"Elephant Island\">Elephant Island</a> in the <a href=\"https://wikipedia.org/wiki/Southern_Ocean\" title=\"Southern Ocean\">Southern Ocean</a> to organise a rescue for the crew of the sunken <a href=\"https://wikipedia.org/wiki/Endurance_(1912_ship)\" title=\"Endurance (1912 ship)\"><i>Endurance</i></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Imperial Trans-Antarctic Expedition", "link": "https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition"}, {"title": "Elephant Island", "link": "https://wikipedia.org/wiki/Elephant_Island"}, {"title": "Southern Ocean", "link": "https://wikipedia.org/wiki/Southern_Ocean"}, {"title": "Endurance (1912 ship)", "link": "https://wikipedia.org/wiki/Endurance_(1912_ship)"}]}, {"year": "1918", "text": "World War I: First tank-to-tank combat, during the second Battle of Villers-Bretonneux. Three British Mark IVs meet three German A7Vs.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: First tank-to-tank combat, during the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Villers-Bretonneux\" title=\"Second Battle of Villers-Bretonneux\">second Battle of Villers-Bretonneux</a>. Three British <a href=\"https://wikipedia.org/wiki/Mark_IV_tank\" title=\"Mark IV tank\">Mark IVs</a> meet three German <a href=\"https://wikipedia.org/wiki/A7V\" title=\"A7V\">A7Vs</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: First tank-to-tank combat, during the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Villers-Bretonneux\" title=\"Second Battle of Villers-Bretonneux\">second Battle of Villers-Bretonneux</a>. Three British <a href=\"https://wikipedia.org/wiki/Mark_IV_tank\" title=\"Mark IV tank\">Mark IVs</a> meet three German <a href=\"https://wikipedia.org/wiki/A7V\" title=\"A7V\">A7Vs</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Second Battle of Villers-Bretonneux", "link": "https://wikipedia.org/wiki/Second_Battle_of_Ville<PERSON>-<PERSON>ux"}, {"title": "Mark IV tank", "link": "https://wikipedia.org/wiki/Mark_IV_tank"}, {"title": "A7V", "link": "https://wikipedia.org/wiki/A7V"}]}, {"year": "1922", "text": "The first segment of the Imperial Wireless Chain providing wireless telegraphy between Leafield in Oxfordshire, England, and Cairo, Egypt, comes into operation.", "html": "1922 - The first segment of the <a href=\"https://wikipedia.org/wiki/Imperial_Wireless_Chain\" title=\"Imperial Wireless Chain\">Imperial Wireless Chain</a> providing <a href=\"https://wikipedia.org/wiki/Wireless_telegraphy\" title=\"Wireless telegraphy\">wireless telegraphy</a> between <a href=\"https://wikipedia.org/wiki/Leafield_wireless_station\" class=\"mw-redirect\" title=\"Leafield wireless station\">Leafield</a> in Oxfordshire, England, and <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, Egypt, comes into operation.", "no_year_html": "The first segment of the <a href=\"https://wikipedia.org/wiki/Imperial_Wireless_Chain\" title=\"Imperial Wireless Chain\">Imperial Wireless Chain</a> providing <a href=\"https://wikipedia.org/wiki/Wireless_telegraphy\" title=\"Wireless telegraphy\">wireless telegraphy</a> between <a href=\"https://wikipedia.org/wiki/Leafield_wireless_station\" class=\"mw-redirect\" title=\"Leafield wireless station\">Leafield</a> in Oxfordshire, England, and <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, Egypt, comes into operation.", "links": [{"title": "Imperial Wireless Chain", "link": "https://wikipedia.org/wiki/Imperial_Wireless_Chain"}, {"title": "Wireless telegraphy", "link": "https://wikipedia.org/wiki/Wireless_telegraphy"}, {"title": "Leafield wireless station", "link": "https://wikipedia.org/wiki/Leafield_wireless_station"}, {"title": "Cairo", "link": "https://wikipedia.org/wiki/Cairo"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON> becomes premier of Denmark (first term).", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes premier of <a href=\"https://wikipedia.org/wiki/Denmark\" title=\"Denmark\">Denmark</a> (first term).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes premier of <a href=\"https://wikipedia.org/wiki/Denmark\" title=\"Denmark\">Denmark</a> (first term).", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Denmark", "link": "https://wikipedia.org/wiki/Denmark"}]}, {"year": "1926", "text": "The Treaty of Berlin is signed. Germany and the Soviet Union each pledge neutrality in the event of an attack on the other by a third party for the next five years.", "html": "1926 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Berlin_(1926)\" title=\"Treaty of Berlin (1926)\">Treaty of Berlin</a> is signed. Germany and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> each pledge neutrality in the event of an attack on the other by a third party for the next five years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Berlin_(1926)\" title=\"Treaty of Berlin (1926)\">Treaty of Berlin</a> is signed. Germany and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> each pledge neutrality in the event of an attack on the other by a third party for the next five years.", "links": [{"title": "Treaty of Berlin (1926)", "link": "https://wikipedia.org/wiki/Treaty_of_Berlin_(1926)"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1932", "text": "<PERSON> leads the mass trespass of Kinder Scout, leading to substantial legal reforms in the United Kingdom.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Mass_trespass_of_Kinder_Scout\" title=\"Mass trespass of Kinder Scout\">mass trespass of Kinder Scout</a>, leading to substantial legal reforms in the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Mass_trespass_of_Kinder_Scout\" title=\"Mass trespass of Kinder Scout\">mass trespass of Kinder Scout</a>, leading to substantial legal reforms in the United Kingdom.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mass trespass of Kinder Scout", "link": "https://wikipedia.org/wiki/Mass_trespass_of_<PERSON>er_Scout"}]}, {"year": "1933", "text": "Nazi Germany begins its persecution of <PERSON><PERSON><PERSON>'s Witnesses by shutting down the Watch Tower Society office in Magdeburg.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> begins its <a href=\"https://wikipedia.org/wiki/Persecution_of_Jehovah%27s_Witnesses_in_Nazi_Germany\" title=\"Persecution of <PERSON><PERSON><PERSON>'s Witnesses in Nazi Germany\">persecution of <PERSON><PERSON><PERSON>'s Witnesses</a> by shutting down the <a href=\"https://wikipedia.org/wiki/Corporations_of_Jehovah%27s_Witnesses\" title=\"Corporations of Jehovah's Witnesses\">Watch Tower Society</a> office in <a href=\"https://wikipedia.org/wiki/Magdeburg\" title=\"Magdeburg\">Magdeburg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> begins its <a href=\"https://wikipedia.org/wiki/Persecution_of_Jehovah%27s_Witnesses_in_Nazi_Germany\" title=\"Persecution of <PERSON><PERSON><PERSON>'s Witnesses in Nazi Germany\">persecution of <PERSON><PERSON><PERSON>'s Witnesses</a> by shutting down the <a href=\"https://wikipedia.org/wiki/Corporations_of_Jehovah%27s_Witnesses\" title=\"Corporations of Je<PERSON>ah's Witnesses\">Watch Tower Society</a> office in <a href=\"https://wikipedia.org/wiki/Magdeburg\" title=\"Magdeburg\">Magdeburg</a>.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Persecution of <PERSON><PERSON><PERSON>'s Witnesses in Nazi Germany", "link": "https://wikipedia.org/wiki/Persecution_of_Jehovah%27s_Witnesses_in_Nazi_Germany"}, {"title": "Corporations of Jehovah's Witnesses", "link": "https://wikipedia.org/wiki/Corporations_of_Jehovah%27s_Witnesses"}, {"title": "Magdeburg", "link": "https://wikipedia.org/wiki/Magdeburg"}]}, {"year": "1944", "text": "World War II: The SBS launches a raid against the garrison of Santorini in Greece.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Special_Boat_Service\" title=\"Special Boat Service\">SBS</a> launches a <a href=\"https://wikipedia.org/wiki/Raid_on_Santorini\" title=\"Raid on Santorini\">raid against the garrison of Santorini</a> in Greece.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Special_Boat_Service\" title=\"Special Boat Service\">SBS</a> launches a <a href=\"https://wikipedia.org/wiki/Raid_on_Santorini\" title=\"Raid on Santorini\">raid against the garrison of Santorini</a> in Greece.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Special Boat Service", "link": "https://wikipedia.org/wiki/Special_Boat_Service"}, {"title": "Raid on Santorini", "link": "https://wikipedia.org/wiki/Raid_on_Santorini"}]}, {"year": "1953", "text": "<PERSON> is knighted by Queen <PERSON>.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is knighted by <PERSON> <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"<PERSON> II\"><PERSON> II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is knighted by <PERSON> <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}]}, {"year": "1955", "text": "The Bandung Conference ends: Twenty-nine non-aligned nations of Asia and Africa finish a meeting that condemns colonialism, racism, and the Cold War.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Bandung_Conference\" title=\"Bandung Conference\">Bandung Conference</a> ends: Twenty-nine non-aligned nations of Asia and Africa finish a meeting that condemns <a href=\"https://wikipedia.org/wiki/Colonialism\" title=\"Colonialism\">colonialism</a>, <a href=\"https://wikipedia.org/wiki/Racism\" title=\"Racism\">racism</a>, and the <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bandung_Conference\" title=\"Bandung Conference\">Bandung Conference</a> ends: Twenty-nine non-aligned nations of Asia and Africa finish a meeting that condemns <a href=\"https://wikipedia.org/wiki/Colonialism\" title=\"Colonialism\">colonialism</a>, <a href=\"https://wikipedia.org/wiki/Racism\" title=\"Racism\">racism</a>, and the <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>.", "links": [{"title": "Bandung Conference", "link": "https://wikipedia.org/wiki/Bandung_Conference"}, {"title": "Colonialism", "link": "https://wikipedia.org/wiki/Colonialism"}, {"title": "Racism", "link": "https://wikipedia.org/wiki/Racism"}, {"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}]}, {"year": "1957", "text": "Suez Crisis: The Suez Canal is reopened following the introduction of UNEF peacekeepers to the region.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>: The <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> is reopened following the introduction of <a href=\"https://wikipedia.org/wiki/United_Nations_Emergency_Force\" title=\"United Nations Emergency Force\">UNEF</a> peacekeepers to the region.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>: The <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> is reopened following the introduction of <a href=\"https://wikipedia.org/wiki/United_Nations_Emergency_Force\" title=\"United Nations Emergency Force\">UNEF</a> peacekeepers to the region.", "links": [{"title": "Suez Crisis", "link": "https://wikipedia.org/wiki/Suez_Crisis"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}, {"title": "United Nations Emergency Force", "link": "https://wikipedia.org/wiki/United_Nations_Emergency_Force"}]}, {"year": "1963", "text": "Marriage of Princess <PERSON> of Kent to <PERSON> at Westminster Abbey in London.", "html": "1963 - Marriage of <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_The_Honourable_Lady_<PERSON><PERSON>\" title=\"Princess <PERSON>, The Honourable Lady <PERSON>\">Princess <PERSON> of Kent</a> to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a> in London.", "no_year_html": "Marriage of <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_The_Honourable_Lady_<PERSON><PERSON>\" title=\"Princess <PERSON>, The Honourable Lady <PERSON>\">Princess <PERSON> of Kent</a> to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>vy\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a> in London.", "links": [{"title": "Princess <PERSON>, The Honourable Lady <PERSON>", "link": "https://wikipedia.org/wiki/Princess_<PERSON>,_The_Honourable_Lady_<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Angus_Ogilvy"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1965", "text": "Civil war breaks out in the Dominican Republic when Colonel <PERSON> overthrows the triumvirate that had been in power since the coup d'état against <PERSON>.", "html": "1965 - Civil war breaks out in the <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a> when Colonel <a href=\"https://wikipedia.org/wiki/Francisco_Caama%C3%B1o\" title=\"<PERSON>\"><PERSON></a> overthrows the <a href=\"https://wikipedia.org/wiki/Triumvirate\" title=\"Triumvirate\">triumvirate</a> that had been in power since the <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> against <a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>.", "no_year_html": "Civil war breaks out in the <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a> when Colonel <a href=\"https://wikipedia.org/wiki/Francisco_Caama%C3%B1o\" title=\"<PERSON>\"><PERSON></a> overthrows the <a href=\"https://wikipedia.org/wiki/Triumvirate\" title=\"Triumvirate\">triumvirate</a> that had been in power since the <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> against <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>.", "links": [{"title": "Dominican Republic", "link": "https://wikipedia.org/wiki/Dominican_Republic"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Caama%C3%B1o"}, {"title": "Triumvirate", "link": "https://wikipedia.org/wiki/Triumvirate"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1967", "text": "Cosmonaut <PERSON> dies in Soyuz 1 when its parachute fails to open. He is the first human to die during a space mission.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">Cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in <a href=\"https://wikipedia.org/wiki/Soyuz_1\" title=\"Soyuz 1\">Soyuz 1</a> when its <a href=\"https://wikipedia.org/wiki/Parachute\" title=\"Parachute\">parachute</a> fails to open. He is the first human to die during a space mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">Cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in <a href=\"https://wikipedia.org/wiki/Soyuz_1\" title=\"Soyuz 1\">Soyuz 1</a> when its <a href=\"https://wikipedia.org/wiki/Parachute\" title=\"Parachute\">parachute</a> fails to open. He is the first human to die during a space mission.", "links": [{"title": "Astronaut", "link": "https://wikipedia.org/wiki/Astronaut"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Soyuz 1", "link": "https://wikipedia.org/wiki/Soyuz_1"}, {"title": "Parachute", "link": "https://wikipedia.org/wiki/Parachute"}]}, {"year": "1967", "text": "Vietnam War: American General <PERSON> says in a news conference that the enemy had \"gained support in the United States that gives him hope that he can win politically that which he cannot win militarily\".", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: American General <a href=\"https://wikipedia.org/wiki/William_Westmoreland\" title=\"William <PERSON>\"><PERSON></a> says in a news conference that the enemy had \"gained support in the United States that gives him hope that he can win politically that which he cannot win militarily\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: American General <a href=\"https://wikipedia.org/wiki/William_Westmoreland\" title=\"William <PERSON>\"><PERSON></a> says in a news conference that the enemy had \"gained support in the United States that gives him hope that he can win politically that which he cannot win militarily\".", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_<PERSON>moreland"}]}, {"year": "1970", "text": "China launches Dong Fang Hong I, becoming the fifth nation to put an object into orbit using its own booster.", "html": "1970 - China launches <i><a href=\"https://wikipedia.org/wiki/Dong_Fang_Hong_I\" class=\"mw-redirect\" title=\"Dong Fang Hong I\"><PERSON> I</a></i>, becoming the fifth nation to put an object into orbit using its own booster.", "no_year_html": "China launches <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hong_I\" class=\"mw-redirect\" title=\"Dong Fang Hong I\"><PERSON> I</a></i>, becoming the fifth nation to put an object into orbit using its own booster.", "links": [{"title": "Dong Fang Hong I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_I"}]}, {"year": "1970", "text": "The Gambia becomes a republic within the Commonwealth of Nations, with <PERSON><PERSON><PERSON> as its first President.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/The_Gambia\" title=\"The Gambia\">The Gambia</a> becomes a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> within the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a>, with <a href=\"https://wikipedia.org/wiki/Dawda_Jawara\" title=\"Dawda Jawara\">Dawda Jawara</a> as its first <a href=\"https://wikipedia.org/wiki/President_of_the_Gambia\" title=\"President of the Gambia\">President</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Gambia\" title=\"The Gambia\">The Gambia</a> becomes a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> within the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a>, with <a href=\"https://wikipedia.org/wiki/Dawda_Jawara\" title=\"Dawda Jawara\">Dawda Jawara</a> as its first <a href=\"https://wikipedia.org/wiki/President_of_the_Gambia\" title=\"President of the Gambia\">President</a>.", "links": [{"title": "The Gambia", "link": "https://wikipedia.org/wiki/The_Gambia"}, {"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dawda_Jawara"}, {"title": "President of the Gambia", "link": "https://wikipedia.org/wiki/President_of_the_Gambia"}]}, {"year": "1979", "text": "<PERSON>, a British activist, died after being knocked unconscious during an Anti-Nazi League demonstration against a National Front election meeting in Southall, London.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Peach\"><PERSON></a>, a British activist, died after being knocked unconscious during an <a href=\"https://wikipedia.org/wiki/Anti-Nazi_League\" title=\"Anti-Nazi League\">Anti-Nazi League</a> demonstration against a <a href=\"https://wikipedia.org/wiki/National_Front_(UK)\" title=\"National Front (UK)\">National Front</a> election meeting in <a href=\"https://wikipedia.org/wiki/Southall\" title=\"Southall\">Southall</a>, London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Peach\"><PERSON></a>, a British activist, died after being knocked unconscious during an <a href=\"https://wikipedia.org/wiki/Anti-Nazi_League\" title=\"Anti-Nazi League\">Anti-Nazi League</a> demonstration against a <a href=\"https://wikipedia.org/wiki/National_Front_(UK)\" title=\"National Front (UK)\">National Front</a> election meeting in <a href=\"https://wikipedia.org/wiki/Southall\" title=\"Southall\">Southall</a>, London.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Anti-Nazi League", "link": "https://wikipedia.org/wiki/Anti-Nazi_League"}, {"title": "National Front (UK)", "link": "https://wikipedia.org/wiki/National_Front_(UK)"}, {"title": "Southall", "link": "https://wikipedia.org/wiki/Southall"}]}, {"year": "1980", "text": "Eight U.S. servicemen die in Operation Eagle Claw as they attempt to end the Iran hostage crisis.", "html": "1980 - Eight U.S. servicemen die in <a href=\"https://wikipedia.org/wiki/Operation_Eagle_Claw\" title=\"Operation Eagle Claw\">Operation Eagle Claw</a> as they attempt to end the <a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>.", "no_year_html": "Eight U.S. servicemen die in <a href=\"https://wikipedia.org/wiki/Operation_Eagle_Claw\" title=\"Operation Eagle Claw\">Operation Eagle Claw</a> as they attempt to end the <a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>.", "links": [{"title": "Operation Eagle Claw", "link": "https://wikipedia.org/wiki/Operation_Eagle_Claw"}, {"title": "Iran hostage crisis", "link": "https://wikipedia.org/wiki/Iran_hostage_crisis"}]}, {"year": "1990", "text": "STS-31: The Hubble Space Telescope is launched from the Space Shuttle Discovery.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/STS-31\" title=\"STS-31\">STS-31</a>: The <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a> is launched from the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle</a> <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Discovery</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/STS-31\" title=\"STS-31\">STS-31</a>: The <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a> is launched from the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle</a> <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Discovery</a></i>.", "links": [{"title": "STS-31", "link": "https://wikipedia.org/wiki/STS-31"}, {"title": "<PERSON>bble Space Telescope", "link": "https://wikipedia.org/wiki/Hubble_Space_Telescope"}, {"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}]}, {"year": "1990", "text": "Gruinard Island, Scotland, is officially declared free of the anthrax disease after 48 years of quarantine.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Gruinard_Island\" title=\"Gruinard Island\">Gruinard Island</a>, Scotland, is officially declared free of the <a href=\"https://wikipedia.org/wiki/Anthrax\" title=\"Anthrax\">anthrax</a> disease after 48 years of <a href=\"https://wikipedia.org/wiki/Quarantine\" title=\"Quarantine\">quarantine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gruinard_Island\" title=\"Gruinard Island\">Gruinard Island</a>, Scotland, is officially declared free of the <a href=\"https://wikipedia.org/wiki/Anthrax\" title=\"Anthrax\">anthrax</a> disease after 48 years of <a href=\"https://wikipedia.org/wiki/Quarantine\" title=\"Quarantine\">quarantine</a>.", "links": [{"title": "Gruinard Island", "link": "https://wikipedia.org/wiki/Gruinard_Island"}, {"title": "Anthrax", "link": "https://wikipedia.org/wiki/Anthrax"}, {"title": "Quarantine", "link": "https://wikipedia.org/wiki/Quarantine"}]}, {"year": "1993", "text": "An IRA bomb devastates the Bishopsgate area of London.", "html": "1993 - An <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">IRA</a> <a href=\"https://wikipedia.org/wiki/1993_Bishopsgate_bombing\" title=\"1993 Bishopsgate bombing\">bomb devastates the Bishopsgate area</a> of London.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">IRA</a> <a href=\"https://wikipedia.org/wiki/1993_Bishopsgate_bombing\" title=\"1993 Bishopsgate bombing\">bomb devastates the Bishopsgate area</a> of London.", "links": [{"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "1993 Bishopsgate bombing", "link": "https://wikipedia.org/wiki/1993_Bishopsgate_bombing"}]}, {"year": "1994", "text": "A Douglas DC-3 ditches in Botany Bay after takeoff from Sydney Airport. All 25 people on board survive.", "html": "1994 - A <a href=\"https://wikipedia.org/wiki/Douglas_DC-3\" title=\"Douglas DC-3\">Douglas DC-3</a> <a href=\"https://wikipedia.org/wiki/1994_South_Pacific_Airmotive_DC-3_crash\" title=\"1994 South Pacific Airmotive DC-3 crash\">ditches</a> in <a href=\"https://wikipedia.org/wiki/Botany_Bay\" title=\"Botany Bay\">Botany Bay</a> after takeoff from <a href=\"https://wikipedia.org/wiki/Sydney_Airport\" title=\"Sydney Airport\">Sydney Airport</a>. All 25 people on board survive.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Douglas_DC-3\" title=\"Douglas DC-3\">Douglas DC-3</a> <a href=\"https://wikipedia.org/wiki/1994_South_Pacific_Airmotive_DC-3_crash\" title=\"1994 South Pacific Airmotive DC-3 crash\">ditches</a> in <a href=\"https://wikipedia.org/wiki/Botany_Bay\" title=\"Botany Bay\">Botany Bay</a> after takeoff from <a href=\"https://wikipedia.org/wiki/Sydney_Airport\" title=\"Sydney Airport\">Sydney Airport</a>. All 25 people on board survive.", "links": [{"title": "Douglas DC-3", "link": "https://wikipedia.org/wiki/Douglas_DC-3"}, {"title": "1994 South Pacific Airmotive DC-3 crash", "link": "https://wikipedia.org/wiki/1994_South_Pacific_Airmotive_DC-3_crash"}, {"title": "Botany Bay", "link": "https://wikipedia.org/wiki/Botany_Bay"}, {"title": "Sydney Airport", "link": "https://wikipedia.org/wiki/Sydney_Airport"}]}, {"year": "1996", "text": "In the United States, the Antiterrorism and Effective Death Penalty Act of 1996 is passed into law.", "html": "1996 - In the United States, the <a href=\"https://wikipedia.org/wiki/Antiterrorism_and_Effective_Death_Penalty_Act_of_1996\" title=\"Antiterrorism and Effective Death Penalty Act of 1996\">Antiterrorism and Effective Death Penalty Act of 1996</a> is passed into law.", "no_year_html": "In the United States, the <a href=\"https://wikipedia.org/wiki/Antiterrorism_and_Effective_Death_Penalty_Act_of_1996\" title=\"Antiterrorism and Effective Death Penalty Act of 1996\">Antiterrorism and Effective Death Penalty Act of 1996</a> is passed into law.", "links": [{"title": "Antiterrorism and Effective Death Penalty Act of 1996", "link": "https://wikipedia.org/wiki/Antiterrorism_and_Effective_Death_Penalty_Act_of_1996"}]}, {"year": "2004", "text": "The United States lifts economic sanctions imposed on Libya 18 years previously, as a reward for its cooperation in eliminating weapons of mass destruction.", "html": "2004 - The United States lifts <a href=\"https://wikipedia.org/wiki/Economic_sanctions\" title=\"Economic sanctions\">economic sanctions</a> imposed on <a href=\"https://wikipedia.org/wiki/History_of_Libya_under_<PERSON><PERSON><PERSON>_<PERSON>#Great_Socialist_People's_Libyan_Arab_Jamahiriya_(1977-2012)\" title=\"History of Libya under <PERSON><PERSON><PERSON>\">Libya</a> 18 years previously, as a reward for its cooperation in eliminating <a href=\"https://wikipedia.org/wiki/Weapons_of_mass_destruction\" class=\"mw-redirect\" title=\"Weapons of mass destruction\">weapons of mass destruction</a>.", "no_year_html": "The United States lifts <a href=\"https://wikipedia.org/wiki/Economic_sanctions\" title=\"Economic sanctions\">economic sanctions</a> imposed on <a href=\"https://wikipedia.org/wiki/History_of_Libya_under_<PERSON><PERSON><PERSON>_<PERSON>#Great_Socialist_People's_Libyan_Arab_Jamahiriya_(1977-2012)\" title=\"History of Libya under <PERSON><PERSON><PERSON>\">Libya</a> 18 years previously, as a reward for its cooperation in eliminating <a href=\"https://wikipedia.org/wiki/Weapons_of_mass_destruction\" class=\"mw-redirect\" title=\"Weapons of mass destruction\">weapons of mass destruction</a>.", "links": [{"title": "Economic sanctions", "link": "https://wikipedia.org/wiki/Economic_sanctions"}, {"title": "History of Libya under <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/History_of_Libya_under_<PERSON><PERSON><PERSON>_<PERSON>#Great_Socialist_People's_Libyan_Arab_Jamahiriya_(1977-2012)"}, {"title": "Weapons of mass destruction", "link": "https://wikipedia.org/wiki/Weapons_of_mass_destruction"}]}, {"year": "2005", "text": "Cardinal <PERSON> is inaugurated as the 265th Pope of the Catholic Church taking the name <PERSON> <PERSON>.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Cardinal_(Catholicism)\" class=\"mw-redirect\" title=\"Cardinal (Catholicism)\">Cardinal</a> <PERSON> is <a href=\"https://wikipedia.org/wiki/Papal_inauguration\" title=\"Papal inauguration\">inaugurated</a> as the 265th <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">Pope</a> of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> taking the name <a href=\"https://wikipedia.org/wiki/Pope_Benedict_XVI\" title=\"Pope Benedict XVI\">Pope <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Catholicism)\" class=\"mw-redirect\" title=\"Cardinal (Catholicism)\">Cardinal</a> <PERSON> is <a href=\"https://wikipedia.org/wiki/Papal_inauguration\" title=\"Papal inauguration\">inaugurated</a> as the 265th <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">Pope</a> of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> taking the name <a href=\"https://wikipedia.org/wiki/<PERSON>_Benedict_XVI\" title=\"Pope Benedict XVI\">Pope <PERSON></a>.", "links": [{"title": "<PERSON> (Catholicism)", "link": "https://wikipedia.org/wiki/<PERSON>_(Catholicism)"}, {"title": "Papal inauguration", "link": "https://wikipedia.org/wiki/Papal_inauguration"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "Bombings in the Egyptian resort city of Dahab kill 23 people and injure about 80.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/2006_Dahab_bombings\" title=\"2006 Dahab bombings\">Bombings</a> in the Egyptian <a href=\"https://wikipedia.org/wiki/Resort_town\" title=\"Resort town\">resort city</a> of <a href=\"https://wikipedia.org/wiki/Dahab\" title=\"Dahab\">Dahab</a> kill 23 people and injure about 80.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2006_Dahab_bombings\" title=\"2006 Dahab bombings\">Bombings</a> in the Egyptian <a href=\"https://wikipedia.org/wiki/Resort_town\" title=\"Resort town\">resort city</a> of <a href=\"https://wikipedia.org/wiki/Dahab\" title=\"Dahab\">Dahab</a> kill 23 people and injure about 80.", "links": [{"title": "2006 Dahab bombings", "link": "https://wikipedia.org/wiki/2006_Dahab_bombings"}, {"title": "Resort town", "link": "https://wikipedia.org/wiki/Resort_town"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hab"}]}, {"year": "2011", "text": "WikiLeaks starts  publishing the Guantanamo Bay files leak.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a> starts publishing the <a href=\"https://wikipedia.org/wiki/Guantanamo_Bay_files_leak\" title=\"Guantanamo Bay files leak\">Guantanamo Bay files leak</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a> starts publishing the <a href=\"https://wikipedia.org/wiki/Guantanamo_Bay_files_leak\" title=\"Guantanamo Bay files leak\">Guantanamo Bay files leak</a>.", "links": [{"title": "WikiLeaks", "link": "https://wikipedia.org/wiki/WikiLeaks"}, {"title": "Guantanamo Bay files leak", "link": "https://wikipedia.org/wiki/Guantanamo_Bay_files_leak"}]}, {"year": "2013", "text": "A building collapses near Dhaka, Bangladesh, killing 1,134 people and injuring about 2,500 others.", "html": "2013 - A <a href=\"https://wikipedia.org/wiki/Rana_Plaza_collapse\" title=\"Rana Plaza collapse\">building collapses</a> near <a href=\"https://wikipedia.org/wiki/Dhaka\" title=\"Dhaka\">Dhaka</a>, Bangladesh, killing 1,134 people and injuring about 2,500 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Rana_Plaza_collapse\" title=\"Rana Plaza collapse\">building collapses</a> near <a href=\"https://wikipedia.org/wiki/Dhaka\" title=\"Dhaka\">Dhaka</a>, Bangladesh, killing 1,134 people and injuring about 2,500 others.", "links": [{"title": "Rana Plaza collapse", "link": "https://wikipedia.org/wiki/Rana_Plaza_collapse"}, {"title": "Dhaka", "link": "https://wikipedia.org/wiki/Dhaka"}]}, {"year": "2013", "text": "Violence in Bachu County, Kashgar Prefecture, of China's Xinjiang results in death of 21 people.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/April_2013_Bachu_unrest\" title=\"April 2013 Bachu unrest\">Violence</a> in <a href=\"https://wikipedia.org/wiki/Bachu_County\" class=\"mw-redirect\" title=\"Bachu County\">Bachu County</a>, <a href=\"https://wikipedia.org/wiki/Kashgar_Prefecture\" title=\"Kashgar Prefecture\">Kashgar Prefecture</a>, of <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>'s <a href=\"https://wikipedia.org/wiki/Xinjiang\" title=\"Xinjiang\">Xinjiang</a> results in death of 21 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/April_2013_Bachu_unrest\" title=\"April 2013 Bachu unrest\">Violence</a> in <a href=\"https://wikipedia.org/wiki/Bachu_County\" class=\"mw-redirect\" title=\"Bachu County\">Bachu County</a>, <a href=\"https://wikipedia.org/wiki/Kashgar_Prefecture\" title=\"Kashgar Prefecture\">Kashgar Prefecture</a>, of <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>'s <a href=\"https://wikipedia.org/wiki/Xinjiang\" title=\"Xinjiang\">Xinjiang</a> results in death of 21 people.", "links": [{"title": "April 2013 Bachu unrest", "link": "https://wikipedia.org/wiki/April_2013_Bachu_unrest"}, {"title": "Bachu County", "link": "https://wikipedia.org/wiki/Bachu_County"}, {"title": "Kashgar Prefecture", "link": "https://wikipedia.org/wiki/Kashgar_Prefecture"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "Xinjiang", "link": "https://wikipedia.org/wiki/Xinjiang"}]}], "Births": [{"year": "1086", "text": "<PERSON><PERSON> of Aragon (d. 1157)", "html": "1086 - <a href=\"https://wikipedia.org/wiki/Ramiro_II_of_Aragon\" title=\"<PERSON><PERSON> II of Aragon\"><PERSON><PERSON> II of Aragon</a> (d. 1157)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ramiro_II_of_Aragon\" title=\"<PERSON><PERSON> II of Aragon\"><PERSON><PERSON> II of Aragon</a> (d. 1157)", "links": [{"title": "Ramiro II of Aragon", "link": "https://wikipedia.org/wiki/Ramiro_II_of_Aragon"}]}, {"year": "1492", "text": "<PERSON><PERSON> of Bavaria, Bavarian duchess and noblewoman (d. 1564)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Bavaria,_Duchess_of_W%C3%BCrttemberg\" title=\"<PERSON><PERSON> of Bavaria, Duchess of Württemberg\"><PERSON><PERSON> of Bavaria</a>, Bavarian duchess and noblewoman (d. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Bavaria,_Duchess_of_W%C3%BCrttemberg\" title=\"<PERSON><PERSON> of Bavaria, Duchess of Württemberg\"><PERSON><PERSON> of Bavaria</a>, Bavarian duchess and noblewoman (d. 1564)", "links": [{"title": "<PERSON><PERSON> of Bavaria, Duchess of Württemberg", "link": "https://wikipedia.org/wiki/Sabina_of_Bavaria,_Duchess_of_W%C3%BCrttemberg"}]}, {"year": "1532", "text": "<PERSON>, English politician (d. 1600)", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1600)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1533", "text": "<PERSON> of Orange, founding father of the Netherlands (d. 1584)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Orange\" class=\"mw-redirect\" title=\"<PERSON> of Orange\"><PERSON> of Orange</a>, founding father of the Netherlands (d. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Orange\" class=\"mw-redirect\" title=\"<PERSON> of Orange\"><PERSON> of Orange</a>, founding father of the Netherlands (d. 1584)", "links": [{"title": "<PERSON> Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Orange"}]}, {"year": "1538", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Mantua (d. 1587)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Mantua\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Mantua\"><PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Mantua</a> (d. 1587)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Mantua\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Mantua\"><PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Mantua</a> (d. 1587)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Duke of Mantua", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Mantua"}]}, {"year": "1545", "text": "<PERSON>, 2nd Earl of Southampton, English Earl (d. 1581)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Southampton\" title=\"<PERSON>, 2nd Earl of Southampton\"><PERSON>, 2nd Earl of Southampton</a>, English Earl (d. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Southampton\" title=\"<PERSON>, 2nd Earl of Southampton\"><PERSON>, 2nd Earl <PERSON> Southampton</a>, English Earl (d. 1581)", "links": [{"title": "<PERSON>, 2nd Earl of Southampton", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Southampton"}]}, {"year": "1562", "text": "<PERSON>, Ming Dynasty Chinese politician, scholar and lay Catholic leader (d. 1633)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ming Dynasty Chinese politician, scholar and lay Catholic leader (d. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ming Dynasty Chinese politician, scholar and lay Catholic leader (d. 1633)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Xu_<PERSON>"}]}, {"year": "1581", "text": "<PERSON>, French priest and saint (d. 1660)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and saint (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and saint (d. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1608", "text": "<PERSON>, Duke of Orléans, third son of King <PERSON> of France (d. 1660)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Orl%C3%A9ans\" title=\"<PERSON>, Duke of Orléans\"><PERSON>, Duke of Orléans</a>, third son of King <PERSON> of France (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Orl%C3%A9ans\" title=\"<PERSON>, Duke of Orléans\"><PERSON>, Duke of Orléans</a>, third son of King <PERSON> of France (d. 1660)", "links": [{"title": "<PERSON>, Duke of Orléans", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_Orl%C3%A9ans"}]}, {"year": "1620", "text": "<PERSON>, English demographer and statistician (d. 1674)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English demographer and statistician (d. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>unt\"><PERSON></a>, English demographer and statistician (d. 1674)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1706", "text": "<PERSON>, Italian pianist and composer (d. 1780)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist and composer (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist and composer (d. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON> the Elder, Irish-English painter and educator (d. 1784)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Irish-English painter and educator (d. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Irish-English painter and educator (d. 1784)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder"}]}, {"year": "1743", "text": "<PERSON>, English clergyman and engineer, invented the power loom (d. 1823)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clergyman and engineer, invented the <a href=\"https://wikipedia.org/wiki/Power_loom\" title=\"Power loom\">power loom</a> (d. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clergyman and engineer, invented the <a href=\"https://wikipedia.org/wiki/Power_loom\" title=\"Power loom\">power loom</a> (d. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Power loom", "link": "https://wikipedia.org/wiki/Power_loom"}]}, {"year": "1784", "text": "<PERSON>, American lawyer and jurist (d. 1860)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, English novelist, essayist, and short story writer (d. 1882)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, essayist, and short story writer (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, essayist, and short story writer (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican politician, President of Mexico (d. 1889)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_<PERSON><PERSON>_de_Tejada\" title=\"<PERSON><PERSON><PERSON><PERSON>da\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican politician, <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_<PERSON><PERSON>_<PERSON>_Tejada\" title=\"<PERSON><PERSON><PERSON><PERSON>da\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican politician, <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1829", "text": "<PERSON><PERSON>, Austrian soprano, educator and essayist (d. 1919)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian soprano, educator and essayist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian soprano, educator and essayist (d. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1845", "text": "<PERSON>, Swiss poet and author, Nobel Prize laureate (d. 1924)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss poet and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss poet and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1856", "text": "<PERSON>, French general and politician, 119th Prime Minister of France (d. 1951)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tain\" title=\"<PERSON>\"><PERSON></a>, French general and politician, 119th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tain\" title=\"<PERSON>\"><PERSON></a>, French general and politician, 119th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Philippe_P%C3%A9tain"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1860", "text": "Queen <PERSON><PERSON>, last Queen of Tahiti (d. 1935)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON>\" title=\"Queen <PERSON><PERSON>\">Queen <PERSON><PERSON></a>, last Queen of <a href=\"https://wikipedia.org/wiki/Tahiti\" title=\"Tahiti\">Tahiti</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON>\" title=\"Queen <PERSON><PERSON>\">Queen <PERSON><PERSON></a>, last Queen of <a href=\"https://wikipedia.org/wiki/Tahiti\" title=\"Tahiti\">Tahiti</a> (d. 1935)", "links": [{"title": "Queen <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Tahiti", "link": "https://wikipedia.org/wiki/Tahiti"}]}, {"year": "1862", "text": "<PERSON><PERSON><PERSON>, Japanese botanist (d. 1957)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese botanist (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese botanist (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1868", "text": "<PERSON>, Scottish golfer (d. 1944)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish golfer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish golfer (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>d"}]}, {"year": "1876", "text": "<PERSON>, German admiral (d. 1960)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German admiral (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German admiral (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Swiss-French painter (d. 1958)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French painter (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French painter (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Hungarian-American circus performer (d. 1984)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-American circus performer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-American circus performer (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1880", "text": "<PERSON>, Swedish-American engineer and businessman, developed the zipper (d. 1954)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American engineer and businessman, developed the <a href=\"https://wikipedia.org/wiki/Zipper\" title=\"<PERSON><PERSON><PERSON>\">zipper</a> (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American engineer and businessman, developed the <a href=\"https://wikipedia.org/wiki/Zipper\" title=\"<PERSON>ipper\">zipper</a> (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>per"}]}, {"year": "1880", "text": "<PERSON>, Croatian entomologist (d. 1964)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BC<PERSON>_(entomologist)\" title=\"<PERSON> (entomologist)\"><PERSON></a>, Croatian entomologist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(entomologist)\" title=\"<PERSON> (entomologist)\"><PERSON></a>, Croatian entomologist (d. 1964)", "links": [{"title": "<PERSON> (entomologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BC<PERSON>_(entomologist)"}]}, {"year": "1882", "text": "<PERSON>, 1st Baron <PERSON>, Scottish-English air marshal (d. 1970)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish-English air marshal (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish-English air marshal (d. 1970)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American triple jumper (d. 1962)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American triple jumper (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American triple jumper (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Irish-Canadian hammer thrower and footballer (d. 1961)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian hammer thrower and footballer (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian hammer thrower and footballer (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, English hunter (d. 1931)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English hunter (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English hunter (d. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON>, Burma-based scholar and educator (d. 1973)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Pe_Maung_Tin\" title=\"Pe Maung Tin\"><PERSON>e Maung Tin</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a>-based scholar and educator (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pe_Maung_Tin\" title=\"Pe Maung Tin\"><PERSON><PERSON> Maung Tin</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a>-based scholar and educator (d. 1973)", "links": [{"title": "Pe Maung Tin", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}, {"year": "1889", "text": "<PERSON>, English academic and politician, Chancellor of the Exchequer (d. 1952)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ps"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian painter and academic (d. 1924)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Lyubov Popova\"><PERSON><PERSON><PERSON><PERSON></a>, Russian painter and academic (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ova\" title=\"Lyubov Popova\"><PERSON><PERSON><PERSON><PERSON></a>, Russian painter and academic (d. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lyubov_Popova"}]}, {"year": "1897", "text": "<PERSON>, Mexican colonel and politician, 45th President of Mexico (d. 1955)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81vila_Camacho\" title=\"<PERSON>ach<PERSON>\"><PERSON></a>, Mexican colonel and politician, 45th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81vila_Camacho\" title=\"<PERSON>\"><PERSON></a>, Mexican colonel and politician, 45th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81vila_Camacho"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1897", "text": "<PERSON>, American linguist, anthropologist, and engineer (d. 1941)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist, anthropologist, and engineer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist, anthropologist, and engineer (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Russian-American mathematician and academic (d. 1986)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American mathematician and academic (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American mathematician and academic (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, English author and educator (d. 1984)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Go<PERSON>\"><PERSON></a>, English author and educator (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Spanish lawyer and politician, founded the Falange (d. 1936)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and politician, founded the <a href=\"https://wikipedia.org/wiki/Falange_Espa%C3%B1ola_y_de_las_JONS\" class=\"mw-redirect\" title=\"Falange Española y de las JONS\">Falange</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and politician, founded the <a href=\"https://wikipedia.org/wiki/Falange_Espa%C3%B1ola_y_de_las_JONS\" class=\"mw-redirect\" title=\"Falange Española y de las JONS\">Falange</a> (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Falange Española y de las JONS", "link": "https://wikipedia.org/wiki/Falange_Espa%C3%B1ola_y_de_las_JONS"}]}, {"year": "1904", "text": "<PERSON>, Dutch-American painter and educator (d. 1997)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American painter and educator (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American painter and educator (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American long jumper (d. 1999)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American novelist, poet, and literary critic (d. 1989)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and literary critic (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and literary critic (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American-born Irish-British Nazi propaganda broadcaster (d. 1946)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-born Irish-British Nazi propaganda broadcaster (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-born Irish-British Nazi propaganda broadcaster (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English nurse (d. 1991)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Mexican cinematographer (d. 1997)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican cinematographer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican cinematographer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American actress (d. 2000)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Day\"><PERSON><PERSON></a>, American actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Day\"><PERSON><PERSON></a>, American actress (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Swedish runner (d. 1991)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Inga_Gentzel\" title=\"Inga Gentzel\"><PERSON><PERSON></a>, Swedish runner (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inga_Gentzel\" title=\"Inga Gentzel\"><PERSON><PERSON></a>, Swedish runner (d. 1991)", "links": [{"title": "Inga Gentzel", "link": "https://wikipedia.org/wiki/Inga_Gentzel"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Polish sculptor (d. 1963)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON><PERSON>%C5%82<PERSON><PERSON>_(sculptor)\" title=\"<PERSON><PERSON><PERSON> (sculptor)\"><PERSON><PERSON><PERSON></a>, Polish sculptor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON><PERSON>%C5%82<PERSON><PERSON>_(sculptor)\" title=\"<PERSON><PERSON><PERSON> (sculptor)\"><PERSON><PERSON><PERSON></a>, Polish sculptor (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON><PERSON>%C5%82<PERSON><PERSON>_(sculptor)"}]}, {"year": "1912", "text": "<PERSON>, American discus thrower (d. 1994)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, German-American scientist and engineer (d. 2014)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American scientist and engineer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American scientist and engineer (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American director, producer, and screenwriter (d. 1977)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/William_Castle\" title=\"William Castle\"><PERSON></a>, American director, producer, and screenwriter (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Castle\" title=\"William Castle\"><PERSON></a>, American director, producer, and screenwriter (d. 1977)", "links": [{"title": "William Castle", "link": "https://wikipedia.org/wiki/William_Castle"}]}, {"year": "1914", "text": "<PERSON>, Canadian ice hockey player and coach (d. 1991)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American chef and author (d. 2001)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chef)\" title=\"<PERSON> (chef)\"><PERSON></a>, American chef and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chef)\" title=\"<PERSON> (chef)\"><PERSON></a>, American chef and author (d. 2001)", "links": [{"title": "<PERSON> (chef)", "link": "https://wikipedia.org/wiki/<PERSON>_(chef)"}]}, {"year": "1916", "text": "<PERSON>, American wrestler and trainer (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American mathematician and academic (d. 2010)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON>, Cypriot lawyer and politician, 4th President of Cyprus (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Clerides\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Clerides\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Glafcos_Clerides"}, {"title": "President of Cyprus", "link": "https://wikipedia.org/wiki/President_of_Cyprus"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Italian race car driver (d. 2011)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Gino_Valenzano\" title=\"G<PERSON>\"><PERSON><PERSON></a>, Italian race car driver (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gino_Valenzano\" title=\"G<PERSON> Valenzano\"><PERSON><PERSON></a>, Italian race car driver (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gino_Valenzano"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian anthropologist and academic (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>remblay\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian anthropologist and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_Tremblay\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian anthropologist and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marc-Ad%C3%A9<PERSON>_Tremblay"}]}, {"year": "1923", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2005)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bo<PERSON>ar"}]}, {"year": "1923", "text": "<PERSON>, American author and illustrator (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Burn\"><PERSON></a>, American author and illustrator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Doris Burn\"><PERSON></a>, American author and illustrator (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, German-English radio host, academic, and politician (d. 2009)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Freud\"><PERSON></a>, German-English radio host, academic, and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Freud\"><PERSON></a>, German-English radio host, academic, and politician (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress and singer (d. 2002)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Italian sprinter (d. 1992)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Franco_Leccese\" title=\"<PERSON>\"><PERSON></a>, Italian sprinter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco_Leccese\" title=\"<PERSON>\"><PERSON></a>, Italian sprinter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_Leccese"}]}, {"year": "1926", "text": "<PERSON>, American actress", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Swedish farmer and politician, 27th Prime Minister of Sweden (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Thorbj%C3%B6rn_F%C3%A4lldin\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish farmer and politician, 27th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thorbj%C3%B6rn_F%C3%A4lldin\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish farmer and politician, 27th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thorbj%C3%B6rn_F%C3%A4lldin"}, {"title": "Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sweden"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Luxembourgish runner and politician, Luxembourgish Minister for Energy (d. 1992)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Luxembourgish runner and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_for_Energy_of_Luxembourg\" class=\"mw-redirect\" title=\"List of Ministers for Energy of Luxembourg\">Luxembourgish Minister for Energy</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Luxembourgish runner and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_for_Energy_of_Luxembourg\" class=\"mw-redirect\" title=\"List of Ministers for Energy of Luxembourg\">Luxembourgish Minister for Energy</a> (d. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Ministers for Energy of Luxembourg", "link": "https://wikipedia.org/wiki/List_of_Ministers_for_Energy_of_Luxembourg"}]}, {"year": "1928", "text": "<PERSON>, Scottish footballer and manager  (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American saxophonist (d. 2008)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Russian-born Armenian Iranologist (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-born Armenian Iranologist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-born Armenian Iranologist (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1929", "text": "Dr<PERSON>, Indian actor and singer (d. 2006)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Dr._<PERSON>\" title=\"Dr. <PERSON>\">Dr. <PERSON></a>, Indian actor and singer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dr._<PERSON>\" title=\"Dr. <PERSON>\">Dr. <PERSON></a>, Indian actor and singer (d. 2006)", "links": [{"title": "Dr. <PERSON>", "link": "https://wikipedia.org/wiki/Dr<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American instrument designer, educator, and author (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American instrument designer, educator, and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American instrument designer, educator, and author (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American actor, director, and producer (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Brazilian lawyer and politician, 31st President of Brazil", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>y"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Algerian footballer and manager (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian footballer and manager (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English painter and illustrator", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Indian journalist and author (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and author (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actress, singer, and dancer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Canadian educator and politician, 56th Mayor of Toronto", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 56th <a href=\"https://wikipedia.org/wiki/Mayor_of_Toronto\" title=\"Mayor of Toronto\">Mayor of Toronto</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 56th <a href=\"https://wikipedia.org/wiki/Mayor_of_Toronto\" title=\"Mayor of Toronto\">Mayor of Toronto</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mayor of Toronto", "link": "https://wikipedia.org/wiki/Mayor_of_Toronto"}]}, {"year": "1936", "text": "<PERSON>, English actress (d. 1990)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Jill_<PERSON>\" title=\"Jill <PERSON>\"><PERSON></a>, English actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jill_<PERSON>\" title=\"Jill Ireland\"><PERSON></a>, English actress (d. 1990)", "links": [{"title": "Jill <PERSON>", "link": "https://wikipedia.org/wiki/Jill_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American saxophonist and composer (d. 2001)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American author (d. 2017)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American journalist, banker, and diplomat, 22nd United States Ambassador to the United Nations (d. 2010)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, banker, and diplomat, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, banker, and diplomat, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations"}]}, {"year": "1941", "text": "<PERSON>, Australian-English guitarist and composer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, Australian-English guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, Australian-English guitarist and composer", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_(guitarist)"}]}, {"year": "1942", "text": "<PERSON>, American lawyer and politician, 54th Mayor of Chicago", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 54th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 54th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American singer, actress, activist, and producer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Barbra_Streisand\" title=\"Barbra Streisand\"><PERSON><PERSON></a>, American singer, actress, activist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barbra_Streisand\" title=\"Barbra Streisand\"><PERSON><PERSON></a>, American singer, actress, activist, and producer", "links": [{"title": "Barbra <PERSON>", "link": "https://wikipedia.org/wiki/Barbra_Streisand"}]}, {"year": "1943", "text": "<PERSON>, American country and gospel bass singer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country and gospel bass singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country and gospel bass singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English footballer (d. 2012)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Gordon_<PERSON>\" title=\"Gordon West\"><PERSON></a>, English footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gordon_West\" title=\"Gordon West\"><PERSON></a>, English footballer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gordon_West"}]}, {"year": "1944", "text": "<PERSON>, English judge", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English judge", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Estonian architect", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian architect", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}]}, {"year": "1944", "text": "<PERSON>, American record producer, musician and singer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, musician and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, musician and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American drummer and songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian lawyer and activist (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Canadian lawyer and activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Canadian lawyer and activist (d. 2013)", "links": [{"title": "<PERSON> (lawyer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)"}]}, {"year": "1946", "text": "<PERSON>, American hunter and television personality", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hunter and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hunter and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Spanish engineer and politician, 22nd President of the European Parliament", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish engineer and politician, 22nd <a href=\"https://wikipedia.org/wiki/President_of_the_European_Parliament\" title=\"President of the European Parliament\">President of the European Parliament</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish engineer and politician, 22nd <a href=\"https://wikipedia.org/wiki/President_of_the_European_Parliament\" title=\"President of the European Parliament\">President of the European Parliament</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of the European Parliament", "link": "https://wikipedia.org/wiki/President_of_the_European_Parliament"}]}, {"year": "1947", "text": "<PERSON>, Brazilian cardinal", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON><PERSON>_de_A<PERSON>z\" title=\"<PERSON>\"><PERSON></a>, Brazilian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Braz_de_Aviz\" title=\"João <PERSON>\"><PERSON></a>, Brazilian cardinal", "links": [{"title": "João <PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON><PERSON>_de_A<PERSON>z"}]}, {"year": "1947", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, <PERSON>, New Zealand-English lawyer and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, New Zealand-English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, New Zealand-English lawyer and politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1948", "text": "<PERSON>, American soldier and politician, 69th Governor of Massachusetts (d. 2013)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 69th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 69th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Ecuadorian-American psychiatrist, therapist, and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian-American psychiatrist, therapist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian-American psychiatrist, therapist, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American sprinter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, French singer-songwriter and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/V%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French singer-songwriter and producer", "links": [{"title": "Véronique <PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A9ronique_<PERSON>son"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and musician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yman"}]}, {"year": "1951", "text": "<PERSON>, Israeli architect and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(industrial_designer)\" title=\"<PERSON> (industrial designer)\"><PERSON></a>, Israeli architect and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(industrial_designer)\" title=\"<PERSON> (industrial designer)\"><PERSON></a>, Israeli architect and academic", "links": [{"title": "<PERSON> (industrial designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(industrial_designer)"}]}, {"year": "1951", "text": "<PERSON>, French author and poet (d. 2022)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bob<PERSON>\"><PERSON></a>, French author and poet (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English bass player and songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Irish educator and politician, 13th Taoiseach of Ireland", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish educator and politician, 13th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish educator and politician, 13th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoise<PERSON>\">Taoiseach of Ireland</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "1952", "text": "<PERSON>, French fashion designer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American film producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American film producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American film producer", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}]}, {"year": "1953", "text": "<PERSON>, American actor and writer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American journalist, activist, and convicted murderer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist, activist, and convicted murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist, activist, and convicted murderer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack Blades\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack Blades\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Blades"}]}, {"year": "1955", "text": "<PERSON><PERSON>, German politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, German politician", "links": [{"title": "<PERSON>-Merk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Me<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Dutch businessman, co-founded Endemol", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, Dutch businessman, co-founded <a href=\"https://wikipedia.org/wiki/Endemol\" title=\"Endemol\">Endemol</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Dutch businessman, co-founded <a href=\"https://wikipedia.org/wiki/Endemol\" title=\"Endemol\">Endemol</a>", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Endemol", "link": "https://wikipedia.org/wiki/Endemol"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Irish trade union leader and politician, 25th Tánaiste of Ireland", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish trade union leader and politician, 25th <a href=\"https://wikipedia.org/wiki/T%C3%A1naiste\" title=\"Tánaiste\">Tánaiste of Ireland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish trade union leader and politician, 25th <a href=\"https://wikipedia.org/wiki/T%C3%A1naiste\" title=\"Tánaiste\">Tánaiste of Ireland</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Tánaiste", "link": "https://wikipedia.org/wiki/T%C3%A1naiste"}]}, {"year": "1955", "text": "<PERSON>, British politician and criminal", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician and criminal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician and criminal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Belgian race car driver (d. 1992)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_N%C3%A8ve\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8ve\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guy_N%C3%A8ve"}]}, {"year": "1955", "text": "<PERSON>, American actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Kee<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Kee<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe"}]}, {"year": "1955", "text": "<PERSON>, New Zealand rugby player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Jr., American admiral", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American admiral", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American admiral", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1957", "text": "<PERSON><PERSON>, Baron <PERSON>, Pakistani-English businessman and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON>, Baron <PERSON></a>, Pakistani-English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON>, Baron <PERSON></a>, Pakistani-English businessman and politician", "links": [{"title": "<PERSON><PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English police officer and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON></a>, English police officer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON></a>, English police officer and politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, British-Australian television host and author (d. 2000)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Australian television host and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Australian television host and author (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English physician and politician, Minister for International Security Strategy", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_International_Security_Strategy\" title=\"Minister for International Security Strategy\">Minister for International Security Strategy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_International_Security_Strategy\" title=\"Minister for International Security Strategy\">Minister for International Security Strategy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for International Security Strategy", "link": "https://wikipedia.org/wiki/Minister_for_International_Security_Strategy"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, German politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Binninger\" title=\"<PERSON><PERSON><PERSON> Binninger\"><PERSON><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Binninger\" title=\"<PERSON><PERSON><PERSON> Binninger\"><PERSON><PERSON><PERSON></a>, German politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Binninger"}]}, {"year": "1962", "text": "<PERSON>, English footballer, coach, and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian rugby league player, coach, and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American bass player, songwriter, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, French singer-songwriter, guitarist, and producer (d. 2010)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Solo\" title=\"Mano Solo\"><PERSON><PERSON></a>, French singer-songwriter, guitarist, and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Mano Solo\"><PERSON><PERSON></a>, French singer-songwriter, guitarist, and producer (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mano_Solo"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, German sprinter (d. 2013)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sprinter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German sprinter (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON> the Entertainer, American comedian, actor, and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Entertainer\" title=\"<PERSON><PERSON> the Entertainer\"><PERSON><PERSON> the Entertainer</a>, American comedian, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Entertainer\" title=\"<PERSON><PERSON> the Entertainer\"><PERSON><PERSON> the Entertainer</a>, American comedian, actor, and producer", "links": [{"title": "<PERSON><PERSON> the Entertainer", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_the_Entertainer"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Beninese-American actor and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Beninese-American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Beninese-American actor and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Polish guitarist, composer, and educator", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Witold_Smorawi%C5%84ski\" title=\"Wito<PERSON>\">W<PERSON><PERSON></a>, Polish guitarist, composer, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Witold_Smorawi%C5%84ski\" title=\"Wito<PERSON>\">W<PERSON><PERSON></a>, Polish guitarist, composer, and educator", "links": [{"title": "<PERSON><PERSON><PERSON>aw<PERSON>", "link": "https://wikipedia.org/wiki/Witold_Smorawi%C5%84ski"}]}, {"year": "1965", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1965)\" title=\"<PERSON> (ice hockey, born 1965)\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1965)\" title=\"<PERSON> (ice hockey, born 1965)\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON> (ice hockey, born 1965)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1965)"}]}, {"year": "1966", "text": "<PERSON>, Canadian comedian and actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Italian footballer, coach, and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alessandro_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English-Canadian singer-songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Croatian basketball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%91a\" title=\"<PERSON>\"><PERSON></a>, Croatian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%91a\" title=\"<PERSON>\"><PERSON></a>, Croatian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ra%C4%91a"}]}, {"year": "1967", "text": "<PERSON>, Venezuelan-American baseball player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Irish actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American baseball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, English composer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Kosovan soldier and politician, 5th Prime Minister of Kosovo", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tha%C3%A7i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kosovan soldier and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kosovo\" title=\"Prime Minister of Kosovo\">Prime Minister of Kosovo</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tha%C3%A7i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kosovan soldier and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kosovo\" title=\"Prime Minister of Kosovo\">Prime Minister of Kosovo</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hashim_Tha%C3%A7i"}, {"title": "Prime Minister of Kosovo", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Kosovo"}]}, {"year": "1969", "text": "<PERSON>, Greek footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>mat<PERSON>dis"}]}, {"year": "1969", "text": "<PERSON>, Scottish actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Scottish academic and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish academic and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>ford"}]}, {"year": "1970", "text": "<PERSON>, Australian cricketer, coach, and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Sri Lankan cricketer and umpire", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan cricketer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Belgian singer-songwriter and guitarist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Scottish footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Slovenian skier and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>re_Ko%C5%A1ir\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian skier and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ko%C5%A1ir\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian skier and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jure_Ko%C5%A1ir"}]}, {"year": "1973", "text": "<PERSON><PERSON>, English gymnast, television and radio host", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English gymnast, television and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English gymnast, television and radio host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American screenwriter and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American bass player and songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American basketball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ver"}]}, {"year": "1973", "text": "<PERSON>, English golfer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English illustrator", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stephen <PERSON>\"><PERSON></a>, English illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stephen <PERSON>\"><PERSON></a>, English illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Norwegian politician", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mj%C3%B8<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>j%C3%B8s_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marte_Mj%C3%B8s_Persen"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Yugoslavian and Serbian water polo player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yugoslavian and Serbian water polo player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yugoslavian and Serbian water polo player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/De<PERSON>_<PERSON>%C4%87"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Norwegian skier", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Irish international footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish international footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian tennis player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian tennis player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Puerto Rican-American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Beltr%C3%A1n"}]}, {"year": "1977", "text": "<PERSON>, Argentine footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego Placente\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_<PERSON>nte"}]}, {"year": "1978", "text": "<PERSON>, Argentine footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego <PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Mexican footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Armenian chess player (d. 2008)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian chess player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian chess player (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Japanese swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American singer-songwriter, talk show host", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American hurdler", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hurdler)\" title=\"<PERSON> (hurdler)\"><PERSON></a>, American hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hurdler)\" title=\"<PERSON> (hurdler)\"><PERSON></a>, American hurdler", "links": [{"title": "<PERSON> (hurdler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hurdler)"}]}, {"year": "1982", "text": "<PERSON>, German volleyball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German volleyball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Ukrainian heptathlete", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian heptathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American sprinter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Estonian cyclist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rein_Taaram%C3%A4e"}]}, {"year": "1987", "text": "<PERSON>, Belgian international footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian international footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Indian actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>aru<PERSON>_<PERSON>\" title=\"<PERSON>aru<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>aru<PERSON>_<PERSON>\" title=\"<PERSON>aru<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Varu<PERSON>_<PERSON>an"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Latvian basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/El%C4%ABna_Babkina\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El%C4%ABna_Babkina\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/El%C4%ABna_Babkina"}]}, {"year": "1989", "text": "<PERSON>, American diver", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Slovenian tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Di%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Di%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hor%C4%8Di%C4%8D"}]}, {"year": "1990", "text": "<PERSON>, South Korean actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>i"}]}, {"year": "1990", "text": "<PERSON>, Czech basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vesel%C3%BD"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, French-Swedish model", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, French-Swedish model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, French-Swedish model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, French figure skater", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Morgan_Cipr%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morgan_Cipr%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Morgan_Cipr%C3%A8s"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English cyclist", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Welsh international footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1993)\" title=\"<PERSON> (footballer, born 1993)\"><PERSON></a>, Welsh international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1993)\" title=\"<PERSON> (footballer, born 1993)\"><PERSON></a>, Welsh international footballer", "links": [{"title": "<PERSON> (footballer, born 1993)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1993)"}]}, {"year": "1994", "text": "<PERSON>, American singer, dancer, and actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, dancer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, dancer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Fisher"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, British-South African YouTuber", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, British-South African YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, British-South African YouTuber", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Australian tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Ashleigh_Barty\" title=\"Ashleigh Barty\"><PERSON><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashleigh_Barty\" title=\"Ashleigh Barty\"><PERSON><PERSON></a>, Australian tennis player", "links": [{"title": "Ashleigh Barty", "link": "https://wikipedia.org/wiki/Ashleigh_Barty"}]}, {"year": "1997", "text": "<PERSON>, New Zealand golfer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Russian tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" class=\"mw-redirect\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" class=\"mw-redirect\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Australian tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "624", "text": "<PERSON><PERSON><PERSON>, saint and archbishop of Canterbury", "html": "624 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, saint and <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">archbishop of Canterbury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, saint and <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">archbishop of Canterbury</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>litus"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}]}, {"year": "1149", "text": "<PERSON><PERSON><PERSON> Chemillé, abbess of Fontevrault", "html": "1149 - <a href=\"https://wikipedia.org/wiki/Petronille_de_Chemill%C3%A9\" title=\"Petronille de Chemillé\"><PERSON><PERSON><PERSON> de Chemillé</a>, abbess of <a href=\"https://wikipedia.org/wiki/Fontevrault\" class=\"mw-redirect\" title=\"Fontevrault\">Fontevrault</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petronille_de_Chemill%C3%A9\" title=\"Petronille de Chemillé\"><PERSON><PERSON><PERSON> de Chemillé</a>, abbess of <a href=\"https://wikipedia.org/wiki/Fontevrault\" class=\"mw-redirect\" title=\"Fontevrault\">Fontevrault</a>", "links": [{"title": "Petronille de Chemillé", "link": "https://wikipedia.org/wiki/Petronille_de_Chemill%C3%A9"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>v<PERSON><PERSON>"}]}, {"year": "1288", "text": "<PERSON> of Austria (b. 1226)", "html": "1288 - <a href=\"https://wikipedia.org/wiki/Gertrude_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (b. 1226)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gertrude_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (b. 1226)", "links": [{"title": "Gertrude of Austria", "link": "https://wikipedia.org/wiki/Gertrude_of_Austria"}]}, {"year": "1338", "text": "<PERSON>, Marquess of Montferrat (b. 1291)", "html": "1338 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquess_of_Montferrat\" class=\"mw-redirect\" title=\"<PERSON>, Marquess of Montferrat\"><PERSON>, Marquess of Montferrat</a> (b. 1291)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquess_of_Montferrat\" class=\"mw-redirect\" title=\"<PERSON>, Marquess of Montferrat\"><PERSON>, Marquess of Montferrat</a> (b. 1291)", "links": [{"title": "<PERSON>, Marquess of Montferrat", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquess_of_Montferrat"}]}, {"year": "1479", "text": "<PERSON>, Spanish poet (b. 1440)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet (b. 1440)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet (b. 1440)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1513", "text": "<PERSON><PERSON><PERSON><PERSON>, Ottoman prince (b. 1465)", "html": "1513 - <a href=\"https://wikipedia.org/wiki/%C5%9Eehzade_Ahmet\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ottoman prince (b. 1465)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%9E<PERSON>zad<PERSON>_<PERSON>met\" class=\"mw-redirect\" title=\"Şehzade Ahmet\"><PERSON><PERSON><PERSON><PERSON></a>, Ottoman prince (b. 1465)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%9<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1617", "text": "<PERSON><PERSON><PERSON>, Italian-French politician, Prime Minister of France (b. 1575)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1575)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1622", "text": "<PERSON><PERSON><PERSON> of Sigmaringen, German friar and saint (b. 1577)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>del<PERSON>_of_Sigmaringen\" title=\"<PERSON><PERSON><PERSON> of Sigmaringen\"><PERSON><PERSON><PERSON> of Sigmaringen</a>, German friar and saint (b. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Sigmaringen\" title=\"<PERSON><PERSON><PERSON> of Sigmaringen\"><PERSON><PERSON><PERSON> of Sigmaringen</a>, German friar and saint (b. 1577)", "links": [{"title": "<PERSON><PERSON><PERSON> of Sigmaringen", "link": "https://wikipedia.org/wiki/Fidel<PERSON>_<PERSON>_Sigma<PERSON>en"}]}, {"year": "1656", "text": "<PERSON>, Danish mathematician and physicist (b. 1561)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mathematician and physicist (b. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mathematician and physicist (b. 1561)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1692", "text": "<PERSON>, Swiss vicar (b. 1633)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss vicar (b. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss vicar (b. 1633)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1731", "text": "<PERSON>, English journalist, novelist, and spy (b. 1660)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, novelist, and spy (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, novelist, and spy (b. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON>, German-Estonian clergyman and translator (b. 1683)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Estonian clergyman and translator (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Estonian clergyman and translator (b. 1683)", "links": [{"title": "<PERSON> thor <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON><PERSON><PERSON>, American minister and academic, founded Dartmouth College (b. 1711)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American minister and academic, founded <a href=\"https://wikipedia.org/wiki/Dartmouth_College\" title=\"Dartmouth College\">Dartmouth College</a> (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American minister and academic, founded <a href=\"https://wikipedia.org/wiki/Dartmouth_College\" title=\"Dartmouth College\">Dartmouth College</a> (b. 1711)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Dartmouth College", "link": "https://wikipedia.org/wiki/Dartmouth_College"}]}, {"year": "1794", "text": "<PERSON> the Elder, Swedish field marshal and politician (b. 1719)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Swedish field marshal and politician (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Swedish field marshal and politician (b. 1719)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_the_Elder"}]}, {"year": "1852", "text": "<PERSON><PERSON>, Russian poet and translator (b. 1783)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and translator (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and translator (b. 1783)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, French author (b. 1796)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author (b. 1796)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON> the Elder, German field marshal (b. 1800)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON><PERSON> the Elder\"><PERSON><PERSON> the Elder</a>, German field marshal (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON><PERSON> <PERSON> the <PERSON>\"><PERSON><PERSON> <PERSON> the Elder</a>, German field marshal (b. 1800)", "links": [{"title": "<PERSON><PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_the_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American psychologist and academic (b. 1844)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"G. <PERSON>\"><PERSON><PERSON></a>, American psychologist and academic (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"G. <PERSON>\"><PERSON><PERSON></a>, American psychologist and academic (b. 1844)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Georgian author and playwright (b. 1862)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian author and playwright (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian author and playwright (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek general (b. 1857)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek general (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek general (b. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anasta<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1938", "text": "<PERSON>, American sculptor (b. 1863)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, French cyclist (b. 1881)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Swedish author and poet (b. 1900)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish author and poet (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish author and poet (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian author (b. 1874)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American magician (b. 1888)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(magician)\" title=\"<PERSON> (magician)\"><PERSON></a>, American magician (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(magician)\" title=\"<PERSON> (magician)\"><PERSON></a>, American magician (b. 1888)", "links": [{"title": "<PERSON> (magician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(magician)"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, German physician (b. 1899)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, German SS officer (b. 1902)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American novelist, short story writer, and poet (b. 1873)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist, short story writer, and poet (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist, short story writer, and poet (b. 1873)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian composer (b. 1863)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/J%C4%81zeps_V%C4%ABtols\" title=\"<PERSON><PERSON><PERSON><PERSON> Vīto<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian composer (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C4%81zeps_V%C4%ABtols\" title=\"<PERSON><PERSON><PERSON><PERSON> Vītols\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian composer (b. 1863)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C4%81zeps_V%C4%ABtols"}]}, {"year": "1954", "text": "<PERSON>, French racing driver (b. 1910)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (b. 1879)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1961", "text": "<PERSON>, American actor, director and screenwriter (b. 1888)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director and screenwriter (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director and screenwriter (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American composer (b. 1897)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German pathologist and bacteriologist (b. 1895)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pathologist and bacteriologist (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pathologist and bacteriologist (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actress (b. 1878)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Georgian poet and author (b. 1902)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian poet and author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian poet and author (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Russian pilot, engineer, and cosmonaut (b. 1927)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pilot, engineer, and cosmonaut (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pilot, engineer, and cosmonaut (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian politician, 32nd Premier of South Australia (b. 1885)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1885)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1968", "text": "<PERSON>, American athlete (b. 1876)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American athlete (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American athlete (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer and pianist (b. 1930)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>nn\"><PERSON></a>, American singer and pianist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Spann\"><PERSON></a>, American singer and pianist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nn"}]}, {"year": "1972", "text": "<PERSON>, Filipino painter (b. 1892)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino painter (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino painter (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American comedian and producer (b. 1895)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and producer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and producer (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American-Swiss painter and educator (b. 1890)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss painter and educator (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss painter and educator (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Swiss-Cuban musicologist and author (b. 1904)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss-Cuban musicologist and author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss-Cuban musicologist and author (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alejo_Carpentier"}]}, {"year": "1982", "text": "<PERSON>, Finnish runner (b. 1896)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Ville_Ritola\" title=\"Ville Ritola\"><PERSON></a>, Finnish runner (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ville_Ritola\" title=\"Ville Ritola\"><PERSON></a>, Finnish runner (b. 1896)", "links": [{"title": "Ville Ritola", "link": "https://wikipedia.org/wiki/Ville_Ritola"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Turkish sociologist, psychologist, and academic (b. 1938)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Erol_G%C3%BCng%C3%B6r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish sociologist, psychologist, and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erol_G%C3%BCng%C3%B6r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish sociologist, psychologist, and academic (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erol_G%C3%BCng%C3%B6r"}]}, {"year": "1983", "text": "<PERSON>, German racing driver (b. 1943)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Spanish author (b. 1891)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez_y_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Spanish author (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez_y_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Spanish author (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_P%C3%A9rez_y_P%C3%A9rez"}]}, {"year": "1986", "text": "<PERSON>, American socialite, Duchess of Windsor (b. 1896)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite, Duchess of Windsor (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite, Duchess of Windsor (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, South African lawyer and activist (b. 1917)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and activist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and activist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Vietnamese philosopher and theorist (b. 1917)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Tran <PERSON>\"><PERSON><PERSON> <PERSON></a>, Vietnamese philosopher and theorist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Vietnamese philosopher and theorist (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch painter (b. 1903)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Lodewijk Bruckman\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Lodewi<PERSON> Bruckman\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1903)", "links": [{"title": "Lodewijk Bruckman", "link": "https://wikipedia.org/wiki/<PERSON>dewijk_Bruckman"}]}, {"year": "1997", "text": "<PERSON>, American director and producer (b. 1941)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American comedian and activist (b. 1927)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and activist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and activist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American engineer, designed the AR-15 rifle (b. 1922)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, designed the <a href=\"https://wikipedia.org/wiki/ArmaLite_AR-15\" title=\"ArmaLite AR-15\">AR-15 rifle</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, designed the <a href=\"https://wikipedia.org/wiki/ArmaLite_AR-15\" title=\"ArmaLite AR-15\">AR-15 rifle</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "ArmaLite AR-15", "link": "https://wikipedia.org/wiki/ArmaLite_AR-15"}]}, {"year": "2001", "text": "<PERSON>, German racing driver (b. 1914)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, German racing driver (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, German racing driver (b. 1914)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "2001", "text": "<PERSON>, American wrestler (b. 1928)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Valentine\"><PERSON></a>, American wrestler (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Luxembourgish sculptor (b. 1908)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgish sculptor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgish sculptor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish astronomer and mathematician (b. 1910)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/N%C3%BCzhet_G%C3%B6kdo%C4%9Fan\" title=\"Nüzhe<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish astronomer and mathematician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%BCzhet_G%C3%B6kdo%C4%9Fan\" title=\"Nüzhe<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish astronomer and mathematician (b. 1910)", "links": [{"title": "Nüzhet Gökdoğan", "link": "https://wikipedia.org/wiki/N%C3%BCzhet_G%C3%B6kdo%C4%9Fan"}]}, {"year": "2004", "text": "<PERSON>, French-Swiss director and producer (b. 1923)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss director and producer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss director and producer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, American businesswoman, co-founded Estée Lauder Companies (b. 1906)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Est%C3%A<PERSON><PERSON>_<PERSON><PERSON>_(businesswoman)\" title=\"<PERSON><PERSON><PERSON> (businesswoman)\"><PERSON><PERSON><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Est%C3%A9e_Lauder_Companies\" class=\"mw-redirect\" title=\"Estée Lauder Companies\">Estée Lauder Companies</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Est%C3%A<PERSON><PERSON>_<PERSON><PERSON>_(businesswoman)\" title=\"<PERSON><PERSON><PERSON> (businesswoman)\"><PERSON><PERSON><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Est%C3%A9e_Lauder_Companies\" class=\"mw-redirect\" title=\"Estée Lauder Companies\">Estée Lauder Companies</a> (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON> (businesswoman)", "link": "https://wikipedia.org/wiki/Est%C3%A<PERSON><PERSON>_<PERSON><PERSON>_(businesswoman)"}, {"title": "Estée Lauder Companies", "link": "https://wikipedia.org/wiki/Est%C3%A9e_Lauder_Companies"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Israeli general and politician, 7th President of Israel (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Israel", "link": "https://wikipedia.org/wiki/President_of_Israel"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Chinese sociologist and academic (b. 1910)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese sociologist and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese sociologist and academic (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tong"}]}, {"year": "2006", "text": "<PERSON>, English footballer (b. 1940)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Romanian-American rabbi and author (b. 1914)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Satmar)\" title=\"<PERSON><PERSON> (Satmar)\"><PERSON><PERSON></a>, Romanian-American rabbi and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Satmar)\" title=\"<PERSON><PERSON> (Satmar)\"><PERSON><PERSON></a>, Romanian-American rabbi and author (b. 1914)", "links": [{"title": "<PERSON><PERSON> (Satmar)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Satmar)"}]}, {"year": "2008", "text": "<PERSON>, American clarinet player, and saxophonist, and composer (b. 1921)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player, and saxophonist, and composer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player, and saxophonist, and composer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Indian guru and philanthropist (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian guru and philanthropist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian guru and philanthropist (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Austrian architect, designed Haas House (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian architect, designed <a href=\"https://wikipedia.org/wiki/Haas_<PERSON>\" title=\"Haas House\">Haas House</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian architect, designed <a href=\"https://wikipedia.org/wiki/Haas_House\" title=\"Haas House\">Haas House</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Haas House", "link": "https://wikipedia.org/wiki/Haas_House"}]}, {"year": "2014", "text": "<PERSON>, Scottish footballer and manager (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> J<PERSON>\"><PERSON></a>, Scottish footballer and manager (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> J<PERSON>\"><PERSON></a>, Scottish footballer and manager (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sandy_<PERSON>ine"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Indian politician (b. 1968)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Polish poet and playwright (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Tadeusz_R%C3%B3%C5%BCewicz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and playwright (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tadeusz_R%C3%B3%C5%BC<PERSON>icz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and playwright (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadeusz_R%C3%B3%C5%BCewicz"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish journalist and politician, Polish Minister of Foreign Affairs (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)\" title=\"Ministry of Foreign Affairs (Poland)\">Polish Minister of Foreign Affairs</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)\" title=\"Ministry of Foreign Affairs (Poland)\">Polish Minister of Foreign Affairs</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON>"}, {"title": "Ministry of Foreign Affairs (Poland)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)"}]}, {"year": "2016", "text": "<PERSON>, American weightlifter and coach (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weightlifter and coach (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weightlifter and coach (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American author and philosopher (b. 1928)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and philosopher (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and philosopher (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American saxophonist (b. 1950)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Canadian sports announcer (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, Canadian sports announcer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, Canadian sports announcer (b. 1933)", "links": [{"title": "<PERSON> (sportscaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)"}]}, {"year": "2024", "text": "<PERSON>, Australian rugby league player (b. 1972)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American politician (b. 1958)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American politician (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American politician (b. 1958)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}, {"year": "2024", "text": "<PERSON>, British musician (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British musician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British musician (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}