{"date": "September 11", "url": "https://wikipedia.org/wiki/September_11", "data": {"Events": [{"year": "9", "text": "The Battle of the Teutoburg Forest ends: The Roman Empire suffers the greatest defeat of its history and the Rhine is established as the border between the Empire and the so-called barbarians for the next four hundred years.", "html": "9 - The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Teutoburg_Forest\" title=\"Battle of the Teutoburg Forest\">Battle of the Teutoburg Forest</a> ends: The <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a> suffers the greatest defeat of its history and the <a href=\"https://wikipedia.org/wiki/Rhine\" title=\"Rhine\">Rhine</a> is established as the border between the Empire and the so-called <a href=\"https://wikipedia.org/wiki/Germanic_peoples\" title=\"Germanic peoples\">barbarians</a> for the next four hundred years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Teutoburg_Forest\" title=\"Battle of the Teutoburg Forest\">Battle of the Teutoburg Forest</a> ends: The <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a> suffers the greatest defeat of its history and the <a href=\"https://wikipedia.org/wiki/Rhine\" title=\"Rhine\">Rhine</a> is established as the border between the Empire and the so-called <a href=\"https://wikipedia.org/wiki/Germanic_peoples\" title=\"Germanic peoples\">barbarians</a> for the next four hundred years.", "links": [{"title": "Battle of the Teutoburg Forest", "link": "https://wikipedia.org/wiki/Battle_of_the_Teutoburg_Forest"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}, {"title": "Rhine", "link": "https://wikipedia.org/wiki/Rhine"}, {"title": "Germanic peoples", "link": "https://wikipedia.org/wiki/Germanic_peoples"}]}, {"year": "1185", "text": "<PERSON> II <PERSON> kills <PERSON> and then appeals to the people, resulting in the revolt that deposes Andronikos <PERSON> and places <PERSON> on the throne of the Byzantine Empire.", "html": "1185 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> kills <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and then appeals to the people, resulting in the revolt that deposes <a href=\"https://wikipedia.org/wiki/Andronikos_I_Komnenos\" title=\"Andronikos I Komnenos\">Andronikos I Komnenos</a> and places <PERSON> on the throne of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> II <PERSON>\"><PERSON></a> kills <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and then appeals to the people, resulting in the revolt that deposes <a href=\"https://wikipedia.org/wiki/Andronikos_I_Komnenos\" title=\"Andronikos I Komnenos\">Andronikos I Komnenos</a> and places <PERSON> on the throne of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Andronikos I Komnenos", "link": "https://wikipedia.org/wiki/Andronikos_I_Komnenos"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "1275", "text": "An earthquake occurred in the south of Great Britain, notably causing multiple fatalities as well as destroying St Michael's Church on Glastonbury Tor.", "html": "1275 - <a href=\"https://wikipedia.org/wiki/1275_British_earthquake\" title=\"1275 British earthquake\">An earthquake</a> occurred in the south of Great Britain, notably causing multiple fatalities as well as destroying St Michael's Church on <a href=\"https://wikipedia.org/wiki/Glastonbury_Tor\" title=\"Glastonbury Tor\">Glastonbury Tor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1275_British_earthquake\" title=\"1275 British earthquake\">An earthquake</a> occurred in the south of Great Britain, notably causing multiple fatalities as well as destroying St Michael's Church on <a href=\"https://wikipedia.org/wiki/Glastonbury_Tor\" title=\"Glastonbury Tor\">Glastonbury Tor</a>.", "links": [{"title": "1275 British earthquake", "link": "https://wikipedia.org/wiki/1275_British_earthquake"}, {"title": "Glastonbury Tor", "link": "https://wikipedia.org/wiki/Glastonbury_Tor"}]}, {"year": "1297", "text": "Battle of Stirling Bridge: Scots jointly led by <PERSON> and <PERSON> defeat the English.", "html": "1297 - <a href=\"https://wikipedia.org/wiki/Battle_of_Stirling_Bridge\" title=\"Battle of Stirling Bridge\">Battle of Stirling Bridge</a>: Scots jointly led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat the English.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Stirling_Bridge\" title=\"Battle of Stirling Bridge\">Battle of Stirling Bridge</a>: Scots jointly led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat the English.", "links": [{"title": "Battle of Stirling Bridge", "link": "https://wikipedia.org/wiki/Battle_of_Stirling_Bridge"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1390", "text": "Lithuanian Civil War (1389-92): The Teutonic Knights begin a five-week siege of Vilnius.", "html": "1390 - <a href=\"https://wikipedia.org/wiki/Lithuanian_Civil_War_(1389%E2%80%9392)\" class=\"mw-redirect\" title=\"Lithuanian Civil War (1389-92)\">Lithuanian Civil War (1389-92)</a>: The <a href=\"https://wikipedia.org/wiki/Teutonic_Knights\" class=\"mw-redirect\" title=\"Teutonic Knights\">Teutonic Knights</a> begin a five-week siege of <a href=\"https://wikipedia.org/wiki/Vilnius\" title=\"Vilnius\">Vilnius</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lithuanian_Civil_War_(1389%E2%80%9392)\" class=\"mw-redirect\" title=\"Lithuanian Civil War (1389-92)\">Lithuanian Civil War (1389-92)</a>: The <a href=\"https://wikipedia.org/wiki/Teutonic_Knights\" class=\"mw-redirect\" title=\"Teutonic Knights\">Teutonic Knights</a> begin a five-week siege of <a href=\"https://wikipedia.org/wiki/Vilnius\" title=\"Vilnius\">Vilnius</a>.", "links": [{"title": "Lithuanian Civil War (1389-92)", "link": "https://wikipedia.org/wiki/Lithuanian_Civil_War_(1389%E2%80%9392)"}, {"title": "Teutonic Knights", "link": "https://wikipedia.org/wiki/Teutonic_Knights"}, {"title": "Vilnius", "link": "https://wikipedia.org/wiki/Vilnius"}]}, {"year": "1541", "text": "Santiago, Chile, is attacked by indigenous warriors, led by <PERSON><PERSON><PERSON><PERSON><PERSON>, to free eight indigenous chiefs held captive by the Spaniards.", "html": "1541 - <a href=\"https://wikipedia.org/wiki/Santiago\" title=\"Santiago\">Santiago</a>, Chile, is attacked by indigenous warriors, led by <a href=\"https://wikipedia.org/wiki/Michimalonco\" title=\"Michimalonco\">Michimalonco</a>, to free eight indigenous chiefs held captive by the Spaniards.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago\" title=\"Santiago\">Santiago</a>, Chile, is attacked by indigenous warriors, led by <a href=\"https://wikipedia.org/wiki/Michimalonco\" title=\"Michimalonco\">Michimalonco</a>, to free eight indigenous chiefs held captive by the Spaniards.", "links": [{"title": "Santiago", "link": "https://wikipedia.org/wiki/Santiago"}, {"title": "Michimalonco", "link": "https://wikipedia.org/wiki/Michimalonco"}]}, {"year": "1565", "text": "Ottoman forces retreat from Malta ending the Great Siege of Malta.", "html": "1565 - <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> forces retreat from <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> ending the <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Malta\" title=\"Great Siege of Malta\">Great Siege of Malta</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> forces retreat from <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> ending the <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Malta\" title=\"Great Siege of Malta\">Great Siege of Malta</a>.", "links": [{"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}, {"title": "Great Siege of Malta", "link": "https://wikipedia.org/wiki/Great_Siege_of_Malta"}]}, {"year": "1609", "text": "<PERSON> arrives on Manhattan Island and meets the indigenous people living there.", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Henry Hudson\"><PERSON></a> arrives on <a href=\"https://wikipedia.org/wiki/Manhattan_Island\" class=\"mw-redirect\" title=\"Manhattan Island\">Manhattan Island</a> and meets the indigenous people living there.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Henry Hudson\"><PERSON></a> arrives on <a href=\"https://wikipedia.org/wiki/Manhattan_Island\" class=\"mw-redirect\" title=\"Manhattan Island\">Manhattan Island</a> and meets the indigenous people living there.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Manhattan Island", "link": "https://wikipedia.org/wiki/Manhattan_Island"}]}, {"year": "1649", "text": "Siege of Drogheda ends: <PERSON>'s Parliamentarian troops take the town and execute its garrison.", "html": "1649 - <a href=\"https://wikipedia.org/wiki/Siege_of_Drogheda\" title=\"Siege of Drogheda\">Siege of Drogheda</a> ends: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Roundhead\" title=\"Roundhead\">Parliamentarian</a> troops take the town and execute its garrison.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Drogheda\" title=\"Siege of Drogheda\">Siege of Drogheda</a> ends: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Roundhead\" title=\"Roundhead\">Parliamentarian</a> troops take the town and execute its garrison.", "links": [{"title": "Siege of Drogheda", "link": "https://wikipedia.org/wiki/Siege_of_Drogheda"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Roundhead", "link": "https://wikipedia.org/wiki/Roundhead"}]}, {"year": "1683", "text": "Coalition forces, including the famous winged Hussars, led by Polish King <PERSON> lift the siege laid by Ottoman forces ahead of the Battle of Vienna.", "html": "1683 - Coalition forces, including the famous <a href=\"https://wikipedia.org/wiki/Winged_Hussars\" class=\"mw-redirect\" title=\"Winged Hussars\">winged Hussars</a>, led by Polish King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lift the siege laid by Ottoman forces ahead of the <a href=\"https://wikipedia.org/wiki/Battle_of_Vienna\" title=\"Battle of Vienna\">Battle of Vienna</a>.", "no_year_html": "Coalition forces, including the famous <a href=\"https://wikipedia.org/wiki/Winged_Hussars\" class=\"mw-redirect\" title=\"Winged Hussars\">winged Hussars</a>, led by Polish King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lift the siege laid by Ottoman forces ahead of the <a href=\"https://wikipedia.org/wiki/Battle_of_Vienna\" title=\"Battle of Vienna\">Battle of Vienna</a>.", "links": [{"title": "Winged <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Winged_Hussars"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Vienna", "link": "https://wikipedia.org/wiki/Battle_of_Vienna"}]}, {"year": "1697", "text": "Battle of Zenta: a major engagement in the Great Turkish War (1683-1699) and one of the most decisive defeats in Ottoman history.", "html": "1697 - <a href=\"https://wikipedia.org/wiki/Battle_of_Zenta\" title=\"Battle of Zenta\">Battle of Zenta</a>: a major engagement in the <a href=\"https://wikipedia.org/wiki/Great_Turkish_War\" title=\"Great Turkish War\">Great Turkish War</a> (1683-1699) and one of the most decisive defeats in <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Zenta\" title=\"Battle of Zenta\">Battle of Zenta</a>: a major engagement in the <a href=\"https://wikipedia.org/wiki/Great_Turkish_War\" title=\"Great Turkish War\">Great Turkish War</a> (1683-1699) and one of the most decisive defeats in <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> history.", "links": [{"title": "Battle of Zenta", "link": "https://wikipedia.org/wiki/Battle_of_Zenta"}, {"title": "Great Turkish War", "link": "https://wikipedia.org/wiki/Great_Turkish_War"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1708", "text": "<PERSON> of Sweden stops his march to conquer Moscow outside Smolensk, marking the turning point in the Great Northern War. The army is defeated nine months later in the Battle of Poltava, and the Swedish Empire ceases to be a major power.", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> stops his march to conquer Moscow outside <a href=\"https://wikipedia.org/wiki/Smolensk\" title=\"Smolensk\">Smolensk</a>, marking the turning point in the <a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>. The army is defeated nine months later in the <a href=\"https://wikipedia.org/wiki/Battle_of_Poltava\" title=\"Battle of Poltava\">Battle of Poltava</a>, and the <a href=\"https://wikipedia.org/wiki/Swedish_Empire\" title=\"Swedish Empire\">Swedish Empire</a> ceases to be a <a href=\"https://wikipedia.org/wiki/Major_power\" class=\"mw-redirect\" title=\"Major power\">major power</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> stops his march to conquer Moscow outside <a href=\"https://wikipedia.org/wiki/Smolensk\" title=\"Smolensk\">Smolensk</a>, marking the turning point in the <a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>. The army is defeated nine months later in the <a href=\"https://wikipedia.org/wiki/Battle_of_Poltava\" title=\"Battle of Poltava\">Battle of Poltava</a>, and the <a href=\"https://wikipedia.org/wiki/Swedish_Empire\" title=\"Swedish Empire\">Swedish Empire</a> ceases to be a <a href=\"https://wikipedia.org/wiki/Major_power\" class=\"mw-redirect\" title=\"Major power\">major power</a>.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}, {"title": "Smolensk", "link": "https://wikipedia.org/wiki/Smolensk"}, {"title": "Great Northern War", "link": "https://wikipedia.org/wiki/Great_Northern_War"}, {"title": "Battle of Poltava", "link": "https://wikipedia.org/wiki/Battle_of_Poltava"}, {"title": "Swedish Empire", "link": "https://wikipedia.org/wiki/Swedish_Empire"}, {"title": "Major power", "link": "https://wikipedia.org/wiki/Major_power"}]}, {"year": "1709", "text": "Battle of Malplaquet: Great Britain, Netherlands, and Austria fight against France.", "html": "1709 - <a href=\"https://wikipedia.org/wiki/Battle_of_Malplaquet\" title=\"Battle of Malplaquet\">Battle of Malplaquet</a>: Great Britain, Netherlands, and Austria fight against France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Malplaquet\" title=\"Battle of Malplaquet\">Battle of Malplaquet</a>: Great Britain, Netherlands, and Austria fight against France.", "links": [{"title": "Battle of Malplaquet", "link": "https://wikipedia.org/wiki/Battle_of_Malplaquet"}]}, {"year": "1714", "text": "Siege of Barcelona: Barcelona, capital city of the Principality of Catalonia, surrenders to Spanish and French Bourbon armies in the War of the Spanish Succession.", "html": "1714 - <a href=\"https://wikipedia.org/wiki/Siege_of_Barcelona_(1713%E2%80%9314)\" class=\"mw-redirect\" title=\"Siege of Barcelona (1713-14)\">Siege of Barcelona</a>: <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a>, capital city of the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a>, surrenders to Spanish and French <a href=\"https://wikipedia.org/wiki/Philip_V_of_Spain\" title=\"Philip V of Spain\">Bourbon</a> armies in the <a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Barcelona_(1713%E2%80%9314)\" class=\"mw-redirect\" title=\"Siege of Barcelona (1713-14)\">Siege of Barcelona</a>: <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a>, capital city of the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a>, surrenders to Spanish and French <a href=\"https://wikipedia.org/wiki/Philip_V_of_Spain\" title=\"Philip V of Spain\">Bourbon</a> armies in the <a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>.", "links": [{"title": "Siege of Barcelona (1713-14)", "link": "https://wikipedia.org/wiki/Siege_of_Barcelona_(1713%E2%80%9314)"}, {"title": "Barcelona", "link": "https://wikipedia.org/wiki/Barcelona"}, {"title": "Principality of Catalonia", "link": "https://wikipedia.org/wiki/Principality_of_Catalonia"}, {"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Philip_V_of_Spain"}, {"title": "War of the Spanish Succession", "link": "https://wikipedia.org/wiki/War_of_the_Spanish_Succession"}]}, {"year": "1758", "text": "Battle of Saint Cast: France repels British invasion during the Seven Years' War.", "html": "1758 - <a href=\"https://wikipedia.org/wiki/Battle_of_Saint_Cast\" class=\"mw-redirect\" title=\"Battle of Saint Cast\">Battle of Saint Cast</a>: France repels British invasion during the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Saint_Cast\" class=\"mw-redirect\" title=\"Battle of Saint Cast\">Battle of Saint Cast</a>: France repels British invasion during the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>.", "links": [{"title": "Battle of Saint Cast", "link": "https://wikipedia.org/wiki/Battle_of_Saint_Cast"}, {"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}]}, {"year": "1775", "text": "<PERSON>'s expedition to Quebec leaves Cambridge, Massachusetts.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_expedition_to_Quebec\" title=\"<PERSON>'s expedition to Quebec\"><PERSON>'s expedition to Quebec</a> leaves <a href=\"https://wikipedia.org/wiki/Cambridge,_Massachusetts\" title=\"Cambridge, Massachusetts\">Cambridge, Massachusetts</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_expedition_to_Quebec\" title=\"<PERSON>'s expedition to Quebec\"><PERSON>'s expedition to Quebec</a> leaves <a href=\"https://wikipedia.org/wiki/Cambridge,_Massachusetts\" title=\"Cambridge, Massachusetts\">Cambridge, Massachusetts</a>.", "links": [{"title": "<PERSON>'s expedition to Quebec", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_expedition_to_Quebec"}, {"title": "Cambridge, Massachusetts", "link": "https://wikipedia.org/wiki/Cambridge,_Massachusetts"}]}, {"year": "1776", "text": "British-American peace conference on Staten Island fails to stop nascent American Revolutionary War.", "html": "1776 - British-American <a href=\"https://wikipedia.org/wiki/Staten_Island_Peace_Conference\" title=\"Staten Island Peace Conference\">peace conference</a> on <a href=\"https://wikipedia.org/wiki/Staten_Island\" title=\"Staten Island\">Staten Island</a> fails to stop nascent <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>.", "no_year_html": "British-American <a href=\"https://wikipedia.org/wiki/Staten_Island_Peace_Conference\" title=\"Staten Island Peace Conference\">peace conference</a> on <a href=\"https://wikipedia.org/wiki/Staten_Island\" title=\"Staten Island\">Staten Island</a> fails to stop nascent <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>.", "links": [{"title": "Staten Island Peace Conference", "link": "https://wikipedia.org/wiki/Staten_Island_Peace_Conference"}, {"title": "Staten Island", "link": "https://wikipedia.org/wiki/Staten_Island"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}]}, {"year": "1777", "text": "American Revolutionary War: Battle of Brandywine: The British celebrate a major victory in Chester County, Pennsylvania.", "html": "1777 - American Revolutionary War: <a href=\"https://wikipedia.org/wiki/Battle_of_Brandywine\" title=\"Battle of Brandywine\">Battle of Brandywine</a>: The British celebrate a major victory in <a href=\"https://wikipedia.org/wiki/Chester_County,_Pennsylvania\" title=\"Chester County, Pennsylvania\">Chester County, Pennsylvania</a>.", "no_year_html": "American Revolutionary War: <a href=\"https://wikipedia.org/wiki/Battle_of_Brandywine\" title=\"Battle of Brandywine\">Battle of Brandywine</a>: The British celebrate a major victory in <a href=\"https://wikipedia.org/wiki/Chester_County,_Pennsylvania\" title=\"Chester County, Pennsylvania\">Chester County, Pennsylvania</a>.", "links": [{"title": "Battle of Brandywine", "link": "https://wikipedia.org/wiki/Battle_of_Brandywine"}, {"title": "Chester County, Pennsylvania", "link": "https://wikipedia.org/wiki/Chester_County,_Pennsylvania"}]}, {"year": "1780", "text": "American Revolutionary War: Sugarloaf massacre: A small detachment of militia from Northampton County, Pennsylvania, are attacked by Native Americans and Loyalists near Little Nescopeck Creek.", "html": "1780 - American Revolutionary War: <a href=\"https://wikipedia.org/wiki/Sugarloaf_massacre\" title=\"Sugarloaf massacre\">Sugarloaf massacre</a>: A small detachment of militia from <a href=\"https://wikipedia.org/wiki/Northampton_County,_Pennsylvania\" title=\"Northampton County, Pennsylvania\">Northampton County, Pennsylvania</a>, are attacked by Native Americans and <a href=\"https://wikipedia.org/wiki/Loyalist_(American_Revolution)\" title=\"Loyalist (American Revolution)\">Loyalists</a> near <a href=\"https://wikipedia.org/wiki/Little_Nescopeck_Creek\" title=\"Little Nescopeck Creek\">Little Nescopeck Creek</a>.", "no_year_html": "American Revolutionary War: <a href=\"https://wikipedia.org/wiki/Sugarloaf_massacre\" title=\"Sugarloaf massacre\">Sugarloaf massacre</a>: A small detachment of militia from <a href=\"https://wikipedia.org/wiki/Northampton_County,_Pennsylvania\" title=\"Northampton County, Pennsylvania\">Northampton County, Pennsylvania</a>, are attacked by Native Americans and <a href=\"https://wikipedia.org/wiki/Loyalist_(American_Revolution)\" title=\"Loyalist (American Revolution)\">Loyalists</a> near <a href=\"https://wikipedia.org/wiki/Little_Nescopeck_Creek\" title=\"Little Nescopeck Creek\">Little Nescopeck Creek</a>.", "links": [{"title": "Sugarloaf massacre", "link": "https://wikipedia.org/wiki/Sugarloaf_massacre"}, {"title": "Northampton County, Pennsylvania", "link": "https://wikipedia.org/wiki/Northampton_County,_Pennsylvania"}, {"title": "Loyalist (American Revolution)", "link": "https://wikipedia.org/wiki/Loyalist_(American_Revolution)"}, {"title": "Little Nescopeck Creek", "link": "https://wikipedia.org/wiki/Little_Nescopeck_Creek"}]}, {"year": "1786", "text": "The beginning of the Annapolis Convention.", "html": "1786 - The beginning of the <a href=\"https://wikipedia.org/wiki/Annapolis_Convention_(1786)\" title=\"Annapolis Convention (1786)\">Annapolis Convention</a>.", "no_year_html": "The beginning of the <a href=\"https://wikipedia.org/wiki/Annapolis_Convention_(1786)\" title=\"Annapolis Convention (1786)\">Annapolis Convention</a>.", "links": [{"title": "Annapolis Convention (1786)", "link": "https://wikipedia.org/wiki/Annapolis_Convention_(1786)"}]}, {"year": "1789", "text": "<PERSON> is appointed the first United States Secretary of the Treasury.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed the first <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed the first <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1792", "text": "The Hope Diamond is stolen along with other French crown jewels when six men break into the house where they are stored.", "html": "1792 - The <a href=\"https://wikipedia.org/wiki/Hope_Diamond\" title=\"Hope Diamond\"><PERSON> Diamond</a> is stolen along with other French crown jewels when six men break into the house where they are stored.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hope_Diamond\" title=\"Hope Diamond\">Hope Diamond</a> is stolen along with other French crown jewels when six men break into the house where they are stored.", "links": [{"title": "Hope Diamond", "link": "https://wikipedia.org/wiki/Hope_Diamond"}]}, {"year": "1800", "text": "The Maltese National Congress Battalions are disbanded by British Civil Commissioner <PERSON>.", "html": "1800 - The Maltese <a href=\"https://wikipedia.org/wiki/National_Congress_Battalions\" title=\"National Congress Battalions\">National Congress Battalions</a> are disbanded by British <a href=\"https://wikipedia.org/wiki/List_of_Civil_Commissioners_of_Malta\" class=\"mw-redirect\" title=\"List of Civil Commissioners of Malta\">Civil Commissioner</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alexander Ball\"><PERSON></a>.", "no_year_html": "The Maltese <a href=\"https://wikipedia.org/wiki/National_Congress_Battalions\" title=\"National Congress Battalions\">National Congress Battalions</a> are disbanded by British <a href=\"https://wikipedia.org/wiki/List_of_Civil_Commissioners_of_Malta\" class=\"mw-redirect\" title=\"List of Civil Commissioners of Malta\">Civil Commissioner</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alexander Ball\"><PERSON></a>.", "links": [{"title": "National Congress Battalions", "link": "https://wikipedia.org/wiki/National_Congress_Battalions"}, {"title": "List of Civil Commissioners of Malta", "link": "https://wikipedia.org/wiki/List_of_Civil_Commissioners_of_Malta"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "France annexes the Kingdom of Piedmont.", "html": "1802 - France annexes the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sardinia\" title=\"Kingdom of Sardinia\">Kingdom of Piedmont</a>.", "no_year_html": "France annexes the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sardinia\" title=\"Kingdom of Sardinia\">Kingdom of Piedmont</a>.", "links": [{"title": "Kingdom of Sardinia", "link": "https://wikipedia.org/wiki/Kingdom_of_Sardinia"}]}, {"year": "1803", "text": "The Battle of Delhi, during the Second Anglo-Maratha War, between British troops under General <PERSON>, and <PERSON><PERSON><PERSON> of Sc<PERSON><PERSON>'s army under General <PERSON> ends in a British victory.", "html": "1803 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Delhi_(1803)\" title=\"Battle of Delhi (1803)\">Battle of Delhi</a>, during the <a href=\"https://wikipedia.org/wiki/Second_Anglo-Maratha_War\" title=\"Second Anglo-Maratha War\">Second Anglo-Maratha War</a>, between British troops under <a href=\"https://wikipedia.org/wiki/Gerard_Lake,_1st_Viscount_Lake\" title=\"Gerard Lake, 1st Viscount <PERSON>\">General <PERSON></a>, and <a href=\"https://wikipedia.org/wiki/Maratha\" class=\"mw-redirect\" title=\"Maratha\">Marathas</a> of <a href=\"https://wikipedia.org/wiki/Scindia\" class=\"mw-redirect\" title=\"Scindia\">Scindia</a>'s army under General <PERSON> ends in a British victory.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Delhi_(1803)\" title=\"Battle of Delhi (1803)\">Battle of Delhi</a>, during the <a href=\"https://wikipedia.org/wiki/Second_Anglo-Maratha_War\" title=\"Second Anglo-Maratha War\">Second Anglo-Maratha War</a>, between British troops under <a href=\"https://wikipedia.org/wiki/Gerard_<PERSON>,_1st_Viscount_Lake\" title=\"<PERSON>, 1st Viscount <PERSON>\">General <PERSON></a>, and <a href=\"https://wikipedia.org/wiki/Maratha\" class=\"mw-redirect\" title=\"Maratha\">Marathas</a> of <a href=\"https://wikipedia.org/wiki/Scindia\" class=\"mw-redirect\" title=\"Scindia\">Scindia</a>'s army under General <PERSON> ends in a British victory.", "links": [{"title": "Battle of Delhi (1803)", "link": "https://wikipedia.org/wiki/Battle_of_Delhi_(1803)"}, {"title": "Second Anglo-Maratha War", "link": "https://wikipedia.org/wiki/Second_Anglo-Maratha_War"}, {"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maratha"}, {"title": "Scindia", "link": "https://wikipedia.org/wiki/Scindia"}]}, {"year": "1813", "text": "War of 1812: British troops arrive in Mount Vernon and prepare to march to and invade Washington, D.C.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: British troops arrive in <a href=\"https://wikipedia.org/wiki/Mount_Vernon\" title=\"Mount Vernon\">Mount Vernon</a> and prepare to march to and invade <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: British troops arrive in <a href=\"https://wikipedia.org/wiki/Mount_Vernon\" title=\"Mount Vernon\">Mount Vernon</a> and prepare to march to and invade <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "Mount Vernon", "link": "https://wikipedia.org/wiki/Mount_Vernon"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}]}, {"year": "1814", "text": "War of 1812: The climax of the Battle of Plattsburgh, a major United States victory in the war.", "html": "1814 - War of 1812: The climax of the <a href=\"https://wikipedia.org/wiki/Battle_of_Plattsburgh\" title=\"Battle of Plattsburgh\">Battle of Plattsburgh</a>, a major United States victory in the war.", "no_year_html": "War of 1812: The climax of the <a href=\"https://wikipedia.org/wiki/Battle_of_Plattsburgh\" title=\"Battle of Plattsburgh\">Battle of Plattsburgh</a>, a major United States victory in the war.", "links": [{"title": "Battle of Plattsburgh", "link": "https://wikipedia.org/wiki/Battle_of_Plattsburgh"}]}, {"year": "1826", "text": "Captain <PERSON>, an ex-freemason is arrested in Batavia, New York for debt after declaring that he would publish The Mysteries of Free Masonry, a book against Freemasonry. This sets into motion the events that led to his mysterious disappearance.", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anti-Mason)\" title=\"<PERSON> (anti-Mason)\">Captain <PERSON></a>, an ex-<a href=\"https://wikipedia.org/wiki/Freemason\" class=\"mw-redirect\" title=\"Freemason\">freemason</a> is arrested in <a href=\"https://wikipedia.org/wiki/Batavia,_New_York\" title=\"Batavia, New York\">Batavia, New York</a> for debt after declaring that he would publish <a rel=\"nofollow\" class=\"external text\" href=\"https://wikipedia.orghttp://www.gutenberg.org/files/18136/18136-h/18136-h.htm\">The Mysteries of Free Masonry</a>, a book against <a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">Freemasonry</a>. This sets into motion the events that led to his <a href=\"https://wikipedia.org/wiki/<PERSON>_(anti-Mason)#Disappearance\" title=\"<PERSON> (anti-Mason)\">mysterious disappearance</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(anti-Mason)\" title=\"<PERSON> (anti-Mason)\">Captain <PERSON></a>, an ex-<a href=\"https://wikipedia.org/wiki/Freemason\" class=\"mw-redirect\" title=\"Freemason\">freemason</a> is arrested in <a href=\"https://wikipedia.org/wiki/Batavia,_New_York\" title=\"Batavia, New York\">Batavia, New York</a> for debt after declaring that he would publish <a rel=\"nofollow\" class=\"external text\" href=\"https://wikipedia.orghttp://www.gutenberg.org/files/18136/18136-h/18136-h.htm\">The Mysteries of Free Masonry</a>, a book against <a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">Freemasonry</a>. This sets into motion the events that led to his <a href=\"https://wikipedia.org/wiki/<PERSON>_(anti-Mason)#Disappearance\" title=\"<PERSON> (anti-Mason)\">mysterious disappearance</a>.", "links": [{"title": "<PERSON> (anti-Mason)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anti-Mason)"}, {"title": "Freemason", "link": "https://wikipedia.org/wiki/Freemason"}, {"title": "Batavia, New York", "link": "https://wikipedia.org/wiki/Batavia,_New_York"}, {"title": "Freemasonry", "link": "https://wikipedia.org/wiki/Freemasonry"}, {"title": "<PERSON> (anti-Mason)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anti-Mason)#Disappearance"}]}, {"year": "1829", "text": "An expedition led by <PERSON><PERSON><PERSON> at Tampico, sent by the Spanish crown to retake Mexico, surrenders at the Battle of Tampico, marking the effective end of Spain's resistance to Mexico's campaign for independence.", "html": "1829 - An expedition led by <a href=\"https://wikipedia.org/wiki/Isidro_Barradas\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Barr<PERSON>\"><PERSON><PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Tampico\" title=\"Tampico\">Tampico</a>, sent by the Spanish crown to retake Mexico, surrenders at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tampico_(1829)\" title=\"Battle of Tampico (1829)\">Battle of Tampico</a>, marking the effective end of Spain's resistance to Mexico's campaign for independence.", "no_year_html": "An expedition led by <a href=\"https://wikipedia.org/wiki/Isidro_Barradas\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Barradas\"><PERSON><PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Tampico\" title=\"Tampico\">Tampico</a>, sent by the Spanish crown to retake Mexico, surrenders at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tampico_(1829)\" title=\"Battle of Tampico (1829)\">Battle of Tampico</a>, marking the effective end of Spain's resistance to Mexico's campaign for independence.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>idro_Barradas"}, {"title": "Tampico", "link": "https://wikipedia.org/wiki/Tampico"}, {"title": "Battle of Tampico (1829)", "link": "https://wikipedia.org/wiki/Battle_of_Tampico_(1829)"}]}, {"year": "1830", "text": "Anti-Masonic Party convention; one of the first American political party conventions.", "html": "1830 - <a href=\"https://wikipedia.org/wiki/Anti-Masonic_Party\" title=\"Anti-Masonic Party\">Anti-Masonic Party</a> convention; one of the first American political party conventions.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anti-Masonic_Party\" title=\"Anti-Masonic Party\">Anti-Masonic Party</a> convention; one of the first American political party conventions.", "links": [{"title": "Anti-Masonic Party", "link": "https://wikipedia.org/wiki/Anti-Masonic_Party"}]}, {"year": "1836", "text": "The Riograndense Republic is proclaimed by rebels after defeating Empire of Brazil's troops in the Battle of Seival, during the Ragamuffin War.", "html": "1836 - The <a href=\"https://wikipedia.org/wiki/Riograndense_Republic\" title=\"Riograndense Republic\">Riograndense Republic</a> is proclaimed by rebels after defeating <a href=\"https://wikipedia.org/wiki/Empire_of_Brazil\" title=\"Empire of Brazil\">Empire of Brazil</a>'s troops in the Battle of Seival, during the <a href=\"https://wikipedia.org/wiki/Ragamuffin_War\" title=\"Ragamuffin War\">Ragamuffin War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Riograndense_Republic\" title=\"Riograndense Republic\">Riograndense Republic</a> is proclaimed by rebels after defeating <a href=\"https://wikipedia.org/wiki/Empire_of_Brazil\" title=\"Empire of Brazil\">Empire of Brazil</a>'s troops in the Battle of Seival, during the <a href=\"https://wikipedia.org/wiki/Ragamuffin_War\" title=\"Ragamuffin War\">Ragamuffin War</a>.", "links": [{"title": "Riograndense Republic", "link": "https://wikipedia.org/wiki/Riograndense_Republic"}, {"title": "Empire of Brazil", "link": "https://wikipedia.org/wiki/Empire_of_Brazil"}, {"title": "Ragamuffin War", "link": "https://wikipedia.org/wiki/Ragamuffin_War"}]}, {"year": "1851", "text": "Christiana Resistance: Escaped slaves led by <PERSON> fight off and kill a slave owner who, with a federal marshal and an armed party, sought to seize three of his former slaves in Christiana, Pennsylvania, thereby creating a cause célèbre between slavery proponents and abolitionists.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Christiana_Resistance\" class=\"mw-redirect\" title=\"Christiana Resistance\">Christiana Resistance</a>: Escaped slaves led by <a href=\"https://wikipedia.org/wiki/<PERSON>_(abolitionist)\" title=\"<PERSON> (abolitionist)\"><PERSON></a> fight off and kill a slave owner who, with a federal marshal and an armed party, sought to seize three of his former slaves in <a href=\"https://wikipedia.org/wiki/Christiana,_Pennsylvania\" title=\"Christiana, Pennsylvania\">Christiana, Pennsylvania</a>, thereby creating a cause célèbre between slavery proponents and abolitionists.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christiana_Resistance\" class=\"mw-redirect\" title=\"Christiana Resistance\">Christiana Resistance</a>: Escaped slaves led by <a href=\"https://wikipedia.org/wiki/<PERSON>_(abolitionist)\" title=\"<PERSON> (abolitionist)\"><PERSON></a> fight off and kill a slave owner who, with a federal marshal and an armed party, sought to seize three of his former slaves in <a href=\"https://wikipedia.org/wiki/Christiana,_Pennsylvania\" title=\"Christiana, Pennsylvania\">Christiana, Pennsylvania</a>, thereby creating a cause célèbre between slavery proponents and abolitionists.", "links": [{"title": "Christiana Resistance", "link": "https://wikipedia.org/wiki/Christiana_Resistance"}, {"title": "<PERSON> (abolitionist)", "link": "https://wikipedia.org/wiki/<PERSON>_(abolitionist)"}, {"title": "Christiana, Pennsylvania", "link": "https://wikipedia.org/wiki/Christiana,_Pennsylvania"}]}, {"year": "1852", "text": "Outbreak of Revolution of September 11 resulting in the State of Buenos Aires declaring independence as a Republic.", "html": "1852 - Outbreak of <a href=\"https://wikipedia.org/wiki/Revolution_of_11_September_1852\" title=\"Revolution of 11 September 1852\">Revolution of September 11</a> resulting in the <a href=\"https://wikipedia.org/wiki/State_of_Buenos_Aires\" title=\"State of Buenos Aires\">State of Buenos Aires</a> declaring independence as a Republic.", "no_year_html": "Outbreak of <a href=\"https://wikipedia.org/wiki/Revolution_of_11_September_1852\" title=\"Revolution of 11 September 1852\">Revolution of September 11</a> resulting in the <a href=\"https://wikipedia.org/wiki/State_of_Buenos_Aires\" title=\"State of Buenos Aires\">State of Buenos Aires</a> declaring independence as a Republic.", "links": [{"title": "Revolution of 11 September 1852", "link": "https://wikipedia.org/wiki/Revolution_of_11_September_1852"}, {"title": "State of Buenos Aires", "link": "https://wikipedia.org/wiki/State_of_Buenos_Aires"}]}, {"year": "1857", "text": "The Mountain Meadows massacre: Mormon settlers and Paiutes massacre 120 pioneers at Mountain Meadows, Utah.", "html": "1857 - The <a href=\"https://wikipedia.org/wiki/Mountain_Meadows_massacre\" class=\"mw-redirect\" title=\"Mountain Meadows massacre\">Mountain Meadows massacre</a>: <a href=\"https://wikipedia.org/wiki/Mormon\" class=\"mw-redirect\" title=\"Mormon\">Mormon</a> settlers and <a href=\"https://wikipedia.org/wiki/Southern_Paiute\" class=\"mw-redirect\" title=\"Southern Paiute\">Paiutes</a> massacre 120 pioneers at <a href=\"https://wikipedia.org/wiki/Mountain_Meadows,_Utah\" class=\"mw-redirect\" title=\"Mountain Meadows, Utah\">Mountain Meadows, Utah</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mountain_Meadows_massacre\" class=\"mw-redirect\" title=\"Mountain Meadows massacre\">Mountain Meadows massacre</a>: <a href=\"https://wikipedia.org/wiki/Mormon\" class=\"mw-redirect\" title=\"Mormon\">Mormon</a> settlers and <a href=\"https://wikipedia.org/wiki/Southern_Paiute\" class=\"mw-redirect\" title=\"Southern Paiute\">Paiutes</a> massacre 120 pioneers at <a href=\"https://wikipedia.org/wiki/Mountain_Meadows,_Utah\" class=\"mw-redirect\" title=\"Mountain Meadows, Utah\">Mountain Meadows, Utah</a>.", "links": [{"title": "Mountain Meadows massacre", "link": "https://wikipedia.org/wiki/Mountain_Meadows_massacre"}, {"title": "Mormon", "link": "https://wikipedia.org/wiki/Mormon"}, {"title": "Southern Paiute", "link": "https://wikipedia.org/wiki/Southern_Paiute"}, {"title": "Mountain Meadows, Utah", "link": "https://wikipedia.org/wiki/Mountain_Meadows,_Utah"}]}, {"year": "1881", "text": "In the Swiss state of Glarus, a rockslide buries parts of the village of Elm, destroying 83 buildings and killing 115 people.", "html": "1881 - In the <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Swiss</a> state of <a href=\"https://wikipedia.org/wiki/Canton_of_Glarus\" title=\"Canton of Glarus\">Glarus</a>, a <a href=\"https://wikipedia.org/wiki/Rockslide_of_Elm\" title=\"Rockslide of Elm\">rockslide</a> buries parts of the village of <a href=\"https://wikipedia.org/wiki/Elm,_Switzerland\" title=\"Elm, Switzerland\">Elm</a>, destroying 83 buildings and killing 115 people.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Swiss</a> state of <a href=\"https://wikipedia.org/wiki/Canton_of_Glarus\" title=\"Canton of Glarus\">Glarus</a>, a <a href=\"https://wikipedia.org/wiki/Rockslide_of_Elm\" title=\"Rockslide of Elm\">rockslide</a> buries parts of the village of <a href=\"https://wikipedia.org/wiki/Elm,_Switzerland\" title=\"Elm, Switzerland\">Elm</a>, destroying 83 buildings and killing 115 people.", "links": [{"title": "Switzerland", "link": "https://wikipedia.org/wiki/Switzerland"}, {"title": "Canton of Glarus", "link": "https://wikipedia.org/wiki/Canton_of_Glarus"}, {"title": "Rockslide of Elm", "link": "https://wikipedia.org/wiki/Rockslide_of_Elm"}, {"title": "Elm, Switzerland", "link": "https://wikipedia.org/wiki/Elm,_Switzerland"}]}, {"year": "1897", "text": "After months of pursuit, generals of Menelik II of Ethiopia capture <PERSON><PERSON>, the last king of the Kaffa.", "html": "1897 - After months of pursuit, generals of <a href=\"https://wikipedia.org/wiki/Menelik_II_of_Ethiopia\" class=\"mw-redirect\" title=\"Menelik II of Ethiopia\">Menelik II of Ethiopia</a> capture <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the last king of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kaffa\" title=\"Kingdom of Kaffa\">Kaffa</a>.", "no_year_html": "After months of pursuit, generals of <a href=\"https://wikipedia.org/wiki/Menelik_II_of_Ethiopia\" class=\"mw-redirect\" title=\"Menelik II of Ethiopia\">Menelik II of Ethiopia</a> capture <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the last king of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kaffa\" title=\"Kingdom of Kaffa\">Kaffa</a>.", "links": [{"title": "Menelik II of Ethiopia", "link": "https://wikipedia.org/wiki/Menelik_II_of_Ethiopia"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Kingdom of Kaffa", "link": "https://wikipedia.org/wiki/Kingdom_of_Kaffa"}]}, {"year": "1903", "text": "The first race at the Milwaukee Mile in West Allis, Wisconsin is held. It is the oldest major speedway in the world.", "html": "1903 - The first race at the <a href=\"https://wikipedia.org/wiki/Milwaukee_Mile\" title=\"Milwaukee Mile\">Milwaukee Mile</a> in <a href=\"https://wikipedia.org/wiki/West_Allis,_Wisconsin\" title=\"West Allis, Wisconsin\">West Allis, Wisconsin</a> is held. It is the oldest major speedway in the world.", "no_year_html": "The first race at the <a href=\"https://wikipedia.org/wiki/Milwaukee_Mile\" title=\"Milwaukee Mile\">Milwaukee Mile</a> in <a href=\"https://wikipedia.org/wiki/West_Allis,_Wisconsin\" title=\"West Allis, Wisconsin\">West Allis, Wisconsin</a> is held. It is the oldest major speedway in the world.", "links": [{"title": "Milwaukee Mile", "link": "https://wikipedia.org/wiki/Milwaukee_Mile"}, {"title": "West Allis, Wisconsin", "link": "https://wikipedia.org/wiki/West_Allis,_Wisconsin"}]}, {"year": "1905", "text": "The Ninth Avenue derailment occurs in New York City, killing 13.", "html": "1905 - The <a href=\"https://wikipedia.org/wiki/Ninth_Avenue_derailment\" title=\"Ninth Avenue derailment\">Ninth Avenue derailment</a> occurs in New York City, killing 13.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ninth_Avenue_derailment\" title=\"Ninth Avenue derailment\">Ninth Avenue derailment</a> occurs in New York City, killing 13.", "links": [{"title": "Ninth Avenue derailment", "link": "https://wikipedia.org/wiki/Ninth_Avenue_derailment"}]}, {"year": "1914", "text": "World War I: Australia invades German New Guinea, defeating a German contingent at the Battle of Bita Paka.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Australia invades <a href=\"https://wikipedia.org/wiki/German_New_Guinea\" title=\"German New Guinea\">German New Guinea</a>, defeating a German contingent at the <a href=\"https://wikipedia.org/wiki/Battle_of_Bita_Paka\" title=\"Battle of Bita Paka\">Battle of Bita Paka</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Australia invades <a href=\"https://wikipedia.org/wiki/German_New_Guinea\" title=\"German New Guinea\">German New Guinea</a>, defeating a German contingent at the <a href=\"https://wikipedia.org/wiki/Battle_of_Bita_Paka\" title=\"Battle of Bita Paka\">Battle of Bita Paka</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "German New Guinea", "link": "https://wikipedia.org/wiki/German_New_Guinea"}, {"title": "Battle of Bita Paka", "link": "https://wikipedia.org/wiki/Battle_of_Bita_Paka"}]}, {"year": "1914", "text": "The Second Period of Russification: The teaching of the Russian language and Russian history in Finnish schools is ordered to be considerably increased as part of the forced Russification program in Finland run by Tsar <PERSON>.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/Second_Period_of_Russification\" class=\"mw-redirect\" title=\"Second Period of Russification\">Second Period of Russification</a>: The teaching of the <a href=\"https://wikipedia.org/wiki/Russian_language\" title=\"Russian language\">Russian language</a> and <a href=\"https://wikipedia.org/wiki/History_of_Russia\" title=\"History of Russia\">Russian history</a> in Finnish schools is ordered to be considerably increased as part of the <a href=\"https://wikipedia.org/wiki/Russification_of_Finland\" title=\"Russification of Finland\">forced Russification program</a> in <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Finland</a> run by Tsar <a href=\"https://wikipedia.org/wiki/Nicholas_II_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> II of Russia\"><PERSON> II</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Second_Period_of_Russification\" class=\"mw-redirect\" title=\"Second Period of Russification\">Second Period of Russification</a>: The teaching of the <a href=\"https://wikipedia.org/wiki/Russian_language\" title=\"Russian language\">Russian language</a> and <a href=\"https://wikipedia.org/wiki/History_of_Russia\" title=\"History of Russia\">Russian history</a> in Finnish schools is ordered to be considerably increased as part of the <a href=\"https://wikipedia.org/wiki/Russification_of_Finland\" title=\"Russification of Finland\">forced Russification program</a> in <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Finland</a> run by Tsar <a href=\"https://wikipedia.org/wiki/Nicholas_II_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> II of Russia\"><PERSON> II</a>.", "links": [{"title": "Second Period of Russification", "link": "https://wikipedia.org/wiki/Second_Period_of_Russification"}, {"title": "Russian language", "link": "https://wikipedia.org/wiki/Russian_language"}, {"title": "History of Russia", "link": "https://wikipedia.org/wiki/History_of_Russia"}, {"title": "Russification of Finland", "link": "https://wikipedia.org/wiki/Russification_of_Finland"}, {"title": "Grand Duchy of Finland", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Finland"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}]}, {"year": "1916", "text": "The Quebec Bridge's central span collapses, killing 11 men. The bridge previously collapsed completely on August 29, 1907.", "html": "1916 - The <a href=\"https://wikipedia.org/wiki/Quebec_Bridge\" title=\"Quebec Bridge\">Quebec Bridge</a>'s central span collapses, killing 11 men. The bridge previously collapsed completely on <a href=\"https://wikipedia.org/wiki/August_29\" title=\"August 29\">August 29</a>, <a href=\"https://wikipedia.org/wiki/1907\" title=\"1907\">1907</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Quebec_Bridge\" title=\"Quebec Bridge\">Quebec Bridge</a>'s central span collapses, killing 11 men. The bridge previously collapsed completely on <a href=\"https://wikipedia.org/wiki/August_29\" title=\"August 29\">August 29</a>, <a href=\"https://wikipedia.org/wiki/1907\" title=\"1907\">1907</a>.", "links": [{"title": "Quebec Bridge", "link": "https://wikipedia.org/wiki/Quebec_Bridge"}, {"title": "August 29", "link": "https://wikipedia.org/wiki/August_29"}, {"title": "1907", "link": "https://wikipedia.org/wiki/1907"}]}, {"year": "1919", "text": "United States Marine Corps invades Honduras.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/History_of_Honduras#Honduras_in_the_twentieth_century\" title=\"History of Honduras\">United States Marine Corps invades Honduras</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/History_of_Honduras#Honduras_in_the_twentieth_century\" title=\"History of Honduras\">United States Marine Corps invades Honduras</a>.", "links": [{"title": "History of Honduras", "link": "https://wikipedia.org/wiki/History_of_Honduras#Honduras_in_the_twentieth_century"}]}, {"year": "1921", "text": "Nahalal, a Jewish moshav in Palestine, is settled.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Nahalal\" title=\"Nahalal\"><PERSON><PERSON><PERSON></a>, a Jewish <a href=\"https://wikipedia.org/wiki/Moshav\" title=\"Moshav\">moshav</a> in <a href=\"https://wikipedia.org/wiki/Mandatory_Palestine\" title=\"Mandatory Palestine\">Palestine</a>, is settled.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nahalal\" title=\"Nahalal\"><PERSON><PERSON><PERSON></a>, a Jewish <a href=\"https://wikipedia.org/wiki/Moshav\" title=\"Moshav\">moshav</a> in <a href=\"https://wikipedia.org/wiki/Mandatory_Palestine\" title=\"Mandatory Palestine\">Palestine</a>, is settled.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nahalal"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>shav"}, {"title": "Mandatory Palestine", "link": "https://wikipedia.org/wiki/Mandatory_Palestine"}]}, {"year": "1922", "text": "The Treaty of Kars is ratified in Yerevan, Armenia.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Kars\" title=\"Treaty of Kars\">Treaty of Kars</a> is ratified in <a href=\"https://wikipedia.org/wiki/Yerevan\" title=\"Yerevan\">Yerevan</a>, <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Kars\" title=\"Treaty of Kars\">Treaty of Kars</a> is ratified in <a href=\"https://wikipedia.org/wiki/Yerevan\" title=\"Yerevan\">Yerevan</a>, <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>.", "links": [{"title": "Treaty of Kars", "link": "https://wikipedia.org/wiki/Treaty_of_Kars"}, {"title": "Yerevan", "link": "https://wikipedia.org/wiki/Yerevan"}, {"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}]}, {"year": "1941", "text": "Construction begins on the Pentagon.", "html": "1941 - Construction begins on <a href=\"https://wikipedia.org/wiki/The_Pentagon\" title=\"The Pentagon\">the Pentagon</a>.", "no_year_html": "Construction begins on <a href=\"https://wikipedia.org/wiki/The_Pentagon\" title=\"The Pentagon\">the Pentagon</a>.", "links": [{"title": "The Pentagon", "link": "https://wikipedia.org/wiki/The_Pentagon"}]}, {"year": "1941", "text": "<PERSON>'s Des Moines speech accusing the British, Jews and FDR's administration of conspiring for war with Germany.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Des_Moines_speech\" title=\"Des Moines speech\">Des Moines speech</a> accusing the British, Jews and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">FDR's</a> administration of conspiring for war with Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Des_Moines_speech\" title=\"Des Moines speech\">Des Moines speech</a> accusing the British, Jews and <a href=\"https://wikipedia.org/wiki/Franklin_<PERSON>_<PERSON>\" title=\"Franklin <PERSON>\">FDR's</a> administration of conspiring for war with Germany.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Des Moines speech", "link": "https://wikipedia.org/wiki/Des_Moines_speech"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "World War II: German troops occupy Corsica and Kosovo-Metohija ending the Italian occupation of Corsica.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">German troops</a> occupy <a href=\"https://wikipedia.org/wiki/Corsica\" title=\"Corsica\">Corsica</a> and <a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a>-<a href=\"https://wikipedia.org/wiki/Metohija\" title=\"Metohija\">Metohija</a> ending the <a href=\"https://wikipedia.org/wiki/Italian_occupation_of_Corsica\" title=\"Italian occupation of Corsica\">Italian occupation of Corsica</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">German troops</a> occupy <a href=\"https://wikipedia.org/wiki/Corsica\" title=\"Corsica\">Corsica</a> and <a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a>-<a href=\"https://wikipedia.org/wiki/Metohija\" title=\"Metohija\">Metohija</a> ending the <a href=\"https://wikipedia.org/wiki/Italian_occupation_of_Corsica\" title=\"Italian occupation of Corsica\">Italian occupation of Corsica</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Wehrmacht", "link": "https://wikipedia.org/wiki/Wehrmacht"}, {"title": "Corsica", "link": "https://wikipedia.org/wiki/Corsica"}, {"title": "Kosovo", "link": "https://wikipedia.org/wiki/Kosovo"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Metohija"}, {"title": "Italian occupation of Corsica", "link": "https://wikipedia.org/wiki/Italian_occupation_of_Corsica"}]}, {"year": "1944", "text": "World War II: RAF bombing raid on Darmstadt and the following firestorm kill 11,500.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/RAF\" class=\"mw-redirect\" title=\"RAF\">RAF</a> bombing raid on <a href=\"https://wikipedia.org/wiki/Darmstadt\" title=\"Darmstadt\">Darmstadt</a> and the following firestorm kill 11,500.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/RAF\" class=\"mw-redirect\" title=\"RAF\">RAF</a> bombing raid on <a href=\"https://wikipedia.org/wiki/Darmstadt\" title=\"Darmstadt\">Darmstadt</a> and the following firestorm kill 11,500.", "links": [{"title": "RAF", "link": "https://wikipedia.org/wiki/RAF"}, {"title": "Darmstadt", "link": "https://wikipedia.org/wiki/Darmstadt"}]}, {"year": "1945", "text": "World War II: Batu <PERSON> camp, a Japanese-run POW and civilian internment camp on the island of Borneo, is liberated by Australian 9th Division forces.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Batu_Lintang_camp\" title=\"Batu Lintang camp\">Batu Lintang camp</a>, a Japanese-run POW and civilian internment camp on the island of <a href=\"https://wikipedia.org/wiki/Borneo\" title=\"Borneo\">Borneo</a>, is liberated by <a href=\"https://wikipedia.org/wiki/9th_Division_(Australia)\" title=\"9th Division (Australia)\">Australian 9th Division</a> forces.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Batu_Lintang_camp\" title=\"Batu Lintang camp\">Batu Lintan<PERSON> camp</a>, a Japanese-run POW and civilian internment camp on the island of <a href=\"https://wikipedia.org/wiki/Borneo\" title=\"Borneo\">Borneo</a>, is liberated by <a href=\"https://wikipedia.org/wiki/9th_Division_(Australia)\" title=\"9th Division (Australia)\">Australian 9th Division</a> forces.", "links": [{"title": "<PERSON><PERSON> camp", "link": "https://wikipedia.org/wiki/Batu_<PERSON>tang_camp"}, {"title": "Borneo", "link": "https://wikipedia.org/wiki/Borneo"}, {"title": "9th Division (Australia)", "link": "https://wikipedia.org/wiki/9th_Division_(Australia)"}]}, {"year": "1954", "text": "Hurricane <PERSON> hits New England (United States) as a Category 2 hurricane, causing significant damage and 29 deaths.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Hurricane_Edna\" title=\"Hurricane Edna\">Hurricane <PERSON></a> hits <a href=\"https://wikipedia.org/wiki/New_England\" title=\"New England\">New England</a> (United States) as a <a href=\"https://wikipedia.org/wiki/Category_2_hurricane\" class=\"mw-redirect\" title=\"Category 2 hurricane\">Category 2 hurricane</a>, causing significant damage and 29 deaths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Edna\" title=\"Hurricane Edna\">Hurricane <PERSON></a> hits <a href=\"https://wikipedia.org/wiki/New_England\" title=\"New England\">New England</a> (United States) as a <a href=\"https://wikipedia.org/wiki/Category_2_hurricane\" class=\"mw-redirect\" title=\"Category 2 hurricane\">Category 2 hurricane</a>, causing significant damage and 29 deaths.", "links": [{"title": "Hurricane Edna", "link": "https://wikipedia.org/wiki/Hurricane_Edna"}, {"title": "New England", "link": "https://wikipedia.org/wiki/New_England"}, {"title": "Category 2 hurricane", "link": "https://wikipedia.org/wiki/Category_2_hurricane"}]}, {"year": "1961", "text": "Hurricane <PERSON> strikes the Texas coast as a Category 4 hurricane, the second strongest storm ever to hit the state.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Hurricane_Carla\" title=\"Hurricane Carla\">Hurricane <PERSON></a> strikes the <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> coast as a Category 4 hurricane, the second strongest storm ever to hit the state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Carla\" title=\"Hurricane Carla\">Hurricane <PERSON></a> strikes the <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> coast as a Category 4 hurricane, the second strongest storm ever to hit the state.", "links": [{"title": "Hurricane Carla", "link": "https://wikipedia.org/wiki/Hurricane_Carla"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}]}, {"year": "1965", "text": "Indo-Pakistani War: The Indian Army captures the town of Burki, just southeast of Lahore.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1965\">Indo-Pakistani War</a>: The <a href=\"https://wikipedia.org/wiki/Indian_Army\" title=\"Indian Army\">Indian Army</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Burki\" title=\"Battle of Burki\">captures</a> the town of <a href=\"https://wikipedia.org/wiki/Barki,_Pakistan\" title=\"Barki, Pakistan\">Burki</a>, just southeast of <a href=\"https://wikipedia.org/wiki/Lahore\" title=\"Lahore\">Lahore</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1965\">Indo-Pakistani War</a>: The <a href=\"https://wikipedia.org/wiki/Indian_Army\" title=\"Indian Army\">Indian Army</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Burki\" title=\"Battle of Burki\">captures</a> the town of <a href=\"https://wikipedia.org/wiki/Barki,_Pakistan\" title=\"Barki, Pakistan\">Burki</a>, just southeast of <a href=\"https://wikipedia.org/wiki/Lahore\" title=\"Lahore\">Lahore</a>.", "links": [{"title": "Indo-Pakistani War of 1965", "link": "https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965"}, {"title": "Indian Army", "link": "https://wikipedia.org/wiki/Indian_Army"}, {"title": "Battle of Burki", "link": "https://wikipedia.org/wiki/Battle_of_Burki"}, {"title": "Barki, Pakistan", "link": "https://wikipedia.org/wiki/Barki,_Pakistan"}, {"title": "Lahore", "link": "https://wikipedia.org/wiki/Lahore"}]}, {"year": "1967", "text": "China's People's Liberation Army (PLA) launched an attack on Indian posts at Nathu La, Sikkim, India, which resulted in military clashes.", "html": "1967 - China's <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a> (PLA) launched an attack on Indian posts at <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_La\" title=\"Nat<PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sikkim\" title=\"Sikkim\">Sikkim</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, which resulted in <a href=\"https://wikipedia.org/wiki/War\" title=\"War\">military clashes</a>.", "no_year_html": "China's <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a> (PLA) launched an attack on Indian posts at <a href=\"https://wikipedia.org/wiki/Nat<PERSON>_La\" title=\"Nat<PERSON> La\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sikkim\" title=\"Sikkim\">Sikkim</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, which resulted in <a href=\"https://wikipedia.org/wiki/War\" title=\"War\">military clashes</a>.", "links": [{"title": "People's Liberation Army", "link": "https://wikipedia.org/wiki/People%27s_Liberation_Army"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nathu_La"}, {"title": "Sikkim", "link": "https://wikipedia.org/wiki/Sikkim"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "War", "link": "https://wikipedia.org/wiki/War"}]}, {"year": "1968", "text": "Air France Flight 1611 crashes off Nice, France, killing 89 passengers and six crew.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Air_France_Flight_1611\" title=\"Air France Flight 1611\">Air France Flight 1611</a> crashes off <a href=\"https://wikipedia.org/wiki/Nice\" title=\"Nice\">Nice</a>, France, killing 89 passengers and six crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_France_Flight_1611\" title=\"Air France Flight 1611\">Air France Flight 1611</a> crashes off <a href=\"https://wikipedia.org/wiki/Nice\" title=\"Nice\">Nice</a>, France, killing 89 passengers and six crew.", "links": [{"title": "Air France Flight 1611", "link": "https://wikipedia.org/wiki/Air_France_Flight_1611"}, {"title": "Nice", "link": "https://wikipedia.org/wiki/Nice"}]}, {"year": "1970", "text": "The Dawson's Field hijackers release 88 of their hostages. The remaining hostages, mostly Jews and Israeli citizens, are held until September 25.", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Field_hijackings\" title=\"Dawson's Field hijackings\">Dawson's Field hijackers</a> release 88 of their hostages. The remaining hostages, mostly Jews and Israeli citizens, are held until <a href=\"https://wikipedia.org/wiki/September_25\" title=\"September 25\">September 25</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Field_hijackings\" title=\"Dawson's Field hijackings\">Dawson's Field hijackers</a> release 88 of their hostages. The remaining hostages, mostly Jews and Israeli citizens, are held until <a href=\"https://wikipedia.org/wiki/September_25\" title=\"September 25\">September 25</a>.", "links": [{"title": "Dawson's Field hijackings", "link": "https://wikipedia.org/wiki/Dawson%27s_Field_hijackings"}, {"title": "September 25", "link": "https://wikipedia.org/wiki/September_25"}]}, {"year": "1971", "text": "The Egyptian Constitution becomes official.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/History_of_the_Egyptian_Constitution\" title=\"History of the Egyptian Constitution\">Egyptian Constitution</a> becomes official.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/History_of_the_Egyptian_Constitution\" title=\"History of the Egyptian Constitution\">Egyptian Constitution</a> becomes official.", "links": [{"title": "History of the Egyptian Constitution", "link": "https://wikipedia.org/wiki/History_of_the_Egyptian_Constitution"}]}, {"year": "1972", "text": "The San Francisco Bay Area Rapid Transit system begins passenger service.", "html": "1972 - The San Francisco <a href=\"https://wikipedia.org/wiki/Bay_Area_Rapid_Transit\" title=\"Bay Area Rapid Transit\">Bay Area Rapid Transit</a> system begins passenger service.", "no_year_html": "The San Francisco <a href=\"https://wikipedia.org/wiki/Bay_Area_Rapid_Transit\" title=\"Bay Area Rapid Transit\">Bay Area Rapid Transit</a> system begins passenger service.", "links": [{"title": "Bay Area Rapid Transit", "link": "https://wikipedia.org/wiki/Bay_Area_Rapid_Transit"}]}, {"year": "1973", "text": "A coup in Chile, headed by General <PERSON><PERSON>, topples the democratically elected president <PERSON>.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/1973_Chilean_coup_d%27%C3%A9tat\" title=\"1973 Chilean coup d'état\">A coup</a> in <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, headed by General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, topples the democratically elected president <a href=\"https://wikipedia.org/wiki/Salvador_Allende\" title=\"Salvador Allende\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1973_Chilean_coup_d%27%C3%A9tat\" title=\"1973 Chilean coup d'état\">A coup</a> in <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, headed by General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, topples the democratically elected president <a href=\"https://wikipedia.org/wiki/Salvador_Allende\" title=\"Salvador Allen<PERSON>\"><PERSON></a>.", "links": [{"title": "1973 Chilean coup d'état", "link": "https://wikipedia.org/wiki/1973_Chilean_coup_d%27%C3%A9tat"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salvador_Allende"}]}, {"year": "1973", "text": "JAT Airways Flight 769 crashes into the Maganik mountain range while on approach to Titograd Airport, killing 35 passengers and six crew.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/JAT_Airways_Flight_769\" class=\"mw-redirect\" title=\"JAT Airways Flight 769\">JAT Airways Flight 769</a> crashes into the <a href=\"https://wikipedia.org/wiki/Maganik\" title=\"Maganik\">Maganik</a> mountain range while on approach to <a href=\"https://wikipedia.org/wiki/Podgorica_Airport\" title=\"Podgorica Airport\">Titograd Airport</a>, killing 35 passengers and six crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/JAT_Airways_Flight_769\" class=\"mw-redirect\" title=\"JAT Airways Flight 769\">JAT Airways Flight 769</a> crashes into the <a href=\"https://wikipedia.org/wiki/Maganik\" title=\"Maganik\">Maganik</a> mountain range while on approach to <a href=\"https://wikipedia.org/wiki/Podgorica_Airport\" title=\"Podgorica Airport\">Titograd Airport</a>, killing 35 passengers and six crew.", "links": [{"title": "JAT Airways Flight 769", "link": "https://wikipedia.org/wiki/JAT_Airways_Flight_769"}, {"title": "Maganik", "link": "https://wikipedia.org/wiki/Maganik"}, {"title": "Podgorica Airport", "link": "https://wikipedia.org/wiki/Podgorica_Airport"}]}, {"year": "1974", "text": "Eastern Air Lines Flight 212 crashes in Charlotte, North Carolina, killing 69 passengers and two crew.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_212\" title=\"Eastern Air Lines Flight 212\">Eastern Air Lines Flight 212</a> crashes in <a href=\"https://wikipedia.org/wiki/Charlotte,_North_Carolina\" title=\"Charlotte, North Carolina\">Charlotte</a>, <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>, killing 69 passengers and two crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_212\" title=\"Eastern Air Lines Flight 212\">Eastern Air Lines Flight 212</a> crashes in <a href=\"https://wikipedia.org/wiki/Charlotte,_North_Carolina\" title=\"Charlotte, North Carolina\">Charlotte</a>, <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>, killing 69 passengers and two crew.", "links": [{"title": "Eastern Air Lines Flight 212", "link": "https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_212"}, {"title": "Charlotte, North Carolina", "link": "https://wikipedia.org/wiki/Charlotte,_North_Carolina"}, {"title": "North Carolina", "link": "https://wikipedia.org/wiki/North_Carolina"}]}, {"year": "1976", "text": "A bomb planted by a Croatian terrorist, <PERSON><PERSON><PERSON>, is found at New York's Grand Central Terminal; one NYPD officer is killed trying to defuse it.", "html": "1976 - A bomb planted by a Croatian terrorist, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bu%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, is found at New York's <a href=\"https://wikipedia.org/wiki/Grand_Central_Terminal\" title=\"Grand Central Terminal\">Grand Central Terminal</a>; one <a href=\"https://wikipedia.org/wiki/NYPD\" class=\"mw-redirect\" title=\"NYPD\">NYPD</a> officer is killed trying to defuse it.", "no_year_html": "A bomb planted by a Croatian terrorist, <a href=\"https://wikipedia.org/wiki/<PERSON>von<PERSON>_Bu%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, is found at New York's <a href=\"https://wikipedia.org/wiki/Grand_Central_Terminal\" title=\"Grand Central Terminal\">Grand Central Terminal</a>; one <a href=\"https://wikipedia.org/wiki/NYPD\" class=\"mw-redirect\" title=\"NYPD\">NYPD</a> officer is killed trying to defuse it.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zvonko_Bu%C5%A1i%C4%87"}, {"title": "Grand Central Terminal", "link": "https://wikipedia.org/wiki/Grand_Central_Terminal"}, {"title": "NYPD", "link": "https://wikipedia.org/wiki/NYPD"}]}, {"year": "1980", "text": "A new constitution of Chile is established under the influence of then Chilean dictator <PERSON><PERSON>, which is subject to controversy in Chile today.", "html": "1980 - A new <a href=\"https://wikipedia.org/wiki/Chilean_Constitution_of_1980\" title=\"Chilean Constitution of 1980\">constitution of Chile</a> is established under the influence of then Chilean dictator <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, which is subject to controversy in Chile today.", "no_year_html": "A new <a href=\"https://wikipedia.org/wiki/Chilean_Constitution_of_1980\" title=\"Chilean Constitution of 1980\">constitution of Chile</a> is established under the influence of then Chilean dictator <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, which is subject to controversy in Chile today.", "links": [{"title": "Chilean Constitution of 1980", "link": "https://wikipedia.org/wiki/Chilean_Constitution_of_1980"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "The international forces that were guaranteeing the safety of Palestinian refugees following Israel's 1982 Invasion of Lebanon leave Beirut. Five days later, several thousand refugees are massacred in the Sabra and Shatila refugee camps by Phalange forces.", "html": "1982 - The international forces that were guaranteeing the safety of <a href=\"https://wikipedia.org/wiki/Palestinian_refugees\" title=\"Palestinian refugees\">Palestinian refugees</a> following <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>'s <a href=\"https://wikipedia.org/wiki/1982_Invasion_of_Lebanon\" class=\"mw-redirect\" title=\"1982 Invasion of Lebanon\">1982 Invasion of Lebanon</a> leave <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>. Five days later, several thousand refugees are massacred in the <a href=\"https://wikipedia.org/wiki/Sabra_and_Shatila_massacre\" title=\"Sabra and Shatila massacre\">Sabra and Shatila refugee camps</a> by Phalange forces.", "no_year_html": "The international forces that were guaranteeing the safety of <a href=\"https://wikipedia.org/wiki/Palestinian_refugees\" title=\"Palestinian refugees\">Palestinian refugees</a> following <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>'s <a href=\"https://wikipedia.org/wiki/1982_Invasion_of_Lebanon\" class=\"mw-redirect\" title=\"1982 Invasion of Lebanon\">1982 Invasion of Lebanon</a> leave <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>. Five days later, several thousand refugees are massacred in the <a href=\"https://wikipedia.org/wiki/Sabra_and_Shatila_massacre\" title=\"Sabra and Shatila massacre\">Sabra and Shatila refugee camps</a> by Phalange forces.", "links": [{"title": "Palestinian refugees", "link": "https://wikipedia.org/wiki/Palestinian_refugees"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "1982 Invasion of Lebanon", "link": "https://wikipedia.org/wiki/1982_Invasion_of_Lebanon"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}, {"title": "Sabra and Shatila massacre", "link": "https://wikipedia.org/wiki/Sabra_and_Shatila_massacre"}]}, {"year": "1985", "text": "Moimenta-Alcafache train crash, the worst railway accident to occur in Portugal.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Moimenta-Alcafache_train_crash\" title=\"Moimenta-Alcafache train crash\">Moimenta-Alcafache train crash</a>, the worst railway accident to occur in <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moimenta-Alcafache_train_crash\" title=\"Moimenta-Alcafache train crash\">Moimenta-Alcafache train crash</a>, the worst railway accident to occur in <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>.", "links": [{"title": "Moimenta-Alcafache train crash", "link": "https://wikipedia.org/wiki/<PERSON>imenta-Alcafache_train_crash"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}]}, {"year": "1989", "text": "Hungary announces that the East German refugees who had been housed in temporary camps were free to leave for West Germany.", "html": "1989 - Hungary announces that the <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East German</a> refugees who had been housed in temporary camps were free to leave for West Germany.", "no_year_html": "Hungary announces that the <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East German</a> refugees who had been housed in temporary camps were free to leave for West Germany.", "links": [{"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}]}, {"year": "1990", "text": "A Faucett Boeing 727 disappears in the Atlantic Ocean while being flown from Malta to Peru.", "html": "1990 - A <a href=\"https://wikipedia.org/wiki/Faucett_Per%C3%BA\" title=\"Faucett Perú\">Faucett</a> Boeing 727 <a href=\"https://wikipedia.org/wiki/1990_Faucett_Per%C3%BA_Boeing_727_disappearance\" title=\"1990 Faucett Perú Boeing 727 disappearance\">disappears</a> in the Atlantic Ocean while being flown from Malta to Peru.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Faucett_Per%C3%BA\" title=\"Faucett Perú\">Faucett</a> Boeing 727 <a href=\"https://wikipedia.org/wiki/1990_Faucett_Per%C3%BA_Boeing_727_disappearance\" title=\"1990 Faucett Perú Boeing 727 disappearance\">disappears</a> in the Atlantic Ocean while being flown from Malta to Peru.", "links": [{"title": "Faucett <PERSON>", "link": "https://wikipedia.org/wiki/Faucett_Per%C3%BA"}, {"title": "1990 Faucett Perú Boeing 727 disappearance", "link": "https://wikipedia.org/wiki/1990_Faucett_Per%C3%BA_Boeing_727_disappearance"}]}, {"year": "1991", "text": "Continental Express Flight 2574 crashes in Colorado County, Texas, near Eagle Lake, killing 11 passengers and three crew.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Continental_Express_Flight_2574\" title=\"Continental Express Flight 2574\">Continental Express Flight 2574</a> crashes in <a href=\"https://wikipedia.org/wiki/Colorado_County,_Texas\" title=\"Colorado County, Texas\">Colorado County, Texas</a>, near <a href=\"https://wikipedia.org/wiki/Eagle_Lake,_Texas\" title=\"Eagle Lake, Texas\">Eagle Lake</a>, killing 11 passengers and three crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Continental_Express_Flight_2574\" title=\"Continental Express Flight 2574\">Continental Express Flight 2574</a> crashes in <a href=\"https://wikipedia.org/wiki/Colorado_County,_Texas\" title=\"Colorado County, Texas\">Colorado County, Texas</a>, near <a href=\"https://wikipedia.org/wiki/Eagle_Lake,_Texas\" title=\"Eagle Lake, Texas\">Eagle Lake</a>, killing 11 passengers and three crew.", "links": [{"title": "Continental Express Flight 2574", "link": "https://wikipedia.org/wiki/Continental_Express_Flight_2574"}, {"title": "Colorado County, Texas", "link": "https://wikipedia.org/wiki/Colorado_County,_Texas"}, {"title": "Eagle Lake, Texas", "link": "https://wikipedia.org/wiki/Eagle_Lake,_Texas"}]}, {"year": "1995", "text": "The first game of the PCA World Chess Championship 1995, pitting incumbent champion <PERSON> against challenger <PERSON><PERSON><PERSON><PERSON>, takes place on the 107th floor of the World Trade Center's South Tower in New York City.", "html": "1995 - The first game of the <a href=\"https://wikipedia.org/wiki/PCA_World_Chess_Championship_1995\" class=\"mw-redirect\" title=\"PCA World Chess Championship 1995\">PCA World Chess Championship 1995</a>, pitting incumbent champion <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> against challenger <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">V<PERSON><PERSON><PERSON></a>, takes place on the 107th floor of the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">World Trade Center</a>'s South Tower in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "no_year_html": "The first game of the <a href=\"https://wikipedia.org/wiki/PCA_World_Chess_Championship_1995\" class=\"mw-redirect\" title=\"PCA World Chess Championship 1995\">PCA World Chess Championship 1995</a>, pitting incumbent champion <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> against challenger <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">V<PERSON><PERSON><PERSON></a>, takes place on the 107th floor of the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">World Trade Center</a>'s South Tower in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "links": [{"title": "PCA World Chess Championship 1995", "link": "https://wikipedia.org/wiki/PCA_World_Chess_Championship_1995"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "World Trade Center (1973-2001)", "link": "https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1997", "text": "NASA's Mars Global Surveyor reaches Mars.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mars_Global_Surveyor\" title=\"Mars Global Surveyor\">Mars Global Surveyor</a> reaches <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mars_Global_Surveyor\" title=\"Mars Global Surveyor\">Mars Global Surveyor</a> reaches <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mars Global Surveyor", "link": "https://wikipedia.org/wiki/Mars_Global_Surveyor"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "1997", "text": "Kurkse tragedy: Fourteen Estonian soldiers of the Baltic Battalion are drowned or die of hypothermia during a training exercise in the Kurkse Strait.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Kurkse_tragedy\" title=\"Kurkse tragedy\">Kurkse tragedy</a>: Fourteen Estonian soldiers of the Baltic Battalion are drowned or die of hypothermia during a training exercise in the Kurkse Strait.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kurkse_tragedy\" title=\"Kurkse tragedy\">Kurkse tragedy</a>: Fourteen Estonian soldiers of the Baltic Battalion are drowned or die of hypothermia during a training exercise in the Kurkse Strait.", "links": [{"title": "Kurkse tragedy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_tragedy"}]}, {"year": "1997", "text": "After a nationwide referendum, Scotland votes to establish a devolved parliament within the United Kingdom.", "html": "1997 - After a <a href=\"https://wikipedia.org/wiki/1997_Scottish_devolution_referendum\" title=\"1997 Scottish devolution referendum\">nationwide referendum</a>, Scotland votes to establish a <a href=\"https://wikipedia.org/wiki/Scottish_Parliament\" title=\"Scottish Parliament\">devolved parliament</a> within the United Kingdom.", "no_year_html": "After a <a href=\"https://wikipedia.org/wiki/1997_Scottish_devolution_referendum\" title=\"1997 Scottish devolution referendum\">nationwide referendum</a>, Scotland votes to establish a <a href=\"https://wikipedia.org/wiki/Scottish_Parliament\" title=\"Scottish Parliament\">devolved parliament</a> within the United Kingdom.", "links": [{"title": "1997 Scottish devolution referendum", "link": "https://wikipedia.org/wiki/1997_Scottish_devolution_referendum"}, {"title": "Scottish Parliament", "link": "https://wikipedia.org/wiki/Scottish_Parliament"}]}, {"year": "2001", "text": "The September 11 attacks, a series of coordinated terrorist attacks killing 2,977 people using four aircraft hijacked by 19 members of al-Qaeda. Two aircraft crash into the World Trade Center in New York City, a third crashes into The Pentagon in Arlington County, Virginia, and a fourth into a field near Shanksville, Pennsylvania.", "html": "2001 - The <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>, a series of coordinated terrorist attacks killing 2,977 people using four aircraft hijacked by 19 members of <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a>. Two aircraft crash into the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">World Trade Center</a> in New York City, a third crashes into <a href=\"https://wikipedia.org/wiki/The_Pentagon\" title=\"The Pentagon\">The Pentagon</a> in <a href=\"https://wikipedia.org/wiki/Arlington_County,_Virginia\" title=\"Arlington County, Virginia\">Arlington County, Virginia</a>, and a fourth into a field near <a href=\"https://wikipedia.org/wiki/Shanksville,_Pennsylvania\" title=\"Shanksville, Pennsylvania\">Shanksville, Pennsylvania</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>, a series of coordinated terrorist attacks killing 2,977 people using four aircraft hijacked by 19 members of <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a>. Two aircraft crash into the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">World Trade Center</a> in New York City, a third crashes into <a href=\"https://wikipedia.org/wiki/The_Pentagon\" title=\"The Pentagon\">The Pentagon</a> in <a href=\"https://wikipedia.org/wiki/Arlington_County,_Virginia\" title=\"Arlington County, Virginia\">Arlington County, Virginia</a>, and a fourth into a field near <a href=\"https://wikipedia.org/wiki/Shanksville,_Pennsylvania\" title=\"Shanksville, Pennsylvania\">Shanksville, Pennsylvania</a>.", "links": [{"title": "September 11 attacks", "link": "https://wikipedia.org/wiki/September_11_attacks"}, {"title": "Al-Qaeda", "link": "https://wikipedia.org/wiki/Al-Qaeda"}, {"title": "World Trade Center (1973-2001)", "link": "https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)"}, {"title": "The Pentagon", "link": "https://wikipedia.org/wiki/The_Pentagon"}, {"title": "Arlington County, Virginia", "link": "https://wikipedia.org/wiki/Arlington_County,_Virginia"}, {"title": "Shanksville, Pennsylvania", "link": "https://wikipedia.org/wiki/Shanksville,_Pennsylvania"}]}, {"year": "2007", "text": "Russia tests the largest conventional weapon ever, the Father of All Bombs.", "html": "2007 - Russia tests the largest <a href=\"https://wikipedia.org/wiki/Conventional_weapon\" title=\"Conventional weapon\">conventional weapon</a> ever, the <a href=\"https://wikipedia.org/wiki/Father_of_All_Bombs\" title=\"Father of All Bombs\">Father of All Bombs</a>.", "no_year_html": "Russia tests the largest <a href=\"https://wikipedia.org/wiki/Conventional_weapon\" title=\"Conventional weapon\">conventional weapon</a> ever, the <a href=\"https://wikipedia.org/wiki/Father_of_All_Bombs\" title=\"Father of All Bombs\">Father of All Bombs</a>.", "links": [{"title": "Conventional weapon", "link": "https://wikipedia.org/wiki/Conventional_weapon"}, {"title": "Father of All Bombs", "link": "https://wikipedia.org/wiki/Father_of_All_Bombs"}]}, {"year": "2008", "text": "A major Channel Tunnel fire breaks out on a freight train, resulting in the closure of part of the tunnel for six months.", "html": "2008 - A major <a href=\"https://wikipedia.org/wiki/2008_Channel_Tunnel_fire\" title=\"2008 Channel Tunnel fire\">Channel Tunnel fire</a> breaks out on a freight train, resulting in the closure of part of the <a href=\"https://wikipedia.org/wiki/Channel_Tunnel\" title=\"Channel Tunnel\">tunnel</a> for six months.", "no_year_html": "A major <a href=\"https://wikipedia.org/wiki/2008_Channel_Tunnel_fire\" title=\"2008 Channel Tunnel fire\">Channel Tunnel fire</a> breaks out on a freight train, resulting in the closure of part of the <a href=\"https://wikipedia.org/wiki/Channel_Tunnel\" title=\"Channel Tunnel\">tunnel</a> for six months.", "links": [{"title": "2008 Channel Tunnel fire", "link": "https://wikipedia.org/wiki/2008_Channel_Tunnel_fire"}, {"title": "Channel Tunnel", "link": "https://wikipedia.org/wiki/Channel_Tunnel"}]}, {"year": "2011", "text": "A dedication ceremony is held at the United States National September 11 Memorial on the 10th anniversary of the September 11 attacks in New York City, and the memorial opens to family members.", "html": "2011 - A dedication ceremony is held at the United States <a href=\"https://wikipedia.org/wiki/National_September_11_Memorial_%26_Museum\" title=\"National September 11 Memorial &amp; Museum\">National September 11 Memorial</a> on the 10th anniversary of the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a> in New York City, and the memorial opens to family members.", "no_year_html": "A dedication ceremony is held at the United States <a href=\"https://wikipedia.org/wiki/National_September_11_Memorial_%26_Museum\" title=\"National September 11 Memorial &amp; Museum\">National September 11 Memorial</a> on the 10th anniversary of the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a> in New York City, and the memorial opens to family members.", "links": [{"title": "National September 11 Memorial & Museum", "link": "https://wikipedia.org/wiki/National_September_11_Memorial_%26_Museum"}, {"title": "September 11 attacks", "link": "https://wikipedia.org/wiki/September_11_attacks"}]}, {"year": "2012", "text": "A total of 315 people are killed in two garment factory fires in Pakistan.", "html": "2012 - A total of 315 people are killed in <a href=\"https://wikipedia.org/wiki/2012_Pakistan_garment_factory_fires\" class=\"mw-redirect\" title=\"2012 Pakistan garment factory fires\">two garment factory fires</a> in <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>.", "no_year_html": "A total of 315 people are killed in <a href=\"https://wikipedia.org/wiki/2012_Pakistan_garment_factory_fires\" class=\"mw-redirect\" title=\"2012 Pakistan garment factory fires\">two garment factory fires</a> in <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>.", "links": [{"title": "2012 Pakistan garment factory fires", "link": "https://wikipedia.org/wiki/2012_Pakistan_garment_factory_fires"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "2012", "text": "The U.S. embassy in Benghazi, Libya is attacked, resulting in four deaths.", "html": "2012 - The U.S. embassy in <a href=\"https://wikipedia.org/wiki/Benghazi\" title=\"Benghazi\">Benghazi</a>, Libya is <a href=\"https://wikipedia.org/wiki/2012_Benghazi_attack\" title=\"2012 Benghazi attack\">attacked</a>, resulting in four deaths.", "no_year_html": "The U.S. embassy in <a href=\"https://wikipedia.org/wiki/Benghazi\" title=\"Benghazi\">Benghazi</a>, Libya is <a href=\"https://wikipedia.org/wiki/2012_Benghazi_attack\" title=\"2012 Benghazi attack\">attacked</a>, resulting in four deaths.", "links": [{"title": "Benghazi", "link": "https://wikipedia.org/wiki/Benghazi"}, {"title": "2012 Benghazi attack", "link": "https://wikipedia.org/wiki/2012_Benghazi_attack"}]}, {"year": "2015", "text": "A crane collapses onto the Masjid al-Haram mosque in Saudi Arabia, killing 111 people and injuring 394 others.", "html": "2015 - A <a href=\"https://wikipedia.org/wiki/Mecca_crane_collapse\" title=\"Mecca crane collapse\">crane collapses</a> onto the <a href=\"https://wikipedia.org/wiki/Masjid_al-Haram\" title=\"Masjid al-Haram\">Masjid al-Haram</a> mosque in <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, killing 111 people and injuring 394 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Mecca_crane_collapse\" title=\"Mecca crane collapse\">crane collapses</a> onto the <a href=\"https://wikipedia.org/wiki/Masjid_al-Haram\" title=\"Masjid al-Haram\">Masjid al-Haram</a> mosque in <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, killing 111 people and injuring 394 others.", "links": [{"title": "Mecca crane collapse", "link": "https://wikipedia.org/wiki/Mecca_crane_collapse"}, {"title": "Ma<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Masjid_al-Haram"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}]}, {"year": "2023", "text": "The Libyan city of Derna experiences catastrophic floods after Storm Daniel causes two dams to collapse, killing over 11,300 people.", "html": "2023 - The Libyan city of <a href=\"https://wikipedia.org/wiki/Derna,_Libya\" title=\"Derna, Libya\">Derna</a> experiences catastrophic floods after <a href=\"https://wikipedia.org/wiki/Storm_Daniel\" title=\"Storm Daniel\"><PERSON></a> causes <a href=\"https://wikipedia.org/wiki/Derna_dam_collapses\" title=\"Derna dam collapses\">two dams to collapse</a>, killing over 11,300 people.", "no_year_html": "The Libyan city of <a href=\"https://wikipedia.org/wiki/Derna,_Libya\" title=\"Derna, Libya\">Derna</a> experiences catastrophic floods after <a href=\"https://wikipedia.org/wiki/Storm_Daniel\" title=\"Storm Daniel\"><PERSON></a> causes <a href=\"https://wikipedia.org/wiki/Derna_dam_collapses\" title=\"Derna dam collapses\">two dams to collapse</a>, killing over 11,300 people.", "links": [{"title": "Derna, Libya", "link": "https://wikipedia.org/wiki/Derna,_Libya"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Daniel"}, {"title": "Derna dam collapses", "link": "https://wikipedia.org/wiki/Der<PERSON>_dam_collapses"}]}, {"year": "2024", "text": "Hurricane <PERSON><PERSON><PERSON> impacts the Gulf of Mexico, as a Category 2 hurricane.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Hurricane_Francine_(2024)\" class=\"mw-redirect\" title=\"Hurricane Francine (2024)\">Hurricane Francine</a> impacts the <a href=\"https://wikipedia.org/wiki/Gulf_of_Mexico\" title=\"Gulf of Mexico\">Gulf of Mexico</a>, as a Category 2 hurricane.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Francine_(2024)\" class=\"mw-redirect\" title=\"Hurricane Francine (2024)\">Hurricane Fr<PERSON><PERSON></a> impacts the <a href=\"https://wikipedia.org/wiki/Gulf_of_Mexico\" title=\"Gulf of Mexico\">Gulf of Mexico</a>, as a Category 2 hurricane.", "links": [{"title": "Hurricane Francine (2024)", "link": "https://wikipedia.org/wiki/Hurricane_Francine_(2024)"}, {"title": "Gulf of Mexico", "link": "https://wikipedia.org/wiki/Gulf_of_Mexico"}]}], "Births": [{"year": "600", "text": "Yuk<PERSON><PERSON> Ch'een II, Mayan ruler", "html": "600 - <a href=\"https://wikipedia.org/wiki/Yuknoom_Ch%27een_II\" class=\"mw-redirect\" title=\"Yuknoom Ch'een II\">Yuknoom Ch'een II</a>, Mayan ruler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yuknoom_Ch%27een_II\" class=\"mw-redirect\" title=\"Yuknoom Ch'een II\">Yuknoom Ch'een II</a>, Mayan ruler", "links": [{"title": "Yuknoom Ch'een II", "link": "https://wikipedia.org/wiki/Yuknoom_Ch%27een_II"}]}, {"year": "1182", "text": "<PERSON><PERSON>, Japanese shōgun (d. 1204)", "html": "1182 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoriie\" title=\"Minamoto no Yoriie\"><PERSON><PERSON> no Yoriie</a>, Japanese shōgun (d. 1204)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoriie\" title=\"Minamoto no Yoriie\"><PERSON><PERSON> no Yoriie</a>, Japanese shōgun (d. 1204)", "links": [{"title": "<PERSON><PERSON> no Yoriie", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1318", "text": "<PERSON> of Lancaster, countess of Arundel (d. 1372)", "html": "1318 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lancaster\"><PERSON> Lancaster</a>, countess of Arundel (d. 1372)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lancaster\"><PERSON> Lancaster</a>, countess of Arundel (d. 1372)", "links": [{"title": "<PERSON> of Lancaster", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1465", "text": "<PERSON>, Italian poet (d. 1536)", "html": "1465 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet (d. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet (d. 1536)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1476", "text": "<PERSON> of Savoy, French regent (d. 1531)", "html": "1476 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a>, French regent (d. 1531)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a>, French regent (d. 1531)", "links": [{"title": "<PERSON> of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1494", "text": "<PERSON> of Brunswick<PERSON>Lüne<PERSON>, Duchess of Guelders (1518-1538) (d. 1572)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Brunswick-L%C3%BCneburg,_Duchess_of_Guelders\" title=\"<PERSON> of Brunswick-Lüneburg, Duchess of Guelders\"><PERSON> of Brunswick-Lüneburg, Duchess of Guelders</a> (1518-1538) (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Brunswick-L%C3%BCneburg,_Duchess_of_Guelders\" title=\"Elisabeth of Brunswick-Lüneburg, Duchess of Guelders\"><PERSON> of Brunswick-Lüneburg, Duchess of Guelders</a> (1518-1538) (d. 1572)", "links": [{"title": "<PERSON> of Brunswick-Lüneburg, Duchess of Guelders", "link": "https://wikipedia.org/wiki/Elisabeth_<PERSON>_Brunswick-L%C3%BCneburg,_Duchess_of_Guelders"}]}, {"year": "1522", "text": "<PERSON><PERSON><PERSON>, Italian ornithologist and botanist (d. 1605)", "html": "1522 - <a href=\"https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian ornithologist and botanist (d. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian ornithologist and botanist (d. 1605)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ndi"}]}, {"year": "1524", "text": "<PERSON>, French poet and author (d. 1585)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1585)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1525", "text": "<PERSON>, Elector of Brandenburg (d. 1598)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (d. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (d. 1598)", "links": [{"title": "<PERSON>, Elector of Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg"}]}, {"year": "1557", "text": "<PERSON>, Spanish priest and founder of Piarists (d. 1648)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish priest and founder of <a href=\"https://wikipedia.org/wiki/Piarists\" title=\"Piaris<PERSON>\">Pi<PERSON><PERSON></a> (d. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish priest and founder of <a href=\"https://wikipedia.org/wiki/Piarists\" title=\"Piarists\">Pi<PERSON><PERSON></a> (d. 1648)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Piarists", "link": "https://wikipedia.org/wiki/Piarists"}]}, {"year": "1572", "text": "<PERSON><PERSON>, Imperial Prince of the Royal House of Timur (d. 1604)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Mughal_prince)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Mughal prince)\"><PERSON><PERSON></a>, Imperial Prince of the Royal House of Timur (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Mughal_prince)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Mughal prince)\"><PERSON><PERSON></a>, Imperial Prince of the Royal House of Timur (d. 1604)", "links": [{"title": "<PERSON><PERSON> (Mughal prince)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(Mughal_prince)"}]}, {"year": "1578", "text": "<PERSON>, Catholic cardinal (d. 1667)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catholic cardinal (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catholic cardinal (d. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1611", "text": "<PERSON> d<PERSON>Auvergne, <PERSON><PERSON><PERSON>, French general (d. 1675)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Tour_d%27<PERSON><PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_Turenne\" class=\"mw-redirect\" title=\"<PERSON> Tour d'Auvergne, Vic<PERSON><PERSON> de Turenne\"><PERSON> d'Auvergne, <PERSON><PERSON><PERSON></a>, French general (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Tour_d%27<PERSON><PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_Turenne\" class=\"mw-redirect\" title=\"Henri <PERSON> la Tour d'Auvergne, Vic<PERSON><PERSON> de Turenne\"><PERSON> d'Auvergne, <PERSON><PERSON><PERSON></a>, French general (d. 1675)", "links": [{"title": "<PERSON> d'Auvergne, Vicomte de Turenne", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_d%27A<PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1681", "text": "<PERSON>, German academic and jurist (d. 1741)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and jurist (d. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and jurist (d. 1741)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1700", "text": "<PERSON>, Scottish poet and playwright (d. 1748)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet,_born_1700)\" title=\"<PERSON> (poet, born 1700)\"><PERSON></a>, Scottish poet and playwright (d. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet,_born_1700)\" title=\"<PERSON> (poet, born 1700)\"><PERSON></a>, Scottish poet and playwright (d. 1748)", "links": [{"title": "<PERSON> (poet, born 1700)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet,_born_1700)"}]}, {"year": "1711", "text": "<PERSON>, English organist and composer (d. 1779)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (d. 1779)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1723", "text": "<PERSON>, German author and educator (d. 1790)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and educator (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and educator (d. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1751", "text": "Princess <PERSON> of Saxe-Meiningen (d. 1827)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/Princess_Charlotte_of_Saxe-Meiningen\" title=\"Princess <PERSON> of Saxe-Meiningen\">Princess <PERSON> of Saxe-Meiningen</a> (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Saxe-Meiningen\" title=\"Princess <PERSON> of Saxe-Meiningen\">Princess <PERSON> of Saxe-Meiningen</a> (d. 1827)", "links": [{"title": "Princess <PERSON> of Saxe-Meiningen", "link": "https://wikipedia.org/wiki/Princess_Charlotte_<PERSON>_Saxe-Meiningen"}]}, {"year": "1764", "text": "<PERSON><PERSON>, Italian organist and composer (d. 1837)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/Vale<PERSON>_<PERSON>i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian organist and composer (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valentino_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian organist and composer (d. 1837)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valentino_Fioravanti"}]}, {"year": "1771", "text": "<PERSON><PERSON>, Scottish surgeon and explorer (d. 1806)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/Mungo_Park_(explorer)\" title=\"Mungo Park (explorer)\">Mungo Park</a>, Scottish surgeon and explorer (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mungo_Park_(explorer)\" title=\"Mungo Park (explorer)\">Mungo Park</a>, Scottish surgeon and explorer (d. 1806)", "links": [{"title": "Mungo <PERSON> (explorer)", "link": "https://wikipedia.org/wiki/Mungo_Park_(explorer)"}]}, {"year": "1786", "text": "<PERSON>, German-Danish pianist and composer (d. 1832)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Danish pianist and composer (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Danish pianist and composer (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, German mineralogist and physicist (d. 1895)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mineralogist and physicist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mineralogist and physicist (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, American lawyer and politician, 13th Lieutenant Governor of New York (d. 1866)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_New_York\" title=\"Lieutenant Governor of New York\">Lieutenant Governor of New York</a> (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_New_York\" title=\"Lieutenant Governor of New York\">Lieutenant Governor of New York</a> (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of New York", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_New_York"}]}, {"year": "1816", "text": "<PERSON>, German lens maker, created the Optical instrument (d. 1888)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lens maker, created the <a href=\"https://wikipedia.org/wiki/Optical_instrument\" title=\"Optical instrument\">Optical instrument</a> (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lens maker, created the <a href=\"https://wikipedia.org/wiki/Optical_instrument\" title=\"Optical instrument\">Optical instrument</a> (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Optical instrument", "link": "https://wikipedia.org/wiki/Optical_instrument"}]}, {"year": "1825", "text": "<PERSON>, Bohemian-Austrian musicologist and critic (d. 1904)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bohemian-Austrian musicologist and critic (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bohemian-Austrian musicologist and critic (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, American painter (d. 1908)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, American painter (d. 1908)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)"}]}, {"year": "1836", "text": "<PERSON><PERSON>, American journalist, author, and explorer (d. 1870)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON>tz <PERSON></a>, American journalist, author, and explorer (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American journalist, author, and explorer (d. 1870)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, Irish-American archbishop (d. 1918)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/John_Ireland_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Irish-American archbishop (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Ireland_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Irish-American archbishop (d. 1918)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/John_<PERSON>_(bishop)"}]}, {"year": "1847", "text": "<PERSON>, American astronomer and academic (d. 1921)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian author and playwright (d. 1905)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian author and playwright (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian author and playwright (d. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, New Zealand rugby player (d. 1934)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 1934)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1861", "text": "<PERSON><PERSON>, Finnish author and journalist (d. 1921)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author and journalist (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author and journalist (d. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1862", "text": "<PERSON>, 1st Viscount <PERSON> of Vimy, English field marshal and politician, 12th Governor General of Canada (d. 1935)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON><PERSON>_of_Vimy\" title=\"<PERSON>, 1st Viscount <PERSON>ng of Vimy\"><PERSON>, 1st Viscount <PERSON> of Vimy</a>, English field marshal and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON><PERSON>_of_Vimy\" title=\"<PERSON>, 1st Viscount <PERSON><PERSON> of Vimy\"><PERSON>, 1st Viscount <PERSON><PERSON> of Vimy</a>, English field marshal and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1935)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON> of Vimy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>_of_V<PERSON><PERSON>"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1862", "text": "<PERSON><PERSON>, American physician (d. 1910)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Hawley Harvey Crippen\"><PERSON><PERSON></a>, American physician (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Hawley Harvey Crippen\"><PERSON><PERSON></a>, American physician (d. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON><PERSON>, American short story writer (d. 1910)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American short story writer (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American short story writer (d. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON>, Latvian poet and playwright (d. 1929)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian poet and playwright (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian poet and playwright (d. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rainis"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, 10th Prince of Sulmona, Italian racing driver, mountaineer, and politician (d. 1927)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_Prince_of_Sulmona\" title=\"<PERSON><PERSON><PERSON>, 10th Prince of Sulmona\"><PERSON><PERSON><PERSON>, 10th Prince of Sulmona</a>, Italian racing driver, mountaineer, and politician (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_Prince_of_Sulmona\" title=\"<PERSON><PERSON><PERSON>, 10th Prince of Sulmona\"><PERSON><PERSON><PERSON>, 10th Prince of Sulmona</a>, Italian racing driver, mountaineer, and politician (d. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>, 10th Prince of Sulmona", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_Prince_of_Sulmona"}]}, {"year": "1876", "text": "<PERSON>, Australian sprinter (d. 1924)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sprinter (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sprinter (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Polish-Russian academic and politician (d. 1926)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Russian academic and politician (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Russian academic and politician (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, English physicist, astronomer, and mathematician (d. 1946)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physicist, astronomer, and mathematician (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physicist, astronomer, and mathematician (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, French engineer (d. 1962)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>\" title=\"Louis <PERSON>\"><PERSON></a>, French engineer (d. 1962)", "links": [{"title": "Louis <PERSON>", "link": "https://wikipedia.org/wiki/Louis_Coatalen"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Indian activist and politician (d. 1974)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist and politician (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist and politician (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nick"}]}, {"year": "1885", "text": "<PERSON><PERSON> <PERSON><PERSON>, English novelist, poet, playwright, and critic (d. 1930)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, poet, playwright, and critic (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, poet, playwright, and critic (d. 1930)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American composer and conductor (d. 1949)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American historian, author, and educator (d. 1949)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and educator (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and educator (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, English-Greek racing driver and engineer (d. 1974)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Greek racing driver and engineer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Greek racing driver and engineer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Indian philosopher and Gandhian, <PERSON><PERSON><PERSON> (d. 1982)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bhave\" title=\"Vinoba Bhave\"><PERSON><PERSON><PERSON></a>, Indian philosopher and Gandhian, <a href=\"https://wikipedia.org/wiki/B<PERSON>t_Ratna\" title=\"Bhara<PERSON> Ratna\"><PERSON><PERSON><PERSON></a> Awardee (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bhave\" title=\"Vinoba Bhave\"><PERSON><PERSON><PERSON></a>, Indian philosopher and Gandhian, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>t_Ratna\" title=\"Bhara<PERSON> Ratna\"><PERSON><PERSON><PERSON></a> Awardee (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ob<PERSON>_Bhave"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>t_<PERSON>na"}]}, {"year": "1898", "text": "<PERSON>, English field marshal and politician, British High Commissioner in Malaya (d. 1979)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/British_High_Commissioner_in_Malaya\" class=\"mw-redirect\" title=\"British High Commissioner in Malaya\">British High Commissioner in Malaya</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/British_High_Commissioner_in_Malaya\" class=\"mw-redirect\" title=\"British High Commissioner in Malaya\">British High Commissioner in Malaya</a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "British High Commissioner in Malaya", "link": "https://wikipedia.org/wiki/British_High_Commissioner_in_Malaya"}]}, {"year": "1899", "text": "<PERSON>, German politician (d. 1945)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American singer-songwriter and politician, 47th Governor of Louisiana (d. 2000)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Louisiana", "link": "https://wikipedia.org/wiki/Governor_of_Louisiana"}]}, {"year": "1899", "text": "<PERSON>, Estonian wrestler and coach (d. 1953)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian wrestler and coach (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian wrestler and coach (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON> <PERSON><PERSON>, American farmer and businessman, founded Gold Kist (d. 1999)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American farmer and businessman, founded <a href=\"https://wikipedia.org/wiki/Gold_Kist\" title=\"Gold Kist\">Gold Kist</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American farmer and businessman, founded <a href=\"https://wikipedia.org/wiki/Gold_Kist\" title=\"Gold Kist\">Gold Kist</a> (d. 1999)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Gold Kist", "link": "https://wikipedia.org/wiki/<PERSON>_Kist"}]}, {"year": "1903", "text": "<PERSON>, German sociologist and philosopher (d. 1969)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American lieutenant and painter (d. 1984)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and painter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and painter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Estonian lawyer and jurist (d. 2010)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and jurist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and jurist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Russian pianist and educator (d. 1974)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and educator (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and educator (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lev_O<PERSON>in"}]}, {"year": "1908", "text": "<PERSON><PERSON>, English journalist (d. 1981)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Indian cricketer (d. 2000)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Cuban singer-songwriter and pianist (d. 1971)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban singer-songwriter and pianist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban singer-songwriter and pianist (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American football player and coach (d. 1983)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON><PERSON>, Venezuelan physician and academic (d. 2014)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON><PERSON>_<PERSON>vit\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan physician and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON><PERSON>_<PERSON>vit\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan physician and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jacinto_Convit"}]}, {"year": "1914", "text": "Serbian Patriarch <PERSON><PERSON><PERSON> (d. 2009)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Serbian_Patriarch_Pavle_II\" class=\"mw-redirect\" title=\"Serbian Patriarch Pavle II\">Serbian Patriarch Pa<PERSON><PERSON> II</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Serbian_Patriarch_Pavle_II\" class=\"mw-redirect\" title=\"Serbian Patriarch Pavle II\">Serbian Patriarch Pa<PERSON><PERSON> II</a> (d. 2009)", "links": [{"title": "Serbian Patriarch <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Serbian_Patriarch_<PERSON><PERSON><PERSON>_II"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian jeweller (d. 2014)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Dajikaka_Gadgil\" title=\"Dajikaka Gadgil\"><PERSON><PERSON><PERSON><PERSON></a>, Indian jeweller (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dajikaka_Gadgil\" title=\"Dajikaka Gadgil\"><PERSON><PERSON><PERSON><PERSON></a>, Indian jeweller (d. 2014)", "links": [{"title": "Dajikaka Gadgil", "link": "https://wikipedia.org/wiki/Dajikaka_Gadgil"}]}, {"year": "1916", "text": "<PERSON>, American film producer, co-founded NFL Films (d. 2015)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer, co-founded <a href=\"https://wikipedia.org/wiki/NFL_Films\" title=\"NFL Films\">NFL Films</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer, co-founded <a href=\"https://wikipedia.org/wiki/NFL_Films\" title=\"NFL Films\">NFL Films</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "NFL Films", "link": "https://wikipedia.org/wiki/NFL_Films"}]}, {"year": "1917", "text": "<PERSON>, American colonel and pilot (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Czech-born English actor (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-born English actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-born English actor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Filipino soldier, lawyer, and politician, 10th President of the Philippines (d. 1989)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino soldier, lawyer, and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino soldier, lawyer, and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1917", "text": "<PERSON>, English-American journalist and author (d. 1996)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and author (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, French art dealer and horse breeder (d. 2001)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French art dealer and horse breeder (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French art dealer and horse breeder (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American tribal leader and colonel (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>ord_Bearskin\" title=\"Leaford Bearskin\"><PERSON><PERSON></a>, American tribal leader and colonel (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ord_Bearskin\" title=\"<PERSON>ord Bearskin\"><PERSON><PERSON></a>, American tribal leader and colonel (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bearskin"}]}, {"year": "1923", "text": "<PERSON>, American actress (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian composer and academic (d. 1984)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian composer and academic (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian composer and academic (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>li<PERSON>_<PERSON>nja<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English actor (d. 1982)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American soldier, engineer, and politician (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, engineer, and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, engineer, and politician (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American football player and coach (d. 2000)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Czech-Canadian pharmacologist and educator (d. 2006)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Canadian pharmacologist and educator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Canadian pharmacologist and educator (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian soldier and composer (d. 1999)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier and composer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier and composer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American baseball player (d. 2005)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Australian rugby league player and coach (d. 2011)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American soldier and businessman (d. 1996)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and businessman (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American soldier and businessman (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, American sergeant, lawyer, and politician, 37th Governor of Florida (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sergeant, lawyer, and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sergeant, lawyer, and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Florida", "link": "https://wikipedia.org/wiki/Governor_of_Florida"}]}, {"year": "1928", "text": "<PERSON>, American actor (d. 2024)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Venezuelan baseball player and manager (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(third_baseman)\" title=\"<PERSON> (third baseman)\"><PERSON></a>, Venezuelan baseball player and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(third_baseman)\" title=\"<PERSON> (third baseman)\"><PERSON></a>, Venezuelan baseball player and manager (d. 2014)", "links": [{"title": "<PERSON> (third baseman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ar<PERSON>%C3%<PERSON><PERSON>_(third_baseman)"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian playwright (d. 1981)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Primo%C5%B<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian playwright (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Primo%C5%B<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian playwright (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Primo%C5%B<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English lawyer and politician, Secretary of State for Northern Ireland (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland\" title=\"Secretary of State for Northern Ireland\">Secretary of State for Northern Ireland</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland\" title=\"Secretary of State for Northern Ireland\">Secretary of State for Northern Ireland</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Northern Ireland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, American actress and dancer (d. 1987)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and dancer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and dancer (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, French author and illustrator (d. 1998)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and illustrator (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and illustrator (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Egyptian footballer, manager, and actor (d. 2002)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer, manager, and actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer, manager, and actor (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, German historian and academic (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German historian and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German historian and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English lawyer and judge (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge (d. 2021)", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)"}]}, {"year": "1933", "text": "<PERSON>, American author and activist (d. 2002)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Italian tennis player", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Estonian composer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Arvo_P%C3%A4rt\" title=\"<PERSON>r<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arvo_P%C3%A4rt\" title=\"<PERSON>rvo <PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arvo_P%C3%A4rt"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Russian general, pilot, and astronaut (d. 2000)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general, pilot, and astronaut (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general, pilot, and astronaut (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Czech actor, director, and playwright (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech actor, director, and playwright (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech actor, director, and playwright (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pavel_Landovsk%C3%BD"}]}, {"year": "1937", "text": "<PERSON>, American captain, pilot, and astronaut", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "Queen <PERSON><PERSON> of Belgium", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON>_of_Belgium\" title=\"Queen <PERSON><PERSON> of Belgium\">Queen <PERSON><PERSON> of Belgium</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON>_of_Belgium\" title=\"Queen <PERSON><PERSON> of Belgium\">Queen <PERSON><PERSON> of Belgium</a>", "links": [{"title": "Queen <PERSON><PERSON> of Belgium", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_of_Belgium"}]}, {"year": "1938", "text": "<PERSON>, English chemist and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, English chemist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American businessman, co-founded Adobe Systems (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Adobe_Systems\" class=\"mw-redirect\" title=\"Adobe Systems\">Adobe Systems</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Adobe_Systems\" class=\"mw-redirect\" title=\"Adobe Systems\">Adobe Systems</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Adobe Systems", "link": "https://wikipedia.org/wiki/Adobe_Systems"}]}, {"year": "1940", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Vietnamese politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/N%C3%B4ng_%C4%90%E1%BB%A9c_M%E1%BA%A1nh\" title=\"Nông Đức M<PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%B4ng_%C4%90%E1%BB%A9c_M%E1%BA%A1nh\" title=\"Nông Đứ<PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%B4ng_%C4%90%E1%BB%A9c_M%E1%BA%A1nh"}]}, {"year": "1940", "text": "<PERSON>, American lawyer (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>, Civil Rights activist and Little Rock Nine member", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>ni<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>ni<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Civil Rights activist and <a href=\"https://wikipedia.org/wiki/Little_Rock_Nine\" title=\"Little Rock Nine\">Little Rock Nine</a> member", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ni<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Civil Rights activist and <a href=\"https://wikipedia.org/wiki/Little_Rock_Nine\" title=\"Little Rock Nine\">Little Rock Nine</a> member", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "Little Rock Nine", "link": "https://wikipedia.org/wiki/Little_Rock_Nine"}]}, {"year": "1942", "text": "<PERSON>, American actress, singer, and dancer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lola Falana\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lola Falana\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "Lola Falana", "link": "https://wikipedia.org/wiki/<PERSON>_Falana"}]}, {"year": "1943", "text": "<PERSON>, Canadian chemist and businessman", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Caill%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian chemist and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Caill%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian chemist and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Caill%C3%A9"}]}, {"year": "1943", "text": "<PERSON>, American musician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hart\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, New Zealand-English journalist and actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English journalist and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English journalist and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Brazilian footballer (d. 1974)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1944)\" title=\"<PERSON><PERSON> (footballer, born 1944)\"><PERSON><PERSON></a>, Brazilian footballer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1944)\" title=\"<PERSON><PERSON> (footballer, born 1944)\"><PERSON><PERSON></a>, Brazilian footballer (d. 1974)", "links": [{"title": "<PERSON><PERSON> (footballer, born 1944)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1944)"}]}, {"year": "1944", "text": "<PERSON>, Belgian educator and politician, Mayor of Brussels (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian educator and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_the_City_of_Brussels\" title=\"List of mayors of the City of Brussels\">Mayor of Brussels</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian educator and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_the_City_of_Brussels\" title=\"List of mayors of the City of Brussels\">Mayor of Brussels</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of mayors of the City of Brussels", "link": "https://wikipedia.org/wiki/List_of_mayors_of_the_City_of_Brussels"}]}, {"year": "1945", "text": "<PERSON>, German footballer and manager (d. 2024)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer and conductor (d. 2021)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and conductor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and conductor (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American rock singer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American serial killer (d. 2009)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English-Scottish singer-songwriter and guitarist (d. 2009)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish singer-songwriter and guitarist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish singer-songwriter and guitarist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English rugby player and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American racing driver (d. 2021)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian biochemist and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biochemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biochemist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian footballer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English motorcycle racer and sportscaster (d. 2003)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer and sportscaster (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer and sportscaster (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English-Dutch mathematician and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Dutch mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Dutch mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American basketball player and coach (d. 2019)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Argentinian rugby player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English soprano", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, English-South African journalist and author (d. 2023)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-South African journalist and author (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-South African journalist and author (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jani_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Former Montserrat Deputy Governor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Former Montserrat Deputy Governor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Former Montserrat Deputy Governor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Australian singer-songwriter (d. 2023)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American psychologist and academic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, American actress and director", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor and baseball player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, South African golfer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, South African golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, South African golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>(golfer)"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Japanese physicist and academic, Nobel Prize laureate", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1960", "text": "<PERSON>, American actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Virginia_Madsen\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Madsen\" title=\"<PERSON>sen\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virginia_Madsen"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Pakistani poet and educator (d. 2012)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani poet and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani poet and educator (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Belgian politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Filip_Dewinter"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Ukrainian pianist and composer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Victoria_Poleva\" title=\"<PERSON>\"><PERSON></a>, Ukrainian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Poleva\" title=\"<PERSON> Pole<PERSON>\"><PERSON></a>, Ukrainian pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victoria_Poleva"}]}, {"year": "1962", "text": "<PERSON>, Spanish footballer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Julio <PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julio_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Syrian politician, 21st President of Syria", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_Syria\" title=\"President of Syria\">President of Syria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_Syria\" title=\"President of Syria\">President of Syria</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Syria", "link": "https://wikipedia.org/wiki/President_of_Syria"}]}, {"year": "1965", "text": "<PERSON>, American wrestling promoter, manager, and journalist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestling promoter, manager, and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestling promoter, manager, and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American singer-songwriter, musician, and DJ", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, musician, and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, musician, and DJ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>by"}]}, {"year": "1967", "text": "<PERSON>, American financial journalist and television personality", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financial journalist and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financial journalist and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter, pianist, actor, and talk show host", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American singer-songwriter, pianist, actor, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American singer-songwriter, pianist, actor, and talk show host", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1967", "text": "<PERSON>, South Korean activist, founded Man of Korea (d. 2013)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-gi\" title=\"<PERSON> Jae-gi\"><PERSON></a>, South Korean activist, founded <a href=\"https://wikipedia.org/wiki/Man_of_Korea\" title=\"Man of Korea\">Man of Korea</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-gi\" title=\"<PERSON> Jae-gi\"><PERSON></a>, South Korean activist, founded <a href=\"https://wikipedia.org/wiki/Man_of_Korea\" title=\"Man of Korea\">Man of Korea</a> (d. 2013)", "links": [{"title": "<PERSON>i", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-gi"}, {"title": "Man of Korea", "link": "https://wikipedia.org/wiki/Man_of_Korea"}]}, {"year": "1967", "text": "<PERSON>, English politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_politician)"}]}, {"year": "1968", "text": "<PERSON>, Estonian journalist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCla\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCla\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Allan_Alak%C3%BCla"}]}, {"year": "1968", "text": "<PERSON>, American director and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Italian artist, photographer and director", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian artist, photographer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian artist, photographer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stefano_<PERSON>l"}]}, {"year": "1969", "text": "<PERSON>, American baseball player, manager, and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eduardo_P%C3%A9rez"}]}, {"year": "1970", "text": "<PERSON>, Mexican wrestler", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Antonio_G%C3%B3mez_Medina\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antonio_G%C3%B3mez_Medina\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_G%C3%B3mez_Medina"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American actress and singer ", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American actress and singer ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American actress and singer ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English singer-songwriter and musician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Argentinian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n"}]}, {"year": "1975", "text": "<PERSON>, South African footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Czech racing driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Enge\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Enge\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Enge"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Indian cricketer and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Mu<PERSON><PERSON>_Kart<PERSON>\" title=\"Mu<PERSON>i Kartik\"><PERSON><PERSON><PERSON></a>, Indian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ka<PERSON>\" title=\"Mu<PERSON><PERSON> Kartik\"><PERSON><PERSON><PERSON></a>, Indian cricketer and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ik"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Welsh guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>da<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luda<PERSON>ris"}]}, {"year": "1977", "text": "<PERSON>, Welsh snooker player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, German footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Serbian footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87"}]}, {"year": "1979", "text": "<PERSON>, French footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Dominican baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank <PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank <PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Chilean footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American actress and artist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Belgian politician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"G<PERSON> Da<PERSON>\"><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"G<PERSON>\"><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>_<PERSON>ems"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian racing driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B4nio_Pizzonia\" title=\"Antônio Pizzonia\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B4nio_Pizzonia\" title=\"Antônio Pizzonia\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian racing driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B4nio_Pizzonia"}]}, {"year": "1981", "text": "<PERSON>, American singer and musician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American mass murderer, responsible for the Columbine High School massacre (d. 1999)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American mass murderer, responsible for the <a href=\"https://wikipedia.org/wiki/Columbine_High_School_massacre\" title=\"Columbine High School massacre\">Columbine High School massacre</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American mass murderer, responsible for the <a href=\"https://wikipedia.org/wiki/Columbine_High_School_massacre\" title=\"Columbine High School massacre\">Columbine High School massacre</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Columbine High School massacre", "link": "https://wikipedia.org/wiki/Columbine_High_School_massacre"}]}, {"year": "1981", "text": "<PERSON>, Australian politician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Ethiopian-Turkish runner", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian-Turkish runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian-Turkish runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Azerbaijani volleyball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani volleyball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Belarusian politician", "html": "1982 - <a href=\"https://wikipedia.org/wiki/S<PERSON>tl<PERSON>_T<PERSON>kaya\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S<PERSON>tl<PERSON>_T<PERSON>kaya\" title=\"Sviatlana T<PERSON>han<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sviatlana_Tsik<PERSON>kaya"}]}, {"year": "1983", "text": "<PERSON>, Kenyan runner", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Nigerian-American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian-American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ellsbury"}]}, {"year": "1984", "text": "<PERSON><PERSON>, New Zealand rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_Malmanche\" title=\"<PERSON><PERSON> Malmanche\"><PERSON><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Malmanche\" title=\"<PERSON><PERSON> de Malmanche\"><PERSON><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ed_de_Malmanche"}]}, {"year": "1984", "text": "<PERSON>, New Zealand rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, South African rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Chili<PERSON>_<PERSON>\" title=\"Chili<PERSON>\"><PERSON><PERSON><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chili<PERSON>_<PERSON>\" title=\"Chili<PERSON>\"><PERSON><PERSON><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chili<PERSON>_<PERSON><PERSON>le"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Italian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actor and musician", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Norwegian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Ghanaian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Jordan_<PERSON>w\" title=\"<PERSON> Ayew\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_<PERSON>w\" title=\"<PERSON>w\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_<PERSON>w"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Norwegian DJ", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Kygo\" title=\"K<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON>go\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian DJ", "links": [{"title": "Kygo", "link": "https://wikipedia.org/wiki/Kygo"}]}, {"year": "1992", "text": "<PERSON>, English discus thrower", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, English discus thrower", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1992", "text": "<PERSON>, Egyptian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1992)\" title=\"<PERSON> (footballer, born 1992)\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1992)\" title=\"<PERSON> (footballer, born 1992)\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON> (footballer, born 1992)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1992)"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American drag queen and entertainer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drag queen and entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drag queen and entertainer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Far<PERSON>_<PERSON>an"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Teuvo_Ter%C3%A4v%C3%A4inen\" title=\"Teu<PERSON> Teräväinen\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Teuvo_Ter%C3%A4v%C3%A4inen\" title=\"Teu<PERSON> Terävä<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teuvo_Ter%C3%A4v%C3%A4inen"}]}, {"year": "1996", "text": "<PERSON>, American professional ice hockey player ", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional ice hockey player ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional ice hockey player ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, French tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Tan\" title=\"<PERSON> Tan\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Tan\" title=\"Harmony Tan\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Tan"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Argentine-Italian basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine-Italian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine-Italian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Liberian-American sprinter", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian-American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian-American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Australian rugby league player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "2001", "text": "<PERSON>, American ice hockey player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}], "Deaths": [{"year": "883", "text": "<PERSON><PERSON>, Byzantine general", "html": "883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Styppiotes\" title=\"<PERSON><PERSON> Styppiotes\"><PERSON><PERSON>ypp<PERSON></a>, Byzantine general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Styppiotes\" title=\"<PERSON><PERSON> Styppiotes\"><PERSON><PERSON>ypp<PERSON></a>, Byzantine general", "links": [{"title": "<PERSON><PERSON>yppiot<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Styppiotes"}]}, {"year": "1063", "text": "<PERSON><PERSON><PERSON> of Hungary (b. 1016)", "html": "1063 - <a href=\"https://wikipedia.org/wiki/B%C3%A9la_I_of_Hungary\" title=\"Béla I of Hungary\"><PERSON><PERSON><PERSON> I of Hungary</a> (b. 1016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9la_I_of_Hungary\" title=\"Béla I of Hungary\"><PERSON><PERSON><PERSON> I of Hungary</a> (b. 1016)", "links": [{"title": "Béla I of Hungary", "link": "https://wikipedia.org/wiki/B%C3%A9la_I_of_Hungary"}]}, {"year": "1161", "text": "<PERSON><PERSON><PERSON>, Queen of Jerusalem (b. 1105)", "html": "1161 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Queen_of_Jerusalem\" title=\"<PERSON><PERSON><PERSON>, Queen of Jerusalem\"><PERSON><PERSON><PERSON>, Queen of Jerusalem</a> (b. 1105)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Queen_of_Jerusalem\" title=\"<PERSON><PERSON><PERSON>, Queen of Jerusalem\"><PERSON><PERSON><PERSON>, Queen of Jerusalem</a> (b. 1105)", "links": [{"title": "<PERSON><PERSON><PERSON>, Queen of Jerusalem", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Queen_of_Jerusalem"}]}, {"year": "1185", "text": "<PERSON>, Byzantine courtier (b. 1130)", "html": "1185 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine courtier (b. 1130)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine courtier (b. 1130)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1279", "text": "<PERSON>, English cardinal (b. 1215)", "html": "1279 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (b. 1215)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (b. 1215)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1297", "text": "<PERSON>, English Treasurer", "html": "1297 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Treasurer\" title=\"Treasurer\">Treasurer</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Treasurer\" title=\"Treasurer\">Treasurer</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Treasurer", "link": "https://wikipedia.org/wiki/Treasurer"}]}, {"year": "1298", "text": "<PERSON> Artois, Lord of Conches, Nonancourt, and Domfront (b. 1269)", "html": "1298 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Artois\"><PERSON> Artois</a>, Lord of <a href=\"https://wikipedia.org/wiki/Conches-en-Ouche\" title=\"Conches-en-Ouche\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Nonancourt\" title=\"Nonancourt\">Nonancourt</a>, and <a href=\"https://wikipedia.org/wiki/Ch%C3%A2teau_de_Domfront\" title=\"Château de Domfront\">Domfront</a> (b. 1269)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Artois\"><PERSON> Artois</a>, Lord of <a href=\"https://wikipedia.org/wiki/Conches-en-Ouche\" title=\"Conches-en-Ouche\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Nonancourt\" title=\"Nonancourt\">Nonancourt</a>, and <a href=\"https://wikipedia.org/wiki/Ch%C3%A2teau_de_Domfront\" title=\"Château de Domfront\">Domfront</a> (b. 1269)", "links": [{"title": "<PERSON> of Artois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Conches-en-Ouche", "link": "https://wikipedia.org/wiki/Conches-en-Ouche"}, {"title": "Nonancourt", "link": "https://wikipedia.org/wiki/Nonancourt"}, {"title": "Château de Domfront", "link": "https://wikipedia.org/wiki/Ch%C3%A2teau_de_Domfront"}]}, {"year": "1349", "text": "<PERSON><PERSON> of Luxembourg, queen of <PERSON> of France (b. 1315)", "html": "1349 - <a href=\"https://wikipedia.org/wiki/Bonne_of_Luxembourg\" title=\"Bonne of Luxembourg\"><PERSON><PERSON> of Luxembourg</a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (b. 1315)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bonne_of_Luxembourg\" title=\"Bonne of Luxembourg\"><PERSON><PERSON> of Luxembourg</a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (b. 1315)", "links": [{"title": "Bonne of Luxembourg", "link": "https://wikipedia.org/wiki/Bonne_of_Luxembourg"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1569", "text": "<PERSON><PERSON>, Italian actress (b. 1530)", "html": "1569 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress (b. 1530)", "links": [{"title": "Vincenza Armani", "link": "https://wikipedia.org/wiki/Vincenza_Armani"}]}, {"year": "1599", "text": "<PERSON>, Italian noblewoman (b. 1577)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian noblewoman (b. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian noblewoman (b. 1577)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1677", "text": "<PERSON>, English philosopher and author (b. 1611)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English philosopher and author (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English philosopher and author (b. 1611)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1680", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (b. 1596)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>\" title=\"Emperor <PERSON><PERSON>Mi<PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>\" title=\"Emperor <PERSON><PERSON>Mi<PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1596)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1721", "text": "<PERSON>, German botanist and physician (b. 1665)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and physician (b. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and physician (b. 1665)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1733", "text": "<PERSON>, French organist and composer (b. 1668)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1668)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>"}]}, {"year": "1760", "text": "<PERSON>, French astronomer and academic (b. 1704)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (b. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, English economist and politician (b. 1772)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and politician (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and politician (b. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, French mathematician and explorer (b. 1786)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and explorer (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and explorer (b. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Dominican politician and writer. He was the leader of the Independence movement of the Dominican Republic against Spain in 1821 (b. 1772)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_N%C3%BA%C3%B1ez_de_C%C3%A1ceres\" title=\"<PERSON>\"><PERSON></a>, Dominican politician and writer. He was the leader of the Independence movement of the Dominican Republic against Spain in 1821 (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_N%C3%BA%C3%B1ez_de_C%C3%A1ceres\" title=\"<PERSON>\"><PERSON></a>, Dominican politician and writer. He was the leader of the Independence movement of the Dominican Republic against Spain in 1821 (b. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_N%C3%BA%C3%B1ez_de_C%C3%A1ceres"}]}, {"year": "1851", "text": "<PERSON>, American minister and dietary reformer, namesake of the graham cracker (b. 1794)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and dietary reformer, namesake of the <a href=\"https://wikipedia.org/wiki/<PERSON>_cracker\" title=\"<PERSON> cracker\">graham cracker</a> (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and dietary reformer, namesake of the <a href=\"https://wikipedia.org/wiki/<PERSON>_cracker\" title=\"<PERSON> cracker\">graham cracker</a> (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> cracker", "link": "https://wikipedia.org/wiki/<PERSON>_cracker"}]}, {"year": "1865", "text": "<PERSON>, French general (b. 1806)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re\" class=\"mw-redirect\" title=\"<PERSON> Lamoricière\"><PERSON></a>, French general (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1888", "text": "<PERSON>, Argentinian journalist and politician, 7th President of Argentina (b. 1811)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Domingo <PERSON>\"><PERSON></a>, Argentinian journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ient<PERSON>"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1896", "text": "<PERSON>, American scholar and educator (b. 1825)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and educator (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and educator (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, Norwegian author (b. 1841)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian author (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian author (b. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, French explorer and author (b. 1847)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer and author (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer and author (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American businessman and politician, 27th Governor of Rhode Island (b. 1830)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"<PERSON> S<PERSON>gue IV\"><PERSON></a>, American businessman and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a> (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"<PERSON>gue IV\"><PERSON></a>, American businessman and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a> (b. 1830)", "links": [{"title": "<PERSON> IV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV"}, {"title": "Governor of Rhode Island", "link": "https://wikipedia.org/wiki/Governor_of_Rhode_Island"}]}, {"year": "1917", "text": "<PERSON>, French captain and pilot (b. 1894)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French captain and pilot (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French captain and pilot (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, New Mexican Congressman and political ally of <PERSON> (b. 1852)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/New_Mexico\" title=\"New Mexico\">New Mexican</a> <a href=\"https://wikipedia.org/wiki/New_Mexico_House_of_Representatives\" title=\"New Mexico House of Representatives\">Congressman</a> and political ally of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/New_Mexico\" title=\"New Mexico\">New Mexican</a> <a href=\"https://wikipedia.org/wiki/New_Mexico_House_of_Representatives\" title=\"New Mexico House of Representatives\">Congressman</a> and political ally of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hilton\"><PERSON></a> (b. 1852)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "New Mexico", "link": "https://wikipedia.org/wiki/New_Mexico"}, {"title": "New Mexico House of Representatives", "link": "https://wikipedia.org/wiki/New_Mexico_House_of_Representatives"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Indian journalist, poet, and activist (b. 1882)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Subramania_Bharati\" title=\"Subramania Bharati\">Subramania Bharati</a>, Indian journalist, poet, and activist (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Subramania_Bharati\" title=\"Subramania Bharati\"><PERSON>rama<PERSON> Bharati</a>, Indian journalist, poet, and activist (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Subramania_Bharati"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese actor and director (b. 1875)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor and director (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor and director (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Polish pilot and businessman, co-founded the RWD Company (b. 1901)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Wigura\" title=\"<PERSON><PERSON> W<PERSON>\"><PERSON><PERSON></a>, Polish pilot and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/RWD_(aircraft_manufacturer)\" title=\"RWD (aircraft manufacturer)\">RWD Company</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Wigura\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish pilot and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/RWD_(aircraft_manufacturer)\" title=\"RWD (aircraft manufacturer)\">RWD Company</a> (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_Wigura"}, {"title": "RWD (aircraft manufacturer)", "link": "https://wikipedia.org/wiki/RWD_(aircraft_manufacturer)"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Polish soldier and pilot (b. 1895)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Franciszek_%C5%BBwirko\" title=\"Franciszek Żwirko\"><PERSON><PERSON><PERSON></a>, Polish soldier and pilot (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franciszek_%C5%BBwirko\" title=\"Franciszek Żwirko\"><PERSON><PERSON><PERSON></a>, Polish soldier and pilot (b. 1895)", "links": [{"title": "Franciszek <PERSON>wirko", "link": "https://wikipedia.org/wiki/Franciszek_%C5%BBwirko"}]}, {"year": "1935", "text": "<PERSON>, American coroner (b. 1867)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(medical_examiner)\" title=\"<PERSON> (medical examiner)\"><PERSON></a>, American coroner (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(medical_examiner)\" title=\"<PERSON> (medical examiner)\"><PERSON></a>, American coroner (b. 1867)", "links": [{"title": "<PERSON> (medical examiner)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(medical_examiner)"}]}, {"year": "1939", "text": "<PERSON>, Russian-French painter and set designer (b. 1861)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French painter and set designer (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French painter and set designer (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Bulgarian physician, journalist, and politician, Soviet Ambassador to France (b. 1873)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian physician, journalist, and politician, <a href=\"https://wikipedia.org/wiki/Soviet_Ambassador_to_France\" class=\"mw-redirect\" title=\"Soviet Ambassador to France\">Soviet Ambassador to France</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian physician, journalist, and politician, <a href=\"https://wikipedia.org/wiki/Soviet_Ambassador_to_France\" class=\"mw-redirect\" title=\"Soviet Ambassador to France\">Soviet Ambassador to France</a> (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Ambassador to France", "link": "https://wikipedia.org/wiki/Soviet_Ambassador_to_France"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Belarusian revolutionary (b. 1878)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian revolutionary (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian revolutionary (b. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Russian revolutionary (b. 1884)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian revolutionary (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian revolutionary (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Pakistani lawyer and politician, 1st Governor-General of Pakistan (b. 1876)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor-General_of_Pakistan\" title=\"Governor-General of Pakistan\">Governor-General of Pakistan</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor-General_of_Pakistan\" title=\"Governor-General of Pakistan\">Governor-General of Pakistan</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor-General of Pakistan", "link": "https://wikipedia.org/wiki/Governor-General_of_Pakistan"}]}, {"year": "1949", "text": "<PERSON>, French composer and conductor (b. 1873)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, South African field marshal and politician, 2nd Prime Minister of South Africa (b. 1870)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African field marshal and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African field marshal and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of South Africa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Africa"}]}, {"year": "1956", "text": "<PERSON>, Canadian colonel and pilot (b. 1894)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and pilot (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and pilot (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American astronomer (b. 1862)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Canadian politician, 34th Mayor of Montreal (b. 1889)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian politician, 34th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian politician, 34th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Montreal", "link": "https://wikipedia.org/wiki/Mayor_of_Montreal"}]}, {"year": "1958", "text": "<PERSON>, English-French poet and author (b. 1874)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French poet and author (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French poet and author (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_W._Service"}]}, {"year": "1959", "text": "<PERSON>, American actor (b. 1907)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1907)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Indian poet and critic (b. 1917)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Gajanan Madhav Muktibod<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and critic (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>nan Madhav Muktib<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and critic (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Mu<PERSON>ib<PERSON>h"}]}, {"year": "1965", "text": "<PERSON>, American educator, founded Toastmasters International (b. 1878)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, founded <a href=\"https://wikipedia.org/wiki/Toastmasters_International\" title=\"Toastmasters International\">Toastmasters International</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, founded <a href=\"https://wikipedia.org/wiki/Toastmasters_International\" title=\"Toastmasters International\">Toastmasters International</a> (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Toastmasters International", "link": "https://wikipedia.org/wiki/Toastmasters_International"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American businessman, co-founded Delta Air Lines (b. 1889)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Woolman\" title=\"<PERSON>lett <PERSON>oolman\"><PERSON><PERSON> <PERSON><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Delta_Air_Lines\" title=\"Delta Air Lines\">Delta Air Lines</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Woolman\" title=\"<PERSON><PERSON>ool<PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Delta_Air_Lines\" title=\"Delta Air Lines\">Delta Air Lines</a> (b. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Delta Air Lines", "link": "https://wikipedia.org/wiki/Delta_Air_Lines"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Polish engineer and academic (b. 1904)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Tadeusz_%C5%BByli%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish engineer and academic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tad<PERSON><PERSON>_%C5%BByli%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish engineer and academic (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadeusz_%C5%BByli%C5%84ski"}]}, {"year": "1968", "text": "<PERSON>, French general (b. 1904)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>gny"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Russian general and politician (b. 1894)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and politician (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and politician (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Chilean physician and politician, 29th President of Chile (b. 1908)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Salvador_Allende\" title=\"<PERSON>\"><PERSON></a>, Chilean physician and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Allende\" title=\"Salvador Allen<PERSON>\"><PERSON></a>, Chilean physician and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salvador_Allende"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Indian philosopher and guru", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher and guru", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher and guru", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American author and illustrator (b. 1893)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player and manager (b. 1895)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Bulgarian author and playwright (b. 1929)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian author and playwright (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian author and playwright (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English photographer (b. 1938)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English photographer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English photographer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Swedish racing driver (b. 1944)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish racing driver (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish racing driver (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, French historian and academic (b. 1914)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and academic (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American politician (b. 1901)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English composer, conductor, and educator (b. 1905)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, conductor, and educator (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, conductor, and educator (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, British Women's Royal Air Force officer (b. 1905)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(WRAF_officer)\" title=\"<PERSON> (WRAF officer)\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Women%27s_Royal_Air_Force_(World_War_II)\" class=\"mw-redirect\" title=\"Women's Royal Air Force (World War II)\">Women's Royal Air Force</a> officer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(WRAF_officer)\" title=\"<PERSON> (WRAF officer)\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Women%27s_Royal_Air_Force_(World_War_II)\" class=\"mw-redirect\" title=\"Women's Royal Air Force (World War II)\">Women's Royal Air Force</a> officer (b. 1905)", "links": [{"title": "<PERSON> (WRAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(WRAF_officer)"}, {"title": "Women's Royal Air Force (World War II)", "link": "https://wikipedia.org/wiki/Women%27s_Royal_Air_Force_(World_War_II)"}]}, {"year": "1985", "text": "<PERSON>, Australian author (b. 1901)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek academic and politician, 138th Prime Minister of Greece (b. 1902)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek academic and politician, 138th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek academic and politician, 138th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1986", "text": "<PERSON>, English author (b. 1895)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>reatfeild"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Canadian actor (b. 1915)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actor (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Jamaican singer-songwriter and guitarist (b. 1944)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter and guitarist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter and guitarist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet and educator (b. 1907)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and educator (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and educator (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English author and illustrator (b. 1935)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Guatemalan anthropologist and activist (b. 1949)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guatemalan anthropologist and activist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guatemalan anthropologist and activist (b. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Austrian-German poet (b. 1920)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German poet (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German poet (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Haitian businessman and activist", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, Haitian businessman and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, Haitian businessman and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antoine_<PERSON>zm%C3%A9ry"}]}, {"year": "1993", "text": "<PERSON>, Austrian-American conductor (b. 1912)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American conductor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American conductor (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American cyclist (b. 1945)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Italian harpsichordist, pianist, and composer (b. 1910)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichordist</a>, pianist, and composer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichordist</a>, pianist, and composer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1994", "text": "<PERSON>, English-American actress (b. 1909)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English neurologist and academic (b. 1952)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English neurologist and academic (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English neurologist and academic (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1933)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American poet (b. 1928)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor (b. 1912)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Cuban painter and lithographer (b. 1967)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Belkis_Ay%C3%B3n\" title=\"Belkis Ayón\"><PERSON><PERSON></a>, Cuban painter and lithographer (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Belkis_Ay%C3%B3n\" title=\"Belkis Ayón\"><PERSON><PERSON></a>, Cuban painter and lithographer (b. 1967)", "links": [{"title": "Belkis Ayón", "link": "https://wikipedia.org/wiki/Belkis_Ay%C3%B3n"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Uruguayan racing driver (b. 1972)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Go<PERSON>lo_Rodr%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON><PERSON><PERSON> (racing driver)\"><PERSON><PERSON><PERSON></a>, Uruguayan racing driver (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>r%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON><PERSON><PERSON> (racing driver)\"><PERSON><PERSON><PERSON></a>, Uruguayan racing driver (b. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Rodr%C3%<PERSON><PERSON><PERSON>_(racing_driver)"}]}, {"year": "2001", "text": "<PERSON>, American author and educator (b. 1938)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "Casualties of the September 11 attacks: see Category:Victims of the September 11 attacks", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Casualties_of_the_September_11_attacks\" title=\"Casualties of the September 11 attacks\">Casualties of the September 11 attacks</a>: <i>see</i> <a href=\"https://wikipedia.org/wiki/Category:Victims_of_the_September_11_attacks\" title=\"Category:Victims of the September 11 attacks\">Category:Victims of the September 11 attacks</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Casualties_of_the_September_11_attacks\" title=\"Casualties of the September 11 attacks\">Casualties of the September 11 attacks</a>: <i>see</i> <a href=\"https://wikipedia.org/wiki/Category:Victims_of_the_September_11_attacks\" title=\"Category:Victims of the September 11 attacks\">Category:Victims of the September 11 attacks</a>", "links": [{"title": "Casualties of the September 11 attacks", "link": "https://wikipedia.org/wiki/Casualties_of_the_September_11_attacks"}, {"title": "Category:Victims of the September 11 attacks", "link": "https://wikipedia.org/wiki/Category:Victims_of_the_September_11_attacks"}]}, {"year": "2002", "text": "<PERSON>, American actress (b. 1922)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hunter\"><PERSON></a>, American actress (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American football player and sportscaster (b. 1933)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American author and illustrator (b. 1953)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Swedish politician, 39th Minister of Foreign Affairs for Sweden (b. 1957)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, 39th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)\" title=\"Minister for Foreign Affairs (Sweden)\">Minister of Foreign Affairs for Sweden</a> (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician, 39th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)\" title=\"Minister for Foreign Affairs (Sweden)\">Minister of Foreign Affairs for Sweden</a> (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)"}]}, {"year": "2003", "text": "<PERSON>, American actor (b. 1948)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American songwriter (b. 1928)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American painter and illustrator (b. 1939)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and illustrator (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and illustrator (b. 1939)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "2004", "text": "Patriarch <PERSON> of Alexandria (b. 1949)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON>_<PERSON>_of_Alexandria\" title=\"Patriarch <PERSON> of Alexandria\">Patriarch <PERSON> of Alexandria</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON>_<PERSON>_of_Alexandria\" title=\"Patriarch <PERSON> of Alexandria\">Patriarch <PERSON> of Alexandria</a> (b. 1949)", "links": [{"title": "Patriarch <PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/Patriarch_<PERSON>_<PERSON>_of_Alexandria"}]}, {"year": "2006", "text": "<PERSON>, Scottish poet and author (b. 1924)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and author (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, German journalist and author (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joachim Fest\"><PERSON></a>, German journalist and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joachim_<PERSON>\" title=\"Joachim Fest\"><PERSON></a>, German journalist and author (b. 1926)", "links": [{"title": "Joachim <PERSON>", "link": "https://wikipedia.org/wiki/Joachim_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Scottish footballer and manager (b. 1946)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American explorer, theologian, and author (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer, theologian, and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer, theologian, and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, French sociologist and author (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Jean_<PERSON>%C3%A9guy\" title=\"<PERSON>\"><PERSON></a>, French sociologist and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jean_<PERSON>%C3%A9guy\" title=\"<PERSON>\"><PERSON></a>, French sociologist and author (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jean_S%C3%A9guy"}]}, {"year": "2007", "text": "<PERSON>, Austrian keyboard player and songwriter (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian keyboard player and songwriter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian keyboard player and songwriter (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American author, poet and musician (b. 1949)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet and musician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet and musician (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Canadian producer and manager (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian producer and manager (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian producer and manager (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American director, producer, and screenwriter (b. 1928)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Japanese author and illustrator (b. 1958)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator (b. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ui"}]}, {"year": "2010", "text": "<PERSON>, American actor (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actor (b. 1914)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1914)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "2011", "text": "<PERSON>, Danish racing driver (b. 1984)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish racing driver (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish racing driver (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English footballer (b. 1932)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Indian soldier and pilot (b. 1975)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian soldier and pilot (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian soldier and pilot (b. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Welsh actor and model (b. 1971)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and model (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and model (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Norwegian civil servant and businessman (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian civil servant and businessman (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian civil servant and businessman (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Norwegian cinematographer and producer (b. 1972)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian cinematographer and producer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian cinematographer and producer (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON>, American lawyer and diplomat, 10th United States Ambassador to Libya (b. 1960)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and diplomat, 10th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Libya\" class=\"mw-redirect\" title=\"United States Ambassador to Libya\">United States Ambassador to Libya</a> (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and diplomat, 10th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Libya\" class=\"mw-redirect\" title=\"United States Ambassador to Libya\">United States Ambassador to Libya</a> (b. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to Libya", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Libya"}]}, {"year": "2013", "text": "<PERSON>, Filipino lawyer and politician, Solicitor General of the Philippines (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_the_Philippines\" title=\"Solicitor General of the Philippines\">Solicitor General of the Philippines</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_the_Philippines\" title=\"Solicitor General of the Philippines\">Solicitor General of the Philippines</a> (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Solicitor General of the Philippines", "link": "https://wikipedia.org/wiki/Solicitor_General_of_the_Philippines"}]}, {"year": "2013", "text": "<PERSON>, French geneticist and biologist (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French geneticist and biologist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French geneticist and biologist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Polish mathematician and computer scientist (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician and computer scientist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician and computer scientist (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American singer-songwriter and producer (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, French composer and conductor (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English actor (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actress, musician and cabaret performer (b. 1969)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, musician and cabaret performer (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, musician and cabaret performer (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON> <PERSON><PERSON>, 3rd President of Indonesia (b. 1936)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> J. Ha<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Indonesia", "link": "https://wikipedia.org/wiki/President_of_Indonesia"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Jamaican singer and songwriter (b. 1942)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Too<PERSON>_Hibbert\" title=\"Toots Hibbert\"><PERSON><PERSON>bbert</a>, Jamaican singer and songwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Too<PERSON>_Hibbert\" title=\"Toots Hibbert\"><PERSON><PERSON></a>, Jamaican singer and songwriter (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hibbert"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON><PERSON>, Peruvian philosopher and academic (b. 1934)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>el_Guzm%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Peruvian philosopher and academic (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Peruvian philosopher and academic (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abimael_Guzm%C3%A1n"}]}, {"year": "2022", "text": "<PERSON>, Spanish novelist, journalist and translator (b. 1951)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Javier_Mar%C3%ADas\" title=\"<PERSON>\"><PERSON></a>, Spanish novelist, journalist and translator (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Javier_<PERSON>%C3%ADas\" title=\"<PERSON>\"><PERSON></a>, Spanish novelist, journalist and translator (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Javier_Mar%C3%ADas"}]}, {"year": "2022", "text": "<PERSON>, American academic, Catholic historian, and Jesuit priest (b. 1927)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, American academic, Catholic historian, and Jesuit priest (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, American academic, Catholic historian, and Jesuit priest (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>%27Malley"}]}, {"year": "2022", "text": "<PERSON>, British classicist and academic (b. 1918)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classicist)\" title=\"<PERSON> (classicist)\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Classics\" title=\"Classics\">classicist</a> and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(classicist)\" title=\"<PERSON> (classicist)\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Classics\" title=\"Classics\">classicist</a> and academic (b. 1918)", "links": [{"title": "<PERSON> (classicist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classicist)"}, {"title": "Classics", "link": "https://wikipedia.org/wiki/Classics"}]}, {"year": "2024", "text": "<PERSON>, British actor (b. 1931)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Peruvian politician, professor, and engineer, 54th President of Peru (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian politician, professor, and engineer, 54th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian politician, professor, and engineer, 54th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "2024", "text": "<PERSON>, American actor and race car driver (b. 1960)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and race car driver (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and race car driver (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American football player and coach (b. 1932)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1932)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}]}}